// Copyright 2016 - 2025 The excelize Authors. All rights reserved. Use of
// this source code is governed by a BSD-style license that can be found in
// the LICENSE file.
//
// Package excelize providing a set of functions that allow you to write to and
// read from XLAM / XLSM / XLSX / XLTM / XLTX files. Supports reading and
// writing spreadsheet documents generated by Microsoft Excel™ 2007 and later.
// Supports complex components by high compatibility, and provided streaming
// API for generating or reading data from a worksheet with huge amounts of
// data. This library needs Go version 1.23 or later.

package excelize

import (
	"encoding/xml"
	"sync"
)

// xlsxStyleSheet is the root element of the Styles part.
type xlsxStyleSheet struct {
	mu           sync.Mutex
	XMLName      xml.Name          `xml:"http://schemas.openxmlformats.org/spreadsheetml/2006/main styleSheet"`
	NumFmts      *xlsxNumFmts      `xml:"numFmts"`
	Fonts        *xlsxFonts        `xml:"fonts"`
	Fills        *xlsxFills        `xml:"fills"`
	Borders      *xlsxBorders      `xml:"borders"`
	CellStyleXfs *xlsxCellStyleXfs `xml:"cellStyleXfs"`
	CellXfs      *xlsxCellXfs      `xml:"cellXfs"`
	CellStyles   *xlsxCellStyles   `xml:"cellStyles"`
	Dxfs         *xlsxDxfs         `xml:"dxfs"`
	TableStyles  *xlsxTableStyles  `xml:"tableStyles"`
	Colors       *xlsxStyleColors  `xml:"colors"`
	ExtLst       *xlsxExtLst       `xml:"extLst"`
}

// xlsxAlignment formatting information pertaining to text alignment in cells.
// There are a variety of choices for how text is aligned both horizontally and
// vertically, as well as indentation settings, and so on.
type xlsxAlignment struct {
	Horizontal      string `xml:"horizontal,attr,omitempty"`
	Indent          int    `xml:"indent,attr,omitempty"`
	JustifyLastLine bool   `xml:"justifyLastLine,attr,omitempty"`
	ReadingOrder    uint64 `xml:"readingOrder,attr,omitempty"`
	RelativeIndent  int    `xml:"relativeIndent,attr,omitempty"`
	ShrinkToFit     bool   `xml:"shrinkToFit,attr,omitempty"`
	TextRotation    int    `xml:"textRotation,attr,omitempty"`
	Vertical        string `xml:"vertical,attr,omitempty"`
	WrapText        bool   `xml:"wrapText,attr,omitempty"`
}

// xlsxProtection (Protection Properties) contains protection properties
// associated with the cell. Each cell has protection properties that can be
// set. The cell protection properties do not take effect unless the sheet has
// been protected.
type xlsxProtection struct {
	Hidden *bool `xml:"hidden,attr"`
	Locked *bool `xml:"locked,attr"`
}

// xlsxLine expresses a single set of cell border.
type xlsxLine struct {
	Style string     `xml:"style,attr,omitempty"`
	Color *xlsxColor `xml:"color"`
}

// xlsxColor is a common mapping used for both the fgColor and bgColor elements.
// Foreground color of the cell fill pattern. Cell fill patterns operate with
// two colors: a background color and a foreground color. These combine
// to make a patterned cell fill. Background color of the cell fill pattern.
// Cell fill patterns operate with two colors: a background color and a
// foreground color. These combine to make a patterned cell fill.
type xlsxColor struct {
	Auto    bool    `xml:"auto,attr,omitempty"`
	RGB     string  `xml:"rgb,attr,omitempty"`
	Indexed int     `xml:"indexed,attr,omitempty"`
	Theme   *int    `xml:"theme,attr"`
	Tint    float64 `xml:"tint,attr,omitempty"`
}

// xlsxFonts directly maps the font element. This element contains all font
// definitions for this workbook.
type xlsxFonts struct {
	Count int         `xml:"count,attr"`
	Font  []*xlsxFont `xml:"font"`
}

// xlsxFont directly maps the font element. This element defines the
// properties for one of the fonts used in this workbook.
type xlsxFont struct {
	B        *attrValBool   `xml:"b"`
	I        *attrValBool   `xml:"i"`
	Strike   *attrValBool   `xml:"strike"`
	Outline  *attrValBool   `xml:"outline"`
	Shadow   *attrValBool   `xml:"shadow"`
	Condense *attrValBool   `xml:"condense"`
	Extend   *attrValBool   `xml:"extend"`
	U        *attrValString `xml:"u"`
	Sz       *attrValFloat  `xml:"sz"`
	Color    *xlsxColor     `xml:"color"`
	Name     *attrValString `xml:"name"`
	Family   *attrValInt    `xml:"family"`
	Charset  *attrValInt    `xml:"charset"`
	Scheme   *attrValString `xml:"scheme"`
}

// xlsxFills directly maps the fills' element. This element defines the cell
// fills portion of the Styles part, consisting of a sequence of fill records. A
// cell fill consists of a background color, foreground color, and pattern to be
// applied across the cell.
type xlsxFills struct {
	Count int         `xml:"count,attr"`
	Fill  []*xlsxFill `xml:"fill"`
}

// xlsxFill directly maps the fill element. This element specifies fill
// formatting.
type xlsxFill struct {
	PatternFill  *xlsxPatternFill  `xml:"patternFill"`
	GradientFill *xlsxGradientFill `xml:"gradientFill"`
}

// xlsxPatternFill is used to specify cell fill information for pattern and
// solid color cell fills. For solid cell fills (no pattern), fgColor is used.
// For cell fills with patterns specified, then the cell fill color is
// specified by the bgColor element.
type xlsxPatternFill struct {
	PatternType string     `xml:"patternType,attr,omitempty"`
	FgColor     *xlsxColor `xml:"fgColor"`
	BgColor     *xlsxColor `xml:"bgColor"`
}

// xlsxGradientFill defines a gradient-style cell fill. Gradient cell fills can
// use one or two colors as the end points of color interpolation.
type xlsxGradientFill struct {
	Bottom float64                 `xml:"bottom,attr,omitempty"`
	Degree float64                 `xml:"degree,attr,omitempty"`
	Left   float64                 `xml:"left,attr,omitempty"`
	Right  float64                 `xml:"right,attr,omitempty"`
	Top    float64                 `xml:"top,attr,omitempty"`
	Type   string                  `xml:"type,attr,omitempty"`
	Stop   []*xlsxGradientFillStop `xml:"stop"`
}

// xlsxGradientFillStop directly maps the stop element.
type xlsxGradientFillStop struct {
	Position float64   `xml:"position,attr"`
	Color    xlsxColor `xml:"color,omitempty"`
}

// xlsxBorders directly maps the borders' element. This element contains borders
// formatting information, specifying all border definitions for all cells in
// the workbook.
type xlsxBorders struct {
	Count  int           `xml:"count,attr"`
	Border []*xlsxBorder `xml:"border"`
}

// xlsxBorder directly maps the border element. Expresses a single set of cell
// border formats (left, right, top, bottom, diagonal). Color is optional. When
// missing, 'automatic' is implied.
type xlsxBorder struct {
	DiagonalDown bool      `xml:"diagonalDown,attr,omitempty"`
	DiagonalUp   bool      `xml:"diagonalUp,attr,omitempty"`
	Outline      bool      `xml:"outline,attr,omitempty"`
	Left         *xlsxLine `xml:"left"`
	Right        *xlsxLine `xml:"right"`
	Top          *xlsxLine `xml:"top"`
	Bottom       *xlsxLine `xml:"bottom"`
	Diagonal     *xlsxLine `xml:"diagonal"`
	Vertical     *xlsxLine `xml:"vertical"`
	Horizontal   *xlsxLine `xml:"horizontal"`
}

// xlsxCellStyles directly maps the cellStyles element. This element contains
// the named cell styles, consisting of a sequence of named style records. A
// named cell style is a collection of direct or themed formatting (e.g., cell
// border, cell fill, and font type/size/style) grouped together into a single
// named style, and can be applied to a cell.
type xlsxCellStyles struct {
	XMLName   xml.Name         `xml:"cellStyles"`
	Count     int              `xml:"count,attr"`
	CellStyle []*xlsxCellStyle `xml:"cellStyle"`
}

// xlsxCellStyle directly maps the cellStyle element. This element represents
// the name and related formatting records for a named cell style in this
// workbook.
type xlsxCellStyle struct {
	XMLName       xml.Name `xml:"cellStyle"`
	Name          string   `xml:"name,attr"`
	XfID          int      `xml:"xfId,attr"`
	BuiltInID     *int     `xml:"builtinId,attr"`
	ILevel        *int     `xml:"iLevel,attr"`
	Hidden        *bool    `xml:"hidden,attr"`
	CustomBuiltIn *bool    `xml:"customBuiltin,attr"`
}

// xlsxCellStyleXfs directly maps the cellStyleXfs element. This element
// contains the master formatting records (xf's) which define the formatting for
// all named cell styles in this workbook. Master formatting records reference
// individual elements of formatting (e.g., number format, font definitions,
// cell fills, etc.) by specifying a zero-based index into those collections.
// Master formatting records also specify whether to apply or ignore particular
// aspects of formatting.
type xlsxCellStyleXfs struct {
	Count int      `xml:"count,attr"`
	Xf    []xlsxXf `xml:"xf,omitempty"`
}

// xlsxXf directly maps the xf element. A single xf element describes all the
// formatting for a cell.
type xlsxXf struct {
	NumFmtID          *int            `xml:"numFmtId,attr"`
	FontID            *int            `xml:"fontId,attr"`
	FillID            *int            `xml:"fillId,attr"`
	BorderID          *int            `xml:"borderId,attr"`
	XfID              *int            `xml:"xfId,attr"`
	QuotePrefix       *bool           `xml:"quotePrefix,attr"`
	PivotButton       *bool           `xml:"pivotButton,attr"`
	ApplyNumberFormat *bool           `xml:"applyNumberFormat,attr"`
	ApplyFont         *bool           `xml:"applyFont,attr"`
	ApplyFill         *bool           `xml:"applyFill,attr"`
	ApplyBorder       *bool           `xml:"applyBorder,attr"`
	ApplyAlignment    *bool           `xml:"applyAlignment,attr"`
	ApplyProtection   *bool           `xml:"applyProtection,attr"`
	Alignment         *xlsxAlignment  `xml:"alignment"`
	Protection        *xlsxProtection `xml:"protection"`
}

// xlsxCellXfs directly maps the cellXfs element. This element contains the
// master formatting records (xf) which define the formatting applied to cells
// in this workbook. These records are the starting point for determining the
// formatting for a cell. Cells in the Sheet Part reference the xf records by
// zero-based index.
type xlsxCellXfs struct {
	Count int      `xml:"count,attr"`
	Xf    []xlsxXf `xml:"xf,omitempty"`
}

// xlsxDxfs directly maps the dxfs element. This element contains the master
// differential formatting records (dxf's) which define formatting for all
// non-cell formatting in this workbook. Whereas xf records fully specify a
// particular aspect of formatting (e.g., cell borders) by referencing those
// formatting definitions elsewhere in the Styles part, dxf records specify
// incremental (or differential) aspects of formatting directly inline within
// the dxf element. The dxf formatting is to be applied on top of or in addition
// to any formatting already present on the object using the dxf record.
type xlsxDxfs struct {
	Count int        `xml:"count,attr"`
	Dxfs  []*xlsxDxf `xml:"dxf"`
}

// xlsxDxf directly maps the dxf element. A single dxf record, expressing
// incremental formatting to be applied.
type xlsxDxf struct {
	Font       *xlsxFont           `xml:"font"`
	NumFmt     *xlsxNumFmt         `xml:"numFmt"`
	Fill       *xlsxFill           `xml:"fill"`
	Alignment  *xlsxAlignment      `xml:"alignment"`
	Border     *xlsxBorder         `xml:"border"`
	Protection *xlsxProtection     `xml:"protection"`
	ExtLst     *xlsxPositiveSize2D `xml:"extLst"`
}

// xlsxTableStyles directly maps the tableStyles element. This element
// represents a collection of Table style definitions for Table styles and
// PivotTable styles used in this workbook. It consists of a sequence of
// tableStyle records, each defining a single Table style.
type xlsxTableStyles struct {
	Count             int               `xml:"count,attr"`
	DefaultPivotStyle string            `xml:"defaultPivotStyle,attr"`
	DefaultTableStyle string            `xml:"defaultTableStyle,attr"`
	TableStyles       []*xlsxTableStyle `xml:"tableStyle"`
}

// xlsxTableStyle directly maps the tableStyle element. This element represents
// a single table style definition that indicates how a spreadsheet application
// should format and display a table.
type xlsxTableStyle struct {
	Name              string `xml:"name,attr,omitempty"`
	Pivot             int    `xml:"pivot,attr"`
	Count             int    `xml:"count,attr,omitempty"`
	Table             bool   `xml:"table,attr,omitempty"`
	TableStyleElement string `xml:",innerxml"`
}

// xlsxNumFmts directly maps the numFmts element. This element defines the
// number formats in this workbook, consisting of a sequence of numFmt records,
// where each numFmt record defines a particular number format, indicating how
// to format and render the numeric value of a cell.
type xlsxNumFmts struct {
	Count  int           `xml:"count,attr"`
	NumFmt []*xlsxNumFmt `xml:"numFmt"`
}

// xlsxNumFmt directly maps the numFmt element. This element specifies number
// format properties which indicate how to format and render the numeric value
// of a cell.
type xlsxNumFmt struct {
	NumFmtID     int    `xml:"numFmtId,attr"`
	FormatCode   string `xml:"formatCode,attr"`
	FormatCode16 string `xml:"http://schemas.microsoft.com/office/spreadsheetml/2015/02/main formatCode16,attr,omitempty"`
}

// xlsxIndexedColors directly maps the single ARGB entry for the corresponding
// color index.
type xlsxIndexedColors struct {
	RgbColor []xlsxColor `xml:"rgbColor"`
}

// xlsxStyleColors directly maps the colors' element. Color information
// associated with this style sheet. This collection is written whenever the
// legacy color palette has been modified (backwards compatibility settings) or
// a custom color has been selected while using this workbook.
type xlsxStyleColors struct {
	IndexedColors *xlsxIndexedColors `xml:"indexedColors"`
	MruColors     *xlsxInnerXML      `xml:"mruColors"`
}

// Alignment directly maps the alignment settings of the cells.
type Alignment struct {
	Horizontal      string
	Indent          int
	JustifyLastLine bool
	ReadingOrder    uint64
	RelativeIndent  int
	ShrinkToFit     bool
	TextRotation    int
	Vertical        string
	WrapText        bool
}

// Border directly maps the border settings of the cells.
type Border struct {
	Type  string
	Color string
	Style int
}

// Font directly maps the font settings of the fonts.
type Font struct {
	Bold         bool
	Italic       bool
	Underline    string
	Family       string
	Size         float64
	Strike       bool
	Color        string
	ColorIndexed int
	ColorTheme   *int
	ColorTint    float64
	VertAlign    string
}

// Fill directly maps the fill settings of the cells.
type Fill struct {
	Type    string
	Pattern int
	Color   []string
	Shading int
}

// Protection directly maps the protection settings of the cells.
type Protection struct {
	Hidden bool
	Locked bool
}

// Style directly maps the style settings of the cells.
type Style struct {
	Border        []Border
	Fill          Fill
	Font          *Font
	Alignment     *Alignment
	Protection    *Protection
	NumFmt        int
	DecimalPlaces *int
	CustomNumFmt  *string
	NegRed        bool
}
