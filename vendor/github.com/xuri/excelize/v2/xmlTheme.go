// Copyright 2016 - 2025 The excelize Authors. All rights reserved. Use of
// this source code is governed by a BSD-style license that can be found in
// the LICENSE file.
//
// Package excelize providing a set of functions that allow you to write to and
// read from XLAM / XLSM / XLSX / XLTM / XLTX files. Supports reading and
// writing spreadsheet documents generated by Microsoft Excel™ 2007 and later.
// Supports complex components by high compatibility, and provided streaming
// API for generating or reading data from a worksheet with huge amounts of
// data. This library needs Go version 1.23 or later.

package excelize

import "encoding/xml"

// xlsxTheme directly maps the theme element in the namespace
// http://schemas.openxmlformats.org/drawingml/2006/main
type xlsxTheme struct {
	XMLName           xml.Name              `xml:"a:theme"`
	XMLNSa            string                `xml:"xmlns:a,attr"`
	XMLNSr            string                `xml:"xmlns:r,attr"`
	Name              string                `xml:"name,attr"`
	ThemeElements     xlsxBaseStyles        `xml:"a:themeElements"`
	ObjectDefaults    xlsxObjectDefaults    `xml:"a:objectDefaults"`
	ExtraClrSchemeLst xlsxExtraClrSchemeLst `xml:"a:extraClrSchemeLst"`
	CustClrLst        *xlsxInnerXML         `xml:"a:custClrLst"`
	ExtLst            *xlsxExtLst           `xml:"a:extLst"`
}

// xlsxBaseStyles defines the theme elements for a theme, and is the workhorse
// of the theme. The bulk of the shared theme information that is used by a
// given document is defined here. Within this complex type is defined a color
// scheme, a font scheme, and a style matrix (format scheme) that defines
// different formatting options for different pieces of a document.
type xlsxBaseStyles struct {
	ClrScheme  xlsxColorScheme `xml:"a:clrScheme"`
	FontScheme xlsxFontScheme  `xml:"a:fontScheme"`
	FmtScheme  xlsxStyleMatrix `xml:"a:fmtScheme"`
	ExtLst     *xlsxExtLst     `xml:"a:extLst"`
}

// xlsxCTColor holds the actual color values that are to be applied to a given
// diagram and how those colors are to be applied.
type xlsxCTColor struct {
	ScrgbClr  *xlsxInnerXML  `xml:"a:scrgbClr"`
	SrgbClr   *attrValString `xml:"a:srgbClr"`
	HslClr    *xlsxInnerXML  `xml:"a:hslClr"`
	SysClr    *xlsxSysClr    `xml:"a:sysClr"`
	SchemeClr *xlsxInnerXML  `xml:"a:schemeClr"`
	PrstClr   *xlsxInnerXML  `xml:"a:prstClr"`
}

// xlsxColorScheme defines a set of colors for the theme. The set of colors
// consists of twelve color slots that can each hold a color of choice.
type xlsxColorScheme struct {
	Name     string      `xml:"name,attr"`
	Dk1      xlsxCTColor `xml:"a:dk1"`
	Lt1      xlsxCTColor `xml:"a:lt1"`
	Dk2      xlsxCTColor `xml:"a:dk2"`
	Lt2      xlsxCTColor `xml:"a:lt2"`
	Accent1  xlsxCTColor `xml:"a:accent1"`
	Accent2  xlsxCTColor `xml:"a:accent2"`
	Accent3  xlsxCTColor `xml:"a:accent3"`
	Accent4  xlsxCTColor `xml:"a:accent4"`
	Accent5  xlsxCTColor `xml:"a:accent5"`
	Accent6  xlsxCTColor `xml:"a:accent6"`
	Hlink    xlsxCTColor `xml:"a:hlink"`
	FolHlink xlsxCTColor `xml:"a:folHlink"`
	ExtLst   *xlsxExtLst `xml:"a:extLst"`
}

// objectDefaults element allows for the definition of default shape, line,
// and textbox formatting properties. An application can use this information
// to format a shape (or text) initially on insertion into a document.
type xlsxObjectDefaults struct {
	ObjectDefaults string `xml:",innerxml"`
}

// xlsxExtraClrSchemeLst element is a container for the list of extra color
// schemes present in a document.
type xlsxExtraClrSchemeLst struct {
	ExtraClrSchemeLst string `xml:",innerxml"`
}

// xlsxCTSupplementalFont defines an additional font that is used for language
// specific fonts in themes. For example, one can specify a font that gets used
// only within the Japanese language context.
type xlsxCTSupplementalFont struct {
	Script   string `xml:"script,attr"`
	Typeface string `xml:"typeface,attr"`
}

// xlsxFontCollection defines a major and minor font which is used in the font
// scheme. A font collection consists of a font definition for Latin, East
// Asian, and complex script. On top of these three definitions, one can also
// define a font for use in a specific language or languages.
type xlsxFontCollection struct {
	Latin  *xlsxCTTextFont          `xml:"a:latin"`
	Ea     *xlsxCTTextFont          `xml:"a:ea"`
	Cs     *xlsxCTTextFont          `xml:"a:cs"`
	Font   []xlsxCTSupplementalFont `xml:"a:font"`
	ExtLst *xlsxExtLst              `xml:"a:extLst"`
}

// xlsxFontScheme element defines the font scheme within the theme. The font
// scheme consists of a pair of major and minor fonts for which to use in a
// document. The major font corresponds well with the heading areas of a
// document, and the minor font corresponds well with the normal text or
// paragraph areas.
type xlsxFontScheme struct {
	Name      string             `xml:"name,attr"`
	MajorFont xlsxFontCollection `xml:"a:majorFont"`
	MinorFont xlsxFontCollection `xml:"a:minorFont"`
	ExtLst    *xlsxExtLst        `xml:"a:extLst"`
}

// xlsxStyleMatrix defines a set of formatting options, which can be referenced
// by documents that apply a certain style to a given part of an object. For
// example, in a given shape, say a rectangle, one can reference a themed line
// style, themed effect, and themed fill that would be theme specific and
// change when the theme is changed.
type xlsxStyleMatrix struct {
	Name           string             `xml:"name,attr,omitempty"`
	FillStyleLst   xlsxFillStyleLst   `xml:"a:fillStyleLst"`
	LnStyleLst     xlsxLnStyleLst     `xml:"a:lnStyleLst"`
	EffectStyleLst xlsxEffectStyleLst `xml:"a:effectStyleLst"`
	BgFillStyleLst xlsxBgFillStyleLst `xml:"a:bgFillStyleLst"`
}

// xlsxFillStyleLst element defines a set of three fill styles that are used
// within a theme. The three fill styles are arranged in order from subtle to
// moderate to intense.
type xlsxFillStyleLst struct {
	FillStyleLst string `xml:",innerxml"`
}

// xlsxLnStyleLst element defines a list of three line styles for use within a
// theme. The three line styles are arranged in order from subtle to moderate
// to intense versions of lines. This list makes up part of the style matrix.
type xlsxLnStyleLst struct {
	LnStyleLst string `xml:",innerxml"`
}

// xlsxEffectStyleLst element defines a set of three effect styles that create
// the effect style list for a theme. The effect styles are arranged in order
// of subtle to moderate to intense.
type xlsxEffectStyleLst struct {
	EffectStyleLst string `xml:",innerxml"`
}

// xlsxBgFillStyleLst element defines a list of background fills that are
// used within a theme. The background fills consist of three fills, arranged
// in order from subtle to moderate to intense.
type xlsxBgFillStyleLst struct {
	BgFillStyleLst string `xml:",innerxml"`
}

// xlsxSysClr element specifies a color bound to predefined operating system
// elements.
type xlsxSysClr struct {
	Val     string `xml:"val,attr"`
	LastClr string `xml:"lastClr,attr"`
}

// decodeTheme defines the structure used to parse the a:theme element for the
// theme.
type decodeTheme struct {
	XMLName           xml.Name              `xml:"http://schemas.openxmlformats.org/drawingml/2006/main theme"`
	Name              string                `xml:"name,attr"`
	ThemeElements     decodeBaseStyles      `xml:"themeElements"`
	ObjectDefaults    xlsxObjectDefaults    `xml:"objectDefaults"`
	ExtraClrSchemeLst xlsxExtraClrSchemeLst `xml:"extraClrSchemeLst"`
	CustClrLst        *xlsxInnerXML         `xml:"custClrLst"`
	ExtLst            *xlsxExtLst           `xml:"extLst"`
}

// decodeBaseStyles defines the structure used to parse the theme elements for a
// theme, and is the workhorse of the theme.
type decodeBaseStyles struct {
	ClrScheme  decodeColorScheme `xml:"clrScheme"`
	FontScheme decodeFontScheme  `xml:"fontScheme"`
	FmtScheme  decodeStyleMatrix `xml:"fmtScheme"`
	ExtLst     *xlsxExtLst       `xml:"extLst"`
}

// decodeColorScheme defines the structure used to parse a set of colors for the
// theme.
type decodeColorScheme struct {
	Name     string        `xml:"name,attr"`
	Dk1      decodeCTColor `xml:"dk1"`
	Lt1      decodeCTColor `xml:"lt1"`
	Dk2      decodeCTColor `xml:"dk2"`
	Lt2      decodeCTColor `xml:"lt2"`
	Accent1  decodeCTColor `xml:"accent1"`
	Accent2  decodeCTColor `xml:"accent2"`
	Accent3  decodeCTColor `xml:"accent3"`
	Accent4  decodeCTColor `xml:"accent4"`
	Accent5  decodeCTColor `xml:"accent5"`
	Accent6  decodeCTColor `xml:"accent6"`
	Hlink    decodeCTColor `xml:"hlink"`
	FolHlink decodeCTColor `xml:"folHlink"`
	ExtLst   *xlsxExtLst   `xml:"extLst"`
}

// decodeFontScheme defines the structure used to parse font scheme within the
// theme.
type decodeFontScheme struct {
	Name      string               `xml:"name,attr"`
	MajorFont decodeFontCollection `xml:"majorFont"`
	MinorFont decodeFontCollection `xml:"minorFont"`
	ExtLst    *xlsxExtLst          `xml:"extLst"`
}

// decodeFontCollection defines the structure used to parse a major and minor
// font which is used in the font scheme.
type decodeFontCollection struct {
	Latin  *xlsxCTTextFont          `xml:"latin"`
	Ea     *xlsxCTTextFont          `xml:"ea"`
	Cs     *xlsxCTTextFont          `xml:"cs"`
	Font   []xlsxCTSupplementalFont `xml:"font"`
	ExtLst *xlsxExtLst              `xml:"extLst"`
}

// decodeCTColor defines the structure used to parse the actual color values
// that are to be applied to a given diagram and how those colors are to be
// applied.
type decodeCTColor struct {
	ScrgbClr  *xlsxInnerXML  `xml:"scrgbClr"`
	SrgbClr   *attrValString `xml:"srgbClr"`
	HslClr    *xlsxInnerXML  `xml:"hslClr"`
	SysClr    *xlsxSysClr    `xml:"sysClr"`
	SchemeClr *xlsxInnerXML  `xml:"schemeClr"`
	PrstClr   *xlsxInnerXML  `xml:"prstClr"`
}

// decodeStyleMatrix defines the structure used to parse a set of formatting
// options, which can be referenced by documents that apply a certain style to
// a given part of an object.
type decodeStyleMatrix struct {
	Name           string             `xml:"name,attr,omitempty"`
	FillStyleLst   xlsxFillStyleLst   `xml:"fillStyleLst"`
	LnStyleLst     xlsxLnStyleLst     `xml:"lnStyleLst"`
	EffectStyleLst xlsxEffectStyleLst `xml:"effectStyleLst"`
	BgFillStyleLst xlsxBgFillStyleLst `xml:"bgFillStyleLst"`
}
