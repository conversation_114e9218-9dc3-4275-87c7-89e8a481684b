// Copyright 2016 - 2025 The excelize Authors. All rights reserved. Use of
// this source code is governed by a BSD-style license that can be found in
// the LICENSE file.
//
// Package excelize providing a set of functions that allow you to write to and
// read from XLAM / XLSM / XLSX / XLTM / XLTX files. Supports reading and
// writing spreadsheet documents generated by Microsoft Excel™ 2007 and later.
// Supports complex components by high compatibility, and provided streaming
// API for generating or reading data from a worksheet with huge amounts of
// data. This library needs Go version 1.23 or later.

package excelize

import (
	"fmt"
	"math"
	"math/big"
	"strconv"
	"strings"
	"time"

	"github.com/xuri/nfp"
)

// languageInfo defined the required fields of localization support for number
// format.
type languageInfo struct {
	apFmt                                string
	tags, weekdayNames, weekdayNamesAbbr []string
	useGannen                            bool
	localMonth                           func(t time.Time, abbr int) string
}

// numberFormat directly maps the number format parser runtime required
// fields.
type numberFormat struct {
	opts                                                                     *Options
	cellType                                                                 CellType
	section                                                                  []nfp.Section
	t                                                                        time.Time
	sectionIdx                                                               int
	date1904, isNumeric, hours, seconds, useMillisecond, useGannen           bool
	number                                                                   float64
	ap, localCode, result, value, valueSectionType                           string
	switchArgument, currencyString                                           string
	fracHolder, fracPadding, intHolder, intPadding, expBaseLen               int
	percent                                                                  int
	useCommaSep, useFraction, usePointer, usePositive, useScientificNotation bool
}

// CultureName is the type of supported language country codes types for apply
// number format.
type CultureName byte

// This section defines the currently supported country code types enumeration
// for apply number format.
const (
	CultureNameUnknown CultureName = iota
	CultureNameEnUS
	CultureNameJaJP
	CultureNameKoKR
	CultureNameZhCN
	CultureNameZhTW
)

var (
	// Excel styles can reference number formats that are built-in, all of which
	// have an id less than 164. Note that this number format code list is under
	// English localization.
	builtInNumFmt = map[int]string{
		0:  "general",
		1:  "0",
		2:  "0.00",
		3:  "#,##0",
		4:  "#,##0.00",
		9:  "0%",
		10: "0.00%",
		11: "0.00E+00",
		12: "# ?/?",
		13: "# ??/??",
		14: "mm-dd-yy",
		15: "d-mmm-yy",
		16: "d-mmm",
		17: "mmm-yy",
		18: "h:mm AM/PM",
		19: "h:mm:ss AM/PM",
		20: "hh:mm",
		21: "hh:mm:ss",
		22: "m/d/yy hh:mm",
		37: "#,##0 ;(#,##0)",
		38: "#,##0 ;[red](#,##0)",
		39: "#,##0.00 ;(#,##0.00)",
		40: "#,##0.00 ;[red](#,##0.00)",
		41: "_(* #,##0_);_(* \\(#,##0\\);_(* \"-\"_);_(@_)",
		42: "_(\"$\"* #,##0_);_(\"$\"* \\(#,##0\\);_(\"$\"* \"-\"_);_(@_)",
		43: "_(* #,##0.00_);_(* \\(#,##0.00\\);_(* \"-\"??_);_(@_)",
		44: "_(\"$\"* #,##0.00_);_(\"$\"* \\(#,##0.00\\);_(\"$\"* \"-\"??_);_(@_)",
		45: "mm:ss",
		46: "[h]:mm:ss",
		47: "mm:ss.0",
		48: "##0.0E+0",
		49: "@",
	}
	// langNumFmt defined number format code provided for language glyphs where
	// they occur in different language.
	langNumFmt = map[string]map[int]string{
		"zh-tw": {
			27: "[$-404]e/m/d",
			28: "[$-404]e\"年\"m\"月\"d\"日\"",
			29: "[$-404]e\"年\"m\"月\"d\"日\"",
			30: "m/d/yy",
			31: "yyyy\"年\"m\"月\"d\"日\"",
			32: "hh\"時\"mm\"分\"",
			33: "hh\"時\"mm\"分\"ss\"秒\"",
			34: "上午/下午hh\"時\"mm\"分\"",
			35: "上午/下午hh\"時\"mm\"分\"ss\"秒\"",
			36: "[$-404]e/m/d",
			50: "[$-404]e/m/d",
			51: "[$-404]e\"年\"m\"月\"d\"日\"",
			52: "上午/下午hh\"時\"mm\"分\"",
			53: "上午/下午hh\"時\"mm\"分\"ss\"秒\"",
			54: "[$-404]e\"年\"m\"月\"d\"日\"",
			55: "上午/下午hh\"時\"mm\"分\"",
			56: "上午/下午hh\"時\"mm\"分\"ss\"秒\"",
			57: "[$-404]e/m/d",
			58: "[$-404]e\"年\"m\"月\"d\"日\"",
		},
		"zh-cn": {
			27: "yyyy\"年\"m\"月\"",
			28: "m\"月\"d\"日\"",
			29: "m\"月\"d\"日\"",
			30: "m/d/yy",
			31: "yyyy\"年\"m\"月\"d\"日\"",
			32: "h\"时\"mm\"分\"",
			33: "h\"时\"mm\"分\"ss\"秒\"",
			34: "上午/下午h\"时\"mm\"分\"",
			35: "上午/下午h\"时\"mm\"分\"ss\"秒\"",
			36: "yyyy\"年\"m\"月\"",
			50: "yyyy\"年\"m\"月\"",
			51: "m\"月\"d\"日\"",
			52: "yyyy\"年\"m\"月\"",
			53: "m\"月\"d\"日\"",
			54: "m\"月\"d\"日\"",
			55: "上午/下午h\"时\"mm\"分\"",
			56: "上午/下午h\"时\"mm\"分\"ss\"秒\"",
			57: "yyyy\"年\"m\"月\"",
			58: "m\"月\"d\"日\"",
		},
		"ja-jp": {
			27: "[$-411]ge.m.d",
			28: "[$-411]ggge\"年\"m\"月\"d\"日\"",
			29: "[$-411]ggge\"年\"m\"月\"d\"日\"",
			30: "m/d/yy",
			31: "yyyy\"年\"m\"月\"d\"日\"",
			32: "h\"時\"mm\"分\"",
			33: "h\"時\"mm\"分\"ss\"秒\"",
			34: "yyyy\"年\"m\"月\"",
			35: "m\"月\"d\"日\"",
			36: "[$-411]ge.m.d",
			50: "[$-411]ge.m.d",
			51: "[$-411]ggge\"年\"m\"月\"d\"日\"",
			52: "yyyy\"年\"m\"月\"",
			53: "m\"月\"d\"日\"",
			54: "[$-411]ggge\"年\"m\"月\"d\"日\"",
			55: "yyyy\"年\"m\"月\"",
			56: "m\"月\"d\"日\"",
			57: "[$-411]ge.m.d",
			58: "[$-411]ggge\"年\"m\"月\"d\"日\"",
		},
		"ko-kr": {
			27: "yyyy\"年\" mm\"月\" dd\"日\"",
			28: "mm-dd",
			29: "mm-dd",
			30: "mm-dd-yy",
			31: "yyyy\"년\" mm\"월\" dd\"일\"",
			32: "h\"시\" mm\"분\"",
			33: "h\"시\" mm\"분\" ss\"초\"",
			34: "yyyy-mm-dd",
			35: "yyyy-mm-dd",
			36: "yyyy\"年\" mm\"月\" dd\"日\"",
			50: "yyyy\"年\" mm\"月\" dd\"日\"",
			51: "mm-dd",
			52: "yyyy-mm-dd",
			53: "yyyy-mm-dd",
			54: "mm-dd",
			55: "yyyy-mm-dd",
			56: "yyyy-mm-dd",
			57: "yyyy\"年\" mm\"月\" dd\"日\"",
			58: "mm-dd",
		},
		"th-th": {
			59: "t0",
			60: "t0.00",
			61: "t#,##0",
			62: "t#,##0.00",
			67: "t0%",
			68: "t0.00%",
			69: "t# ?/?",
			70: "t# ??/??",
			71: "\u0E27/\u0E14/\u0E1B\u0E1B\u0E1B\u0E1B",
			72: "\u0E27-\u0E14\u0E14\u0E14-\u0E1B\u0E1B",
			73: "\u0E27-\u0E14\u0E14\u0E14",
			74: "\u0E14\u0E14\u0E14-\u0E1B\u0E1B",
			75: "\u0E0A:\u0E19\u0E19",
			76: "\u0E0A:\u0E19\u0E19:\u0E17\u0E17",
			77: "\u0E27/\u0E14/\u0E1B\u0E1B\u0E1B\u0E1B \u0E0A:\u0E19\u0E19",
			78: "\u0E19\u0E19:\u0E17\u0E17",
			79: "[\u0E0A%5D]\u0E19\u0E19:\u0E17\u0E17",
			80: "\u0E19\u0E19:\u0E17\u0E17.0",
			81: "d/m/bb",
		},
	}
	// currencyNumFmt defined the currency number format map.
	currencyNumFmt = map[int]string{
		164: "\"¥\"#,##0.00",
		165: "[$$-409]#,##0.00",
		166: "[$$-45C]#,##0.00",
		167: "[$$-1004]#,##0.00",
		168: "[$$-404]#,##0.00",
		169: "[$$-C09]#,##0.00",
		170: "[$$-2809]#,##0.00",
		171: "[$$-1009]#,##0.00",
		172: "[$$-2009]#,##0.00",
		173: "[$$-1409]#,##0.00",
		174: "[$$-4809]#,##0.00",
		175: "[$$-2C09]#,##0.00",
		176: "[$$-2409]#,##0.00",
		177: "[$$-1000]#,##0.00",
		178: "#,##0.00\\ [$$-C0C]",
		179: "[$$-475]#,##0.00",
		180: "[$$-83E]#,##0.00",
		181: "[$$-86B]\\ #,##0.00",
		182: "[$$-340A]\\ #,##0.00",
		183: "[$$-240A]#,##0.00",
		184: "[$$-300A]\\ #,##0.00",
		185: "[$$-440A]#,##0.00",
		186: "[$$-80A]#,##0.00",
		187: "[$$-500A]#,##0.00",
		188: "[$$-540A]#,##0.00",
		189: "[$$-380A]\\ #,##0.00",
		190: "[$£-809]#,##0.00",
		191: "[$£-491]#,##0.00",
		192: "[$£-452]#,##0.00",
		193: "[$¥-804]#,##0.00",
		194: "[$¥-411]#,##0.00",
		195: "[$¥-478]#,##0.00",
		196: "[$¥-451]#,##0.00",
		197: "[$¥-480]#,##0.00",
		198: "#,##0.00\\ [$\u058F-42B]",
		199: "[$\u060B-463]#,##0.00",
		200: "[$\u060B-48C]#,##0.00",
		201: "[$\u09F3-845]\\ #,##0.00",
		202: "#,##0.00[$\u17DB-453]",
		203: "[$\u20A1-140A]#,##0.00",
		204: "[$\u20A6-468]\\ #,##0.00",
		205: "[$\u20A6-470]\\ #,##0.00",
		206: "[$\u20A9-412]#,##0.00",
		207: "[$\u20AA-40D]\\ #,##0.00",
		208: "#,##0.00\\ [$\u20AB-42A]",
		209: "#,##0.00\\ [$\u20AC-42D]",
		210: "#,##0.00\\ [$\u20AC-47E]",
		211: "#,##0.00\\ [$\u20AC-403]",
		212: "#,##0.00\\ [$\u20AC-483]",
		213: "[$\u20AC-813]\\ #,##0.00",
		214: "[$\u20AC-413]\\ #,##0.00",
		215: "[$\u20AC-1809]#,##0.00",
		216: "#,##0.00\\ [$\u20AC-425]",
		217: "[$\u20AC-2]\\ #,##0.00",
		218: "#,##0.00\\ [$\u20AC-1]",
		219: "#,##0.00\\ [$\u20AC-40B]",
		220: "#,##0.00\\ [$\u20AC-80C]",
		221: "#,##0.00\\ [$\u20AC-40C]",
		222: "#,##0.00\\ [$\u20AC-140C]",
		223: "#,##0.00\\ [$\u20AC-180C]",
		224: "[$\u20AC-200C]#,##0.00",
		225: "#,##0.00\\ [$\u20AC-456]",
		226: "#,##0.00\\ [$\u20AC-C07]",
		227: "#,##0.00\\ [$\u20AC-407]",
		228: "#,##0.00\\ [$\u20AC-1007]",
		229: "#,##0.00\\ [$\u20AC-408]",
		230: "#,##0.00\\ [$\u20AC-243B]",
		231: "[$\u20AC-83C]#,##0.00",
		232: "[$\u20AC-410]\\ #,##0.00",
		233: "[$\u20AC-476]#,##0.00",
		234: "#,##0.00\\ [$\u20AC-2C1A]",
		235: "[$\u20AC-426]\\ #,##0.00",
		236: "#,##0.00\\ [$\u20AC-427]",
		237: "#,##0.00\\ [$\u20AC-82E]",
		238: "#,##0.00\\ [$\u20AC-46E]",
		239: "[$\u20AC-43A]#,##0.00",
		240: "#,##0.00\\ [$\u20AC-C3B]",
		241: "#,##0.00\\ [$\u20AC-482]",
		242: "#,##0.00\\ [$\u20AC-816]",
		243: "#,##0.00\\ [$\u20AC-301A]",
		244: "#,##0.00\\ [$\u20AC-203B]",
		245: "#,##0.00\\ [$\u20AC-41B]",
		246: "#,##0.00\\ [$\u20AC-424]",
		247: "#,##0.00\\ [$\u20AC-C0A]",
		248: "#,##0.00\\ [$\u20AC-81D]",
		249: "#,##0.00\\ [$\u20AC-484]",
		250: "#,##0.00\\ [$\u20AC-42E]",
		251: "[$\u20AC-462]\\ #,##0.00",
		252: "#,##0.00\\ [$₭-454]",
		253: "#,##0.00\\ [$₮-450]",
		254: "[$\u20AE-C50]#,##0.00",
		255: "[$\u20B1-3409]#,##0.00",
		256: "[$\u20B1-464]#,##0.00",
		257: "#,##0.00[$\u20B4-422]",
		258: "[$\u20B8-43F]#,##0.00",
		259: "[$\u20B9-460]#,##0.00",
		260: "[$\u20B9-4009]\\ #,##0.00",
		261: "[$\u20B9-447]\\ #,##0.00",
		262: "[$\u20B9-439]\\ #,##0.00",
		263: "[$\u20B9-44B]\\ #,##0.00",
		264: "[$\u20B9-860]#,##0.00",
		265: "[$\u20B9-457]\\ #,##0.00",
		266: "[$\u20B9-458]#,##0.00",
		267: "[$\u20B9-44E]\\ #,##0.00",
		268: "[$\u20B9-861]#,##0.00",
		269: "[$\u20B9-448]\\ #,##0.00",
		270: "[$\u20B9-446]\\ #,##0.00",
		271: "[$\u20B9-44F]\\ #,##0.00",
		272: "[$\u20B9-459]#,##0.00",
		273: "[$\u20B9-449]\\ #,##0.00",
		274: "[$\u20B9-820]#,##0.00",
		275: "#,##0.00\\ [$\u20BA-41F]",
		276: "#,##0.00\\ [$\u20BC-42C]",
		277: "#,##0.00\\ [$\u20BC-82C]",
		278: "#,##0.00\\ [$\u20BD-419]",
		279: "#,##0.00[$\u20BD-485]",
		280: "#,##0.00\\ [$\u20BE-437]",
		281: "[$B/.-180A]\\ #,##0.00",
		282: "[$Br-472]#,##0.00",
		283: "[$Br-477]#,##0.00",
		284: "#,##0.00[$Br-473]",
		285: "[$Bs-46B]\\ #,##0.00",
		286: "[$Bs-400A]\\ #,##0.00",
		287: "[$Bs.-200A]\\ #,##0.00",
		288: "[$BWP-832]\\ #,##0.00",
		289: "[$C$-4C0A]#,##0.00",
		290: "[$CA$-85D]#,##0.00",
		291: "[$CA$-47C]#,##0.00",
		292: "[$CA$-45D]#,##0.00",
		293: "[$CFA-340C]#,##0.00",
		294: "[$CFA-280C]#,##0.00",
		295: "#,##0.00\\ [$CFA-867]",
		296: "#,##0.00\\ [$CFA-488]",
		297: "#,##0.00\\ [$CHF-100C]",
		298: "[$CHF-1407]\\ #,##0.00",
		299: "[$CHF-807]\\ #,##0.00",
		300: "[$CHF-810]\\ #,##0.00",
		301: "[$CHF-417]\\ #,##0.00",
		302: "[$CLP-47A]\\ #,##0.00",
		303: "[$CN¥-850]#,##0.00",
		304: "#,##0.00\\ [$DZD-85F]",
		305: "[$FCFA-2C0C]#,##0.00",
		306: "#,##0.00\\ [$Ft-40E]",
		307: "[$G-3C0C]#,##0.00",
		308: "[$Gs.-3C0A]\\ #,##0.00",
		309: "[$GTQ-486]#,##0.00",
		310: "[$HK$-C04]#,##0.00",
		311: "[$HK$-3C09]#,##0.00",
		312: "#,##0.00\\ [$HRK-41A]",
		313: "[$IDR-3809]#,##0.00",
		314: "[$IQD-492]#,##0.00",
		315: "#,##0.00\\ [$ISK-40F]",
		316: "[$K-455]#,##0.00",
		317: "#,##0.00\\ [$K\u010D-405]",
		318: "#,##0.00\\ [$KM-141A]",
		319: "#,##0.00\\ [$KM-101A]",
		320: "#,##0.00\\ [$KM-181A]",
		321: "[$kr-438]\\ #,##0.00",
		322: "[$kr-43B]\\ #,##0.00",
		323: "#,##0.00\\ [$kr-83B]",
		324: "[$kr-414]\\ #,##0.00",
		325: "[$kr-814]\\ #,##0.00",
		326: "#,##0.00\\ [$kr-41D]",
		327: "[$kr.-406]\\ #,##0.00",
		328: "[$kr.-46F]\\ #,##0.00",
		329: "[$Ksh-441]#,##0.00",
		330: "[$L-818]#,##0.00",
		331: "[$L-819]#,##0.00",
		332: "[$L-480A]\\ #,##0.00",
		333: "#,##0.00\\ [$Lek\u00EB-41C]",
		334: "[$MAD-45F]#,##0.00",
		335: "[$MAD-380C]#,##0.00",
		336: "#,##0.00\\ [$MAD-105F]",
		337: "[$MOP$-1404]#,##0.00",
		338: "#,##0.00\\ [$MVR-465]_-",
		339: "#,##0.00[$Nfk-873]",
		340: "[$NGN-466]#,##0.00",
		341: "[$NGN-467]#,##0.00",
		342: "[$NGN-469]#,##0.00",
		343: "[$NGN-471]#,##0.00",
		344: "[$NOK-103B]\\ #,##0.00",
		345: "[$NOK-183B]\\ #,##0.00",
		346: "[$NZ$-481]#,##0.00",
		347: "[$PKR-859]\\ #,##0.00",
		348: "[$PYG-474]#,##0.00",
		349: "[$Q-100A]#,##0.00",
		350: "[$R-436]\\ #,##0.00",
		351: "[$R-1C09]\\ #,##0.00",
		352: "[$R-435]\\ #,##0.00",
		353: "[$R$-416]\\ #,##0.00",
		354: "[$RD$-1C0A]#,##0.00",
		355: "#,##0.00\\ [$RF-487]",
		356: "[$RM-4409]#,##0.00",
		357: "[$RM-43E]#,##0.00",
		358: "#,##0.00\\ [$RON-418]",
		359: "[$Rp-421]#,##0.00",
		360: "[$Rs-420]#,##0.00_-",
		361: "[$Rs.-849]\\ #,##0.00",
		362: "#,##0.00\\ [$RSD-81A]",
		363: "#,##0.00\\ [$RSD-C1A]",
		364: "#,##0.00\\ [$RUB-46D]",
		365: "#,##0.00\\ [$RUB-444]",
		366: "[$S/.-C6B]\\ #,##0.00",
		367: "[$S/.-280A]\\ #,##0.00",
		368: "#,##0.00\\ [$SEK-143B]",
		369: "#,##0.00\\ [$SEK-1C3B]",
		370: "#,##0.00\\ [$so\u02BBm-443]",
		371: "#,##0.00\\ [$so\u02BBm-843]",
		372: "#,##0.00\\ [$SYP-45A]",
		373: "[$THB-41E]#,##0.00",
		374: "#,##0.00[$TMT-442]",
		375: "[$US$-3009]#,##0.00",
		376: "[$ZAR-46C]\\ #,##0.00",
		377: "[$ZAR-430]#,##0.00",
		378: "[$ZAR-431]#,##0.00",
		379: "[$ZAR-432]\\ #,##0.00",
		380: "[$ZAR-433]#,##0.00",
		381: "[$ZAR-434]\\ #,##0.00",
		382: "#,##0.00\\ [$z\u0142-415]",
		383: "#,##0.00\\ [$\u0434\u0435\u043D-42F]",
		384: "#,##0.00\\ [$КМ-201A]",
		385: "#,##0.00\\ [$КМ-1C1A]",
		386: "#,##0.00\\ [$\u043B\u0432.-402]",
		387: "#,##0.00\\ [$р.-423]",
		388: "#,##0.00\\ [$\u0441\u043E\u043C-440]",
		389: "#,##0.00\\ [$\u0441\u043E\u043C-428]",
		390: "[$\u062C.\u0645.-C01]\\ #,##0.00_-",
		391: "[$\u062F.\u0623.-2C01]\\ #,##0.00_-",
		392: "[$\u062F.\u0625.-3801]\\ #,##0.00_-",
		393: "[$\u062F.\u0628.-3C01]\\ #,##0.00_-",
		394: "[$\u062F.\u062A.-1C01]\\ #,##0.00_-",
		395: "[$\u062F.\u062C.-1401]\\ #,##0.00_-",
		396: "[$\u062F.\u0639.-801]\\ #,##0.00_-",
		397: "[$\u062F.\u0643.-3401]\\ #,##0.00_-",
		398: "[$\u062F.\u0644.-1001]#,##0.00_-",
		399: "[$\u062F.\u0645.-1801]\\ #,##0.00_-",
		400: "[$\u0631-846]\\ #,##0.00",
		401: "[$\u0631.\u0633.-401]\\ #,##0.00_-",
		402: "[$\u0631.\u0639.-2001]\\ #,##0.00_-",
		403: "[$\u0631.\u0642.-4001]\\ #,##0.00_-",
		404: "[$\u0631.\u064A.-2401]\\ #,##0.00_-",
		405: "[$\u0631\u06CC\u0627\u0644-429]#,##0.00_-",
		406: "[$\u0644.\u0633.-2801]\\ #,##0.00_-",
		407: "[$\u0644.\u0644.-3001]\\ #,##0.00_-",
		408: "[$\u1265\u122D-45E]#,##0.00",
		409: "[$\u0930\u0942-461]#,##0.00",
		410: "[$\u0DBB\u0DD4.-45B]\\ #,##0.00",
		411: "[$ADP]\\ #,##0.00",
		412: "[$AED]\\ #,##0.00",
		413: "[$AFA]\\ #,##0.00",
		414: "[$AFN]\\ #,##0.00",
		415: "[$ALL]\\ #,##0.00",
		416: "[$AMD]\\ #,##0.00",
		417: "[$ANG]\\ #,##0.00",
		418: "[$AOA]\\ #,##0.00",
		419: "[$ARS]\\ #,##0.00",
		420: "[$ATS]\\ #,##0.00",
		421: "[$AUD]\\ #,##0.00",
		422: "[$AWG]\\ #,##0.00",
		423: "[$AZM]\\ #,##0.00",
		424: "[$AZN]\\ #,##0.00",
		425: "[$BAM]\\ #,##0.00",
		426: "[$BBD]\\ #,##0.00",
		427: "[$BDT]\\ #,##0.00",
		428: "[$BEF]\\ #,##0.00",
		429: "[$BGL]\\ #,##0.00",
		430: "[$BGN]\\ #,##0.00",
		431: "[$BHD]\\ #,##0.00",
		432: "[$BIF]\\ #,##0.00",
		433: "[$BMD]\\ #,##0.00",
		434: "[$BND]\\ #,##0.00",
		435: "[$BOB]\\ #,##0.00",
		436: "[$BOV]\\ #,##0.00",
		437: "[$BRL]\\ #,##0.00",
		438: "[$BSD]\\ #,##0.00",
		439: "[$BTN]\\ #,##0.00",
		440: "[$BWP]\\ #,##0.00",
		441: "[$BYR]\\ #,##0.00",
		442: "[$BZD]\\ #,##0.00",
		443: "[$CAD]\\ #,##0.00",
		444: "[$CDF]\\ #,##0.00",
		445: "[$CHE]\\ #,##0.00",
		446: "[$CHF]\\ #,##0.00",
		447: "[$CHW]\\ #,##0.00",
		448: "[$CLF]\\ #,##0.00",
		449: "[$CLP]\\ #,##0.00",
		450: "[$CNY]\\ #,##0.00",
		451: "[$COP]\\ #,##0.00",
		452: "[$COU]\\ #,##0.00",
		453: "[$CRC]\\ #,##0.00",
		454: "[$CSD]\\ #,##0.00",
		455: "[$CUC]\\ #,##0.00",
		456: "[$CVE]\\ #,##0.00",
		457: "[$CYP]\\ #,##0.00",
		458: "[$CZK]\\ #,##0.00",
		459: "[$DEM]\\ #,##0.00",
		460: "[$DJF]\\ #,##0.00",
		461: "[$DKK]\\ #,##0.00",
		462: "[$DOP]\\ #,##0.00",
		463: "[$DZD]\\ #,##0.00",
		464: "[$ECS]\\ #,##0.00",
		465: "[$ECV]\\ #,##0.00",
		466: "[$EEK]\\ #,##0.00",
		467: "[$EGP]\\ #,##0.00",
		468: "[$ERN]\\ #,##0.00",
		469: "[$ESP]\\ #,##0.00",
		470: "[$ETB]\\ #,##0.00",
		471: "[$EUR]\\ #,##0.00",
		472: "[$FIM]\\ #,##0.00",
		473: "[$FJD]\\ #,##0.00",
		474: "[$FKP]\\ #,##0.00",
		475: "[$FRF]\\ #,##0.00",
		476: "[$GBP]\\ #,##0.00",
		477: "[$GEL]\\ #,##0.00",
		478: "[$GHC]\\ #,##0.00",
		479: "[$GHS]\\ #,##0.00",
		480: "[$GIP]\\ #,##0.00",
		481: "[$GMD]\\ #,##0.00",
		482: "[$GNF]\\ #,##0.00",
		483: "[$GRD]\\ #,##0.00",
		484: "[$GTQ]\\ #,##0.00",
		485: "[$GYD]\\ #,##0.00",
		486: "[$HKD]\\ #,##0.00",
		487: "[$HNL]\\ #,##0.00",
		488: "[$HRK]\\ #,##0.00",
		489: "[$HTG]\\ #,##0.00",
		490: "[$HUF]\\ #,##0.00",
		491: "[$IDR]\\ #,##0.00",
		492: "[$IEP]\\ #,##0.00",
		493: "[$ILS]\\ #,##0.00",
		494: "[$INR]\\ #,##0.00",
		495: "[$IQD]\\ #,##0.00",
		496: "[$IRR]\\ #,##0.00",
		497: "[$ISK]\\ #,##0.00",
		498: "[$ITL]\\ #,##0.00",
		499: "[$JMD]\\ #,##0.00",
		500: "[$JOD]\\ #,##0.00",
		501: "[$JPY]\\ #,##0.00",
		502: "[$KAF]\\ #,##0.00",
		503: "[$KES]\\ #,##0.00",
		504: "[$KGS]\\ #,##0.00",
		505: "[$KHR]\\ #,##0.00",
		506: "[$KMF]\\ #,##0.00",
		507: "[$KPW]\\ #,##0.00",
		508: "[$KRW]\\ #,##0.00",
		509: "[$KWD]\\ #,##0.00",
		510: "[$KYD]\\ #,##0.00",
		511: "[$KZT]\\ #,##0.00",
		512: "[$LAK]\\ #,##0.00",
		513: "[$LBP]\\ #,##0.00",
		514: "[$LKR]\\ #,##0.00",
		515: "[$LRD]\\ #,##0.00",
		516: "[$LSL]\\ #,##0.00",
		517: "[$LTL]\\ #,##0.00",
		518: "[$LUF]\\ #,##0.00",
		519: "[$LVL]\\ #,##0.00",
		520: "[$LYD]\\ #,##0.00",
		521: "[$MAD]\\ #,##0.00",
		522: "[$MDL]\\ #,##0.00",
		523: "[$MGA]\\ #,##0.00",
		524: "[$MGF]\\ #,##0.00",
		525: "[$MKD]\\ #,##0.00",
		526: "[$MMK]\\ #,##0.00",
		527: "[$MNT]\\ #,##0.00",
		528: "[$MOP]\\ #,##0.00",
		529: "[$MRO]\\ #,##0.00",
		530: "[$MTL]\\ #,##0.00",
		531: "[$MUR]\\ #,##0.00",
		532: "[$MVR]\\ #,##0.00",
		533: "[$MWK]\\ #,##0.00",
		534: "[$MXN]\\ #,##0.00",
		535: "[$MXV]\\ #,##0.00",
		536: "[$MYR]\\ #,##0.00",
		537: "[$MZM]\\ #,##0.00",
		538: "[$MZN]\\ #,##0.00",
		539: "[$NAD]\\ #,##0.00",
		540: "[$NGN]\\ #,##0.00",
		541: "[$NIO]\\ #,##0.00",
		542: "[$NLG]\\ #,##0.00",
		543: "[$NOK]\\ #,##0.00",
		544: "[$NPR]\\ #,##0.00",
		545: "[$NTD]\\ #,##0.00",
		546: "[$NZD]\\ #,##0.00",
		547: "[$OMR]\\ #,##0.00",
		548: "[$PAB]\\ #,##0.00",
		549: "[$PEN]\\ #,##0.00",
		550: "[$PGK]\\ #,##0.00",
		551: "[$PHP]\\ #,##0.00",
		552: "[$PKR]\\ #,##0.00",
		553: "[$PLN]\\ #,##0.00",
		554: "[$PTE]\\ #,##0.00",
		555: "[$PYG]\\ #,##0.00",
		556: "[$QAR]\\ #,##0.00",
		557: "[$ROL]\\ #,##0.00",
		558: "[$RON]\\ #,##0.00",
		559: "[$RSD]\\ #,##0.00",
		560: "[$RUB]\\ #,##0.00",
		561: "[$RUR]\\ #,##0.00",
		562: "[$RWF]\\ #,##0.00",
		563: "[$SAR]\\ #,##0.00",
		564: "[$SBD]\\ #,##0.00",
		565: "[$SCR]\\ #,##0.00",
		566: "[$SDD]\\ #,##0.00",
		567: "[$SDG]\\ #,##0.00",
		568: "[$SDP]\\ #,##0.00",
		569: "[$SEK]\\ #,##0.00",
		570: "[$SGD]\\ #,##0.00",
		571: "[$SHP]\\ #,##0.00",
		572: "[$SIT]\\ #,##0.00",
		573: "[$SKK]\\ #,##0.00",
		574: "[$SLL]\\ #,##0.00",
		575: "[$SOS]\\ #,##0.00",
		576: "[$SPL]\\ #,##0.00",
		577: "[$SRD]\\ #,##0.00",
		578: "[$SRG]\\ #,##0.00",
		579: "[$STD]\\ #,##0.00",
		580: "[$SVC]\\ #,##0.00",
		581: "[$SYP]\\ #,##0.00",
		582: "[$SZL]\\ #,##0.00",
		583: "[$THB]\\ #,##0.00",
		584: "[$TJR]\\ #,##0.00",
		585: "[$TJS]\\ #,##0.00",
		586: "[$TMM]\\ #,##0.00",
		587: "[$TMT]\\ #,##0.00",
		588: "[$TND]\\ #,##0.00",
		589: "[$TOP]\\ #,##0.00",
		590: "[$TRL]\\ #,##0.00",
		591: "[$TRY]\\ #,##0.00",
		592: "[$TTD]\\ #,##0.00",
		593: "[$TWD]\\ #,##0.00",
		594: "[$TZS]\\ #,##0.00",
		595: "[$UAH]\\ #,##0.00",
		596: "[$UGX]\\ #,##0.00",
		597: "[$USD]\\ #,##0.00",
		598: "[$USN]\\ #,##0.00",
		599: "[$USS]\\ #,##0.00",
		600: "[$UYI]\\ #,##0.00",
		601: "[$UYU]\\ #,##0.00",
		602: "[$UZS]\\ #,##0.00",
		603: "[$VEB]\\ #,##0.00",
		604: "[$VEF]\\ #,##0.00",
		605: "[$VND]\\ #,##0.00",
		606: "[$VUV]\\ #,##0.00",
		607: "[$WST]\\ #,##0.00",
		608: "[$XAF]\\ #,##0.00",
		609: "[$XAG]\\ #,##0.00",
		610: "[$XAU]\\ #,##0.00",
		611: "[$XB5]\\ #,##0.00",
		612: "[$XBA]\\ #,##0.00",
		613: "[$XBB]\\ #,##0.00",
		614: "[$XBC]\\ #,##0.00",
		615: "[$XBD]\\ #,##0.00",
		616: "[$XCD]\\ #,##0.00",
		617: "[$XDR]\\ #,##0.00",
		618: "[$XFO]\\ #,##0.00",
		619: "[$XFU]\\ #,##0.00",
		620: "[$XOF]\\ #,##0.00",
		621: "[$XPD]\\ #,##0.00",
		622: "[$XPF]\\ #,##0.00",
		623: "[$XPT]\\ #,##0.00",
		624: "[$XTS]\\ #,##0.00",
		625: "[$XXX]\\ #,##0.00",
		626: "[$YER]\\ #,##0.00",
		627: "[$YUM]\\ #,##0.00",
		628: "[$ZAR]\\ #,##0.00",
		629: "[$ZMK]\\ #,##0.00",
		630: "[$ZMW]\\ #,##0.00",
		631: "[$ZWD]\\ #,##0.00",
		632: "[$ZWL]\\ #,##0.00",
		633: "[$ZWN]\\ #,##0.00",
		634: "[$ZWR]\\ #,##0.00",
	}
	// supportedTokenTypes list the supported number format token types currently.
	supportedTokenTypes = []string{
		nfp.TokenTypeAlignment,
		nfp.TokenSubTypeCurrencyString,
		nfp.TokenSubTypeLanguageInfo,
		nfp.TokenTypeColor,
		nfp.TokenTypeCurrencyLanguage,
		nfp.TokenTypeDateTimes,
		nfp.TokenTypeDecimalPoint,
		nfp.TokenTypeDenominator,
		nfp.TokenTypeDigitalPlaceHolder,
		nfp.TokenTypeElapsedDateTimes,
		nfp.TokenTypeExponential,
		nfp.TokenTypeFraction,
		nfp.TokenTypeGeneral,
		nfp.TokenTypeHashPlaceHolder,
		nfp.TokenTypeLiteral,
		nfp.TokenTypePercent,
		nfp.TokenTypeRepeatsChar,
		nfp.TokenTypeSwitchArgument,
		nfp.TokenTypeTextPlaceHolder,
		nfp.TokenTypeThousandsSeparator,
		nfp.TokenTypeZeroPlaceHolder,
	}
	// supportedNumberTokenTypes list the supported number token types.
	supportedNumberTokenTypes = []string{
		nfp.TokenTypeDenominator,
		nfp.TokenTypeDigitalPlaceHolder,
		nfp.TokenTypeExponential,
		nfp.TokenTypeFraction,
		nfp.TokenTypeHashPlaceHolder,
		nfp.TokenTypePercent,
		nfp.TokenTypeZeroPlaceHolder,
	}
	// supportedDateTimeTokenTypes list the supported date and time token types.
	supportedDateTimeTokenTypes = []string{
		nfp.TokenTypeDateTimes,
		nfp.TokenTypeElapsedDateTimes,
	}
	// supportedLanguageInfo directly maps the supported language decimal ID and tags.
	supportedLanguageInfo = map[int]languageInfo{
		54:    {tags: []string{"af"}, localMonth: localMonthsNameAfrikaans, apFmt: apFmtAfrikaans, weekdayNames: weekdayNamesAfrikaans, weekdayNamesAbbr: weekdayNamesAfrikaansAbbr},
		1078:  {tags: []string{"af-ZA"}, localMonth: localMonthsNameAfrikaans, apFmt: apFmtAfrikaans, weekdayNames: weekdayNamesAfrikaans, weekdayNamesAbbr: weekdayNamesAfrikaansAbbr},
		28:    {tags: []string{"sq"}, localMonth: localMonthsNameAlbanian, apFmt: apFmtAlbanian, weekdayNames: weekdayNamesAlbanian, weekdayNamesAbbr: weekdayNamesAlbanianAbbr},
		1052:  {tags: []string{"sq-AL"}, localMonth: localMonthsNameAlbanian, apFmt: apFmtAlbanian, weekdayNames: weekdayNamesAlbanian, weekdayNamesAbbr: weekdayNamesAlbanianAbbr},
		132:   {tags: []string{"gsw"}, localMonth: localMonthsNameAlsatian, apFmt: apFmtAlsatian, weekdayNames: weekdayNamesAlsatian, weekdayNamesAbbr: weekdayNamesAlsatianAbbr},
		1156:  {tags: []string{"gsw-FR"}, localMonth: localMonthsNameAlsatianFrance, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesAlsatianFrance, weekdayNamesAbbr: weekdayNamesAlsatianFranceAbbr},
		94:    {tags: []string{"am"}, localMonth: localMonthsNameAmharic, apFmt: apFmtAmharic, weekdayNames: weekdayNamesAmharic, weekdayNamesAbbr: weekdayNamesAmharicAbbr},
		1118:  {tags: []string{"am-ET"}, localMonth: localMonthsNameAmharic, apFmt: apFmtAmharic, weekdayNames: weekdayNamesAmharic, weekdayNamesAbbr: weekdayNamesAmharicAbbr},
		1:     {tags: []string{"ar"}, localMonth: localMonthsNameArabic, apFmt: apFmtArabic, weekdayNames: weekdayNamesArabic, weekdayNamesAbbr: weekdayNamesArabicAbbr},
		5121:  {tags: []string{"ar-DZ"}, localMonth: localMonthsNameArabic, apFmt: apFmtArabic, weekdayNames: weekdayNamesArabic, weekdayNamesAbbr: weekdayNamesArabicAbbr},
		15361: {tags: []string{"ar-BH"}, localMonth: localMonthsNameArabic, apFmt: apFmtArabic, weekdayNames: weekdayNamesArabic, weekdayNamesAbbr: weekdayNamesArabicAbbr},
		3073:  {tags: []string{"ar-EG"}, localMonth: localMonthsNameArabic, apFmt: apFmtArabic, weekdayNames: weekdayNamesArabic, weekdayNamesAbbr: weekdayNamesArabicAbbr},
		2049:  {tags: []string{"ar-IQ"}, localMonth: localMonthsNameArabicIraq, apFmt: apFmtArabic, weekdayNames: weekdayNamesArabic, weekdayNamesAbbr: weekdayNamesArabicAbbr},
		11265: {tags: []string{"ar-JO"}, localMonth: localMonthsNameArabicIraq, apFmt: apFmtArabic, weekdayNames: weekdayNamesArabic, weekdayNamesAbbr: weekdayNamesArabicAbbr},
		13313: {tags: []string{"ar-KW"}, localMonth: localMonthsNameArabic, apFmt: apFmtArabic, weekdayNames: weekdayNamesArabic, weekdayNamesAbbr: weekdayNamesArabicAbbr},
		12289: {tags: []string{"ar-LB"}, localMonth: localMonthsNameArabicIraq, apFmt: apFmtArabic, weekdayNames: weekdayNamesArabic, weekdayNamesAbbr: weekdayNamesArabicAbbr},
		6145:  {tags: []string{"ar-MA"}, localMonth: localMonthsNameArabic, apFmt: apFmtArabic, weekdayNames: weekdayNamesArabic, weekdayNamesAbbr: weekdayNamesArabicAbbr},
		8193:  {tags: []string{"ar-OM"}, localMonth: localMonthsNameArabic, apFmt: apFmtArabic, weekdayNames: weekdayNamesArabic, weekdayNamesAbbr: weekdayNamesArabicAbbr},
		16385: {tags: []string{"ar-QA"}, localMonth: localMonthsNameArabic, apFmt: apFmtArabic, weekdayNames: weekdayNamesArabic, weekdayNamesAbbr: weekdayNamesArabicAbbr},
		1025:  {tags: []string{"ar-SA"}, localMonth: localMonthsNameArabic, apFmt: apFmtArabic, weekdayNames: weekdayNamesArabic, weekdayNamesAbbr: weekdayNamesArabicAbbr},
		10241: {tags: []string{"ar-SY"}, localMonth: localMonthsNameArabicIraq, apFmt: apFmtArabic, weekdayNames: weekdayNamesArabic, weekdayNamesAbbr: weekdayNamesArabicAbbr},
		7169:  {tags: []string{"ar-TN"}, localMonth: localMonthsNameArabic, apFmt: apFmtArabic, weekdayNames: weekdayNamesArabic, weekdayNamesAbbr: weekdayNamesArabicAbbr},
		14337: {tags: []string{"ar-AE"}, localMonth: localMonthsNameArabic, apFmt: apFmtArabic, weekdayNames: weekdayNamesArabic, weekdayNamesAbbr: weekdayNamesArabicAbbr},
		9217:  {tags: []string{"ar-YE"}, localMonth: localMonthsNameArabic, apFmt: apFmtArabic, weekdayNames: weekdayNamesArabic, weekdayNamesAbbr: weekdayNamesArabicAbbr},
		43:    {tags: []string{"hy"}, localMonth: localMonthsNameArmenian, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesArmenian, weekdayNamesAbbr: weekdayNamesArmenianAbbr},
		1067:  {tags: []string{"hy-AM"}, localMonth: localMonthsNameArmenian, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesArmenian, weekdayNamesAbbr: weekdayNamesArmenianAbbr},
		77:    {tags: []string{"as"}, localMonth: localMonthsNameAssamese, apFmt: apFmtAssamese, weekdayNames: weekdayNamesAssamese, weekdayNamesAbbr: weekdayNamesAssameseAbbr},
		1101:  {tags: []string{"as-IN"}, localMonth: localMonthsNameAssamese, apFmt: apFmtAssamese, weekdayNames: weekdayNamesAssamese, weekdayNamesAbbr: weekdayNamesAssameseAbbr},
		29740: {tags: []string{"az-Cyrl"}, localMonth: localMonthsNameAzerbaijaniCyrillic, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesAzerbaijaniCyrillic, weekdayNamesAbbr: weekdayNamesAzerbaijaniCyrillicAbbr},
		2092:  {tags: []string{"az-Cyrl-AZ"}, localMonth: localMonthsNameAzerbaijaniCyrillic, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesAzerbaijaniCyrillic, weekdayNamesAbbr: weekdayNamesAzerbaijaniCyrillicAbbr},
		44:    {tags: []string{"az"}, localMonth: localMonthsNameAzerbaijani, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesAzerbaijani, weekdayNamesAbbr: weekdayNamesAzerbaijaniAbbr},
		30764: {tags: []string{"az-Latn"}, localMonth: localMonthsNameAzerbaijani, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesAzerbaijani, weekdayNamesAbbr: weekdayNamesAzerbaijaniAbbr},
		1068:  {tags: []string{"az-Latn-AZ"}, localMonth: localMonthsNameAzerbaijani, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesAzerbaijani, weekdayNamesAbbr: weekdayNamesAzerbaijaniAbbr},
		69:    {tags: []string{"bn"}, localMonth: localMonthsNameBangla, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesBangla, weekdayNamesAbbr: weekdayNamesBanglaAbbr},
		2117:  {tags: []string{"bn-BD"}, localMonth: localMonthsNameBangla, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesBangla, weekdayNamesAbbr: weekdayNamesBanglaAbbr},
		1093:  {tags: []string{"bn-IN"}, localMonth: localMonthsNameBangla, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesBangla, weekdayNamesAbbr: weekdayNamesBanglaAbbr},
		109:   {tags: []string{"ba"}, localMonth: localMonthsNameBashkir, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesBashkir, weekdayNamesAbbr: weekdayNamesBashkirAbbr},
		1133:  {tags: []string{"ba-RU"}, localMonth: localMonthsNameBashkir, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesBashkir, weekdayNamesAbbr: weekdayNamesBashkirAbbr},
		45:    {tags: []string{"eu"}, localMonth: localMonthsNameBasque, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesBasque, weekdayNamesAbbr: weekdayNamesBasqueAbbr},
		1069:  {tags: []string{"eu-ES"}, localMonth: localMonthsNameBasque, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesBasque, weekdayNamesAbbr: weekdayNamesBasqueAbbr},
		35:    {tags: []string{"be"}, localMonth: localMonthsNameBelarusian, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesBelarusian, weekdayNamesAbbr: weekdayNamesBelarusianAbbr},
		1059:  {tags: []string{"be-BY"}, localMonth: localMonthsNameBelarusian, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesBelarusian, weekdayNamesAbbr: weekdayNamesBelarusianAbbr},
		25626: {tags: []string{"bs-Cyrl"}, localMonth: localMonthsNameBosnianCyrillic, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesBosnianCyrillic, weekdayNamesAbbr: weekdayNamesBosnianCyrillicAbbr},
		8218:  {tags: []string{"bs-Cyrl-BA"}, localMonth: localMonthsNameBosnianCyrillic, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesBosnianCyrillic, weekdayNamesAbbr: weekdayNamesBosnianCyrillicAbbr},
		26650: {tags: []string{"bs-Latn"}, localMonth: localMonthsNameBosnian, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesBosnian, weekdayNamesAbbr: weekdayNamesBosnianAbbr},
		30746: {tags: []string{"bs"}, localMonth: localMonthsNameBosnian, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesBosnian, weekdayNamesAbbr: weekdayNamesBosnianAbbr},
		5146:  {tags: []string{"bs-Latn-BA"}, localMonth: localMonthsNameBosnian, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesBosnian, weekdayNamesAbbr: weekdayNamesBosnianAbbr},
		126:   {tags: []string{"br"}, localMonth: localMonthsNameBreton, apFmt: apFmtBreton, weekdayNames: weekdayNamesBreton, weekdayNamesAbbr: weekdayNamesBretonAbbr},
		1150:  {tags: []string{"br-FR"}, localMonth: localMonthsNameBreton, apFmt: apFmtBreton, weekdayNames: weekdayNamesBreton, weekdayNamesAbbr: weekdayNamesBretonAbbr},
		2:     {tags: []string{"bg"}, localMonth: localMonthsNameBulgarian, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesBulgarian, weekdayNamesAbbr: weekdayNamesBulgarianAbbr},
		1026:  {tags: []string{"bg-BG"}, localMonth: localMonthsNameBulgarian, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesBulgarian, weekdayNamesAbbr: weekdayNamesBulgarianAbbr},
		85:    {tags: []string{"my"}, localMonth: localMonthsNameBurmese, apFmt: apFmtBurmese, weekdayNames: weekdayNamesBurmese, weekdayNamesAbbr: weekdayNamesBurmese},
		1109:  {tags: []string{"my-MM"}, localMonth: localMonthsNameBurmese, apFmt: apFmtBurmese, weekdayNames: weekdayNamesBurmese, weekdayNamesAbbr: weekdayNamesBurmese},
		3:     {tags: []string{"ca"}, localMonth: localMonthsNameValencian, apFmt: apFmtSpanishAR, weekdayNames: weekdayNamesValencian, weekdayNamesAbbr: weekdayNamesValencianAbbr},
		1027:  {tags: []string{"ca-ES"}, localMonth: localMonthsNameValencian, apFmt: apFmtSpanishAR, weekdayNames: weekdayNamesValencian, weekdayNamesAbbr: weekdayNamesValencianAbbr},
		1119:  {tags: []string{"tzm-Arab-MA"}, localMonth: localMonthsNameArabicIraq, apFmt: apFmtArabic, weekdayNames: weekdayNamesArabic, weekdayNamesAbbr: weekdayNamesArabicAbbr},
		146:   {tags: []string{"ku"}, localMonth: localMonthsNameCentralKurdish, apFmt: apFmtCentralKurdish, weekdayNames: weekdayNamesCentralKurdish, weekdayNamesAbbr: weekdayNamesCentralKurdish},
		31890: {tags: []string{"ku-Arab"}, localMonth: localMonthsNameCentralKurdish, apFmt: apFmtCentralKurdish, weekdayNames: weekdayNamesCentralKurdish, weekdayNamesAbbr: weekdayNamesCentralKurdish},
		1170:  {tags: []string{"ku-Arab-IQ"}, localMonth: localMonthsNameCentralKurdish, apFmt: apFmtCentralKurdish, weekdayNames: weekdayNamesCentralKurdish, weekdayNamesAbbr: weekdayNamesCentralKurdish},
		92:    {tags: []string{"chr"}, localMonth: localMonthsNameCherokee, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesCherokee, weekdayNamesAbbr: weekdayNamesCherokeeAbbr},
		31836: {tags: []string{"chr-Cher"}, localMonth: localMonthsNameCherokee, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesCherokee, weekdayNamesAbbr: weekdayNamesCherokeeAbbr},
		1116:  {tags: []string{"chr-Cher-US"}, localMonth: localMonthsNameCherokee, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesCherokee, weekdayNamesAbbr: weekdayNamesCherokeeAbbr},
		4:     {tags: []string{"zh-Hans"}, localMonth: localMonthsNameChinese1, apFmt: nfp.AmPm[2], weekdayNames: weekdayNamesChinese, weekdayNamesAbbr: weekdayNamesChineseAbbr2},
		30724: {tags: []string{"zh"}, localMonth: localMonthsNameChinese1, apFmt: nfp.AmPm[2], weekdayNames: weekdayNamesChinese, weekdayNamesAbbr: weekdayNamesChineseAbbr2},
		2052:  {tags: []string{"zh-CN"}, localMonth: localMonthsNameChinese1, apFmt: nfp.AmPm[2], weekdayNames: weekdayNamesChinese, weekdayNamesAbbr: weekdayNamesChineseAbbr},
		4100:  {tags: []string{"zh-SG"}, localMonth: localMonthsNameChinese2, apFmt: nfp.AmPm[2], weekdayNames: weekdayNamesChinese, weekdayNamesAbbr: weekdayNamesChineseAbbr},
		31748: {tags: []string{"zh-Hant"}, localMonth: localMonthsNameChinese3, apFmt: nfp.AmPm[2], weekdayNames: weekdayNamesChinese, weekdayNamesAbbr: weekdayNamesChineseAbbr2},
		3076:  {tags: []string{"zh-HK"}, localMonth: localMonthsNameChinese2, apFmt: nfp.AmPm[2], weekdayNames: weekdayNamesChinese, weekdayNamesAbbr: weekdayNamesChineseAbbr2},
		5124:  {tags: []string{"zh-MO"}, localMonth: localMonthsNameChinese3, apFmt: nfp.AmPm[2], weekdayNames: weekdayNamesChinese, weekdayNamesAbbr: weekdayNamesChineseAbbr2},
		1028:  {tags: []string{"zh-TW"}, localMonth: localMonthsNameChinese3, apFmt: nfp.AmPm[2], weekdayNames: weekdayNamesChinese, weekdayNamesAbbr: weekdayNamesChineseAbbr2, useGannen: true},
		9:     {tags: []string{"en"}, localMonth: localMonthsNameEnglish, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesEnglish, weekdayNamesAbbr: weekdayNamesEnglishAbbr},
		4096: {tags: []string{
			"aa", "aa-DJ", "aa-ER", "aa-ER", "aa-NA", "agq", "agq-CM", "ak", "ak-GH", "sq-ML",
			"gsw-LI", "gsw-CH", "ar-TD", "ar-KM", "ar-DJ", "ar-ER", "ar-IL", "ar-MR", "ar-PS",
			"ar-SO", "ar-SS", "ar-SD", "ar-001", "ast", "ast-ES", "asa", "asa-TZ", "ksf", "ksf-CM",
			"bm", "bm-Latn-ML", "bas", "bas-CM", "bem", "bem-ZM", "bez", "bez-TZ", "byn", "byn-ER",
			"brx", "brx-IN", "ca-AD", "ca-FR", "ca-IT", "ceb", "ceb-Latn", "ceb-Latn-PH", "tzm-Latn-MA",
			"ccp", "ccp-Cakm", "ccp-Cakm-BD", "ccp-Cakm-IN", "ce-RU", "cgg", "cgg-UG", "cu-RU", "swc",
			"swc-CD", "kw", "ke-GB", "da-GL", "dua", "dua-CM", "nl-AW", "nl-BQ", "nl-CW", "nl-SX",
			"nl-SR", "dz", "ebu", "ebu-KE", "en-AS", "en-AI", "en-AG", "en-AT", "en-BS", "en-BB",
			"en-BE", "en-BM", "en-BW", "en-IO", "en-VG", "en-BI", "en-CM", "en-KY", "en-CX", "en-CC",
			"en-CK", "en-CY", "en-DK", "en-DM", "en-ER", "en-150", "en-FK", "en-FI", "en-FJ", "en-GM",
			"en-DE", "en-GH", "en-GI", "en-GD", "en-GU", "en-GG", "en-GY", "en-IM", "en-IL", "en-JE",
			"en-KE", "en-KI", "en-LS", "en-LR", "en-MO", "en-MG", "en-MW", "en-MT", "en-MH", "en-MU",
			"en-FM", "en-MS", "en-NA", "en-NR", "en-NL", "en-NG", "en-NU", "en-NF", "en-MP", "en-PK",
			"en-PW", "en-PG", "en-PN", "en-PR", "en-RW", "en-KN", "en-LC", "en-VC", "en-WS", "en-SC",
			"en-SL", "en-SX", "en-SI", "en-SB", "en-SS", "en-SH", "en-SD", "en-SZ", "en-SE", "en-CH",
			"en-TZ", "en-TK", "en-TO", "en-TC", "en-TV", "en-UG", "en-UM", "en-VI", "en-VU", "en-001",
			"en-ZM", "eo", "eo-001", "ee", "ee-GH", "ee-TG", "ewo", "ewo-CM", "fo-DK", "fr-DZ",
			"fr-BJ", "fr-BF", "fr-BI", "fr-CF", "fr-TD", "fr-KM", "fr-CG", "fr-DJ", "fr-GQ", "fr-GF",
			"fr-PF", "fr-GA", "fr-GP", "fr-GN", "fr-MG", "fr-MQ", "fr-MR", "fr-MU", "fr-YT", "fr-NC",
			"fr-NE", "fr-RW", "fr-BL", "fr-MF", "fr-PM", "fr-SC", "fr-SY", "fr-TG", "fr-TN", "fr-VU",
			"fr-WF", "fur", "fur-IT", "ff-Latn-BF", "ff-CM", "ff-Latn-CM", "ff-Latn-GM", "ff-Latn-GH",
			"ff-GN", "ff-Latn-GN", "ff-Latn-GW", "ff-Latn-LR", "ff-MR", "ff-Latn-MR", "ff-Latn-NE",
			"ff-Latn-SL", "lg", "lg-UG", "de-BE", "de-IT", "el-CY", "guz", "guz-KE", "ha-Latn-GH",
			"ha-Latn-NG", "ia-FR", "ia-001", "it-SM", "it-VA", "jv", "jv-Latn", "jv-Latn-ID", "dyo",
			"dyo-SN", "kea", "kea-CV", "kab", "kab-DZ", "kkj", "kkj-CM", "kln", "kln-KE", "kam",
			"kam-KE", "ks-Arab-IN", "ki", "ki-KE", "sw-TZ", "sw-UG", "ko-KP", "khq", "khq-ML", "ses",
			"ses-ML", "nmg", "nmq-CM", "ku-Arab-IR", "lkt", "lkt-US", "lag", "lag-TZ", "ln", "ln-AO",
			"ln-CF", "ln-CD", "nds", "nds-DE", "nds-NL", "lu", "lu-CD", "luo", "luo", "luo-KE", "luy",
			"luy-KE", "jmc", "jmc-TZ", "mgh", "mgh-MZ", "kde", "kde-TZ", "mg", "mg-MG", "gv", "gv-IM",
			"mas", "mas-KE", "mas-TZ", "mas-IR", "mer", "mer-KE", "mgo", "mgo-CM", "mfe", "mfe-MU",
			"mua", "mua-CM", "nqo", "nqo-GN", "nqa", "naq-NA", "nnh", "nnh-CM", "jgo", "jgo-CM",
			"lrc-IQ", "lrc-IR", "nd", "nd-ZW", "nb-SJ", "nus", "nus-SD", "nus-SS", "nyn", "nyn-UG",
			"om-KE", "os", "os-GE", "os-RU", "ps-PK", "fa-AF", "pt-AO", "pt-CV", "pt-GQ", "pt-GW",
			"pt-LU", "pt-MO", "pt-MZ", "pt-ST", "pt-CH", "pt-TL", "prg-001", "ksh", "ksh-DE", "rof",
			"rof-TZ", "rn", "rn-BI", "ru-BY", "ru-KZ", "ru-KG", "ru-UA", "rwk", "rwk-TZ", "ssy",
			"ssy-ER", "saq", "saq-KE", "sg", "sq-CF", "sbp", "sbp-TZ", "seh", "seh-MZ", "ksb", "ksb-TZ",
			"sn", "sn-Latn", "sn-Latn-ZW", "xog", "xog-UG", "so-DJ", "so-ET", "so-KE", "nr", "nr-ZA",
			"st-LS", "es-BZ", "es-BR", "es-PH", "zgh", "zgh-Tfng-MA", "zgh-Tfng", "ss", "ss-ZA",
			"ss-SZ", "sv-AX", "shi", "shi-Tfng", "shi-Tfng-MA", "shi-Latn", "shi-Latn-MA", "dav",
			"dav-KE", "ta-MY", "ta-SG", "twq", "twq-NE", "teo", "teo-KE", "teo-UG", "bo-IN", "tig",
			"tig-ER", "to", "to-TO", "tr-CY", "uz-Arab", "us-Arab-AF", "vai", "vai-Vaii",
			"vai-Vaii-LR", "vai-Latn-LR", "vai-Latn", "vo", "vo-001", "vun", "vun-TZ", "wae",
			"wae-CH", "wal", "wae-ET", "yav", "yav-CM", "yo-BJ", "dje", "dje-NE",
		}, localMonth: localMonthsNameEnglish, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesEnglish, weekdayNamesAbbr: weekdayNamesEnglishAbbr},
		3081:  {tags: []string{"en-AU"}, localMonth: localMonthsNameEnglish, apFmt: strings.ToLower(nfp.AmPm[0]), weekdayNames: weekdayNamesEnglish, weekdayNamesAbbr: weekdayNamesEnglishAbbr},
		10249: {tags: []string{"en-BZ"}, localMonth: localMonthsNameEnglish, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesEnglish, weekdayNamesAbbr: weekdayNamesEnglishAbbr},
		4105:  {tags: []string{"en-CA"}, localMonth: localMonthsNameEnglish, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesEnglish, weekdayNamesAbbr: weekdayNamesEnglishAbbr},
		9225:  {tags: []string{"en-029"}, localMonth: localMonthsNameEnglish, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesEnglish, weekdayNamesAbbr: weekdayNamesEnglishAbbr},
		15369: {tags: []string{"en-HK"}, localMonth: localMonthsNameEnglish, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesEnglish, weekdayNamesAbbr: weekdayNamesEnglishAbbr},
		16393: {tags: []string{"en-IN"}, localMonth: localMonthsNameEnglish, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesEnglish, weekdayNamesAbbr: weekdayNamesEnglishAbbr},
		6153:  {tags: []string{"en-IE"}, localMonth: localMonthsNameEnglish, apFmt: strings.ToLower(nfp.AmPm[0]), weekdayNames: weekdayNamesEnglish, weekdayNamesAbbr: weekdayNamesEnglishAbbr},
		8201:  {tags: []string{"en-JM"}, localMonth: localMonthsNameEnglish, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesEnglish, weekdayNamesAbbr: weekdayNamesEnglishAbbr},
		17417: {tags: []string{"en-MY"}, localMonth: localMonthsNameEnglish, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesEnglish, weekdayNamesAbbr: weekdayNamesEnglishAbbr},
		5129:  {tags: []string{"en-NZ"}, localMonth: localMonthsNameEnglish, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesEnglish, weekdayNamesAbbr: weekdayNamesEnglishAbbr},
		13321: {tags: []string{"en-PH"}, localMonth: localMonthsNameEnglish, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesEnglish, weekdayNamesAbbr: weekdayNamesEnglishAbbr},
		18441: {tags: []string{"en-SG"}, localMonth: localMonthsNameEnglish, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesEnglish, weekdayNamesAbbr: weekdayNamesEnglishAbbr},
		7177:  {tags: []string{"en-ZA"}, localMonth: localMonthsNameEnglish, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesEnglish, weekdayNamesAbbr: weekdayNamesEnglishAbbr},
		11273: {tags: []string{"en-TT"}, localMonth: localMonthsNameEnglish, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesEnglish, weekdayNamesAbbr: weekdayNamesEnglishAbbr},
		19465: {tags: []string{"en-AE"}, localMonth: localMonthsNameEnglish, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesEnglish, weekdayNamesAbbr: weekdayNamesEnglishAbbr},
		2057:  {tags: []string{"en-GB"}, localMonth: localMonthsNameEnglish, apFmt: strings.ToLower(nfp.AmPm[0]), weekdayNames: weekdayNamesEnglish, weekdayNamesAbbr: weekdayNamesEnglishAbbr},
		1033:  {tags: []string{"en-US"}, localMonth: localMonthsNameEnglish, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesEnglish, weekdayNamesAbbr: weekdayNamesEnglishAbbr},
		12297: {tags: []string{"en-ZW"}, localMonth: localMonthsNameEnglish, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesEnglish, weekdayNamesAbbr: weekdayNamesEnglishAbbr},
		37:    {tags: []string{"et"}, localMonth: localMonthsNameEstonian, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesEstonian, weekdayNamesAbbr: weekdayNamesEstonianAbbr},
		1061:  {tags: []string{"et-EE"}, localMonth: localMonthsNameEstonian, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesEstonian, weekdayNamesAbbr: weekdayNamesEstonianAbbr},
		56:    {tags: []string{"fo"}, localMonth: localMonthsNameFaroese, apFmt: apFmtFaroese, weekdayNames: weekdayNamesFaroese, weekdayNamesAbbr: weekdayNamesFaroeseAbbr},
		1080:  {tags: []string{"fo-FO"}, localMonth: localMonthsNameFaroese, apFmt: apFmtFaroese, weekdayNames: weekdayNamesFaroese, weekdayNamesAbbr: weekdayNamesFaroeseAbbr},
		100:   {tags: []string{"fil"}, localMonth: localMonthsNameFilipino, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesFilipino, weekdayNamesAbbr: weekdayNamesFilipinoAbbr},
		1124:  {tags: []string{"fil-PH"}, localMonth: localMonthsNameFilipino, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesFilipino, weekdayNamesAbbr: weekdayNamesFilipinoAbbr},
		11:    {tags: []string{"fi"}, localMonth: localMonthsNameFinnish, apFmt: apFmtFinnish, weekdayNames: weekdayNamesFinnish, weekdayNamesAbbr: weekdayNamesFinnishAbbr},
		1035:  {tags: []string{"fi-FI"}, localMonth: localMonthsNameFinnish, apFmt: apFmtFinnish, weekdayNames: weekdayNamesFinnish, weekdayNamesAbbr: weekdayNamesFinnishAbbr},
		12:    {tags: []string{"fr"}, localMonth: localMonthsNameFrench, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesFrench, weekdayNamesAbbr: weekdayNamesFrenchAbbr},
		2060:  {tags: []string{"fr-BE"}, localMonth: localMonthsNameFrench, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesFrench, weekdayNamesAbbr: weekdayNamesFrenchAbbr},
		11276: {tags: []string{"fr-CM"}, localMonth: localMonthsNameFrench, apFmt: apFmtCameroon, weekdayNames: weekdayNamesFrench, weekdayNamesAbbr: weekdayNamesFrenchAbbr},
		3084:  {tags: []string{"fr-CA"}, localMonth: localMonthsNameFrench, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesFrench, weekdayNamesAbbr: weekdayNamesFrenchAbbr},
		7180:  {tags: []string{"fr-029"}, localMonth: localMonthsNameCaribbean, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesFrench, weekdayNamesAbbr: weekdayNamesFrenchAbbr},
		9228:  {tags: []string{"fr-CD"}, localMonth: localMonthsNameFrench, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesFrench, weekdayNamesAbbr: weekdayNamesFrenchAbbr},
		12300: {tags: []string{"fr-CI"}, localMonth: localMonthsNameFrench, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesFrench, weekdayNamesAbbr: weekdayNamesFrenchAbbr},
		1036:  {tags: []string{"fr-FR"}, localMonth: localMonthsNameFrench, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesFrench, weekdayNamesAbbr: weekdayNamesFrenchAbbr},
		15372: {tags: []string{"fr-HT"}, localMonth: localMonthsNameFrench, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesFrench, weekdayNamesAbbr: weekdayNamesFrenchAbbr},
		5132:  {tags: []string{"fr-LU"}, localMonth: localMonthsNameFrench, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesFrench, weekdayNamesAbbr: weekdayNamesFrenchAbbr},
		13324: {tags: []string{"fr-ML"}, localMonth: localMonthsNameFrench, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesFrench, weekdayNamesAbbr: weekdayNamesFrenchAbbr},
		14348: {tags: []string{"fr-MA"}, localMonth: localMonthsNameMorocco, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesFrench, weekdayNamesAbbr: weekdayNamesFrenchAbbr},
		6156:  {tags: []string{"fr-MC"}, localMonth: localMonthsNameFrench, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesFrench, weekdayNamesAbbr: weekdayNamesFrenchAbbr},
		8204:  {tags: []string{"fr-RE"}, localMonth: localMonthsNameFrench, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesFrench, weekdayNamesAbbr: weekdayNamesFrenchAbbr},
		10252: {tags: []string{"fr-SN"}, localMonth: localMonthsNameFrench, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesFrench, weekdayNamesAbbr: weekdayNamesFrenchAbbr},
		98:    {tags: []string{"fy"}, localMonth: localMonthsNameFrisian, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesFrisian, weekdayNamesAbbr: weekdayNamesFrisianAbbr},
		1122:  {tags: []string{"fy-NL"}, localMonth: localMonthsNameFrisian, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesFrisian, weekdayNamesAbbr: weekdayNamesFrisianAbbr},
		103:   {tags: []string{"ff"}, localMonth: localMonthsNameFulah, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesFulah, weekdayNamesAbbr: weekdayNamesFulahAbbr},
		31847: {tags: []string{"ff-Latn"}, localMonth: localMonthsNameFulah, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesFulah, weekdayNamesAbbr: weekdayNamesFulahAbbr},
		1127:  {tags: []string{"ff-NG", "ff-Latn-NG"}, localMonth: localMonthsNameNigeria, apFmt: apFmtNigeria, weekdayNames: weekdayNamesNigeria, weekdayNamesAbbr: weekdayNamesNigeriaAbbr},
		2151:  {tags: []string{"ff-SN"}, localMonth: localMonthsNameNigeria, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesNigeria, weekdayNamesAbbr: weekdayNamesNigeriaAbbr},
		86:    {tags: []string{"gl"}, localMonth: localMonthsNameGalician, apFmt: apFmtCuba, weekdayNames: weekdayNamesGalician, weekdayNamesAbbr: weekdayNamesGalicianAbbr},
		1110:  {tags: []string{"gl-ES"}, localMonth: localMonthsNameGalician, apFmt: apFmtCuba, weekdayNames: weekdayNamesGalician, weekdayNamesAbbr: weekdayNamesGalicianAbbr},
		55:    {tags: []string{"ka"}, localMonth: localMonthsNameGeorgian, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesGeorgian, weekdayNamesAbbr: weekdayNamesGeorgianAbbr},
		1079:  {tags: []string{"ka-GE"}, localMonth: localMonthsNameGeorgian, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesGeorgian, weekdayNamesAbbr: weekdayNamesGeorgianAbbr},
		7:     {tags: []string{"de"}, localMonth: localMonthsNameGerman, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesGerman, weekdayNamesAbbr: weekdayNamesGermanAbbr},
		3079:  {tags: []string{"de-AT"}, localMonth: localMonthsNameAustria, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesGerman, weekdayNamesAbbr: weekdayNamesGermanAbbr},
		1031:  {tags: []string{"de-DE"}, localMonth: localMonthsNameGerman, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesGerman, weekdayNamesAbbr: weekdayNamesGermanAbbr},
		5127:  {tags: []string{"de-LI"}, localMonth: localMonthsNameGerman, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesGerman, weekdayNamesAbbr: weekdayNamesGermanAbbr},
		2055:  {tags: []string{"de-CH"}, localMonth: localMonthsNameGerman, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesGerman, weekdayNamesAbbr: weekdayNamesGermanAbbr},
		8:     {tags: []string{"el"}, localMonth: localMonthsNameGreek, apFmt: apFmtGreek, weekdayNames: weekdayNamesGreek, weekdayNamesAbbr: weekdayNamesGreekAbbr},
		1032:  {tags: []string{"el-GR"}, localMonth: localMonthsNameGreek, apFmt: apFmtGreek, weekdayNames: weekdayNamesGreek, weekdayNamesAbbr: weekdayNamesGreekAbbr},
		111:   {tags: []string{"kl"}, localMonth: localMonthsNameGreenlandic, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesGreenlandic, weekdayNamesAbbr: weekdayNamesGreenlandicAbbr},
		1135:  {tags: []string{"kl-GL"}, localMonth: localMonthsNameGreenlandic, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesGreenlandic, weekdayNamesAbbr: weekdayNamesGreenlandicAbbr},
		116:   {tags: []string{"gn"}, localMonth: localMonthsNameGuarani, apFmt: apFmtCuba, weekdayNames: weekdayNamesGuarani, weekdayNamesAbbr: weekdayNamesGuaraniAbbr},
		1140:  {tags: []string{"gn-PY"}, localMonth: localMonthsNameGuarani, apFmt: apFmtCuba, weekdayNames: weekdayNamesGuarani, weekdayNamesAbbr: weekdayNamesGuaraniAbbr},
		71:    {tags: []string{"gu"}, localMonth: localMonthsNameGujarati, apFmt: apFmtGujarati, weekdayNames: weekdayNamesGujarati, weekdayNamesAbbr: weekdayNamesGujaratiAbbr},
		1095:  {tags: []string{"gu-IN"}, localMonth: localMonthsNameGujarati, apFmt: apFmtGujarati, weekdayNames: weekdayNamesGujarati, weekdayNamesAbbr: weekdayNamesGujaratiAbbr},
		104:   {tags: []string{"ha"}, localMonth: localMonthsNameHausa, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesHausa, weekdayNamesAbbr: weekdayNamesHausaAbbr},
		31848: {tags: []string{"ha-Latn"}, localMonth: localMonthsNameHausa, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesHausa, weekdayNamesAbbr: weekdayNamesHausaAbbr},
		1128:  {tags: []string{"ha-Latn-NG"}, localMonth: localMonthsNameHausa, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesHausa, weekdayNamesAbbr: weekdayNamesHausaAbbr},
		117:   {tags: []string{"haw"}, localMonth: localMonthsNameHawaiian, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesHawaiian, weekdayNamesAbbr: weekdayNamesHawaiianAbbr},
		1141:  {tags: []string{"haw-US"}, localMonth: localMonthsNameHawaiian, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesHawaiian, weekdayNamesAbbr: weekdayNamesHawaiianAbbr},
		13:    {tags: []string{"he"}, localMonth: localMonthsNameHebrew, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesHebrew, weekdayNamesAbbr: weekdayNamesHebrewAbbr},
		1037:  {tags: []string{"he-IL"}, localMonth: localMonthsNameHebrew, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesHebrew, weekdayNamesAbbr: weekdayNamesHebrewAbbr},
		57:    {tags: []string{"hi"}, localMonth: localMonthsNameHindi, apFmt: apFmtHindi, weekdayNames: weekdayNamesHindi, weekdayNamesAbbr: weekdayNamesHindiAbbr},
		1081:  {tags: []string{"hi-IN"}, localMonth: localMonthsNameHindi, apFmt: apFmtHindi, weekdayNames: weekdayNamesHindi, weekdayNamesAbbr: weekdayNamesHindiAbbr},
		14:    {tags: []string{"hu"}, localMonth: localMonthsNameHungarian, apFmt: apFmtHungarian, weekdayNames: weekdayNamesHungarian, weekdayNamesAbbr: weekdayNamesHungarianAbbr},
		1038:  {tags: []string{"hu-HU"}, localMonth: localMonthsNameHungarian, apFmt: apFmtHungarian, weekdayNames: weekdayNamesHungarian, weekdayNamesAbbr: weekdayNamesHungarianAbbr},
		15:    {tags: []string{"is"}, localMonth: localMonthsNameIcelandic, apFmt: apFmtIcelandic, weekdayNames: weekdayNamesIcelandic, weekdayNamesAbbr: weekdayNamesIcelandicAbbr},
		1039:  {tags: []string{"is-IS"}, localMonth: localMonthsNameIcelandic, apFmt: apFmtIcelandic, weekdayNames: weekdayNamesIcelandic, weekdayNamesAbbr: weekdayNamesIcelandicAbbr},
		112:   {tags: []string{"ig"}, localMonth: localMonthsNameIgbo, apFmt: apFmtIgbo, weekdayNames: weekdayNamesIgbo, weekdayNamesAbbr: weekdayNamesIgboAbbr},
		1136:  {tags: []string{"ig-NG"}, localMonth: localMonthsNameIgbo, apFmt: apFmtIgbo, weekdayNames: weekdayNamesIgbo, weekdayNamesAbbr: weekdayNamesIgboAbbr},
		33:    {tags: []string{"id"}, localMonth: localMonthsNameIndonesian, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesIndonesian, weekdayNamesAbbr: weekdayNamesIndonesianAbbr},
		1057:  {tags: []string{"id-ID"}, localMonth: localMonthsNameIndonesian, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesIndonesian, weekdayNamesAbbr: weekdayNamesIndonesianAbbr},
		93:    {tags: []string{"iu"}, localMonth: localMonthsNameInuktitut, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesInuktitut, weekdayNamesAbbr: weekdayNamesInuktitutAbbr},
		31837: {tags: []string{"iu-Latn"}, localMonth: localMonthsNameInuktitut, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesInuktitut, weekdayNamesAbbr: weekdayNamesInuktitutAbbr},
		2141:  {tags: []string{"iu-Latn-CA"}, localMonth: localMonthsNameInuktitut, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesInuktitut, weekdayNamesAbbr: weekdayNamesInuktitutAbbr},
		30813: {tags: []string{"iu-Cans"}, localMonth: localMonthsNameSyllabics, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesSyllabics, weekdayNamesAbbr: weekdayNamesSyllabicsAbbr},
		1117:  {tags: []string{"iu-Cans-CA"}, localMonth: localMonthsNameSyllabics, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesSyllabics, weekdayNamesAbbr: weekdayNamesSyllabicsAbbr},
		60:    {tags: []string{"ga"}, localMonth: localMonthsNameIrish, apFmt: apFmtIrish, weekdayNames: weekdayNamesIrish, weekdayNamesAbbr: weekdayNamesIrishAbbr},
		2108:  {tags: []string{"ga-IE"}, localMonth: localMonthsNameIrish, apFmt: apFmtIrish, weekdayNames: weekdayNamesIrish, weekdayNamesAbbr: weekdayNamesIrishAbbr},
		16:    {tags: []string{"it"}, localMonth: localMonthsNameItalian, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesItalian, weekdayNamesAbbr: weekdayNamesItalianAbbr},
		1040:  {tags: []string{"it-IT"}, localMonth: localMonthsNameItalian, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesItalian, weekdayNamesAbbr: weekdayNamesItalianAbbr},
		2064:  {tags: []string{"it-CH"}, localMonth: localMonthsNameItalian, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesItalian, weekdayNamesAbbr: weekdayNamesItalianAbbr},
		17:    {tags: []string{"ja"}, localMonth: localMonthsNameChinese3, apFmt: apFmtJapanese, weekdayNames: weekdayNamesJapanese, weekdayNamesAbbr: weekdayNamesJapaneseAbbr},
		1041:  {tags: []string{"ja-JP"}, localMonth: localMonthsNameChinese3, apFmt: apFmtJapanese, weekdayNames: weekdayNamesJapanese, weekdayNamesAbbr: weekdayNamesJapaneseAbbr},
		75:    {tags: []string{"kn"}, localMonth: localMonthsNameKannada, apFmt: apFmtKannada, weekdayNames: weekdayNamesKannada, weekdayNamesAbbr: weekdayNamesKannadaAbbr},
		1099:  {tags: []string{"kn-IN"}, localMonth: localMonthsNameKannada, apFmt: apFmtKannada, weekdayNames: weekdayNamesKannada, weekdayNamesAbbr: weekdayNamesKannadaAbbr},
		1137:  {tags: []string{"kr-Latn-NG"}, localMonth: localMonthsNameEnglish, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesEnglish, weekdayNamesAbbr: weekdayNamesEnglishAbbr},
		96:    {tags: []string{"ks"}, localMonth: localMonthsNameKashmiri, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesKashmiri, weekdayNamesAbbr: weekdayNamesKashmiriAbbr},
		1120:  {tags: []string{"ks-Arab"}, localMonth: localMonthsNameKashmiri, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesKashmiri, weekdayNamesAbbr: weekdayNamesKashmiriAbbr},
		2144:  {tags: []string{"ks-Deva-IN"}, localMonth: localMonthsNameEnglish, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesEnglish, weekdayNamesAbbr: weekdayNamesEnglishAbbr},
		63:    {tags: []string{"kk"}, localMonth: localMonthsNameKazakh, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesKazakh, weekdayNamesAbbr: weekdayNamesKazakhAbbr},
		1087:  {tags: []string{"kk-KZ"}, localMonth: localMonthsNameKazakh, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesKazakh, weekdayNamesAbbr: weekdayNamesKazakhAbbr},
		83:    {tags: []string{"km"}, localMonth: localMonthsNameKhmer, apFmt: apFmtKhmer, weekdayNames: weekdayNamesKhmer, weekdayNamesAbbr: weekdayNamesKhmerAbbr},
		1107:  {tags: []string{"km-KH"}, localMonth: localMonthsNameKhmer, apFmt: apFmtKhmer, weekdayNames: weekdayNamesKhmer, weekdayNamesAbbr: weekdayNamesKhmerAbbr},
		134:   {tags: []string{"quc"}, localMonth: localMonthsNameKiche, apFmt: apFmtCuba, weekdayNames: weekdayNamesKiche, weekdayNamesAbbr: weekdayNamesKicheAbbr},
		1158:  {tags: []string{"quc-Latn-GT"}, localMonth: localMonthsNameKiche, apFmt: apFmtCuba, weekdayNames: weekdayNamesKiche, weekdayNamesAbbr: weekdayNamesKicheAbbr},
		135:   {tags: []string{"rw"}, localMonth: localMonthsNameKinyarwanda, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesKinyarwanda, weekdayNamesAbbr: weekdayNamesKinyarwandaAbbr},
		1159:  {tags: []string{"rw-RW"}, localMonth: localMonthsNameKinyarwanda, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesKinyarwanda, weekdayNamesAbbr: weekdayNamesKinyarwandaAbbr},
		65:    {tags: []string{"sw"}, localMonth: localMonthsNameKiswahili, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesKiswahili, weekdayNamesAbbr: weekdayNamesKiswahiliAbbr},
		1089:  {tags: []string{"sw-KE"}, localMonth: localMonthsNameKiswahili, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesKiswahili, weekdayNamesAbbr: weekdayNamesKiswahiliAbbr},
		87:    {tags: []string{"kok"}, localMonth: localMonthsNameKonkani, apFmt: apFmtKonkani, weekdayNames: weekdayNamesKonkani, weekdayNamesAbbr: weekdayNamesKonkaniAbbr},
		1111:  {tags: []string{"kok-IN"}, localMonth: localMonthsNameKonkani, apFmt: apFmtKonkani, weekdayNames: weekdayNamesKonkani, weekdayNamesAbbr: weekdayNamesKonkaniAbbr},
		18:    {tags: []string{"ko"}, localMonth: localMonthsNameKorean, apFmt: apFmtKorean, weekdayNames: weekdayNamesKorean, weekdayNamesAbbr: weekdayNamesKoreanAbbr},
		1042:  {tags: []string{"ko-KR"}, localMonth: localMonthsNameKorean, apFmt: apFmtKorean, weekdayNames: weekdayNamesKorean, weekdayNamesAbbr: weekdayNamesKoreanAbbr},
		64:    {tags: []string{"ky"}, localMonth: localMonthsNameKyrgyz, apFmt: apFmtKyrgyz, weekdayNames: weekdayNamesKyrgyz, weekdayNamesAbbr: weekdayNamesKyrgyzAbbr},
		1088:  {tags: []string{"ky-KG"}, localMonth: localMonthsNameKyrgyz, apFmt: apFmtKyrgyz, weekdayNames: weekdayNamesKyrgyz, weekdayNamesAbbr: weekdayNamesKyrgyzAbbr},
		84:    {tags: []string{"lo"}, localMonth: localMonthsNameLao, apFmt: apFmtLao, weekdayNames: weekdayNamesLao, weekdayNamesAbbr: weekdayNamesLaoAbbr},
		1108:  {tags: []string{"lo-LA"}, localMonth: localMonthsNameLao, apFmt: apFmtLao, weekdayNames: weekdayNamesLao, weekdayNamesAbbr: weekdayNamesLaoAbbr},
		1142:  {tags: []string{"la-VA"}, localMonth: localMonthsNameLatin, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesLatin, weekdayNamesAbbr: weekdayNamesLatinAbbr},
		38:    {tags: []string{"lv"}, localMonth: localMonthsNameLatvian, apFmt: apFmtLatvian, weekdayNames: weekdayNamesLatvian, weekdayNamesAbbr: weekdayNamesLatvianAbbr},
		1062:  {tags: []string{"lv-LV"}, localMonth: localMonthsNameLatvian, apFmt: apFmtLatvian, weekdayNames: weekdayNamesLatvian, weekdayNamesAbbr: weekdayNamesLatvianAbbr},
		39:    {tags: []string{"lt"}, localMonth: localMonthsNameLithuanian, apFmt: apFmtLithuanian, weekdayNames: weekdayNamesLithuanian, weekdayNamesAbbr: weekdayNamesLithuanianAbbr},
		1063:  {tags: []string{"lt-LT"}, localMonth: localMonthsNameLithuanian, apFmt: apFmtLithuanian, weekdayNames: weekdayNamesLithuanian, weekdayNamesAbbr: weekdayNamesLithuanianAbbr},
		31790: {tags: []string{"dsb"}, localMonth: localMonthsNameLowerSorbian, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesLowerSorbian, weekdayNamesAbbr: weekdayNamesLowerSorbianAbbr},
		2094:  {tags: []string{"dsb-DE"}, localMonth: localMonthsNameLowerSorbian, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesLowerSorbian, weekdayNamesAbbr: weekdayNamesLowerSorbianAbbr},
		110:   {tags: []string{"lb"}, localMonth: localMonthsNameLuxembourgish, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesLuxembourgish, weekdayNamesAbbr: weekdayNamesLuxembourgishAbbr},
		1134:  {tags: []string{"lb-LU"}, localMonth: localMonthsNameLuxembourgish, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesLuxembourgish, weekdayNamesAbbr: weekdayNamesLuxembourgishAbbr},
		47:    {tags: []string{"mk"}, localMonth: localMonthsNameMacedonian, apFmt: apFmtMacedonian, weekdayNames: weekdayNamesMacedonian, weekdayNamesAbbr: weekdayNamesMacedonianAbbr},
		1071:  {tags: []string{"mk-MK"}, localMonth: localMonthsNameMacedonian, apFmt: apFmtMacedonian, weekdayNames: weekdayNamesMacedonian, weekdayNamesAbbr: weekdayNamesMacedonianAbbr},
		62:    {tags: []string{"ms"}, localMonth: localMonthsNameMalay, apFmt: apFmtMalay, weekdayNames: weekdayNamesMalay, weekdayNamesAbbr: weekdayNamesMalayAbbr},
		2110:  {tags: []string{"ms-BN"}, localMonth: localMonthsNameMalay, apFmt: apFmtMalay, weekdayNames: weekdayNamesMalay, weekdayNamesAbbr: weekdayNamesMalayAbbr},
		1086:  {tags: []string{"ms-MY"}, localMonth: localMonthsNameMalay, apFmt: apFmtMalay, weekdayNames: weekdayNamesMalay, weekdayNamesAbbr: weekdayNamesMalayAbbr},
		76:    {tags: []string{"ml"}, localMonth: localMonthsNameMalayalam, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesMalayalam, weekdayNamesAbbr: weekdayNamesMalayalamAbbr},
		1100:  {tags: []string{"ml-IN"}, localMonth: localMonthsNameMalayalam, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesMalayalam, weekdayNamesAbbr: weekdayNamesMalayalamAbbr},
		58:    {tags: []string{"mt"}, localMonth: localMonthsNameMaltese, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesMaltese, weekdayNamesAbbr: weekdayNamesMalteseAbbr},
		1082:  {tags: []string{"mt-MT"}, localMonth: localMonthsNameMaltese, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesMaltese, weekdayNamesAbbr: weekdayNamesMalteseAbbr},
		129:   {tags: []string{"mi"}, localMonth: localMonthsNameMaori, apFmt: apFmtCuba, weekdayNames: weekdayNamesMaori, weekdayNamesAbbr: weekdayNamesMaoriAbbr},
		1153:  {tags: []string{"mi-NZ"}, localMonth: localMonthsNameMaori, apFmt: apFmtCuba, weekdayNames: weekdayNamesMaori, weekdayNamesAbbr: weekdayNamesMaoriAbbr},
		122:   {tags: []string{"arn"}, localMonth: localMonthsNameMapudungun, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesMapudungun, weekdayNamesAbbr: weekdayNamesMapudungunAbbr},
		1146:  {tags: []string{"arn-CL"}, localMonth: localMonthsNameMapudungun, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesMapudungun, weekdayNamesAbbr: weekdayNamesMapudungunAbbr},
		78:    {tags: []string{"mr"}, localMonth: localMonthsNameMarathi, apFmt: apFmtKonkani, weekdayNames: weekdayNamesMarathi, weekdayNamesAbbr: weekdayNamesMarathiAbbr},
		1102:  {tags: []string{"mr-IN"}, localMonth: localMonthsNameMarathi, apFmt: apFmtKonkani, weekdayNames: weekdayNamesMarathi, weekdayNamesAbbr: weekdayNamesMarathiAbbr},
		124:   {tags: []string{"moh"}, localMonth: localMonthsNameMohawk, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesMohawk, weekdayNamesAbbr: weekdayNamesEnglishAbbr},
		1148:  {tags: []string{"moh-CA"}, localMonth: localMonthsNameMohawk, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesMohawk, weekdayNamesAbbr: weekdayNamesEnglishAbbr},
		80:    {tags: []string{"mn"}, localMonth: localMonthsNameMongolian, apFmt: apFmtMongolian, weekdayNames: weekdayNamesMongolian, weekdayNamesAbbr: weekdayNamesMongolianAbbr},
		30800: {tags: []string{"mn-Cyrl"}, localMonth: localMonthsNameMongolian, apFmt: apFmtMongolian, weekdayNames: weekdayNamesMongolian, weekdayNamesAbbr: weekdayNamesMongolianCyrlAbbr},
		1104:  {tags: []string{"mn-MN"}, localMonth: localMonthsNameMongolian, apFmt: apFmtMongolian, weekdayNames: weekdayNamesMongolian, weekdayNamesAbbr: weekdayNamesMongolianCyrlAbbr},
		31824: {tags: []string{"mn-Mong"}, localMonth: localMonthsNameTraditionalMongolian, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesTraditionalMongolian, weekdayNamesAbbr: weekdayNamesTraditionalMongolian},
		2128:  {tags: []string{"mn-Mong-CN"}, localMonth: localMonthsNameTraditionalMongolian, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesTraditionalMongolian, weekdayNamesAbbr: weekdayNamesTraditionalMongolian},
		3152:  {tags: []string{"mn-Mong-MN"}, localMonth: localMonthsNameTraditionalMongolian, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesTraditionalMongolianMN, weekdayNamesAbbr: weekdayNamesTraditionalMongolianMN},
		97:    {tags: []string{"ne"}, localMonth: localMonthsNameNepali, apFmt: apFmtHindi, weekdayNames: weekdayNamesNepali, weekdayNamesAbbr: weekdayNamesNepaliAbbr},
		2145:  {tags: []string{"ne-IN"}, localMonth: localMonthsNameNepaliIN, apFmt: apFmtHindi, weekdayNames: weekdayNamesNepaliIN, weekdayNamesAbbr: weekdayNamesNepaliINAbbr},
		1121:  {tags: []string{"ne-NP"}, localMonth: localMonthsNameNepali, apFmt: apFmtHindi, weekdayNames: weekdayNamesNepali, weekdayNamesAbbr: weekdayNamesNepaliAbbr},
		20:    {tags: []string{"no"}, localMonth: localMonthsNameNorwegian, apFmt: apFmtCuba, weekdayNames: weekdayNamesNorwegian, weekdayNamesAbbr: weekdayNamesNorwegianAbbr},
		31764: {tags: []string{"nb"}, localMonth: localMonthsNameNorwegian, apFmt: apFmtCuba, weekdayNames: weekdayNamesNorwegian, weekdayNamesAbbr: weekdayNamesNorwegianNOAbbr},
		1044:  {tags: []string{"nb-NO"}, localMonth: localMonthsNameNorwegian, apFmt: apFmtCuba, weekdayNames: weekdayNamesNorwegian, weekdayNamesAbbr: weekdayNamesNorwegianNOAbbr},
		30740: {tags: []string{"nn"}, localMonth: localMonthsNameNorwegian, apFmt: apFmtNorwegian, weekdayNames: weekdayNamesNorwegianNynorsk, weekdayNamesAbbr: weekdayNamesNorwegianNynorskAbbr},
		2068:  {tags: []string{"nn-NO"}, localMonth: localMonthsNameNorwegian, apFmt: apFmtNorwegian, weekdayNames: weekdayNamesNorwegianNynorsk, weekdayNamesAbbr: weekdayNamesNorwegianNynorskAbbr},
		130:   {tags: []string{"oc"}, localMonth: localMonthsNameOccitan, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesOccitan, weekdayNamesAbbr: weekdayNamesOccitanAbbr},
		1154:  {tags: []string{"oc-FR"}, localMonth: localMonthsNameOccitan, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesOccitan, weekdayNamesAbbr: weekdayNamesOccitanAbbr},
		72:    {tags: []string{"or"}, localMonth: localMonthsNameOdia, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesOdia, weekdayNamesAbbr: weekdayNamesOdiaAbbr},
		1096:  {tags: []string{"or-IN"}, localMonth: localMonthsNameOdia, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesOdia, weekdayNamesAbbr: weekdayNamesOdiaAbbr},
		114:   {tags: []string{"om"}, localMonth: localMonthsNameOromo, apFmt: apFmtOromo, weekdayNames: weekdayNamesOromo, weekdayNamesAbbr: weekdayNamesOromoAbbr},
		1138:  {tags: []string{"om-ET"}, localMonth: localMonthsNameOromo, apFmt: apFmtOromo, weekdayNames: weekdayNamesOromo, weekdayNamesAbbr: weekdayNamesOromoAbbr},
		99:    {tags: []string{"ps"}, localMonth: localMonthsNamePashto, apFmt: apFmtPashto, weekdayNames: weekdayNamesPashto, weekdayNamesAbbr: weekdayNamesPashto},
		1123:  {tags: []string{"ps-AF"}, localMonth: localMonthsNamePashto, apFmt: apFmtPashto, weekdayNames: weekdayNamesPashto, weekdayNamesAbbr: weekdayNamesPashto},
		41:    {tags: []string{"fa"}, localMonth: localMonthsNamePersian, apFmt: apFmtPersian, weekdayNames: weekdayNamesPersian, weekdayNamesAbbr: weekdayNamesPersian},
		1065:  {tags: []string{"fa-IR"}, localMonth: localMonthsNamePersian, apFmt: apFmtPersian, weekdayNames: weekdayNamesPersian, weekdayNamesAbbr: weekdayNamesPersian},
		21:    {tags: []string{"pl"}, localMonth: localMonthsNamePolish, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesPolish, weekdayNamesAbbr: weekdayNamesPolishAbbr},
		1045:  {tags: []string{"pl-PL"}, localMonth: localMonthsNamePolish, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesPolish, weekdayNamesAbbr: weekdayNamesPolishAbbr},
		22:    {tags: []string{"pt"}, localMonth: localMonthsNamePortuguese, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesPortuguese, weekdayNamesAbbr: weekdayNamesPortugueseAbbr},
		1046:  {tags: []string{"pt-BR"}, localMonth: localMonthsNamePortuguese, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesPortuguese, weekdayNamesAbbr: weekdayNamesPortugueseAbbr},
		2070:  {tags: []string{"pt-PT"}, localMonth: localMonthsNamePortuguese, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesPortuguese, weekdayNamesAbbr: weekdayNamesPortugueseAbbr},
		70:    {tags: []string{"pa"}, localMonth: localMonthsNamePunjabi, apFmt: apFmtPunjabi, weekdayNames: weekdayNamesPunjabi, weekdayNamesAbbr: weekdayNamesPunjabiAbbr},
		31814: {tags: []string{"pa-Arab"}, localMonth: localMonthsNamePunjabiArab, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesPunjabiArab, weekdayNamesAbbr: weekdayNamesPunjabiArab},
		1094:  {tags: []string{"pa-IN"}, localMonth: localMonthsNamePunjabi, apFmt: apFmtPunjabi, weekdayNames: weekdayNamesPunjabi, weekdayNamesAbbr: weekdayNamesPunjabiAbbr},
		2118:  {tags: []string{"pa-Arab-PK"}, localMonth: localMonthsNamePunjabiArab, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesPunjabiArab, weekdayNamesAbbr: weekdayNamesPunjabiArab},
		107:   {tags: []string{"quz"}, localMonth: localMonthsNameQuechua, apFmt: apFmtCuba, weekdayNames: weekdayNamesQuechua, weekdayNamesAbbr: weekdayNamesQuechuaAbbr},
		1131:  {tags: []string{"quz-BO"}, localMonth: localMonthsNameQuechua, apFmt: apFmtCuba, weekdayNames: weekdayNamesQuechua, weekdayNamesAbbr: weekdayNamesQuechuaAbbr},
		2155:  {tags: []string{"quz-EC"}, localMonth: localMonthsNameQuechuaEcuador, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesQuechuaEcuador, weekdayNamesAbbr: weekdayNamesQuechuaEcuadorAbbr},
		3179:  {tags: []string{"quz-PE"}, localMonth: localMonthsNameQuechua, apFmt: apFmtCuba, weekdayNames: weekdayNamesQuechuaPeru, weekdayNamesAbbr: weekdayNamesQuechuaPeruAbbr},
		24:    {tags: []string{"ro"}, localMonth: localMonthsNameRomanian, apFmt: apFmtCuba, weekdayNames: weekdayNamesRomanian, weekdayNamesAbbr: weekdayNamesRomanianAbbr},
		2072:  {tags: []string{"ro-MD"}, localMonth: localMonthsNameRomanian, apFmt: apFmtCuba, weekdayNames: weekdayNamesRomanian, weekdayNamesAbbr: weekdayNamesRomanianMoldovaAbbr},
		1048:  {tags: []string{"ro-RO"}, localMonth: localMonthsNameRomanian, apFmt: apFmtCuba, weekdayNames: weekdayNamesRomanian, weekdayNamesAbbr: weekdayNamesRomanianAbbr},
		23:    {tags: []string{"rm"}, localMonth: localMonthsNameRomansh, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesRomansh, weekdayNamesAbbr: weekdayNamesRomanshAbbr},
		1047:  {tags: []string{"rm-CH"}, localMonth: localMonthsNameRomansh, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesRomansh, weekdayNamesAbbr: weekdayNamesRomanshAbbr},
		25:    {tags: []string{"ru"}, localMonth: localMonthsNameRussian, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesRussian, weekdayNamesAbbr: weekdayNamesRussianAbbr},
		2073:  {tags: []string{"ru-MD"}, localMonth: localMonthsNameRussian, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesRussian, weekdayNamesAbbr: weekdayNamesRussianAbbr},
		1049:  {tags: []string{"ru-RU"}, localMonth: localMonthsNameRussian, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesRussian, weekdayNamesAbbr: weekdayNamesRussianAbbr},
		133:   {tags: []string{"sah"}, localMonth: localMonthsNameSakha, apFmt: apFmtSakha, weekdayNames: weekdayNamesSakha, weekdayNamesAbbr: weekdayNamesSakhaAbbr},
		1157:  {tags: []string{"sah-RU"}, localMonth: localMonthsNameSakha, apFmt: apFmtSakha, weekdayNames: weekdayNamesSakha, weekdayNamesAbbr: weekdayNamesSakhaAbbr},
		28731: {tags: []string{"smn"}, localMonth: localMonthsNameSami, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesSami, weekdayNamesAbbr: weekdayNamesSamiAbbr},
		9275:  {tags: []string{"smn-FI"}, localMonth: localMonthsNameSami, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesSami, weekdayNamesAbbr: weekdayNamesSamiAbbr},
		31803: {tags: []string{"smj"}, localMonth: localMonthsNameSamiLule, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesSamiSamiLule, weekdayNamesAbbr: weekdayNamesSamiSwedenAbbr},
		4155:  {tags: []string{"smj-NO"}, localMonth: localMonthsNameSamiLule, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesSamiSamiLule, weekdayNamesAbbr: weekdayNamesSamiSamiLuleAbbr},
		5179:  {tags: []string{"smj-SE"}, localMonth: localMonthsNameSamiLule, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesSamiSweden, weekdayNamesAbbr: weekdayNamesSamiSwedenAbbr},
		59:    {tags: []string{"se"}, localMonth: localMonthsNameSamiNorthern, apFmt: apFmtSamiNorthern, weekdayNames: weekdayNamesSamiNorthern, weekdayNamesAbbr: weekdayNamesSamiNorthernAbbr},
		3131:  {tags: []string{"se-FI"}, localMonth: localMonthsNameSamiNorthernFI, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesSamiNorthernFI, weekdayNamesAbbr: weekdayNamesSamiNorthernFIAbbr},
		1083:  {tags: []string{"se-NO"}, localMonth: localMonthsNameSamiNorthern, apFmt: apFmtSamiNorthern, weekdayNames: weekdayNamesSamiNorthern, weekdayNamesAbbr: weekdayNamesSamiNorthernAbbr},
		2107:  {tags: []string{"se-SE"}, localMonth: localMonthsNameSamiNorthern, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesSamiNorthernSE, weekdayNamesAbbr: weekdayNamesSamiNorthernSEAbbr},
		29755: {tags: []string{"sms"}, localMonth: localMonthsNameSamiSkolt, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesSamiSkolt, weekdayNamesAbbr: weekdayNamesSamiSkoltAbbr},
		8251:  {tags: []string{"sms-FI"}, localMonth: localMonthsNameSamiSkolt, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesSamiSkolt, weekdayNamesAbbr: weekdayNamesSamiSkoltAbbr},
		30779: {tags: []string{"sma"}, localMonth: localMonthsNameSamiSouthern, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesSamiSouthern, weekdayNamesAbbr: weekdayNamesSamiSouthernAbbr},
		6203:  {tags: []string{"sma-NO"}, localMonth: localMonthsNameSamiSouthern, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesSamiSouthern, weekdayNamesAbbr: weekdayNamesSamiSouthernAbbr},
		7227:  {tags: []string{"sma-SE"}, localMonth: localMonthsNameSamiSouthern, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesSamiSouthern, weekdayNamesAbbr: weekdayNamesSamiSouthernAbbr},
		79:    {tags: []string{"sa"}, localMonth: localMonthsNameSanskrit, apFmt: apFmtSanskrit, weekdayNames: weekdayNamesSanskrit, weekdayNamesAbbr: weekdayNamesSanskritAbbr},
		1103:  {tags: []string{"sa-IN"}, localMonth: localMonthsNameSanskrit, apFmt: apFmtSanskrit, weekdayNames: weekdayNamesSanskrit, weekdayNamesAbbr: weekdayNamesSanskritAbbr},
		145:   {tags: []string{"gd"}, localMonth: localMonthsNameScottishGaelic, apFmt: apFmtScottishGaelic, weekdayNames: weekdayNamesGaelic, weekdayNamesAbbr: weekdayNamesGaelicAbbr},
		1169:  {tags: []string{"gd-GB"}, localMonth: localMonthsNameScottishGaelic, apFmt: apFmtScottishGaelic, weekdayNames: weekdayNamesGaelic, weekdayNamesAbbr: weekdayNamesGaelicAbbr},
		27674: {tags: []string{"sr-Cyrl"}, localMonth: localMonthsNameSerbian, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesSerbian, weekdayNamesAbbr: weekdayNamesSerbianAbbr},
		7194:  {tags: []string{"sr-Cyrl-BA"}, localMonth: localMonthsNameSerbianBA, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesSerbianBA, weekdayNamesAbbr: weekdayNamesSerbianBAAbbr},
		12314: {tags: []string{"sr-Cyrl-ME"}, localMonth: localMonthsNameSerbian, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesSerbianME, weekdayNamesAbbr: weekdayNamesSerbianBAAbbr},
		10266: {tags: []string{"sr-Cyrl-RS"}, localMonth: localMonthsNameSerbian, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesSerbian, weekdayNamesAbbr: weekdayNamesSerbianAbbr},
		3098:  {tags: []string{"sr-Cyrl-CS"}, localMonth: localMonthsNameSerbian, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesSerbian, weekdayNamesAbbr: weekdayNamesSerbianAbbr},
		28698: {tags: []string{"sr-Latn"}, localMonth: localMonthsNameSerbianLatin, apFmt: apFmtSerbianLatin, weekdayNames: weekdayNamesSerbianLatin, weekdayNamesAbbr: weekdayNamesSerbianLatinAbbr},
		31770: {tags: []string{"sr"}, localMonth: localMonthsNameSerbianLatin, apFmt: apFmtSerbianLatin, weekdayNames: weekdayNamesSerbianLatin, weekdayNamesAbbr: weekdayNamesSerbianLatinAbbr},
		6170:  {tags: []string{"sr-Latn-BA"}, localMonth: localMonthsNameSerbianLatin, apFmt: apFmtSerbianLatinBA, weekdayNames: weekdayNamesSerbianLatinBA, weekdayNamesAbbr: weekdayNamesSerbianLatinBAAbbr},
		11290: {tags: []string{"sr-Latn-ME"}, localMonth: localMonthsNameSerbianLatin, apFmt: apFmtSerbianLatinBA, weekdayNames: weekdayNamesSerbianLatinME, weekdayNamesAbbr: weekdayNamesSerbianLatinAbbr},
		9242:  {tags: []string{"sr-Latn-RS"}, localMonth: localMonthsNameSerbianLatin, apFmt: apFmtSerbianLatin, weekdayNames: weekdayNamesSerbianLatin, weekdayNamesAbbr: weekdayNamesSerbianLatinAbbr},
		2074:  {tags: []string{"sr-Latn-CS"}, localMonth: localMonthsNameSerbianLatinCS, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesSerbianLatin, weekdayNamesAbbr: weekdayNamesSerbianLatinCSAbbr},
		108:   {tags: []string{"nso"}, localMonth: localMonthsNameSesothoSaLeboa, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesSesothoSaLeboa, weekdayNamesAbbr: weekdayNamesSesothoSaLeboaAbbr},
		1132:  {tags: []string{"nso-ZA"}, localMonth: localMonthsNameSesothoSaLeboa, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesSesothoSaLeboa, weekdayNamesAbbr: weekdayNamesSesothoSaLeboaAbbr},
		50:    {tags: []string{"tn"}, localMonth: localMonthsNameSetswana, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesSetswana, weekdayNamesAbbr: weekdayNamesSetswanaAbbr},
		2098:  {tags: []string{"tn-BW"}, localMonth: localMonthsNameSetswana, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesSetswana, weekdayNamesAbbr: weekdayNamesSetswanaAbbr},
		1074:  {tags: []string{"tn-ZA"}, localMonth: localMonthsNameSetswana, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesSetswana, weekdayNamesAbbr: weekdayNamesSetswanaAbbr},
		89:    {tags: []string{"sd"}, localMonth: localMonthsNameSindhi, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesSindhi, weekdayNamesAbbr: weekdayNamesSindhiAbbr},
		31833: {tags: []string{"sd-Arab"}, localMonth: localMonthsNameSindhi, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesSindhi, weekdayNamesAbbr: weekdayNamesSindhiAbbr},
		2137:  {tags: []string{"sd-Arab-PK"}, localMonth: localMonthsNameSindhi, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesSindhi, weekdayNamesAbbr: weekdayNamesSindhiAbbr},
		91:    {tags: []string{"si"}, localMonth: localMonthsNameSinhala, apFmt: apFmtSinhala, weekdayNames: weekdayNamesSindhi, weekdayNamesAbbr: weekdayNamesSindhiAbbr},
		1115:  {tags: []string{"si-LK"}, localMonth: localMonthsNameSinhala, apFmt: apFmtSinhala, weekdayNames: weekdayNamesSindhi, weekdayNamesAbbr: weekdayNamesSindhiAbbr},
		27:    {tags: []string{"sk"}, localMonth: localMonthsNameSlovak, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesSlovak, weekdayNamesAbbr: weekdayNamesSlovakAbbr},
		1051:  {tags: []string{"sk-SK"}, localMonth: localMonthsNameSlovak, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesSlovak, weekdayNamesAbbr: weekdayNamesSlovakAbbr},
		36:    {tags: []string{"sl"}, localMonth: localMonthsNameSlovenian, apFmt: apFmtSlovenian, weekdayNames: weekdayNamesSlovenian, weekdayNamesAbbr: weekdayNamesSlovenianAbbr},
		1060:  {tags: []string{"sl-SI"}, localMonth: localMonthsNameSlovenian, apFmt: apFmtSlovenian, weekdayNames: weekdayNamesSlovenian, weekdayNamesAbbr: weekdayNamesSlovenianAbbr},
		119:   {tags: []string{"so"}, localMonth: localMonthsNameSomali, apFmt: apFmtSomali, weekdayNames: weekdayNamesSomali, weekdayNamesAbbr: weekdayNamesSomaliAbbr},
		1143:  {tags: []string{"so-SO"}, localMonth: localMonthsNameSomali, apFmt: apFmtSomali, weekdayNames: weekdayNamesSomali, weekdayNamesAbbr: weekdayNamesSomaliAbbr},
		48:    {tags: []string{"st"}, localMonth: localMonthsNameSotho, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesSotho, weekdayNamesAbbr: weekdayNamesSothoAbbr},
		1072:  {tags: []string{"st-ZA"}, localMonth: localMonthsNameSotho, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesSotho, weekdayNamesAbbr: weekdayNamesSothoAbbr},
		10:    {tags: []string{"es"}, localMonth: localMonthsNameSpanish, apFmt: apFmtSpanish, weekdayNames: weekdayNamesSpanish, weekdayNamesAbbr: weekdayNamesSpanishAbbr},
		11274: {tags: []string{"es-AR"}, localMonth: localMonthsNameSpanish, apFmt: apFmtSpanishAR, weekdayNames: weekdayNamesSpanish, weekdayNamesAbbr: weekdayNamesSpanishARAbbr},
		8202:  {tags: []string{"es-VE"}, localMonth: localMonthsNameSpanish, apFmt: apFmtSpanishAR, weekdayNames: weekdayNamesSpanish, weekdayNamesAbbr: weekdayNamesSpanishARAbbr},
		16394: {tags: []string{"es-BO"}, localMonth: localMonthsNameSpanish, apFmt: apFmtSpanishAR, weekdayNames: weekdayNamesSpanish, weekdayNamesAbbr: weekdayNamesSpanishARAbbr},
		13322: {tags: []string{"es-CL"}, localMonth: localMonthsNameSpanish, apFmt: apFmtSpanishAR, weekdayNames: weekdayNamesSpanish, weekdayNamesAbbr: weekdayNamesSpanishARAbbr},
		9226:  {tags: []string{"es-CO"}, localMonth: localMonthsNameSpanish, apFmt: apFmtSpanishAR, weekdayNames: weekdayNamesSpanish, weekdayNamesAbbr: weekdayNamesSpanishARAbbr},
		5130:  {tags: []string{"es-CR"}, localMonth: localMonthsNameSpanish, apFmt: apFmtSpanishAR, weekdayNames: weekdayNamesSpanish, weekdayNamesAbbr: weekdayNamesSpanishARAbbr},
		23562: {tags: []string{"es-CU"}, localMonth: localMonthsNameSpanish, apFmt: apFmtCuba, weekdayNames: weekdayNamesSpanish, weekdayNamesAbbr: weekdayNamesSpanishARAbbr},
		7178:  {tags: []string{"es-DO"}, localMonth: localMonthsNameSpanish, apFmt: apFmtSpanishAR, weekdayNames: weekdayNamesSpanish, weekdayNamesAbbr: weekdayNamesSpanishARAbbr},
		12298: {tags: []string{"es-EC"}, localMonth: localMonthsNameSpanish, apFmt: apFmtSpanishAR, weekdayNames: weekdayNamesSpanish, weekdayNamesAbbr: weekdayNamesSpanishARAbbr},
		17418: {tags: []string{"es-SV"}, localMonth: localMonthsNameSpanish, apFmt: apFmtSpanishAR, weekdayNames: weekdayNamesSpanish, weekdayNamesAbbr: weekdayNamesSpanishARAbbr},
		4106:  {tags: []string{"es-GT"}, localMonth: localMonthsNameSpanish, apFmt: apFmtSpanishAR, weekdayNames: weekdayNamesSpanish, weekdayNamesAbbr: weekdayNamesSpanishARAbbr},
		18442: {tags: []string{"es-HN"}, localMonth: localMonthsNameSpanish, apFmt: apFmtSpanishAR, weekdayNames: weekdayNamesSpanish, weekdayNamesAbbr: weekdayNamesSpanishARAbbr},
		22538: {tags: []string{"es-419"}, localMonth: localMonthsNameSpanish, apFmt: apFmtCuba, weekdayNames: weekdayNamesSpanish, weekdayNamesAbbr: weekdayNamesSpanishARAbbr},
		2058:  {tags: []string{"es-MX"}, localMonth: localMonthsNameSpanish, apFmt: apFmtSpanish, weekdayNames: weekdayNamesSpanish, weekdayNamesAbbr: weekdayNamesSpanishARAbbr},
		19466: {tags: []string{"es-NI"}, localMonth: localMonthsNameSpanish, apFmt: apFmtSpanishAR, weekdayNames: weekdayNamesSpanish, weekdayNamesAbbr: weekdayNamesSpanishARAbbr},
		6154:  {tags: []string{"es-PA"}, localMonth: localMonthsNameSpanish, apFmt: apFmtSpanishAR, weekdayNames: weekdayNamesSpanish, weekdayNamesAbbr: weekdayNamesSpanishARAbbr},
		15370: {tags: []string{"es-PY"}, localMonth: localMonthsNameSpanish, apFmt: apFmtSpanishAR, weekdayNames: weekdayNamesSpanish, weekdayNamesAbbr: weekdayNamesSpanishARAbbr},
		10250: {tags: []string{"es-PE"}, localMonth: localMonthsNameSpanishPE, apFmt: apFmtSpanishAR, weekdayNames: weekdayNamesSpanish, weekdayNamesAbbr: weekdayNamesSpanishARAbbr},
		20490: {tags: []string{"es-PR"}, localMonth: localMonthsNameSpanish, apFmt: apFmtSpanishAR, weekdayNames: weekdayNamesSpanish, weekdayNamesAbbr: weekdayNamesSpanishARAbbr},
		1034:  {tags: []string{"es-ES_tradnl"}, localMonth: localMonthsNameSpanish, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesSpanish, weekdayNamesAbbr: weekdayNamesSpanishAbbr},
		3082:  {tags: []string{"es-ES"}, localMonth: localMonthsNameSpanish, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesSpanish, weekdayNamesAbbr: weekdayNamesSpanishAbbr},
		21514: {tags: []string{"es-US"}, localMonth: localMonthsNameSpanish, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesSpanish, weekdayNamesAbbr: weekdayNamesSpanishUSAbbr},
		14346: {tags: []string{"es-UY"}, localMonth: localMonthsNameSpanishPE, apFmt: apFmtSpanishAR, weekdayNames: weekdayNamesSpanish, weekdayNamesAbbr: weekdayNamesSpanishARAbbr},
		29:    {tags: []string{"sv"}, localMonth: localMonthsNameSwedish, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesSwedish, weekdayNamesAbbr: weekdayNamesSwedishAbbr},
		2077:  {tags: []string{"sv-FI"}, localMonth: localMonthsNameSwedishFI, apFmt: apFmtSwedish, weekdayNames: weekdayNamesSwedish, weekdayNamesAbbr: weekdayNamesSwedishAbbr},
		1053:  {tags: []string{"sv-SE"}, localMonth: localMonthsNameSwedishFI, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesSwedish, weekdayNamesAbbr: weekdayNamesSwedishAbbr},
		90:    {tags: []string{"syr"}, localMonth: localMonthsNameSyriac, apFmt: apFmtSyriac, weekdayNames: weekdayNamesSyriac, weekdayNamesAbbr: weekdayNamesSyriacAbbr},
		1114:  {tags: []string{"syr-SY"}, localMonth: localMonthsNameSyriac, apFmt: apFmtSyriac, weekdayNames: weekdayNamesSyriac, weekdayNamesAbbr: weekdayNamesSyriacAbbr},
		40:    {tags: []string{"tg"}, localMonth: localMonthsNameTajik, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesTajik, weekdayNamesAbbr: weekdayNamesTajikAbbr},
		31784: {tags: []string{"tg-Cyrl"}, localMonth: localMonthsNameTajik, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesTajik, weekdayNamesAbbr: weekdayNamesTajikAbbr},
		1064:  {tags: []string{"tg-Cyrl-TJ"}, localMonth: localMonthsNameTajik, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesTajik, weekdayNamesAbbr: weekdayNamesTajikAbbr},
		95:    {tags: []string{"tzm"}, localMonth: localMonthsNameTamazight, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesTamazight, weekdayNamesAbbr: weekdayNamesTamazightAbbr},
		31839: {tags: []string{"tzm-Latn"}, localMonth: localMonthsNameTamazight, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesTamazight, weekdayNamesAbbr: weekdayNamesTamazightAbbr},
		2143:  {tags: []string{"tzm-Latn-DZ"}, localMonth: localMonthsNameTamazight, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesTamazight, weekdayNamesAbbr: weekdayNamesTamazightAbbr},
		73:    {tags: []string{"ta"}, localMonth: localMonthsNameTamil, apFmt: apFmtTamil, weekdayNames: weekdayNamesTamil, weekdayNamesAbbr: weekdayNamesTamilAbbr},
		1097:  {tags: []string{"ta-IN"}, localMonth: localMonthsNameTamil, apFmt: apFmtTamil, weekdayNames: weekdayNamesTamil, weekdayNamesAbbr: weekdayNamesTamilAbbr},
		2121:  {tags: []string{"ta-LK"}, localMonth: localMonthsNameTamilLK, apFmt: apFmtTamil, weekdayNames: weekdayNamesTamilLK, weekdayNamesAbbr: weekdayNamesTamilLKAbbr},
		68:    {tags: []string{"tt"}, localMonth: localMonthsNameTatar, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesTatar, weekdayNamesAbbr: weekdayNamesTatarAbbr},
		1092:  {tags: []string{"tt-RU"}, localMonth: localMonthsNameTatar, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesTatar, weekdayNamesAbbr: weekdayNamesTatarAbbr},
		74:    {tags: []string{"te"}, localMonth: localMonthsNameTelugu, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesTelugu, weekdayNamesAbbr: weekdayNamesTeluguAbbr},
		1098:  {tags: []string{"te-IN"}, localMonth: localMonthsNameTelugu, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesTelugu, weekdayNamesAbbr: weekdayNamesTeluguAbbr},
		30:    {tags: []string{"th"}, localMonth: localMonthsNameThai, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesThai, weekdayNamesAbbr: weekdayNamesThaiAbbr},
		1054:  {tags: []string{"th-TH"}, localMonth: localMonthsNameThai, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesThai, weekdayNamesAbbr: weekdayNamesThaiAbbr},
		81:    {tags: []string{"bo"}, localMonth: localMonthsNameTibetan, apFmt: apFmtTibetan, weekdayNames: weekdayNamesTibetan, weekdayNamesAbbr: weekdayNamesTibetanAbbr},
		1105:  {tags: []string{"bo-CN"}, localMonth: localMonthsNameTibetan, apFmt: apFmtTibetan, weekdayNames: weekdayNamesTibetan, weekdayNamesAbbr: weekdayNamesTibetanAbbr},
		115:   {tags: []string{"ti"}, localMonth: localMonthsNameTigrinya, apFmt: apFmtTigrinya, weekdayNames: weekdayNamesTigrinya, weekdayNamesAbbr: weekdayNamesTigrinyaAbbr},
		2163:  {tags: []string{"ti-ER"}, localMonth: localMonthsNameTigrinya, apFmt: apFmtTigrinyaER, weekdayNames: weekdayNamesTigrinya, weekdayNamesAbbr: weekdayNamesTigrinyaAbbr},
		1139:  {tags: []string{"ti-ET"}, localMonth: localMonthsNameTigrinya, apFmt: apFmtTigrinya, weekdayNames: weekdayNamesTigrinya, weekdayNamesAbbr: weekdayNamesTigrinyaAbbr},
		49:    {tags: []string{"ts"}, localMonth: localMonthsNameTsonga, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesTsonga, weekdayNamesAbbr: weekdayNamesTsongaAbbr},
		1073:  {tags: []string{"ts-ZA"}, localMonth: localMonthsNameTsonga, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesTsonga, weekdayNamesAbbr: weekdayNamesTsongaAbbr},
		31:    {tags: []string{"tr"}, localMonth: localMonthsNameTurkish, apFmt: apFmtTurkish, weekdayNames: weekdayNamesTurkish, weekdayNamesAbbr: weekdayNamesTurkishAbbr},
		1055:  {tags: []string{"tr-TR"}, localMonth: localMonthsNameTurkish, apFmt: apFmtTurkish, weekdayNames: weekdayNamesTurkish, weekdayNamesAbbr: weekdayNamesTurkishAbbr},
		66:    {tags: []string{"tk"}, localMonth: localMonthsNameTurkmen, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesTurkmen, weekdayNamesAbbr: weekdayNamesTurkmenAbbr},
		1090:  {tags: []string{"tk-TM"}, localMonth: localMonthsNameTurkmen, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesTurkmen, weekdayNamesAbbr: weekdayNamesTurkmenAbbr},
		34:    {tags: []string{"uk"}, localMonth: localMonthsNameUkrainian, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesUkrainian, weekdayNamesAbbr: weekdayNamesUkrainianAbbr},
		1058:  {tags: []string{"uk-UA"}, localMonth: localMonthsNameUkrainian, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesUkrainian, weekdayNamesAbbr: weekdayNamesUkrainianAbbr},
		46:    {tags: []string{"hsb"}, localMonth: localMonthsNameUpperSorbian, apFmt: apFmtUpperSorbian, weekdayNames: weekdayNamesSorbian, weekdayNamesAbbr: weekdayNamesSorbianAbbr},
		1070:  {tags: []string{"hsb-DE"}, localMonth: localMonthsNameUpperSorbian, apFmt: apFmtUpperSorbian, weekdayNames: weekdayNamesSorbian, weekdayNamesAbbr: weekdayNamesSorbianAbbr},
		32:    {tags: []string{"ur"}, localMonth: localMonthsNamePunjabiArab, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesUrdu, weekdayNamesAbbr: weekdayNamesUrdu},
		2080:  {tags: []string{"ur-IN"}, localMonth: localMonthsNamePunjabiArab, apFmt: apFmtUrdu, weekdayNames: weekdayNamesUrduIN, weekdayNamesAbbr: weekdayNamesUrduIN},
		1056:  {tags: []string{"ur-PK"}, localMonth: localMonthsNamePunjabiArab, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesUrdu, weekdayNamesAbbr: weekdayNamesUrdu},
		128:   {tags: []string{"ug"}, localMonth: localMonthsNameUyghur, apFmt: apFmtUyghur, weekdayNames: weekdayNamesUyghur, weekdayNamesAbbr: weekdayNamesUyghurAbbr},
		1152:  {tags: []string{"ug-CN"}, localMonth: localMonthsNameUyghur, apFmt: apFmtUyghur, weekdayNames: weekdayNamesUyghur, weekdayNamesAbbr: weekdayNamesUyghurAbbr},
		30787: {tags: []string{"uz-Cyrl"}, localMonth: localMonthsNameUzbekCyrillic, apFmt: apFmtUzbekCyrillic, weekdayNames: weekdayNamesUzbekCyrillic, weekdayNamesAbbr: weekdayNamesUzbekCyrillicAbbr},
		2115:  {tags: []string{"uz-Cyrl-UZ"}, localMonth: localMonthsNameUzbekCyrillic, apFmt: apFmtUzbekCyrillic, weekdayNames: weekdayNamesUzbekCyrillic, weekdayNamesAbbr: weekdayNamesUzbekCyrillicAbbr},
		67:    {tags: []string{"uz"}, localMonth: localMonthsNameUzbek, apFmt: apFmtUzbek, weekdayNames: weekdayNamesUzbek, weekdayNamesAbbr: weekdayNamesUzbekAbbr},
		31811: {tags: []string{"uz-Latn"}, localMonth: localMonthsNameUzbek, apFmt: apFmtUzbek, weekdayNames: weekdayNamesUzbek, weekdayNamesAbbr: weekdayNamesUzbekAbbr},
		1091:  {tags: []string{"uz-Latn-UZ"}, localMonth: localMonthsNameUzbek, apFmt: apFmtUzbek, weekdayNames: weekdayNamesUzbek, weekdayNamesAbbr: weekdayNamesUzbekAbbr},
		2051:  {tags: []string{"ca-ES-valencia"}, localMonth: localMonthsNameValencian, apFmt: apFmtSpanishAR, weekdayNames: weekdayNamesValencian, weekdayNamesAbbr: weekdayNamesValencianAbbr},
		51:    {tags: []string{"ve"}, localMonth: localMonthsNameVenda, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesVenda, weekdayNamesAbbr: weekdayNamesVendaAbbr},
		1075:  {tags: []string{"ve-ZA"}, localMonth: localMonthsNameVenda, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesVenda, weekdayNamesAbbr: weekdayNamesVendaAbbr},
		42:    {tags: []string{"vi"}, localMonth: localMonthsNameVietnamese, apFmt: apFmtVietnamese, weekdayNames: weekdayNamesVietnamese, weekdayNamesAbbr: weekdayNamesVietnameseAbbr},
		1066:  {tags: []string{"vi-VN"}, localMonth: localMonthsNameVietnamese, apFmt: apFmtVietnamese, weekdayNames: weekdayNamesVietnamese, weekdayNamesAbbr: weekdayNamesVietnameseAbbr},
		82:    {tags: []string{"cy"}, localMonth: localMonthsNameWelsh, apFmt: apFmtWelsh, weekdayNames: weekdayNamesWelsh, weekdayNamesAbbr: weekdayNamesWelshAbbr},
		1106:  {tags: []string{"cy-GB"}, localMonth: localMonthsNameWelsh, apFmt: apFmtWelsh, weekdayNames: weekdayNamesWelsh, weekdayNamesAbbr: weekdayNamesWelshAbbr},
		136:   {tags: []string{"wo"}, localMonth: localMonthsNameWolof, apFmt: apFmtWolof, weekdayNames: weekdayNamesWolof, weekdayNamesAbbr: weekdayNamesWolofAbbr},
		1160:  {tags: []string{"wo-SN"}, localMonth: localMonthsNameWolof, apFmt: apFmtWolof, weekdayNames: weekdayNamesWolof, weekdayNamesAbbr: weekdayNamesWolofAbbr},
		52:    {tags: []string{"xh"}, localMonth: localMonthsNameXhosa, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesXhosa, weekdayNamesAbbr: weekdayNamesXhosaAbbr},
		1076:  {tags: []string{"xh-ZA"}, localMonth: localMonthsNameXhosa, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesXhosa, weekdayNamesAbbr: weekdayNamesXhosaAbbr},
		120:   {tags: []string{"ii"}, localMonth: localMonthsNameYi, apFmt: apFmtYi, weekdayNames: weekdayNamesYi, weekdayNamesAbbr: weekdayNamesYiAbbr},
		1144:  {tags: []string{"ii-CN"}, localMonth: localMonthsNameYi, apFmt: apFmtYi, weekdayNames: weekdayNamesYi, weekdayNamesAbbr: weekdayNamesYiAbbr},
		1085:  {tags: []string{"yi-001"}, localMonth: localMonthsNameYiddish, apFmt: apFmtYiddish, weekdayNames: weekdayNamesYiddish, weekdayNamesAbbr: weekdayNamesYiddishAbbr},
		106:   {tags: []string{"yo"}, localMonth: localMonthsNameYoruba, apFmt: apFmtYoruba, weekdayNames: weekdayNamesYoruba, weekdayNamesAbbr: weekdayNamesYorubaAbbr},
		1130:  {tags: []string{"yo-NG"}, localMonth: localMonthsNameYoruba, apFmt: apFmtYoruba, weekdayNames: weekdayNamesYoruba, weekdayNamesAbbr: weekdayNamesYorubaAbbr},
		53:    {tags: []string{"zu"}, localMonth: localMonthsNameZulu, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesZulu, weekdayNamesAbbr: weekdayNamesZuluAbbr},
		1077:  {tags: []string{"zu-ZA"}, localMonth: localMonthsNameZulu, apFmt: nfp.AmPm[0], weekdayNames: weekdayNamesZulu, weekdayNamesAbbr: weekdayNamesZuluAbbr},
	}
	// supportedLanguageCodeInfo directly maps the supported language code and tags.
	supportedLanguageCodeInfo = map[string]languageInfo{
		"JA-JP-X-GANNEN":    {tags: []string{"ja-JP"}, localMonth: localMonthsNameChinese3, apFmt: apFmtJapanese, weekdayNames: weekdayNamesJapanese, weekdayNamesAbbr: weekdayNamesJapaneseAbbr},
		"JA-JP-X-GANNEN,80": {tags: []string{"ja-JP"}, localMonth: localMonthsNameChinese3, apFmt: apFmtJapanese, weekdayNames: weekdayNamesJapanese, weekdayNamesAbbr: weekdayNamesJapaneseAbbr, useGannen: true},
	}
	// republicOfChinaYear defined start time of the Republic of China
	republicOfChinaYear = time.Date(1912, time.January, 1, 0, 0, 0, 0, time.UTC)
	// republicOfChinaEraName defined the Republic of China era name for the Republic of China calendar.
	republicOfChinaEraName = []string{"\u4e2d\u83ef\u6c11\u570b", "\u6c11\u570b", "\u524d"}
	// japaneseEraYears list the Japanese era name periods.
	japaneseEraYears = []time.Time{
		time.Date(1868, time.August, 8, 0, 0, 0, 0, time.UTC),
		time.Date(1912, time.June, 30, 0, 0, 0, 0, time.UTC),
		time.Date(1926, time.November, 25, 0, 0, 0, 0, time.UTC),
		time.Date(1989, time.January, 8, 0, 0, 0, 0, time.UTC),
		time.Date(2019, time.April, 1, 0, 0, 0, 0, time.UTC),
	}
	// japaneseEraNames list the Japanese era name for the Japanese emperor reign calendar.
	japaneseEraNames = []string{"\u660E\u6CBB", "\u5927\u6B63", "\u662D\u548C", "\u5E73\u6210", "\u4EE4\u548C"}
	// japaneseEraYear list the Japanese era name symbols.
	japaneseEraSymbols = []string{"M", "T", "S", "H", "R"}
	// monthNamesAfrikaans list the month names in the Afrikaans.
	monthNamesAfrikaans = []string{"Januarie", "Februarie", "Maart", "April", "Mei", "Junie", "Julie", "Augustus", "September", "Oktober", "November", "Desember"}
	// monthNamesAfrikaansAbbr lists the month name abbreviations in the Afrikaans.
	monthNamesAfrikaansAbbr = []string{"Jan.", "Feb.", "Maa.", "Apr.", "Mei", "Jun.", "Jul.", "Aug.", "Sep.", "Okt.", "Nov.", "Des."}
	// monthNamesAlbanian list the month names in the Albanian.
	monthNamesAlbanian = []string{"janar", "shkurt", "mars", "prill", "maj", "qershor", "korrik", "gusht", "shtator", "tetor", "nëntor", "dhjetor"}
	// monthNamesAlbanianAbbr lists the month name abbreviations in the Albanian.
	monthNamesAlbanianAbbr = []string{"jan", "shk", "mar", "pri", "maj", "qer", "krr", "gush", "sht", "tet", "nën", "dhj"}
	// monthNamesAlsatian list the month names in the Alsatian.
	monthNamesAlsatian = []string{"Januar", "Februar", "März", "April", "Mai", "Juni", "Juli", "Auguscht", "Septämber", "Oktoober", "Novämber", "Dezämber"}
	// monthNamesAlsatianAbbr lists the month name abbreviations in the Alsatian France.
	monthNamesAlsatianAbbr = []string{"Jan", "Feb", "Mär", "Apr", "Mai", "Jun", "Jul", "Aug", "Sep", "Okt", "Nov", "Dez"}
	// monthNamesAlsatianFrance list the month names in the Alsatian.
	monthNamesAlsatianFrance = []string{"Jänner", "Feverje", "März", "Àpril", "Mai", "Jüni", "Jüli", "Augscht", "September", "Oktower", "Nowember", "Dezember"}
	// monthNamesAlsatianFranceAbbr lists the month name abbreviations in the Alsatian France.
	monthNamesAlsatianFranceAbbr = []string{"Jän.", "Fev.", "März", "Apr.", "Mai", "Jüni", "Jüli", "Aug.", "Sept.", "Okt.", "Now.", "Dez."}
	// monthNamesAmharic list the month names in the Amharic.
	monthNamesAmharic = []string{
		"\u1303\u1295\u12E9\u12C8\u122A",
		"\u134C\u1265\u1229\u12C8\u122A",
		"\u121B\u122D\u127D",
		"\u12A4\u1355\u122A\u120D",
		"\u121C\u12ED",
		"\u1301\u1295",
		"\u1301\u120B\u12ED",
		"\u12A6\u1308\u1235\u1275",
		"\u1234\u1355\u1274\u121D\u1260\u122D",
		"\u12A6\u12AD\u1276\u1260\u122D",
		"\u1296\u126C\u121D\u1260\u122D",
		"\u12F2\u1234\u121D\u1260\u122D",
	}
	// monthNamesAmharicAbbr lists the month name abbreviations in the Amharic.
	monthNamesAmharicAbbr = []string{
		"\u1303\u1295\u12E9",
		"\u134C\u1265\u1229",
		"\u121B\u122D\u127D",
		"\u12A4\u1355\u122A",
		"\u121C\u12ED",
		"\u1301\u1295",
		"\u1301\u120B\u12ED",
		"\u12A6\u1308\u1235",
		"\u1234\u1355\u1274",
		"\u12A6\u12AD\u1276",
		"\u1296\u126C\u121D",
		"\u12F2\u1234\u121D",
	}
	// monthNamesArabic list the month names in the Arabic.
	monthNamesArabic = []string{
		"\u064A\u0646\u0627\u064A\u0631",
		"\u0641\u0628\u0631\u0627\u064A\u0631",
		"\u0645\u0627\u0631\u0633",
		"\u0623\u0628\u0631\u064A\u0644",
		"\u0645\u0627\u064A\u0648",
		"\u064A\u0648\u0646\u064A\u0648",
		"\u064A\u0648\u0644\u064A\u0648",
		"\u0623\u063A\u0633\u0637\u0633",
		"\u0633\u0628\u062A\u0645\u0628\u0631",
		"\u0623\u0643\u062A\u0648\u0628\u0631",
		"\u0646\u0648\u0641\u0645\u0628\u0631",
		"\u062F\u064A\u0633\u0645\u0628\u0631",
	}
	// monthNamesArabicIraq list the month names in the Arabic Iraq.
	monthNamesArabicIraq = []string{
		"\u0643\u0627\u0646\u0648\u0646%A0\u0627\u0644\u062B\u0627\u0646\u064A",
		"\u0634\u0628\u0627\u0637",
		"\u0622\u0630\u0627\u0631",
		"\u0646\u064A\u0633\u0627\u0646",
		"\u0623\u064A\u0627\u0631",
		"\u062D\u0632\u064A\u0631\u0627\u0646",
		"\u062A\u0645\u0648\u0632",
		"\u0622\u0628",
		"\u0623\u064A\u0644\u0648\u0644",
		"\u062A\u0634\u0631\u064A\u0646%A0\u0627\u0644\u0623\u0648\u0644",
		"\u062A\u0634\u0631\u064A\u0646%A0\u0627\u0644\u062B\u0627\u0646\u064A",
		"\u0643\u0627\u0646\u0648\u0646%A0\u0627\u0644\u0623\u0648\u0644",
	}
	// monthNamesArmenian list the month names in the Armenian.
	monthNamesArmenian = []string{
		"\u0540\u0578\u0582\u0576\u057E\u0561\u0580",
		"\u0553\u0565\u057F\u0580\u057E\u0561\u0580",
		"\u0544\u0561\u0580\u057F",
		"\u0531\u057A\u0580\u056B\u056C",
		"\u0544\u0561\u0575\u056B\u057D",
		"\u0540\u0578\u0582\u0576\u056B\u057D",
		"\u0540\u0578\u0582\u056C\u056B\u057D",
		"\u0555\u0563\u0578\u057D\u057F\u0578\u057D",
		"\u054D\u0565\u057A\u057F\u0565\u0574\u0562\u0565\u0580",
		"\u0540\u0578\u056F\u057F\u0565\u0574\u0562\u0565\u0580",
		"\u0546\u0578\u0575\u0565\u0574\u0562\u0565\u0580",
		"\u0534\u0565\u056F\u057F\u0565\u0574\u0562\u0565\u0580",
	}
	// monthNamesArmenianAbbr lists the month name abbreviations in the Armenian.
	monthNamesArmenianAbbr = []string{
		"\u0540\u0576\u057E",
		"\u0553\u057F\u057E",
		"\u0544\u0580\u057F",
		"\u0531\u057A\u0580",
		"\u0544\u0575\u057D",
		"\u0540\u0576\u057D",
		"\u0540\u056C\u057D",
		"\u0555\u0563\u057D",
		"\u054D\u057A\u057F",
		"\u0540\u056F\u057F",
		"\u0546\u0575\u0574",
		"\u0534\u056F\u057F",
	}
	// monthNamesAssamese list the month names in the Assamese.
	monthNamesAssamese = []string{
		"\u099C\u09BE\u09A8\u09C1\u09F1\u09BE\u09F0\u09C0",
		"\u09AB\u09C7\u09AC\u09CD\u09B0\u09C1\u09F1\u09BE\u09F0\u09C0",
		"\u09AE\u09BE\u09B0\u09CD\u099A",
		"\u098F\u09AA\u09CD\u09B0\u09BF\u09B2",
		"\u09AE\u09C7",
		"\u099C\u09C1\u09A8",
		"\u099C\u09C1\u09B2\u09BE\u0987",
		"\u0986\u0997\u09B7\u09CD\u099F",
		"\u099A\u09C7\u09AA\u09CD\u099F\u09C7\u09AE\u09CD\u09AC\u09F0",
		"\u0985\u0995\u09CD\u099F\u09CB\u09AC\u09F0",
		"\u09A8\u09AC\u09C7\u09AE\u09CD\u09AC\u09F0",
		"\u09A1\u09BF\u099A\u09C7\u09AE\u09CD\u09AC\u09F0",
	}
	// monthNamesAssameseAbbr lists the month name abbreviations in the Assamese.
	monthNamesAssameseAbbr = []string{
		"\u099C\u09BE\u09A8\u09C1",
		"\u09AB\u09C7\u09AC\u09CD\u09B0\u09C1",
		"\u09AE\u09BE\u09B0\u09CD\u099A",
		"\u098F\u09AA\u09CD\u09B0\u09BF\u09B2",
		"\u09AE\u09C7",
		"\u099C\u09C1\u09A8",
		"\u099C\u09C1\u09B2\u09BE\u0987",
		"\u0986\u0997\u09B7\u09CD\u099F",
		"\u099A\u09C7\u09AA\u09CD\u099F\u09C7",
		"\u0985\u0995\u09CD\u099F\u09CB",
		"\u09A8\u09AC\u09C7",
		"\u09A1\u09BF\u099A\u09C7",
	}
	// monthNamesAzerbaijaniCyrillic list the month names in the Azerbaijani (Cyrillic).
	monthNamesAzerbaijaniCyrillic = []string{
		"j\u0430\u043D\u0432\u0430\u0440",
		"\u0444\u0435\u0432\u0440\u0430\u043B",
		"\u043C\u0430\u0440\u0442",
		"\u0430\u043F\u0440\u0435\u043B",
		"\u043C\u0430\u0458",
		"\u0438\u0458\u0443\u043D",
		"\u0438\u0458\u0443\u043B",
		"\u0430\u0432\u0433\u0443\u0441\u0442",
		"\u0441\u0435\u043D\u0442\u0458\u0430\u0431\u0440",
		"\u043E\u043A\u0442\u0458\u0430\u0431\u0440",
		"\u043D\u043E\u0458\u0430\u0431\u0440",
		"\u0434\u0435\u043A\u0430\u0431\u0440",
	}
	// monthNamesAzerbaijaniCyrillicAbbr lists the month name abbreviations in the Azerbaijani (Cyrillic).
	monthNamesAzerbaijaniCyrillicAbbr = []string{
		"\u0408\u0430\u043D",
		"\u0424\u0435\u0432",
		"\u041C\u0430\u0440",
		"\u0410\u043F\u0440",
		"\u041C\u0430\u0458",
		"\u0418\u0458\u0443\u043D",
		"\u0418\u0458\u0443\u043B",
		"\u0410\u0432\u0433",
		"\u0421\u0435\u043D",
		"\u041E\u043A\u0442",
		"\u041D\u043E\u044F",
		"\u0414\u0435\u043A",
	}
	// monthNamesAzerbaijani list the month names in the Azerbaijani.
	monthNamesAzerbaijani = []string{"yanvar", "fevral", "mart", "aprel", "may", "iyun", "iyul", "avgust", "sentyabr", "oktyabr", "noyabr", "dekabr"}
	// monthNamesAzerbaijaniAbbr lists the month name abbreviations in the Azerbaijani.
	monthNamesAzerbaijaniAbbr = []string{"yan", "fev", "mar", "apr", "may", "iyn", "iyl", "avq", "sen", "okt", "noy", "dek"}
	// monthNamesAustria list the month names in the Austrian.
	monthNamesAustria = []string{"Jänner", "Februar", "März", "April", "Mai", "Juni", "Juli", "August", "September", "Oktober", "November", "Dezember"}
	// monthNamesAustriaAbbr list the month name abbreviations in the Austrian.
	monthNamesAustriaAbbr = []string{"Jän", "Feb", "Mär", "Apr", "Mai", "Jun", "Jul", "Aug", "Sep", "Okt", "Nov", "Dez"}
	// monthNamesBangla list the month names in the Bangla.
	monthNamesBangla = []string{
		"\u099C\u09BE\u09A8\u09C1\u09AF\u09BC\u09BE\u09B0\u09C0",
		"\u09AB\u09C7\u09AC\u09CD\u09B0\u09C1\u09AF\u09BC\u09BE\u09B0\u09C0",
		"\u09AE\u09BE\u09B0\u09CD\u099A",
		"\u098F\u09AA\u09CD\u09B0\u09BF\u09B2",
		"\u09AE\u09C7",
		"\u099C\u09C1\u09A8",
		"\u099C\u09C1\u09B2\u09BE\u0987",
		"\u0986\u0997\u09B8\u09CD\u099F",
		"\u09B8\u09C7\u09AA\u09CD\u099F\u09C7\u09AE\u09CD\u09AC\u09B0",
		"\u0985\u0995\u09CD\u099F\u09CB\u09AC\u09B0",
		"\u09A8\u09AD\u09C7\u09AE\u09CD\u09AC\u09B0",
		"\u09A1\u09BF\u09B8\u09C7\u09AE\u09CD\u09AC\u09B0",
	}
	// monthNamesBashkir list the month names in the Bashkir.
	monthNamesBashkir = []string{
		"\u0493\u0438\u043D\u0443\u0430\u0440",
		"\u0444\u0435\u0432\u0440\u0430\u043B\u044C",
		"\u043C\u0430\u0440\u0442",
		"\u0430\u043F\u0440\u0435\u043B\u044C",
		"\u043C\u0430\u0439",
		"\u0438\u044E\u043D\u044C",
		"\u0438\u044E\u043B\u044C",
		"\u0430\u0432\u0433\u0443\u0441\u0442",
		"\u0441\u0435\u043D\u0442\u044F\u0431\u0440\u044C",
		"\u043E\u043A\u0442\u044F\u0431\u0440\u044C",
		"\u043D\u043E\u044F\u0431\u0440\u044C",
		"\u0434\u0435\u043A\u0430\u0431\u0440\u044C",
	}
	// monthNamesBashkirAbbr lists the month name abbreviations in the Bashkir.
	monthNamesBashkirAbbr = []string{
		"\u0493\u0438\u043D",
		"\u0444\u0435\u0432",
		"\u043C\u0430\u0440",
		"\u0430\u043F\u0440",
		"\u043C\u0430\u0439",
		"\u0438\u044E\u043D",
		"\u0438\u044E\u043B",
		"\u0430\u0432\u0433",
		"\u0441\u0435\u043D",
		"\u043E\u043A\u0442",
		"\u043D\u043E\u044F",
		"\u0434\u0435\u043A",
	}
	// monthNamesBasque list the month names in the Basque.
	monthNamesBasque = []string{"urtarrila", "otsaila", "martxoa", "apirila", "maiatza", "ekaina", "uztaila", "abuztua", "iraila", "urria", "azaroa", "abendua"}
	// monthNamesBasqueAbbr lists the month name abbreviations in the Basque.
	monthNamesBasqueAbbr = []string{"urt.", "ots.", "mar.", "api.", "mai.", "eka.", "uzt.", "abu.", "ira.", "urr.", "aza.", "abe."}
	// monthNamesBelarusian list the month names in the Belarusian.
	monthNamesBelarusian = []string{
		"\u0441\u0442\u0443\u0434\u0437\u0435\u043D\u044C",
		"\u043B\u044E\u0442\u044B",
		"\u0441\u0430\u043A\u0430\u0432\u0456\u043A",
		"\u043A\u0440\u0430\u0441\u0430\u0432\u0456\u043A",
		"\u043C\u0430\u0439",
		"\u0447\u044D\u0440\u0432\u0435\u043D\u044C",
		"\u043B\u0456\u043F\u0435\u043D\u044C",
		"\u0436\u043D\u0456\u0432\u0435\u043D\u044C",
		"\u0432\u0435\u0440\u0430\u0441\u0435\u043D\u044C",
		"\u043A\u0430\u0441\u0442\u0440\u044B\u0447\u043D\u0456\u043A",
		"\u043B\u0456\u0441\u0442\u0430\u043F\u0430\u0434",
		"\u0441\u043D\u0435\u0436\u0430\u043D\u044C",
	}
	// monthNamesBelarusianAbbr lists the month name abbreviations in the Belarusian.
	monthNamesBelarusianAbbr = []string{
		"\u0441\u0442\u0443\u0434\u0437",
		"\u043B\u044E\u0442",
		"\u0441\u0430\u043A",
		"\u043A\u0440\u0430\u0441",
		"\u043C\u0430\u0439",
		"\u0447\u044D\u0440\u0432",
		"\u043B\u0456\u043F",
		"\u0436\u043D",
		"\u0432\u0435\u0440",
		"\u043A\u0430\u0441\u0442\u0440",
		"\u043B\u0456\u0441\u0442",
		"\u0441\u043D\u0435\u0436",
	}
	// monthNamesBosnianCyrillic list the month names in the Bosnian (Cyrillic).
	monthNamesBosnianCyrillic = []string{
		"\u0458\u0430\u043D\u0443\u0430\u0440",
		"\u0444\u0435\u0431\u0440\u0443\u0430\u0440",
		"\u043C\u0430\u0440\u0442",
		"\u0430\u043F\u0440\u0438\u043B",
		"\u043C\u0430\u0458",
		"\u0458\u0443\u043D",
		"\u0458\u0443\u043B",
		"\u0430\u0432\u0433\u0443\u0441\u0442",
		"\u0441\u0435\u043F\u0442\u0435\u043C\u0431\u0430\u0440",
		"\u043E\u043A\u0442\u043E\u0431\u0430\u0440",
		"\u043D\u043E\u0432\u0435\u043C\u0431\u0430\u0440",
		"\u0434\u0435\u0446\u0435\u043C\u0431\u0430\u0440",
	}
	// monthNamesBosnianCyrillicAbbr lists the month name abbreviations in the Bosnian (Cyrillic).
	monthNamesBosnianCyrillicAbbr = []string{
		"\u0458\u0430\u043D",
		"\u0444\u0435\u0431",
		"\u043C\u0430\u0440",
		"\u0430\u043F\u0440",
		"\u043C\u0430\u0458",
		"\u0458\u0443\u043D",
		"\u0458\u0443\u043B",
		"\u0430\u0432\u0433",
		"\u0441\u0435\u043F",
		"\u043E\u043A\u0442",
		"\u043D\u043E\u0432",
		"\u0434\u0435\u0446",
	}
	// monthNamesBosnian list the month names in the Bosnian.
	monthNamesBosnian = []string{"januar", "februar", "mart", "april", "maj", "juni", "juli", "august", "septembar", "oktobar", "novembar", "decembar"}
	// monthNamesBosnianAbbr lists the month name abbreviations in the Bosnian.
	monthNamesBosnianAbbr = []string{"jan", "feb", "mar", "apr", "maj", "jun", "jul", "aug", "sep", "okt", "nov", "dec"}
	// monthNamesBreton list the month names in the Breton.
	monthNamesBreton = []string{"Genver", "Cʼhwevrer", "Meurzh", "Ebrel", "Mae", "Mezheven", "Gouere", "Eost", "Gwengolo", "Here", "Du", "Kerzu"}
	// monthNamesBretonAbbr lists the month name abbreviations in the Breton.
	monthNamesBretonAbbr = []string{"Gen.", "Cʼhwe.", "Meur.", "Ebr.", "Mae", "Mezh.", "Goue.", "Eost", "Gwen.", "Here", "Du", "Kzu."}
	// monthNamesBulgarian list the month names in the Bulgarian.
	monthNamesBulgarian = []string{
		"\u044F\u043D\u0443\u0430\u0440\u0438",
		"\u0444\u0435\u0432\u0440\u0443\u0430\u0440\u0438",
		"\u043C\u0430\u0440\u0442",
		"\u0430\u043F\u0440\u0438\u043B",
		"\u043C\u0430\u0439",
		"\u044E\u043D\u0438",
		"\u044E\u043B\u0438",
		"\u0430\u0432\u0433\u0443\u0441\u0442",
		"\u0441\u0435\u043F\u0442\u0435\u043C\u0432\u0440\u0438",
		"\u043E\u043A\u0442\u043E\u043C\u0432\u0440\u0438",
		"\u043D\u043E\u0435\u043C\u0432\u0440\u0438",
		"\u0434\u0435\u043A\u0435\u043C\u0432\u0440\u0438",
	}
	// monthNamesBurmese list the month names in the Burmese.
	monthNamesBurmese = []string{
		"\u1007\u1014\u103A\u1014\u101D\u102B\u101B\u102E",
		"\u1016\u1031\u1016\u1031\u102C\u103A\u101D\u102B\u101B\u102E",
		"\u1019\u1010\u103A",
		"\u1027\u1015\u103C\u102E",
		"\u1019\u1031",
		"\u1007\u103D\u1014\u103A",
		"\u1007\u1030\u101C\u102D\u102F\u1004\u103A",
		"\u1029\u1002\u102F\u1010\u103A",
		"\u1005\u1000\u103A\u1010\u1004\u103A\u1018\u102C",
		"\u1021\u1031\u102C\u1000\u103A\u1010\u102D\u102F\u1018\u102C",
		"\u1014\u102D\u102F\u101D\u1004\u103A\u1018\u102C",
		"\u1012\u102E\u1007\u1004\u103A\u1018\u102C",
	}
	// monthNamesBurmeseAbbr lists the month name abbreviations in the Burmese.
	monthNamesBurmeseAbbr = []string{
		"\u1007\u1014\u103A",
		"\u1016\u1031",
		"\u1019\u1010\u103A",
		"\u1027",
		"\u1019\u1031",
		"\u1007\u103D\u1014\u103A",
		"\u1007\u1030",
		"\u1029",
		"\u1005\u1000\u103A",
		"\u1021\u1031\u102C\u1000\u103A",
		"\u1014\u102D\u102F",
		"\u1012\u102E",
	}
	//	monthNamesCaribbean list the month names in the Caribbean.
	monthNamesCaribbean = []string{"Janvier", "Février", "Mars", "Avril", "Mai", "Juin", "Juillet", "Août", "Septembre", "Octobre", "Novembre", "Décembre"}
	//	monthNamesCaribbeanAbbr lists the month name abbreviations in the Caribbean.
	monthNamesCaribbeanAbbr = []string{"Janv.", "Févr.", "Mars", "Avr.", "Mai", "Juin", "Juil.", "Août", "Sept.", "Oct.", "Nov.", "Déc."}
	//	monthNamesCentralKurdish list the month names in the Central Kurdish.
	monthNamesCentralKurdish = []string{
		"\u06A9\u0627\u0646\u0648\u0648\u0646\u06CC%20\u062F\u0648\u0648\u06D5\u0645",
		"\u0634\u0648\u0628\u0627\u062A",
		"\u0626\u0627\u0632\u0627\u0631",
		"\u0646\u06CC\u0633\u0627\u0646",
		"\u0626\u0627\u06CC\u0627\u0631",
		"\u062D\u0648\u0632\u06D5\u06CC\u0631\u0627\u0646",
		"\u062A\u06D5\u0645\u0648\u0648\u0632",
		"\u0626\u0627\u0628",
		"\u0626\u06D5\u06CC\u0644\u0648\u0648\u0644",
		"\u062A\u0634\u0631\u06CC\u0646\u06CC%20\u06CC\u06D5\u06A9\u06D5\u0645",
		"\u062A\u0634\u0631\u06CC\u0646\u06CC%20\u062F\u0648\u0648\u06D5\u0645",
		"\u06A9\u0627\u0646\u0648\u0646\u06CC%20\u06CC\u06D5\u06A9\u06D5\u0645",
	}
	// monthNamesCherokee list the month names in the Cherokee.
	monthNamesCherokee = []string{
		"\u13A4\u13C3\u13B8\u13D4\u13C5",
		"\u13A7\u13A6\u13B5",
		"\u13A0\u13C5\u13F1",
		"\u13DD\u13EC\u13C2",
		"\u13A0\u13C2\u13CD\u13AC\u13D8",
		"\u13D5\u13AD\u13B7\u13F1",
		"\u13AB\u13F0\u13C9\u13C2",
		"\u13A6\u13B6\u13C2",
		"\u13DA\u13B5\u13CD\u13D7",
		"\u13DA\u13C2\u13C5\u13D7",
		"\u13C5\u13D3\u13D5\u13C6",
		"\u13A4\u13CD\u13A9\u13F1",
	}
	// monthNamesCherokeeAbbr lists the month name abbreviations in the Cherokee.
	monthNamesCherokeeAbbr = []string{
		"\u13A4\u13C3\u13B8",
		"\u13A7\u13A6\u13B5",
		"\u13A0\u13C5\u13F1",
		"\u13DD\u13EC\u13C2",
		"\u13A0\u13C2\u13CD",
		"\u13D5\u13AD\u13B7",
		"\u13AB\u13F0\u13C9",
		"\u13A6\u13B6\u13C2",
		"\u13DA\u13B5\u13CD",
		"\u13DA\u13C2\u13C5",
		"\u13C5\u13D3\u13D5",
		"\u13A4\u13CD\u13A9",
	}
	// monthNamesChinese list the month names in the Chinese.
	monthNamesChinese = []string{"一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"}
	// monthNamesChineseAbbr lists the month name abbreviations in the Chinese.
	monthNamesChineseAbbr = []string{"一", "二", "三", "四", "五", "六", "七", "八", "九", "十", "十一", "十二"}
	// monthNamesChineseNum list the month number and character abbreviation in the Chinese.
	monthNamesChineseNum = []string{"0月", "1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月"}
	// monthNamesEstonian list the month names in the Estonian.
	monthNamesEstonian = []string{"jaanuar", "veebruar", "märts", "aprill", "mai", "juuni", "juuli", "august", "september", "oktoober", "november", "detsember"}
	// monthNamesEstonianAbbr lists the month name abbreviations in the Estonian.
	monthNamesEstonianAbbr = []string{"jaan", "veebr", "märts", "apr", "mai", "juuni", "juuli", "aug", "sept", "okt", "nov", "dets"}
	// monthNamesFaroese list the month names in the Faroese.
	monthNamesFaroese = []string{"januar", "februar", "mars", "apríl", "mai", "juni", "juli", "august", "september", "oktober", "november", "desember"}
	// monthsNameFaroeseAbbr lists the month name abbreviations in the Faroese.
	monthsNameFaroeseAbbr = []string{"jan", "feb", "mar", "apr", "mai", "jun", "jul", "aug", "sep", "okt", "nov", "des"}
	// monthNamesFilipino list the month names in the Filipino.
	monthNamesFilipino = []string{"Enero", "Pebrero", "Marso", "Abril", "Mayo", "Hunyo", "Hulyo", "Agosto", "Setyembre", "Oktubre", "Nobyembre", "Disyembre"}
	// monthNamesFilipinoAbbr lists the month name abbreviations in the Filipino.
	monthNamesFilipinoAbbr = []string{"Ene", "Peb", "Mar", "Abr", "May", "Hun", "Hul", "Ago", "Set", "Okt", "Nob", "Dis"}
	// monthsNamesFinnish list the month names in the Finnish.
	monthNamesFinnish = []string{"Etammikuu", "helmikuu", "maaliskuu", "huhtikuu", "toukokuu", "kesäkuu", "heinäkuu", "elokuu", "syyskuu", "lokakuu", "marraskuu", "joulukuu"}
	// monthsNamesFinnishAbbr lists the month name abbreviations in the Finnish.
	monthNamesFinnishAbbr = []string{"tammi", "helmi", "maalis", "huhti", "touko", "kesä", "heinä", "elo", "syys", "loka", "marras", "joulu"}
	// monthNamesFrench list the month names in the French.
	monthNamesFrench = []string{"janvier", "février", "mars", "avril", "mai", "juin", "juillet", "août", "septembre", "octobre", "novembre", "décembre"}
	// monthNamesFrenchAbbr lists the month name abbreviations in the French.
	monthNamesFrenchAbbr = []string{"janv.", "févr.", "mars", "avr.", "mai", "juin", "juil.", "août", "sept.", "oct.", "nov.", "déc."}
	// monthNamesFrisian list the month names in the Frisian.
	monthNamesFrisian = []string{"Jannewaris", "Febrewaris", "Maart", "April", "Maaie", "Juny", "July", "Augustus", "Septimber", "Oktober", "Novimber", "Desimber"}
	// monthNamesFrisianAbbr lists the month name abbreviations in the Frisian.
	monthNamesFrisianAbbr = []string{"jan", "feb", "Mrt", "Apr", "maa", "Jun", "Jul", "Aug", "sep", "Okt", "Nov", "Des"}
	// monthNamesFulah list the month names in the Fulah.
	monthNamesFulah = []string{"siilo", "colte", "mbooy", "seeɗto", "duujal", "korse", "morso", "juko", "siilto", "yarkomaa", "jolal", "bowte"}
	// monthNamesFulahAbbr lists the month name abbreviations in the Fulah.
	monthNamesFulahAbbr = []string{"sii", "col", "mbo", "see", "duu", "kor", "mor", "juk", "slt", "yar", "jol", "bow"}
	// monthNamesGalician list the month names in the Galician.
	monthNamesGalician = []string{"Xaneiro", "Febreiro", "Marzo", "Abril", "Maio", "Xuño", "Xullo", "Agosto", "Setembro", "Outubro", "Novembro", "Decembro"}
	// monthNamesGalicianAbbr lists the month name abbreviations in the Galician.
	monthNamesGalicianAbbr = []string{"Xan.", "Feb.", "Mar.", "Abr.", "Maio", "Xuño", "Xul.", "Ago.", "Set.", "Out.", "Nov.", "Dec."}
	// monthNamesGeorgian list the month names in the Georgian.
	monthNamesGeorgian = []string{
		"\u10D8\u10D0\u10DC\u10D5\u10D0\u10E0\u10D8",
		"\u10D7\u10D4\u10D1\u10D4\u10E0\u10D5\u10D0\u10DA\u10D8",
		"\u10DB\u10D0\u10E0\u10E2\u10D8",
		"\u10D0\u10DE\u10E0\u10D8\u10DA\u10D8",
		"\u10DB\u10D0\u10D8\u10E1\u10D8",
		"\u10D8\u10D5\u10DC\u10D8\u10E1\u10D8",
		"\u10D8\u10D5\u10DA\u10D8\u10E1\u10D8",
		"\u10D0\u10D2\u10D5\u10D8\u10E1\u10E2\u10DD",
		"\u10E1\u10D4\u10E5\u10E2\u10D4\u10DB\u10D1\u10D4\u10E0\u10D8",
		"\u10DD\u10E5\u10E2\u10DD\u10DB\u10D1\u10D4\u10E0\u10D8",
		"\u10DC\u10DD\u10D4\u10DB\u10D1\u10D4\u10E0\u10D8",
		"\u10D3\u10D4\u10D9\u10D4\u10DB\u10D1\u10D4\u10E0\u10D8",
	}
	// monthNamesGeorgianAbbr lists the month name abbreviations in the Georgian.
	monthNamesGeorgianAbbr = []string{
		"\u10D8\u10D0\u10DC",
		"\u10D7\u10D4\u10D1",
		"\u10DB\u10D0\u10E0",
		"\u10D0\u10DE\u10E0",
		"\u10DB\u10D0\u10D8",
		"\u10D8\u10D5\u10DC",
		"\u10D8\u10D5\u10DA",
		"\u10D0\u10D2\u10D5",
		"\u10E1\u10D4\u10E5",
		"\u10DD\u10E5\u10E2",
		"\u10DC\u10DD\u10D4",
		"\u10D3\u10D4\u10D9",
	}
	// monthNamesGerman list the month names in the German.
	monthNamesGerman = []string{"Januar", "Februar", "März", "April", "Mai", "Juni", "Juli", "August", "September", "Oktober", "November", "Dezember"}
	// monthNamesGermanAbbr list the month abbreviations in the German.
	monthNamesGermanAbbr = []string{"Jan", "Feb", "Mär", "Apr", "Mai", "Jun", "Jul", "Aug", "Sep", "Okt", "Nov", "Dez"}
	// monthNamesGreek list the month names in the Greek.
	monthNamesGreek = []string{
		"\u0399\u03B1\u03BD\u03BF\u03C5\u03AC\u03C1\u03B9\u03BF\u03C2",
		"\u03A6\u03B5\u03B2\u03C1\u03BF\u03C5\u03AC\u03C1\u03B9\u03BF\u03C2",
		"\u039C\u03AC\u03C1\u03C4\u03B9\u03BF\u03C2",
		"\u0391\u03C0\u03C1\u03AF\u03BB\u03B9\u03BF\u03C2",
		"\u039C\u03AC\u03B9\u03BF\u03C2",
		"\u0399\u03BF\u03CD\u03BD\u03B9\u03BF\u03C2",
		"\u0399\u03BF\u03CD\u03BB\u03B9\u03BF\u03C2",
		"\u0391\u03CD\u03B3\u03BF\u03C5\u03C3\u03C4\u03BF\u03C2",
		"\u03A3\u03B5\u03C0\u03C4\u03AD\u03BC\u03B2\u03C1\u03B9\u03BF\u03C2",
		"\u039F\u03BA\u03C4\u03CE\u03B2\u03C1\u03B9\u03BF\u03C2",
		"\u039D\u03BF\u03AD\u03BC\u03B2\u03C1\u03B9\u03BF\u03C2",
		"\u0394\u03B5\u03BA\u03AD\u03BC\u03B2\u03C1\u03B9\u03BF\u03C2",
	}
	// monthNamesGreekAbbr list the month abbreviations in the Greek.
	monthNamesGreekAbbr = []string{
		"\u0399\u03B1\u03BD",
		"\u03A6\u03B5\u03B2",
		"\u039C\u03B1\u03C1",
		"\u0391\u03C0\u03C1",
		"\u039C\u03B1\u03CA",
		"\u0399\u03BF\u03C5\u03BD",
		"\u0399\u03BF\u03C5\u03BB",
		"\u0391\u03C5\u03B3",
		"\u03A3\u03B5\u03C0",
		"\u039F\u03BA\u03C4",
		"\u039D\u03BF\u03B5",
		"\u0394\u03B5\u03BA",
	}
	// monthNamesGreenlandic list the month names in the Greenlandic.
	monthNamesGreenlandic = []string{"januaari", "februaari", "marsi", "apriili", "maaji", "juuni", "juuli", "aggusti", "septembari", "oktobari", "novembari", "decembari"}
	// monthNamesGreenlandicAbbr list the month abbreviations in the Greenlandic.
	monthNamesGreenlandicAbbr = []string{"jan", "feb", "mar", "apr", "mai", "jun", "jul", "aug", "sep", "okt", "nov", "dec"}
	// monthNamesGuarani list the month names in the Guarani.
	monthNamesGuarani = []string{"jasyte\u0129", "jasyk%F5i", "jasyapy", "jasyrundy", "jasypo", "jasypote\u0129", "jasypok%F5i", "jasypoapy", "jasyporundy", "jasypa", "jasypate\u0129", "jasypak%F5i"}
	// monthNamesGuaraniAbbr list the month abbreviations in the Guarani.
	monthNamesGuaraniAbbr = []string{"jteĩ", "jkõi", "japy", "jrun", "jpo", "jpot", "jpok", "jpoa", "jpor", "jpa", "jpat", "jpak"}
	// monthNamesGujarati list the month names in the Gujarati.
	monthNamesGujarati = []string{
		"\u0A9C\u0ABE\u0AA8\u0ACD\u0AAF\u0AC1\u0A86\u0AB0\u0AC0",
		"\u0AAB\u0AC7\u0AAC\u0ACD\u0AB0\u0AC1\u0A86\u0AB0\u0AC0",
		"\u0AAE\u0ABE\u0AB0\u0ACD\u0A9A",
		"\u0A8F\u0AAA\u0ACD\u0AB0\u0ABF\u0AB2",
		"\u0AAE\u0AC7",
		"\u0A9C\u0AC2\u0AA8",
		"\u0A9C\u0AC1\u0AB2\u0ABE\u0A88",
		"\u0A91\u0A97\u0AB8\u0ACD\u0A9F",
		"\u0AB8\u0AAA\u0ACD\u0A9F\u0AC7\u0AAE\u0ACD\u0AAC\u0AB0",
		"\u0A91\u0A95\u0ACD\u0A9F\u0ACB\u0AAC\u0AB0",
		"\u0AA8\u0AB5\u0AC7\u0AAE\u0ACD\u0AAC\u0AB0",
		"\u0AA1\u0ABF\u0AB8\u0AC7\u0AAE\u0ACD\u0AAC\u0AB0",
	}
	// monthNamesGujaratiAbbr list the month abbreviations in the Gujarati.
	monthNamesGujaratiAbbr = []string{
		"\u0A9C\u0ABE\u0AA8\u0ACD\u0AAF\u0AC1",
		"\u0AAB\u0AC7\u0AAC\u0ACD\u0AB0\u0AC1",
		"\u0AAE\u0ABE\u0AB0\u0ACD\u0A9A",
		"\u0A8F\u0AAA\u0ACD\u0AB0\u0ABF\u0AB2",
		"\u0AAE\u0AC7",
		"\u0A9C\u0AC2\u0AA8",
		"\u0A9C\u0AC1\u0AB2\u0ABE\u0A88",
		"\u0A91\u0A97",
		"\u0AB8\u0AAA\u0ACD\u0A9F\u0AC7",
		"\u0A91\u0A95\u0ACD\u0A9F\u0ACB",
		"\u0AA8\u0AB5\u0AC7",
		"\u0AA1\u0ABF\u0AB8\u0AC7",
	}
	// monthNamesHausa list the month names in the Hausa.
	monthNamesHausa = []string{"Janairu", "Fabrairu", "Maris", "Afirilu", "Mayu", "Yuni", "Yuli", "Agusta", "Satumba", "Oktoba", "Nuwamba", "Disamba"}
	// monthNamesHawaiian list the month names in the Hawaiian.
	monthNamesHawaiian = []string{"Ianuali", "Pepeluali", "Malaki", "ʻApelila", "Mei", "Iune", "Iulai", "ʻAukake", "Kepakemapa", "ʻOkakopa", "Nowemapa", "Kekemapa"}
	// monthNamesHawaiianAbbr list the month name abbreviations in the Hawaiiann.
	monthNamesHawaiianAbbr = []string{"Ian.", "Pep.", "Mal.", "ʻAp.", "Mei", "Iun.", "Iul.", "ʻAu.", "Kep.", "ʻOk.", "Now.", "Kek."}
	// monthNamesHebrew list the month names in the Hebrew.
	monthNamesHebrew = []string{
		"\u05D9\u05E0\u05D5\u05D0\u05E8",
		"\u05E4\u05D1\u05E8\u05D5\u05D0\u05E8",
		"\u05DE\u05E8\u05E5",
		"\u05D0\u05E4\u05E8\u05D9\u05DC",
		"\u05DE\u05D0\u05D9",
		"\u05D9\u05D5\u05E0\u05D9",
		"\u05D9\u05D5\u05DC\u05D9",
		"\u05D0\u05D5\u05D2\u05D5\u05E1\u05D8",
		"\u05E1\u05E4\u05D8\u05DE\u05D1\u05E8",
		"\u05D0\u05D5\u05E7\u05D8\u05D5\u05D1\u05E8",
		"\u05E0\u05D5\u05D1\u05DE\u05D1\u05E8",
		"\u05D3\u05E6\u05DE\u05D1\u05E8",
	}
	// monthNamesHindi list the month names in the Hindi.
	monthNamesHindi = []string{
		"\u091C\u0928\u0935\u0930\u0940",
		"\u092B\u0930\u0935\u0930\u0940",
		"\u092E\u093E\u0930\u094D\u091A",
		"\u0905\u092A\u094D\u0930\u0948\u0932",
		"\u092E\u0908",
		"\u091C\u0942\u0928",
		"\u091C\u0941\u0932\u093E\u0908",
		"\u0905\u0917\u0938\u094D\u0924",
		"\u0938\u093F\u0924\u092E\u094D\u092C\u0930",
		"\u0905\u0915\u094D\u0924\u0942\u092C\u0930",
		"\u0928\u0935\u092E\u094D\u092C\u0930",
		"\u0926\u093F\u0938\u092E\u094D\u092C\u0930",
	}
	// monthNamesHungarian list the month names in the Hungarian.
	monthNamesHungarian = []string{"január", "február", "március", "április", "május", "június", "július", "augusztus", "szeptember", "október", "november", "december"}
	// monthNamesHungarianAbbr list the month name abbreviations in the Hungarian.
	monthNamesHungarianAbbr = []string{"jan.", "febr.", "márc.", "ápr.", "máj.", "jún.", "júl.", "aug.", "szept.", "okt.", "nov.", "dec."}
	// monthNamesIcelandic list the month names in the Icelandic.
	monthNamesIcelandic = []string{"janúar", "febrúar", "mars", "apríl", "maí", "júní", "júlí", "ágúst", "september", "október", "nóvember", "desember"}
	// monthNamesIcelandicAbbr list the month name abbreviations in the Icelandic.
	monthNamesIcelandicAbbr = []string{"jan.", "feb.", "mar.", "apr.", "maí", "jún.", "júl.", "ágú.", "sep.", "okt.", "nóv.", "des."}
	// monthNamesIgbo list the month names in the Igbo.
	monthNamesIgbo = []string{"Jenụwarị", "Febụwarị", "Machị", "Eprelu", "Mey", "Juun", "Julaị", "Ọgọst", "Septemba", "Ọcktọba", "Nọvemba", "Disemba"}
	// monthNamesIgboAbbr list the month name abbreviations in the Igbo.
	monthNamesIgboAbbr = []string{"Jen", "Feb", "Mac", "Epr", "Mey", "Jun", "Jul", "Ọgọ", "Sep", "Ọkt", "Nọv", "Dis"}
	// monthNamesIndonesian list the month names in the Indonesian.
	monthNamesIndonesian = []string{"Januari", "Februari", "Maret", "April", "Mei", "Juni", "Juli", "Agustus", "September", "Oktober", "November", "Desember"}
	// monthNamesIndonesianAbbr list the month name abbreviations in the Indonesian.
	monthNamesIndonesianAbbr = []string{"Jan", "Feb", "Mar", "Apr", "Mei", "Jun", "Jul", "Agu", "Sep", "Okt", "Nov", "Des"}
	// monthNamesInuktitut list the month names in the Inuktitut.
	monthNamesInuktitut = []string{"Jaannuari", "Viivvuari", "Maatsi", "Iipuri", "Mai", "Juuni", "Julai", "Aaggiisi", "Sitipiri", "Utupiri", "Nuvipiri", "Tisipiri"}
	// monthNamesInuktitutAbbr list the month name abbreviations in the Inuktitut.
	monthNamesInuktitutAbbr = []string{"Jan", "Viv", "Mas", "Ipu", "Mai", "Jun", "Jul", "Agi", "Sii", "Uut", "Nuv", "Tis"}
	// monthNamesIrish list the month names in the Irish.
	monthNamesIrish = []string{"Eanáir", "Feabhra", "Márta", "Aibreán", "Bealtaine", "Meitheamh", "Iúil", "Lúnasa", "Meán Fómhair", "Deireadh Fómhair", "Samhain", "Nollaig"}
	// monthNamesIrishAbbr lists the month abbreviations in the Irish.
	monthNamesIrishAbbr = []string{"Ean", "Feabh", "Márta", "Aib", "Beal", "Meith", "Iúil", "Lún", "MFómh", "DFómh", "Samh", "Noll"}
	// monthNamesItalian list the month names in the Italian.
	monthNamesItalian = []string{"gennaio", "febbraio", "marzo", "aprile", "maggio", "giugno", "luglio", "agosto", "settembre", "ottobre", "novembre", "dicembre"}
	// monthNamesItalianAbbr list the month name abbreviations in the Italian.
	monthNamesItalianAbbr = []string{"gen", "feb", "mar", "apr", "mag", "giu", "lug", "ago", "set", "ott", "nov", "dic"}
	// monthNamesKannada list the month names in the Kannada.
	monthNamesKannada = []string{
		"\u0C9C\u0CA8\u0CB5\u0CB0\u0CBF",
		"\u0CAB\u0CC6\u0CAC\u0CCD\u0CB0\u0CB5\u0CB0\u0CBF",
		"\u0CAE\u0CBE\u0CB0\u0CCD\u0C9A\u0CCD",
		"\u0C8F\u0C8F\u0CAA\u0CCD\u0CB0\u0CBF\u0CB2\u0CCD",
		"\u0CAE\u0CC7",
		"\u0C9C\u0CC2\u0CA8\u0CCD",
		"\u0C9C\u0CC1\u0CB2\u0CC8",
		"\u0C86\u0C97\u0CB8\u0CCD\u0C9F\u0CCD",
		"\u0CB8\u0CC6\u0CAA\u0CCD\u0C9F\u0C82\u0CAC\u0CB0\u0CCD",
		"\u0C85\u0C95\u0CCD\u0C9F\u0CCB\u0CAC\u0CB0\u0CCD",
		"\u0CA8\u0CB5\u0CC6\u0C82\u0CAC\u0CB0\u0CCD",
		"\u0CA1\u0CBF\u0CB8\u0CC6\u0C82\u0CAC\u0CB0\u0CCD",
	}
	// monthNamesKannadaAbbr lists the month abbreviations in the Kannada.
	monthNamesKannadaAbbr = []string{
		"\u0C9C\u0CA8\u0CB5\u0CB0\u0CBF",
		"\u0CAB\u0CC6\u0CAC\u0CCD\u0CB0\u0CB5\u0CB0\u0CBF",
		"\u0CAE\u0CBE\u0CB0\u0CCD\u0C9A\u0CCD",
		"\u0C8E\u0CAA\u0CCD\u0CB0\u0CBF\u0CB2\u0CCD",
		"\u0CAE\u0CC7",
		"\u0C9C\u0CC2\u0CA8\u0CCD",
		"\u0C9C\u0CC1\u0CB2\u0CC8",
		"\u0C86\u0C97\u0CB8\u0CCD\u0C9F\u0CCD",
		"\u0CB8\u0CC6\u0CAA\u0CCD\u0C9F\u0C82\u0CAC\u0CB0\u0CCD",
		"\u0C85\u0C95\u0CCD\u0C9F\u0CCB\u0CAC\u0CB0\u0CCD",
		"\u0CA8\u0CB5\u0CC6\u0C82\u0CAC\u0CB0\u0CCD",
		"\u0CA1\u0CBF\u0CB8\u0CC6\u0C82\u0CAC\u0CB0\u0CCD",
	}
	// monthNamesKashmiri list the month names in the Kashmiri.
	monthNamesKashmiri = []string{
		"\u062C\u0646\u0624\u0631\u06CC",
		"\u0641\u0631\u0624\u0631\u06CC",
		"\u0645\u0627\u0631\u0655\u0686",
		"\u0627\u067E\u0631\u06CC\u0644",
		"\u0645\u06CC\u0654",
		"\u062C\u0648\u0657\u0646",
		"\u062C\u0648\u0657\u0644\u0627\u06CC\u06CC",
		"\u0627\u06AF\u0633\u062A",
		"\u0633\u062A\u0645\u0628\u0631",
		"\u0627\u06A9\u062A\u0648\u0657\u0628\u0631",
		"\u0646\u0648\u0645\u0628\u0631",
		"\u062F\u0633\u0645\u0628\u0631",
	}
	// monthNamesKazakh list the month names in the Kazakh.
	monthNamesKazakh = []string{
		"\u049A\u0430\u04A3\u0442\u0430\u0440",
		"\u0410\u049B\u043F\u0430\u043D",
		"\u041D\u0430\u0443\u0440\u044B\u0437",
		"\u0421\u04D9\u0443\u0456\u0440",
		"\u041C\u0430\u043C\u044B\u0440",
		"\u041C\u0430\u0443\u0441\u044B\u043C",
		"\u0428\u0456\u043B\u0434\u0435",
		"\u0422\u0430\u043C\u044B\u0437",
		"\u049A\u044B\u0440\u043A\u04AF\u0439\u0435\u043A",
		"\u049A\u0430\u0437\u0430\u043D",
		"\u049A\u0430\u0440\u0430\u0448\u0430",
		"\u0416\u0435\u043B\u0442\u043E\u049B\u0441\u0430\u043D",
	}
	// monthNamesKazakhAbbr list the month name abbreviations in the Kazakh.
	monthNamesKazakhAbbr = []string{
		"\u049B\u0430\u04A3",
		"\u0430\u049B\u043F",
		"\u043D\u0430\u0443",
		"\u0441\u04D9\u0443",
		"\u043C\u0430\u043C",
		"\u043C\u0430\u0443",
		"\u0448\u0456\u043B",
		"\u0442\u0430\u043C",
		"\u049B\u044B\u0440",
		"\u049B\u0430\u0437",
		"\u049B\u0430\u0440",
		"\u0436\u0435\u043B",
	}
	// monthNamesKhmer list the month names in the Khmer.
	monthNamesKhmer = []string{
		"\u1798\u1780\u179A\u17B6",
		"\u1780\u17BB\u1798\u17D2\u1797\u17C8",
		"\u1798\u17B7\u1793\u17B6",
		"\u1798\u17C1\u179F\u17B6",
		"\u17A7\u179F\u1797\u17B6",
		"\u1798\u17B7\u1790\u17BB\u1793\u17B6",
		"\u1780\u1780\u17D2\u1780\u178A\u17B6",
		"\u179F\u17B8\u17A0\u17B6",
		"\u1780\u1789\u17D2\u1789\u17B6",
		"\u178F\u17BB\u179B\u17B6",
		"\u179C\u17B7\u1785\u17D2\u1786\u17B7\u1780\u17B6",
		"\u1792\u17D2\u1793\u17BC",
	}
	// monthNamesKhmerAbbr list the month name abbreviations in the Khmer.
	monthNamesKhmerAbbr = []string{
		"\u17E1", "\u17E2", "\u17E3", "\u17E4", "\u17E5", "\u17E6", "\u17E7", "\u17E8", "\u17E9", "\u17E1\u17E0", "\u17E1\u17E1", "\u17E1\u17E2",
		"\u1798", "\u1780", "\u1798", "\u1798", "\u17A7", "\u1798", "\u1780", "\u179F", "\u1780", "\u178F", "\u179C", "\u1792",
	}
	// monthNamesKiche list the month names in the Kiche.
	monthNamesKiche = []string{"nab'e ik'", "ukab' ik'", "urox ik'", "ukaj ik'", "uro ik'", "uwaq ik'", "uwuq ik'", "uwajxaq ik'", "ub'elej ik'", "ulaj ik'", "ujulaj ik'", "ukab'laj ik'"}
	// monthNamesKicheAbbr list the month name abbreviations in the Kiche.
	monthNamesKicheAbbr = []string{"nab'e", "ukab'", "urox", "ukaj", "uro", "uwaq", "uwuq", "uwajxaq", "ub'elej", "ulaj", "ujulaj", "ukab'laj"}
	// monthNamesKinyarwanda list the month names in the Kinyarwanda.
	monthNamesKinyarwanda = []string{"Mutarama", "Gashyantare", "Werurwe", "Mata", "Gicuransi", "Kamena", "Nyakanga", "Kanama", "Nzeli", "Ukwakira", "Ugushyingo", "Ukuboza"}
	// monthNamesKinyarwandaAbbr list the month name abbreviations in the Kinyarwanda.
	monthNamesKinyarwandaAbbr = []string{"mut.", "gas.", "wer.", "mat.", "gic.", "kam.", "Nyak", "kan.", "nze.", "Ukwak", "Ugus", "Ukub"}
	// monthNamesKiswahili list the month names in the Kiswahili.
	monthNamesKiswahili = []string{"Januari", "Februari", "Machi", "Aprili", "Mei", "Juni", "Julai", "Agosti", "Septemba", "Oktoba", "Novemba", "Desemba"}
	// monthNamesKiswahiliAbbr list the month name abbreviations in the Kiswahili.
	monthNamesKiswahiliAbbr = []string{"Jan", "Feb", "Mac", "Apr", "Mei", "Jun", "Jul", "Ago", "Sep", "Okt", "Nov", "Des"}
	// monthNamesKonkani list the month names in the Konkani.
	monthNamesKonkani = []string{
		"\u091C\u093E\u0928\u0947\u0935\u093E\u0930\u0940",
		"\u092B\u0947\u092C\u094D\u0930\u0941\u0935\u093E\u0930\u0940",
		"\u092E\u093E\u0930\u094D\u091A",
		"\u090F\u092A\u094D\u0930\u093F\u0932",
		"\u092E\u0947",
		"\u091C\u0942\u0928",
		"\u091C\u0941\u0932\u0948",
		"\u0911\u0917\u0938\u094D\u091F",
		"\u0938\u092A\u094D\u091F\u0947\u0902\u092C\u0930",
		"\u0911\u0915\u094D\u091F\u094B\u092C\u0930",
		"\u0928\u094B\u0935\u0947\u092E\u094D\u092C\u0930",
		"\u0921\u093F\u0938\u0947\u0902\u092C\u0930",
	}
	// monthNamesKonkaniAbbr list the month name abbreviations in the Konkani.
	monthNamesKonkaniAbbr = []string{
		"\u091C\u093E\u0928\u0947",
		"\u092B\u0947\u092C\u094D\u0930\u0941",
		"\u092E\u093E\u0930\u094D\u091A",
		"\u090F\u092A\u094D\u0930\u093F\u0932",
		"\u092E\u0947",
		"\u091C\u0942\u0928",
		"\u091C\u0941\u0932\u0948",
		"\u0911\u0917.",
		"\u0938\u092A\u094D\u091F\u0947\u0902.",
		"\u0911\u0915\u094D\u091F\u094B.",
		"\u0928\u094B\u0935\u0947.",
		"\u0921\u093F\u0938\u0947\u0902",
	}
	// monthNamesKoreanAbbr lists out the month number plus 월 for the Korean language.
	monthNamesKoreanAbbr = []string{"1월", "2월", "3월", "4월", "5월", "6월", "7월", "8월", "9월", "10월", "11월", "12월"}
	// monthNamesKyrgyz list the month names in the Kyrgyz.
	monthNamesKyrgyz = []string{
		"\u042F\u043D\u0432\u0430\u0440\u044C",
		"\u0424\u0435\u0432\u0440\u0430\u043B\u044C",
		"\u041C\u0430\u0440\u0442",
		"\u0410\u043F\u0440\u0435\u043B\u044C",
		"\u041C\u0430\u0439",
		"\u0418\u044E\u043D\u044C",
		"\u0418\u044E\u043B\u044C",
		"\u0410\u0432\u0433\u0443\u0441\u0442",
		"\u0421\u0435\u043D\u0442\u044F\u0431\u0440\u044C",
		"\u041E\u043A\u0442\u044F\u0431\u0440\u044C",
		"\u041D\u043E\u044F\u0431\u0440\u044C",
		"\u0414\u0435\u043A\u0430\u0431\u0440\u044C",
	}
	// monthNamesKyrgyzAbbr lists the month name abbreviations in the Kyrgyz.
	monthNamesKyrgyzAbbr = []string{
		"\u042F\u043D\u0432",
		"\u0424\u0435\u0432",
		"\u041C\u0430\u0440",
		"\u0410\u043F\u0440",
		"\u041C\u0430\u0439",
		"\u0418\u044E\u043D",
		"\u0418\u044E\u043B",
		"\u0410\u0432\u0433",
		"\u0421\u0435\u043D",
		"\u041E\u043A\u0442",
		"\u041D\u043E\u044F",
		"\u0414\u0435\u043A",
	}
	// monthNamesLao list the month names in the Lao.
	monthNamesLao = []string{
		"\u0EA1\u0EB1\u0E87\u0E81\u0EAD\u0E99",
		"\u0E81\u0EB8\u0EA1\u0E9E\u0EB2",
		"\u0EA1\u0EB5\u0E99\u0EB2",
		"\u0EC0\u0EA1\u0EAA\u0EB2",
		"\u0E9E\u0EB6\u0E94\u0EAA\u0EB0\u0E9E\u0EB2",
		"\u0EA1\u0EB4\u0E96\u0EB8\u0E99\u0EB2",
		"\u0E81\u0ECD\u0EA5\u0EB0\u0E81\u0EBB\u0E94",
		"\u0EAA\u0EB4\u0E87\u0EAB\u0EB2",
		"\u0E81\u0EB1\u0E99\u0E8D\u0EB2",
		"\u0E95\u0EB8\u0EA5\u0EB2",
		"\u0E9E\u0EB0\u0E88\u0EB4\u0E81",
		"\u0E97\u0EB1\u0E99\u0EA7\u0EB2",
	}
	// monthNamesLaoAbbr lists the month name abbreviations in the Lao.
	monthNamesLaoAbbr = []string{
		"\u0EA1.\u0E81.",
		"\u0E81.\u0E9E.",
		"\u0EA1.\u0E99.",
		"\u0EA1.\u0EAA.",
		"\u0E9E.\u0E9E.",
		"\u0EA1\u0EB4.\u0E96.",
		"\u0E81.\u0EA5.",
		"\u0EAA.\u0EAB.",
		"\u0E81.\u0E8D.",
		"\u0E95.\u0EA5.",
		"\u0E9E.\u0E88.",
		"\u0E97.\u0EA7.",
	}
	// monthNamesLatin list the month names in the Latin.
	monthNamesLatin = []string{"Ianuarius", "Februarius", "Martius", "Aprilis", "Maius", "Iunius", "Quintilis", "Sextilis", "September", "October", "November", "December"}
	// monthNamesLatinAbbr list the month name abbreviations in the Latin.
	monthNamesLatinAbbr = []string{"Ian", "Feb", "Mar", "Apr", "Mai", "Iun", "Quint", "Sext", "Sept", "Oct", "Nov", "Dec"}
	// monthNamesLatvian list the month names in the Latvian.
	monthNamesLatvian = []string{"janvāris", "februāris", "marts", "aprīlis", "maijs", "jūnijs", "jūlijs", "augusts", "septembris", "oktobris", "novembris", "decembris"}
	// monthNamesLatvianAbbr list the month name abbreviations in the Latvian.
	monthNamesLatvianAbbr = []string{"janv.", "febr.", "marts", "apr.", "maijs", "jūn.", "jūl.", "aug.", "sept.", "okt.", "nov.", "dec."}
	// monthNamesLithuanian list the month names in the Lithuanian.
	monthNamesLithuanian = []string{"sausis", "vasaris", "kovas", "balandis", "gegužė", "birželis", "liepa", "rugpjūtis", "rugsėjis", "spalis", "lapkritis", "gruodis"}
	// monthNamesLithuanianAbbr list the month name abbreviations in the Lithuanian.
	monthNamesLithuanianAbbr = []string{"saus.", "vas.", "kov.", "bal.", "geg.", "birž.", "liep.", "rugp.", "rugs.", "spal.", "lapkr.", "gruod."}
	// monthNamesLowerSorbian list the month names in the Lower Sorbian.
	monthNamesLowerSorbian = []string{"januar", "februar", "měrc", "apryl", "maj", "junij", "julij", "awgust", "september", "oktober", "nowember", "december"}
	// monthNamesLowerSorbianAbbr list the month name abbreviations in the LowerSorbian.
	monthNamesLowerSorbianAbbr = []string{"jan", "feb", "měr", "apr", "maj", "jun", "jul", "awg", "sep", "okt", "now", "dec"}
	// monthNamesLuxembourgish list the month names in the Lower Sorbian.
	monthNamesLuxembourgish = []string{"Januar", "Februar", "Mäerz", "Abrëll", "Mee", "Juni", "Juli", "August", "September", "Oktober", "November", "Dezember"}
	// monthNamesLuxembourgishAbbr list the month name abbreviations in the Luxembourgish.
	monthNamesLuxembourgishAbbr = []string{"Jan", "Feb", "Mäe", "Abr", "Mee", "Jun", "Jul", "Aug", "Sep", "Okt", "Nov", "Dez"}
	// monthNamesMacedonian list the month names in the Lower Sorbian.
	monthNamesMacedonian = []string{
		"\u0458\u0430\u043D\u0443\u0430\u0440\u0438",
		"\u0444\u0435\u0432\u0440\u0443\u0430\u0440\u0438",
		"\u043C\u0430\u0440\u0442",
		"\u0430\u043F\u0440\u0438\u043B",
		"\u043C\u0430\u0458",
		"\u0458\u0443\u043D\u0438",
		"\u0458\u0443\u043B\u0438",
		"\u0430\u0432\u0433\u0443\u0441\u0442",
		"\u0441\u0435\u043F\u0442\u0435\u043C\u0432\u0440\u0438",
		"\u043E\u043A\u0442\u043E\u043C\u0432\u0440\u0438",
		"\u043D\u043E\u0435\u043C\u0432\u0440\u0438",
		"\u0434\u0435\u043A\u0435\u043C\u0432\u0440\u0438",
	}
	// monthNamesMacedonianAbbr list the month name abbreviations in the Macedonian.
	monthNamesMacedonianAbbr = []string{
		"\u0458\u0430\u043D.",
		"\u0444\u0435\u0432.",
		"\u043C\u0430\u0440.",
		"\u0430\u043F\u0440.",
		"\u043C\u0430\u0458",
		"\u0458\u0443\u043D.",
		"\u0458\u0443\u043B.",
		"\u0430\u0432\u0433.",
		"\u0441\u0435\u043F\u0442.",
		"\u043E\u043A\u0442.",
		"\u043D\u043E\u0435\u043C.",
		"\u0434\u0435\u043A.",
	}
	// monthNamesMalay list the month names in the Malay.
	monthNamesMalay = []string{"Januari", "Februari", "Mac", "April", "Mei", "Jun", "Julai", "Ogos", "September", "Oktober", "November", "Disember"}
	// monthNamesMalayAbbr list the month name abbreviations in the Malay.
	monthNamesMalayAbbr = []string{"Jan", "Feb", "Mac", "Apr", "Mei", "Jun", "Jul", "Ogo", "Sep", "Okt", "Nov", "Dis"}
	// monthNamesMalayalam list the month names in the Malayalam.
	monthNamesMalayalam = []string{
		"\u0D1C\u0D28\u0D41\u0D35\u0D30\u0D3F",
		"\u0D2B\u0D46\u0D2C\u0D4D\u0D30\u0D41\u0D35\u0D30\u0D3F",
		"\u0D2E\u0D3E\u0D30\u0D4D\u200D\u200C\u0D1A\u0D4D\u0D1A\u0D4D",
		"\u0D0F\u0D2A\u0D4D\u0D30\u0D3F\u0D32\u0D4D\u200D",
		"\u0D2E\u0D47\u0D2F\u0D4D",
		"\u0D1C\u0D42\u0D7A",
		"\u0D1C\u0D42\u0D32\u0D48",
		"\u0D06\u0D17\u0D38\u0D4D\u0D31\u0D4D\u0D31\u0D4D",
		"\u0D38\u0D46\u0D2A\u0D4D\u200C\u0D31\u0D4D\u0D31\u0D02\u0D2C\u0D30\u0D4D\u200D",
		"\u0D12\u0D15\u0D4D\u200C\u0D1F\u0D4B\u0D2C\u0D30\u0D4D\u200D",
		"\u0D28\u0D35\u0D02\u0D2C\u0D30\u0D4D\u200D",
		"\u0D21\u0D3F\u0D38\u0D02\u0D2C\u0D30\u0D4D\u200D",
	}
	// monthNamesMalayalamAbbr list the month name abbreviations in the Malayalam.
	monthNamesMalayalamAbbr = []string{
		"\u0D1C\u0D28\u0D41",
		"\u0D2B\u0D46\u0D2C\u0D4D\u0D30\u0D41",
		"\u0D2E\u0D3E\u0D7C",
		"\u0D0F\u0D2A\u0D4D\u0D30\u0D3F",
		"\u0D2E\u0D47\u0D2F\u0D4D",
		"\u0D1C\u0D42\u0D7A",
		"\u0D1C\u0D42\u0D32\u0D48",
		"\u0D13\u0D17",
		"\u0D38\u0D46\u0D2A\u0D4D\u0D31\u0D4D\u0D31\u0D02",
		"\u0D12\u0D15\u0D4D\u0D1F\u0D4B",
		"\u0D28\u0D35\u0D02",
		"\u0D21\u0D3F\u0D38\u0D02",
	}
	// monthNamesMaltese list the month names in the Maltese.
	monthNamesMaltese = []string{"Jannar", "Frar", "Marzu", "April", "Mejju", "Ġunju", "Lulju", "Awwissu", "Settembru", "Ottubru", "Novembru", "Diċembru"}
	// monthNamesMalteseAbbr list the month name abbreviations in the Maltese.
	monthNamesMalteseAbbr = []string{"Jan", "Fra", "Mar", "Apr", "Mej", "Ġun", "Lul", "Aww", "Set", "Ott", "Nov", "Diċ"}
	// monthNamesMaori list the month names in the Maori.
	monthNamesMaori = []string{"Kohitātea", "Huitanguru", "Poutūterangi", "Paengawhāwhā", "Haratua", "Pipiri", "Hōngongoi", "Hereturikōkā", "Mahuru", "Whiringa ā-nuku", "Whiringa ā-rangi", "Hakihea"}
	// monthNamesMaoriAbbr list the month name abbreviations in the Maori.
	monthNamesMaoriAbbr = []string{"Kohi", "Hui", "Pou", "Pae", "Hara", "Pipi", "Hōngo", "Here", "Mahu", "Nuku", "Rangi", "Haki"}
	// monthNamesMapudungun list the month name abbreviations in the Mapudungun.
	monthNamesMapudungun = []string{"Kiñe Tripantu", "Epu", "Kila", "Meli", "Kechu", "Cayu", "Regle", "Purha", "Aiya", "Marhi", "Marhi Kiñe", "Marhi Epu"}
	// monthNamesMarathi list the month names in the Marathi.
	monthNamesMarathi = []string{
		"\u091C\u093E\u0928\u0947\u0935\u093E\u0930\u0940",
		"\u092B\u0947\u092C\u094D\u0930\u0941\u0935\u093E\u0930\u0940",
		"\u092E\u093E\u0930\u094D\u091A",
		"\u090F\u092A\u094D\u0930\u093F\u0932",
		"\u092E\u0947",
		"\u091C\u0942\u0928",
		"\u091C\u0941\u0932\u0948",
		"\u0911\u0917\u0938\u094D\u091F",
		"\u0938\u092A\u094D\u091F\u0947\u0902\u092C\u0930",
		"\u0911\u0915\u094D\u091F\u094B\u092C\u0930",
		"\u0928\u094B\u0935\u094D\u0939\u0947\u0902\u092C\u0930",
		"\u0921\u093F\u0938\u0947\u0902\u092C\u0930",
	}
	// monthNamesMarathiAbbr lists the month name abbreviations in Marathi.
	monthNamesMarathiAbbr = []string{
		"\u091C\u093E\u0928\u0947.",
		"\u092B\u0947\u092C\u094D\u0930\u0941.",
		"\u092E\u093E\u0930\u094D\u091A",
		"\u090F\u092A\u094D\u0930\u093F",
		"\u092E\u0947",
		"\u091C\u0942\u0928",
		"\u091C\u0941\u0932\u0948",
		"\u0911\u0917.",
		"\u0938\u092A\u094D\u091F\u0947\u0902.",
		"\u0911\u0915\u094D\u091F\u094B.",
		"\u0928\u094B\u0935\u094D\u0939\u0947\u0902.",
		"\u0921\u093F\u0938\u0947\u0902.",
	}
	// monthNamesMohawk list the month names in the Mohawk.
	monthNamesMohawk = []string{"Tsothohrkó:Wa", "Enniska", "Enniskó:Wa", "Onerahtókha", "Onerahtohkó:Wa", "Ohiari:Ha", "Ohiarihkó:Wa", "Seskéha", "Seskehkó:Wa", "Kenténha", "Kentenhkó:Wa", "Tsothóhrha"}
	// monthNamesMongolian list the month names in the Mongolian.
	monthNamesMongolian = []string{
		"\u041D\u044D\u0433\u0434\u04AF\u0433\u044D\u044D\u0440 \u0441\u0430\u0440",
		"\u0425\u043E\u0451\u0440\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440",
		"\u0413\u0443\u0440\u0430\u0432\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440",
		"\u0414\u04E9\u0440\u04E9\u0432\u0434\u04AF\u0433\u044D\u044D\u0440 \u0441\u0430\u0440",
		"\u0422\u0430\u0432\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440",
		"\u0417\u0443\u0440\u0433\u0430\u0430\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440",
		"\u0414\u043E\u043B\u043E\u043E\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440",
		"\u041D\u0430\u0439\u043C\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440",
		"\u0415\u0441\u0434\u04AF\u0433\u044D\u044D\u0440 \u0441\u0430\u0440",
		"\u0410\u0440\u0430\u0432\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440",
		"\u0410\u0440\u0432\u0430\u043D \u043D\u044D\u0433\u0434\u04AF\u0433\u044D\u044D\u0440 \u0441\u0430\u0440",
		"\u0410\u0440\u0432\u0430\u043D \u0445\u043E\u0451\u0440\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440",
	}
	// monthNamesMongolianAbbr lists the month name abbreviations in Mongolian.
	monthNamesMongolianAbbr = []string{"1-р сар", "2-р сар", "3-р сар", "4-р сар", "5-р сар", "6-р сар", "7-р сар", "8-р сар", "9-р сар", "10-р сар", "11-р сар", "12-р сар"}
	// monthNamesMoroccoAbbr lists the month name abbreviations in the Morocco.
	monthNamesMoroccoAbbr = []string{"jan.", "fév.", "mar.", "avr.", "mai", "jui.", "juil.", "août", "sept.", "oct.", "nov.", "déc."}
	// monthNamesNepali list the month names in the Nepali.
	monthNamesNepali = []string{
		"\u091C\u0928\u0935\u0930\u0940",
		"\u092B\u0947\u092C\u094D\u0930\u0941\u0905\u0930\u0940",
		"\u092E\u093E\u0930\u094D\u091A",
		"\u0905\u092A\u094D\u0930\u093F\u0932",
		"\u092E\u0947",
		"\u091C\u0942\u0928",
		"\u091C\u0941\u0932\u093E\u0908",
		"\u0905\u0917\u0938\u094D\u0924",
		"\u0938\u0947\u092A\u094D\u091F\u0947\u092E\u094D\u092C\u0930",
		"\u0905\u0915\u094D\u091F\u094B\u092C\u0930",
		"\u0928\u094B\u092D\u0947\u092E\u094D\u092C\u0930",
		"\u0921\u093F\u0938\u0947\u092E\u094D\u092C\u0930",
	}
	// monthNamesNepaliAbbr lists the month name abbreviations in the Nepali.
	monthNamesNepaliAbbr = []string{
		"\u091C\u0928",
		"\u092B\u0947\u092C",
		"\u092E\u093E\u0930\u094D\u091A",
		"\u0905\u092A\u094D\u0930\u093F\u0932",
		"\u092E\u0947",
		"\u091C\u0942\u0928",
		"\u091C\u0941\u0932\u093E\u0908",
		"\u0905\u0917",
		"\u0938\u0947\u092A\u094D\u091F",
		"\u0905\u0915\u094D\u091F",
		"\u0928\u094B\u092D",
		"\u0921\u093F\u0938",
	}
	// monthNamesNepaliIN list the month names in the India Nepali.
	monthNamesNepaliIN = []string{
		"\u091C\u0928\u0935\u0930\u0940",
		"\u092B\u0930\u0935\u0930\u0940",
		"\u092E\u093E\u0930\u094D\u091A",
		"\u0905\u092A\u094D\u0930\u0947\u0932",
		"\u092E\u0908",
		"\u091C\u0941\u0928",
		"\u091C\u0941\u0932\u093E\u0908",
		"\u0905\u0917\u0938\u094D\u091F",
		"\u0938\u0947\u092A\u094D\u091F\u0947\u092E\u094D\u092C\u0930",
		"\u0905\u0915\u094D\u091F\u094B\u092C\u0930",
		"\u0928\u094B\u092D\u0947\u092E\u094D\u092C\u0930",
		"\u0926\u093F\u0938\u092E\u094D\u092C\u0930",
	}
	// monthNamesNepaliINAbbr lists the month name abbreviations in the India Nepali.
	monthNamesNepaliINAbbr = []string{
		"\u091C\u0928\u0935\u0930\u0940",
		"\u092B\u0947\u092C\u094D\u0930\u0941\u0905\u0930\u0940",
		"\u092E\u093E\u0930\u094D\u091A",
		"\u0905\u092A\u094D\u0930\u093F",
		"\u092E\u0947",
		"\u091C\u0941\u0928",
		"\u091C\u0941\u0932\u093E",
		"\u0905\u0917\u0938\u094D\u091F",
		"\u0938\u0947\u092A\u094D\u091F\u0947\u092E\u094D\u092C\u0930",
		"\u0905\u0915\u094D\u091F\u094B",
		"\u0928\u094B\u092D\u0947",
		"\u0921\u093F\u0938\u0947",
	}
	// monthNamesNigeria list the month names in the Nigeria.
	monthNamesNigeria = []string{"samwiee", "feeburyee", "marsa", "awril", "me", "suyeŋ", "sulyee", "ut", "satambara", "oktoobar", "nowamburu", "deesamburu"}
	// monthNamesNigeriaAbbr lists the month name abbreviations in the Nigeria.
	monthNamesNigeriaAbbr = []string{"samw", "feeb", "mar", "awr", "me", "suy", "sul", "ut", "sat", "okt", "now", "dees"}
	// monthNamesNorwegian list the month names in the Norwegian.
	monthNamesNorwegian = []string{"januar", "februar", "mars", "april", "mai", "juni", "juli", "august", "september", "oktober", "november", "desember"}
	// monthNamesOccitan list the month names in the Occitan.
	monthNamesOccitan = []string{"genièr", "febrièr", "març", "abril", "mai", "junh", "julhet", "agost", "setembre", "octobre", "novembre", "decembre"}
	// monthNamesOccitanAbbr lists the month name abbreviations in the Occitan.
	monthNamesOccitanAbbr = []string{"gen.", "feb.", "març", "abr.", "mai", "junh", "julh", "ag.", "set.", "oct.", "nov.", "dec."}
	// monthNamesOdia list the month names in the Odia.
	monthNamesOdia = []string{
		"\u0B1C\u0B3E\u0B28\u0B41\u0B5F\u0B3E\u0B30\u0B40",
		"\u0B2B\u0B47\u0B2C\u0B43\u0B06\u0B30\u0B40",
		"\u0B2E\u0B3E\u0B30\u0B4D\u0B1A\u0B4D\u0B1A",
		"\u0B0F\u0B2A\u0B4D\u0B30\u0B3F\u0B32\u0B4D\u200C",
		"\u0B2E\u0B47",
		"\u0B1C\u0B41\u0B28\u0B4D\u200C",
		"\u0B1C\u0B41\u0B32\u0B3E\u0B07",
		"\u0B05\u0B17\u0B37\u0B4D\u0B1F",
		"\u0B38\u0B47\u0B2A\u0B4D\u0B1F\u0B47\u0B2E\u0B4D\u0B2C\u0B30",
		"\u0B05\u0B15\u0B4D\u0B1F\u0B4B\u0B2C\u0B30",
		"\u0B28\u0B2D\u0B47\u0B2E\u0B4D\u0B2C\u0B30",
		"\u0B21\u0B3F\u0B38\u0B47\u0B2E\u0B4D\u0B2C\u0B30",
	}
	// monthNamesOromo list the month names in the Oromo.
	monthNamesOromo = []string{"Amajjii", "Guraandhala", "Bitooteessa", "Elba", "Caamsa", "Waxabajjii", "Adooleessa", "Hagayya", "Fuulbana", "Onkololeessa", "Sadaasa", "Muddee"}
	// monthNamesOromoAbbr list the month abbreviations in the Oromo.
	monthNamesOromoAbbr = []string{"Ama", "Gur", "Bit", "Elb", "Cam", "Wax", "Ado", "Hag", "Ful", "Onk", "Sad", "Mud"}
	// monthNamesPashto list the month names in the Pashto.
	monthNamesPashto = []string{
		"\u0633\u0644\u0648\u0627\u063A\u0647",
		"\u0643\u0628",
		"\u0648\u0631\u0649",
		"\u063A\u0648\u064A\u0649",
		"\u063A\u0628\u0631\u06AB\u0648\u0644\u0649",
		"\u0686\u0646\u06AB\u0627 \u069A\u0632\u0645\u0631\u0649",
		"\u0632\u0645\u0631\u0649",
		"\u0648\u0696\u0649",
		"\u062A\u0644\u0647",
		"\u0644\u0693\u0645",
		"\u0644\u0646\u0688 \u06CD",
		"\u0645\u0631\u063A\u0648\u0645\u0649",
	}
	// monthNamesPersian list the month names in the Persian.
	monthNamesPersian = []string{
		"\u0698\u0627\u0646\u0648\u064A\u0647",
		"\u0641\u0648\u0631\u064A\u0647",
		"\u0645\u0627\u0631\u0633",
		"\u0622\u0648\u0631\u064A\u0644",
		"\u0645\u0647",
		"\u0698\u0648\u0626\u0646",
		"\u0698\u0648\u0626\u064A\u0647",
		"\u0627\u0648\u062A",
		"\u0633\u067E\u062A\u0627\u0645\u0628\u0631",
		"\u0627\u064F\u0643\u062A\u0628\u0631",
		"\u0646\u0648\u0627\u0645\u0628\u0631",
		"\u062F\u0633\u0627\u0645\u0628\u0631",
	}
	// monthNamesPolish list the month names in the Polish.
	monthNamesPolish = []string{"styczeń", "luty", "marzec", "kwiecień", "maj", "czerwiec", "lipiec", "sierpień", "wrzesień", "październik", "listopad", "grudzień"}
	// monthNamesPortuguese list the month names in the Portuguese.
	monthNamesPortuguese = []string{"janeiro", "fevereiro", "março", "abril", "maio", "junho", "julho", "agosto", "setembro", "outubro", "novembro", "dezembro"}
	// monthNamesPunjabi list the month names in the Punjabi.
	monthNamesPunjabi = []string{
		"\u0A1C\u0A28\u0A35\u0A30\u0A40",
		"\u0A2B\u0A3C\u0A30\u0A35\u0A30\u0A40",
		"\u0A2E\u0A3E\u0A30\u0A1A",
		"\u0A05\u0A2A\u0A4D\u0A30\u0A48\u0A32",
		"\u0A2E\u0A08",
		"\u0A1C\u0A42\u0A28",
		"\u0A1C\u0A41\u0A32\u0A3E\u0A08",
		"\u0A05\u0A17\u0A38\u0A24",
		"\u0A38\u0A24\u0A70\u0A2C\u0A30",
		"\u0A05\u0A15\u0A24\u0A42\u0A2C\u0A30",
		"\u0A28\u0A35\u0A70\u0A2C\u0A30",
		"\u0A26\u0A38\u0A70\u0A2C\u0A30",
	}
	// monthNamesPunjabiArab list the month names in the Punjabi Arab.
	monthNamesPunjabiArab = []string{
		"\u062C\u0646\u0648\u0631\u06CC",
		"\u0641\u0631\u0648\u0631\u06CC",
		"\u0645\u0627\u0631\u0686",
		"\u0627\u067E\u0631\u06CC\u0644",
		"\u0645\u0626\u06CC",
		"\u062C\u0648\u0646",
		"\u062C\u0648\u0644\u0627\u0626\u06CC",
		"\u0627\u06AF\u0633\u062A",
		"\u0633\u062A\u0645\u0628\u0631",
		"\u0627\u06A9\u062A\u0648\u0628\u0631",
		"\u0646\u0648\u0645\u0628\u0631",
		"\u062F\u0633\u0645\u0628\u0631",
	}
	// monthNamesQuechua list the month names in the Quechua.
	monthNamesQuechua = []string{"Qulla puquy", "Hatun puquy", "Pauqar waray", "ayriwa", "Aymuray", "Inti raymi", "Anta Sitwa", "Qhapaq Sitwa", "Uma raymi", "Kantaray", "Ayamarq'a", "Kapaq Raymi"}
	// monthNamesQuechuaEcuador list the month names in the Quechua Ecuador.
	monthNamesQuechuaEcuador = []string{"kulla", "panchi", "pawkar", "ayriwa", "aymuray", "raymi", "sitwa", "karwa", "kuski", "wayru", "sasi", "kapak"}
	// monthNamesRomanian list the month names in the Romanian.
	monthNamesRomanian = []string{"ianuarie", "februarie", "martie", "aprilie", "mai", "iunie", "iulie", "august", "septembrie", "octombrie", "noiembrie", "decembrie"}
	// monthNamesRomanianAbbr list the month abbreviations in the Romanian.
	monthNamesRomanianAbbr = []string{"ian.", "feb.", "mar.", "apr.", "mai", "iun.", "iul.", "aug.", "sept.", "oct.", "nov.", "dec."}
	// monthNamesRomansh list the month names in the Romansh.
	monthNamesRomansh = []string{"schaner", "favrer", "mars", "avrigl", "matg", "zercladur", "fanadur", "avust", "settember", "october", "november", "december"}
	// monthNamesRomanshAbbr list the month abbreviations in the Romansh.
	monthNamesRomanshAbbr = []string{"schan.", "favr.", "mars", "avr.", "matg", "zercl.", "fan.", "avust", "sett.", "oct.", "nov.", "dec."}
	// monthNamesRussian list the month names in the Russian.
	monthNamesRussian = []string{
		"\u044F\u043D\u0432\u0430\u0440\u044C",
		"\u0444\u0435\u0432\u0440\u0430\u043B\u044C",
		"\u043C\u0430\u0440\u0442",
		"\u0430\u043F\u0440\u0435\u043B\u044C",
		"\u043C\u0430\u0439",
		"\u0438\u044E\u043D\u044C",
		"\u0438\u044E\u043B\u044C",
		"\u0430\u0432\u0433\u0443\u0441\u0442",
		"\u0441\u0435\u043D\u0442\u044F\u0431\u0440\u044C",
		"\u043E\u043A\u0442\u044F\u0431\u0440\u044C",
		"\u043D\u043E\u044F\u0431\u0440\u044C",
		"\u0434\u0435\u043A\u0430\u0431\u0440\u044C",
	}
	// monthNamesRussianAbbr list the month abbreviations in the Russian.
	monthNamesRussianAbbr = []string{
		"\u044F\u043D\u0432.",
		"\u0444\u0435\u0432.",
		"\u043C\u0430\u0440\u0442",
		"\u0430\u043F\u0440.",
		"\u043C\u0430\u0439",
		"\u0438\u044E\u043D\u044C",
		"\u0438\u044E\u043B\u044C",
		"\u0430\u0432\u0433.",
		"\u0441\u0435\u043D.",
		"\u043E\u043A\u0442.",
		"\u043D\u043E\u044F.",
		"\u0434\u0435\u043A.",
	}
	// monthNamesSakha list the month names in the Sakha.
	monthNamesSakha = []string{
		"\u0422\u043E\u0445\u0441\u0443\u043D\u043D\u044C\u0443",
		"\u041E\u043B\u0443\u043D\u043D\u044C\u0443",
		"\u041A\u0443\u043B\u0443\u043D \u0442\u0443\u0442\u0430\u0440",
		"\u041C\u0443\u0443\u0441 \u0443\u0441\u0442\u0430\u0440",
		"\u042B\u0430\u043C \u044B\u0439\u0430",
		"\u0411\u044D\u0441 \u044B\u0439\u0430",
		"\u041E\u0442 \u044B\u0439\u0430",
		"\u0410\u0442\u044B\u0440\u0434\u044C\u0430\u0445 \u044B\u0439\u0430",
		"\u0411\u0430\u043B\u0430\u0495\u0430\u043D \u044B\u0439\u0430",
		"\u0410\u043B\u0442\u044B\u043D\u043D\u044C\u044B",
		"\u0421\u044D\u0442\u0438\u043D\u043D\u044C\u0438",
		"\u0410\u0445\u0441\u044B\u043D\u043D\u044C\u044B",
	}
	// monthNamesSakhaAbbr list the month abbreviations in the Sakha.
	monthNamesSakhaAbbr = []string{
		"\u0422\u0445\u0441",
		"\u041E\u043B\u043D",
		"\u041A\u043B\u043D",
		"\u041C\u0441\u0443",
		"\u042B\u0430\u043C",
		"\u0411\u044D\u0441",
		"\u041E\u0442\u044B",
		"\u0410\u0442\u0440",
		"\u0411\u043B\u0495",
		"\u0410\u043B\u0442",
		"\u0421\u044D\u0442",
		"\u0410\u0445\u0441",
	}
	// monthNamesSami list the month names in the Sami.
	monthNamesSami = []string{"uđđâivemáánu", "kuovâmáánu", "njuhčâmáánu", "cuáŋuimáánu", "vyesimáánu", "kesimáánu", "syeinimáánu", "porgemáánu", "čohčâmáánu", "roovvâdmáánu", "skammâmáánu", "juovlâmáánu"}
	// monthNamesSamiAbbr list the month abbreviations in the Sami.
	monthNamesSamiAbbr = []string{"uđiv", "kuov", "njuh", "cuáŋ", "vyes", "kesi", "syei", "porg", "čohč", "roov", "skam", "juov"}
	// monthNamesSamiLule list the month names in the Sami (Lule).
	monthNamesSamiLule = []string{"ådåjakmánno", "guovvamánno", "sjnjuktjamánno", "vuoratjismánno", "moarmesmánno", "biehtsemánno", "sjnjilltjamánno", "bårggemánno", "ragátmánno", "gålgådismánno", "basádismánno", "javllamánno"}
	// monthNamesSamiLuleAbbr list the month abbreviations in the Sami (Lule).
	monthNamesSamiLuleAbbr = []string{"ådåj", "guov", "snju", "vuor", "moar", "bieh", "snji", "bårg", "ragá", "gålg", "basá", "javl"}
	// monthNamesSamiNorthern list the month names in the Sami (Northern).
	monthNamesSamiNorthern = []string{"ođđajagemánnu", "guovvamánnu", "njukčamánnu", "cuoŋománnu", "miessemánnu", "geassemánnu", "suoidnemánnu", "borgemánnu", "čakčamánnu", "golggotmánnu", "skábmamánnu", "juovlamánnu"}
	// monthNamesSamiNorthernAbbr list the month abbreviations in the Sami (Northern).
	monthNamesSamiNorthernAbbr = []string{"ođđj", "guov", "njuk", "cuoŋ", "mies", "geas", "suoi", "borg", "čakč", "golg", "skáb", "juov"}
	// monthNamesSamiSkolt list the month names in the Sami (Skolt).
	monthNamesSamiSkolt = []string{"ođđee´jjmään", "tä´lvvmään", "pâ´zzlâšttam-mään", "njuhččmään", "vue´ssmään", "ǩie´ssmään", "suei´nnmään", "på´rǧǧmään", "čõhččmään", "kålggmään", "skamm-mään", "rosttovmään"}
	// monthNamesSamiSouthern list the month names in the Sami (Southern).
	monthNamesSamiSouthern = []string{"tsïengele", "goevte", "njoktje", "voerhtje", "suehpede", "ruffie", "snjaltje", "mïetske", "skïerede", "golke", "rahka", "goeve"}
	// monthNamesSamiSouthernAbbr list the month abbreviations in the Sami (Southern).
	monthNamesSamiSouthernAbbr = []string{"tsïen", "goevt", "njok", "voer", "sueh", "ruff", "snja", "mïet", "skïer", "golk", "rahk", "goev"}
	// monthNamesSanskrit list the month names in the Sanskrit.
	monthNamesSanskrit = []string{
		"\u091C\u093E\u0928\u094D\u092F\u0941\u0905\u0930\u0940",
		"\u092B\u0947\u092C\u094D\u0930\u0941\u0905\u0930\u0940",
		"\u092E\u093E\u0930\u094D\u091A",
		"\u090F\u092A\u094D\u0930\u093F\u0932",
		"\u092E\u0947",
		"\u091C\u0942\u0928",
		"\u091C\u0941\u0932\u0948",
		"\u0911\u0917\u0938\u094D\u091F",
		"\u0938\u092A\u094D\u091F\u0947\u0902\u092C\u0930",
		"\u0911\u0915\u094D\u091F\u094B\u092C\u0930",
		"\u0928\u094B\u0935\u094D\u0939\u0947\u0902\u092C\u0930",
		"\u0921\u093F\u0938\u0947\u0902\u092C\u0930",
	}
	// monthNamesScottishGaelic list the month names in the Scottish Gaelic.
	monthNamesScottishGaelic = []string{"Am Faoilleach", "An Gearran", "Am Màrt", "An Giblean", "An Cèitean", "An t-Ògmhios", "An t-Iuchar", "An Lùnastal", "An t-Sultain", "An Dàmhair", "An t-Samhain", "An Dùbhlachd"}
	// monthNamesScottishGaelicAbbr list the month abbreviations in the ScottishGaelic.
	monthNamesScottishGaelicAbbr = []string{"Faoi", "Gear", "Màrt", "Gibl", "Cèit", "Ògmh", "Iuch", "Lùna", "Sult", "Dàmh", "Samh", "Dùbh"}
	// monthNamesSerbian list the month names in the Serbian (Cyrillic).
	monthNamesSerbian = []string{
		"\u0458\u0430\u043D\u0443\u0430\u0440",
		"\u0444\u0435\u0431\u0440\u0443\u0430\u0440",
		"\u043C\u0430\u0440\u0442",
		"\u0430\u043F\u0440\u0438\u043B",
		"\u043C\u0430\u0458",
		"\u0458\u0443\u043D",
		"\u0458\u0443\u043B",
		"\u0430\u0432\u0433\u0443\u0441\u0442",
		"\u0441\u0435\u043F\u0442\u0435\u043C\u0431\u0430\u0440",
		"\u043E\u043A\u0442\u043E\u0431\u0430\u0440",
		"\u043D\u043E\u0432\u0435\u043C\u0431\u0430\u0440",
		"\u0434\u0435\u0446\u0435\u043C\u0431\u0430\u0440",
	}
	// monthNamesSerbianAbbr lists the month name abbreviations in the Serbian
	// (Cyrillic).
	monthNamesSerbianAbbr = []string{
		"\u0458\u0430\u043D.",
		"\u0444\u0435\u0431.",
		"\u043C\u0430\u0440\u0442",
		"\u0430\u043F\u0440.",
		"\u043C\u0430\u0458",
		"\u0458\u0443\u043D",
		"\u0458\u0443\u043B",
		"\u0430\u0432\u0433.",
		"\u0441\u0435\u043F\u0442.",
		"\u043E\u043A\u0442.",
		"\u043D\u043E\u0432.",
		"\u0434\u0435\u0446.",
	}
	// monthNamesSerbianBA list the month names in the Serbian (Cyrillic) Bosnia
	// and Herzegovina.
	monthNamesSerbianBA = []string{
		"\u0458\u0430\u043D\u0443\u0430\u0440",
		"\u0444\u0435\u0431\u0440\u0443\u0430\u0440",
		"\u043C\u0430\u0440\u0442",
		"\u0430\u043F\u0440\u0438\u043B",
		"\u043C\u0430\u0458",
		"\u0458\u0443\u043D\u0438",
		"\u0458\u0443\u043B\u0438",
		"\u0430\u0432\u0433\u0443\u0441\u0442",
		"\u0441\u0435\u043F\u0442\u0435\u043C\u0431\u0430\u0440",
		"\u043E\u043A\u0442\u043E\u0431\u0430\u0440",
		"\u043D\u043E\u0432\u0435\u043C\u0431\u0430\u0440",
		"\u0434\u0435\u0446\u0435\u043C\u0431\u0430\u0440",
	}
	// monthNamesSerbianBAAbbr lists the month name abbreviations in the Serbian
	// (Cyrillic) Bosnia and Herzegovina.
	monthNamesSerbianBAAbbr = []string{
		"\u0458\u0430\u043D",
		"\u0444\u0435\u0431",
		"\u043C\u0430\u0440",
		"\u0430\u043F\u0440",
		"\u043C\u0430\u0458",
		"\u0458\u0443\u043D",
		"\u0458\u0443\u043B",
		"\u0430\u0432\u0433",
		"\u0441\u0435\u043F",
		"\u043E\u043A\u0442",
		"\u043D\u043E\u0432",
		"\u0434\u0435\u0446",
	}
	// monthNamesSerbianLatin list the month names in the Serbian (Latin).
	monthNamesSerbianLatin = []string{"januar", "februar", "mart", "april", "maj", "jun", "jul", "avgust", "septembar", "oktobar", "novembar", "decembar"}
	// monthNamesSerbianLatinAbbr lists the month name abbreviations in the
	// Serbian(Latin) and Montenegro (Former).
	monthNamesSerbianLatinAbbr = []string{"jan.", "feb.", "mart", "apr.", "maj", "jun", "jul", "avg.", "sept.", "okt.", "nov.", "dec."}
	// monthNamesSesothoSaLeboa list the month names in the Sesotho sa Leboa.
	monthNamesSesothoSaLeboa = []string{"Janaware", "Feberware", "Matšhe", "Aprele", "Mei", "June", "Julae", "Agostose", "Setemere", "Oktoboro", "Nofemere", "Disemere"}
	// monthNamesSesothoSaLeboaAbbr lists the month name abbreviations in the
	// Sesotho sa Leboa.
	monthNamesSesothoSaLeboaAbbr = []string{"Jan", "Feb", "Matš", "Apr", "Mei", "June", "Julae", "Agost", "Set", "Oky", "Nof", "Dis"}
	// monthNamesSetswana list the month names in the Setswana.
	monthNamesSetswana = []string{"Ferikgong", "Tlhakole", "Mopitlwe", "Moranang", "Motsheganang", "Seetebosigo", "Phukwi", "Phatwe", "Lwetse", "Diphalane", "Ngwanatsele", "Sedimonthole"}
	// monthNamesSetswanaAbbr lists the month name abbreviations in the Setswana.
	monthNamesSetswanaAbbr = []string{"Fer.", "Tlh.", "Mop.", "Mor.", "Motsh.", "Seet.", "Phk.", "Pht.", "Lwetse.", "Diph.", "Ngwn.", "Sed."}
	// monthNamesSindhi list the month names in the Sindhi.
	monthNamesSindhi = []string{
		"\u062C\u0646\u0648\u0631\u064A",
		"\u0641\u0631\u0648\u0631\u064A",
		"\u0645\u0627\u0631\u0686",
		"\u0627\u067E\u0631\u064A\u0644",
		"\u0645\u0654\u064A",
		"\u062C\u0648\u0646",
		"\u062C\u0648\u0644\u0627\u0621\u0650",
		"\u0622\u06AF\u0633\u062A",
		"\u0633\u062A\u0645\u0628\u0631",
		"\u0622\u06A9\u062A\u0648\u0628\u0631",
		"\u0646\u0648\u0645\u0628\u0631",
		"\u068A\u0633\u0645\u0628\u0631",
	}
	// monthNamesSinhala list the month names in the Sinhala.
	monthNamesSinhala = []string{
		"\u0DA2\u0DB1\u0DC0\u0DCF\u0DBB\u0DD2",
		"\u0DB4\u0DD9\u0DB6\u0DBB\u0DC0\u0DCF\u0DBB\u0DD2",
		"\u0DB8\u0DCF\u0DBB\u0DCA\u0DAD\u0DD4",
		"\u0D85\u0DB4\u0DCA\u200D\u0DBB\u0DDA\u0DBD\u0DCA",
		"\u0DB8\u0DD0\u0DBA\u0DD2",
		"\u0DA2\u0DD6\u0DB1\u0DD2",
		"\u0DA2\u0DD6\u0DBD\u0DD2",
		"\u0D85\u0D9C\u0DDD\u0DC3\u0DCA\u0DAD\u0DD4",
		"\u0DC3\u0DD0\u0DB4\u0DCA\u0DAD\u0DD0\u0DB8\u0DCA\u0DB6\u0DBB\u0DCA",
		"\u0D94\u0D9A\u0DCA\u0DAD\u0DDD\u0DB6\u0DBB\u0DCA",
		"\u0DB1\u0DDC\u0DC0\u0DD0\u0DB8\u0DCA\u0DB6\u0DBB\u0DCA",
		"\u0DAF\u0DD9\u0DC3\u0DD0\u0DB8\u0DCA\u0DB6\u0DBB\u0DCA",
	}
	// monthNamesSinhalaAbbr lists the month name abbreviations in Sinhala.
	monthNamesSinhalaAbbr = []string{
		"\u0DA2\u0DB1.",
		"\u0DB4\u0DD9\u0DB6.",
		"\u0DB8\u0DCF\u0DBB\u0DCA\u0DAD\u0DD4.",
		"\u0D85\u0DB4\u0DCA\u200D\u0DBB\u0DDA\u0DBD\u0DCA.",
		"\u0DB8\u0DD0\u0DBA\u0DD2",
		"\u0DA2\u0DD6\u0DB1\u0DD2",
		"\u0DA2\u0DD6\u0DBD\u0DD2",
		"\u0D85\u0D9C\u0DDD.",
		"\u0DC3\u0DD0\u0DB4\u0DCA.",
		"\u0D94\u0D9A\u0DCA.",
		"\u0DB1\u0DDC\u0DC0\u0DD0.",
		"\u0DAF\u0DD9\u0DC3\u0DD0.",
	}
	// monthNamesSlovak list the month names in the Slovak.
	monthNamesSlovak = []string{"január", "február", "marec", "apríl", "máj", "jún", "júl", "august", "september", "október", "november", "december"}
	// monthNamesSlovenian list the month names in the Slovenian.
	monthNamesSlovenian = []string{"januar", "februar", "marec", "april", "maj", "junij", "julij", "avgust", "september", "oktober", "november", "december"}
	// monthNamesSlovenianAbbr list the month abbreviations in the Slovenian.
	monthNamesSlovenianAbbr = []string{"jan.", "feb.", "mar.", "apr.", "maj", "jun.", "jul.", "avg.", "sep.", "okt.", "nov.", "dec."}
	// monthNamesSomali list the month names in the Somali.
	monthNamesSomali = []string{"Jannaayo", "Febraayo", "Maarso", "Abriil", "May", "Juun", "Luuliyo", "Ogost", "Sebtembar", "Oktoobar", "Nofembar", "Desembar"}
	// monthNamesSomaliAbbr list the month abbreviations in the Somali.
	monthNamesSomaliAbbr = []string{"Jan", "Feb", "Mar", "Abr", "May", "Jun", "Lul", "Ogs", "Seb", "Okt", "Nof", "Dis"}
	// monthNamesSotho list the month names in the Sotho.
	monthNamesSotho = []string{"Phesekgong", "Hlakola", "Hlakubele", "Mmese", "Motsheanong", "Phupjane", "Phupu", "Phata", "Leotshe", "Mphalane", "Pundungwane", "Tshitwe"}
	// monthNamesSothoAbbr list the month abbreviations in the Sotho.
	monthNamesSothoAbbr = []string{"Phe", "Kol", "Ube", "Mme", "Mot", "Jan", "Upu", "Pha", "Leo", "Mph", "Pun", "Tsh"}
	// monthNamesSpanish list the month names in the Spanish.
	monthNamesSpanish = []string{"enero", "febrero", "marzo", "abril", "mayo", "junio", "julio", "agosto", "septiembre", "octubre", "noviembre", "diciembre"}
	// monthNamesSpanishAbbr list the month abbreviations in the Spanish.
	monthNamesSpanishAbbr = []string{"ene", "feb", "mar", "abr", "may", "jun", "jul", "ago", "sep", "oct", "nov", "dic"}
	// monthNamesSpanishPE list the month names in the Spanish Peru.
	monthNamesSpanishPE = []string{"Enero", "Febrero", "Marzo", "Abril", "Mayo", "Junio", "Julio", "Agosto", "Setiembre", "Octubre", "Noviembre", "Diciembre"}
	// monthNamesSpanishPEAbbr list the month abbreviations in the Spanish Peru.
	monthNamesSpanishPEAbbr = []string{"Ene.", "Feb.", "Mar.", "Abr.", "May.", "Jun.", "Jul.", "Ago.", "Set.", "Oct.", "Nov.", "Dic."}
	// monthNamesSwedish list the month names in the Swedish.
	monthNamesSwedish = []string{"januari", "februari", "mars", "april", "maj", "juni", "juli", "augusti", "september", "oktober", "november", "december"}
	// monthNamesSwedishAbbr list the month abbreviations in the Swedish.
	monthNamesSwedishAbbr = []string{"jan", "feb", "mar", "apr", "maj", "jun", "jul", "aug", "sep", "okt", "nov", "dec"}
	// monthNamesSwedishFIAbbr list the month abbreviations in the Swedish Finland.
	monthNamesSwedishFIAbbr = []string{"jan.", "feb.", "mars", "apr.", "maj", "juni", "juli", "aug.", "sep.", "okt.", "nov.", "dec."}
	// monthNamesSyriac list the month names in the Syriac.
	monthNamesSyriac = []string{
		"\u071F\u0722\u0718\u0722%A0\u0710\u071A\u072A\u071D",
		"\u072B\u0712\u071B",
		"\u0710\u0715\u072A",
		"\u0722\u071D\u0723\u0722",
		"\u0710\u071D\u072A",
		"\u071A\u0719\u071D\u072A\u0722",
		"\u072C\u0721\u0718\u0719",
		"\u0710\u0712",
		"\u0710\u071D\u0720\u0718\u0720",
		"\u072C\u072B\u072A\u071D%A0\u0729\u0715\u071D\u0721",
		"\u072C\u072B\u072A\u071D%A0\u0710\u071A\u072A\u071D",
		"\u071F\u0722\u0718\u0722%A0\u0729\u0715\u071D\u0721",
	}
	// monthNamesSyriacAbbr lists the month name abbreviations in the Syriac.
	monthNamesSyriacAbbr = []string{
		"\u071F\u0722%A0\u070F\u0712",
		"\u072B\u0712\u071B",
		"\u0710\u0715\u072A",
		"\u0722\u071D\u0723\u0722",
		"\u0710\u071D\u072A",
		"\u071A\u0719\u071D\u072A\u0722",
		"\u072C\u0721\u0718\u0719",
		"\u0710\u0712",
		"\u0710\u071D\u0720\u0718\u0720",
		"\u070F\u072C\u072B%A0\u070F\u0710",
		"\u070F\u072C\u072B%A0\u070F\u0712",
		"\u070F\u071F\u0722%A0\u070F\u0710",
	}
	// monthNamesSyllabics list the month names in the Syllabics.
	monthNamesSyllabics = []string{
		"\u152E\u14D0\u14C4\u140A\u1546",
		"\u1556\u155D\u1557\u140A\u1546",
		"\u14AB\u1466\u14EF",
		"\u1404\u1433\u1546",
		"\u14AA\u1403",
		"\u152B\u14C2",
		"\u152A\u14DA\u1403",
		"\u140B\u14A1\u148C\u14EF",
		"\u14EF\u144E\u1431\u1546",
		"\u1405\u1450\u1431\u1546",
		"\u14C4\u1555\u1431\u1546",
		"\u144E\u14EF\u1431\u1546",
	}
	// monthNamesSyllabicsAbbr lists the month name abbreviations in the Syllabics.
	monthNamesSyllabicsAbbr = []string{
		"\u152E\u14D0\u14C4",
		"\u1556\u155D\u1557",
		"\u14AB\u1466\u14EF",
		"\u1404\u1433\u1546",
		"\u14AA\u1403",
		"\u152B\u14C2",
		"\u152A\u14DA\u1403",
		"\u140B\u14A1\u148C",
		"\u14EF\u144E\u1431",
		"\u1405\u1450\u1431",
		"\u14C4\u1555\u1431",
		"\u144E\u14EF\u1431",
	}
	// monthNamesTajik list the month names in the Tajik.
	monthNamesTajik = []string{
		"\u044F\u043D\u0432\u0430\u0440",
		"\u0444\u0435\u0432\u0440\u0430\u043B",
		"\u043C\u0430\u0440\u0442",
		"\u0430\u043F\u0440\u0435\u043B",
		"\u043C\u0430\u0439",
		"\u0438\u044E\u043D",
		"\u0438\u044E\u043B",
		"\u0430\u0432\u0433\u0443\u0441\u0442",
		"\u0441\u0435\u043D\u0442\u044F\u0431\u0440",
		"\u043E\u043A\u0442\u044F\u0431\u0440",
		"\u043D\u043E\u044F\u0431\u0440",
		"\u0434\u0435\u043A\u0430\u0431\u0440",
	}
	// monthNamesTajikAbbr lists the month name abbreviations in Tajik.
	monthNamesTajikAbbr = []string{
		"\u044F\u043D\u0432",
		"\u0444\u0435\u0432",
		"\u043C\u0430\u0440",
		"\u0430\u043F\u0440",
		"\u043C\u0430\u0439",
		"\u0438\u044E\u043D",
		"\u0438\u044E\u043B",
		"\u0430\u0432\u0433",
		"\u0441\u0435\u043D",
		"\u043E\u043A\u0442",
		"\u043D\u043E\u044F",
		"\u0434\u0435\u043A",
	}
	// monthNamesTamazight list the month names in the Tamazight.
	monthNamesTamazight = []string{"Yennayer", "Furar", "Meghres", "Yebrir", "Magu", "Yunyu", "Yulyu", "Ghuct", "Cutenber", "Tuber", "Nunember", "Dujanbir"}
	// monthNamesTamazightAbbr list the month abbreviations in the Tamazight.
	monthNamesTamazightAbbr = []string{"Yen", "Fur", "Megh", "Yeb", "May", "Yun", "Yul", "Ghu", "Cut", "Tub", "Nun", "Duj"}
	// monthNamesTamil list the month names in the Tamil.
	monthNamesTamil = []string{
		"\u0B9C\u0BA9\u0BB5\u0BB0\u0BBF",
		"\u0BAA\u0BBF\u0BAA\u0BCD\u0BB0\u0BB5\u0BB0\u0BBF",
		"\u0BAE\u0BBE\u0BB0\u0BCD\u0B9A\u0BCD",
		"\u0B8F\u0BAA\u0BCD\u0BB0\u0BB2\u0BCD",
		"\u0BAE\u0BC7",
		"\u0B9C\u0BC2\u0BA9\u0BCD",
		"\u0B9C\u0BC2\u0BB2\u0BC8",
		"\u0B86\u0B95\u0BB8\u0BCD\u0B9F\u0BCD",
		"\u0B9A\u0BC6\u0BAA\u0BCD\u0B9F\u0BAE\u0BCD\u0BAA\u0BB0\u0BCD",
		"\u0B85\u0B95\u0BCD\u0B9F\u0BCB\u0BAA\u0BB0\u0BCD",
		"\u0BA8\u0BB5\u0BAE\u0BCD\u0BAA\u0BB0\u0BCD",
		"\u0B9F\u0BBF\u0B9A\u0BAE\u0BCD\u0BAA\u0BB0\u0BCD",
	}
	// monthNamesTamilAbbr lists the month name abbreviations in Tamil.
	monthNamesTamilAbbr = []string{
		"\u0B9C\u0BA9.",
		"\u0BAA\u0BBF\u0BAA\u0BCD.",
		"\u0BAE\u0BBE\u0BB0\u0BCD.",
		"\u0B8F\u0BAA\u0BCD.",
		"\u0BAE\u0BC7",
		"\u0B9C\u0BC2\u0BA9\u0BCD",
		"\u0B9C\u0BC2\u0BB2\u0BC8",
		"\u0B86\u0B95.",
		"\u0B9A\u0BC6\u0BAA\u0BCD.",
		"\u0B85\u0B95\u0BCD.",
		"\u0BA8\u0BB5.",
		"\u0B9F\u0BBF\u0B9A.",
	}
	// monthNamesTatar list the month names in the Tatar.
	monthNamesTatar = []string{
		"\u0433\u044B\u0439\u043D\u0432\u0430\u0440",
		"\u0444\u0435\u0432\u0440\u0430\u043B\u044C",
		"\u043C\u0430\u0440\u0442",
		"\u0430\u043F\u0440\u0435\u043B\u044C",
		"\u043C\u0430\u0439",
		"\u0438\u044E\u043D\u044C",
		"\u0438\u044E\u043B\u044C",
		"\u0430\u0432\u0433\u0443\u0441\u0442",
		"\u0441\u0435\u043D\u0442\u044F\u0431\u0440\u044C",
		"\u043E\u043A\u0442\u044F\u0431\u0440\u044C",
		"\u043D\u043E\u044F\u0431\u0440\u044C",
		"\u0434\u0435\u043A\u0430\u0431\u0440\u044C",
	}
	// monthNamesTatarAbbr lists the month name abbreviations in the Tatar.
	monthNamesTatarAbbr = []string{
		"\u0433\u044B\u0439\u043D.",
		"\u0444\u0435\u0432.",
		"\u043C\u0430\u0440.",
		"\u0430\u043F\u0440.",
		"\u043C\u0430\u0439",
		"\u0438\u044E\u043D\u044C",
		"\u0438\u044E\u043B\u044C",
		"\u0430\u0432\u0433.",
		"\u0441\u0435\u043D.",
		"\u043E\u043A\u0442.",
		"\u043D\u043E\u044F\u0431.",
		"\u0434\u0435\u043A.",
	}
	// monthNamesTelugu list the month names in the Telugu.
	monthNamesTelugu = []string{
		"\u0C1C\u0C28\u0C35\u0C30\u0C3F",
		"\u0C2B\u0C3F\u0C2C\u0C4D\u0C30\u0C35\u0C30\u0C3F",
		"\u0C2E\u0C3E\u0C30\u0C4D\u0C1A\u0C3F",
		"\u0C0F\u0C2A\u0C4D\u0C30\u0C3F\u0C32\u0C4D",
		"\u0C2E\u0C47",
		"\u0C1C\u0C42\u0C28\u0C4D",
		"\u0C1C\u0C41\u0C32\u0C48",
		"\u0C06\u0C17\u0C38\u0C4D\u0C1F\u0C41",
		"\u0C38\u0C46\u0C2A\u0C4D\u0C1F\u0C46\u0C02\u0C2C\u0C30\u0C4D",
		"\u0C05\u0C15\u0C4D\u0C1F\u0C4B\u0C2C\u0C30\u0C4D",
		"\u0C28\u0C35\u0C02\u0C2C\u0C30\u0C4D",
		"\u0C21\u0C3F\u0C38\u0C46\u0C02\u0C2C\u0C30\u0C4D",
	}
	// monthNamesTeluguAbbr lists the month name abbreviations in the Telugu.
	monthNamesTeluguAbbr = []string{
		"\u0C1C\u0C28",
		"\u0C2B\u0C3F\u0C2C\u0C4D\u0C30",
		"\u0C2E\u0C3E\u0C30\u0C4D\u0C1A\u0C3F",
		"\u0C0F\u0C2A\u0C4D\u0C30\u0C3F",
		"\u0C2E\u0C47",
		"\u0C1C\u0C42\u0C28\u0C4D",
		"\u0C1C\u0C41\u0C32\u0C48",
		"\u0C06\u0C17",
		"\u0C38\u0C46\u0C2A\u0C4D\u0C1F\u0C46\u0C02",
		"\u0C05\u0C15\u0C4D\u0C1F\u0C4B",
		"\u0C28\u0C35\u0C02",
		"\u0C21\u0C3F\u0C38\u0C46\u0C02",
	}
	// monthNamesThai list the month names in the Thai.
	monthNamesThai = []string{
		"\u0e21\u0e01\u0e23\u0e32\u0e04\u0e21",
		"\u0e01\u0e38\u0e21\u0e20\u0e32\u0e1e\u0e31\u0e19\u0e18\u0e4c",
		"\u0e21\u0e35\u0e19\u0e32\u0e04\u0e21",
		"\u0e40\u0e21\u0e29\u0e32\u0e22\u0e19",
		"\u0e1e\u0e24\u0e29\u0e20\u0e32\u0e04\u0e21",
		"\u0e21\u0e34\u0e16\u0e38\u0e19\u0e32\u0e22\u0e19",
		"\u0e01\u0e23\u0e01\u0e0e\u0e32\u0e04\u0e21",
		"\u0e2a\u0e34\u0e07\u0e2b\u0e32\u0e04\u0e21",
		"\u0e01\u0e31\u0e19\u0e22\u0e32\u0e22\u0e19",
		"\u0e15\u0e38\u0e25\u0e32\u0e04\u0e21",
		"\u0e1e\u0e24\u0e28\u0e08\u0e34\u0e01\u0e32\u0e22\u0e19",
		"\u0e18\u0e31\u0e19\u0e27\u0e32\u0e04\u0e21",
	}
	// monthNamesTibetan list the month names in the Tibetan.
	monthNamesTibetan = []string{
		"\u0f5f\u0fb3\u0f0b\u0f56\u0f0b\u0f51\u0f44\u0f0b\u0f54\u0f7c\u0f0b",
		"\u0f5f\u0fb3\u0f0b\u0f56\u0f0b\u0f42\u0f49\u0f72\u0f66\u0f0b\u0f54\u0f0b",
		"\u0f5f\u0fb3\u0f0b\u0f56\u0f0b\u0f42\u0f66\u0f74\u0f58\u0f0b\u0f54\u0f0b",
		"\u0f5f\u0fb3\u0f0b\u0f56\u0f0b\u0f56\u0f5e\u0f72\u0f0b\u0f54\u0f0b",
		"\u0f5f\u0fb3\u0f0b\u0f56\u0f0b\u0f63\u0f94\u0f0b\u0f54\u0f0b",
		"\u0f5f\u0fb3\u0f0b\u0f56\u0f0b\u0f51\u0fb2\u0f74\u0f42\u0f0b\u0f54\u0f0b",
		"\u0f5f\u0fb3\u0f0b\u0f56\u0f0b\u0f56\u0f51\u0f74\u0f53\u0f0b\u0f54\u0f0b",
		"\u0f5f\u0fb3\u0f0b\u0f56\u0f0b\u0f56\u0f62\u0f92\u0fb1\u0f51\u0f0b\u0f54\u0f0b",
		"\u0f5f\u0fb3\u0f0b\u0f56\u0f0b\u0f51\u0f42\u0f74\u0f0b\u0f54\u0f0b",
		"\u0f66\u0fa4\u0fb1\u0f72\u0f0b\u0f5f\u0fb3\u0f0b\u0f56\u0f45\u0f74\u0f0b\u0f54\u0f0d",
		"\u0f5f\u0fb3\u0f0b\u0f56\u0f0b\u0f56\u0f45\u0f74\u0f0b\u0f42\u0f45\u0f72\u0f42\u0f0b\u0f54\u0f0b",
		"\u0f5f\u0fb3\u0f0b\u0f56\u0f0b\u0f56\u0f45\u0f74\u0f0b\u0f42\u0f49\u0f72\u0f66\u0f0b\u0f54\u0f0b",
	}
	// monthNamesTibetanAbbr lists the month name abbreviations in the Tibetan.
	monthNamesTibetanAbbr = []string{
		"\u0f5f\u0fb3\u0f0b\u0f21",
		"\u0f5f\u0fb3\u0f0b\u0f22",
		"\u0f5f\u0fb3\u0f0b\u0f23",
		"\u0f5f\u0fb3\u0f0b\u0f24",
		"\u0f5f\u0fb3\u0f0b\u0f25",
		"\u0f5f\u0fb3\u0f0b\u0f26",
		"\u0f5f\u0fb3\u0f0b\u0f27",
		"\u0f5f\u0fb3\u0f0b\u0f28",
		"\u0f5f\u0fb3\u0f0b\u0f29",
		"\u0f5f\u0fb3\u0f0b\u0f21\u0f20",
		"\u0f5f\u0fb3\u0f0b\u0f21\u0f21",
		"\u0f5f\u0fb3\u0f0b\u0f21\u0f22",
	}
	// monthNamesTigrinya list the month names in the Tigrinya.
	monthNamesTigrinya = []string{
		"\u1325\u122A",
		"\u1208\u12AB\u1272\u1275",
		"\u1218\u130B\u1262\u1275",
		"\u121A\u12EB\u12DD\u12EB",
		"\u130D\u1295\u1266\u1275",
		"\u1230\u1290",
		"\u1213\u121D\u1208",
		"\u1290\u1213\u1230",
		"\u1218\u1235\u12A8\u1228\u121D",
		"\u1325\u1245\u121D\u1272",
		"\u1215\u12F3\u122D",
		"\u1273\u1215\u1233\u1235",
	}
	// monthNamesTigrinyaAbbr lists the month name abbreviations in the Tigrinya
	monthNamesTigrinyaAbbr = []string{
		"\u1325\u122A",
		"\u1208\u12AB",
		"\u1218\u130B",
		"\u121A\u12EB",
		"\u130D\u1295",
		"\u1230\u1290",
		"\u1213\u121D",
		"\u1290\u1213",
		"\u1218\u1235",
		"\u1325\u1245",
		"\u1215\u12F3",
		"\u1273\u1215",
	}
	// monthNamesTsonga list the month names in the Tsonga.
	monthNamesTsonga = []string{"Sunguti", "Nyenyenyani", "Nyenyankulu", "Dzivamisoko", "Mudyaxihi", "Khotavuxika", "Mawuwani", "Mhawuri", "Ndzhati", "Nhlangula", "Hukuri", "N’wendzamhala"}
	// monthNamesTsongaAbbr lists the month name abbreviations in Tsonga, this prevents string concatenation.
	monthNamesTsongaAbbr = []string{"Sun", "Yan", "Kul", "Dzi", "Mud", "Kho", "Maw", "Mha", "Ndz", "Nhl", "Huk", "N’w"}
	// monthNamesTradMongolian lists the month number for use with traditional Mongolian.
	monthNamesTradMongolian = []string{"M01", "M02", "M03", "M04", "M05", "M06", "M07", "M08", "M09", "M10", "M11", "M12"}
	// monthNamesTurkish list the month names in the Turkish.
	monthNamesTurkish = []string{"Ocak", "Şubat", "Mart", "Nisan", "Mayıs", "Haziran", "Temmuz", "Ağustos", "Eylül", "Ekim", "Kasım", "Aralık"}
	// monthNamesTurkishAbbr lists the month name abbreviations in Turkish, this prevents string concatenation.
	monthNamesTurkishAbbr = []string{"Oca", "Şub", "Mar", "Nis", "May", "Haz", "Tem", "Ağu", "Eyl", "Eki", "Kas", "Ara"}
	// monthNamesTurkmen list the month names in the Turkmen.
	monthNamesTurkmen = []string{"Ýanwar", "Fewral", "Mart", "Aprel", "Maý", "lýun", "lýul", "Awgust", "Sentýabr", "Oktýabr", "Noýabr", "Dekabr"}
	// monthNamesTurkmenAbbr lists the month name abbreviations in Turkmen, this prevents string concatenation.
	monthNamesTurkmenAbbr = []string{"Ýan", "Few", "Mart", "Apr", "Maý", "lýun", "lýul", "Awg", "Sen", "Okt", "Noý", "Dek"}
	// monthNamesUkrainian list the month names in the Ukrainian.
	monthNamesUkrainian = []string{
		"\u0441\u0456\u0447\u0435\u043D\u044C",
		"\u043B\u044E\u0442\u0438\u0439",
		"\u0431\u0435\u0440\u0435\u0437\u0435\u043D\u044C",
		"\u043A\u0432\u0456\u0442\u0435\u043D\u044C",
		"\u0442\u0440\u0430\u0432\u0435\u043D\u044C",
		"\u0447\u0435\u0440\u0432\u0435\u043D\u044C",
		"\u043B\u0438\u043F\u0435\u043D\u044C",
		"\u0441\u0435\u0440\u043F\u0435\u043D\u044C",
		"\u0432\u0435\u0440\u0435\u0441\u0435\u043D\u044C",
		"\u0436\u043E\u0432\u0442\u0435\u043D\u044C",
		"\u043B\u0438\u0441\u0442\u043E\u043F\u0430\u0434",
		"\u0433\u0440\u0443\u0434\u0435\u043D\u044C",
	}
	// monthNamesUkrainianAbbr lists the month name abbreviations in Ukrainian.
	monthNamesUkrainianAbbr = []string{
		"\u0421\u0456\u0447",
		"\u041B\u044E\u0442",
		"\u0411\u0435\u0440",
		"\u041A\u0432\u0456",
		"\u0422\u0440\u0430",
		"\u0427\u0435\u0440",
		"\u041B\u0438\u043F",
		"\u0421\u0435\u0440",
		"\u0412\u0435\u0440",
		"\u0416\u043E\u0432",
		"\u041B\u0438\u0441",
		"\u0413\u0440\u0443",
	}
	// monthNamesUpperSorbian list the month names in the Upper Sorbian.
	monthNamesUpperSorbian = []string{"januar", "februar", "měrc", "apryl", "meja", "junij", "julij", "awgust", "september", "oktober", "nowember", "december"}
	// monthNamesUpperSorbianAbbr lists the month name abbreviations in the Upper Sorbian, this prevents string concatenation.
	monthNamesUpperSorbianAbbr = []string{"jan", "feb", "měr", "apr", "mej", "jun", "jul", "awg", "sep", "okt", "now", "dec"}
	// monthNamesUyghur list the month names in the Uyghur.
	monthNamesUyghur = []string{
		"\u064A\u0627\u0646\u06CB\u0627\u0631",
		"\u0641\u06D0\u06CB\u0631\u0627\u0644",
		"\u0645\u0627\u0631\u062A",
		"\u0626\u0627\u067E\u0631\u06D0\u0644",
		"\u0645\u0627\u064A",
		"\u0626\u0649\u064A\u06C7\u0646",
		"\u0626\u0649\u064A\u06C7\u0644",
		"\u0626\u0627\u06CB\u063A\u06C7\u0633\u062A",
		"\u0633\u06D0\u0646\u062A\u06D5\u0628\u0649\u0631",
		"\u0626\u06C6\u0643\u062A\u06D5\u0628\u0649\u0631",
		"\u0646\u0648\u064A\u0627\u0628\u0649\u0631",
		"\u062F\u06D0\u0643\u0627\u0628\u0649\u0631",
	}
	// monthNamesUzbek list the month names in the Uzbek.
	monthNamesUzbek = []string{"Yanvar", "Fevral", "Mart", "Aprel", "May", "Iyun", "Iyul", "Avgust", "Sentabr", "Oktabr", "Noyabr", "Dekabr"}
	// monthNamesUzbekAbbr lists the month name abbreviations in the Uzbek, this prevents string concatenation.
	monthNamesUzbekAbbr = []string{"Yan", "Fev", "Mar", "Apr", "May", "Iyn", "Iyl", "Avg", "Sen", "Okt", "Noy", "Dek"}
	// monthNamesValencian list the month names in the Valencian.
	monthNamesValencian = []string{"gener", "febrer", "març", "abril", "maig", "juny", "juliol", "agost", "setembre", "octubre", "novembre", "desembre"}
	// monthNamesValencianAbbr lists the month name abbreviations in the Valencian, this prevents string concatenation.
	monthNamesValencianAbbr = []string{"gen.", "febr.", "març", "abr.", "maig", "juny", "jul.", "ag.", "set.", "oct.", "nov.", "des."}
	// monthNamesVenda list the month names in the Venda.
	monthNamesVenda = []string{"Phando", "Luhuhi", "Ṱhafamuhwe", "Lambamai", "Shundunthule", "Fulwi", "Fulwana", "Ṱhangule", "Khubvumedzi", "Tshimedzi", "Ḽara", "Nyendavhusiku"}
	// monthNamesVendaAbbr lists the month name abbreviations in the Venda, this prevents string concatenation.
	monthNamesVendaAbbr = []string{"Pha", "Luh", "Ṱhf", "Lam", "Shu", "Lwi", "Lwa", "Ṱha", "Khu", "Tsh", "Ḽar", "Nye"}
	// monthNamesVietnamese list the month name used for Vietnamese
	monthNamesVietnamese = []string{"Tháng 1", "Tháng 2", "Tháng 3", "Tháng 4", "Tháng 5", "Tháng 6", "Tháng 7", "Tháng 8", "Tháng 9", "Tháng 10", "Tháng 11", "Tháng 12"}
	// monthNamesVietnameseAbbr3 list the mid-form abbreviation for Vietnamese months.
	monthNamesVietnameseAbbr3 = []string{"Thg 1", "Thg 2", "Thg 3", "Thg 4", "Thg 5", "Thg 6", "Thg 7", "Thg 8", "Thg 9", "Thg 10", "Thg 11", "Thg 12"}
	// monthNamesVietnameseAbbr5 list the short-form abbreviation for Vietnamese months.
	monthNamesVietnameseAbbr5 = []string{"T 1", "T 2", "T 3", "T 4", "T 5", "T 6", "T 7", "T 8", "T 9", "T 10", "T 11", "T 12"}
	// monthNamesWelsh list the month names in the Welsh.
	monthNamesWelsh = []string{"Ionawr", "Chwefror", "Mawrth", "Ebrill", "Mai", "Mehefin", "Gorffennaf", "Awst", "Medi", "Hydref", "Tachwedd", "Rhagfyr"}
	// monthNamesWelshAbbr lists the month name abbreviations in the Welsh, this prevents string concatenation.
	monthNamesWelshAbbr = []string{"Ion", "Chwef", "Maw", "Ebr", "Mai", "Meh", "Gorff", "Awst", "Medi", "Hyd", "Tach", "Rhag"}
	// monthNamesWolof list the month names in the Wolof.
	monthNamesWolof = []string{"Samwiye", "Fewriye", "Maars", "Awril", "Me", "Suwe", "Sullet", "Ut", "Septàmbar", "Oktoobar", "Noowàmbar", "Desàmbar"}
	// monthNamesWolofAbbr list the month name abbreviations in the Wolof, this prevents string concatenation.
	monthNamesWolofAbbr = []string{"Sam.", "Few.", "Maa", "Awr.", "Me", "Suw", "Sul.", "Ut", "Sept.", "Okt.", "Now.", "Des."}
	// monthNamesXhosa list the month names in the Xhosa.
	monthNamesXhosa = []string{"uJanuwari", "uFebuwari", "uMatshi", "uAprili", "uMeyi", "uJuni", "uJulayi", "uAgasti", "uSeptemba", "uOktobha", "uNovemba", "uDisemba"}
	// monthNamesXhosaAbbr list the month abbreviations in the Xhosa, this prevents string concatenation.
	monthNamesXhosaAbbr = []string{"uJan.", "uFeb.", "uMat.", "uEpr.", "uMey.", "uJun.", "uJul.", "uAg.", "uSep.", "uOkt.", "uNov.", "uDis."}
	// monthNamesYi list the month names in the Yi.
	monthNamesYi = []string{"\ua2cd", "\ua44d", "\ua315", "\ua1d6", "\ua26c", "\ua0d8", "\ua3c3", "\ua246", "\ua22c", "\ua2b0", "\ua2b0\ua2aa", "\ua2b0\ua44b"}
	// monthNamesYiSuffix lists the month names in Yi with the "\ua1aa" suffix.
	monthNamesYiSuffix = []string{"\ua2cd\ua1aa", "\ua44d\ua1aa", "\ua315\ua1aa", "\ua1d6\ua1aa", "\ua26c\ua1aa", "\ua0d8\ua1aa", "\ua3c3\ua1aa", "\ua246\ua1aa", "\ua22c\ua1aa", "\ua2b0\ua1aa", "\ua2b0\ua2aa\ua1aa", "\ua2b0\ua44b\ua1aa"}
	// monthNamesYiddish list the month names in the Yiddish.
	monthNamesYiddish = []string{
		"\u05D9\u05D0\u05B7\u05E0\u05D5\u05D0\u05B7\u05E8",
		"\u05E4\u05BF\u05E2\u05D1\u05E8\u05D5\u05D0\u05B7\u05E8",
		"\u05DE\u05E2\u05E8\u05E5",
		"\u05D0\u05B7\u05E4\u05BC\u05E8\u05D9\u05DC",
		"\u05DE\u05D9\u05D9",
		"\u05D9\u05D5\u05E0\u05D9",
		"\u05D9\u05D5\u05DC\u05D9",
		"\u05D0\u05D5\u05D9\u05D2\u05D5\u05E1\u05D8",
		"\u05E1\u05E2\u05E4\u05BC\u05D8\u05E2\u05DE\u05D1\u05E2\u05E8",
		"\u05D0\u05E7\u05D8\u05D0\u05D1\u05E2\u05E8",
		"\u05E0\u05D0\u05D5\u05D5\u05E2\u05DE\u05D1\u05E2\u05E8",
		"\u05D3\u05E2\u05E6\u05E2\u05DE\u05D1\u05E2\u05E8",
	}
	// monthNamesYiddishAbbr lists the month name abbreviations in Yiddish.
	monthNamesYiddishAbbr = []string{
		"\u05D9\u05D0\u05B7\u05E0",
		"\u05E4\u05BF\u05E2\u05D1",
		"\u05DE\u05E2\u05E8\u05E5",
		"\u05D0\u05B7\u05E4\u05BC\u05E8",
		"\u05DE\u05D9\u05D9",
		"\u05D9\u05D5\u05E0\u05D9",
		"\u05D9\u05D5\u05DC\u05D9",
		"\u05D0\u05D5\u05D9\u05D2",
		"\u05E1\u05E2\u05E4\u05BC",
		"\u05D0\u05E7\u05D8",
		"\u05E0\u05D0\u05D5\u05D5",
		"\u05D3\u05E2\u05E6",
	}
	// monthNamesYoruba list the month names in the Yoruba.
	monthNamesYoruba = []string{
		"\u1E62\u1EB9\u0301r\u1EB9\u0301",
		"%C8r%E8l%E8",
		"\u1EB8r\u1EB9\u0300n%E0",
		"%CCgb%E9",
		"\u1EB8\u0300bibi",
		"%D2k%FAdu",
		"Ag\u1EB9m\u1ECD",
		"%D2g%FAn",
		"Owewe",
		"\u1ECC\u0300w%E0r%E0",
		"B%E9l%FA",
		"\u1ECC\u0300p\u1EB9\u0300",
	}
	// monthNamesYorubaAbbr lists the month name abbreviations in the Yoruba.
	monthNamesYorubaAbbr = []string{
		"\u1E62\u1EB9\u0301",
		"%C8r",
		"\u1EB8r",
		"%CCg",
		"\u1EB8\u0300b",
		"%D2k",
		"Ag",
		"%D2g",
		"Ow",
		"\u1ECC\u0300w",
		"B%E9",
		"\u1ECC\u0300p",
	}
	// monthNamesZulu list the month names in the Zulu.
	monthNamesZulu = []string{"Januwari", "Febhuwari", "Mashi", "Ephreli", "Meyi", "Juni", "Julayi", "Agasti", "Septemba", "Okthoba", "Novemba", "Disemba"}
	// monthNamesZuluAbbr list the month name abbreviations in the Zulu.
	monthNamesZuluAbbr = []string{"Jan", "Feb", "Mas", "Eph", "Mey", "Jun", "Jul", "Agas", "Sep", "Okt", "Nov", "Dis"}
	// weekdayNamesAfrikaans list the weekday name in the Afrikaans.
	weekdayNamesAfrikaans = []string{"Sondag", "Maandag", "Dinsdag", "Woensdag", "Donderdag", "Vrydag", "Saterdag"}
	// weekdayNamesAfrikaansAbbr list the weekday name abbreviations in the Afrikaans.
	weekdayNamesAfrikaansAbbr = []string{"So.", "Ma.", "Di.", "Wo.", "Do.", "Vr.", "Sa."}
	// weekdayNamesAlbanian list the weekday name in the Albanian.
	weekdayNamesAlbanian = []string{"e diel", "e hënë", "e martë", "e mërkurë", "e enjte", "e premte", "e shtunë"}
	// weekdayNamesAlbanianAbbr list the weekday name abbreviations in the Albanian.
	weekdayNamesAlbanianAbbr = []string{"die", "hën", "mar", "mër", "enj", "pre", "sht"}
	// weekdayNamesAlsatian list the weekday name in the Alsatian.
	weekdayNamesAlsatian = []string{"Sunntig", "Määntig", "Ziischtig", "Mittwuch", "Dunschtig", "Friitig", "Samschtig"}
	// weekdayNamesAlsatianAbbr list the weekday name abbreviations in the Alsatian.
	weekdayNamesAlsatianAbbr = []string{"Su.", "Mä.", "Zi.", "Mi.", "Du.", "Fr.", "Sa."}
	// weekdayNamesAlsatianFrance list the weekday name in the Alsatian France.
	weekdayNamesAlsatianFrance = []string{"Sundi", "Manti", "Zischti", "Mettwuch", "Dunnerschti", "Friti", "Sàmschti"}
	// weekdayNamesAlsatianFranceAbbr list the weekday name abbreviations in the Alsatian France.
	weekdayNamesAlsatianFranceAbbr = []string{"Su.", "Ma.", "Zi.", "Me.", "Du.", "Fr.", "Sà."}
	// weekdayNamesAmharic list the weekday name in the Amharic.
	weekdayNamesAmharic = []string{
		"\u12A5\u1211\u12F5",
		"\u1230\u129E",
		"\u121B\u12AD\u1230\u129E",
		"\u1228\u1261\u12D5",
		"\u1210\u1219\u1235",
		"\u12D3\u122D\u1265",
		"\u1245\u12F3\u121C",
	}
	// weekdayNamesAmharicAbbr list the weekday name abbreviations in the Amharic.
	weekdayNamesAmharicAbbr = []string{
		"\u12A5\u1211\u12F5",
		"\u1230\u129E",
		"\u121B\u12AD\u1230",
		"\u1228\u1261\u12D5",
		"\u1210\u1219\u1235",
		"\u12D3\u122D\u1265",
		"\u1245\u12F3\u121C",
	}
	// weekdayNamesArabic list the weekday name in the Arabic.
	weekdayNamesArabic = []string{
		"\u0627\u0644\u0623\u062D\u062F",
		"\u0627\u0644\u0625\u062B\u0646\u064A\u0646",
		"\u0627\u0644\u062B\u0644\u0627\u062B\u0627\u0621",
		"\u0627\u0644\u0623\u0631\u0628\u0639\u0627\u0621",
		"\u0627\u0644\u062E\u0645\u064A\u0633",
		"\u0627\u0644\u062C\u0645\u0639\u0629",
		"\u0627\u0644\u0633\u0628\u062A",
	}
	// weekdayNamesArabicAbbr list the weekday name abbreviations in the Arabic.
	weekdayNamesArabicAbbr = []string{
		"\u0627\u0644\u0623\u062D\u062F",
		"\u0627\u0644\u0625\u062B\u0646\u064A\u0646",
		"\u0627\u0644\u062B\u0644\u0627\u062B\u0627\u0621",
		"\u0627\u0644\u0623\u0631\u0628\u0639\u0627\u0621",
		"\u0627\u0644\u062E\u0645\u064A\u0633",
		"\u0627\u0644\u062C\u0645\u0639\u0629",
		"\u0627\u0644\u0633\u0628\u062A",
	}
	// weekdayNamesArmenian list the weekday name in the Armenian.
	weekdayNamesArmenian = []string{
		"\u053F\u056B\u0580\u0561\u056F\u056B",
		"\u0535\u0580\u056F\u0578\u0582\u0577\u0561\u0562\u0569\u056B",
		"\u0535\u0580\u0565\u0584\u0577\u0561\u0562\u0569\u056B",
		"\u0549\u0578\u0580\u0565\u0584\u0577\u0561\u0562\u0569\u056B",
		"\u0540\u056B\u0576\u0563\u0577\u0561\u0562\u0569\u056B",
		"\u0548\u0582\u0580\u0562\u0561\u0569",
		"\u0547\u0561\u0562\u0561\u0569",
	}
	// weekdayNamesArmenianAbbr list the weekday name abbreviations in the Armenian.
	weekdayNamesArmenianAbbr = []string{
		"\u053F\u056B\u0580",
		"\u0535\u0580\u056F",
		"\u0535\u0580\u0584",
		"\u0549\u0580\u0584",
		"\u0540\u0576\u0563",
		"\u0548\u0582\u0580",
		"\u0547\u0562\u0569",
	}
	// weekdayNamesAssamese list the weekday name in the Assamese.
	weekdayNamesAssamese = []string{
		"\u09F0\u09AC\u09BF\u09AC\u09BE\u09F0",
		"\u09B8\u09CB\u09AE\u09AC\u09BE\u09F0",
		"\u09AE\u0999\u09CD\u0997\u09B2\u09AC\u09BE\u09F0",
		"\u09AC\u09C1\u09A7\u09AC\u09BE\u09F0",
		"\u09AC\u09C3\u09B9\u09B8\u09CD\u09AA\u09A4\u09BF\u09AC\u09BE\u09F0",
		"\u09B6\u09C1\u0995\u09CD\u09B0\u09AC\u09BE\u09F0",
		"\u09B6\u09A8\u09BF\u09AC\u09BE\u09F0",
	}
	// weekdayNamesAssameseAbbr list the weekday name abbreviations in the Assamese.
	weekdayNamesAssameseAbbr = []string{
		"\u09F0\u09AC\u09BF.",
		"\u09B8\u09CB\u09AE.",
		"\u09AE\u0999\u09CD\u0997\u09B2.",
		"\u09AC\u09C1\u09A7.",
		"\u09AC\u09C3\u09B9.",
		"\u09B6\u09C1\u0995\u09CD\u09B0.",
		"\u09B6\u09A8\u09BF.",
	}
	// weekdayNamesAzerbaijaniCyrillic list the weekday name in the Azerbaijani (Cyrillic).
	weekdayNamesAzerbaijaniCyrillic = []string{
		"\u0431\u0430\u0437\u0430\u0440",
		"\u0431\u0430\u0437\u0430\u0440%A0\u0435\u0440\u0442\u04D9\u0441\u0438",
		"\u0447\u04D9\u0440\u0448\u04D9\u043D\u0431\u04D9%A0\u0430\u0445\u0448\u0430\u043C\u044B",
		"\u0447\u04D9\u0440\u0448\u04D9\u043D\u0431\u04D9",
		"\u04B9\u04AF\u043C\u04D9%A0\u0430\u0445\u0448\u0430\u043C\u044B",
		"\u04B9\u04AF\u043C\u04D9",
		"\u0448\u04D9\u043D\u0431\u04D9",
	}
	// weekdayNamesAzerbaijaniCyrillicAbbr list the weekday name abbreviations in the Azerbaijani (Cyrillic).
	weekdayNamesAzerbaijaniCyrillicAbbr = []string{"\u0411", "\u0411\u0435", "\u0427\u0430", "\u0427", "\u04B8\u0430", "\u04B8", "\u0428"}
	// weekdayNamesAzerbaijani list the weekday name in the Azerbaijani.
	weekdayNamesAzerbaijani = []string{
		"bazar",
		"bazar%E7ert\u0259si",
		"%E7\u0259r\u015F\u0259nb\u0259%A0ax\u015Fam\u0131",
		"%E7\u0259r\u015F\u0259nb\u0259",
		"c%FCm\u0259%20ax\u015Fam\u0131",
		"c%FCm\u0259",
		"\u015F\u0259nb\u0259",
	}
	// weekdayNamesAzerbaijaniAbbr list the weekday name abbreviations in the Azerbaijani.
	weekdayNamesAzerbaijaniAbbr = []string{"B.", "B.E.", "%C7.A.", "%C7.", "C.A.", "C.", "\u015E."}
	// weekdayNamesBangla list the weekday name in the Bangla.
	weekdayNamesBangla = []string{
		"\u09B0\u09AC\u09BF\u09AC\u09BE\u09B0",
		"\u09B8\u09CB\u09AE\u09AC\u09BE\u09B0",
		"\u09AE\u0999\u09CD\u0997\u09B2\u09AC\u09BE\u09B0",
		"\u09AC\u09C1\u09A7\u09AC\u09BE\u09B0",
		"\u09AC\u09C3\u09B9\u09B8\u09CD\u09AA\u09A4\u09BF\u09AC\u09BE\u09B0",
		"\u09B6\u09C1\u0995\u09CD\u09B0\u09AC\u09BE\u09B0",
		"\u09B6\u09A8\u09BF\u09AC\u09BE\u09B0",
	}
	// weekdayNamesBanglaAbbr list the weekday name abbreviations in the Bangla.
	weekdayNamesBanglaAbbr = []string{
		"\u09B0\u09AC\u09BF.",
		"\u09B8\u09CB\u09AE.",
		"\u09AE\u0999\u09CD\u0997\u09B2.",
		"\u09AC\u09C1\u09A7.",
		"\u09AC\u09C3\u09B9\u09B8\u09CD\u09AA\u09A4\u09BF.",
		"\u09B6\u09C1\u0995\u09CD\u09B0.",
		"\u09B6\u09A8\u09BF.",
	}
	// weekdayNamesBashkir list the weekday name in the Bashkir.
	weekdayNamesBashkir = []string{
		"\u0419\u04D9\u043A\u0448\u04D9\u043C\u0431\u0435",
		"\u0414\u04AF\u0448\u04D9\u043C\u0431\u0435",
		"\u0428\u0438\u0448\u04D9\u043C\u0431\u0435",
		"\u0428\u0430\u0440\u0448\u0430\u043C\u0431\u044B",
		"\u041A\u0435\u0441\u0430\u0499\u043D\u0430",
		"\u0419\u043E\u043C\u0430",
		"\u0428\u04D9\u043C\u0431\u0435",
	}
	// weekdayNamesBashkirAbbr list the weekday name abbreviations in the Bashkir.
	weekdayNamesBashkirAbbr = []string{
		"\u0419\u0448",
		"\u0414\u0448",
		"\u0428\u0448",
		"\u0428\u0440",
		"\u041A\u0441",
		"\u0419\u043C",
		"\u0428\u0431",
	}
	// weekdayNamesBasque list the weekday name in the Basque.
	weekdayNamesBasque = []string{"igandea", "astelehena", "asteartea", "asteazkena", "osteguna", "ostirala", "larunbata"}
	// weekdayNamesBasqueAbbr list the weekday name abbreviations in the Basque.
	weekdayNamesBasqueAbbr = []string{"ig.", "al.", "ar.", "az.", "og.", "or.", "lr."}
	// weekdayNamesBelarusian list the weekday name in the Belarusian.
	weekdayNamesBelarusian = []string{
		"\u043D\u044F\u0434\u0437\u0435\u043B\u044F",
		"\u043F\u0430\u043D\u044F\u0434\u0437\u0435\u043B\u0430\u043A",
		"\u0430\u045E\u0442\u043E\u0440\u0430\u043A",
		"\u0441\u0435\u0440\u0430\u0434\u0430",
		"\u0447\u0430\u0446\u0432\u0435\u0440",
		"\u043F\u044F\u0442\u043D\u0456\u0446\u0430",
		"\u0441\u0443\u0431\u043E\u0442\u0430",
	}
	// weekdayNamesBelarusianAbbr list the weekday name abbreviations in the Belarusian.
	weekdayNamesBelarusianAbbr = []string{
		"\u043D\u0434",
		"\u043F\u043D",
		"\u0430\u045E\u0442",
		"\u0441\u0440",
		"\u0447\u0446",
		"\u043F\u0442",
		"\u0441\u0431",
	}
	// weekdayNamesBosnianCyrillic list the weekday name in the Bosnian (Cyrillic).
	weekdayNamesBosnianCyrillic = []string{
		"\u043D\u0435\u0434\u0458\u0435\u0459\u0430",
		"\u043F\u043E\u043D\u0435\u0434\u0458\u0435\u0459\u0430\u043A",
		"\u0443\u0442\u043E\u0440\u0430\u043A",
		"\u0441\u0440\u0438\u0458\u0435\u0434\u0430",
		"\u0447\u0435\u0442\u0432\u0440\u0442\u0430\u043A",
		"\u043F\u0435\u0442\u0430\u043A",
		"\u0441\u0443\u0431\u043E\u0442\u0430",
	}
	// weekdayNamesBosnianCyrillicAbbr list the weekday name abbreviations in the Bosnian (Cyrillic).
	weekdayNamesBosnianCyrillicAbbr = []string{
		"\u043D\u0435\u0434",
		"\u043F\u043E\u043D",
		"\u0443\u0442\u043E",
		"\u0441\u0440\u0435",
		"\u0447\u0435\u0442",
		"\u043F\u0435\u0442",
		"\u0441\u0443\u0431",
	}
	// weekdayNamesBosnian list the weekday name in the Bosnian.
	weekdayNamesBosnian = []string{"nedjelja", "ponedjeljak", "utorak", "srijeda", "četvrtak", "petak", "subota"}
	// weekdayNamesBosnianAbbr list the weekday name abbreviations in the Bosnian.
	weekdayNamesBosnianAbbr = []string{"ned", "pon", "uto", "sri", "čet", "pet", "sub"}
	// weekdayNamesBreton list the weekday name in the Breton.
	weekdayNamesBreton = []string{"Sul", "Lun", "Meurzh", "Merc'her", "Yaou", "Gwener", "Sadorn"}
	// weekdayNamesBretonAbbr list the weekday name abbreviations in the Breton.
	weekdayNamesBretonAbbr = []string{"Sul", "Lun", "Meu.", "Mer.", "Yaou", "Gwe.", "Sad."}
	// weekdayNamesBulgarian list the weekday name in the Bulgarian.
	weekdayNamesBulgarian = []string{
		"\u043D\u0435\u0434\u0435\u043B\u044F",
		"\u043F\u043E\u043D\u0435\u0434\u0435\u043B\u043D\u0438\u043A",
		"\u0432\u0442\u043E\u0440\u043D\u0438\u043A",
		"\u0441\u0440\u044F\u0434\u0430",
		"\u0447\u0435\u0442\u0432\u044A\u0440\u0442\u044A\u043A",
		"\u043F\u0435\u0442\u044A\u043A",
		"\u0441\u044A\u0431\u043E\u0442\u0430",
	}
	// weekdayNamesBulgarianAbbr list the weekday name abbreviations in the Bulgarian.
	weekdayNamesBulgarianAbbr = []string{
		"\u043D\u0435\u0434",
		"\u043F\u043E\u043D",
		"\u0432\u0442",
		"\u0441\u0440",
		"\u0447\u0435\u0442\u0432",
		"\u043F\u0435\u0442",
		"\u0441\u044A\u0431",
	}
	// weekdayNamesBurmese list the weekday name in the Burmese.
	weekdayNamesBurmese = []string{
		"\u1010\u1014\u1004\u103A\u1039\u1002\u1014\u103D\u1031",
		"\u1010\u1014\u1004\u103A\u1039\u101C\u102C",
		"\u1021\u1004\u103A\u1039\u1002\u102B",
		"\u1017\u102F\u1012\u1039\u1013\u101F\u1030\u1038",
		"\u1000\u103C\u102C\u101E\u1015\u1010\u1031\u1038",
		"\u101E\u1031\u102C\u1000\u103C\u102C",
		"\u1005\u1014\u1031",
	}
	// weekdayNamesCentralKurdish list the weekday name in the Central Kurdish.
	weekdayNamesCentralKurdish = []string{
		"\u06CC\u06D5\u06A9\u0634\u06D5\u0645\u0645\u06D5",
		"\u062F\u0648\u0648\u0634\u06D5\u0645\u0645\u06D5",
		"\u0633\u06CE\u0634\u06D5\u0645\u0645\u06D5",
		"\u0686\u0648\u0627\u0631\u0634\u06D5\u0645\u0645\u06D5",
		"\u067E\u06CE\u0646\u062C\u0634\u06D5\u0645\u0645\u06D5",
		"\u06BE\u06D5\u06CC\u0646\u06CC",
		"\u0634\u06D5\u0645\u0645\u06D5",
	}
	// weekdayNamesCherokee list the weekday name in the Cherokee.
	weekdayNamesCherokee = []string{
		"\u13A4\u13BE\u13D9\u13D3\u13C6\u13CD\u13AC",
		"\u13A4\u13BE\u13D9\u13D3\u13C9\u13C5\u13AF",
		"\u13D4\u13B5\u13C1\u13A2\u13A6",
		"\u13E6\u13A2\u13C1\u13A2\u13A6",
		"\u13C5\u13A9\u13C1\u13A2\u13A6",
		"\u13E7\u13BE\u13A9\u13B6\u13CD\u13D7",
		"\u13A4\u13BE\u13D9\u13D3\u13C8\u13D5\u13BE",
	}
	// weekdayNamesCherokeeAbbr list the weekday name abbreviations in the Cherokee.
	weekdayNamesCherokeeAbbr = []string{
		"\u13C6\u13CD\u13AC",
		"\u13C9\u13C5\u13AF",
		"\u13D4\u13B5\u13C1",
		"\u13E6\u13A2\u13C1",
		"\u13C5\u13A9\u13C1",
		"\u13E7\u13BE\u13A9",
		"\u13C8\u13D5\u13BE",
	}
	// weekdayNamesChinese list the weekday name in the Chinese.
	weekdayNamesChinese = []string{"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"}
	// weekdayNamesChineseAbbr list the weekday name abbreviations in the Chinese.
	weekdayNamesChineseAbbr = []string{"周日", "周一", "周二", "周三", "周四", "周五", "周六"}
	// weekdayNamesChineseAbbr list the weekday name abbreviations in the Chinese.
	weekdayNamesChineseAbbr2 = []string{"週日", "週一", "週二", "週三", "週四", "週五", "週六"}
	// weekdayNamesEnglish list the weekday name in the English.
	weekdayNamesEnglish = []string{"Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"}
	// weekdayNamesEnglishAbbr list the weekday name abbreviations in the English.
	weekdayNamesEnglishAbbr = []string{"Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"}
	// weekdayNamesEstonian list the weekday name in the Estonian.
	weekdayNamesEstonian = []string{"pühapäev", "esmaspäev", "teisipäev", "kolmapäev", "neljapäev", "reede", "laupäev"}
	// weekdayNamesEstonianAbbr list the weekday name abbreviations in the Estonian.
	weekdayNamesEstonianAbbr = []string{"P", "E", "T", "K", "N", "R", "L"}
	// weekdayNamesFaroese list the weekday name in the Faroese.
	weekdayNamesFaroese = []string{"sunnudagur", "mánadagur", "týsdagur", "mikudagur", "hósdagur", "fríggjadagur", "leygardagur"}
	// weekdayNamesFaroeseAbbr list the weekday name abbreviations in the Faroese.
	weekdayNamesFaroeseAbbr = []string{"sun.", "mán.", "týs.", "mik.", "hós.", "frí.", "ley."}
	// weekdayNamesFilipino list the weekday name in the Filipino.
	weekdayNamesFilipino = []string{"Linggo", "Lunes", "Martes", "Miyerkules", "Huwebes", "Biyernes", "Sabado"}
	// weekdayNamesFilipinoAbbr list the weekday name abbreviations in the Filipino.
	weekdayNamesFilipinoAbbr = []string{"Lin", "Lun", "Mar", "Miy", "Huw", "Biy", "Sab"}
	// weekdayNamesFinnish list the weekday name in the Finnish
	weekdayNamesFinnish = []string{"sunnuntai", "maanantai", "tiistai", "keskiviikko", "torstai", "perjantai", "lauantai"}
	// weekdayNamesFinnishAbbr list the weekday name abbreviations in the Finnish
	weekdayNamesFinnishAbbr = []string{"su", "ma", "ti", "ke", "to", "pe", "la"}
	// weekdayNamesFrench list the weekday name in the French.
	weekdayNamesFrench = []string{"dimanche", "lundi", "mardi", "mercredi", "jeudi", "vendredi", "samedi"}
	// weekdayNamesFrenchAbbr list the weekday name abbreviations in the French.
	weekdayNamesFrenchAbbr = []string{"dim.", "lun.", "mar.", "mer.", "jeu.", "ven.", "sam."}
	// weekdayNamesFrisian list the weekday name in the Frisian.
	weekdayNamesFrisian = []string{"snein", "moandei", "tiisdei", "woansdei", "tongersdei", "freed", "sneon"}
	// weekdayNamesFrisianAbbr list the weekday name abbreviations in the Frisian.
	weekdayNamesFrisianAbbr = []string{"sni", "moa", "tii", "woa", "ton", "fre", "sno"}
	// weekdayNamesFulah list the weekday name in the Fulah.
	weekdayNamesFulah = []string{"dewo", "aaɓnde", "mawbaare", "njeslaare", "naasaande", "mawnde", "hoore-biir"}
	// weekdayNamesFulahAbbr list the weekday name abbreviations in the Fulah
	weekdayNamesFulahAbbr = []string{"dew", "aaɓ", "maw", "nje", "naa", "mwd", "hbi"}
	// weekdayNamesNigeria list the weekday name in the Nigeria
	weekdayNamesNigeria = []string{"alete", "altine", "talaata", "alarba", "alkamiisa", "aljumaa", "asete"}
	// weekdayNamesNigeriaAbbr list the weekday name abbreviations in the Nigeria.
	weekdayNamesNigeriaAbbr = []string{"alet", "alt.", "tal.", "alar.", "alk.", "alj.", "aset"}
	// weekdayNamesGalician list the weekday name in the Galician.
	weekdayNamesGalician = []string{"domingo", "luns", "martes", "mércores", "xoves", "venres", "sábado"}
	// weekdayNamesGalicianAbbr list the weekday name abbreviations in the Galician.
	weekdayNamesGalicianAbbr = []string{"dom.", "luns", "mar.", "mér.", "xov.", "ven.", "sáb."}
	// weekdayNamesGeorgian list the weekday name in the Georgian.
	weekdayNamesGeorgian = []string{
		"\u10D9\u10D5\u10D8\u10E0\u10D0",
		"\u10DD\u10E0\u10E8\u10D0\u10D1\u10D0\u10D7\u10D8",
		"\u10E1\u10D0\u10DB\u10E8\u10D0\u10D1\u10D0\u10D7\u10D8",
		"\u10DD\u10D7\u10EE\u10E8\u10D0\u10D1\u10D0\u10D7\u10D8",
		"\u10EE\u10E3\u10D7\u10E8\u10D0\u10D1\u10D0\u10D7\u10D8",
		"\u10DE\u10D0\u10E0\u10D0\u10E1\u10D9\u10D4\u10D5\u10D8",
		"\u10E8\u10D0\u10D1\u10D0\u10D7\u10D8",
	}
	// weekdayNamesGeorgianAbbr list the weekday name abbreviations in the Georgian.
	weekdayNamesGeorgianAbbr = []string{
		"\u10D9\u10D5.",
		"\u10DD\u10E0\u10E8.",
		"\u10E1\u10D0\u10DB\u10E8.",
		"\u10DD\u10D7\u10EE\u10E8.",
		"\u10EE\u10E3\u10D7\u10E8.",
		"\u10DE\u10D0\u10E0.",
		"\u10E8\u10D0\u10D1.",
	}
	// weekdayNamesGerman list the weekday name in the German.
	weekdayNamesGerman = []string{"Sonntag", "Montag", "Dienstag", "Mittwoch", "Donnerstag", "Freitag", "Samstag"}
	// weekdayNamesGermanAbbr list the weekday name abbreviations in the German.
	weekdayNamesGermanAbbr = []string{"So", "Mo", "Di", "Mi", "Do", "Fr", "Sa"}
	// weekdayNamesGreek list the weekday name in the Greek.
	weekdayNamesGreek = []string{
		"\u039A\u03C5\u03C1\u03B9\u03B1\u03BA\u03AE",
		"\u0394\u03B5\u03C5\u03C4\u03AD\u03C1\u03B1",
		"\u03A4\u03C1\u03AF\u03C4\u03B7",
		"\u03A4\u03B5\u03C4\u03AC\u03C1\u03C4\u03B7",
		"\u03A0\u03AD\u03BC\u03C0\u03C4\u03B7",
		"\u03A0\u03B1\u03C1\u03B1\u03C3\u03BA\u03B5\u03C5\u03AE",
		"\u03A3\u03AC\u03B2\u03B2\u03B1\u03C4\u03BF",
	}
	// weekdayNamesGreekAbbr list the weekday name abbreviations in the Greek.
	weekdayNamesGreekAbbr = []string{
		"\u039A\u03C5\u03C1",
		"\u0394\u03B5\u03C5",
		"\u03A4\u03C1\u03B9",
		"\u03A4\u03B5\u03C4",
		"\u03A0\u03B5\u03BC",
		"\u03A0\u03B1\u03C1",
		"\u03A3\u03B1\u03B2",
	}
	// weekdayNamesGreenlandic list the weekday name in the Greenlandic.
	weekdayNamesGreenlandic = []string{"sapaat", "ataasinngorneq", "marlunngorneq", "pingasunngorneq", "sisamanngorneq", "tallimanngorneq", "arfininngorneq"}
	// weekdayNamesGreenlandicAbbr list the weekday name abbreviations in the Greenlandic.
	weekdayNamesGreenlandicAbbr = []string{"sap.", "at.", "marl.", "ping.", "sis.", "tall.", "arf."}
	// weekdayNamesGuarani list the weekday name in the Guarani.
	weekdayNamesGuarani = []string{"arate\u0129", "arak%F5i", "araapy", "ararundy", "arapo", "arapote\u0129", "arapok%F5i"}
	// weekdayNamesGuaraniAbbr list the weekday name abbreviations in the Guarani.
	weekdayNamesGuaraniAbbr = []string{"te\u0129", "k%F5i", "apy", "ndy", "po", "ote\u0129", "ok%F5i"}
	// weekdayNamesGujarati list the weekday name in the Gujarati.
	weekdayNamesGujarati = []string{
		"\u0AB0\u0AB5\u0ABF\u0AB5\u0ABE\u0AB0",
		"\u0AB8\u0ACB\u0AAE\u0AB5\u0ABE\u0AB0",
		"\u0AAE\u0A82\u0A97\u0AB3\u0AB5\u0ABE\u0AB0",
		"\u0AAC\u0AC1\u0AA7\u0AB5\u0ABE\u0AB0",
		"\u0A97\u0AC1\u0AB0\u0AC1\u0AB5\u0ABE\u0AB0",
		"\u0AB6\u0AC1\u0A95\u0ACD\u0AB0\u0AB5\u0ABE\u0AB0",
		"\u0AB6\u0AA8\u0ABF\u0AB5\u0ABE\u0AB0",
	}
	// weekdayNamesGujaratiAbbr list the weekday name abbreviations in the Gujarati.
	weekdayNamesGujaratiAbbr = []string{
		"\u0AB0\u0AB5\u0ABF",
		"\u0AB8\u0ACB\u0AAE",
		"\u0AAE\u0A82\u0A97\u0AB3",
		"\u0AAC\u0AC1\u0AA7",
		"\u0A97\u0AC1\u0AB0\u0AC1",
		"\u0AB6\u0AC1\u0A95\u0ACD\u0AB0",
		"\u0AB6\u0AA8\u0ABF",
	}
	// weekdayNamesHausa list the weekday name in the Hausa.
	weekdayNamesHausa = []string{"Lahadi", "Litinin", "Talata", "Laraba", "Alhamis", "Jummaʼa", "Asabar"}
	// weekdayNamesHausaAbbr list the weekday name abbreviations in the Hausa.
	weekdayNamesHausaAbbr = []string{"Lah", "Lit", "Tal", "Lar", "Alh", "Jum", "Asa"}
	// weekdayNamesHawaiian list the weekday name in the Hawaiian.
	weekdayNamesHawaiian = []string{"Lāpule", "Poʻakahi", "Poʻalua", "Poʻakolu", "Poʻahā", "Poʻalima", "Poʻaono"}
	// weekdayNamesHawaiianAbbr list the weekday name abbreviations in the Hawaiian.
	weekdayNamesHawaiianAbbr = []string{"LP", "P1", "P2", "P3", "P4", "P5", "P6"}
	// weekdayNamesHebrew list the weekday name in the Hebrew.
	weekdayNamesHebrew = []string{
		"\u05D9\u05D5\u05DD%A0\u05E8\u05D0\u05E9\u05D5\u05DF",
		"\u05D9\u05D5\u05DD%A0\u05E9\u05E0\u05D9",
		"\u05D9\u05D5\u05DD%A0\u05E9\u05DC\u05D9\u05E9\u05D9",
		"\u05D9\u05D5\u05DD%A0\u05E8\u05D1\u05D9\u05E2\u05D9",
		"\u05D9\u05D5\u05DD%A0\u05D7\u05DE\u05D9\u05E9\u05D9",
		"\u05D9\u05D5\u05DD%A0\u05E9\u05D9\u05E9\u05D9",
		"\u05E9\u05D1\u05EA",
	}
	// weekdayNamesHebrewAbbr list the weekday name abbreviations in the Hebrew.
	weekdayNamesHebrewAbbr = []string{
		"\u05D9\u05D5\u05DD%A0\u05D0",
		"\u05D9\u05D5\u05DD%A0\u05D1",
		"\u05D9\u05D5\u05DD%A0\u05D2",
		"\u05D9\u05D5\u05DD%A0\u05D3",
		"\u05D9\u05D5\u05DD%A0\u05D4",
		"\u05D9\u05D5\u05DD%A0\u05D5",
		"\u05E9\u05D1\u05EA",
	}
	// weekdayNamesHindi list the weekday name in the Hindi.
	weekdayNamesHindi = []string{
		"\u0930\u0935\u093F\u0935\u093E\u0930",
		"\u0938\u094B\u092E\u0935\u093E\u0930",
		"\u092E\u0902\u0917\u0932\u0935\u093E\u0930",
		"\u092C\u0941\u0927\u0935\u093E\u0930",
		"\u0917\u0941\u0930\u0941\u0935\u093E\u0930",
		"\u0936\u0941\u0915\u094D\u0930\u0935\u093E\u0930",
		"\u0936\u0928\u093F\u0935\u093E\u0930",
	}
	// weekdayNamesHindiAbbr list the weekday name abbreviations in the Hindi.
	weekdayNamesHindiAbbr = []string{
		"\u0930\u0935\u093F.",
		"\u0938\u094B\u092E.",
		"\u092E\u0902\u0917\u0932.",
		"\u092C\u0941\u0927.",
		"\u0917\u0941\u0930\u0941.",
		"\u0936\u0941\u0915\u094D\u0930.",
		"\u0936\u0928\u093F.",
	}
	// weekdayNamesHungarian list the weekday name in the Hungarian.
	weekdayNamesHungarian = []string{"vasárnap", "hétfő", "kedd", "szerda", "csütörtök", "péntek", "szombat"}
	// weekdayNamesHungarianAbbr list the weekday name abbreviations in the Hungarian.
	weekdayNamesHungarianAbbr = []string{"V", "H", "K", "Sze", "Cs", "P", "Szo"}
	// weekdayNamesIcelandic list the weekday name in the Icelandic.
	weekdayNamesIcelandic = []string{"sunnudagur", "mánudagur", "þriðjudagur", "miðvikudagur", "fimmtudagur", "föstudagur", "laugardagur"}
	// weekdayNamesIcelandicAbbr list the weekday name abbreviations in the Icelandic.
	weekdayNamesIcelandicAbbr = []string{"sun.", "mán.", "þri.", "mið.", "fim.", "fös.", "lau."}
	// weekdayNamesIgbo list the weekday name in the Igbo.
	weekdayNamesIgbo = []string{"Ụbọchị Ụka", "Mọnde", "Tiuzdee", "Wenezdee", "Tọọzdee", "Fraịdee", "Satọdee"}
	// weekdayNamesIgboAbbr list the weekday name abbreviations in the Igbo.
	weekdayNamesIgboAbbr = []string{"Ụka", "Mọn", "Tiu", "Wen", "Tọọ", "Fraị", "Satọdee"}
	// weekdayNamesIndonesian list the weekday name in the Indonesian.
	weekdayNamesIndonesian = []string{"Minggu", "Senin", "Selasa", "Rabu", "Kamis", "Jumat", "Sabtu"}
	// weekdayNamesIndonesianAbbr list the weekday name abbreviations in the Indonesian.
	weekdayNamesIndonesianAbbr = []string{"Mgg", "Sen", "Sel", "Rab", "Kam", "Jum", "Sab"}
	// weekdayNamesInuktitut list the weekday name in the Inuktitut.
	weekdayNamesInuktitut = []string{"Naattiinguja", "Naggajjau", "Aippiq", "Pingatsiq", "Sitammiq", "Tallirmiq", "Sivataarvik"}
	// weekdayNamesInuktitutAbbr list the weekday name abbreviations in the Inuktitut.
	weekdayNamesInuktitutAbbr = []string{"Nat", "Nag", "Aip", "Pi", "Sit", "Tal", "Siv"}
	// weekdayNamesSyllabics list the weekday name in the Syllabics.
	weekdayNamesSyllabics = []string{
		"\u14C8\u1466\u144F\u1591\u152D",
		"\u14C7\u14A1\u1490\u153E\u152D\u1405",
		"\u140A\u1403\u1449\u1431\u1585",
		"\u1431\u1593\u1466\u14EF\u1585",
		"\u14EF\u1455\u14BB\u14A5\u1585",
		"\u1455\u14EA\u14D5\u1550\u14A5\u1585",
		"\u14EF\u1559\u1456\u1550\u1555\u1483",
	}
	// weekdayNamesSyllabicsAbbr list the weekday name abbreviations in the Syllabics.
	weekdayNamesSyllabicsAbbr = []string{
		"\u14C8\u1466\u144F",
		"\u14C7\u14A1\u1490",
		"\u140A\u1403\u1449\u1431",
		"\u1431\u1593\u1466\u14EF",
		"\u14EF\u1455",
		"\u1455\u14EA\u14D5",
		"\u14EF\u1559\u1456\u1550\u1555\u1483",
	}
	// weekdayNamesIrish list the weekday name in the Irish.
	weekdayNamesIrish = []string{"Dé Domhnaigh", "Dé Luain", "Dé Máirt", "Dé Céadaoin", "Déardaoin", "Dé hAoine", "Dé Sathairn"}
	// weekdayNamesIrishAbbr list the weekday name abbreviations in the Irish.
	weekdayNamesIrishAbbr = []string{"Domh", "Luan", "Máirt", "Céad", "Déar", "Aoine", "Sath"}
	// weekdayNamesItalian list the weekday name in the Italian.
	weekdayNamesItalian = []string{"domenica", "lunedì", "martedì", "mercoledì", "giovedì", "venerdì", "sabato"}
	// weekdayNamesItalianAbbr list the weekday name abbreviations in the Italian.
	weekdayNamesItalianAbbr = []string{"dom", "lun", "mar", "mer", "gio", "ven", "sab"}
	// weekdayNamesJapanese list the weekday name in the Japanese.
	weekdayNamesJapanese = []string{"日曜日", "月曜日", "火曜日", "水曜日", "木曜日", "金曜日", "土曜日"}
	// weekdayNamesJapaneseAbbr list the weekday name abbreviations in the Japanese.
	weekdayNamesJapaneseAbbr = []string{"日", "月", "火", "水", "木", "金", "土"}
	// weekdayNamesKannada list the weekday name in the Kannada.
	weekdayNamesKannada = []string{
		"\u0CAD\u0CBE\u0CA8\u0CC1\u0CB5\u0CBE\u0CB0",
		"\u0CB8\u0CCB\u0CAE\u0CB5\u0CBE\u0CB0",
		"\u0CAE\u0C82\u0C97\u0CB3\u0CB5\u0CBE\u0CB0",
		"\u0CAC\u0CC1\u0CA7\u0CB5\u0CBE\u0CB0",
		"\u0C97\u0CC1\u0CB0\u0CC1\u0CB5\u0CBE\u0CB0",
		"\u0CB6\u0CC1\u0C95\u0CCD\u0CB0\u0CB5\u0CBE\u0CB0",
		"\u0CB6\u0CA8\u0CBF\u0CB5\u0CBE\u0CB0",
	}
	// weekdayNamesKannadaAbbr list the weekday name abbreviations in the Kannada.
	weekdayNamesKannadaAbbr = []string{
		"\u0CAD\u0CBE\u0CA8\u0CC1.",
		"\u0CB8\u0CCB\u0CAE.",
		"\u0CAE\u0C82\u0C97\u0CB3.",
		"\u0CAC\u0CC1\u0CA7.",
		"\u0C97\u0CC1\u0CB0\u0CC1.",
		"\u0CB6\u0CC1\u0C95\u0CCD\u0CB0.",
		"\u0CB6\u0CA8\u0CBF.",
	}
	// weekdayNamesKashmiri list the weekday name in the Kashmiri.
	weekdayNamesKashmiri = []string{
		"\u0627\u064E\u062A\u06BE\u0648\u0627\u0631",
		"\u0698\u0654\u0646\u062F\u0631\u0655\u0631\u0648\u0627\u0631",
		"\u0628\u06C6\u0645\u0648\u0627\u0631",
		"\u0628\u0648\u062F\u0648\u0627\u0631",
		"\u0628\u0631\u0620\u0633\u0648\u0627\u0631",
		"\u062C\u064F\u0645\u06C1",
		"\u0628\u0679\u0648\u0627\u0631",
	}
	// weekdayNamesKashmiriAbbr list the weekday name abbreviations in the Kashmiri.
	weekdayNamesKashmiriAbbr = []string{
		"\u0622\u062A\u06BE\u0648\u0627\u0631",
		"\u0698\u0654\u0646\u062F\u0655\u0631\u0648\u0627\u0631",
		"\u0628\u06C6\u0645\u0648\u0627\u0631",
		"\u0628\u0648\u062F\u0648\u0627\u0631",
		"\u0628\u0631\u0620\u0633\u0648\u0627\u0631",
		"\u062C\u064F\u0645\u06C1",
		"\u0628\u0679\u0648\u0627\u0631",
	}
	// weekdayNamesKazakh list the weekday name in the Kazakh.
	weekdayNamesKazakh = []string{
		"\u0436\u0435\u043A\u0441\u0435\u043D\u0431\u0456",
		"\u0434\u04AF\u0439\u0441\u0435\u043D\u0431\u0456",
		"\u0441\u0435\u0439\u0441\u0435\u043D\u0431\u0456",
		"\u0441\u04D9\u0440\u0441\u0435\u043D\u0431\u0456",
		"\u0431\u0435\u0439\u0441\u0435\u043D\u0431\u0456",
		"\u0436\u04B1\u043C\u0430",
		"\u0441\u0435\u043D\u0431\u0456",
	}
	// weekdayNamesKazakhAbbr list the weekday name abbreviations in the Kazakh.
	weekdayNamesKazakhAbbr = []string{
		"\u0436\u0435\u043A",
		"\u0434\u04AF\u0439",
		"\u0441\u0435\u0439",
		"\u0441\u04D9\u0440",
		"\u0431\u0435\u0439",
		"\u0436\u04B1\u043C",
		"\u0441\u0435\u043D",
	}
	// weekdayNamesKhmer list the weekday name in the Khmer.
	weekdayNamesKhmer = []string{
		"\u1790\u17D2\u1784\u17C3\u17A2\u17B6\u1791\u17B7\u178F\u17D2\u1799",
		"\u1790\u17D2\u1784\u17C3\u1785\u17D0\u1793\u17D2\u1791",
		"\u1790\u17D2\u1784\u17C3\u17A2\u1784\u17D2\u1782\u17B6\u179A",
		"\u1790\u17D2\u1784\u17C3\u1796\u17BB\u1792",
		"\u1790\u17D2\u1784\u17C3\u1796\u17D2\u179A\u17A0\u179F\u17D2\u1794\u178F\u17B7\u17CD",
		"\u1790\u17D2\u1784\u17C3\u179F\u17BB\u1780\u17D2\u179A",
		"\u1790\u17D2\u1784\u17C3\u179F\u17C5\u179A\u17CD",
	}
	// weekdayNamesKhmerAbbr list the weekday name abbreviations in the Khmer.
	weekdayNamesKhmerAbbr = []string{
		"\u17A2\u17B6\u1791\u17B7.",
		"\u1785.",
		"\u17A2.",
		"\u1796\u17BB",
		"\u1796\u17D2\u179A\u17A0.",
		"\u179F\u17BB.",
		"\u179F.",
	}
	// weekdayNamesKiche list the weekday name in the Kiche.
	weekdayNamesKiche = []string{"juq'ij", "kaq'ij", "oxq'ij", "kajq'ij", "joq'ij", "waqq'ij", "wuqq'ij"}
	// weekdayNamesKicheAbbr list the weekday name abbreviations in the Kiche.
	weekdayNamesKicheAbbr = []string{"juq'", "kaq'", "oxq'", "kajq'", "joq'", "waqq'", "wuqq'"}
	// weekdayNamesKinyarwanda list the weekday name in the Kinyarwanda.
	weekdayNamesKinyarwanda = []string{"Ku cyumweru", "Ku wa mbere", "Ku wa kabiri", "Ku wa gatatu", "Ku wa kane", "Ku wa gatanu", "Ku wa gatandatu"}
	// weekdayNamesKinyarwandaAbbr list the weekday name abbreviations in the Kinyarwanda.
	weekdayNamesKinyarwandaAbbr = []string{"cyu.", "mbe.", "kab.", "gat.", "kan.", "gnu.", "gat."}
	// weekdayNamesKiswahili list the weekday name in the Kiswahili.
	weekdayNamesKiswahili = []string{"Jumapili", "Jumatatu", "Jumanne", "Jumatano", "Alhamisi", "Ijumaa", "Jumamosi"}
	// weekdayNamesKiswahiliAbbr list the weekday name abbreviations in the Kiswahili.
	weekdayNamesKiswahiliAbbr = []string{"Jpl", "Jtt", "Jnn", "Jtn", "Alh", "Ijm", "Jms"}
	// weekdayNamesKonkani list the weekday name in the Konkani.
	weekdayNamesKonkani = []string{
		"\u0906\u092F\u0924\u093E\u0930",
		"\u0938\u094B\u092E\u093E\u0930",
		"\u092E\u0902\u0917\u0933\u093E\u0930",
		"\u092C\u0941\u0927\u0935\u093E\u0930",
		"\u092C\u093F\u0930\u0947\u0938\u094D\u0924\u093E\u0930",
		"\u0938\u0941\u0915\u094D\u0930\u093E\u0930",
		"\u0936\u0947\u0928\u0935\u093E\u0930",
	}
	// weekdayNamesKonkaniAbbr list the weekday name abbreviations in the Konkani.
	weekdayNamesKonkaniAbbr = []string{
		"\u0906\u092F.",
		"\u0938\u094B\u092E.",
		"\u092E\u0902\u0917\u0933.",
		"\u092C\u0941\u0927.",
		"\u092C\u093F\u0930\u0947.",
		"\u0938\u0941\u0915\u094D\u0930.",
		"\u0936\u0947\u0928.",
	}
	// weekdayNamesKorean list the weekday name in the Korean.
	weekdayNamesKorean = []string{"일요일", "월요일", "화요일", "수요일", "목요일", "금요일", "토요일"}
	// weekdayNamesKoreanAbbr list the weekday name abbreviations in the Korean.
	weekdayNamesKoreanAbbr = []string{"일", "월", "화", "수", "목", "금", "토"}
	// weekdayNamesKyrgyz list the weekday name in the Kyrgyz.
	weekdayNamesKyrgyz = []string{
		"\u0436\u0435\u043A\u0448\u0435\u043C\u0431\u0438",
		"\u0434\u04AF\u0439\u0448\u04E9\u043C\u0431\u04AF",
		"\u0448\u0435\u0439\u0448\u0435\u043C\u0431\u0438",
		"\u0448\u0430\u0440\u0448\u0435\u043C\u0431\u0438",
		"\u0431\u0435\u0439\u0448\u0435\u043C\u0431\u0438",
		"\u0436\u0443\u043C\u0430",
		"\u0438\u0448\u0435\u043C\u0431\u0438",
	}
	// weekdayNamesKyrgyzAbbr list the weekday name abbreviations in the Kyrgyz.
	weekdayNamesKyrgyzAbbr = []string{
		"\u0436\u0435\u043A.",
		"\u0434\u04AF\u0439.",
		"\u0448\u0435\u0439\u0448.",
		"\u0448\u0430\u0440\u0448.",
		"\u0431\u0435\u0439\u0448.",
		"\u0436\u0443\u043C\u0430",
		"\u0438\u0448\u043C.",
	}
	// weekdayNamesLao list the weekday name in the Lao.
	weekdayNamesLao = []string{
		"\u0EA7\u0EB1\u0E99\u0EAD\u0EB2\u0E97\u0EB4\u0E94",
		"\u0EA7\u0EB1\u0E99\u0E88\u0EB1\u0E99",
		"\u0EA7\u0EB1\u0E99\u0EAD\u0EB1\u0E87\u0E84\u0EB2\u0E99",
		"\u0EA7\u0EB1\u0E99\u0E9E\u0EB8\u0E94",
		"\u0EA7\u0EB1\u0E99\u0E9E\u0EB0\u0EAB\u0EB1\u0E94",
		"\u0EA7\u0EB1\u0E99\u0EAA\u0EB8\u0E81",
		"\u0EA7\u0EB1\u0E99\u0EC0\u0EAA\u0EBB\u0EB2",
	}
	// weekdayNamesLaoAbbr list the weekday name abbreviations in the Lao.
	weekdayNamesLaoAbbr = []string{
		"\u0EAD\u0EB2\u0E97\u0EB4\u0E94",
		"\u0E88\u0EB1\u0E99",
		"\u0EAD\u0EB1\u0E87\u0E84\u0EB2\u0E99",
		"\u0E9E\u0EB8\u0E94",
		"\u0E9E\u0EB0\u0EAB\u0EB1\u0E94",
		"\u0EAA\u0EB8\u0E81",
		"\u0EC0\u0EAA\u0EBB\u0EB2",
	}
	// weekdayNamesLatin list the weekday name in the Latin.
	weekdayNamesLatin = []string{"Solis", "Lunae", "Martis", "Mercurii", "Jovis", "Veneris", "Saturni"}
	// weekdayNamesLatinAbbr list the weekday name abbreviations in the Latin.
	weekdayNamesLatinAbbr = []string{"Sol", "Lun", "Mar", "Mer", "Jov", "Ven", "Sat"}
	// weekdayNamesLatvian list the weekday name in the Latvian.
	weekdayNamesLatvian = []string{"svētdiena", "pirmdiena", "otrdiena", "trešdiena", "ceturtdiena", "piektdiena", "sestdiena"}
	// weekdayNamesLatvianAbbr list the weekday name abbreviations in the Latvian.
	weekdayNamesLatvianAbbr = []string{"svētd.", "pirmd.", "otrd.", "trešd.", "ceturtd.", "piektd.", "sestd."}
	// weekdayNamesLithuanian list the weekday name in the Lithuanian.
	weekdayNamesLithuanian = []string{"sekmadienis", "pirmadienis", "antradienis", "trečiadienis", "ketvirtadienis", "penktadienis", "šeštadienis"}
	// weekdayNamesLithuanianAbbr list the weekday name abbreviations in the Lithuanian.
	weekdayNamesLithuanianAbbr = []string{"sk", "pr", "an", "tr", "kt", "pn", "št"}
	// weekdayNamesLowerSorbian list the weekday name in the Lower Sorbian.
	weekdayNamesLowerSorbian = []string{"nje\u017Aela", "ponje\u017Aele", "wa\u0142tora", "srjoda", "stw%F3rtk", "p\u011Btk", "sobota"}
	// weekdayNamesLowerSorbianAbbr list the weekday name abbreviations in the Luxembourgish.
	weekdayNamesLowerSorbianAbbr = []string{"nje", "pon", "wa\u0142", "srj", "stw", "p\u011Bt", "sob"}
	// weekdayNamesLuxembourgish list the weekday name in the Luxembourgish
	weekdayNamesLuxembourgish = []string{"Sonndeg", "Méindeg", "Dënschdeg", "Mëttwoch", "Donneschdeg", "Freideg", "Samschdeg"}
	// weekdayNamesLuxembourgishAbbr list the weekday name abbreviations in the Lower Sorbian.
	weekdayNamesLuxembourgishAbbr = []string{"Son", "Méi", "Dën", "Mët", "Don", "Fre", "Sam"}
	// weekdayNamesMacedonian list the weekday name in the Macedonian.
	weekdayNamesMacedonian = []string{
		"\u043D\u0435\u0434\u0435\u043B\u0430",
		"\u043F\u043E\u043D\u0435\u0434\u0435\u043B\u043D\u0438\u043A",
		"\u0432\u0442\u043E\u0440\u043D\u0438\u043A",
		"\u0441\u0440\u0435\u0434\u0430",
		"\u0447\u0435\u0442\u0432\u0440\u0442\u043E\u043A",
		"\u043F\u0435\u0442\u043E\u043A",
		"\u0441\u0430\u0431\u043E\u0442\u0430",
	}
	// weekdayNamesMacedonianAbbr list the weekday name abbreviations in the Macedonian.
	weekdayNamesMacedonianAbbr = []string{
		"\u043D\u0435\u0434.",
		"\u043F\u043E\u043D.",
		"\u0432\u0442.",
		"\u0441\u0440\u0435.",
		"\u0447\u0435\u0442.",
		"\u043F\u0435\u0442.",
		"\u0441\u0430\u0431.",
	}
	// weekdayNamesMalay list the weekday name in the Malay.
	weekdayNamesMalay = []string{"Ahad", "Isnin", "Selasa", "Rabu", "Khamis", "Jumaat", "Sabtu"}
	// weekdayNamesMalayAbbr list the weekday name abbreviations in the Lower Sorbian.
	weekdayNamesMalayAbbr = []string{"Ahd", "Isn", "Sel", "Rab", "Kha", "Jum", "Sab"}
	// weekdayNamesMalayalam list the weekday name in the Malayalam.
	weekdayNamesMalayalam = []string{
		"\u0D1E\u0D3E\u0D2F\u0D31\u0D3E\u0D34\u0D4D\u200C\u0D1A",
		"\u0D24\u0D3F\u0D19\u0D4D\u0D15\u0D33\u0D3E\u0D34\u0D4D\u200C\u0D1A",
		"\u0D1A\u0D4A\u0D35\u0D4D\u0D35\u0D3E\u0D34\u0D4D\u0D1A",
		"\u0D2C\u0D41\u0D27\u0D28\u0D3E\u0D34\u0D4D\u200C\u0D1A",
		"\u0D35\u0D4D\u0D2F\u0D3E\u0D34\u0D3E\u0D34\u0D4D\u200C\u0D1A",
		"\u0D35\u0D46\u0D33\u0D4D\u0D33\u0D3F\u0D2F\u0D3E\u0D34\u0D4D\u200C\u0D1A",
		"\u0D36\u0D28\u0D3F\u0D2F\u0D3E\u0D34\u0D4D\u200C\u0D1A",
	}
	// weekdayNamesMalayalamAbbr list the weekday name abbreviations in the Malayalam.
	weekdayNamesMalayalamAbbr = []string{
		"\u0D1E\u0D3E\u0D2F\u0D7C",
		"\u0D24\u0D3F\u0D19\u0D4D\u0D15\u0D7E",
		"\u0D1A\u0D4A\u0D35\u0D4D\u0D35",
		"\u0D2C\u0D41\u0D27\u0D7B",
		"\u0D35\u0D4D\u0D2F\u0D3E\u0D34\u0D02",
		"\u0D35\u0D46\u0D33\u0D4D\u0D33\u0D3F",
		"\u0D36\u0D28\u0D3F",
	}
	// weekdayNamesMaltese list the weekday name in the Maltese.
	weekdayNamesMaltese = []string{"Il-\u0126add", "It-Tnejn", "It-Tlieta", "L-Erbg\u0127a", "Il-\u0126amis", "Il-\u0120img\u0127a", "Is-Sibt"}
	// weekdayNamesMalteseAbbr list the weekday name abbreviations in the Maltese.
	weekdayNamesMalteseAbbr = []string{"\u0126ad", "Tne", "Tli", "Erb", "\u0126am", "\u0120im", "Sib"}
	// weekdayNamesMaori list the weekday name in the Maori.
	weekdayNamesMaori = []string{"Rātapu", "Rāhina", "Rātū", "Rāapa", "Rāpare", "Rāmere", "Rāhoroi"}
	// weekdayNamesMaoriAbbr list the weekday name abbreviations in the Maori.
	weekdayNamesMaoriAbbr = []string{"Ta", "Hi", "Tū", "Apa", "Pa", "Me", "Ho"}
	// weekdayNamesMapudungun list the weekday name in the Mapudungun.
	weekdayNamesMapudungun = []string{"Kiñe Ante", "Epu Ante", "Kila Ante", "Meli Ante", "Kechu Ante", "Cayu Ante", "Regle Ante"}
	// weekdayNamesMapudungunAbbr list the weekday name abbreviations in the Mapudungun.
	weekdayNamesMapudungunAbbr = []string{"Kiñe", "Epu", "Kila", "Meli", "Kechu", "Cayu", "Regle"}
	// weekdayNamesMarathi list the weekday name in the Marathi.
	weekdayNamesMarathi = []string{
		"\u0930\u0935\u093F\u0935\u093E\u0930",
		"\u0938\u094B\u092E\u0935\u093E\u0930",
		"\u092E\u0902\u0917\u0933\u0935\u093E\u0930",
		"\u092C\u0941\u0927\u0935\u093E\u0930",
		"\u0917\u0941\u0930\u0941\u0935\u093E\u0930",
		"\u0936\u0941\u0915\u094D\u0930\u0935\u093E\u0930",
		"\u0936\u0928\u093F\u0935\u093E\u0930",
	}
	// weekdayNamesMarathiAbbr list the weekday name abbreviations in the Marathi.
	weekdayNamesMarathiAbbr = []string{
		"\u0930\u0935\u093F.",
		"\u0938\u094B\u092E.",
		"\u092E\u0902\u0917\u0933.",
		"\u092C\u0941\u0927.",
		"\u0917\u0941\u0930\u0941.",
		"\u0936\u0941\u0915\u094D\u0930.",
		"\u0936\u0928\u093F.",
	}
	// weekdayNamesMohawk list the weekday name in the Mohawk.
	weekdayNamesMohawk = []string{"Awentatokentì:ke", "Awentataón'ke", "Ratironhia'kehronòn:ke", "Soséhne", "Okaristiiáhne", "Ronwaia'tanentaktonhne", "Entákta"}
	// weekdayNamesMongolian list the weekday name in the Mongolian.
	weekdayNamesMongolian = []string{
		"\u043D\u044F\u043C",
		"\u0434\u0430\u0432\u0430\u0430",
		"\u043C\u044F\u0433\u043C\u0430\u0440",
		"\u043B\u0445\u0430\u0433\u0432\u0430",
		"\u043F\u04AF\u0440\u044D\u0432",
		"\u0431\u0430\u0430\u0441\u0430\u043D",
		"\u0431\u044F\u043C\u0431\u0430",
	}
	// weekdayNamesMongolianAbbr list the weekday name abbreviations in the Mongolian.
	weekdayNamesMongolianAbbr = []string{
		"\u041D\u044F",
		"\u0414\u0430",
		"\u041C\u044F",
		"\u041B\u0445",
		"\u041F\u04AF",
		"\u0411\u0430",
		"\u0411\u044F",
	}
	// weekdayNamesMongolianCyrlAbbr list the weekday name abbreviations in the Mongolian (Cyrillic).
	weekdayNamesMongolianCyrlAbbr = []string{
		"\u041D\u044F",
		"\u0414\u0430",
		"\u041C\u044F",
		"\u041B\u0445\u0430",
		"\u041F\u04AF",
		"\u0411\u0430",
		"\u0411\u044F",
	}
	// weekdayNamesTraditionalMongolian list the weekday name abbreviations in the Traditional Mongolian.
	weekdayNamesTraditionalMongolian = []string{
		"\u182D\u1820\u1837\u1820\u182D\u202F\u1824\u1828%20\u1821\u1833\u1826\u1837",
		"\u182D\u1820\u1837\u1820\u182D\u202F\u1824\u1828%20\u1828\u1822\u182D\u1821\u1828",
		"\u182D\u1820\u1837\u1820\u182D\u202F\u1824\u1828%20\u182C\u1823\u1836\u1820\u1837",
		"\u182D\u1820\u1837\u1820\u182D\u202F\u1824\u1828%20\u182D\u1824\u1837\u182A\u1820\u1828",
		"\u182D\u1820\u1837\u1820\u182D\u202F\u1824\u1828%20\u1833\u1825\u1837\u182A\u1821\u1828",
		"\u182D\u1820\u1837\u1820\u182D\u202F\u1824\u1828%20\u1832\u1820\u182A\u1824\u1828",
		"\u182D\u1820\u1837\u1820\u182D\u202F\u1824\u1828%20\u1835\u1822\u1837\u182D\u1824\u182D\u1820\u1828",
	}
	// weekdayNamesTraditionalMongolianMN list the weekday name abbreviations in the Traditional Mongolian MN.
	weekdayNamesTraditionalMongolianMN = []string{
		"\u1828\u1822\u182E\u180E\u1820",
		"\u1833\u1820\u1838\u1820",
		"\u182E\u1822\u182D\u182E\u1820\u1837",
		"\u1840\u1820\u182D\u182A\u1820",
		"\u182B\u1826\u1837\u182A\u1826",
		"\u182A\u1820\u1830\u1820\u1829",
		"\u182A\u1822\u182E\u182A\u1820",
	}
	// weekdayNamesNepali list the weekday name in the Nepali.
	weekdayNamesNepali = []string{
		"\u0906\u0907\u0924\u0935\u093E\u0930",
		"\u0938\u094B\u092E\u0935\u093E\u0930",
		"\u092E\u0919\u094D\u0917\u0932\u0935\u093E\u0930",
		"\u092C\u0941\u0927\u0935\u093E\u0930",
		"\u092C\u093F\u0939\u0940\u0935\u093E\u0930",
		"\u0936\u0941\u0915\u094D\u0930\u0935\u093E\u0930",
		"\u0936\u0928\u093F\u0935\u093E\u0930",
	}
	// weekdayNamesNepaliAbbr list the weekday name abbreviations in the Nepali.
	weekdayNamesNepaliAbbr = []string{
		"\u0906\u0907\u0924",
		"\u0938\u094B\u092E",
		"\u092E\u0919\u094D\u0917\u0932",
		"\u092C\u0941\u0927",
		"\u092C\u093F\u0939\u0940",
		"\u0936\u0941\u0915\u094D\u0930",
		"\u0936\u0928\u093F",
	}
	// weekdayNamesNepaliIN list the weekday name in the Nepali India.
	weekdayNamesNepaliIN = []string{
		"\u0906\u0907\u0924\u092C\u093E\u0930",
		"\u0938\u094B\u092E\u092C\u093E\u0930",
		"\u092E\u0919\u094D\u0917\u0932\u092C\u093E\u0930",
		"\u092C\u0941\u0927\u092C\u093E\u0930",
		"\u092C\u093F\u0939\u093F\u092C\u093E\u0930",
		"\u0936\u0941\u0915\u094D\u0930\u092C\u093E\u0930",
		"\u0936\u0928\u093F\u092C\u093E\u0930",
	}
	// weekdayNamesNepaliINAbbr list the weekday name abbreviations in the Nepali India.
	weekdayNamesNepaliINAbbr = []string{
		"\u0906\u0907\u0924",
		"\u0938\u094B\u092E",
		"\u092E\u0919\u094D\u0917\u0932",
		"\u092C\u0941\u0927",
		"\u092C\u093F\u0939\u093F",
		"\u0936\u0941\u0915\u094D\u0930",
		"\u0936\u0928\u093F",
	}
	// weekdayNamesNorwegian list the weekday name in the Norwegian.
	weekdayNamesNorwegian = []string{"s%F8ndag", "mandag", "tirsdag", "onsdag", "torsdag", "fredag", "l%F8rdag"}
	// weekdayNamesNorwegianAbbr list the weekday name abbreviations in the Norwegian.
	weekdayNamesNorwegianAbbr = []string{"s%F8n.", "man.", "tir.", "ons.", "tor.", "fre.", "l%F8r."}
	// weekdayNamesNorwegianNOAbbr list the weekday name abbreviations in the Norwegian Norway.
	weekdayNamesNorwegianNOAbbr = []string{"s%F8n", "man", "tir", "ons", "tor", "fre", "l%F8r"}
	// weekdayNamesNorwegianNynorsk list the weekday name abbreviations in the Norwegian Nynorsk.
	weekdayNamesNorwegianNynorsk = []string{"s%F8ndag", "m%E5ndag", "tysdag", "onsdag", "torsdag", "fredag", "laurdag"}
	// weekdayNamesNorwegianNynorskAbbr list the weekday name abbreviations in the Norwegian Nynorsk.
	weekdayNamesNorwegianNynorskAbbr = []string{"s%F8n", "m%E5n", "tys", "ons", "tor", "fre", "lau"}
	// weekdayNamesOccitan list the weekday name abbreviations in the Occitan.
	weekdayNamesOccitan = []string{"dimenge", "diluns", "dimarts", "dimècres", "dijòus", "divendres", "dissabte"}
	// weekdayNamesOccitanAbbr list the weekday name abbreviations in the Occitan.
	weekdayNamesOccitanAbbr = []string{"dg.", "dl.", "dma.", "dmc.", "dj.", "dv.", "ds."}
	// weekdayNamesOdia list the weekday name in the Odia.
	weekdayNamesOdia = []string{
		"\u0B30\u0B2C\u0B3F\u0B2C\u0B3E\u0B30",
		"\u0B38\u0B4B\u0B2E\u0B2C\u0B3E\u0B30",
		"\u0B2E\u0B19\u0B4D\u0B17\u0B33\u0B2C\u0B3E\u0B30",
		"\u0B2C\u0B41\u0B27\u0B2C\u0B3E\u0B30",
		"\u0B17\u0B41\u0B30\u0B41\u0B2C\u0B3E\u0B30",
		"\u0B36\u0B41\u0B15\u0B4D\u0B30\u0B2C\u0B3E\u0B30",
		"\u0B36\u0B28\u0B3F\u0B2C\u0B3E\u0B30",
	}
	// weekdayNamesOdiaAbbr list the weekday name abbreviations in the Odia.
	weekdayNamesOdiaAbbr = []string{
		"\u0B30\u0B2C\u0B3F.",
		"\u0B38\u0B4B\u0B2E.",
		"\u0B2E\u0B19\u0B4D\u0B17\u0B33.",
		"\u0B2C\u0B41\u0B27.",
		"\u0B17\u0B41\u0B30\u0B41.",
		"\u0B36\u0B41\u0B15\u0B4D\u0B30.",
		"\u0B36\u0B28\u0B3F.",
	}
	// weekdayNamesOromo list the weekday name abbreviations in the Oromo.
	weekdayNamesOromo = []string{"Dilbata", "Wiixata", "Qibxata", "Roobii", "Kamiisa", "Jimaata", "Sanbata"}
	// weekdayNamesOromoAbbr list the weekday name abbreviations in the Oromo.
	weekdayNamesOromoAbbr = []string{"Dil", "Wix", "Qib", "Rob", "Kam", "Jim", "San"}
	// weekdayNamesPashto list the weekday name in the Pashto.
	weekdayNamesPashto = []string{
		"\u064A\u0648\u0646\u06CD",
		"\u062F\u0648\u0646\u06CD",
		"\u062F\u0631\u06D0\u0646\u06CD",
		"\u0685\u0644\u0631\u0646\u06CD",
		"\u067E\u064A\u0646\u0681\u0646\u06CD",
		"\u062C\u0645\u0639\u0647",
		"\u0627\u0648\u0646\u06CD",
	}
	// weekdayNamesPersian list the weekday name in the Persian.
	weekdayNamesPersian = []string{
		"\u064A\u0643\u0634\u0646\u0628\u0647",
		"\u062F\u0648\u0634\u0646\u0628\u0647",
		"\u0633\u0647%A0\u0634\u0646\u0628\u0647",
		"\u0686\u0647\u0627\u0631\u0634\u0646\u0628\u0647",
		"\u067E\u0646\u062C\u0634\u0646\u0628\u0647",
		"\u062C\u0645\u0639\u0647",
		"\u0634\u0646\u0628\u0647",
	}
	// weekdayNamesPolish list the weekday name abbreviations in the Polish.
	weekdayNamesPolish = []string{"niedziela", "poniedziałek", "wtorek", "środa", "czwartek", "piątek", "sobota"}
	// weekdayNamesPolishAbbr list the weekday name abbreviations in the Polish.
	weekdayNamesPolishAbbr = []string{"niedz.", "pon.", "wt.", "śr.", "czw.", "pt.", "sob."}
	// weekdayNamesPortuguese list the weekday name abbreviations in the Portuguese.
	weekdayNamesPortuguese = []string{"domingo", "segunda-feira", "terça-feira", "quarta-feira", "quinta-feira", "sexta-feira", "sábado"}
	// weekdayNamesPortugueseAbbr list the weekday name abbreviations in the Portuguese.
	weekdayNamesPortugueseAbbr = []string{"dom", "seg", "ter", "qua", "qui", "sex", "sáb"}
	// weekdayNamesPunjabi list the weekday name in the Punjabi.
	weekdayNamesPunjabi = []string{
		"\u0A10\u0A24\u0A35\u0A3E\u0A30",
		"\u0A38\u0A4B\u0A2E\u0A35\u0A3E\u0A30",
		"\u0A2E\u0A70\u0A17\u0A32\u0A35\u0A3E\u0A30",
		"\u0A2C\u0A41\u0A71\u0A27\u0A35\u0A3E\u0A30",
		"\u0A35\u0A40\u0A30\u0A35\u0A3E\u0A30",
		"\u0A38\u0A3C\u0A41\u0A71\u0A15\u0A30\u0A35\u0A3E\u0A30",
		"\u0A38\u0A3C\u0A28\u0A3F\u0A71\u0A1A\u0A30\u0A35\u0A3E\u0A30",
	}
	// weekdayNamesPunjabiAbbr list the weekday name abbreviations in the Punjabi.
	weekdayNamesPunjabiAbbr = []string{
		"\u0A10\u0A24.",
		"\u0A38\u0A4B\u0A2E.",
		"\u0A2E\u0A70\u0A17\u0A32.",
		"\u0A2C\u0A41\u0A71\u0A27.",
		"\u0A35\u0A40\u0A30.",
		"\u0A38\u0A3C\u0A41\u0A15\u0A30.",
		"\u0A38\u0A3C\u0A28\u0A3F\u0A71\u0A1A\u0A30.",
	}
	// weekdayNamesPunjabiArab list the weekday name in the Punjabi Arab.
	weekdayNamesPunjabiArab = []string{
		"\u067E\u064A\u0631",
		"\u0645\u0646\u06AF\u0644",
		"\u0628\u062F\u06BE",
		"\u062C\u0645\u0639\u0631\u0627\u062A",
		"\u062C\u0645\u0639\u0647",
		"\u0647\u0641\u062A\u0647",
		"\u0627\u062A\u0648\u0627\u0631",
	}
	// weekdayNamesQuechua list the weekday name abbreviations in the Quechua.
	weekdayNamesQuechua = []string{"intichaw", "killachaw", "atipachaw", "quyllurchaw", "Ch' askachaw", "Illapachaw", "k'uychichaw"}
	// weekdayNamesQuechuaAbbr list the weekday name abbreviations in the Quechua.
	weekdayNamesQuechuaAbbr = []string{"int", "kil", "ati", "quy", "Ch'", "Ill", "k'u"}
	// weekdayNamesQuechuaEcuador list the weekday name abbreviations in the Quechua Ecuador.
	weekdayNamesQuechuaEcuador = []string{"inti", "awaki", "wanra", "chillay", "kullka", "chaska", "wakma"}
	// weekdayNamesQuechuaEcuadorAbbr list the weekday name abbreviations in the Quechua Ecuador.
	weekdayNamesQuechuaEcuadorAbbr = []string{"int", "awk", "wan", "chy", "kuk", "cha", "wak"}
	// weekdayNamesQuechuaPeru list the weekday name abbreviations in the Quechua Peru.
	weekdayNamesQuechuaPeru = []string{"Domingo", "Lunes", "Martes", "Miércoles", "Jueves", "Viernes", "Sábado"}
	// weekdayNamesQuechuaPeruAbbr list the weekday name abbreviations in the Quechua Peru.
	weekdayNamesQuechuaPeruAbbr = []string{"Dom", "Lun", "Mar", "Mié", "Jue", "Vie", "Sab"}
	// weekdayNamesRomanian list the weekday name abbreviations in the Romanian.
	weekdayNamesRomanian = []string{"duminică", "luni", "marți", "miercuri", "joi", "vineri", "sâmbătă"}
	// weekdayNamesRomanianAbbr list the weekday name abbreviations in the Romanian.
	weekdayNamesRomanianAbbr = []string{"dum.", "lun.", "mar.", "mie.", "joi", "vin.", "sâm."}
	// weekdayNamesRomanianMoldovaAbbr list the weekday name abbreviations in the Romanian Moldova.
	weekdayNamesRomanianMoldovaAbbr = []string{"Du", "Lu", "Mar", "Mie", "Jo", "Vi", "Sâ"}
	// weekdayNamesRomansh list the weekday name abbreviations in the Romansh.
	weekdayNamesRomansh = []string{"dumengia", "glindesdi", "mardi", "mesemna", "gievgia", "venderdi", "sonda"}
	// weekdayNamesRomanshAbbr list the weekday name abbreviations in the Romansh.
	weekdayNamesRomanshAbbr = []string{"du", "gli", "ma", "me", "gie", "ve", "so"}
	// weekdayNamesRussian list the weekday name abbreviations in the Russian.
	weekdayNamesRussian = []string{
		"\u0432\u043E\u0441\u043A\u0440\u0435\u0441\u0435\u043D\u044C\u0435",
		"\u043F\u043E\u043D\u0435\u0434\u0435\u043B\u044C\u043D\u0438\u043A",
		"\u0432\u0442\u043E\u0440\u043D\u0438\u043A",
		"\u0441\u0440\u0435\u0434\u0430",
		"\u0447\u0435\u0442\u0432\u0435\u0440\u0433",
		"\u043F\u044F\u0442\u043D\u0438\u0446\u0430",
		"\u0441\u0443\u0431\u0431\u043E\u0442\u0430",
	}
	// weekdayNamesRussianAbbr list the weekday name abbreviations in the Russian.
	weekdayNamesRussianAbbr = []string{
		"\u0412\u0441",
		"\u041F\u043D",
		"\u0412\u0442",
		"\u0421\u0440",
		"\u0427\u0442",
		"\u041F\u0442",
		"\u0421\u0431",
	}
	// weekdayNamesSakha list the weekday name abbreviations in the Sakha.
	weekdayNamesSakha = []string{
		"\u04E8\u0440\u04E9\u0431\u04AF\u043B",
		"\u044D\u043D\u0438\u0434\u0438\u044D\u043D\u043D\u044C\u0438\u043A",
		"\u041E\u043F\u0442\u0443\u043E\u0440\u0443\u043D\u043D\u044C\u0443\u043A",
		"\u0421\u044D\u0440\u044D\u0434\u044D\u044D",
		"\u0427\u044D\u043F\u043F\u0438\u044D\u0440",
		"\u0411\u044D\u044D\u0442\u0438\u043D\u0441\u044D",
		"\u0421\u0443\u0431\u0443\u043E\u0442\u0430",
	}
	// weekdayNamesSakhaAbbr list the weekday name abbreviations in the Sakha.
	weekdayNamesSakhaAbbr = []string{
		"\u04E8\u0440",
		"\u0431\u043D",
		"\u043E\u043F",
		"\u0441\u044D",
		"\u0447\u043F",
		"\u0431\u044D",
		"\u0441\u0431",
	}
	// weekdayNamesSami list the weekday name abbreviations in the Sami.
	weekdayNamesSami = []string{"pasepeivi", "vuossargâ", "majebargâ", "koskokko", "tuorâstâh", "vástuppeivi", "lávurdâh"}
	// weekdayNamesSamiAbbr list the weekday name abbreviations in the Sami.
	weekdayNamesSamiAbbr = []string{"pas", "vuo", "maj", "kos", "tuo", "vás", "láv"}
	// weekdayNamesSamiSamiLule list the weekday name abbreviations in the Sami (SamiLule).
	weekdayNamesSamiSamiLule = []string{"ájllek", "mánnodahka", "dijstahka", "gasskavahkko", "duorastahka", "bierjjedahka", "lávvodahka"}
	// weekdayNamesSamiSamiLuleAbbr list the weekday name abbreviations in the Sami (SamiLule).
	weekdayNamesSamiSamiLuleAbbr = []string{"ájl", "mán", "dis", "gas", "duor", "bier", "láv"}
	// weekdayNamesSamiSweden list the weekday name abbreviations in the Sami (Lule) Sweden.
	weekdayNamesSamiSweden = []string{"sådnåbiejvve", "mánnodahka", "dijstahka", "gasskavahkko", "duorastahka", "bierjjedahka", "lávvodahka"}
	// weekdayNamesSamiSwedenAbbr list the weekday name abbreviations in the Sami (Lule) Sweden.
	weekdayNamesSamiSwedenAbbr = []string{"såd", "mán", "dis", "gas", "duor", "bier", "láv"}
	// weekdayNamesSamiNorthern list the weekday name abbreviations in the Sami (Northern).
	weekdayNamesSamiNorthern = []string{"sotnabeaivi", "vuossárga", "maŋŋebárga", "gaskavahkku", "duorasdat	", "bearjadat", "lávvardat"}
	// weekdayNamesSamiNorthernFIAbbr list the weekday name abbreviations in the Sami (Northern).
	weekdayNamesSamiNorthernAbbr = []string{"sotn", "vuos", "maŋ", "gask", "duor", "bear", "láv"}
	// weekdayNamesSamiNorthernFI list the weekday name abbreviations in the Sami (Northern) Finland.
	weekdayNamesSamiNorthernFI = []string{"sotnabeaivi", "vuossárga", "maŋŋebárga", "gaskavahkku", "duorastat", "bearjadat", "lávvardat"}
	// weekdayNamesSamiNorthernFIAbbr list the weekday name abbreviations in the Sami (Northern) Finland.
	weekdayNamesSamiNorthernFIAbbr = []string{"so", "má", "di", "ga", "du", "be", "lá"}
	// weekdayNamesSamiNorthernSE list the weekday name abbreviations in the Sami (Northern) Sweden.
	weekdayNamesSamiNorthernSE = []string{"sotnabeaivi", "mánnodat", "disdat", "gaskavahkku", "duorastat", "bearjadat", "lávvardat"}
	// weekdayNamesSamiNorthernSEAbbr list the weekday name abbreviations in the Sami (Northern) Sweden.
	weekdayNamesSamiNorthernSEAbbr = []string{"sotn", "mán", "dis", "gask", "duor", "bear", "láv"}
	// weekdayNamesSamiSkolt list the weekday name abbreviations in the Sami (Skolt).
	weekdayNamesSamiSkolt = []string{"p%E2%B4sspei%B4vv", "vu%F5ssargg", "m%E2%E2ibargg", "se%E4rad", "neljdpei%B4vv", "pi%E2tn%E2c", "sue%B4vet"}
	// weekdayNamesSamiSkoltAbbr list the weekday name abbreviations in the Sami (Skolt).
	weekdayNamesSamiSkoltAbbr = []string{"p%E2", "vu", "m%E2", "se", "ne", "pi", "su"}
	// weekdayNamesSamiSouthern list the weekday name abbreviations in the Sami (Southern).
	weekdayNamesSamiSouthern = []string{"aejlege", "m%E5anta", "d%E6jsta", "gaskev%E5hkoe", "duarsta", "bearjadahke", "laavvardahke"}
	// weekdayNamesSamiSouthernAbbr list the weekday name abbreviations in the Sami (Southern).
	weekdayNamesSamiSouthernAbbr = []string{"aej", "m%E5a", "d%E6j", "gask", "duar", "bearj", "laav"}
	// weekdayNamesSanskrit list the weekday name abbreviations in the Sanskrit.
	weekdayNamesSanskrit = []string{
		"\u0930\u0935\u093F\u0935\u093E\u0938\u0930\u0903",
		"\u0938\u094B\u092E\u0935\u093E\u0938\u0930\u0903",
		"\u092E\u0902\u0917\u0932\u0935\u093E\u0938\u0930\u0903",
		"\u092C\u0941\u0927\u0935\u093E\u0938\u0930\u0903",
		"\u0917\u0941\u0930\u0941\u0935\u093E\u0938\u0930%3A",
		"\u0936\u0941\u0915\u094D\u0930\u0935\u093E\u0938\u0930\u0903",
		"\u0936\u0928\u093F\u0935\u093E\u0938\u0930\u0903",
	}
	// weekdayNamesSanskritAbbr list the weekday name abbreviations in the Sanskrit.
	weekdayNamesSanskritAbbr = []string{
		"\u0930\u0935\u093F",
		"\u0938\u094B\u092E",
		"\u092E\u0919\u094D\u0917",
		"\u092C\u0941\u0927",
		"\u0917\u0941\u0930\u0941",
		"\u0936\u0941\u0915\u094D\u0930",
		"\u0936\u0928\u093F",
	}
	// weekdayNamesGaelic list the weekday name abbreviations in the Gaelic.
	weekdayNamesGaelic = []string{"DiDòmhnaich", "DiLuain", "DiMàirt", "DiCiadain", "DiarDaoin", "DihAoine", "DiSathairne"}
	// weekdayNamesGaelicAbbr list the weekday name abbreviations in the Gaelic
	weekdayNamesGaelicAbbr = []string{"DiD", "DiL", "DiM", "DiC", "Dia", "Dih", "DiS"}
	// weekdayNamesSerbian list the weekday name abbreviations in the Serbian.
	weekdayNamesSerbian = []string{
		"\u043D\u0435\u0434\u0435\u0459\u0430",
		"\u043F\u043E\u043D\u0435\u0434\u0435\u0459\u0430\u043A",
		"\u0443\u0442\u043E\u0440\u0430\u043A",
		"\u0441\u0440\u0435\u0434\u0430",
		"\u0447\u0435\u0442\u0432\u0440\u0442\u0430\u043A",
		"\u043F\u0435\u0442\u0430\u043A",
		"\u0441\u0443\u0431\u043E\u0442\u0430",
	}
	// weekdayNamesSerbianAbbr list the weekday name abbreviations in the Serbian.
	weekdayNamesSerbianAbbr = []string{
		"\u043D\u0435\u0434.",
		"\u043F\u043E\u043D.",
		"\u0443\u0442.",
		"\u0441\u0440.",
		"\u0447\u0435\u0442.",
		"\u043F\u0435\u0442.",
		"\u0441\u0443\u0431.",
	}
	// weekdayNamesSerbianBA list the weekday name abbreviations in the Serbian (Cyrillic) Bosnia and Herzegovina.
	weekdayNamesSerbianBA = []string{
		"\u043D\u0435\u0434\u0458\u0435\u0459\u0430",
		"\u043F\u043E\u043D\u0435\u0434\u0458\u0435\u0459\u0430\u043A",
		"\u0443\u0442\u043E\u0440\u0430\u043A",
		"\u0441\u0440\u0438\u0458\u0435\u0434\u0430",
		"\u0447\u0435\u0442\u0432\u0440\u0442\u0430\u043A",
		"\u043F\u0435\u0442\u0430\u043A",
		"\u0441\u0443\u0431\u043E\u0442\u0430",
	}
	// weekdayNamesSerbianBAAbbr list the weekday name abbreviations in the Serbian (Cyrillic) Bosnia and Herzegovina.
	weekdayNamesSerbianBAAbbr = []string{
		"\u043D\u0435\u0434",
		"\u043F\u043E\u043D",
		"\u0443\u0442\u043E",
		"\u0441\u0440\u0438",
		"\u0447\u0435\u0442",
		"\u043F\u0435\u0442",
		"\u0441\u0443\u0431",
	}
	// weekdayNamesSerbianLatin list the weekday name abbreviations in the Serbian (Latin).
	weekdayNamesSerbianLatin = []string{"nedelja", "ponedeljak", "utorak", "sreda", "četvrtak", "petak", "subota"}
	// weekdayNamesSerbianLatinAbbr list the weekday name abbreviations in the Serbian (Latin).
	weekdayNamesSerbianLatinAbbr = []string{"ned", "pon", "uto", "sre", "čet", "pet", "sub"}
	// weekdayNamesSerbianLatinBA list the weekday name abbreviations in the Serbian (Latin) Bosnia and Herzegovina.
	weekdayNamesSerbianLatinBA = []string{"nedjelja", "ponedjeljak", "utorak", "srijeda", "četvrtak", "petak", "subota"}
	// weekdayNamesSerbianLatinBAAbbr list the weekday name abbreviations in the Serbian (Latin) Bosnia and Herzegovina.
	weekdayNamesSerbianLatinBAAbbr = []string{"ned", "pon", "uto", "sri", "čet", "pet", "sub"}
	// weekdayNamesSerbianLatinCSAbbr list the weekday name abbreviations in the Serbian (Latin) Serbia and Montenegro (Former).
	weekdayNamesSerbianLatinCSAbbr = []string{"ned.", "pon.", "uto.", "sre.", "čet.", "pet.", "sub."}
	// weekdayNamesSerbianLatinME list the weekday name abbreviations in the Serbian (Latin) Montenegro.
	weekdayNamesSerbianLatinME = []string{"nedjelja", "ponedeljak", "utorak", "srijeda", "četvrtak", "petak", "subota"}
	// weekdayNamesSerbianME list the weekday name abbreviations in the Serbian (Cyrillic) Montenegro.
	weekdayNamesSerbianME = []string{
		"\u043D\u0435\u0434\u0435\u0459\u0430",
		"\u043F\u043E\u043D\u0435\u0434\u0458\u0435\u0459\u0430\u043A",
		"\u0443\u0442\u043E\u0440\u0430\u043A",
		"\u0441\u0440\u0438\u0458\u0435\u0434\u0430",
		"\u0447\u0435\u0442\u0432\u0440\u0442\u0430\u043A",
		"\u043F\u0435\u0442\u0430\u043A",
		"\u0441\u0443\u0431\u043E\u0442\u0430",
	}
	// weekdayNamesSesothoSaLeboa list the weekday name abbreviations in the Sesotho sa Leboa.
	weekdayNamesSesothoSaLeboa = []string{"Lamorena", "Musopologo", "Labobedi", "Laboraro", "Labone", "Labohlano", "Mokibelo"}
	// weekdayNamesSesothoSaLeboaAbbr list the weekday name abbreviations in the Sesotho sa Leboa.
	weekdayNamesSesothoSaLeboaAbbr = []string{"Lam", "Moš", "Lbb", "Lbr", "Lbn", "Lbh", "Mok"}
	// weekdayNamesSetswana list the weekday name abbreviations in the Setswana.
	weekdayNamesSetswana = []string{"Sontaga", "Mosopulogo", "Labobedi", "Laboraro", "Labone", "Labotlhano", "Matlhatso"}
	// weekdayNamesSetswanaAbbr list the weekday name abbreviations in the Setswana.
	weekdayNamesSetswanaAbbr = []string{"Sont.", "Mos.", "Lab.", "Labr.", "Labn.", "Labt.", "Matlh."}
	// weekdayNamesSindhi list the weekday name abbreviations in the Sindhi.
	weekdayNamesSindhi = []string{
		"\u0633\u0648\u0645\u0631",
		"\u0627\u06B1\u0627\u0631\u0648",
		"\u0627\u0631\u0628\u0639",
		"\u062E\u0645\u064A\u0633",
		"\u062C\u0645\u0639\u0648",
		"\u0687\u0646\u0687\u0631",
		"\u0622\u0686\u0631",
	}
	// weekdayNamesSindhiAbbr list the weekday name abbreviations in the Sindhi.
	weekdayNamesSindhiAbbr = []string{
		"\u0633\u0648",
		"\u0627\u06B1",
		"\u0627\u0631",
		"\u062E\u0645",
		"\u062C\u0645\u0639\u0648",
		"\u0687\u0646",
		"\u0622\u0686",
	}
	// weekdayNamesSlovak list the weekday name abbreviations in the Slovak.
	weekdayNamesSlovak = []string{"nedeľa", "pondelok", "utorok", "streda", "štvrtok", "piatok", "sobota"}
	// weekdayNamesSlovakAbbr list the weekday name abbreviations in the Slovak.
	weekdayNamesSlovakAbbr = []string{"ne", "po", "ut", "st", "št", "pi", "so"}
	// weekdayNamesSlovenian list the weekday name abbreviations in the Slovenian.
	weekdayNamesSlovenian = []string{"nedelja", "ponedeljek", "torek", "sreda", "četrtek", "petek", "sobota"}
	// weekdayNamesSlovenianAbbr list the weekday name abbreviations in the Slovenian.
	weekdayNamesSlovenianAbbr = []string{"ned.", "pon.", "tor.", "sre.", "čet.", "pet.", "sob."}
	// weekdayNamesSomali list the weekday name abbreviations in the Somali.
	weekdayNamesSomali = []string{"Axad", "Isniin", "Talaado", "Arbaco", "Khamiis", "Jimco", "Sabti"}
	// weekdayNamesSomaliAbbr list the weekday name abbreviations in the Somali.
	weekdayNamesSomaliAbbr = []string{"Axd", "Isn", "Tldo", "Arbc", "Khms", "Jmc", "Sbti"}
	// weekdayNamesSotho list the weekday name abbreviations in the Sotho.
	weekdayNamesSotho = []string{"Sontaha", "Mmantaha", "Labobedi", "Laboraru", "Labone", "Labohlane", "Moqebelo"}
	// weekdayNamesSothoAbbr list the weekday name abbreviations in the Sotho.
	weekdayNamesSothoAbbr = []string{"Son", "Mma", "Bed", "Rar", "Ne", "Hla", "Moq"}
	// weekdayNamesSpanish list the weekday name abbreviations in the Spanish.
	weekdayNamesSpanish = []string{"domingo", "lunes", "martes", "miércoles", "jueves", "viernes", "sábado"}
	// weekdayNamesSpanishAbbr list the weekday name abbreviations in the Spanish Argentina.
	weekdayNamesSpanishAbbr = []string{"do.", "lu.", "ma.", "mi.", "ju.", "vi.", "sá."}
	// weekdayNamesSpanishARAbbr list the weekday name abbreviations in the Spanish Argentina.
	weekdayNamesSpanishARAbbr = []string{"dom.", "lun.", "mar.", "mié.", "jue.", "vie.", "sáb."}
	// weekdayNamesSpanishUSAbbr list the weekday name abbreviations in the Spanish United States.
	weekdayNamesSpanishUSAbbr = []string{"dom", "lun", "mar", "mié", "jue", "vie", "sáb"}
	// weekdayNamesSwedish list the weekday name abbreviations in the Swedish.
	weekdayNamesSwedish = []string{"söndag", "måndag", "tisdag", "onsdag", "torsdag", "fredag", "lördag"}
	// weekdayNamesSwedishAbbr list the weekday name abbreviations in the Swedish Argentina.
	weekdayNamesSwedishAbbr = []string{"sön", "mån", "tis", "ons", "tor", "fre", "lör"}
	// weekdayNamesSyriac list the weekday name abbreviations in the Syriac.
	weekdayNamesSyriac = []string{
		"\u071A\u0715%A0\u0712\u072B\u0712\u0710",
		"\u072C\u072A\u071D\u0722%A0\u0712\u072B\u0712\u0710",
		"\u072C\u0720\u072C\u0710%A0\u0712\u072B\u0712\u0710",
		"\u0710\u072A\u0712\u0725\u0710%A0\u0712\u072B\u0712\u0710",
		"\u071A\u0721\u072B\u0710%A0\u0712\u072B\u0712\u0710",
		"\u0725\u072A\u0718\u0712\u072C\u0710",
		"\u072B\u0712\u072C\u0710",
	}
	// weekdayNamesSyriacAbbr list the weekday name abbreviations in the Syriac.
	weekdayNamesSyriacAbbr = []string{
		"\u070F\u0710%A0\u070F\u0712\u072B",
		"\u070F\u0712%A0\u070F\u0712\u072B",
		"\u070F\u0713%A0\u070F\u0712\u072B",
		"\u070F\u0715%A0\u070F\u0712\u072B",
		"\u070F\u0717%A0\u070F\u0712\u072B",
		"\u070F\u0725\u072A\u0718\u0712",
		"\u070F\u072B\u0712",
	}
	// weekdayNamesTajik list the weekday name abbreviations in the Tajik.
	weekdayNamesTajik = []string{
		"\u042F\u043A\u0448\u0430\u043D\u0431\u0435",
		"\u0434\u0443\u0448\u0430\u043D\u0431\u0435",
		"\u0441\u0435\u0448\u0430\u043D\u0431\u0435",
		"\u0447\u043E\u0440\u0448\u0430\u043D\u0431\u0435",
		"\u043F\u0430\u043D\u04B7\u0448\u0430\u043D\u0431\u0435",
		"\u04B7\u0443\u043C\u044A\u0430",
		"\u0448\u0430\u043D\u0431\u0435",
	}
	// weekdayNamesTajikAbbr list the weekday name abbreviations in the Tajik.
	weekdayNamesTajikAbbr = []string{
		"\u043F\u043A\u0448",
		"\u0434\u0448\u0431",
		"\u0441\u0448\u0431",
		"\u0447\u0448\u0431",
		"\u043F\u0448\u0431",
		"\u04B7\u0443\u043C",
		"\u0448\u043D\u0431",
	}
	// weekdayNamesTamazight list the weekday name abbreviations in the Tamazight.
	weekdayNamesTamazight = []string{"lh'ed", "letnayen", "ttlata", "larebâa", "lexmis", "ldjemâa", "ssebt"}
	// weekdayNamesTamazightAbbr list the weekday name abbreviations in the Tamazight Argentina.
	weekdayNamesTamazightAbbr = []string{"lh'd", "let", "ttl", "lar", "lex", "ldj", "sse"}
	// weekdayNamesTamil list the weekday name abbreviations in the Tamil.
	weekdayNamesTamil = []string{
		"\u0B9E\u0BBE\u0BAF\u0BBF\u0BB1\u0BCD\u0BB1\u0BC1\u0B95\u0BCD\u0B95\u0BBF\u0BB4\u0BAE\u0BC8",
		"\u0BA4\u0BBF\u0B99\u0BCD\u0B95\u0BB3\u0BCD\u0B95\u0BBF\u0BB4\u0BAE\u0BC8",
		"\u0B9A\u0BC6\u0BB5\u0BCD\u0BB5\u0BBE\u0BAF\u0BCD\u0B95\u0BCD\u0B95\u0BBF\u0BB4\u0BAE\u0BC8",
		"\u0BAA\u0BC1\u0BA4\u0BA9\u0BCD\u0B95\u0BBF\u0BB4\u0BAE\u0BC8",
		"\u0BB5\u0BBF\u0BAF\u0BBE\u0BB4\u0B95\u0BCD\u0B95\u0BBF\u0BB4\u0BAE\u0BC8",
		"\u0BB5\u0BC6\u0BB3\u0BCD\u0BB3\u0BBF\u0B95\u0BCD\u0B95\u0BBF\u0BB4\u0BAE\u0BC8",
		"\u0B9A\u0BA9\u0BBF\u0B95\u0BCD\u0B95\u0BBF\u0BB4\u0BAE\u0BC8",
	}
	// weekdayNamesTamilAbbr list the weekday name abbreviations in the Tamil.
	weekdayNamesTamilAbbr = []string{
		"\u0B9E\u0BBE\u0BAF\u0BBF\u0BB1\u0BC1",
		"\u0BA4\u0BBF\u0B99\u0BCD\u0B95\u0BB3\u0BCD",
		"\u0B9A\u0BC6\u0BB5\u0BCD\u0BB5\u0BBE\u0BAF\u0BCD",
		"\u0BAA\u0BC1\u0BA4\u0BA9\u0BCD",
		"\u0BB5\u0BBF\u0BAF\u0BBE\u0BB4\u0BA9\u0BCD",
		"\u0BB5\u0BC6\u0BB3\u0BCD\u0BB3\u0BBF",
		"\u0B9A\u0BA9\u0BBF",
	}
	// weekdayNamesTamilLK list the weekday name abbreviations in the Tamil Sri Lanka.
	weekdayNamesTamilLK = []string{
		"\u0B9E\u0BBE\u0BAF\u0BBF\u0BB1\u0BC1",
		"\u0BA4\u0BBF\u0B99\u0BCD\u0B95\u0BB3\u0BCD",
		"\u0B9A\u0BC6\u0BB5\u0BCD\u0BB5\u0BBE\u0BAF\u0BCD",
		"\u0BAA\u0BC1\u0BA4\u0BA9\u0BCD",
		"\u0BB5\u0BBF\u0BAF\u0BBE\u0BB4\u0BA9\u0BCD",
		"\u0BB5\u0BC6\u0BB3\u0BCD\u0BB3\u0BBF",
		"\u0B9A\u0BA9\u0BBF",
	}
	// weekdayNamesTamilLKAbbr list the weekday name abbreviations in the Tamil Sri Lanka.
	weekdayNamesTamilLKAbbr = []string{
		"\u0B9E\u0BBE\u0BAF\u0BBF.",
		"\u0BA4\u0BBF\u0B99\u0BCD.",
		"\u0B9A\u0BC6\u0BB5\u0BCD.",
		"\u0BAA\u0BC1\u0BA4.",
		"\u0BB5\u0BBF\u0BAF\u0BBE.",
		"\u0BB5\u0BC6\u0BB3\u0BCD.",
		"\u0B9A\u0BA9\u0BBF",
	}
	// weekdayNamesTatar list the weekday name abbreviations in the Tatar.
	weekdayNamesTatar = []string{
		"\u044F\u043A\u0448\u04D9\u043C\u0431\u0435",
		"\u0434\u04AF\u0448\u04D9\u043C\u0431\u0435",
		"\u0441\u0438\u0448\u04D9\u043C\u0431\u0435",
		"\u0447\u04D9\u0440\u0448\u04D9\u043C\u0431\u0435",
		"\u043F\u04D9\u043D\u0497\u0435\u0448\u04D9\u043C\u0431\u0435",
		"\u0497\u043E\u043C\u0433\u0430",
		"\u0448\u0438\u043C\u0431\u04D9",
	}
	// weekdayNamesTatarAbbr list the weekday name abbreviations in the Tatar.
	weekdayNamesTatarAbbr = []string{
		"\u044F\u043A\u0448.",
		"\u0434\u04AF\u0448.",
		"\u0441\u0438\u0448.",
		"\u0447\u04D9\u0440\u0448.",
		"\u043F\u04D9\u043D\u0497.",
		"\u0497\u043E\u043C.",
		"\u0448\u0438\u043C.",
	}
	// weekdayNamesTelugu list the weekday name abbreviations in the Telugu.
	weekdayNamesTelugu = []string{
		"\u0C06\u0C26\u0C3F\u0C35\u0C3E\u0C30\u0C02",
		"\u0C38\u0C4B\u0C2E\u0C35\u0C3E\u0C30\u0C02",
		"\u0C2E\u0C02\u0C17\u0C33\u0C35\u0C3E\u0C30\u0C02",
		"\u0C2C\u0C41\u0C27\u0C35\u0C3E\u0C30\u0C02",
		"\u0C17\u0C41\u0C30\u0C41\u0C35\u0C3E\u0C30\u0C02",
		"\u0C36\u0C41\u0C15\u0C4D\u0C30\u0C35\u0C3E\u0C30\u0C02",
		"\u0C36\u0C28\u0C3F\u0C35\u0C3E\u0C30\u0C02",
	}
	// weekdayNamesTeluguAbbr list the weekday name abbreviations in the Telugu.
	weekdayNamesTeluguAbbr = []string{
		"\u0C06\u0C26\u0C3F",
		"\u0C38\u0C4B\u0C2E",
		"\u0C2E\u0C02\u0C17\u0C33",
		"\u0C2C\u0C41\u0C27",
		"\u0C17\u0C41\u0C30\u0C41",
		"\u0C36\u0C41\u0C15\u0C4D\u0C30",
		"\u0C36\u0C28\u0C3F",
	}
	// weekdayNamesThai list the weekday name abbreviations in the Thai.
	weekdayNamesThai = []string{
		"\u0E2D\u0E32\u0E17\u0E34\u0E15\u0E22\u0E4C",
		"\u0E08\u0E31\u0E19\u0E17\u0E23\u0E4C",
		"\u0E2D\u0E31\u0E07\u0E04\u0E32\u0E23",
		"\u0E1E\u0E38\u0E18",
		"\u0E1E\u0E24\u0E2B\u0E31\u0E2A\u0E1A\u0E14\u0E35",
		"\u0E28\u0E38\u0E01\u0E23\u0E4C",
		"\u0E40\u0E2A\u0E32\u0E23\u0E4C",
	}
	// weekdayNamesThaiAbbr list the weekday name abbreviations in the Thai.
	weekdayNamesThaiAbbr = []string{
		"\u0E2D\u0E32.",
		"\u0E08.",
		"\u0E2D.",
		"\u0E1E.",
		"\u0E1E\u0E24.",
		"\u0E28.",
		"\u0E2A.",
	}
	// weekdayNamesTibetan list the weekday name abbreviations in the Tibetan.
	weekdayNamesTibetan = []string{
		"\u0F42\u0F5F\u0F60\u0F0B\u0F49\u0F72\u0F0B\u0F58\u0F0D",
		"\u0F42\u0F5F\u0F60\u0F0B\u0F5F\u0FB3\u0F0B\u0F56\u0F0D",
		"\u0F42\u0F5F\u0F60\u0F0B\u0F58\u0F72\u0F42\u0F0B\u0F51\u0F58\u0F62\u0F0D",
		"\u0F42\u0F5F\u0F60\u0F0B\u0F63\u0FB7\u0F42\u0F0B\u0F54\u0F0D",
		"\u0F42\u0F5F\u0F60\u0F0B\u0F55\u0F74\u0F62\u0F0B\u0F56\u0F74\u0F0D",
		"\u0F42\u0F5F\u0F60\u0F0B\u0F54\u0F0B\u0F66\u0F44\u0F66\u0F0D",
		"\u0F42\u0F5F\u0F60\u0F0B\u0F66\u0FA4\u0F7A\u0F53\u0F0B\u0F54\u0F0D",
	}
	// weekdayNamesTibetanAbbr list the weekday name abbreviations in the Tibetan.
	weekdayNamesTibetanAbbr = []string{
		"\u0F49\u0F72\u0F0B\u0F58\u0F0D",
		"\u0F5F\u0FB3\u0F0B\u0F56\u0F0D",
		"\u0F58\u0F72\u0F42\u0F0B\u0F51\u0F58\u0F62\u0F0D",
		"\u0F63\u0FB7\u0F42\u0F0B\u0F54\u0F0D",
		"\u0F55\u0F74\u0F62\u0F0B\u0F56\u0F74\u0F0D",
		"\u0F54\u0F0B\u0F66\u0F44\u0F66\u0F0D",
		"\u0F66\u0FA4\u0F7A\u0F53\u0F0B\u0F54\u0F0D",
	}
	// weekdayNamesTigrinya list the weekday name abbreviations in the Tigrinya.
	weekdayNamesTigrinya = []string{
		"\u1230\u1295\u1260\u1275",
		"\u1230\u1291\u12ED",
		"\u1220\u1209\u1235",
		"\u1228\u1261\u12D5",
		"\u1283\u1219\u1235",
		"\u12D3\u122D\u1262",
		"\u1240\u12F3\u121D",
	}
	// weekdayNamesTigrinyaAbbr list the weekday name abbreviations in the Tigrinya.
	weekdayNamesTigrinyaAbbr = []string{
		"\u1230\u1295",
		"\u1230\u1291",
		"\u1230\u1209",
		"\u1228\u1261",
		"\u1213\u1219",
		"\u12D3\u122D",
		"\u1240\u12F3",
	}
	// weekdayNamesTsonga list the weekday name abbreviations in the Tsonga.
	weekdayNamesTsonga = []string{"Sonta", "Musumbhunuku", "Ravumbirhi", "Ravunharhu", "Ravumune", "Ravuntlhanu", "Mugqivela"}
	// weekdayNamesTsongaAbbr list the weekday name abbreviations in the Tsonga.
	weekdayNamesTsongaAbbr = []string{"Son", "Mus", "Bir", "Har", "Ne", "Tlh", "Mug"}
	// weekdayNamesTurkish list the weekday name abbreviations in the Turkish.
	weekdayNamesTurkish = []string{"Pazar", "Pazartesi", "Salı", "Çarşamba", "Perşembe", "Cuma", "Cumartesi"}
	// weekdayNamesTurkishAbbr list the weekday name abbreviations in the Turkish.
	weekdayNamesTurkishAbbr = []string{"Paz", "Pzt", "Sal", "Çar", "Per", "Cum", "Cmt"}
	// weekdayNamesTurkmen list the weekday name abbreviations in the Turkmen.
	weekdayNamesTurkmen = []string{"Ýekşenbe", "Duşenbe", "Sişenbe", "Çarşenbe", "Penşenbe", "Anna", "Şenbe"}
	// weekdayNamesTurkmenAbbr list the weekday name abbreviations in the Turkmen.
	weekdayNamesTurkmenAbbr = []string{"Ýb", "Db", "Sb", "Çb", "Pb", "An", "Şb"}
	// weekdayNamesUkrainian list the weekday name abbreviations in the Ukrainian.
	weekdayNamesUkrainian = []string{
		"\u043D\u0435\u0434\u0456\u043B\u044F",
		"\u043F\u043E\u043D\u0435\u0434\u0456\u043B\u043E\u043A",
		"\u0432\u0456\u0432\u0442\u043E\u0440\u043E\u043A",
		"\u0441\u0435\u0440\u0435\u0434\u0430",
		"\u0447\u0435\u0442\u0432\u0435\u0440",
		"\u043F%27\u044F\u0442\u043D\u0438\u0446\u044F",
		"\u0441\u0443\u0431\u043E\u0442\u0430",
	}
	// weekdayNamesUkrainianAbbr list the weekday name abbreviations in the Ukrainian.
	weekdayNamesUkrainianAbbr = []string{
		"\u041D\u0434",
		"\u041F\u043D",
		"\u0412\u0442",
		"\u0421\u0440",
		"\u0427\u0442",
		"\u041F\u0442",
		"\u0421\u0431",
	}
	// weekdayNamesSorbian list the weekday name abbreviations in the Sorbian.
	weekdayNamesSorbian = []string{"njedźela", "póndźela", "wutora", "srjeda", "štwórtk", "pjatk", "sobota"}
	// weekdayNamesSorbianAbbr list the weekday name abbreviations in the Sorbian.
	weekdayNamesSorbianAbbr = []string{"nje", "pón", "wut", "srj", "štw", "pja", "sob"}
	// weekdayNamesUrdu list the weekday name abbreviations in the Urdu.
	weekdayNamesUrdu = []string{
		"\u0627\u062A\u0648\u0627\u0631",
		"\u067E\u064A\u0631",
		"\u0645\u0646\u06AF\u0644",
		"\u0628\u062F\u06BE",
		"\u062C\u0645\u0639\u0631\u0627\u062A",
		"\u062C\u0645\u0639\u0647",
		"\u0647\u0641\u062A\u0647",
	}
	// weekdayNamesUrduIN list the weekday name abbreviations in the Urdu India.
	weekdayNamesUrduIN = []string{
		"\u0627\u062A\u0648\u0627\u0631",
		"\u067E\u06CC\u0631",
		"\u0645\u0646\u06AF\u0644",
		"\u0628\u062F\u06BE",
		"\u062C\u0645\u0639\u0631\u0627\u062A",
		"\u062C\u0645\u0639\u06C1",
		"\u06C1\u0641\u062A\u06C1",
	}
	// weekdayNamesUyghur list the weekday name abbreviations in the Uyghur.
	weekdayNamesUyghur = []string{
		"\u064A\u06D5\u0643\u0634\u06D5\u0646\u0628\u06D5",
		"\u062F\u06C8\u0634\u06D5\u0646\u0628\u06D5",
		"\u0633\u06D5\u064A\u0634\u06D5\u0646\u0628\u06D5",
		"\u0686\u0627\u0631\u0634\u06D5\u0646\u0628\u06D5",
		"\u067E\u06D5\u064A\u0634\u06D5\u0646\u0628\u06D5",
		"\u062C\u06C8\u0645\u06D5",
		"\u0634\u06D5\u0646\u0628\u06D5",
	}
	// weekdayNamesUyghurAbbr list the weekday name abbreviations in the Uyghur.
	weekdayNamesUyghurAbbr = []string{
		"\u064A\u06D5",
		"\u062F\u06C8",
		"\u0633\u06D5",
		"\u0686\u0627",
		"\u067E\u06D5",
		"\u062C\u06C8",
		"\u0634\u06D5",
	}
	// weekdayNamesUzbekCyrillic list the weekday name abbreviations in the Uzbek Cyrillic.
	weekdayNamesUzbekCyrillic = []string{
		"\u044F\u043A\u0448\u0430\u043D\u0431\u0430",
		"\u0434\u0443\u0448\u0430\u043D\u0431\u0430",
		"\u0441\u0435\u0448\u0430\u043D\u0431\u0430",
		"\u0447\u043E\u0440\u0448\u0430\u043D\u0431\u0430",
		"\u043F\u0430\u0439\u0448\u0430\u043D\u0431\u0430",
		"\u0436\u0443\u043C\u0430",
		"\u0448\u0430\u043D\u0431\u0430",
	}
	// weekdayNamesUzbekCyrillicAbbr list the weekday name abbreviations in the Uzbek Cyrillic.
	weekdayNamesUzbekCyrillicAbbr = []string{
		"\u044F\u043A\u0448",
		"\u0434\u0443\u0448",
		"\u0441\u0435\u0448",
		"\u0447\u043E\u0440",
		"\u043F\u0430\u0439",
		"\u0436\u0443\u043C",
		"\u0448\u0430\u043D",
	}
	// weekdayNamesUzbek list the weekday name abbreviations in the Uzbek.
	weekdayNamesUzbek = []string{"yakshanba", "dushanba", "seshanba", "chorshanba", "payshanba", "juma", "shanba"}
	// weekdayNamesUzbekAbbr list the weekday name abbreviations in the Uzbek.
	weekdayNamesUzbekAbbr = []string{"Yak", "Dush", "Sesh", "Chor", "Pay", "Jum", "Shan"}
	// weekdayNamesValencian list the weekday name abbreviations in the Valencian.
	weekdayNamesValencian = []string{"diumenge", "dilluns", "dimarts", "dimecres", "dijous", "divendres", "dissabte"}
	// weekdayNamesValencianAbbr list the weekday name abbreviations in the Valencian.
	weekdayNamesValencianAbbr = []string{"dg.", "dl.", "dt.", "dc.", "dj.", "dv.", "ds."}
	// weekdayNamesVenda list the weekday name abbreviations in the Venda.
	weekdayNamesVenda = []string{"Swondaha", "Musumbuluwo", "Ḽavhuvhili", "Ḽavhuraru", "Ḽavhuṋa", "Ḽavhuṱanu", "Mugivhela"}
	// weekdayNamesVendaAbbr list the weekday name abbreviations in the Venda.
	weekdayNamesVendaAbbr = []string{"Swo", "Mus", "Vhi", "Rar", "Ṋa", "Ṱan", "Mug"}
	// weekdayNamesVietnamese list the weekday name abbreviations in the Vietnamese.
	weekdayNamesVietnamese = []string{"Ch\u1EE7%20Nh\u1EADt", "Th\u1EE9%20Hai", "Th\u1EE9%20Ba", "Th\u1EE9%20T\u01B0", "Th\u1EE9%20N\u0103m", "Th\u1EE9%20S%E1u", "Th\u1EE9%20B\u1EA3y"}
	// weekdayNamesVietnameseAbbr list the weekday name abbreviations in the Vietnamese.
	weekdayNamesVietnameseAbbr = []string{"CN", "T2", "T3", "T4", "T5", "T6", "T7"}
	// weekdayNamesWelsh list the weekday name abbreviations in the Welsh.
	weekdayNamesWelsh = []string{"Dydd Sul", "Dydd Llun", "Dydd Mawrth", "Dydd Mercher", "Dydd Iau", "Dydd Gwener", "Dydd Sadwrn"}
	// weekdayNamesWelshAbbr list the weekday name abbreviations in the Welsh.
	weekdayNamesWelshAbbr = []string{"Sul", "Llun", "Maw", "Mer", "Iau", "Gwe", "Sad"}
	// weekdayNamesWolof list the weekday name abbreviations in the Wolof.
	weekdayNamesWolof = []string{"Dib%E9er", "Altine", "Talaata", "%C0llarba", "Alxames", "%C0jjuma", "Gaawu"}
	// weekdayNamesWolofAbbr list the weekday name abbreviations in the Wolof.
	weekdayNamesWolofAbbr = []string{"Dib.", "Alt.", "Tal.", "%C0ll.", "Alx.", "%C0jj.", "Gaa."}
	// weekdayNamesXhosa list the weekday name abbreviations in the Xhosa.
	weekdayNamesXhosa = []string{"Cawe", "Mvulo", "Lwesibini", "Lwesithathu", "Lwesine", "Lwesihlanu", "Mgqibelo"}
	// weekdayNamesXhosaAbbr list the weekday name abbreviations in the Xhosa.
	weekdayNamesXhosaAbbr = []string{"iCa.", "uMv.", "uLwesib.", "uLwesith.", "uLwesin.", "uLwesihl.", "uMgq."}
	// weekdayNamesYi list the weekday name abbreviations in the Yi.
	weekdayNamesYi = []string{
		"\uA46D\uA18F\uA44D",
		"\uA18F\uA282\uA494",
		"\uA18F\uA282\uA44D",
		"\uA18F\uA282\uA315",
		"\uA18F\uA282\uA1D6",
		"\uA18F\uA282\uA26C",
		"\uA18F\uA282\uA0D8",
	}
	// weekdayNamesYiAbbr list the weekday name abbreviations in the Yi.
	weekdayNamesYiAbbr = []string{
		"\uA46D\uA18F",
		"\uA18F\uA494",
		"\uA18F\uA44D",
		"\uA18F\uA315",
		"\uA18F\uA1D6",
		"\uA18F\uA26C",
		"\uA18F\uA0D8",
	}
	// weekdayNamesYiddish list the weekday name abbreviations in the Yiddish.
	weekdayNamesYiddish = []string{
		"\u05D6\u05D5\u05E0\u05D8\u05D9\u05E7",
		"\u05DE\u05D0\u05B8\u05E0\u05D8\u05D9\u05E7",
		"\u05D3\u05D9\u05E0\u05E1\u05D8\u05D9\u05E7",
		"\u05DE\u05D9\u05D8\u05D5\u05D5\u05D0\u05DA",
		"\u05D3\u05D0\u05E0\u05E2\u05E8\u05E9\u05D8\u05D9\u05E7",
		"\u05E4\u05BF\u05E8\u05F2\u05B7\u05D8\u05D9\u05E7",
		"\u05E9\u05D1\u05EA",
	}
	// weekdayNamesYiddishAbbr list the weekday name abbreviations in the Yiddish.
	weekdayNamesYiddishAbbr = []string{
		"\u05D9\u05D5\u05DD%A0\u05D0",
		"\u05D9\u05D5\u05DD%A0\u05D1",
		"\u05D9\u05D5\u05DD%A0\u05D2",
		"\u05D9\u05D5\u05DD%A0\u05D3",
		"\u05D9\u05D5\u05DD%A0\u05D4",
		"\u05D9\u05D5\u05DD%A0\u05D5",
		"\u05E9\u05D1\u05EA",
	}
	// weekdayNamesYoruba list the weekday name abbreviations in the Yoruba.
	weekdayNamesYoruba = []string{
		"\u1ECCj\u1ECD\u0301%20%C0%ECk%FA",
		"\u1ECCj\u1ECD\u0301%20Aj%E9",
		"\u1ECCj\u1ECD\u0301%20%CCs\u1EB9\u0301gun",
		"\u1ECCj\u1ECD\u0301r%FA",
		"\u1ECCj\u1ECD\u0301b\u1ECD",
		"\u1ECCj\u1ECD\u0301%20\u1EB8t%EC",
		"\u1ECCj\u1ECD\u0301%20%C0b%E1m\u1EB9\u0301ta",
	}
	// weekdayNamesYorubaAbbr list the weekday name abbreviations in the Yoruba.
	weekdayNamesYorubaAbbr = []string{"%C0%ECk", "Aj", "%CC\u1E63g", "\u1ECCjr", "\u1ECCjb", "\u1EB8t", "%C0b%E1"}
	// weekdayNamesZulu list the weekday name abbreviations in the Zulu.
	weekdayNamesZulu = []string{"ISonto", "UMsombuluko", "ULwesibili", "ULwesithathu", "ULwesine", "ULwesihlanu", "UMgqibelo"}
	// weekdayNamesZuluAbbr list the weekday name abbreviations in the Zulu.
	weekdayNamesZuluAbbr = []string{"Son.", "Mso.", "Bi.", "Tha.", "Ne.", "Hla.", "Mgq."}
	// apFmtAfrikaans defined the AM/PM name in the Afrikaans.
	apFmtAfrikaans = "vm./nm."
	// apFmtAlbanian defined the AM/PM name in the Albanian.
	apFmtAlbanian = "p.d./m.d."
	// apFmtAlsatian defined the AM/PM name in the Alsatian.
	apFmtAlsatian = "vorm./nam."
	// apFmtAmharic defined the AM/PM name in the Amharic.
	apFmtAmharic = "\u1325\u12CB\u1275/\u12A8\u1230\u12D3\u1275"
	// apFmtArabic defined the AM/PM name in the Arabic.
	apFmtArabic = "\u0635/\u0645"
	// apFmtAssamese defined the AM/PM name in the Assamese.
	apFmtAssamese = "\u09F0\u09BE\u09A4\u09BF\u09AA\u09C1/\u0986\u09AC\u09C7\u09B2\u09BF"
	// apFmtBreton defined the AM/PM name in the Assamese.
	apFmtBreton = "A.M./G.M."
	// apFmtBurmese defined the AM/PM name in the Assamese.
	apFmtBurmese = "\u1014\u1036\u1014\u1000\u103A/\u100A\u1014\u1031"
	// apFmtCameroon defined the AM/PM name in the Cameroon.
	apFmtCameroon = "mat./soir"
	// apFmtCentralKurdish defined the AM/PM name in the Central Kurdish.
	apFmtCentralKurdish = "\u067E.\u0646/\u062F.\u0646"
	// apFmtCuba defined the AM/PM name in the Cuba.
	apFmtCuba = "a.m./p.m."
	// apFmtFaroese defined the AM/PM name in the Faroese.
	apFmtFaroese = "um fyr./um sein."
	// apFmtFinnish defined the AM/PM name in the Finnish.
	apFmtFinnish = "ap./ip."
	// apFmtGreek defined the AM/PM name in the Greek.
	apFmtGreek = "\u03C0\u03BC/\u03BC\u03BC"
	// apFmtGujarati defined the AM/PM name in the Gujarati.
	apFmtGujarati = "\u0AAA\u0AC2\u0AB0\u0ACD\u0AB5 \u0AAE\u0AA7\u0ACD\u0AAF\u0ABE\u0AB9\u0ACD\u0AA8/\u0A89\u0AA4\u0ACD\u0AA4\u0AB0 \u0AAE\u0AA7\u0ACD\u0AAF\u0ABE\u0AB9\u0ACD\u0AA8"
	// apFmtHindi defined the AM/PM name in the Hindi.
	apFmtHindi = "\u092A\u0942\u0930\u094D\u0935\u093E\u0939\u094D\u0928/\u0905\u092A\u0930\u093E\u0939\u094D\u0928"
	// apFmtHungarian defined the AM/PM name in the Hungarian.
	apFmtHungarian = "de./du."
	// apFmtIcelandic defined the AM/PM name in the Icelandic.
	apFmtIcelandic = "f.h./e.h."
	// apFmtIgbo defined the AM/PM name in the Igbo.
	apFmtIgbo = "A.M./P.M."
	// apFmtIrish defined the AM/PM name in the Irish.
	apFmtIrish = "r.n./i.n."
	// apFmtJapanese defined the AM/PM name in the Japanese.
	apFmtJapanese = "午前/午後"
	// apFmtKannada defined the AM/PM name in the Kannada.
	apFmtKannada = "\u0CAA\u0CC2\u0CB0\u0CCD\u0CB5\u0CBE\u0CB9\u0CCD\u0CA8/\u0C85\u0CAA\u0CB0\u0CBE\u0CB9\u0CCD\u0CA8"
	// apFmtKhmer defined the AM/PM name in the Khmer.
	apFmtKhmer = "\u1796\u17D2\u179A\u17B9\u1780/\u179B\u17D2\u1784\u17B6\u1785"
	// apFmtKonkani defined the AM/PM name in the Konkani.
	apFmtKonkani = "\u092E.\u092A\u0942./\u092E.\u0928\u0902."
	// apFmtKorean defined the AM/PM name in the Korean.
	apFmtKorean = "오전/오후"
	// apFmtKyrgyz defined the AM/PM name in the Kyrgyz.
	apFmtKyrgyz = "\u0442\u04A3/\u0442\u043A"
	// apFmtLao defined the AM/PM name in the Lao.
	apFmtLao = "\u0E81\u0EC8\u0EAD\u0E99\u0E97\u0EC8\u0EBD\u0E87/\u0EAB\u0EBC\u0EB1\u0E87\u0E97\u0EC8\u0EBD\u0E87"
	// apFmtLatvian defined the AM/PM name in the Latvian.
	apFmtLatvian = "priekšp./pēcp."
	// apFmtLithuanian defined the AM/PM name in the Lithuanian.
	apFmtLithuanian = "priešpiet/popiet"
	// apFmtMacedonian defined the AM/PM name in the Macedonian.
	apFmtMacedonian = "\u043F\u0440\u0435\u0442\u043F\u043B./\u043F\u043E\u043F\u043B."
	// apFmtMalay defined the AM/PM name in the Malay.
	apFmtMalay = "PG/PTG"
	// apFmtMongolian defined the AM/PM name in the Mongolian.
	apFmtMongolian = "\u04AF.\u04E9./\u04AF.\u0445."
	// apFmtNigeria defined the AM/PM name in the Nigeria.
	apFmtNigeria = "subaka/kikiiɗe"
	// apFmtNorwegian defined the AM/PM name in the Norwegian.
	apFmtNorwegian = "f.m./e.m."
	// apFmtOromo defined the AM/PM name in the Oromo.
	apFmtOromo = "WD/WB"
	// apFmtPashto defined the AM/PM name in the Pashto.
	apFmtPashto = "\u063A.\u0645./\u063A.\u0648."
	// apFmtPersian defined the AM/PM name in the Persian.
	apFmtPersian = "\u0642.\u0638/\u0628.\u0638"
	// apFmtPunjabi defined the AM/PM name in the Punjabi.
	apFmtPunjabi = "\u0A38\u0A35\u0A47\u0A30/\u0A38\u0A3C\u0A3E\u0A2E"
	// apFmtSakha defined the AM/PM name in the Sakha.
	apFmtSakha = "\u041A\u0418/\u041A\u041A"
	// apFmtSamiNorthern defined the AM/PM name in the Sami (Northern).
	apFmtSamiNorthern = "i.b./e.b."
	// apFmtSanskrit defined the AM/PM name in the Sanskrit.
	apFmtSanskrit = "\u092E\u0927\u094D\u092F\u093E\u0928\u092A\u0942\u0930\u094D\u0935/\u092E\u0927\u094D\u092F\u093E\u0928\u092A\u091A\u094D\u092F\u093E\u0924"
	// apFmtScottishGaelic defined the AM/PM name in the Scottish Gaelic.
	apFmtScottishGaelic = "m/f"
	// apFmtSerbianLatin defined the AM/PM name in the Serbian (Latin).
	apFmtSerbianLatin = "pre podne/po podne"
	// apFmtSerbianLatinBA defined the AM/PM name in the Serbian (Latin) Bosnia
	// and Herzegovina.
	apFmtSerbianLatinBA = "prije podne/po podne"
	// apFmtSinhala defined the AM/PM name in the Sinhala.
	apFmtSinhala = "\u0DB4\u0DD9.\u0DC0./\u0DB4.\u0DC0."
	// apFmtSlovenian defined the AM/PM name in the Slovenian.
	apFmtSlovenian = "dop./pop."
	// apFmtSomali defined the AM/PM name in the Somali.
	apFmtSomali = "GH/GD"
	// apFmtSpanish defined the AM/PM name in the Spanish.
	apFmtSpanish = "a. m./p. m."
	// apFmtSpanishAR defined the AM/PM name in the Spanish Argentina.
	apFmtSpanishAR = "a.%A0m./p.%A0m."
	// apFmtSwedish defined the AM/PM name in the Swedish.
	apFmtSwedish = "fm/em"
	// apFmtSyriac defined the AM/PM name in the Syriac.
	apFmtSyriac = "\u0729.\u071B/\u0712.\u071B"
	// apFmtTamil defined the AM/PM name in the Tamil.
	apFmtTamil = "\u0B95\u0BBE\u0BB2\u0BC8/\u0BAE\u0BBE\u0BB2\u0BC8"
	// apFmtTibetan defined the AM/PM name in the Tibetan.
	apFmtTibetan = "\u0f66\u0f94\u0f0b\u0f51\u0fb2\u0f7c\u0f0b/\u0f55\u0fb1\u0f72\u0f0b\u0f51\u0fb2\u0f7c\u0f0b"
	// apFmtTigrinya defined the AM/PM name in the Tigrinya.
	apFmtTigrinya = "\u1295\u1309\u1206/\u12F5\u1215\u122A%20\u1250\u1275\u122A"
	// apFmtTigrinyaER defined the AM/PM name in the Tigrinya Eritrea.
	apFmtTigrinyaER = "\u1295\u1309\u1206%20\u1230\u12D3\u1270/\u12F5\u1215\u122D%20\u1230\u12D3\u1275"
	// apFmtTurkish defined the AM/PM name in the Turkish.
	apFmtTurkish = "\u00F6\u00F6/\u00F6\u0053"
	// apFmtUpperSorbian defined the AM/PM name in the Upper Sorbian.
	apFmtUpperSorbian = "dopołdnja/popołdnju"
	// apFmtUrdu defined the AM/PM name in the Urdu.
	apFmtUrdu = "\u062F\u0646/\u0631\u0627\u062A"
	// apFmtUyghur defined the AM/PM name in the Uyghur.
	apFmtUyghur = "\u0686\u06C8\u0634\u062A\u0649\u0646%20\u0628\u06C7\u0631\u06C7\u0646/\u0686\u06C8\u0634\u062A\u0649\u0646%20\u0643\u06D0\u064A\u0649\u0646"
	// apFmtUzbek defined the AM/PM name in the Uzbek.
	apFmtUzbek = "TO/TK"
	// apFmtUzbekCyrillic defined the AM/PM name in the Uzbek Cyrillic.
	apFmtUzbekCyrillic = "\u0422\u041E/\u0422\u041A"
	// apFmtVietnamese defined the AM/PM name in the Vietnamese.
	apFmtVietnamese = "SA/CH"
	// apFmtWelsh defined the AM/PM name in the Welsh.
	apFmtWelsh = "yb/yh"
	// apFmtWolof defined the AM/PM name in the Wolof.
	apFmtWolof = "Sub/Ngo"
	// apFmtYi defined the AM/PM name in the Yi.
	apFmtYi = "\ua3b8\ua111/\ua06f\ua2d2"
	// apFmtYiddish defined the AM/PM name in the Yiddish.
	apFmtYiddish = "\u05E4\u05BF\u05D0\u05B7\u05E8\u05DE\u05D9\u05D8\u05D0\u05B8\u05D2/\u05E0\u05D0\u05B8\u05DB\u05DE\u05D9\u05D8\u05D0\u05B8\u05D2"
	// apFmtYoruba defined the AM/PM name in the Yoruba.
	apFmtYoruba = "%C0%E1r\u1ECD\u0300/\u1ECC\u0300s%E1n"
	// switchArgumentFunc defined the switch argument printer function.
	switchArgumentFunc = map[string]func(s string) string{
		"[DBNum1]": func(s string) string {
			r := strings.NewReplacer(
				"0", "\u25cb", "1", "\u4e00", "2", "\u4e8c", "3", "\u4e09", "4", "\u56db",
				"5", "\u4e94", "6", "\u516d", "7", "\u4e03", "8", "\u516b", "9", "\u4e5d",
			)
			return r.Replace(s)
		},
		"[DBNum2]": func(s string) string {
			r := strings.NewReplacer(
				"0", "\u96f6", "1", "\u58f9", "2", "\u8d30", "3", "\u53c1", "4", "\u8086",
				"5", "\u4f0d", "6", "\u9646", "7", "\u67d2", "8", "\u634c", "9", "\u7396",
			)
			return r.Replace(s)
		},
		"[DBNum3]": func(s string) string {
			r := strings.NewReplacer(
				"0", "\uff10", "1", "\uff11", "2", "\uff12", "3", "\uff13", "4", "\uff14",
				"5", "\uff15", "6", "\uff16", "7", "\uff17", "8", "\uff18", "9", "\uff19",
			)
			return r.Replace(s)
		},
	}
	// langNumFmtFunc defines functions to apply language number format code.
	langNumFmtFunc = map[CultureName]func(f *File, numFmtID int) string{
		CultureNameEnUS: func(f *File, numFmtID int) string {
			return f.langNumFmtFuncEnUS(numFmtID)
		},
		CultureNameJaJP: func(f *File, numFmtID int) string {
			return f.langNumFmtFuncJaJP(numFmtID)
		},
		CultureNameKoKR: func(f *File, numFmtID int) string {
			return f.langNumFmtFuncKoKR(numFmtID)
		},
		CultureNameZhCN: func(f *File, numFmtID int) string {
			return f.langNumFmtFuncZhCN(numFmtID)
		},
		CultureNameZhTW: func(f *File, numFmtID int) string {
			return f.langNumFmtFuncZhTW(numFmtID)
		},
	}
)

// getSupportedLanguageInfo returns language information by giving language code.
// This function does not support different calendar type of the language
// currently. For example: the hexadecimal language code 3010429 (fa-IR,301)
// will be convert to 0429 (fa-IR).
func getSupportedLanguageInfo(lang string) (languageInfo, bool) {
	hex := lang
	if len(hex) > 4 {
		hex = hex[len(hex)-4:]
	}
	n := new(big.Int)
	n.SetString(hex, 16)
	if info, ok := supportedLanguageInfo[int(n.Int64())]; ok {
		return info, ok
	}
	if info, ok := supportedLanguageCodeInfo[lang]; ok {
		return info, ok
	}
	for _, info := range supportedLanguageInfo {
		if inStrSlice(info.tags, lang, false) != -1 {
			return info, true
		}
	}
	return languageInfo{}, false
}

// applyBuiltInNumFmt provides a function to returns a value after formatted
// with built-in number format code, or specified sort date format code.
func (f *File) applyBuiltInNumFmt(c *xlsxC, fmtCode string, numFmtID int, date1904 bool, cellType CellType) string {
	if f.options != nil && f.options.ShortDatePattern != "" {
		if numFmtID == 14 {
			fmtCode = f.options.ShortDatePattern
		}
		if numFmtID == 22 {
			fmtCode = fmt.Sprintf("%s hh:mm", f.options.ShortDatePattern)
		}
	}
	return format(c.V, fmtCode, date1904, cellType, f.options)
}

// langNumFmtFuncEnUS returns number format code by given date and time pattern
// for country code en-us.
func (f *File) langNumFmtFuncEnUS(numFmtID int) string {
	shortDatePattern, longTimePattern := "M/d/yy", "h:mm:ss"
	if f.options.ShortDatePattern != "" {
		shortDatePattern = f.options.ShortDatePattern
	}
	if f.options.LongTimePattern != "" {
		longTimePattern = f.options.LongTimePattern
	}
	if 32 <= numFmtID && numFmtID <= 35 {
		return longTimePattern
	}
	if (27 <= numFmtID && numFmtID <= 31) || (50 <= numFmtID && numFmtID <= 58) {
		return shortDatePattern
	}
	return ""
}

// langNumFmtFuncJaJP returns number format code by given date and time pattern
// for country code ja-jp.
func (f *File) langNumFmtFuncJaJP(numFmtID int) string {
	if numFmtID == 30 && f.options.ShortDatePattern != "" {
		return f.options.ShortDatePattern
	}
	if (32 <= numFmtID && numFmtID <= 33) && f.options.LongTimePattern != "" {
		return f.options.LongTimePattern
	}
	return langNumFmt["ja-jp"][numFmtID]
}

// langNumFmtFuncKoKR returns number format code by given date and time pattern
// for country code ko-kr.
func (f *File) langNumFmtFuncKoKR(numFmtID int) string {
	if numFmtID == 30 && f.options.ShortDatePattern != "" {
		return f.options.ShortDatePattern
	}
	if (32 <= numFmtID && numFmtID <= 33) && f.options.LongTimePattern != "" {
		return f.options.LongTimePattern
	}
	return langNumFmt["ko-kr"][numFmtID]
}

// langNumFmtFuncZhCN returns number format code by given date and time pattern
// for country code zh-cn.
func (f *File) langNumFmtFuncZhCN(numFmtID int) string {
	if numFmtID == 30 && f.options.ShortDatePattern != "" {
		return f.options.ShortDatePattern
	}
	if (32 <= numFmtID && numFmtID <= 33) && f.options.LongTimePattern != "" {
		return f.options.LongTimePattern
	}
	return langNumFmt["zh-cn"][numFmtID]
}

// langNumFmtFuncZhTW returns number format code by given date and time pattern
// for country code zh-tw.
func (f *File) langNumFmtFuncZhTW(numFmtID int) string {
	if numFmtID == 30 && f.options.ShortDatePattern != "" {
		return f.options.ShortDatePattern
	}
	if (32 <= numFmtID && numFmtID <= 33) && f.options.LongTimePattern != "" {
		return f.options.LongTimePattern
	}
	return langNumFmt["zh-tw"][numFmtID]
}

// checkDateTimePattern check and validate date and time options field value.
func (f *File) checkDateTimePattern() error {
	for _, pattern := range []string{f.options.LongDatePattern, f.options.LongTimePattern, f.options.ShortDatePattern} {
		p := nfp.NumberFormatParser()
		for _, section := range p.Parse(pattern) {
			for _, token := range section.Items {
				if inStrSlice(supportedTokenTypes, token.TType, false) == -1 || inStrSlice(supportedNumberTokenTypes, token.TType, false) != -1 {
					return ErrUnsupportedNumberFormat
				}
			}
		}
	}
	return nil
}

// extractNumFmtDecimal returns decimal places, if has a decimal point token and
// zero place holder token from a number format code token list.
func extractNumFmtDecimal(tokens []nfp.Token) (int, bool, bool) {
	decimal, point, zero := 0, false, false
	for _, token := range tokens {
		if token.TType == nfp.TokenTypeDecimalPoint {
			point = true
		}
		if token.TType == nfp.TokenTypeZeroPlaceHolder {
			if point {
				decimal = len(token.TValue)
			}
			zero = true
		}
	}
	return decimal, point, zero
}

// extractNumFmtDecimal returns decimal places from a number format code that
// has the same decimal places in positive part negative part or only positive
// part, if the given number format code is not suitable for numeric this
// function will return -1.
func (f *File) extractNumFmtDecimal(fmtCode string) int {
	var (
		p                                              = nfp.NumberFormatParser()
		pos, neg, posPoint, negPoint, posZero, negZero bool
		posDecimal, negDecimal                         int
	)
	for i, section := range p.Parse(fmtCode) {
		if i == 0 {
			pos = true
			posDecimal, posPoint, posZero = extractNumFmtDecimal(section.Items)
		}
		if i == 1 {
			neg = true
			negDecimal, negPoint, negZero = extractNumFmtDecimal(section.Items)
		}
	}
	if !pos {
		return -1
	}
	equalPosNegDecimal := posPoint && negPoint && posDecimal == negDecimal
	equalPosNegZero := !posPoint && !negPoint && posZero && negZero
	if neg {
		if equalPosNegDecimal {
			return posDecimal
		}
		if equalPosNegZero {
			return 0
		}
		return -1
	}
	if posPoint {
		return posDecimal
	}
	if posZero {
		return 0
	}
	return -1
}

// getBuiltInNumFmtCode convert number format index to number format code with
// specified locale and language.
func (f *File) getBuiltInNumFmtCode(numFmtID int) (string, bool) {
	if fmtCode, ok := builtInNumFmt[numFmtID]; ok {
		return fmtCode, true
	}
	if isLangNumFmt(numFmtID) {
		if fn, ok := langNumFmtFunc[f.options.CultureInfo]; ok {
			return fn(f, numFmtID), true
		}
	}
	return "", false
}

// prepareNumberic split the number into two before and after parts by a
// decimal point.
func (nf *numberFormat) prepareNumberic(value string) {
	if nf.cellType != CellTypeNumber && nf.cellType != CellTypeDate {
		return
	}
	if nf.isNumeric, _, _ = isNumeric(value); !nf.isNumeric {
		return
	}
}

// format provides a function to return a string parse by number format
// expression. If the given number format is not supported, this will return
// the original cell value.
func format(value, numFmt string, date1904 bool, cellType CellType, opts *Options) string {
	p := nfp.NumberFormatParser()
	nf := numberFormat{opts: opts, section: p.Parse(numFmt), value: value, date1904: date1904, cellType: cellType}
	nf.number, nf.valueSectionType = nf.getValueSectionType(value)
	nf.prepareNumberic(value)
	for i, section := range nf.section {
		nf.sectionIdx = i
		if section.Type != nf.valueSectionType {
			continue
		}
		if nf.isNumeric {
			switch section.Type {
			case nfp.TokenSectionPositive:
				return nf.alignmentHandler(nf.positiveHandler())
			default:
				return nf.alignmentHandler(nf.negativeHandler())
			}
		}
		return nf.alignmentHandler(nf.textHandler())
	}
	return value
}

// getNumberPartLen returns the length of integer and fraction parts for the
// numeric.
func (nf *numberFormat) getNumberPartLen() (int, int) {
	var intPart, fracPart, intLen, fracLen int
	parts := strings.Split(strconv.FormatFloat(math.Abs(nf.number), 'f', -1, 64), ".")
	intPart = len(parts[0])
	if len(parts) == 2 {
		fracPart = len(parts[1])
	}
	if nf.intHolder > intPart {
		nf.intHolder = intPart
	}
	if intLen = intPart; nf.intPadding+nf.intHolder > intPart {
		intLen = nf.intPadding + nf.intHolder
	}
	if fracLen = fracPart; fracPart > nf.fracHolder+nf.fracPadding {
		fracLen = nf.fracHolder + nf.fracPadding
	}
	if nf.fracPadding > fracPart {
		fracLen = nf.fracPadding
	}
	return intLen, fracLen
}

// getNumberFmtConf generate the number format padding and placeholder
// configurations.
func (nf *numberFormat) getNumberFmtConf() {
	for _, token := range nf.section[nf.sectionIdx].Items {
		if token.TType == nfp.TokenTypeHashPlaceHolder {
			if nf.usePointer {
				nf.fracHolder += len(token.TValue)
				continue
			}
			nf.intHolder += len(token.TValue)
		}
		if token.TType == nfp.TokenTypeExponential {
			nf.useScientificNotation = true
		}
		if token.TType == nfp.TokenTypeThousandsSeparator {
			nf.useCommaSep = true
		}
		if token.TType == nfp.TokenTypePercent {
			nf.percent += len(token.TValue)
		}
		if token.TType == nfp.TokenTypeDecimalPoint {
			nf.usePointer = true
		}
		if token.TType == nfp.TokenTypeFraction {
			nf.useFraction = true
		}
		if token.TType == nfp.TokenTypeSwitchArgument {
			nf.switchArgument = token.TValue
		}
		if token.TType == nfp.TokenTypeZeroPlaceHolder {
			nf.intHolder = 0
			if nf.usePointer {
				if nf.useScientificNotation {
					nf.expBaseLen += len(token.TValue)
					continue
				}
				nf.fracPadding += len(token.TValue)
				continue
			}
			nf.intPadding += len(token.TValue)
		}
	}
}

// handleDigitsLiteral apply hash and zero place holder tokens for the number
// literal.
func handleDigitsLiteral(text string, tokenValueLen, intPartLen, hashZeroPartLen int) (int, string) {
	var result string
	l := tokenValueLen
	if intPartLen == 0 && len(text) > hashZeroPartLen {
		l = len(text) + tokenValueLen - hashZeroPartLen
	}
	if len(text) < hashZeroPartLen {
		intPartLen += len(text) - hashZeroPartLen
	}
	for i := 0; i < l; i++ {
		j := i + intPartLen
		if 0 <= j && j < len([]rune(text)) {
			result += string([]rune(text)[j])
		}
	}
	return l, result
}

// printNumberLiteral apply literal tokens for the pre-formatted text.
func (nf *numberFormat) printNumberLiteral(text string) string {
	var (
		result                      string
		frac                        float64
		useFraction                 bool
		intPartLen, hashZeroPartLen int
	)
	if nf.usePositive {
		result += "-"
	}
	for _, token := range nf.section[nf.sectionIdx].Items {
		if token.TType == nfp.TokenTypeHashPlaceHolder || token.TType == nfp.TokenTypeZeroPlaceHolder {
			hashZeroPartLen += len(token.TValue)
		}
	}
	for _, token := range nf.section[nf.sectionIdx].Items {
		if token.TType == nfp.TokenTypeCurrencyLanguage {
			_, _ = nf.currencyLanguageHandler(token)
			result += nf.currencyString
		}
		if token.TType == nfp.TokenTypeLiteral {
			result += token.TValue
		}
		if token.TType == nfp.TokenTypeHashPlaceHolder || token.TType == nfp.TokenTypeZeroPlaceHolder || token.TType == nfp.TokenTypeDigitalPlaceHolder {
			digits, str := handleDigitsLiteral(text, len(token.TValue), intPartLen, hashZeroPartLen)
			intPartLen += digits
			result += str
		}
		if token.TType == nfp.TokenTypeFraction {
			_, frac = math.Modf(nf.number)
			frac, useFraction = math.Abs(frac), true
		}
		if useFraction {
			result += nf.fractionHandler(frac, token)
		}
	}
	return nf.printSwitchArgument(result)
}

// fractionHandler handling fraction number format expression for positive and
// negative numeric.
func (nf *numberFormat) fractionHandler(frac float64, token nfp.Token) string {
	var rat, result string
	if token.TType == nfp.TokenTypeDigitalPlaceHolder {
		fracPlaceHolder := len(token.TValue)
		for i := 0; i < 5000; i++ {
			if r := newRat(frac, int64(i), 0); len(r.Denom().String()) <= fracPlaceHolder {
				if rat = r.String(); strings.HasPrefix(rat, "0/") {
					rat = strings.Repeat(" ", 3)
				}
				continue
			}
			break
		}
		result += rat
	}
	if token.TType == nfp.TokenTypeDenominator {
		denom, _ := strconv.ParseFloat(token.TValue, 64)
		result += fmt.Sprintf("%d/%d", int(math.Round(frac*denom)), int(math.Round(denom)))
	}
	return result
}

// printCommaSep format number with thousands separator.
func printCommaSep(text string) string {
	var (
		target strings.Builder
		subStr = strings.Split(text, ".")
		length = len(subStr[0])
	)
	for i := 0; i < length; i++ {
		if i > 0 && (length-i)%3 == 0 {
			target.WriteString(",")
		}
		target.WriteByte(text[i])
	}
	if len(subStr) == 2 {
		target.WriteString(".")
		target.WriteString(subStr[1])
	}
	return target.String()
}

// printSwitchArgument format number with switch argument.
func (nf *numberFormat) printSwitchArgument(text string) string {
	if nf.switchArgument == "" {
		return text
	}
	if fn, ok := switchArgumentFunc[nf.switchArgument]; ok {
		return fn(text)
	}
	return nf.value
}

// printBigNumber format number which precision great than 15 with fraction
// zero padding and percentage symbol.
func (nf *numberFormat) printBigNumber(decimal float64, fracLen int) string {
	var exp float64
	if nf.percent > 0 {
		exp = 1
	}
	result := strings.TrimLeft(strconv.FormatFloat(decimal*math.Pow(100, exp), 'f', -1, 64), "-")
	if nf.useCommaSep {
		result = printCommaSep(result)
	}
	if fracLen > 0 {
		if parts := strings.Split(result, "."); len(parts) == 2 {
			fracPartLen := len(parts[1])
			if fracPartLen < fracLen {
				result = fmt.Sprintf("%s%s", result, strings.Repeat("0", fracLen-fracPartLen))
			}
			if fracPartLen > fracLen {
				result = fmt.Sprintf("%s.%s", parts[0], parts[1][:fracLen])
			}
		} else {
			result = fmt.Sprintf("%s.%s", result, strings.Repeat("0", fracLen))
		}
	}
	if nf.percent > 0 {
		return fmt.Sprintf("%s%%", result)
	}
	return result
}

// numberHandler handling number format expression for positive and negative
// numeric.
func (nf *numberFormat) numberHandler() string {
	nf.getNumberFmtConf()
	var (
		num             = nf.number
		intLen, fracLen = nf.getNumberPartLen()
		result          string
	)
	if isNum, precision, decimal := isNumeric(nf.value); isNum {
		if precision > 15 && intLen+fracLen > 15 && !nf.useScientificNotation {
			return nf.printNumberLiteral(nf.printBigNumber(decimal, fracLen))
		}
	}
	paddingLen := intLen + fracLen
	if fracLen > 0 {
		paddingLen++
	}
	fmtCode := fmt.Sprintf("%%0%d.%df%s", paddingLen, fracLen, strings.Repeat("%%", nf.percent))
	if nf.useScientificNotation {
		if nf.expBaseLen != 2 {
			return nf.value
		}
		fmtCode = fmt.Sprintf("%%.%dE%s", fracLen, strings.Repeat("%%", nf.percent))
	}
	if nf.percent > 0 {
		num *= math.Pow(100, float64(nf.percent))
	}
	if nf.useFraction {
		num = math.Floor(math.Abs(num))
	}
	if !nf.useScientificNotation {
		ratio := math.Pow(10, float64(fracLen))
		num = math.Round(num*ratio) / ratio
	}
	if result = fmt.Sprintf(fmtCode, math.Abs(num)); nf.useCommaSep {
		result = printCommaSep(result)
	}
	return nf.printNumberLiteral(result)
}

// dateTimeHandler handling data and time number format expression for a
// positive numeric.
func (nf *numberFormat) dateTimeHandler() string {
	nf.t, nf.hours, nf.seconds = timeFromExcelTime(nf.number, nf.date1904), false, false
	if !nf.useMillisecond {
		nf.t = nf.t.Add(time.Duration(math.Round(float64(nf.t.Nanosecond())/1e9)) * time.Second)
	}
	for i, token := range nf.section[nf.sectionIdx].Items {
		if token.TType == nfp.TokenTypeCurrencyLanguage {
			if changeNumFmtCode, err := nf.currencyLanguageHandler(token); err != nil || changeNumFmtCode {
				return nf.value
			}
			nf.result += nf.currencyString
		}
		if token.TType == nfp.TokenTypeDateTimes {
			nf.dateTimesHandler(i, token)
		}
		if token.TType == nfp.TokenTypeElapsedDateTimes {
			nf.elapsedDateTimesHandler(token)
		}
		if token.TType == nfp.TokenTypeLiteral {
			nf.result += token.TValue
			continue
		}
		if token.TType == nfp.TokenTypeDecimalPoint {
			nf.result += "."
		}
		if token.TType == nfp.TokenTypeSwitchArgument {
			nf.switchArgument = token.TValue
		}
		if token.TType == nfp.TokenTypeZeroPlaceHolder {
			zeroHolderLen := len(token.TValue)
			if zeroHolderLen > 3 {
				zeroHolderLen = 3
			}
			nf.result += fmt.Sprintf("%03d", nf.t.Nanosecond()/1e6)[:zeroHolderLen]
		}
	}
	return nf.printSwitchArgument(nf.result)
}

// alignmentHandler will be handling alignment token for each number format
// selection for a number format expression.
func (nf *numberFormat) alignmentHandler(result string) string {
	tokens := nf.section[nf.sectionIdx].Items
	if len(tokens) == 0 {
		return result
	}
	if tokens[0].TType == nfp.TokenTypeAlignment {
		result = nfp.Whitespace + result
	}
	if l := len(tokens); tokens[l-1].TType == nfp.TokenTypeAlignment {
		result += nfp.Whitespace
	}
	return result
}

// positiveHandler will be handling positive selection for a number format
// expression.
func (nf *numberFormat) positiveHandler() string {
	var fmtNum bool
	for _, token := range nf.section[nf.sectionIdx].Items {
		if token.TType == nfp.TokenTypeGeneral {
			if isNum, precision, _ := isNumeric(nf.value); isNum && precision > 11 {
				return strconv.FormatFloat(nf.number, 'G', 10, 64)
			}
			return nf.value
		}
		if inStrSlice(supportedNumberTokenTypes, token.TType, true) != -1 {
			fmtNum = true
		}
		if inStrSlice(supportedDateTimeTokenTypes, token.TType, true) != -1 {
			if fmtNum || nf.number < 0 {
				return nf.value
			}
			var useDateTimeTokens bool
			for _, token := range nf.section[nf.sectionIdx].Items {
				if inStrSlice(supportedDateTimeTokenTypes, token.TType, false) != -1 {
					if useDateTimeTokens && nf.useMillisecond {
						return nf.value
					}
					useDateTimeTokens = true
				}
				if inStrSlice(supportedNumberTokenTypes, token.TType, false) != -1 {
					if token.TType == nfp.TokenTypeZeroPlaceHolder {
						nf.useMillisecond = true
						continue
					}
					return nf.value
				}
			}
			return nf.dateTimeHandler()
		}
	}
	return nf.numberHandler()
}

// currencyLanguageHandler will be handling currency and language types tokens
// for a number format expression.
func (nf *numberFormat) currencyLanguageHandler(token nfp.Token) (bool, error) {
	for _, part := range token.Parts {
		if inStrSlice(supportedTokenTypes, part.Token.TType, true) == -1 {
			return false, ErrUnsupportedNumberFormat
		}
		if part.Token.TType == nfp.TokenSubTypeLanguageInfo {
			if inStrSlice([]string{"F800", "x-sysdate", "1010000"}, part.Token.TValue, false) != -1 {
				if nf.opts != nil && nf.opts.LongDatePattern != "" {
					nf.value = format(nf.value, nf.opts.LongDatePattern, nf.date1904, nf.cellType, nf.opts)
					return true, nil
				}
				part.Token.TValue = "409"
			}
			if inStrSlice([]string{"F400", "x-systime"}, part.Token.TValue, false) != -1 {
				if nf.opts != nil && nf.opts.LongTimePattern != "" {
					nf.value = format(nf.value, nf.opts.LongTimePattern, nf.date1904, nf.cellType, nf.opts)
					return true, nil
				}
				part.Token.TValue = "409"
			}
			if _, ok := getSupportedLanguageInfo(strings.ToUpper(part.Token.TValue)); !ok {
				return false, ErrUnsupportedNumberFormat
			}
			nf.localCode = strings.ToUpper(part.Token.TValue)
		}
		if part.Token.TType == nfp.TokenSubTypeCurrencyString {
			nf.currencyString = part.Token.TValue
			return false, nil
		}
	}
	return false, nil
}

// localAmPm return AM/PM name by supported language ID.
func (nf *numberFormat) localAmPm(ap string) string {
	if languageInfo, ok := getSupportedLanguageInfo(nf.localCode); ok {
		return languageInfo.apFmt
	}
	return ap
}

// localMonthsNameAfrikaans returns the Afrikaans name of the month.
func localMonthsNameAfrikaans(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesAfrikaansAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesAfrikaans[int(t.Month())-1]
	}
	return monthNamesAfrikaansAbbr[int(t.Month())-1][:1]
}

// localMonthsNameAlbanian returns the Albanian name of the month.
func localMonthsNameAlbanian(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesAlbanianAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesAlbanian[int(t.Month())-1]
	}
	return monthNamesAlbanianAbbr[int(t.Month())-1][:1]
}

// localMonthsNameAlsatian returns the Alsatian name of the month.
func localMonthsNameAlsatian(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesAlsatianAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesAlsatian[int(t.Month())-1]
	}
	return monthNamesAlsatianAbbr[int(t.Month())-1][:1]
}

// localMonthsNameAlsatianFrance returns the Alsatian France name of the month.
func localMonthsNameAlsatianFrance(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesAlsatianFranceAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesAlsatianFrance[int(t.Month())-1]
	}
	return monthNamesAlsatianFranceAbbr[int(t.Month())-1][:1]
}

// localMonthsNameAmharic returns the Amharic name of the month.
func localMonthsNameAmharic(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesAmharicAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesAmharic[int(t.Month())-1]
	}
	return string([]rune(monthNamesAmharic[int(t.Month())-1])[:1])
}

// localMonthsNameArabic returns the Arabic name of the month.
func localMonthsNameArabic(t time.Time, abbr int) string {
	if abbr == 5 {
		return string([]rune(monthNamesArabic[int(t.Month())-1])[:1])
	}
	return monthNamesArabic[int(t.Month())-1]
}

// localMonthsNameArabicIraq returns the Arabic Iraq name of the month.
func localMonthsNameArabicIraq(t time.Time, abbr int) string {
	if abbr == 5 {
		return string([]rune(monthNamesArabicIraq[int(t.Month())-1])[:1])
	}
	return monthNamesArabicIraq[int(t.Month())-1]
}

// localMonthsNameArmenian returns the Armenian name of the month.
func localMonthsNameArmenian(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesArmenianAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesArmenian[int(t.Month())-1]
	}
	return string([]rune(monthNamesArmenianAbbr[int(t.Month())-1])[:1])
}

// localMonthsNameAssamese returns the Assamese name of the month.
func localMonthsNameAssamese(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesAssameseAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesAssamese[int(t.Month())-1]
	}
	return string([]rune(monthNamesAssameseAbbr[int(t.Month())-1])[:1])
}

// localMonthsNameAzerbaijaniCyrillic returns the Azerbaijani (Cyrillic) name of the month.
func localMonthsNameAzerbaijaniCyrillic(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesAzerbaijaniCyrillicAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesAzerbaijaniCyrillic[int(t.Month())-1]
	}
	return string([]rune(monthNamesAzerbaijaniCyrillic[int(t.Month())-1])[:1])
}

// localMonthsNameAzerbaijani returns the Azerbaijani name of the month.
func localMonthsNameAzerbaijani(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesAzerbaijaniAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesAzerbaijani[int(t.Month())-1]
	}
	return string([]rune(monthNamesAzerbaijani[int(t.Month())-1])[:1])
}

// localMonthsNameAustria returns the Austria name of the month.
func localMonthsNameAustria(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesAustriaAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesAustria[int(t.Month())-1]
	}
	return monthNamesAustriaAbbr[int(t.Month())-1][:1]
}

// localMonthsNameBangla returns the German name of the month.
func localMonthsNameBangla(t time.Time, abbr int) string {
	if abbr == 3 || abbr == 4 {
		return monthNamesBangla[int(t.Month())-1]
	}
	return string([]rune(monthNamesBangla[int(t.Month())-1])[:1])
}

// localMonthsNameBashkir returns the Bashkir name of the month.
func localMonthsNameBashkir(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesBashkirAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesBashkir[int(t.Month())-1]
	}
	return string([]rune(monthNamesBashkir[int(t.Month())-1])[:1])
}

// localMonthsNameBasque returns the Basque name of the month.
func localMonthsNameBasque(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesBasqueAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesBasque[int(t.Month())-1]
	}
	return string([]rune(monthNamesBasque[int(t.Month())-1])[:1])
}

// localMonthsNameBelarusian returns the Belarusian name of the month.
func localMonthsNameBelarusian(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesBelarusianAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesBelarusian[int(t.Month())-1]
	}
	return string([]rune(monthNamesBelarusian[int(t.Month())-1])[:1])
}

// localMonthsNameBosnianCyrillic returns the Bosnian (Cyrillic) name of the month.
func localMonthsNameBosnianCyrillic(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesBosnianCyrillicAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesBosnianCyrillic[int(t.Month())-1]
	}
	return string([]rune(monthNamesBosnianCyrillic[int(t.Month())-1])[:1])
}

// localMonthsNameBosnian returns the Bosnian name of the month.
func localMonthsNameBosnian(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesBosnianAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesBosnian[int(t.Month())-1]
	}
	return string([]rune(monthNamesBosnian[int(t.Month())-1])[:1])
}

// localMonthsNameBreton returns the Breton name of the month.
func localMonthsNameBreton(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesBretonAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesBreton[int(t.Month())-1]
	}
	return string([]rune(monthNamesBreton[int(t.Month())-1])[:1])
}

// localMonthsNameBulgarian returns the Bulgarian name of the month.
func localMonthsNameBulgarian(t time.Time, abbr int) string {
	if abbr == 3 {
		return string([]rune(monthNamesBulgarian[int(t.Month())-1])[:3])
	}
	if abbr == 4 {
		return monthNamesBulgarian[int(t.Month())-1]
	}
	return string([]rune(monthNamesBulgarian[int(t.Month())-1])[:1])
}

// localMonthsNameBurmese returns the Burmese name of the month.
func localMonthsNameBurmese(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesBurmeseAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesBurmese[int(t.Month())-1]
	}
	return string([]rune(monthNamesBurmese[int(t.Month())-1])[:1])
}

// localMonthsNameCaribbean returns the Caribbean name of the month.
func localMonthsNameCaribbean(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesCaribbeanAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesCaribbean[int(t.Month())-1]
	}
	return monthNamesCaribbeanAbbr[int(t.Month())-1][:1]
}

// localMonthsNameCentralKurdish returns the Central Kurdish name of the month.
func localMonthsNameCentralKurdish(t time.Time, abbr int) string {
	if abbr == 5 {
		return string([]rune(monthNamesCentralKurdish[int(t.Month())-1])[:1])
	}
	return monthNamesCentralKurdish[int(t.Month())-1]
}

// localMonthsNameCherokee returns the Cherokee name of the month.
func localMonthsNameCherokee(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesCherokeeAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesCherokee[int(t.Month())-1]
	}
	return string([]rune(monthNamesCherokee[int(t.Month())-1])[:1])
}

// localMonthsNameChinese1 returns the Chinese name of the month.
func localMonthsNameChinese1(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesChineseNum[t.Month()]
	}
	if abbr == 4 {
		return monthNamesChinese[int(t.Month())-1]
	}
	return monthNamesChineseAbbr[int(t.Month())-1]
}

// localMonthsNameChinese2 returns the Chinese name of the month.
func localMonthsNameChinese2(t time.Time, abbr int) string {
	if abbr == 3 || abbr == 4 {
		return monthNamesChinese[int(t.Month())-1]
	}
	return monthNamesChineseAbbr[int(t.Month())-1]
}

// localMonthsNameChinese3 returns the Chinese name of the month.
func localMonthsNameChinese3(t time.Time, abbr int) string {
	if abbr == 3 || abbr == 4 {
		return monthNamesChineseNum[t.Month()]
	}
	return strconv.Itoa(int(t.Month()))
}

// localMonthsNameEnglish returns the English name of the month.
func localMonthsNameEnglish(t time.Time, abbr int) string {
	if abbr == 3 {
		return t.Month().String()[:3]
	}
	if abbr == 4 {
		return t.Month().String()
	}
	return t.Month().String()[:1]
}

// localMonthsNameEstonian returns the Estonian name of the month.
func localMonthsNameEstonian(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesEstonianAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesEstonian[int(t.Month())-1]
	}
	return monthNamesEstonianAbbr[int(t.Month())-1][:1]
}

// localMonthsNameFaroese returns the Faroese name of the month.
func localMonthsNameFaroese(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthsNameFaroeseAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesFaroese[int(t.Month())-1]
	}
	return monthsNameFaroeseAbbr[int(t.Month())-1][:1]
}

// localMonthsNameFilipino returns the Filipino name of the month.
func localMonthsNameFilipino(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesFilipinoAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesFilipino[int(t.Month())-1]
	}
	return fmt.Sprintf("%02d", int(t.Month()))
}

// localMonthsNameFinnish returns the Finnish name of the month.
func localMonthsNameFinnish(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesFinnishAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesFinnish[int(t.Month())-1]
	}
	return fmt.Sprintf("%02d", int(t.Month()))
}

// localMonthsNameFrench returns the French name of the month.
func localMonthsNameFrench(t time.Time, abbr int) string {
	if abbr == 3 {
		month := monthNamesFrench[int(t.Month())-1]
		if len([]rune(month)) <= 4 {
			return monthNamesFrench[int(t.Month())-1]
		}
		return monthNamesFrenchAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesFrench[int(t.Month())-1]
	}
	return monthNamesFrenchAbbr[int(t.Month())-1][:1]
}

// localMonthsNameFrisian returns the Frisian name of the month.
func localMonthsNameFrisian(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesFrisianAbbr[int(t.Month())-1]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesFrisian[int(t.Month())-1]
	}
	return monthNamesFrisian[int(t.Month())-1][:1]
}

// localMonthsNameFulah returns the Fulah name of the month.
func localMonthsNameFulah(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesFulahAbbr[int(t.Month())-1]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesFulah[int(t.Month())-1]
	}
	return monthNamesFulah[int(t.Month())-1][:1]
}

// localMonthsNameGalician returns the Galician name of the month.
func localMonthsNameGalician(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesGalicianAbbr[int(t.Month())-1]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesGalician[int(t.Month())-1]
	}
	return monthNamesGalician[int(t.Month())-1][:1]
}

// localMonthsNameGeorgian returns the Georgian name of the month.
func localMonthsNameGeorgian(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesGeorgianAbbr[int(t.Month())-1]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesGeorgian[int(t.Month())-1]
	}
	return string([]rune(monthNamesGeorgian[int(t.Month())-1])[:1])
}

// localMonthsNameGerman returns the German name of the month.
func localMonthsNameGerman(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesGermanAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesGerman[int(t.Month())-1]
	}
	return monthNamesGermanAbbr[int(t.Month())-1][:1]
}

// localMonthsNameGreek returns the Greek name of the month.
func localMonthsNameGreek(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesGreekAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesGreek[int(t.Month())-1]
	}
	return string([]rune(monthNamesGreekAbbr[int(t.Month())-1])[:1])
}

// localMonthsNameGreenlandic returns the Greenlandic name of the month.
func localMonthsNameGreenlandic(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesGreenlandicAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesGreenlandic[int(t.Month())-1]
	}
	return string([]rune(monthNamesGreenlandicAbbr[int(t.Month())-1])[:1])
}

// localMonthsNameGuarani returns the Guarani name of the month.
func localMonthsNameGuarani(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesGuaraniAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesGuarani[int(t.Month())-1]
	}
	return string([]rune(monthNamesGuaraniAbbr[int(t.Month())-1])[:1])
}

// localMonthsNameGujarati returns the Gujarati name of the month.
func localMonthsNameGujarati(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesGujaratiAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesGujarati[int(t.Month())-1]
	}
	return string([]rune(monthNamesGujaratiAbbr[int(t.Month())-1])[:1])
}

// localMonthsNameHausa returns the Hausa name of the month.
func localMonthsNameHausa(t time.Time, abbr int) string {
	if abbr == 3 {
		return string([]rune(monthNamesHausa[int(t.Month())-1])[:3])
	}
	if abbr == 4 {
		return monthNamesHausa[int(t.Month())-1]
	}
	return string([]rune(monthNamesHausa[int(t.Month())-1])[:1])
}

// localMonthsNameHawaiian returns the Hawaiian name of the month.
func localMonthsNameHawaiian(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesHawaiianAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesHawaiian[int(t.Month())-1]
	}
	return string([]rune(monthNamesHawaiianAbbr[int(t.Month())-1])[:1])
}

// localMonthsNameHebrew returns the Hebrew name of the month.
func localMonthsNameHebrew(t time.Time, abbr int) string {
	if abbr == 3 {
		return string([]rune(monthNamesHebrew[int(t.Month())-1])[:3])
	}
	if abbr == 4 {
		return monthNamesHebrew[int(t.Month())-1]
	}
	return string([]rune(monthNamesHebrew[int(t.Month())-1])[:1])
}

// localMonthsNameHindi returns the Hindi name of the month.
func localMonthsNameHindi(t time.Time, abbr int) string {
	if abbr == 3 || abbr == 4 {
		return monthNamesHindi[int(t.Month())-1]
	}
	return string([]rune(monthNamesHindi[int(t.Month())-1])[:1])
}

// localMonthsNameHungarian returns the Hungarian name of the month.
func localMonthsNameHungarian(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesHungarianAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesHungarian[int(t.Month())-1]
	}
	return string([]rune(monthNamesHungarianAbbr[int(t.Month())-1])[:1])
}

// localMonthsNameIcelandic returns the Icelandic name of the month.
func localMonthsNameIcelandic(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesIcelandicAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesIcelandic[int(t.Month())-1]
	}
	return string([]rune(monthNamesIcelandicAbbr[int(t.Month())-1])[:1])
}

// localMonthsNameIgbo returns the Igbo name of the month.
func localMonthsNameIgbo(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesIgboAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesIgbo[int(t.Month())-1]
	}
	return string([]rune(monthNamesIgboAbbr[int(t.Month())-1])[:1])
}

// localMonthsNameIndonesian returns the Indonesian name of the month.
func localMonthsNameIndonesian(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesIndonesianAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesIndonesian[int(t.Month())-1]
	}
	return string([]rune(monthNamesIndonesianAbbr[int(t.Month())-1])[:1])
}

// localMonthsNameInuktitut returns the Inuktitut name of the month.
func localMonthsNameInuktitut(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesInuktitutAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesInuktitut[int(t.Month())-1]
	}
	return string([]rune(monthNamesInuktitutAbbr[int(t.Month())-1])[:1])
}

// localMonthsNameIrish returns the Irish name of the month.
func localMonthsNameIrish(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesIrishAbbr[(t.Month() - 1)]
	}
	if abbr == 4 {
		return monthNamesIrish[int(t.Month())-1]
	}
	return monthNamesIrishAbbr[int(t.Month())-1][:1]
}

// localMonthsNameItalian returns the Italian name of the month.
func localMonthsNameItalian(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesItalianAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesItalian[int(t.Month())-1]
	}
	return monthNamesItalianAbbr[int(t.Month())-1][:1]
}

// localMonthsNameKannada returns the Kannada name of the month.
func localMonthsNameKannada(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesKannadaAbbr[int(t.Month())-1]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesKannada[int(t.Month())-1]
	}
	return string([]rune(monthNamesKannada[int(t.Month())-1])[:1])
}

// localMonthsNameKashmiri returns the Kashmiri name of the month.
func localMonthsNameKashmiri(t time.Time, abbr int) string {
	if abbr == 5 {
		return string([]rune(monthNamesKashmiri[int(t.Month())-1])[:1])
	}
	return monthNamesKashmiri[int(t.Month())-1]
}

// localMonthsNameKazakh returns the Kazakh name of the month.
func localMonthsNameKazakh(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesKazakhAbbr[int(t.Month())-1]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesKazakh[int(t.Month())-1]
	}
	return string([]rune(monthNamesKazakh[int(t.Month())-1])[:1])
}

// localMonthsNameKhmer returns the Khmer name of the month.
func localMonthsNameKhmer(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesKhmerAbbr[int(t.Month())-1]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesKhmer[int(t.Month())-1]
	}
	return string([]rune(monthNamesKhmerAbbr[int(t.Month())+11])[:1])
}

// localMonthsNameKiche returns the Kiche name of the month.
func localMonthsNameKiche(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesKicheAbbr[int(t.Month())-1]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesKiche[int(t.Month())-1]
	}
	return string([]rune(monthNamesKicheAbbr[(t.Month() - 1)])[:1])
}

// localMonthsNameKinyarwanda returns the Kinyarwanda name of the month.
func localMonthsNameKinyarwanda(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesKinyarwandaAbbr[int(t.Month())-1]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesKinyarwanda[int(t.Month())-1]
	}
	return string([]rune(monthNamesKinyarwanda[(t.Month() - 1)])[:1])
}

// localMonthsNameKiswahili returns the Kiswahili name of the month.
func localMonthsNameKiswahili(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesKiswahiliAbbr[int(t.Month())-1]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesKiswahili[int(t.Month())-1]
	}
	return string([]rune(monthNamesKiswahili[(t.Month() - 1)])[:1])
}

// localMonthsNameKonkani returns the Konkani name of the month.
func localMonthsNameKonkani(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesKonkaniAbbr[int(t.Month())-1]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesKonkani[int(t.Month())-1]
	}
	return string([]rune(monthNamesKonkani[(t.Month() - 1)])[:1])
}

// localMonthsNameKorean returns the Korean name of the month.
func localMonthsNameKorean(t time.Time, abbr int) string {
	if abbr == 4 || abbr > 6 {
		return monthNamesKoreanAbbr[int(t.Month())-1]
	}
	return strconv.Itoa(int(t.Month()))
}

// localMonthsNameKyrgyz returns the Kyrgyz name of the month.
func localMonthsNameKyrgyz(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesKyrgyzAbbr[int(t.Month())-1]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesKyrgyz[int(t.Month())-1]
	}
	return string([]rune(monthNamesKyrgyz[(t.Month() - 1)])[:1])
}

// localMonthsNameLao returns the Lao name of the month.
func localMonthsNameLao(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesLaoAbbr[int(t.Month())-1]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesLao[int(t.Month())-1]
	}
	return string([]rune(monthNamesLao[(t.Month() - 1)])[:1])
}

// localMonthsNameLatin returns the Latin name of the month.
func localMonthsNameLatin(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesLatinAbbr[int(t.Month())-1]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesLatin[int(t.Month())-1]
	}
	return string([]rune(monthNamesLatin[(t.Month() - 1)])[:1])
}

// localMonthsNameLatvian returns the Latvian name of the month.
func localMonthsNameLatvian(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesLatvianAbbr[int(t.Month())-1]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesLatvian[int(t.Month())-1]
	}
	return string([]rune(monthNamesLatvian[(t.Month() - 1)])[:1])
}

// localMonthsNameLithuanian returns the Lithuanian name of the month.
func localMonthsNameLithuanian(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesLithuanianAbbr[int(t.Month())-1]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesLithuanian[int(t.Month())-1]
	}
	return string([]rune(monthNamesLithuanian[(t.Month() - 1)])[:1])
}

// localMonthsNameLowerSorbian returns the LowerSorbian name of the month.
func localMonthsNameLowerSorbian(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesLowerSorbianAbbr[int(t.Month())-1]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesLowerSorbian[int(t.Month())-1]
	}
	return string([]rune(monthNamesLowerSorbian[(t.Month() - 1)])[:1])
}

// localMonthsNameLuxembourgish returns the Luxembourgish name of the month.
func localMonthsNameLuxembourgish(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesLuxembourgishAbbr[int(t.Month())-1]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesLuxembourgish[int(t.Month())-1]
	}
	return string([]rune(monthNamesLuxembourgish[(t.Month() - 1)])[:1])
}

// localMonthsNameMacedonian returns the Macedonian name of the month.
func localMonthsNameMacedonian(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesMacedonianAbbr[int(t.Month())-1]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesMacedonian[int(t.Month())-1]
	}
	return string([]rune(monthNamesMacedonian[(t.Month() - 1)])[:1])
}

// localMonthsNameMalay returns the Malay name of the month.
func localMonthsNameMalay(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesMalayAbbr[int(t.Month())-1]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesMalay[int(t.Month())-1]
	}
	return string([]rune(monthNamesMalay[(t.Month() - 1)])[:1])
}

// localMonthsNameMalayalam returns the Malayalam name of the month.
func localMonthsNameMalayalam(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesMalayalamAbbr[int(t.Month())-1]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesMalayalam[int(t.Month())-1]
	}
	return string([]rune(monthNamesMalayalam[(t.Month() - 1)])[:1])
}

// localMonthsNameMaltese returns the Maltese name of the month.
func localMonthsNameMaltese(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesMalteseAbbr[int(t.Month())-1]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesMaltese[int(t.Month())-1]
	}
	return string([]rune(monthNamesMaltese[(t.Month() - 1)])[:1])
}

// localMonthsNameMaori returns the Maori name of the month.
func localMonthsNameMaori(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesMaoriAbbr[int(t.Month())-1]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesMaori[int(t.Month())-1]
	}
	return string([]rune(monthNamesMaori[(t.Month() - 1)])[:1])
}

// localMonthsNameMapudungun returns the Mapudungun name of the month.
func localMonthsNameMapudungun(t time.Time, abbr int) string {
	if abbr == 5 {
		return string([]rune(monthNamesMapudungun[(t.Month() - 1)])[:1])
	}
	return monthNamesMapudungun[int(t.Month())-1]
}

// localMonthsNameMarathi returns the Marathi name of the month.
func localMonthsNameMarathi(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesMarathiAbbr[int(t.Month())-1]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesMarathi[int(t.Month())-1]
	}
	return string([]rune(monthNamesMarathi[(t.Month() - 1)])[:1])
}

// localMonthsNameMohawk returns the Mohawk name of the month.
func localMonthsNameMohawk(t time.Time, abbr int) string {
	if abbr == 3 {
		return t.Month().String()[:3]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesMohawk[int(t.Month())-1]
	}
	return string([]rune(monthNamesMohawk[(t.Month() - 1)])[:1])
}

// localMonthsNameMongolian returns the Mongolian name of the month.
func localMonthsNameMongolian(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesMongolianAbbr[int(t.Month())-1]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesMongolian[int(t.Month())-1]
	}
	return string([]rune(monthNamesMongolian[(t.Month() - 1)])[:1])
}

// localMonthsNameMorocco returns the Morocco name of the month.
func localMonthsNameMorocco(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesMoroccoAbbr[int(t.Month())-1]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesFrench[int(t.Month())-1]
	}
	return monthNamesFrench[int(t.Month())-1][:1]
}

// localMonthsNameNepali returns the Nepali name of the month.
func localMonthsNameNepali(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesNepaliAbbr[int(t.Month())-1]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesNepali[int(t.Month())-1]
	}
	return string([]rune(monthNamesNepali[(t.Month() - 1)])[:1])
}

// localMonthsNameNepaliIN returns the India Nepali name of the month.
func localMonthsNameNepaliIN(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesNepaliINAbbr[int(t.Month())-1]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesNepaliIN[int(t.Month())-1]
	}
	return string([]rune(monthNamesNepaliIN[(t.Month() - 1)])[:1])
}

// localMonthsNameNigeria returns the Nigeria name of the month.
func localMonthsNameNigeria(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesNigeriaAbbr[int(t.Month())-1]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesNigeria[int(t.Month())-1]
	}
	return monthNamesNigeria[int(t.Month())-1][:1]
}

// localMonthsNameNorwegian returns the Norwegian name of the month.
func localMonthsNameNorwegian(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthsNameFaroeseAbbr[int(t.Month())-1]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesNorwegian[int(t.Month())-1]
	}
	return string([]rune(monthNamesNorwegian[(t.Month() - 1)])[:1])
}

// localMonthsNameOccitan returns the Occitan name of the month.
func localMonthsNameOccitan(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesOccitanAbbr[int(t.Month())-1]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesOccitan[int(t.Month())-1]
	}
	return string([]rune(monthNamesOccitan[(t.Month() - 1)])[:1])
}

// localMonthsNameOdia returns the Odia name of the month.
func localMonthsNameOdia(t time.Time, abbr int) string {
	if abbr == 5 {
		return string([]rune(monthNamesOdia[(t.Month() - 1)])[:1])
	}
	return monthNamesOdia[int(t.Month())-1]
}

// localMonthsNameOromo returns the Oromo name of the month.
func localMonthsNameOromo(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesOromoAbbr[int(t.Month())-1]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesOromo[int(t.Month())-1]
	}
	return string([]rune(monthNamesOromo[(t.Month() - 1)])[:1])
}

// localMonthsNamePashto returns the Pashto name of the month.
func localMonthsNamePashto(t time.Time, abbr int) string {
	if int(t.Month()) == 6 {
		if abbr == 3 {
			return "\u0686\u0646\u06AB\u0627 \u069A"
		}
		if abbr == 4 || abbr > 6 {
			return "\u0686\u0646\u06AB\u0627 \u069A\u0632\u0645\u0631\u0649"
		}
	}
	return monthNamesPashto[int(t.Month())-1]
}

// localMonthsNamePersian returns the Persian name of the month.
func localMonthsNamePersian(t time.Time, abbr int) string {
	if abbr == 5 {
		return string([]rune(monthNamesPersian[(t.Month() - 1)])[:1])
	}
	return monthNamesPersian[int(t.Month())-1]
}

// localMonthsNamePolish returns the Polish name of the month.
func localMonthsNamePolish(t time.Time, abbr int) string {
	if abbr == 3 {
		return string([]rune(monthNamesPolish[(t.Month() - 1)])[:3])
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesPolish[int(t.Month())-1]
	}
	return string([]rune(monthNamesPolish[(t.Month() - 1)])[:1])
}

// localMonthsNamePortuguese returns the Portuguese name of the month.
func localMonthsNamePortuguese(t time.Time, abbr int) string {
	if abbr == 3 {
		return string([]rune(monthNamesPortuguese[(t.Month() - 1)])[:3])
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesPortuguese[int(t.Month())-1]
	}
	return string([]rune(monthNamesPortuguese[(t.Month() - 1)])[:1])
}

// localMonthsNamePunjabi returns the Punjabi name of the month.
func localMonthsNamePunjabi(t time.Time, abbr int) string {
	if abbr == 5 {
		return string([]rune(monthNamesPunjabi[(t.Month() - 1)])[:1])
	}
	return monthNamesPunjabi[int(t.Month())-1]
}

// localMonthsNamePunjabiArab returns the Punjabi Arab name of the month.
func localMonthsNamePunjabiArab(t time.Time, abbr int) string {
	if abbr == 5 {
		return string([]rune(monthNamesPunjabiArab[(t.Month() - 1)])[:1])
	}
	return monthNamesPunjabiArab[int(t.Month())-1]
}

// localMonthsNameQuechua returns the Quechua name of the month.
func localMonthsNameQuechua(t time.Time, abbr int) string {
	if abbr == 3 {
		return string([]rune(monthNamesQuechua[(t.Month() - 1)])[:3])
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesQuechua[int(t.Month())-1]
	}
	return string([]rune(monthNamesQuechua[(t.Month() - 1)])[:1])
}

// localMonthsNameQuechuaEcuador returns the QuechuaEcuador name of the month.
func localMonthsNameQuechuaEcuador(t time.Time, abbr int) string {
	if abbr == 3 {
		if int(t.Month()) == 1 {
			return string([]rune(monthNamesQuechuaEcuador[(t.Month() - 1)])[:4])
		}
		return string([]rune(monthNamesQuechuaEcuador[(t.Month() - 1)])[:3])
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesQuechuaEcuador[int(t.Month())-1]
	}
	return string([]rune(monthNamesQuechuaEcuador[(t.Month() - 1)])[:1])
}

// localMonthsNameRomanian returns the Romanian name of the month.
func localMonthsNameRomanian(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesRomanianAbbr[int(t.Month())-1]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesRomanian[int(t.Month())-1]
	}
	return string([]rune(monthNamesRomanian[(t.Month() - 1)])[:1])
}

// localMonthsNameRomansh returns the Romansh name of the month.
func localMonthsNameRomansh(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesRomanshAbbr[int(t.Month())-1]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesRomansh[int(t.Month())-1]
	}
	return string([]rune(monthNamesRomansh[(t.Month() - 1)])[:1])
}

// localMonthsNameRussian returns the Russian name of the month.
func localMonthsNameRussian(t time.Time, abbr int) string {
	if abbr == 3 {
		month := monthNamesRussian[int(t.Month())-1]
		if len([]rune(month)) <= 4 {
			return month
		}
		return monthNamesRussianAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesRussian[int(t.Month())-1]
	}
	return string([]rune(monthNamesRussian[int(t.Month())-1])[:1])
}

// localMonthsNameSakha returns the Sakha name of the month.
func localMonthsNameSakha(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesSakhaAbbr[int(t.Month())-1]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesSakha[int(t.Month())-1]
	}
	return string([]rune(monthNamesSakha[(t.Month() - 1)])[:1])
}

// localMonthsNameSami returns the Sami name of the month.
func localMonthsNameSami(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesSamiAbbr[int(t.Month())-1]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesSami[int(t.Month())-1]
	}
	return string([]rune(monthNamesSami[(t.Month() - 1)])[:1])
}

// localMonthsNameSamiLule returns the Sami (Lule) name of the month.
func localMonthsNameSamiLule(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesSamiLuleAbbr[int(t.Month())-1]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesSamiLule[int(t.Month())-1]
	}
	return string([]rune(monthNamesSamiLule[(t.Month() - 1)])[:1])
}

// localMonthsNameSamiNorthern returns the Sami (Northern) name of the month.
func localMonthsNameSamiNorthern(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesSamiNorthernAbbr[(t.Month() - 1)]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesSamiNorthern[int(t.Month())-1]
	}
	return string([]rune(monthNamesSamiNorthern[(t.Month() - 1)])[:1])
}

// localMonthsNameSamiNorthernFI returns the Sami (Northern) Finland name of the
// month.
func localMonthsNameSamiNorthernFI(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesSamiNorthernAbbr[(t.Month() - 1)]
	}
	if abbr == 4 || abbr > 6 {
		if int(t.Month()) == 1 {
			return "ođđajagemánu"
		}
		return monthNamesSamiNorthern[int(t.Month())-1]
	}
	return string([]rune(monthNamesSamiNorthern[(t.Month() - 1)])[:1])
}

// localMonthsNameSamiSkolt returns the Sami (Skolt) name of the month.
func localMonthsNameSamiSkolt(t time.Time, abbr int) string {
	if abbr == 5 {
		return string([]rune(monthNamesSamiSkolt[(t.Month() - 1)])[:1])
	}
	return monthNamesSamiSkolt[int(t.Month())-1]
}

// localMonthsNameSamiSouthern returns the Sami (Southern) name of the month.
func localMonthsNameSamiSouthern(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesSamiSouthernAbbr[(t.Month() - 1)]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesSamiSouthern[int(t.Month())-1]
	}
	return string([]rune(monthNamesSamiSouthern[(t.Month() - 1)])[:1])
}

// localMonthsNameSanskrit returns the Sanskrit name of the month.
func localMonthsNameSanskrit(t time.Time, abbr int) string {
	if abbr == 5 {
		return string([]rune(monthNamesSanskrit[(t.Month() - 1)])[:1])
	}
	return monthNamesSanskrit[int(t.Month())-1]
}

// localMonthsNameScottishGaelic returns the Scottish Gaelic name of the month.
func localMonthsNameScottishGaelic(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesScottishGaelicAbbr[(t.Month() - 1)]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesScottishGaelic[int(t.Month())-1]
	}
	return string([]rune(monthNamesScottishGaelic[(t.Month() - 1)])[:1])
}

// localMonthsNameSerbian returns the Serbian (Cyrillic) name of the month.
func localMonthsNameSerbian(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesSerbianAbbr[(t.Month() - 1)]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesSerbian[int(t.Month())-1]
	}
	return string([]rune(monthNamesSerbian[(t.Month() - 1)])[:1])
}

// localMonthsNameSerbianBA returns the Serbian (Cyrillic) Bosnia and
// Herzegovina name of the month.
func localMonthsNameSerbianBA(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesSerbianBAAbbr[(t.Month() - 1)]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesSerbianBA[int(t.Month())-1]
	}
	return string([]rune(monthNamesSerbianBA[(t.Month() - 1)])[:1])
}

// localMonthsNameSerbianLatin returns the Serbian (Latin) name of the month.
func localMonthsNameSerbianLatin(t time.Time, abbr int) string {
	if abbr == 3 {
		return string([]rune(monthNamesSerbianLatin[(t.Month() - 1)])[:3])
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesSerbianLatin[int(t.Month())-1]
	}
	return string([]rune(monthNamesSerbianLatin[(t.Month() - 1)])[:1])
}

// localMonthsNameSerbianLatinCS returns the Serbian (Latin) name of the month.
func localMonthsNameSerbianLatinCS(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesSerbianLatinAbbr[(t.Month() - 1)]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesSerbianLatin[int(t.Month())-1]
	}
	return string([]rune(monthNamesSerbianLatin[(t.Month() - 1)])[:1])
}

// localMonthsNameSesothoSaLeboa returns the Sesotho sa Leboa name of the month.
func localMonthsNameSesothoSaLeboa(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesSesothoSaLeboaAbbr[(t.Month() - 1)]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesSesothoSaLeboa[int(t.Month())-1]
	}
	return string([]rune(monthNamesSesothoSaLeboa[(t.Month() - 1)])[:1])
}

// localMonthsNameSetswana returns the Setswana name of the month.
func localMonthsNameSetswana(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesSetswanaAbbr[(t.Month() - 1)]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesSetswana[int(t.Month())-1]
	}
	return string([]rune(monthNamesSetswana[(t.Month() - 1)])[:1])
}

// localMonthsNameSindhi returns the Sindhi name of the month.
func localMonthsNameSindhi(t time.Time, abbr int) string {
	if abbr == 5 {
		return string([]rune(monthNamesSindhi[(t.Month() - 1)])[:1])
	}
	return monthNamesSindhi[int(t.Month())-1]
}

// localMonthsNameSinhala returns the Sinhala name of the month.
func localMonthsNameSinhala(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesSinhalaAbbr[(t.Month() - 1)]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesSinhala[int(t.Month())-1]
	}
	return string([]rune(monthNamesSinhala[(t.Month() - 1)])[:1])
}

// localMonthsNameSlovak returns the Slovak name of the month.
func localMonthsNameSlovak(t time.Time, abbr int) string {
	if abbr == 3 {
		return strconv.Itoa(int(t.Month()))
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesSlovak[int(t.Month())-1]
	}
	return string([]rune(monthNamesSlovak[(t.Month() - 1)])[:1])
}

// localMonthsNameSlovenian returns the Slovenian name of the month.
func localMonthsNameSlovenian(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesSlovenianAbbr[(t.Month() - 1)]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesSlovenian[int(t.Month())-1]
	}
	return string([]rune(monthNamesSlovenian[(t.Month() - 1)])[:1])
}

// localMonthsNameSomali returns the Somali name of the month.
func localMonthsNameSomali(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesSomaliAbbr[(t.Month() - 1)]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesSomali[int(t.Month())-1]
	}
	return string([]rune(monthNamesSomali[(t.Month() - 1)])[:1])
}

// localMonthsNameSotho returns the Sotho name of the month.
func localMonthsNameSotho(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesSothoAbbr[(t.Month() - 1)]
	}
	if abbr == 4 || abbr > 6 {
		return monthNamesSotho[int(t.Month())-1]
	}
	return string([]rune(monthNamesSotho[(t.Month() - 1)])[:1])
}

// localMonthsNameSpanish returns the Spanish name of the month.
func localMonthsNameSpanish(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesSpanishAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesSpanish[int(t.Month())-1]
	}
	return monthNamesSpanishAbbr[int(t.Month())-1][:1]
}

// localMonthsNameSpanishPE returns the Spanish Peru name of the month.
func localMonthsNameSpanishPE(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesSpanishPEAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesSpanishPE[int(t.Month())-1]
	}
	return monthNamesSpanishPEAbbr[int(t.Month())-1][:1]
}

// localMonthsNameSwedish returns the Swedish name of the month.
func localMonthsNameSwedish(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesSwedishAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesSwedish[int(t.Month())-1]
	}
	return monthNamesSwedishAbbr[int(t.Month())-1][:1]
}

// localMonthsNameSwedishFI returns the Swedish Finland name of the month.
func localMonthsNameSwedishFI(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesSwedishFIAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesSwedish[int(t.Month())-1]
	}
	return monthNamesSwedishFIAbbr[int(t.Month())-1][:1]
}

// localMonthsNameSyriac returns the Syriac name of the month.
func localMonthsNameSyriac(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesSyriacAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesSyriac[int(t.Month())-1]
	}
	return string([]rune(monthNamesSyriac[(t.Month() - 1)])[:1])
}

// localMonthsNameTajik returns the Tajik name of the month.
func localMonthsNameTajik(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesTajikAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesTajik[int(t.Month())-1]
	}
	return string([]rune(monthNamesTajik[(t.Month() - 1)])[:1])
}

// localMonthsNameTamazight returns the Tamazight name of the month.
func localMonthsNameTamazight(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesTamazightAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesTamazight[int(t.Month())-1]
	}
	return string([]rune(monthNamesTamazight[(t.Month() - 1)])[:1])
}

// localMonthsNameTamil returns the Tamil name of the month.
func localMonthsNameTamil(t time.Time, abbr int) string {
	if abbr == 5 {
		return string([]rune(monthNamesTamil[(t.Month() - 1)])[:1])
	}
	return monthNamesTamil[int(t.Month())-1]
}

// localMonthsNameTamilLK returns the Tamil Sri Lanka name of the month.
func localMonthsNameTamilLK(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesTamilAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesTamil[int(t.Month())-1]
	}
	return string([]rune(monthNamesTamil[(t.Month() - 1)])[:1])
}

// localMonthsNameTatar returns the Tatar name of the month.
func localMonthsNameTatar(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesTatarAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesTatar[int(t.Month())-1]
	}
	return string([]rune(monthNamesTatar[(t.Month() - 1)])[:1])
}

// localMonthsNameTelugu returns the Telugu name of the month.
func localMonthsNameTelugu(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesTeluguAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesTelugu[int(t.Month())-1]
	}
	return string([]rune(monthNamesTelugu[(t.Month() - 1)])[:1])
}

// localMonthsNameSyllabics returns the Syllabics name of the month.
func localMonthsNameSyllabics(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesSyllabicsAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesSyllabics[int(t.Month())-1]
	}
	return string([]rune(monthNamesSyllabics[int(t.Month())-1])[:1])
}

// localMonthsNameThai returns the Thai name of the month.
func localMonthsNameThai(t time.Time, abbr int) string {
	if abbr == 3 {
		r := []rune(monthNamesThai[int(t.Month())-1])
		return string(r[:1]) + "." + string(r[len(r)-2:len(r)-1]) + "."
	}
	if abbr == 4 {
		return monthNamesThai[int(t.Month())-1]
	}
	return string([]rune(monthNamesThai[int(t.Month())-1])[:1])
}

// localMonthsNameTibetan returns the Tibetan name of the month.
func localMonthsNameTibetan(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesTibetanAbbr[int(t.Month())-1]
	}
	if abbr == 5 {
		if t.Month() == 10 {
			return "\u0f66"
		}
		return "\u0f5f"
	}
	return monthNamesTibetan[int(t.Month())-1]
}

// localMonthsNameTigrinya returns the Tigrinya name of the month.
func localMonthsNameTigrinya(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesTigrinyaAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesTigrinya[int(t.Month())-1]
	}
	return string([]rune(monthNamesTigrinya[(t.Month() - 1)])[:1])
}

// localMonthsNameTsonga returns the Tsonga name of the month.
func localMonthsNameTsonga(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesTsongaAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesTsonga[int(t.Month())-1]
	}
	return string([]rune(monthNamesTsonga[(t.Month() - 1)])[:1])
}

// localMonthsNameTraditionalMongolian returns the Traditional Mongolian name of
// the month.
func localMonthsNameTraditionalMongolian(t time.Time, abbr int) string {
	if abbr == 5 {
		return "M"
	}
	return monthNamesTradMongolian[t.Month()-1]
}

// localMonthsNameTurkish returns the Turkish name of the month.
func localMonthsNameTurkish(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesTurkishAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesTurkish[int(t.Month())-1]
	}
	return string([]rune(monthNamesTurkishAbbr[int(t.Month())-1])[:1])
}

// localMonthsNameTurkmen returns the Turkmen name of the month.
func localMonthsNameTurkmen(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesTurkmenAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesTurkmen[int(t.Month())-1]
	}
	return string([]rune(monthNamesTurkmenAbbr[int(t.Month())-1])[:1])
}

// localMonthsNameUkrainian returns the Ukrainian name of the month.
func localMonthsNameUkrainian(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesUkrainianAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesUkrainian[int(t.Month())-1]
	}
	return string([]rune(monthNamesUkrainian[int(t.Month())-1])[:1])
}

// localMonthsNameUpperSorbian returns the Upper Sorbian name of the month.
func localMonthsNameUpperSorbian(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesUpperSorbianAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesUpperSorbian[int(t.Month())-1]
	}
	return string([]rune(monthNamesUpperSorbian[int(t.Month())-1])[:1])
}

// localMonthsNameUyghur returns the Uyghur name of the month.
func localMonthsNameUyghur(t time.Time, abbr int) string {
	if abbr == 3 {
		return fmt.Sprintf("%d-\u0626\u0627\u064A", int(t.Month()))
	}
	if abbr == 4 {
		return monthNamesUyghur[int(t.Month())-1]
	}
	return string([]rune(monthNamesUyghur[int(t.Month())-1])[:1])
}

// localMonthsNameUzbek returns the Uzbek name of the month.
func localMonthsNameUzbek(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesUzbekAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesUzbek[int(t.Month())-1]
	}
	return string([]rune(monthNamesUzbek[int(t.Month())-1])[:1])
}

// localMonthsNameUzbekCyrillic returns the Uzbek (Cyrillic) name of the month.
func localMonthsNameUzbekCyrillic(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesTajikAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesTajik[int(t.Month())-1]
	}
	return string([]rune(monthNamesTajik[int(t.Month())-1])[:1])
}

// localMonthsNameValencian returns the Valencian name of the month.
func localMonthsNameValencian(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesValencianAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesValencian[int(t.Month())-1]
	}
	return string([]rune(monthNamesValencian[int(t.Month())-1])[:1])
}

// localMonthsNameVenda returns the Venda name of the month.
func localMonthsNameVenda(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesVendaAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesVenda[int(t.Month())-1]
	}
	return string([]rune(monthNamesVenda[int(t.Month())-1])[:1])
}

// localMonthsNameVietnamese returns the Vietnamese name of the month.
func localMonthsNameVietnamese(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesVietnameseAbbr3[t.Month()-1]
	}
	if abbr == 5 {
		return monthNamesVietnameseAbbr5[t.Month()-1]
	}
	return monthNamesVietnamese[t.Month()-1]
}

// localMonthsNameWelsh returns the Welsh name of the month.
func localMonthsNameWelsh(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesWelshAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesWelsh[int(t.Month())-1]
	}
	return monthNamesWelshAbbr[int(t.Month())-1][:1]
}

// localMonthsNameWolof returns the Wolof name of the month.
func localMonthsNameWolof(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesWolofAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesWolof[int(t.Month())-1]
	}
	return monthNamesWolof[int(t.Month())-1][:1]
}

// localMonthsNameXhosa returns the Xhosa name of the month.
func localMonthsNameXhosa(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesXhosaAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesXhosa[int(t.Month())-1]
	}
	return "u"
}

// localMonthsNameYi returns the Yi name of the month.
func localMonthsNameYi(t time.Time, abbr int) string {
	if abbr == 3 || abbr == 4 {
		return monthNamesYiSuffix[t.Month()-1]
	}
	return string([]rune(monthNamesYi[int(t.Month())-1])[:1])
}

// localMonthsNameYiddish returns the Yiddish name of the month.
func localMonthsNameYiddish(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesYiddishAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesYiddish[int(t.Month())-1]
	}
	return string([]rune(monthNamesYiddish[int(t.Month())-1])[:1])
}

// localMonthsNameYoruba returns the Yoruba name of the month.
func localMonthsNameYoruba(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesYorubaAbbr[int(t.Month())-1]
	}
	if abbr == 4 {
		return monthNamesYoruba[int(t.Month())-1]
	}
	return string([]rune(monthNamesYoruba[int(t.Month())-1])[:1])
}

// localMonthsNameZulu returns the Zulu name of the month.
func localMonthsNameZulu(t time.Time, abbr int) string {
	if abbr == 3 {
		return monthNamesZuluAbbr[t.Month()-1]
	}
	if abbr == 4 {
		return monthNamesZulu[int(t.Month())-1]
	}
	return monthNamesZuluAbbr[int(t.Month())-1][:1]
}

// localMonthsName return months name by supported language ID.
func (nf *numberFormat) localMonthsName(abbr int) string {
	if languageInfo, ok := getSupportedLanguageInfo(nf.localCode); ok {
		return languageInfo.localMonth(nf.t, abbr)
	}
	return localMonthsNameEnglish(nf.t, abbr)
}

// dateTimesHandler will be handling date and times types tokens for a number
// format expression.
func (nf *numberFormat) dateTimesHandler(i int, token nfp.Token) {
	if idx := inStrSlice(nfp.AmPm, strings.ToUpper(token.TValue), false); idx != -1 {
		if nf.ap == "" {
			nextHours := nf.hoursNext(i)
			aps := strings.Split(nf.localAmPm(token.TValue), "/")
			nf.ap = aps[0]
			if nextHours >= 12 {
				nf.ap = aps[1]
			}
		}
		nf.result += nf.ap
		return
	}
	if strings.Contains(strings.ToUpper(token.TValue), "M") {
		l := len(token.TValue)
		if l == 1 && nf.isMonthToken(i) {
			nf.result += strconv.Itoa(int(nf.t.Month()))
			return
		}
		if l == 2 && nf.isMonthToken(i) {
			nf.result += fmt.Sprintf("%02d", int(nf.t.Month()))
			return
		}
		if l == 3 {
			nf.result += nf.localMonthsName(3)
			return
		}
		if l == 4 || l > 5 {
			nf.result += nf.localMonthsName(4)
			return
		}
		if l == 5 {
			nf.result += nf.localMonthsName(5)
			return
		}
	}
	nf.yearsHandler(token)
	nf.daysHandler(token)
	nf.hoursHandler(i, token)
	nf.minutesHandler(token)
	nf.secondsHandler(token)
}

// eraYear convert time to the Japanese era years.
func eraYear(t time.Time) (int, int) {
	i, year := 0, -1
	for i = len(japaneseEraYears) - 1; i > 0; i-- {
		if y := japaneseEraYears[i]; t.After(y) {
			year = t.Year() - y.Year() + 1
			break
		}
	}
	return i, year
}

// japaneseYearHandler handling the Japanease calendar years.
func (nf *numberFormat) japaneseYearHandler(token nfp.Token, langInfo languageInfo) {
	if strings.Contains(strings.ToUpper(token.TValue), "G") {
		i, year := eraYear(nf.t)
		if year == -1 {
			return
		}
		nf.useGannen = langInfo.useGannen
		switch len(token.TValue) {
		case 1:
			nf.useGannen = false
			nf.result += japaneseEraSymbols[i]
		case 2:
			nf.result += japaneseEraNames[i][:3]
		default:
			nf.result += japaneseEraNames[i]
		}
	}
	if strings.Contains(strings.ToUpper(token.TValue), "E") {
		_, year := eraYear(nf.t)
		if year == -1 {
			nf.result += strconv.Itoa(nf.t.Year())
			return
		}
		if year == 1 && nf.useGannen {
			nf.result += "\u5143"
			return
		}
		if len(token.TValue) == 1 && !nf.useGannen {
			nf.result += strconv.Itoa(year)
			return
		}
		if len(token.TValue) == 2 {
			nf.result += fmt.Sprintf("%02d", year)
		}
	}
}

// republicOfChinaYearHandler handling the Republic of China calendar years.
func (nf *numberFormat) republicOfChinaYearHandler(token nfp.Token, langInfo languageInfo) {
	if strings.Contains(strings.ToUpper(token.TValue), "G") {
		year := nf.t.Year() - republicOfChinaYear.Year() + 1
		if year == 1 {
			nf.useGannen = langInfo.useGannen
		}
		var name string
		if name = republicOfChinaEraName[0]; len(token.TValue) < 3 {
			name = republicOfChinaEraName[1]
		}
		if year < 0 {
			name += republicOfChinaEraName[2]
		}
		nf.result += name
	}
	if strings.Contains(strings.ToUpper(token.TValue), "E") {
		year := nf.t.Year() - republicOfChinaYear.Year() + 1
		if year < 0 {
			year = republicOfChinaYear.Year() - nf.t.Year()
		}
		if year == 1 && nf.useGannen {
			nf.result += "\u5143"
			return
		}
		if len(token.TValue) == 1 && !nf.useGannen {
			nf.result += strconv.Itoa(year)
		}
	}
}

// yearsHandler will be handling years in the date and times types tokens for a
// number format expression.
func (nf *numberFormat) yearsHandler(token nfp.Token) {
	langInfo, _ := getSupportedLanguageInfo(nf.localCode)
	if strings.Contains(strings.ToUpper(token.TValue), "Y") {
		year := nf.t.Year()
		if nf.opts != nil && nf.opts.CultureInfo == CultureNameKoKR {
			year += 2333
		}
		if len(token.TValue) <= 2 {
			nf.result += strconv.Itoa(year)[2:]
			return
		}
		nf.result += strconv.Itoa(year)
		return
	}
	if inStrSlice(langInfo.tags, "zh-TW", false) != -1 ||
		nf.opts != nil && nf.opts.CultureInfo == CultureNameZhTW {
		nf.republicOfChinaYearHandler(token, langInfo)
		return
	}
	if inStrSlice(langInfo.tags, "ja-JP", false) != -1 ||
		nf.opts != nil && nf.opts.CultureInfo == CultureNameJaJP {
		nf.japaneseYearHandler(token, langInfo)
		return
	}
	if strings.Contains(strings.ToUpper(token.TValue), "E") {
		nf.result += strconv.Itoa(nf.t.Year())
		return
	}
}

// daysHandler will be handling days in the date and times types tokens for a
// number format expression.
func (nf *numberFormat) daysHandler(token nfp.Token) {
	info, _ := getSupportedLanguageInfo(nf.localCode)
	l := len(token.TValue)
	weekdayNames, weekdayNamesAbbr := info.weekdayNames, info.weekdayNamesAbbr
	if len(weekdayNames) != 7 {
		weekdayNames = weekdayNamesEnglish
	}
	if len(weekdayNamesAbbr) != 7 {
		weekdayNamesAbbr = weekdayNamesEnglishAbbr
	}
	if strings.Contains(strings.ToUpper(token.TValue), "A") {
		if l == 3 {
			nf.result += weekdayNamesAbbr[nf.t.Weekday()]
		}
		if l > 3 {
			nf.result += weekdayNames[nf.t.Weekday()]
		}
		return
	}
	if strings.Contains(strings.ToUpper(token.TValue), "D") {
		switch l {
		case 1:
			nf.result += strconv.Itoa(nf.t.Day())
		case 2:
			nf.result += fmt.Sprintf("%02d", nf.t.Day())
		case 3:
			nf.result += weekdayNamesAbbr[nf.t.Weekday()]
		default:
			nf.result += weekdayNames[nf.t.Weekday()]
		}
	}
}

// hoursHandler will be handling hours in the date and times types tokens for a
// number format expression.
func (nf *numberFormat) hoursHandler(i int, token nfp.Token) {
	if nf.hours = strings.Contains(strings.ToUpper(token.TValue), "H"); nf.hours {
		h := nf.t.Hour()
		ap, ok := nf.apNext(i)
		if ok {
			nf.ap = ap[0]
			if h >= 12 {
				nf.ap = ap[1]
			}
			if h > 12 {
				h -= 12
			}
		}
		if nf.ap != "" {
			if nf.hoursNext(i) == -1 && h > 12 {
				h -= 12
			}
			if h == 0 {
				h = 12
			}
		}
		switch len(token.TValue) {
		case 1:
			nf.result += strconv.Itoa(h)
			return
		default:
			nf.result += fmt.Sprintf("%02d", h)
			return
		}
	}
}

// minutesHandler will be handling minutes in the date and times types tokens
// for a number format expression.
func (nf *numberFormat) minutesHandler(token nfp.Token) {
	if strings.Contains(strings.ToUpper(token.TValue), "M") {
		nf.hours = false
		switch len(token.TValue) {
		case 1:
			nf.result += strconv.Itoa(nf.t.Minute())
			return
		default:
			nf.result += fmt.Sprintf("%02d", nf.t.Minute())
		}
	}
}

// secondsHandler will be handling seconds in the date and times types tokens
// for a number format expression.
func (nf *numberFormat) secondsHandler(token nfp.Token) {
	if nf.seconds = strings.Contains(strings.ToUpper(token.TValue), "S"); !nf.seconds {
		return
	}
	if len(token.TValue) == 1 {
		nf.result += strconv.Itoa(nf.t.Second())
		return
	}
	nf.result += fmt.Sprintf("%02d", nf.t.Second())
}

// elapsedDateTimesHandler will be handling elapsed date and times types tokens
// for a number format expression.
func (nf *numberFormat) elapsedDateTimesHandler(token nfp.Token) {
	if strings.Contains(strings.ToUpper(token.TValue), "H") {
		nf.result += fmt.Sprintf("%.f", math.Floor(nf.t.Sub(excel1900Epoc).Hours()))
		return
	}
	if strings.Contains(strings.ToUpper(token.TValue), "M") {
		nf.result += fmt.Sprintf("%.f", math.Floor(nf.t.Sub(excel1900Epoc).Minutes()))
		return
	}
	if strings.Contains(strings.ToUpper(token.TValue), "S") {
		nf.result += fmt.Sprintf("%.f", math.Floor(nf.t.Sub(excel1900Epoc).Seconds()))
		return
	}
}

// hoursNext detects if a token of type hours exists after a given tokens list.
func (nf *numberFormat) hoursNext(i int) int {
	tokens := nf.section[nf.sectionIdx].Items
	for idx := i + 1; idx < len(tokens); idx++ {
		if tokens[idx].TType == nfp.TokenTypeDateTimes {
			if strings.Contains(strings.ToUpper(tokens[idx].TValue), "H") {
				t := timeFromExcelTime(nf.number, false)
				return t.Hour()
			}
		}
	}
	return -1
}

// apNext detects if a token of type AM/PM exists after a given tokens list.
func (nf *numberFormat) apNext(i int) ([]string, bool) {
	tokens := nf.section[nf.sectionIdx].Items
	for idx := i + 1; idx < len(tokens); idx++ {
		if tokens[idx].TType == nfp.TokenTypeDateTimes {
			if strings.Contains(strings.ToUpper(tokens[idx].TValue), "H") {
				return nil, false
			}
			if i := inStrSlice(nfp.AmPm, tokens[idx].TValue, false); i != -1 {
				return strings.Split(nf.localAmPm(tokens[idx].TValue), "/"), true
			}
		}
	}
	return nil, false
}

// isMonthToken detects if the given token represents minutes, if no hours and
// seconds tokens before the given token or not seconds after the given token,
// the current token is a minutes token.
func (nf *numberFormat) isMonthToken(i int) bool {
	tokens := nf.section[nf.sectionIdx].Items
	var timePrevious, secondsNext bool
	for idx := i - 1; idx >= 0; idx-- {
		if tokens[idx].TType == nfp.TokenTypeDateTimes {
			timePrevious = strings.ContainsAny(strings.ToUpper(tokens[idx].TValue), "HS")
			break
		}
		if tokens[idx].TType == nfp.TokenTypeElapsedDateTimes {
			timePrevious = true
			break
		}
	}
	for idx := i + 1; idx < len(tokens); idx++ {
		if tokens[idx].TType == nfp.TokenTypeDateTimes {
			secondsNext = strings.Contains(strings.ToUpper(tokens[idx].TValue), "S")
			break
		}
	}
	return !(timePrevious || secondsNext)
}

// negativeHandler will be handling negative selection for a number format
// expression.
func (nf *numberFormat) negativeHandler() (result string) {
	for _, token := range nf.section[nf.sectionIdx].Items {
		if inStrSlice(supportedTokenTypes, token.TType, true) == -1 || token.TType == nfp.TokenTypeGeneral {
			return nf.value
		}
		if inStrSlice(supportedDateTimeTokenTypes, token.TType, true) != -1 {
			return nf.value
		}
	}
	return nf.numberHandler()
}

// textHandler will be handling text selection for a number format expression.
func (nf *numberFormat) textHandler() (result string) {
	for _, token := range nf.section[nf.sectionIdx].Items {
		if token.TType == nfp.TokenTypeLiteral {
			result += token.TValue
		}
		if token.TType == nfp.TokenTypeTextPlaceHolder || token.TType == nfp.TokenTypeZeroPlaceHolder {
			result += nf.value
		}
	}
	return result
}

// getValueSectionType returns its applicable number format expression section
// based on the given value.
func (nf *numberFormat) getValueSectionType(value string) (float64, string) {
	if nf.cellType != CellTypeNumber && nf.cellType != CellTypeDate {
		return 0, nfp.TokenSectionText
	}
	isNum, _, _ := isNumeric(value)
	if !isNum {
		return 0, nfp.TokenSectionText
	}
	number, _ := strconv.ParseFloat(value, 64)
	if number >= 0 {
		return number, nfp.TokenSectionPositive
	}
	var hasNeg bool
	for _, sec := range nf.section {
		if sec.Type == nfp.TokenSectionNegative {
			hasNeg = true
		}
	}
	if !hasNeg {
		nf.usePositive = true
		return number, nfp.TokenSectionPositive
	}
	return number, nfp.TokenSectionNegative
}
