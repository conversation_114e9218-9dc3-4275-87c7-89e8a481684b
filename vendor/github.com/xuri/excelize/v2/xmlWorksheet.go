// Copyright 2016 - 2025 The excelize Authors. All rights reserved. Use of
// this source code is governed by a BSD-style license that can be found in
// the LICENSE file.
//
// Package excelize providing a set of functions that allow you to write to and
// read from XLAM / XLSM / XLSX / XLTM / XLTX files. Supports reading and
// writing spreadsheet documents generated by Microsoft Excel™ 2007 and later.
// Supports complex components by high compatibility, and provided streaming
// API for generating or reading data from a worksheet with huge amounts of
// data. This library needs Go version 1.23 or later.

package excelize

import (
	"encoding/xml"
	"sync"
)

// xlsxWorksheet directly maps the worksheet element in the namespace
// http://schemas.openxmlformats.org/spreadsheetml/2006/main.
type xlsxWorksheet struct {
	mu                     sync.Mutex
	formulaSI              sync.Map
	XMLName                xml.Name                     `xml:"http://schemas.openxmlformats.org/spreadsheetml/2006/main worksheet"`
	SheetPr                *xlsxSheetPr                 `xml:"sheetPr"`
	Dimension              *xlsxDimension               `xml:"dimension"`
	SheetViews             *xlsxSheetViews              `xml:"sheetViews"`
	SheetFormatPr          *xlsxSheetFormatPr           `xml:"sheetFormatPr"`
	Cols                   *xlsxCols                    `xml:"cols"`
	SheetData              xlsxSheetData                `xml:"sheetData"`
	SheetCalcPr            *xlsxInnerXML                `xml:"sheetCalcPr"`
	SheetProtection        *xlsxSheetProtection         `xml:"sheetProtection"`
	ProtectedRanges        *xlsxInnerXML                `xml:"protectedRanges"`
	Scenarios              *xlsxInnerXML                `xml:"scenarios"`
	AutoFilter             *xlsxAutoFilter              `xml:"autoFilter"`
	SortState              *xlsxSortState               `xml:"sortState"`
	DataConsolidate        *xlsxInnerXML                `xml:"dataConsolidate"`
	CustomSheetViews       *xlsxCustomSheetViews        `xml:"customSheetViews"`
	MergeCells             *xlsxMergeCells              `xml:"mergeCells"`
	PhoneticPr             *xlsxPhoneticPr              `xml:"phoneticPr"`
	ConditionalFormatting  []*xlsxConditionalFormatting `xml:"conditionalFormatting"`
	DataValidations        *xlsxDataValidations         `xml:"dataValidations"`
	Hyperlinks             *xlsxHyperlinks              `xml:"hyperlinks"`
	PrintOptions           *xlsxPrintOptions            `xml:"printOptions"`
	PageMargins            *xlsxPageMargins             `xml:"pageMargins"`
	PageSetUp              *xlsxPageSetUp               `xml:"pageSetup"`
	HeaderFooter           *xlsxHeaderFooter            `xml:"headerFooter"`
	RowBreaks              *xlsxRowBreaks               `xml:"rowBreaks"`
	ColBreaks              *xlsxColBreaks               `xml:"colBreaks"`
	CustomProperties       *xlsxInnerXML                `xml:"customProperties"`
	CellWatches            *xlsxInnerXML                `xml:"cellWatches"`
	IgnoredErrors          *xlsxIgnoredErrors           `xml:"ignoredErrors"`
	SmartTags              *xlsxInnerXML                `xml:"smartTags"`
	Drawing                *xlsxDrawing                 `xml:"drawing"`
	LegacyDrawing          *xlsxLegacyDrawing           `xml:"legacyDrawing"`
	LegacyDrawingHF        *xlsxLegacyDrawingHF         `xml:"legacyDrawingHF"`
	DrawingHF              *xlsxDrawingHF               `xml:"drawingHF"`
	Picture                *xlsxPicture                 `xml:"picture"`
	OleObjects             *xlsxInnerXML                `xml:"oleObjects"`
	Controls               *xlsxInnerXML                `xml:"controls"`
	WebPublishItems        *xlsxInnerXML                `xml:"webPublishItems"`
	AlternateContent       *xlsxAlternateContent        `xml:"mc:AlternateContent"`
	TableParts             *xlsxTableParts              `xml:"tableParts"`
	ExtLst                 *xlsxExtLst                  `xml:"extLst"`
	DecodeAlternateContent *xlsxInnerXML                `xml:"http://schemas.openxmlformats.org/markup-compatibility/2006 AlternateContent"`
}

// xlsxDrawing change r:id to rid in the namespace.
type xlsxDrawing struct {
	XMLName xml.Name `xml:"drawing"`
	RID     string   `xml:"http://schemas.openxmlformats.org/officeDocument/2006/relationships id,attr,omitempty"`
}

// xlsxHeaderFooter directly maps the headerFooter element in the namespace
// http://schemas.openxmlformats.org/spreadsheetml/2006/main - When printed or
// viewed in page layout view (§18.18.69), each page of a worksheet can have a
// page header, a page footer, or both. The headers and footers on odd-numbered
// pages can differ from those on even-numbered pages, and the headers and
// footers on the first page can differ from those on odd- and even-numbered
// pages. In the latter case, the first page is not considered an odd page.
type xlsxHeaderFooter struct {
	XMLName          xml.Name `xml:"headerFooter"`
	DifferentOddEven bool     `xml:"differentOddEven,attr,omitempty"`
	DifferentFirst   bool     `xml:"differentFirst,attr,omitempty"`
	ScaleWithDoc     *bool    `xml:"scaleWithDoc,attr"`
	AlignWithMargins *bool    `xml:"alignWithMargins,attr"`
	OddHeader        string   `xml:"oddHeader,omitempty"`
	OddFooter        string   `xml:"oddFooter,omitempty"`
	EvenHeader       string   `xml:"evenHeader,omitempty"`
	EvenFooter       string   `xml:"evenFooter,omitempty"`
	FirstHeader      string   `xml:"firstHeader,omitempty"`
	FirstFooter      string   `xml:"firstFooter,omitempty"`
}

// xlsxDrawingHF (Drawing Reference in Header Footer) specifies the usage of
// drawing objects to be rendered in the headers and footers of the sheet. It
// specifies an explicit relationship to the part containing the DrawingML
// shapes used in the headers and footers. It also indicates where in the
// headers and footers each shape belongs. One drawing object can appear in
// each of the left section, center section and right section of a header and
// a footer.
type xlsxDrawingHF struct {
	Content string `xml:",innerxml"`
}

// xlsxPageSetUp directly maps the pageSetup element in the namespace
// http://schemas.openxmlformats.org/spreadsheetml/2006/main - Page setup
// settings for the worksheet.
type xlsxPageSetUp struct {
	XMLName            xml.Name `xml:"pageSetup"`
	BlackAndWhite      bool     `xml:"blackAndWhite,attr,omitempty"`
	CellComments       string   `xml:"cellComments,attr,omitempty"`
	Copies             int      `xml:"copies,attr,omitempty"`
	Draft              bool     `xml:"draft,attr,omitempty"`
	Errors             string   `xml:"errors,attr,omitempty"`
	FirstPageNumber    string   `xml:"firstPageNumber,attr,omitempty"`
	FitToHeight        *int     `xml:"fitToHeight,attr"`
	FitToWidth         *int     `xml:"fitToWidth,attr"`
	HorizontalDPI      string   `xml:"horizontalDpi,attr,omitempty"`
	RID                string   `xml:"http://schemas.openxmlformats.org/officeDocument/2006/relationships id,attr,omitempty"`
	Orientation        string   `xml:"orientation,attr,omitempty"`
	PageOrder          string   `xml:"pageOrder,attr,omitempty"`
	PaperHeight        string   `xml:"paperHeight,attr,omitempty"`
	PaperSize          *int     `xml:"paperSize,attr"`
	PaperWidth         string   `xml:"paperWidth,attr,omitempty"`
	Scale              int      `xml:"scale,attr,omitempty"`
	UseFirstPageNumber bool     `xml:"useFirstPageNumber,attr,omitempty"`
	UsePrinterDefaults bool     `xml:"usePrinterDefaults,attr,omitempty"`
	VerticalDPI        string   `xml:"verticalDpi,attr,omitempty"`
}

// xlsxPrintOptions directly maps the printOptions element in the namespace
// http://schemas.openxmlformats.org/spreadsheetml/2006/main - Print options for
// the sheet. Printer-specific settings are stored separately in the Printer
// Settings part.
type xlsxPrintOptions struct {
	XMLName            xml.Name `xml:"printOptions"`
	GridLines          bool     `xml:"gridLines,attr,omitempty"`
	GridLinesSet       bool     `xml:"gridLinesSet,attr,omitempty"`
	Headings           bool     `xml:"headings,attr,omitempty"`
	HorizontalCentered bool     `xml:"horizontalCentered,attr,omitempty"`
	VerticalCentered   bool     `xml:"verticalCentered,attr,omitempty"`
}

// xlsxPageMargins directly maps the pageMargins element in the namespace
// http://schemas.openxmlformats.org/spreadsheetml/2006/main - Page margins for
// a sheet or a custom sheet view.
type xlsxPageMargins struct {
	XMLName xml.Name `xml:"pageMargins"`
	Left    float64  `xml:"left,attr"`
	Right   float64  `xml:"right,attr"`
	Top     float64  `xml:"top,attr"`
	Bottom  float64  `xml:"bottom,attr"`
	Header  float64  `xml:"header,attr"`
	Footer  float64  `xml:"footer,attr"`
}

// xlsxSheetFormatPr directly maps the sheetFormatPr element in the namespace
// http://schemas.openxmlformats.org/spreadsheetml/2006/main. This element
// specifies the sheet formatting properties.
type xlsxSheetFormatPr struct {
	XMLName          xml.Name `xml:"sheetFormatPr"`
	BaseColWidth     uint8    `xml:"baseColWidth,attr,omitempty"`
	DefaultColWidth  float64  `xml:"defaultColWidth,attr,omitempty"`
	DefaultRowHeight float64  `xml:"defaultRowHeight,attr"`
	CustomHeight     bool     `xml:"customHeight,attr,omitempty"`
	ZeroHeight       bool     `xml:"zeroHeight,attr,omitempty"`
	ThickTop         bool     `xml:"thickTop,attr,omitempty"`
	ThickBottom      bool     `xml:"thickBottom,attr,omitempty"`
	OutlineLevelRow  uint8    `xml:"outlineLevelRow,attr,omitempty"`
	OutlineLevelCol  uint8    `xml:"outlineLevelCol,attr,omitempty"`
}

// xlsxSheetViews represents worksheet views collection.
type xlsxSheetViews struct {
	XMLName   xml.Name        `xml:"sheetViews"`
	SheetView []xlsxSheetView `xml:"sheetView"`
}

// xlsxSheetView represents a single sheet view definition. When more than one
// sheet view is defined in the file, it means that when opening the workbook,
// each sheet view corresponds to a separate window within the spreadsheet
// application, where each window is showing the particular sheet containing
// the same workbookViewId value, the last sheetView definition is loaded, and
// the others are discarded. When multiple windows are viewing the same sheet,
// multiple sheetView elements (with corresponding workbookView entries) are
// saved.
type xlsxSheetView struct {
	WindowProtection         bool             `xml:"windowProtection,attr,omitempty"`
	ShowFormulas             bool             `xml:"showFormulas,attr,omitempty"`
	ShowGridLines            *bool            `xml:"showGridLines,attr"`
	ShowRowColHeaders        *bool            `xml:"showRowColHeaders,attr"`
	ShowZeros                *bool            `xml:"showZeros,attr,omitempty"`
	RightToLeft              bool             `xml:"rightToLeft,attr,omitempty"`
	TabSelected              bool             `xml:"tabSelected,attr,omitempty"`
	ShowRuler                *bool            `xml:"showRuler,attr,omitempty"`
	ShowWhiteSpace           *bool            `xml:"showWhiteSpace,attr"`
	ShowOutlineSymbols       bool             `xml:"showOutlineSymbols,attr,omitempty"`
	DefaultGridColor         *bool            `xml:"defaultGridColor,attr"`
	View                     string           `xml:"view,attr,omitempty"`
	TopLeftCell              string           `xml:"topLeftCell,attr,omitempty"`
	ColorID                  int              `xml:"colorId,attr,omitempty"`
	ZoomScale                float64          `xml:"zoomScale,attr,omitempty"`
	ZoomScaleNormal          float64          `xml:"zoomScaleNormal,attr,omitempty"`
	ZoomScalePageLayoutView  float64          `xml:"zoomScalePageLayoutView,attr,omitempty"`
	ZoomScaleSheetLayoutView float64          `xml:"zoomScaleSheetLayoutView,attr,omitempty"`
	WorkbookViewID           int              `xml:"workbookViewId,attr"`
	Pane                     *xlsxPane        `xml:"pane,omitempty"`
	Selection                []*xlsxSelection `xml:"selection"`
}

// xlsxSelection directly maps the selection element in the namespace
// http://schemas.openxmlformats.org/spreadsheetml/2006/main - Worksheet view
// selection.
type xlsxSelection struct {
	ActiveCell   string `xml:"activeCell,attr,omitempty"`
	ActiveCellID *int   `xml:"activeCellId,attr"`
	Pane         string `xml:"pane,attr,omitempty"`
	SQRef        string `xml:"sqref,attr,omitempty"`
}

// xlsxSelection directly maps the selection element. Worksheet view pane.
type xlsxPane struct {
	ActivePane  string  `xml:"activePane,attr,omitempty"`
	State       string  `xml:"state,attr,omitempty"` // Either "split" or "frozen"
	TopLeftCell string  `xml:"topLeftCell,attr,omitempty"`
	XSplit      float64 `xml:"xSplit,attr,omitempty"`
	YSplit      float64 `xml:"ySplit,attr,omitempty"`
}

// xlsxSheetPr directly maps the sheetPr element in the namespace
// http://schemas.openxmlformats.org/spreadsheetml/2006/main - Sheet-level
// properties.
type xlsxSheetPr struct {
	XMLName                           xml.Name         `xml:"sheetPr"`
	SyncHorizontal                    bool             `xml:"syncHorizontal,attr,omitempty"`
	SyncVertical                      bool             `xml:"syncVertical,attr,omitempty"`
	SyncRef                           string           `xml:"syncRef,attr,omitempty"`
	TransitionEvaluation              bool             `xml:"transitionEvaluation,attr,omitempty"`
	TransitionEntry                   bool             `xml:"transitionEntry,attr,omitempty"`
	Published                         *bool            `xml:"published,attr"`
	CodeName                          string           `xml:"codeName,attr,omitempty"`
	FilterMode                        bool             `xml:"filterMode,attr,omitempty"`
	EnableFormatConditionsCalculation *bool            `xml:"enableFormatConditionsCalculation,attr"`
	TabColor                          *xlsxColor       `xml:"tabColor"`
	OutlinePr                         *xlsxOutlinePr   `xml:"outlinePr"`
	PageSetUpPr                       *xlsxPageSetUpPr `xml:"pageSetUpPr"`
}

// xlsxOutlinePr maps to the outlinePr element. SummaryBelow allows you to
// adjust the direction of grouper controls.
type xlsxOutlinePr struct {
	ApplyStyles        *bool `xml:"applyStyles,attr"`
	SummaryBelow       *bool `xml:"summaryBelow,attr"`
	SummaryRight       *bool `xml:"summaryRight,attr"`
	ShowOutlineSymbols *bool `xml:"showOutlineSymbols,attr"`
}

// xlsxPageSetUpPr expresses page setup properties of the worksheet.
type xlsxPageSetUpPr struct {
	AutoPageBreaks bool `xml:"autoPageBreaks,attr,omitempty"`
	FitToPage      bool `xml:"fitToPage,attr,omitempty"`
}

// xlsxCols defines column width and column formatting for one or more columns
// of the worksheet.
type xlsxCols struct {
	XMLName xml.Name  `xml:"cols"`
	Col     []xlsxCol `xml:"col"`
}

// xlsxCol directly maps the col (Column Width & Formatting). Defines column
// width and column formatting for one or more columns of the worksheet.
type xlsxCol struct {
	BestFit      bool     `xml:"bestFit,attr,omitempty"`
	Collapsed    bool     `xml:"collapsed,attr,omitempty"`
	CustomWidth  bool     `xml:"customWidth,attr,omitempty"`
	Hidden       bool     `xml:"hidden,attr,omitempty"`
	Max          int      `xml:"max,attr"`
	Min          int      `xml:"min,attr"`
	OutlineLevel uint8    `xml:"outlineLevel,attr,omitempty"`
	Phonetic     bool     `xml:"phonetic,attr,omitempty"`
	Style        int      `xml:"style,attr,omitempty"`
	Width        *float64 `xml:"width,attr"`
}

// xlsxDimension directly maps the dimension element in the namespace
// http://schemas.openxmlformats.org/spreadsheetml/2006/main - This element
// specifies the used range of the worksheet. It specifies the row and column
// bounds of used cells in the worksheet. This is optional and is not
// required. Used cells include cells with formulas, text content, and cell
// formatting. When an entire column is formatted, only the first cell in that
// column is considered used.
type xlsxDimension struct {
	XMLName xml.Name `xml:"dimension"`
	Ref     string   `xml:"ref,attr"`
}

// xlsxSheetData collection represents the cell table itself. This collection
// expresses information about each cell, grouped together by rows in the
// worksheet.
type xlsxSheetData struct {
	XMLName xml.Name  `xml:"sheetData"`
	Row     []xlsxRow `xml:"row"`
}

// xlsxRow directly maps the row element. The element expresses information
// about an entire row of a worksheet, and contains all cell definitions for a
// particular row in the worksheet.
type xlsxRow struct {
	C            []xlsxC  `xml:"c"`
	R            int      `xml:"r,attr,omitempty"`
	Spans        string   `xml:"spans,attr,omitempty"`
	S            int      `xml:"s,attr,omitempty"`
	CustomFormat bool     `xml:"customFormat,attr,omitempty"`
	Ht           *float64 `xml:"ht,attr"`
	Hidden       bool     `xml:"hidden,attr,omitempty"`
	CustomHeight bool     `xml:"customHeight,attr,omitempty"`
	OutlineLevel uint8    `xml:"outlineLevel,attr,omitempty"`
	Collapsed    bool     `xml:"collapsed,attr,omitempty"`
	ThickTop     bool     `xml:"thickTop,attr,omitempty"`
	ThickBot     bool     `xml:"thickBot,attr,omitempty"`
	Ph           bool     `xml:"ph,attr,omitempty"`
}

// xlsxSortState directly maps the sortState element. This collection
// preserves the AutoFilter sort state.
type xlsxSortState struct {
	ColumnSort    bool   `xml:"columnSort,attr,omitempty"`
	CaseSensitive bool   `xml:"caseSensitive,attr,omitempty"`
	SortMethod    string `xml:"sortMethod,attr,omitempty"`
	Ref           string `xml:"ref,attr"`
	Content       string `xml:",innerxml"`
}

// xlsxCustomSheetViews directly maps the customSheetViews element. This is a
// collection of custom sheet views.
type xlsxCustomSheetViews struct {
	XMLName         xml.Name               `xml:"customSheetViews"`
	CustomSheetView []*xlsxCustomSheetView `xml:"customSheetView"`
}

// xlsxBrk directly maps the row or column break to use when paginating a
// worksheet.
type xlsxBrk struct {
	ID  int  `xml:"id,attr,omitempty"`
	Min int  `xml:"min,attr,omitempty"`
	Max int  `xml:"max,attr,omitempty"`
	Man bool `xml:"man,attr,omitempty"`
	Pt  bool `xml:"pt,attr,omitempty"`
}

// xlsxRowBreaks directly maps a collection of the row breaks.
type xlsxRowBreaks struct {
	XMLName xml.Name `xml:"rowBreaks"`
	xlsxBreaks
}

// xlsxRowBreaks directly maps a collection of the column breaks.
type xlsxColBreaks struct {
	XMLName xml.Name `xml:"colBreaks"`
	xlsxBreaks
}

// xlsxBreaks directly maps a collection of the row or column breaks.
type xlsxBreaks struct {
	Brk              []*xlsxBrk `xml:"brk"`
	Count            int        `xml:"count,attr,omitempty"`
	ManualBreakCount int        `xml:"manualBreakCount,attr,omitempty"`
}

// xlsxCustomSheetView directly maps the customSheetView element.
type xlsxCustomSheetView struct {
	Pane           *xlsxPane         `xml:"pane"`
	Selection      *xlsxSelection    `xml:"selection"`
	RowBreaks      *xlsxBreaks       `xml:"rowBreaks"`
	ColBreaks      *xlsxBreaks       `xml:"colBreaks"`
	PageMargins    *xlsxPageMargins  `xml:"pageMargins"`
	PrintOptions   *xlsxPrintOptions `xml:"printOptions"`
	PageSetup      *xlsxPageSetUp    `xml:"pageSetup"`
	HeaderFooter   *xlsxHeaderFooter `xml:"headerFooter"`
	AutoFilter     *xlsxAutoFilter   `xml:"autoFilter"`
	ExtLst         *xlsxExtLst       `xml:"extLst"`
	GUID           string            `xml:"guid,attr"`
	Scale          int               `xml:"scale,attr,omitempty"`
	ColorID        int               `xml:"colorId,attr,omitempty"`
	ShowPageBreaks bool              `xml:"showPageBreaks,attr,omitempty"`
	ShowFormulas   bool              `xml:"showFormulas,attr,omitempty"`
	ShowGridLines  bool              `xml:"showGridLines,attr,omitempty"`
	ShowRowCol     bool              `xml:"showRowCol,attr,omitempty"`
	OutlineSymbols bool              `xml:"outlineSymbols,attr,omitempty"`
	ZeroValues     bool              `xml:"zeroValues,attr,omitempty"`
	FitToPage      bool              `xml:"fitToPage,attr,omitempty"`
	PrintArea      bool              `xml:"printArea,attr,omitempty"`
	Filter         bool              `xml:"filter,attr,omitempty"`
	ShowAutoFilter bool              `xml:"showAutoFilter,attr,omitempty"`
	HiddenRows     bool              `xml:"hiddenRows,attr,omitempty"`
	HiddenColumns  bool              `xml:"hiddenColumns,attr,omitempty"`
	State          string            `xml:"state,attr,omitempty"`
	FilterUnique   bool              `xml:"filterUnique,attr,omitempty"`
	View           string            `xml:"view,attr,omitempty"`
	ShowRuler      bool              `xml:"showRuler,attr,omitempty"`
	TopLeftCell    string            `xml:"topLeftCell,attr,omitempty"`
}

// xlsxMergeCell directly maps the mergeCell element. A single merged cell.
type xlsxMergeCell struct {
	Ref  string `xml:"ref,attr,omitempty"`
	rect []int
}

// xlsxMergeCells directly maps the mergeCells element. This collection
// expresses all the merged cells in the sheet.
type xlsxMergeCells struct {
	XMLName xml.Name         `xml:"mergeCells"`
	Count   int              `xml:"count,attr,omitempty"`
	Cells   []*xlsxMergeCell `xml:"mergeCell,omitempty"`
}

// xlsxDataValidations expresses all data validation information for cells in a
// sheet which have data validation features applied.
type xlsxDataValidations struct {
	XMLName        xml.Name              `xml:"dataValidations"`
	Count          int                   `xml:"count,attr,omitempty"`
	DisablePrompts bool                  `xml:"disablePrompts,attr,omitempty"`
	XWindow        int                   `xml:"xWindow,attr,omitempty"`
	YWindow        int                   `xml:"yWindow,attr,omitempty"`
	DataValidation []*xlsxDataValidation `xml:"dataValidation"`
}

// DataValidation directly maps the single item of data validation defined
// on a range of the worksheet.
type xlsxDataValidation struct {
	AllowBlank       bool          `xml:"allowBlank,attr"`
	Error            *string       `xml:"error,attr"`
	ErrorStyle       *string       `xml:"errorStyle,attr"`
	ErrorTitle       *string       `xml:"errorTitle,attr"`
	Operator         string        `xml:"operator,attr,omitempty"`
	Prompt           *string       `xml:"prompt,attr"`
	PromptTitle      *string       `xml:"promptTitle,attr"`
	ShowDropDown     bool          `xml:"showDropDown,attr,omitempty"`
	ShowErrorMessage bool          `xml:"showErrorMessage,attr,omitempty"`
	ShowInputMessage bool          `xml:"showInputMessage,attr,omitempty"`
	Sqref            string        `xml:"sqref,attr"`
	XMSqref          string        `xml:"sqref,omitempty"`
	Type             string        `xml:"type,attr,omitempty"`
	Formula1         *xlsxInnerXML `xml:"formula1"`
	Formula2         *xlsxInnerXML `xml:"formula2"`
}

// xlsxC collection represents a cell in the worksheet. Information about the
// cell's location (reference), value, data type, formatting, and formula is
// expressed here.
//
// This simple type is restricted to the values listed in the following table:
//
//	 Enumeration Value         | Description
//	---------------------------+---------------------------------
//	 b (Boolean)               | Cell containing a boolean.
//	 d (Date)                  | Cell contains a date in the ISO 8601 format.
//	 e (Error)                 | Cell containing an error.
//	 inlineStr (Inline String) | Cell containing an (inline) rich string, i.e.,
//	                           | one not in the shared string table. If this
//	                           | cell type is used, then the cell value is in
//	                           | the is element rather than the v element in
//	                           | the cell (c element).
//	 n (Number)                | Cell containing a number.
//	 s (Shared String)         | Cell containing a shared string.
//	 str (String)              | Cell containing a formula string.
type xlsxC struct {
	XMLName  xml.Name `xml:"c"`
	XMLSpace xml.Attr `xml:"space,attr,omitempty"`
	R        string   `xml:"r,attr,omitempty"` // Cell ID, e.g. A1
	S        int      `xml:"s,attr,omitempty"` // Style reference
	T        string   `xml:"t,attr,omitempty"` // Type
	Cm       *uint    `xml:"cm,attr"`
	Vm       *uint    `xml:"vm,attr"`
	Ph       *bool    `xml:"ph,attr"`
	F        *xlsxF   `xml:"f"`           // Formula
	V        string   `xml:"v,omitempty"` // Value
	IS       *xlsxSI  `xml:"is"`
	f        string
}

// xlsxF represents a formula for the cell. The formula expression is
// contained in the character node of this element.
type xlsxF struct {
	Content string `xml:",chardata"`
	T       string `xml:"t,attr,omitempty"` // Formula type
	Aca     bool   `xml:"aca,attr,omitempty"`
	Ref     string `xml:"ref,attr,omitempty"` // Shared formula ref
	Dt2D    bool   `xml:"dt2D,attr,omitempty"`
	Dtr     bool   `xml:"dtr,attr,omitempty"`
	Del1    bool   `xml:"del1,attr,omitempty"`
	Del2    bool   `xml:"del2,attr,omitempty"`
	R1      string `xml:"r1,attr,omitempty"`
	R2      string `xml:"r2,attr,omitempty"`
	Ca      bool   `xml:"ca,attr,omitempty"`
	Si      *int   `xml:"si,attr"` // Shared formula index
	Bx      bool   `xml:"bx,attr,omitempty"`
}

// xlsxSheetProtection collection expresses the sheet protection options to
// enforce when the sheet is protected.
type xlsxSheetProtection struct {
	XMLName             xml.Name `xml:"sheetProtection"`
	AlgorithmName       string   `xml:"algorithmName,attr,omitempty"`
	Password            string   `xml:"password,attr,omitempty"`
	HashValue           string   `xml:"hashValue,attr,omitempty"`
	SaltValue           string   `xml:"saltValue,attr,omitempty"`
	SpinCount           int      `xml:"spinCount,attr,omitempty"`
	Sheet               bool     `xml:"sheet,attr"`
	Objects             bool     `xml:"objects,attr"`
	Scenarios           bool     `xml:"scenarios,attr"`
	FormatCells         bool     `xml:"formatCells,attr"`
	FormatColumns       bool     `xml:"formatColumns,attr"`
	FormatRows          bool     `xml:"formatRows,attr"`
	InsertColumns       bool     `xml:"insertColumns,attr"`
	InsertRows          bool     `xml:"insertRows,attr"`
	InsertHyperlinks    bool     `xml:"insertHyperlinks,attr"`
	DeleteColumns       bool     `xml:"deleteColumns,attr"`
	DeleteRows          bool     `xml:"deleteRows,attr"`
	SelectLockedCells   bool     `xml:"selectLockedCells,attr"`
	Sort                bool     `xml:"sort,attr"`
	AutoFilter          bool     `xml:"autoFilter,attr"`
	PivotTables         bool     `xml:"pivotTables,attr"`
	SelectUnlockedCells bool     `xml:"selectUnlockedCells,attr"`
}

// xlsxPhoneticPr (Phonetic Properties) represents a collection of phonetic
// properties that affect the display of phonetic text for this String Item
// (si). Phonetic text is used to give hints as to the pronunciation of an East
// Asian language, and the hints are displayed as text within the spreadsheet
// cells across the top portion of the cell. Since the phonetic hints are text,
// every phonetic hint is expressed as a phonetic run (rPh), and these
// properties specify how to display that phonetic run.
type xlsxPhoneticPr struct {
	XMLName   xml.Name `xml:"phoneticPr"`
	Alignment string   `xml:"alignment,attr,omitempty"`
	FontID    *int     `xml:"fontId,attr"`
	Type      string   `xml:"type,attr,omitempty"`
}

// A Conditional Format is a format, such as cell shading or font color, that a
// spreadsheet application can automatically apply to cells if a specified
// condition is true. This collection expresses conditional formatting rules
// applied to a particular cell or range.
type xlsxConditionalFormatting struct {
	XMLName xml.Name      `xml:"conditionalFormatting"`
	Pivot   bool          `xml:"pivot,attr,omitempty"`
	SQRef   string        `xml:"sqref,attr,omitempty"`
	CfRule  []*xlsxCfRule `xml:"cfRule"`
}

// xlsxCfRule (Conditional Formatting Rule) represents a description of a
// conditional formatting rule.
type xlsxCfRule struct {
	Type         string          `xml:"type,attr,omitempty"`
	DxfID        *int            `xml:"dxfId,attr"`
	Priority     int             `xml:"priority,attr,omitempty"`
	StopIfTrue   bool            `xml:"stopIfTrue,attr,omitempty"`
	AboveAverage *bool           `xml:"aboveAverage,attr"`
	Percent      bool            `xml:"percent,attr,omitempty"`
	Bottom       bool            `xml:"bottom,attr,omitempty"`
	Operator     string          `xml:"operator,attr,omitempty"`
	Text         string          `xml:"text,attr,omitempty"`
	TimePeriod   string          `xml:"timePeriod,attr,omitempty"`
	Rank         int             `xml:"rank,attr,omitempty"`
	StdDev       int             `xml:"stdDev,attr,omitempty"`
	EqualAverage bool            `xml:"equalAverage,attr,omitempty"`
	Formula      []string        `xml:"formula,omitempty"`
	ColorScale   *xlsxColorScale `xml:"colorScale"`
	DataBar      *xlsxDataBar    `xml:"dataBar"`
	IconSet      *xlsxIconSet    `xml:"iconSet"`
	ExtLst       *xlsxExtLst     `xml:"extLst"`
}

// xlsxColorScale (Color Scale) describes a gradated color scale in this
// conditional formatting rule.
type xlsxColorScale struct {
	Cfvo  []*xlsxCfvo  `xml:"cfvo"`
	Color []*xlsxColor `xml:"color"`
}

// dataBar (Data Bar) describes a data bar conditional formatting rule.
type xlsxDataBar struct {
	MaxLength int          `xml:"maxLength,attr,omitempty"`
	MinLength int          `xml:"minLength,attr,omitempty"`
	ShowValue *bool        `xml:"showValue,attr"`
	Cfvo      []*xlsxCfvo  `xml:"cfvo"`
	Color     []*xlsxColor `xml:"color"`
}

// xlsxIconSet (Icon Set) describes an icon set conditional formatting rule.
type xlsxIconSet struct {
	Cfvo      []*xlsxCfvo `xml:"cfvo"`
	IconSet   string      `xml:"iconSet,attr,omitempty"`
	ShowValue *bool       `xml:"showValue,attr"`
	Percent   bool        `xml:"percent,attr,omitempty"`
	Reverse   bool        `xml:"reverse,attr,omitempty"`
}

// cfvo (Conditional Format Value Object) describes the values of the
// interpolation points in a gradient scale.
type xlsxCfvo struct {
	Gte    bool        `xml:"gte,attr,omitempty"`
	Type   string      `xml:"type,attr,omitempty"`
	Val    string      `xml:"val,attr,omitempty"`
	ExtLst *xlsxExtLst `xml:"extLst"`
}

// xlsxHyperlinks directly maps the hyperlinks element in the namespace
// http://schemas.openxmlformats.org/spreadsheetml/2006/main - A hyperlink can
// be stored in a package as a relationship. Hyperlinks shall be identified by
// containing a target which specifies the destination of the given hyperlink.
type xlsxHyperlinks struct {
	XMLName   xml.Name        `xml:"hyperlinks"`
	Hyperlink []xlsxHyperlink `xml:"hyperlink"`
}

// xlsxHyperlink directly maps the hyperlink element in the namespace
// http://schemas.openxmlformats.org/spreadsheetml/2006/main
type xlsxHyperlink struct {
	Ref      string `xml:"ref,attr"`
	Location string `xml:"location,attr,omitempty"`
	Display  string `xml:"display,attr,omitempty"`
	Tooltip  string `xml:"tooltip,attr,omitempty"`
	RID      string `xml:"http://schemas.openxmlformats.org/officeDocument/2006/relationships id,attr,omitempty"`
}

// xlsxTableParts directly maps the tableParts element in the namespace
// http://schemas.openxmlformats.org/spreadsheetml/2006/main - The table element
// has several attributes applied to identify the table and the data range it
// covers. The table id attribute needs to be unique across all table parts, the
// same goes for the name and displayName. The displayName has the further
// restriction that it must be unique across all defined names in the workbook.
// Later on we will see that you can define names for many elements, such as
// cells or formulas. The name value is used for the object model in Microsoft
// Office Excel. The displayName is used for references in formulas. The ref
// attribute is used to identify the cell range that the table covers. This
// includes not only the table data, but also the table header containing column
// names.
// To add columns to your table you add new tableColumn elements to the
// tableColumns container. Similar to the shared string table the collection
// keeps a count attribute identifying the number of columns. Besides the table
// definition in the table part there is also the need to identify which tables
// are displayed in the worksheet. The worksheet part has a separate element
// tableParts to store this information. Each table part is referenced through
// the relationship ID and again a count of the number of table parts is
// maintained. The following markup sample is taken from the documents
// accompanying this book. The sheet data element has been removed to reduce the
// size of the sample. To reference the table, just add the tableParts element,
// of course after having created and stored the table part. For example:
//
//	   <worksheet xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main">
//	       ...
//	       <tableParts count="1">
//			      <tablePart r:id="rId1" />
//	       </tableParts>
//	   </worksheet>
type xlsxTableParts struct {
	XMLName    xml.Name         `xml:"tableParts"`
	Count      int              `xml:"count,attr,omitempty"`
	TableParts []*xlsxTablePart `xml:"tablePart"`
}

// xlsxTablePart directly maps the tablePart element in the namespace
// http://schemas.openxmlformats.org/spreadsheetml/2006/main
type xlsxTablePart struct {
	RID string `xml:"http://schemas.openxmlformats.org/officeDocument/2006/relationships id,attr,omitempty"`
}

// xlsxPicture directly maps the picture element in the namespace
// http://schemas.openxmlformats.org/spreadsheetml/2006/main - Background sheet
// image. For example:
//
//	<picture r:id="rId1"/>
type xlsxPicture struct {
	XMLName xml.Name `xml:"picture"`
	RID     string   `xml:"http://schemas.openxmlformats.org/officeDocument/2006/relationships id,attr,omitempty"`
}

// xlsxIgnoredError specifies a single ignored error for a range of cells.
type xlsxIgnoredError struct {
	XMLName            xml.Name `xml:"ignoredError"`
	Sqref              string   `xml:"sqref,attr"`
	EvalError          bool     `xml:"evalError,attr,omitempty"`
	TwoDigitTextYear   bool     `xml:"twoDigitTextYear,attr,omitempty"`
	NumberStoredAsText bool     `xml:"numberStoredAsText,attr,omitempty"`
	Formula            bool     `xml:"formula,attr,omitempty"`
	FormulaRange       bool     `xml:"formulaRange,attr,omitempty"`
	UnlockedFormula    bool     `xml:"unlockedFormula,attr,omitempty"`
	EmptyCellReference bool     `xml:"emptyCellReference,attr,omitempty"`
	ListDataValidation bool     `xml:"listDataValidation,attr,omitempty"`
	CalculatedColumn   bool     `xml:"calculatedColumn,attr,omitempty"`
}

// xlsxIgnoredErrors specifies a collection of ignored errors, by cell range.
type xlsxIgnoredErrors struct {
	XMLName      xml.Name           `xml:"ignoredErrors"`
	IgnoredError []xlsxIgnoredError `xml:"ignoredError"`
	ExtLst       *xlsxExtLst        `xml:"extLst"`
}

// xlsxLegacyDrawing directly maps the legacyDrawing element in the namespace
// http://schemas.openxmlformats.org/spreadsheetml/2006/main - A comment is a
// rich text note that is attached to, and associated with, a cell, separate
// from other cell content. Comment content is stored separate from the cell,
// and is displayed in a drawing object (like a text box) that is separate from,
// but associated with, a cell. Comments are used as reminders, such as noting
// how a complex formula works, or to provide feedback to other users. Comments
// can also be used to explain assumptions made in a formula or to call out
// something special about the cell.
type xlsxLegacyDrawing struct {
	XMLName xml.Name `xml:"legacyDrawing"`
	RID     string   `xml:"http://schemas.openxmlformats.org/officeDocument/2006/relationships id,attr,omitempty"`
}

// xlsxLegacyDrawingHF specifies the explicit relationship to the part
// containing the VML defining pictures rendered in the header / footer of the
// sheet.
type xlsxLegacyDrawingHF struct {
	XMLName xml.Name `xml:"legacyDrawingHF"`
	RID     string   `xml:"http://schemas.openxmlformats.org/officeDocument/2006/relationships id,attr,omitempty"`
}

// decodeX14SparklineGroups directly maps the sparklineGroups element.
type decodeX14SparklineGroups struct {
	XMLName xml.Name `xml:"sparklineGroups"`
	XMLNSXM string   `xml:"xmlns:xm,attr"`
	Content string   `xml:",innerxml"`
}

// decodeX14ConditionalFormattingExt directly maps the ext element.
type decodeX14ConditionalFormattingExt struct {
	XMLName xml.Name `xml:"ext"`
	ID      string   `xml:"id"`
}

// decodeX14ConditionalFormattings directly maps the conditionalFormattings
// element.
type decodeX14ConditionalFormattings struct {
	XMLName xml.Name `xml:"conditionalFormattings"`
	XMLNSXM string   `xml:"xmlns:xm,attr"`
	Content string   `xml:",innerxml"`
}

// decodeX14ConditionalFormattingRules directly maps the conditionalFormattings
// element.
type decodeX14ConditionalFormattingRules struct {
	XMLName xml.Name                         `xml:"conditionalFormattings"`
	XMLNSXM string                           `xml:"xmlns:xm,attr"`
	CondFmt []decodeX14ConditionalFormatting `xml:"conditionalFormatting"`
}

// decodeX14ConditionalFormatting directly maps the conditionalFormatting
// element.
type decodeX14ConditionalFormatting struct {
	XMLName xml.Name           `xml:"conditionalFormatting"`
	CfRule  []*decodeX14CfRule `xml:"cfRule"`
}

// decodeX14CfRule directly maps the cfRule element.
type decodeX14CfRule struct {
	XMLName xml.Name          `xml:"cfRule"`
	Type    string            `xml:"type,attr,omitempty"`
	ID      string            `xml:"id,attr,omitempty"`
	DataBar *decodeX14DataBar `xml:"dataBar"`
}

// decodeX14DataBar directly maps the dataBar element.
type decodeX14DataBar struct {
	XMLName           xml.Name    `xml:"dataBar"`
	MaxLength         int         `xml:"maxLength,attr"`
	MinLength         int         `xml:"minLength,attr"`
	Border            bool        `xml:"border,attr,omitempty"`
	Gradient          *bool       `xml:"gradient,attr"`
	ShowValue         bool        `xml:"showValue,attr,omitempty"`
	Direction         string      `xml:"direction,attr,omitempty"`
	Cfvo              []*xlsxCfvo `xml:"cfvo"`
	BorderColor       *xlsxColor  `xml:"borderColor"`
	NegativeFillColor *xlsxColor  `xml:"negativeFillColor"`
	AxisColor         *xlsxColor  `xml:"axisColor"`
}

// xlsxX14ConditionalFormattings directly maps the conditionalFormattings
// element.
type xlsxX14ConditionalFormattings struct {
	XMLName xml.Name `xml:"x14:conditionalFormattings"`
	Content string   `xml:",innerxml"`
}

// xlsxX14ConditionalFormatting directly maps the conditionalFormatting element.
type xlsxX14ConditionalFormatting struct {
	XMLName xml.Name         `xml:"x14:conditionalFormatting"`
	XMLNSXM string           `xml:"xmlns:xm,attr"`
	CfRule  []*xlsxX14CfRule `xml:"x14:cfRule"`
}

// xlsxX14CfRule directly maps the cfRule element.
type xlsxX14CfRule struct {
	Type    string         `xml:"type,attr,omitempty"`
	ID      string         `xml:"id,attr,omitempty"`
	DataBar *xlsx14DataBar `xml:"x14:dataBar"`
}

// xlsx14DataBar directly maps the dataBar element.
type xlsx14DataBar struct {
	MaxLength         int         `xml:"maxLength,attr"`
	MinLength         int         `xml:"minLength,attr"`
	Border            bool        `xml:"border,attr"`
	Gradient          bool        `xml:"gradient,attr"`
	ShowValue         bool        `xml:"showValue,attr,omitempty"`
	Direction         string      `xml:"direction,attr,omitempty"`
	Cfvo              []*xlsxCfvo `xml:"x14:cfvo"`
	BorderColor       *xlsxColor  `xml:"x14:borderColor"`
	NegativeFillColor *xlsxColor  `xml:"x14:negativeFillColor"`
	AxisColor         *xlsxColor  `xml:"x14:axisColor"`
}

// xlsxX14SparklineGroups directly maps the sparklineGroups element.
type xlsxX14SparklineGroups struct {
	XMLName         xml.Name                 `xml:"x14:sparklineGroups"`
	XMLNSXM         string                   `xml:"xmlns:xm,attr"`
	SparklineGroups []*xlsxX14SparklineGroup `xml:"x14:sparklineGroup"`
	Content         string                   `xml:",innerxml"`
}

// xlsxX14SparklineGroup directly maps the sparklineGroup element.
type xlsxX14SparklineGroup struct {
	XMLName             xml.Name          `xml:"x14:sparklineGroup"`
	ManualMax           int               `xml:"manualMax,attr,omitempty"`
	ManualMin           int               `xml:"manualMin,attr,omitempty"`
	LineWeight          float64           `xml:"lineWeight,attr,omitempty"`
	Type                string            `xml:"type,attr,omitempty"`
	DateAxis            bool              `xml:"dateAxis,attr,omitempty"`
	DisplayEmptyCellsAs string            `xml:"displayEmptyCellsAs,attr,omitempty"`
	Markers             bool              `xml:"markers,attr,omitempty"`
	High                bool              `xml:"high,attr,omitempty"`
	Low                 bool              `xml:"low,attr,omitempty"`
	First               bool              `xml:"first,attr,omitempty"`
	Last                bool              `xml:"last,attr,omitempty"`
	Negative            bool              `xml:"negative,attr,omitempty"`
	DisplayXAxis        bool              `xml:"displayXAxis,attr,omitempty"`
	DisplayHidden       bool              `xml:"displayHidden,attr,omitempty"`
	MinAxisType         string            `xml:"minAxisType,attr,omitempty"`
	MaxAxisType         string            `xml:"maxAxisType,attr,omitempty"`
	RightToLeft         bool              `xml:"rightToLeft,attr,omitempty"`
	ColorSeries         *xlsxColor        `xml:"x14:colorSeries"`
	ColorNegative       *xlsxColor        `xml:"x14:colorNegative"`
	ColorAxis           *xlsxColor        `xml:"x14:colorAxis"`
	ColorMarkers        *xlsxColor        `xml:"x14:colorMarkers"`
	ColorFirst          *xlsxColor        `xml:"x14:colorFirst"`
	ColorLast           *xlsxColor        `xml:"x14:colorLast"`
	ColorHigh           *xlsxColor        `xml:"x14:colorHigh"`
	ColorLow            *xlsxColor        `xml:"x14:colorLow"`
	Sparklines          xlsxX14Sparklines `xml:"x14:sparklines"`
}

// xlsxX14Sparklines directly maps the sparklines element.
type xlsxX14Sparklines struct {
	Sparkline []*xlsxX14Sparkline `xml:"x14:sparkline"`
}

// xlsxX14Sparkline directly maps the sparkline element.
type xlsxX14Sparkline struct {
	F     string `xml:"xm:f"`
	Sqref string `xml:"xm:sqref"`
}

// DataValidation directly maps the settings of the data validation rule.
type DataValidation struct {
	AllowBlank       bool
	Error            *string
	ErrorStyle       *string
	ErrorTitle       *string
	Operator         string
	Prompt           *string
	PromptTitle      *string
	ShowDropDown     bool
	ShowErrorMessage bool
	ShowInputMessage bool
	Sqref            string
	Type             string
	Formula1         string
	Formula2         string
}

// SparklineOptions directly maps the settings of the sparkline.
type SparklineOptions struct {
	Location      []string
	Range         []string
	Max           int
	CustMax       int
	Min           int
	CustMin       int
	Type          string
	Weight        float64
	DateAxis      bool
	Markers       bool
	High          bool
	Low           bool
	First         bool
	Last          bool
	Negative      bool
	Axis          bool
	Hidden        bool
	Reverse       bool
	Style         int
	SeriesColor   string
	NegativeColor string
	MarkersColor  string
	FirstColor    string
	LastColor     string
	HightColor    string
	LowColor      string
	EmptyCells    string
}

// Selection directly maps the settings of the worksheet selection.
type Selection struct {
	SQRef      string
	ActiveCell string
	Pane       string
}

// Panes directly maps the settings of the panes.
type Panes struct {
	Freeze      bool
	Split       bool
	XSplit      int
	YSplit      int
	TopLeftCell string
	ActivePane  string
	Selection   []Selection
}

// ConditionalFormatOptions directly maps the conditional format settings of the cells.
type ConditionalFormatOptions struct {
	Type           string
	AboveAverage   bool
	Percent        bool
	Format         *int
	Criteria       string
	Value          string
	MinType        string
	MidType        string
	MaxType        string
	MinValue       string
	MidValue       string
	MaxValue       string
	MinColor       string
	MidColor       string
	MaxColor       string
	BarColor       string
	BarBorderColor string
	BarDirection   string
	BarOnly        bool
	BarSolid       bool
	IconStyle      string
	ReverseIcons   bool
	IconsOnly      bool
	StopIfTrue     bool
}

// SheetProtectionOptions directly maps the settings of worksheet protection.
type SheetProtectionOptions struct {
	AlgorithmName       string
	AutoFilter          bool
	DeleteColumns       bool
	DeleteRows          bool
	EditObjects         bool
	EditScenarios       bool
	FormatCells         bool
	FormatColumns       bool
	FormatRows          bool
	InsertColumns       bool
	InsertHyperlinks    bool
	InsertRows          bool
	Password            string
	PivotTables         bool
	SelectLockedCells   bool
	SelectUnlockedCells bool
	Sort                bool
}

// HeaderFooterOptions directly maps the settings of header and footer.
type HeaderFooterOptions struct {
	AlignWithMargins *bool
	DifferentFirst   bool
	DifferentOddEven bool
	ScaleWithDoc     *bool
	OddHeader        string
	OddFooter        string
	EvenHeader       string
	EvenFooter       string
	FirstHeader      string
	FirstFooter      string
}

// PageLayoutMarginsOptions directly maps the settings of page layout margins.
type PageLayoutMarginsOptions struct {
	Bottom       *float64
	Footer       *float64
	Header       *float64
	Left         *float64
	Right        *float64
	Top          *float64
	Horizontally *bool
	Vertically   *bool
}

// PageLayoutOptions directly maps the settings of page layout.
type PageLayoutOptions struct {
	// Size defines the paper size of the worksheet.
	Size *int
	// Orientation defines the orientation of page layout for a worksheet.
	Orientation *string
	// FirstPageNumber specified the first printed page number. If no value is
	// specified, then 'automatic' is assumed.
	FirstPageNumber *uint
	// AdjustTo defines the print scaling. This attribute is restricted to
	// value ranging from 10 (10%) to 400 (400%). This setting is overridden
	// when fitToWidth and/or fitToHeight are in use.
	AdjustTo *uint
	// FitToHeight specified the number of vertical pages to fit on.
	FitToHeight *int
	// FitToWidth specified the number of horizontal pages to fit on.
	FitToWidth *int
	// BlackAndWhite specified print black and white.
	BlackAndWhite *bool
	// PageOrder specifies the ordering of multiple pages. Values
	// accepted: overThenDown and downThenOver
	PageOrder *string
}

// ViewOptions directly maps the settings of sheet view.
type ViewOptions struct {
	// DefaultGridColor indicating that the consuming application should use
	// the default grid lines color(system dependent). Overrides any color
	// specified in colorId.
	DefaultGridColor *bool
	// RightToLeft indicating whether the sheet is in 'right to left' display
	// mode. When in this mode, Column A is on the far right, Column B; is one
	// column left of Column A, and so on. Also, information in cells is
	// displayed in the Right to Left format.
	RightToLeft *bool
	// ShowFormulas indicating whether this sheet should display formulas.
	ShowFormulas *bool
	// ShowGridLines indicating whether this sheet should display grid lines.
	ShowGridLines *bool
	// ShowRowColHeaders indicating whether the sheet should display row and
	// column headings.
	ShowRowColHeaders *bool
	// ShowRuler indicating this sheet should display ruler.
	ShowRuler *bool
	// ShowZeros indicating whether to "show a zero in cells that have zero
	// value". When using a formula to reference another cell which is empty,
	// the referenced value becomes 0 when the flag is true. (Default setting
	// is true.)
	ShowZeros *bool
	// TopLeftCell specifies a location of the top left visible cell Location
	// of the top left visible cell in the bottom right pane (when in
	// Left-to-Right mode).
	TopLeftCell *string
	// View indicating how sheet is displayed, by default it uses empty string
	// available options: normal, pageLayout, pageBreakPreview
	View *string
	// ZoomScale specifies a window zoom magnification for current view
	// representing percent values. This attribute is restricted to values
	// ranging from 10 to 400. Horizontal & Vertical scale together.
	ZoomScale *float64
}

// SheetPropsOptions provides a function to set worksheet properties. There 4
// kinds of presets "Custom Scaling Options" in the spreadsheet applications, if
// you need to set those kind of scaling options, please using the
// "SetSheetProps" and "SetPageLayout" functions to approach these 4 scaling
// options:
//
// 1. No Scaling (Print sheets at their actual size):
//
//	disable := false
//	if err := f.SetSheetProps("Sheet1", &excelize.SheetPropsOptions{
//	    FitToPage: &disable,
//	}); err != nil {
//	    fmt.Println(err)
//	}
//
// 2. Fit Sheet on One Page (Shrink the printout so that it fits on one page):
//
//	enable := true
//	if err := f.SetSheetProps("Sheet1", &excelize.SheetPropsOptions{
//	    FitToPage: &enable,
//	}); err != nil {
//	    fmt.Println(err)
//	}
//
// 3. Fit All Columns on One Page (Shrink the printout so that it is one page
// wide):
//
//	enable, zero := true, 0
//	if err := f.SetSheetProps("Sheet1", &excelize.SheetPropsOptions{
//	    FitToPage: &enable,
//	}); err != nil {
//	    fmt.Println(err)
//	}
//	if err := f.SetPageLayout("Sheet1", &excelize.PageLayoutOptions{
//	    FitToHeight: &zero,
//	}); err != nil {
//	    fmt.Println(err)
//	}
//
// 4. Fit All Rows on One Page (Shrink the printout so that it is one page
// high):
//
//	enable, zero := true, 0
//	if err := f.SetSheetProps("Sheet1", &excelize.SheetPropsOptions{
//	    FitToPage: &enable,
//	}); err != nil {
//	    fmt.Println(err)
//	}
//	if err := f.SetPageLayout("Sheet1", &excelize.PageLayoutOptions{
//	    FitToWidth: &zero,
//	}); err != nil {
//	    fmt.Println(err)
//	}
type SheetPropsOptions struct {
	// Specifies a stable name of the sheet, which should not change over time,
	// and does not change from user input. This name should be used by code
	// to reference a particular sheet.
	CodeName *string
	// EnableFormatConditionsCalculation indicating whether the conditional
	// formatting calculations shall be evaluated. If set to false, then the
	// min/max values of color scales or data bars or threshold values in Top N
	// rules shall not be updated. Essentially the conditional
	// formatting "calc" is off.
	EnableFormatConditionsCalculation *bool
	// Published indicating whether the worksheet is published.
	Published *bool
	// AutoPageBreaks indicating whether the sheet displays Automatic Page
	// Breaks.
	AutoPageBreaks *bool
	// FitToPage indicating whether the Fit to Page print option is enabled.
	FitToPage *bool
	// TabColorIndexed represents the indexed color value.
	TabColorIndexed *int
	// TabColorRGB represents the standard Alpha Red Green Blue color value.
	TabColorRGB *string
	// TabColorTheme represents the zero-based index into the collection,
	// referencing a particular value expressed in the Theme part.
	TabColorTheme *int
	// TabColorTint specifies the tint value applied to the color.
	TabColorTint *float64
	// OutlineSummaryBelow indicating whether summary rows appear below detail
	// in an outline, when applying an outline.
	OutlineSummaryBelow *bool
	// OutlineSummaryRight indicating whether summary columns appear to the
	// right of detail in an outline, when applying an outline.
	OutlineSummaryRight *bool
	// BaseColWidth specifies the number of characters of the maximum digit
	// width of the normal style's font. This value does not include margin
	// padding or extra padding for grid lines. It is only the number of
	// characters.
	BaseColWidth *uint8
	// DefaultColWidth specifies the default column width measured as the
	// number of characters of the maximum digit width of the normal style's
	// font.
	DefaultColWidth *float64
	// DefaultRowHeight specifies the default row height measured in point
	// size. Optimization so we don't have to write the height on all rows.
	// This can be written out if most rows have custom height, to achieve the
	// optimization.
	DefaultRowHeight *float64
	// CustomHeight specifies the custom height.
	CustomHeight *bool
	// ZeroHeight specifies if rows are hidden.
	ZeroHeight *bool
	// ThickTop specifies if rows have a thick top border by default.
	ThickTop *bool
	// ThickBottom specifies if rows have a thick bottom border by default.
	ThickBottom *bool
}
