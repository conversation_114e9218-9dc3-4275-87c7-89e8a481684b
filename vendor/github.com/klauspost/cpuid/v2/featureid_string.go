// Code generated by "stringer -type=FeatureID,Vendor"; DO NOT EDIT.

package cpuid

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[ADX-1]
	_ = x[AESNI-2]
	_ = x[AMD3DNOW-3]
	_ = x[AMD3DNOWEXT-4]
	_ = x[AMXBF16-5]
	_ = x[AMXFP16-6]
	_ = x[AMXINT8-7]
	_ = x[AMXFP8-8]
	_ = x[AMXTILE-9]
	_ = x[AMXTF32-10]
	_ = x[AMXCOMPLEX-11]
	_ = x[AMXTRANSPOSE-12]
	_ = x[APX_F-13]
	_ = x[AVX-14]
	_ = x[AVX10-15]
	_ = x[AVX10_128-16]
	_ = x[AVX10_256-17]
	_ = x[AVX10_512-18]
	_ = x[AVX2-19]
	_ = x[AVX512BF16-20]
	_ = x[AVX512BITALG-21]
	_ = x[AVX512BW-22]
	_ = x[AVX512CD-23]
	_ = x[AVX512DQ-24]
	_ = x[AVX512ER-25]
	_ = x[AVX512F-26]
	_ = x[AVX512FP16-27]
	_ = x[AVX512IFMA-28]
	_ = x[AVX512PF-29]
	_ = x[AVX512VBMI-30]
	_ = x[AVX512VBMI2-31]
	_ = x[AVX512VL-32]
	_ = x[AVX512VNNI-33]
	_ = x[AVX512VP2INTERSECT-34]
	_ = x[AVX512VPOPCNTDQ-35]
	_ = x[AVXIFMA-36]
	_ = x[AVXNECONVERT-37]
	_ = x[AVXSLOW-38]
	_ = x[AVXVNNI-39]
	_ = x[AVXVNNIINT8-40]
	_ = x[AVXVNNIINT16-41]
	_ = x[BHI_CTRL-42]
	_ = x[BMI1-43]
	_ = x[BMI2-44]
	_ = x[CETIBT-45]
	_ = x[CETSS-46]
	_ = x[CLDEMOTE-47]
	_ = x[CLMUL-48]
	_ = x[CLZERO-49]
	_ = x[CMOV-50]
	_ = x[CMPCCXADD-51]
	_ = x[CMPSB_SCADBS_SHORT-52]
	_ = x[CMPXCHG8-53]
	_ = x[CPBOOST-54]
	_ = x[CPPC-55]
	_ = x[CX16-56]
	_ = x[EFER_LMSLE_UNS-57]
	_ = x[ENQCMD-58]
	_ = x[ERMS-59]
	_ = x[F16C-60]
	_ = x[FLUSH_L1D-61]
	_ = x[FMA3-62]
	_ = x[FMA4-63]
	_ = x[FP128-64]
	_ = x[FP256-65]
	_ = x[FSRM-66]
	_ = x[FXSR-67]
	_ = x[FXSROPT-68]
	_ = x[GFNI-69]
	_ = x[HLE-70]
	_ = x[HRESET-71]
	_ = x[HTT-72]
	_ = x[HWA-73]
	_ = x[HYBRID_CPU-74]
	_ = x[HYPERVISOR-75]
	_ = x[IA32_ARCH_CAP-76]
	_ = x[IA32_CORE_CAP-77]
	_ = x[IBPB-78]
	_ = x[IBPB_BRTYPE-79]
	_ = x[IBRS-80]
	_ = x[IBRS_PREFERRED-81]
	_ = x[IBRS_PROVIDES_SMP-82]
	_ = x[IBS-83]
	_ = x[IBSBRNTRGT-84]
	_ = x[IBSFETCHSAM-85]
	_ = x[IBSFFV-86]
	_ = x[IBSOPCNT-87]
	_ = x[IBSOPCNTEXT-88]
	_ = x[IBSOPSAM-89]
	_ = x[IBSRDWROPCNT-90]
	_ = x[IBSRIPINVALIDCHK-91]
	_ = x[IBS_FETCH_CTLX-92]
	_ = x[IBS_OPDATA4-93]
	_ = x[IBS_OPFUSE-94]
	_ = x[IBS_PREVENTHOST-95]
	_ = x[IBS_ZEN4-96]
	_ = x[IDPRED_CTRL-97]
	_ = x[INT_WBINVD-98]
	_ = x[INVLPGB-99]
	_ = x[KEYLOCKER-100]
	_ = x[KEYLOCKERW-101]
	_ = x[LAHF-102]
	_ = x[LAM-103]
	_ = x[LBRVIRT-104]
	_ = x[LZCNT-105]
	_ = x[MCAOVERFLOW-106]
	_ = x[MCDT_NO-107]
	_ = x[MCOMMIT-108]
	_ = x[MD_CLEAR-109]
	_ = x[MMX-110]
	_ = x[MMXEXT-111]
	_ = x[MOVBE-112]
	_ = x[MOVDIR64B-113]
	_ = x[MOVDIRI-114]
	_ = x[MOVSB_ZL-115]
	_ = x[MOVU-116]
	_ = x[MPX-117]
	_ = x[MSRIRC-118]
	_ = x[MSRLIST-119]
	_ = x[MSR_PAGEFLUSH-120]
	_ = x[NRIPS-121]
	_ = x[NX-122]
	_ = x[OSXSAVE-123]
	_ = x[PCONFIG-124]
	_ = x[POPCNT-125]
	_ = x[PPIN-126]
	_ = x[PREFETCHI-127]
	_ = x[PSFD-128]
	_ = x[RDPRU-129]
	_ = x[RDRAND-130]
	_ = x[RDSEED-131]
	_ = x[RDTSCP-132]
	_ = x[RRSBA_CTRL-133]
	_ = x[RTM-134]
	_ = x[RTM_ALWAYS_ABORT-135]
	_ = x[SBPB-136]
	_ = x[SERIALIZE-137]
	_ = x[SEV-138]
	_ = x[SEV_64BIT-139]
	_ = x[SEV_ALTERNATIVE-140]
	_ = x[SEV_DEBUGSWAP-141]
	_ = x[SEV_ES-142]
	_ = x[SEV_RESTRICTED-143]
	_ = x[SEV_SNP-144]
	_ = x[SGX-145]
	_ = x[SGXLC-146]
	_ = x[SHA-147]
	_ = x[SME-148]
	_ = x[SME_COHERENT-149]
	_ = x[SM3_X86-150]
	_ = x[SM4_X86-151]
	_ = x[SPEC_CTRL_SSBD-152]
	_ = x[SRBDS_CTRL-153]
	_ = x[SRSO_MSR_FIX-154]
	_ = x[SRSO_NO-155]
	_ = x[SRSO_USER_KERNEL_NO-156]
	_ = x[SSE-157]
	_ = x[SSE2-158]
	_ = x[SSE3-159]
	_ = x[SSE4-160]
	_ = x[SSE42-161]
	_ = x[SSE4A-162]
	_ = x[SSSE3-163]
	_ = x[STIBP-164]
	_ = x[STIBP_ALWAYSON-165]
	_ = x[STOSB_SHORT-166]
	_ = x[SUCCOR-167]
	_ = x[SVM-168]
	_ = x[SVMDA-169]
	_ = x[SVMFBASID-170]
	_ = x[SVML-171]
	_ = x[SVMNP-172]
	_ = x[SVMPF-173]
	_ = x[SVMPFT-174]
	_ = x[SYSCALL-175]
	_ = x[SYSEE-176]
	_ = x[TBM-177]
	_ = x[TDX_GUEST-178]
	_ = x[TLB_FLUSH_NESTED-179]
	_ = x[TME-180]
	_ = x[TOPEXT-181]
	_ = x[TSCRATEMSR-182]
	_ = x[TSXLDTRK-183]
	_ = x[VAES-184]
	_ = x[VMCBCLEAN-185]
	_ = x[VMPL-186]
	_ = x[VMSA_REGPROT-187]
	_ = x[VMX-188]
	_ = x[VPCLMULQDQ-189]
	_ = x[VTE-190]
	_ = x[WAITPKG-191]
	_ = x[WBNOINVD-192]
	_ = x[WRMSRNS-193]
	_ = x[X87-194]
	_ = x[XGETBV1-195]
	_ = x[XOP-196]
	_ = x[XSAVE-197]
	_ = x[XSAVEC-198]
	_ = x[XSAVEOPT-199]
	_ = x[XSAVES-200]
	_ = x[AESARM-201]
	_ = x[ARMCPUID-202]
	_ = x[ASIMD-203]
	_ = x[ASIMDDP-204]
	_ = x[ASIMDHP-205]
	_ = x[ASIMDRDM-206]
	_ = x[ATOMICS-207]
	_ = x[CRC32-208]
	_ = x[DCPOP-209]
	_ = x[EVTSTRM-210]
	_ = x[FCMA-211]
	_ = x[FHM-212]
	_ = x[FP-213]
	_ = x[FPHP-214]
	_ = x[GPA-215]
	_ = x[JSCVT-216]
	_ = x[LRCPC-217]
	_ = x[PMULL-218]
	_ = x[RNDR-219]
	_ = x[TLB-220]
	_ = x[TS-221]
	_ = x[SHA1-222]
	_ = x[SHA2-223]
	_ = x[SHA3-224]
	_ = x[SHA512-225]
	_ = x[SM3-226]
	_ = x[SM4-227]
	_ = x[SVE-228]
	_ = x[lastID-229]
	_ = x[firstID-0]
}

const _FeatureID_name = "firstIDADXAESNIAMD3DNOWAMD3DNOWEXTAMXBF16AMXFP16AMXINT8AMXFP8AMXTILEAMXTF32AMXCOMPLEXAMXTRANSPOSEAPX_FAVXAVX10AVX10_128AVX10_256AVX10_512AVX2AVX512BF16AVX512BITALGAVX512BWAVX512CDAVX512DQAVX512ERAVX512FAVX512FP16AVX512IFMAAVX512PFAVX512VBMIAVX512VBMI2AVX512VLAVX512VNNIAVX512VP2INTERSECTAVX512VPOPCNTDQAVXIFMAAVXNECONVERTAVXSLOWAVXVNNIAVXVNNIINT8AVXVNNIINT16BHI_CTRLBMI1BMI2CETIBTCETSSCLDEMOTECLMULCLZEROCMOVCMPCCXADDCMPSB_SCADBS_SHORTCMPXCHG8CPBOOSTCPPCCX16EFER_LMSLE_UNSENQCMDERMSF16CFLUSH_L1DFMA3FMA4FP128FP256FSRMFXSRFXSROPTGFNIHLEHRESETHTTHWAHYBRID_CPUHYPERVISORIA32_ARCH_CAPIA32_CORE_CAPIBPBIBPB_BRTYPEIBRSIBRS_PREFERREDIBRS_PROVIDES_SMPIBSIBSBRNTRGTIBSFETCHSAMIBSFFVIBSOPCNTIBSOPCNTEXTIBSOPSAMIBSRDWROPCNTIBSRIPINVALIDCHKIBS_FETCH_CTLXIBS_OPDATA4IBS_OPFUSEIBS_PREVENTHOSTIBS_ZEN4IDPRED_CTRLINT_WBINVDINVLPGBKEYLOCKERKEYLOCKERWLAHFLAMLBRVIRTLZCNTMCAOVERFLOWMCDT_NOMCOMMITMD_CLEARMMXMMXEXTMOVBEMOVDIR64BMOVDIRIMOVSB_ZLMOVUMPXMSRIRCMSRLISTMSR_PAGEFLUSHNRIPSNXOSXSAVEPCONFIGPOPCNTPPINPREFETCHIPSFDRDPRURDRANDRDSEEDRDTSCPRRSBA_CTRLRTMRTM_ALWAYS_ABORTSBPBSERIALIZESEVSEV_64BITSEV_ALTERNATIVESEV_DEBUGSWAPSEV_ESSEV_RESTRICTEDSEV_SNPSGXSGXLCSHASMESME_COHERENTSM3_X86SM4_X86SPEC_CTRL_SSBDSRBDS_CTRLSRSO_MSR_FIXSRSO_NOSRSO_USER_KERNEL_NOSSESSE2SSE3SSE4SSE42SSE4ASSSE3STIBPSTIBP_ALWAYSONSTOSB_SHORTSUCCORSVMSVMDASVMFBASIDSVMLSVMNPSVMPFSVMPFTSYSCALLSYSEETBMTDX_GUESTTLB_FLUSH_NESTEDTMETOPEXTTSCRATEMSRTSXLDTRKVAESVMCBCLEANVMPLVMSA_REGPROTVMXVPCLMULQDQVTEWAITPKGWBNOINVDWRMSRNSX87XGETBV1XOPXSAVEXSAVECXSAVEOPTXSAVESAESARMARMCPUIDASIMDASIMDDPASIMDHPASIMDRDMATOMICSCRC32DCPOPEVTSTRMFCMAFHMFPFPHPGPAJSCVTLRCPCPMULLRNDRTLBTSSHA1SHA2SHA3SHA512SM3SM4SVElastID"

var _FeatureID_index = [...]uint16{0, 7, 10, 15, 23, 34, 41, 48, 55, 61, 68, 75, 85, 97, 102, 105, 110, 119, 128, 137, 141, 151, 163, 171, 179, 187, 195, 202, 212, 222, 230, 240, 251, 259, 269, 287, 302, 309, 321, 328, 335, 346, 358, 366, 370, 374, 380, 385, 393, 398, 404, 408, 417, 435, 443, 450, 454, 458, 472, 478, 482, 486, 495, 499, 503, 508, 513, 517, 521, 528, 532, 535, 541, 544, 547, 557, 567, 580, 593, 597, 608, 612, 626, 643, 646, 656, 667, 673, 681, 692, 700, 712, 728, 742, 753, 763, 778, 786, 797, 807, 814, 823, 833, 837, 840, 847, 852, 863, 870, 877, 885, 888, 894, 899, 908, 915, 923, 927, 930, 936, 943, 956, 961, 963, 970, 977, 983, 987, 996, 1000, 1005, 1011, 1017, 1023, 1033, 1036, 1052, 1056, 1065, 1068, 1077, 1092, 1105, 1111, 1125, 1132, 1135, 1140, 1143, 1146, 1158, 1165, 1172, 1186, 1196, 1208, 1215, 1234, 1237, 1241, 1245, 1249, 1254, 1259, 1264, 1269, 1283, 1294, 1300, 1303, 1308, 1317, 1321, 1326, 1331, 1337, 1344, 1349, 1352, 1361, 1377, 1380, 1386, 1396, 1404, 1408, 1417, 1421, 1433, 1436, 1446, 1449, 1456, 1464, 1471, 1474, 1481, 1484, 1489, 1495, 1503, 1509, 1515, 1523, 1528, 1535, 1542, 1550, 1557, 1562, 1567, 1574, 1578, 1581, 1583, 1587, 1590, 1595, 1600, 1605, 1609, 1612, 1614, 1618, 1622, 1626, 1632, 1635, 1638, 1641, 1647}

func (i FeatureID) String() string {
	if i < 0 || i >= FeatureID(len(_FeatureID_index)-1) {
		return "FeatureID(" + strconv.FormatInt(int64(i), 10) + ")"
	}
	return _FeatureID_name[_FeatureID_index[i]:_FeatureID_index[i+1]]
}
func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[VendorUnknown-0]
	_ = x[Intel-1]
	_ = x[AMD-2]
	_ = x[VIA-3]
	_ = x[Transmeta-4]
	_ = x[NSC-5]
	_ = x[KVM-6]
	_ = x[MSVM-7]
	_ = x[VMware-8]
	_ = x[XenHVM-9]
	_ = x[Bhyve-10]
	_ = x[Hygon-11]
	_ = x[SiS-12]
	_ = x[RDC-13]
	_ = x[Ampere-14]
	_ = x[ARM-15]
	_ = x[Broadcom-16]
	_ = x[Cavium-17]
	_ = x[DEC-18]
	_ = x[Fujitsu-19]
	_ = x[Infineon-20]
	_ = x[Motorola-21]
	_ = x[NVIDIA-22]
	_ = x[AMCC-23]
	_ = x[Qualcomm-24]
	_ = x[Marvell-25]
	_ = x[QEMU-26]
	_ = x[QNX-27]
	_ = x[ACRN-28]
	_ = x[SRE-29]
	_ = x[Apple-30]
	_ = x[lastVendor-31]
}

const _Vendor_name = "VendorUnknownIntelAMDVIATransmetaNSCKVMMSVMVMwareXenHVMBhyveHygonSiSRDCAmpereARMBroadcomCaviumDECFujitsuInfineonMotorolaNVIDIAAMCCQualcommMarvellQEMUQNXACRNSREApplelastVendor"

var _Vendor_index = [...]uint8{0, 13, 18, 21, 24, 33, 36, 39, 43, 49, 55, 60, 65, 68, 71, 77, 80, 88, 94, 97, 104, 112, 120, 126, 130, 138, 145, 149, 152, 156, 159, 164, 174}

func (i Vendor) String() string {
	if i < 0 || i >= Vendor(len(_Vendor_index)-1) {
		return "Vendor(" + strconv.FormatInt(int64(i), 10) + ")"
	}
	return _Vendor_name[_Vendor_index[i]:_Vendor_index[i+1]]
}
