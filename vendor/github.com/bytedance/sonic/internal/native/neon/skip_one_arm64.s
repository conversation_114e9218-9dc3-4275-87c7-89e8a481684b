// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

#include "go_asm.h"
#include "funcdata.h"
#include "textflag.h"

TEXT ·__skip_one_entry__(SB), NOSPLIT, $176
	NO_LOCAL_POINTERS
	WORD $0x100000a0 // adr x0, .+20
	MOVD R0, ret(FP)
	RET
	  // .p2align 4, 0x00
lCPI0_0:
	WORD $0x08040201
	WORD $0x80402010
	WORD $0x08040201
	WORD $0x80402010
	// // .byte 1
// .byte 2
// .byte 4
// .byte 8
// .byte 16
// .byte 32
// .byte 64
// .byte 128
// .byte 1
// .byte 2
// .byte 4
// .byte 8
// .byte 16
// .byte 32
// .byte 64
// .byte 128

lCPI0_1:
	WORD $0x09010800
	WORD $0x0b030a02
	WORD $0x0d050c04
	WORD $0x0f070e06
	// // .byte 0
// .byte 8
// .byte 1
// .byte 9
// .byte 2
// .byte 10
// .byte 3
// .byte 11
// .byte 4
// .byte 12
// .byte 5
// .byte 13
// .byte 6
// .byte 14
// .byte 7
// .byte 15

lCPI0_2:
	WORD $0x00000001; WORD $0x00000000  // .quad 1
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	  // .p2align 2, 0x00
_skip_one:
	WORD $0xd10303ff  // sub	sp, sp, #192
	WORD $0xa905effc  // stp	x28, x27, [sp, #88]
	WORD $0xa906e7fa  // stp	x26, x25, [sp, #104]
	WORD $0xa907dff8  // stp	x24, x23, [sp, #120]
	WORD $0xa908d7f6  // stp	x22, x21, [sp, #136]
	WORD $0xa909cff4  // stp	x20, x19, [sp, #152]
	WORD $0xa90afbfd  // stp	fp, lr, [sp, #168]
	WORD $0xa93ffbfd  // stp	fp, lr, [sp, #-8]
	WORD $0xd10023fd  // sub	fp, sp, #8
	WORD $0x3730ee83  // tbnz	w3, #6, LBB0_397 $7632(%rip)
Lloh0:
	WORD $0x10fffe48  // adr	x8, lCPI0_2 $-56(%rip)
Lloh1:
	WORD $0x3dc00100  // ldr	q0, [x8, lCPI0_2@PAGEOFF] $0(%rip)
	WORD $0xaa0203ea  // mov	x10, x2
	WORD $0x3c808540  // str	q0, [x10], #8
	WORD $0xf9400009  // ldr	x9, [x0]
	WORD $0xaa2903eb  // mvn	x11, x9
	WORD $0xf940003e  // ldr	lr, [x1]
	WORD $0xcb0903ec  // neg	x12, x9
	WORD $0xd100052d  // sub	x13, x9, #1
	WORD $0x9280000e  // mov	x14, #-1
	WORD $0x5280002f  // mov	w15, #1
	WORD $0xd284c010  // mov	x16, #9728
	WORD $0xf2c00030  // movk	x16, #1, lsl #32
	WORD $0x4f01e440  // movi.16b	v0, #34
	WORD $0x4f02e781  // movi.16b	v1, #92
Lloh2:
	WORD $0x10fffb68  // adr	x8, lCPI0_0 $-148(%rip)
Lloh3:
	WORD $0x3dc00102  // ldr	q2, [x8, lCPI0_0@PAGEOFF] $0(%rip)
Lloh4:
	WORD $0x10fffba8  // adr	x8, lCPI0_1 $-140(%rip)
Lloh5:
	WORD $0x3dc00103  // ldr	q3, [x8, lCPI0_1@PAGEOFF] $0(%rip)
	WORD $0x4f01e404  // movi.16b	v4, #32
	WORD $0x4f01e5c5  // movi.16b	v5, #46
	WORD $0x4f01e566  // movi.16b	v6, #43
	WORD $0x4f01e5a7  // movi.16b	v7, #45
	WORD $0x4f06e610  // movi.16b	v16, #208
	WORD $0x4f00e551  // movi.16b	v17, #10
	WORD $0x4f06e7f2  // movi.16b	v18, #223
	WORD $0x4f02e4b3  // movi.16b	v19, #69
	WORD $0x52800034  // mov	w20, #1
LBB0_2:
	WORD $0xf9400408  // ldr	x8, [x0, #8]
	WORD $0xeb0803df  // cmp	lr, x8
	WORD $0x54000162  // b.hs	LBB0_7 $44(%rip)
	WORD $0x387e6931  // ldrb	w17, [x9, lr]
	WORD $0x7100363f  // cmp	w17, #13
	WORD $0x54000100  // b.eq	LBB0_7 $32(%rip)
	WORD $0x7100823f  // cmp	w17, #32
	WORD $0x540000c0  // b.eq	LBB0_7 $24(%rip)
	WORD $0x51002e31  // sub	w17, w17, #11
	WORD $0x31000a3f  // cmn	w17, #2
	WORD $0x54000062  // b.hs	LBB0_7 $12(%rip)
	WORD $0xaa1e03fb  // mov	x27, lr
	WORD $0x14000031  // b	LBB0_23 $196(%rip)
LBB0_7:
	WORD $0x910007db  // add	x27, lr, #1
	WORD $0xeb08037f  // cmp	x27, x8
	WORD $0x54000122  // b.hs	LBB0_11 $36(%rip)
	WORD $0x387b6931  // ldrb	w17, [x9, x27]
	WORD $0x7100363f  // cmp	w17, #13
	WORD $0x540000c0  // b.eq	LBB0_11 $24(%rip)
	WORD $0x7100823f  // cmp	w17, #32
	WORD $0x54000080  // b.eq	LBB0_11 $16(%rip)
	WORD $0x51002e31  // sub	w17, w17, #11
	WORD $0x31000a3f  // cmn	w17, #2
	WORD $0x540004c3  // b.lo	LBB0_23 $152(%rip)
LBB0_11:
	WORD $0x91000bdb  // add	x27, lr, #2
	WORD $0xeb08037f  // cmp	x27, x8
	WORD $0x54000122  // b.hs	LBB0_15 $36(%rip)
	WORD $0x387b6931  // ldrb	w17, [x9, x27]
	WORD $0x7100363f  // cmp	w17, #13
	WORD $0x540000c0  // b.eq	LBB0_15 $24(%rip)
	WORD $0x7100823f  // cmp	w17, #32
	WORD $0x54000080  // b.eq	LBB0_15 $16(%rip)
	WORD $0x51002e31  // sub	w17, w17, #11
	WORD $0x31000a3f  // cmn	w17, #2
	WORD $0x54000363  // b.lo	LBB0_23 $108(%rip)
LBB0_15:
	WORD $0x91000fdb  // add	x27, lr, #3
	WORD $0xeb08037f  // cmp	x27, x8
	WORD $0x54000122  // b.hs	LBB0_19 $36(%rip)
	WORD $0x387b6931  // ldrb	w17, [x9, x27]
	WORD $0x7100363f  // cmp	w17, #13
	WORD $0x540000c0  // b.eq	LBB0_19 $24(%rip)
	WORD $0x7100823f  // cmp	w17, #32
	WORD $0x54000080  // b.eq	LBB0_19 $16(%rip)
	WORD $0x51002e31  // sub	w17, w17, #11
	WORD $0x31000a3f  // cmn	w17, #2
	WORD $0x54000203  // b.lo	LBB0_23 $64(%rip)
LBB0_19:
	WORD $0x910013db  // add	x27, lr, #4
	WORD $0xeb08037f  // cmp	x27, x8
	WORD $0x54011942  // b.hs	LBB0_474 $9000(%rip)
LBB0_20:
	WORD $0x387b6931  // ldrb	w17, [x9, x27]
	WORD $0x7100823f  // cmp	w17, #32
	WORD $0x9ad121f1  // lsl	x17, x15, x17
	WORD $0x8a100231  // and	x17, x17, x16
	WORD $0xfa409a24  // ccmp	x17, #0, #4, ls
	WORD $0x540000a0  // b.eq	LBB0_22 $20(%rip)
	WORD $0x9100077b  // add	x27, x27, #1
	WORD $0xeb1b011f  // cmp	x8, x27
	WORD $0x54ffff01  // b.ne	LBB0_20 $-32(%rip)
	WORD $0x1400075a  // b	LBB0_417 $7528(%rip)
LBB0_22:
	WORD $0xeb08037f  // cmp	x27, x8
	WORD $0x5400eb02  // b.hs	LBB0_417 $7520(%rip)
LBB0_23:
	WORD $0x9100077e  // add	lr, x27, #1
	WORD $0xf900003e  // str	lr, [x1]
	WORD $0x8b1b0125  // add	x5, x9, x27
	WORD $0x394000b5  // ldrb	w21, [x5]
	WORD $0x3400ea75  // cbz	w21, LBB0_417 $7500(%rip)
	WORD $0xd1000687  // sub	x7, x20, #1
	WORD $0xf8677948  // ldr	x8, [x10, x7, lsl #3]
	WORD $0xb10005df  // cmn	x14, #1
	WORD $0x9a8e036e  // csel	x14, x27, x14, eq
	WORD $0x71000d1f  // cmp	w8, #3
	WORD $0x54000ccc  // b.gt	LBB0_40 $408(%rip)
	WORD $0x7100051f  // cmp	w8, #1
	WORD $0x54001920  // b.eq	LBB0_55 $804(%rip)
	WORD $0x7100091f  // cmp	w8, #2
	WORD $0x54002c60  // b.eq	LBB0_95 $1420(%rip)
	WORD $0x71000d1f  // cmp	w8, #3
	WORD $0x540019c1  // b.ne	LBB0_59 $824(%rip)
	WORD $0x71008abf  // cmp	w21, #34
	WORD $0x54014e41  // b.ne	LBB0_543 $10696(%rip)
	WORD $0x52800088  // mov	w8, #4
	WORD $0xf8277948  // str	x8, [x10, x7, lsl #3]
	WORD $0xf9400408  // ldr	x8, [x0, #8]
	WORD $0xeb1e0114  // subs	x20, x8, lr
	WORD $0x372830e3  // tbnz	w3, #5, LBB0_110 $1564(%rip)
	WORD $0x54014280  // b.eq	LBB0_515 $10320(%rip)
	WORD $0xf101029f  // cmp	x20, #64
	WORD $0x54008cc3  // b.lo	LBB0_249 $4504(%rip)
	WORD $0xd2800005  // mov	x5, #0
	WORD $0x92800008  // mov	x8, #-1
LBB0_33:
	WORD $0x8b1e0131  // add	x17, x9, lr
	WORD $0xad405634  // ldp	q20, q21, [x17]
	WORD $0xad415e36  // ldp	q22, q23, [x17, #32]
	WORD $0x6e208e98  // cmeq.16b	v24, v20, v0
	WORD $0x6e208eb9  // cmeq.16b	v25, v21, v0
	WORD $0x6e208eda  // cmeq.16b	v26, v22, v0
	WORD $0x6e208efb  // cmeq.16b	v27, v23, v0
	WORD $0x6e218e94  // cmeq.16b	v20, v20, v1
	WORD $0x6e218eb5  // cmeq.16b	v21, v21, v1
	WORD $0x6e218ed6  // cmeq.16b	v22, v22, v1
	WORD $0x6e218ef7  // cmeq.16b	v23, v23, v1
	WORD $0x4e221f18  // and.16b	v24, v24, v2
	WORD $0x4e030318  // tbl.16b	v24, { v24 }, v3
	WORD $0x4e71bb18  // addv.8h	h24, v24
	WORD $0x1e260311  // fmov	w17, s24
	WORD $0x4e221f38  // and.16b	v24, v25, v2
	WORD $0x4e030318  // tbl.16b	v24, { v24 }, v3
	WORD $0x4e71bb18  // addv.8h	h24, v24
	WORD $0x1e260304  // fmov	w4, s24
	WORD $0x4e221f58  // and.16b	v24, v26, v2
	WORD $0x4e030318  // tbl.16b	v24, { v24 }, v3
	WORD $0x4e71bb18  // addv.8h	h24, v24
	WORD $0x1e260307  // fmov	w7, s24
	WORD $0x4e221f78  // and.16b	v24, v27, v2
	WORD $0x4e030318  // tbl.16b	v24, { v24 }, v3
	WORD $0x4e71bb18  // addv.8h	h24, v24
	WORD $0x1e260315  // fmov	w21, s24
	WORD $0x4e221e94  // and.16b	v20, v20, v2
	WORD $0x4e030294  // tbl.16b	v20, { v20 }, v3
	WORD $0x4e71ba94  // addv.8h	h20, v20
	WORD $0x1e260296  // fmov	w22, s20
	WORD $0x4e221eb4  // and.16b	v20, v21, v2
	WORD $0x4e030294  // tbl.16b	v20, { v20 }, v3
	WORD $0x4e71ba94  // addv.8h	h20, v20
	WORD $0x1e260297  // fmov	w23, s20
	WORD $0x4e221ed4  // and.16b	v20, v22, v2
	WORD $0x4e030294  // tbl.16b	v20, { v20 }, v3
	WORD $0x4e71ba94  // addv.8h	h20, v20
	WORD $0x1e260298  // fmov	w24, s20
	WORD $0x4e221ef4  // and.16b	v20, v23, v2
	WORD $0x4e030294  // tbl.16b	v20, { v20 }, v3
	WORD $0x4e71ba94  // addv.8h	h20, v20
	WORD $0x1e260299  // fmov	w25, s20
	WORD $0xd3607ce7  // lsl	x7, x7, #32
	WORD $0xaa15c0e7  // orr	x7, x7, x21, lsl #48
	WORD $0x53103c84  // lsl	w4, w4, #16
	WORD $0xaa0400e4  // orr	x4, x7, x4
	WORD $0xaa110091  // orr	x17, x4, x17
	WORD $0xd3607f04  // lsl	x4, x24, #32
	WORD $0xaa19c084  // orr	x4, x4, x25, lsl #48
	WORD $0x53103ee7  // lsl	w7, w23, #16
	WORD $0xaa070084  // orr	x4, x4, x7
	WORD $0xaa160087  // orr	x7, x4, x22
	WORD $0xb5000107  // cbnz	x7, LBB0_37 $32(%rip)
	WORD $0xb5000185  // cbnz	x5, LBB0_38 $48(%rip)
	WORD $0xb50002d1  // cbnz	x17, LBB0_39 $88(%rip)
LBB0_36:
	WORD $0xd1010294  // sub	x20, x20, #64
	WORD $0x910103de  // add	lr, lr, #64
	WORD $0xf100fe9f  // cmp	x20, #63
	WORD $0x54fff8a8  // b.hi	LBB0_33 $-236(%rip)
	WORD $0x1400041f  // b	LBB0_246 $4220(%rip)
LBB0_37:
	WORD $0xb100051f  // cmn	x8, #1
	WORD $0xdac000e4  // rbit	x4, x7
	WORD $0xdac01084  // clz	x4, x4
	WORD $0x8b1e0084  // add	x4, x4, lr
	WORD $0x9a841108  // csel	x8, x8, x4, ne
LBB0_38:
	WORD $0x8a2500e4  // bic	x4, x7, x5
	WORD $0xaa0404b5  // orr	x21, x5, x4, lsl #1
	WORD $0x8a3500e5  // bic	x5, x7, x21
	WORD $0x9201f0a5  // and	x5, x5, #0xaaaaaaaaaaaaaaaa
	WORD $0xab0400a4  // adds	x4, x5, x4
	WORD $0x1a9f37e5  // cset	w5, hs
	WORD $0xd37ff884  // lsl	x4, x4, #1
	WORD $0xd200f084  // eor	x4, x4, #0x5555555555555555
	WORD $0x8a150084  // and	x4, x4, x21
	WORD $0x8a240231  // bic	x17, x17, x4
	WORD $0xb4fffd91  // cbz	x17, LBB0_36 $-80(%rip)
LBB0_39:
	WORD $0xdac00231  // rbit	x17, x17
	WORD $0xdac01231  // clz	x17, x17
	WORD $0x8b1e0231  // add	x17, x17, lr
	WORD $0x9100063e  // add	lr, x17, #1
	WORD $0xb6f87f9e  // tbz	lr, #63, LBB0_243 $4080(%rip)
	WORD $0x140009bb  // b	LBB0_514 $9964(%rip)
LBB0_40:
	WORD $0x7100111f  // cmp	w8, #4
	WORD $0x54000d20  // b.eq	LBB0_57 $420(%rip)
	WORD $0x7100151f  // cmp	w8, #5
	WORD $0x54002060  // b.eq	LBB0_97 $1036(%rip)
	WORD $0x7100191f  // cmp	w8, #6
	WORD $0x54000d21  // b.ne	LBB0_59 $420(%rip)
	WORD $0x71008abf  // cmp	w21, #34
	WORD $0x54001f81  // b.ne	LBB0_96 $1008(%rip)
	WORD $0x52800048  // mov	w8, #2
	WORD $0xf8277948  // str	x8, [x10, x7, lsl #3]
	WORD $0xf9400408  // ldr	x8, [x0, #8]
	WORD $0xeb1e0114  // subs	x20, x8, lr
	WORD $0x37283743  // tbnz	w3, #5, LBB0_137 $1768(%rip)
	WORD $0x540135e0  // b.eq	LBB0_515 $9916(%rip)
	WORD $0xf101029f  // cmp	x20, #64
	WORD $0x54009143  // b.lo	LBB0_280 $4648(%rip)
	WORD $0xd2800005  // mov	x5, #0
	WORD $0x92800008  // mov	x8, #-1
LBB0_48:
	WORD $0x8b1e0131  // add	x17, x9, lr
	WORD $0xad405634  // ldp	q20, q21, [x17]
	WORD $0xad415e36  // ldp	q22, q23, [x17, #32]
	WORD $0x6e208e98  // cmeq.16b	v24, v20, v0
	WORD $0x6e208eb9  // cmeq.16b	v25, v21, v0
	WORD $0x6e208eda  // cmeq.16b	v26, v22, v0
	WORD $0x6e208efb  // cmeq.16b	v27, v23, v0
	WORD $0x6e218e94  // cmeq.16b	v20, v20, v1
	WORD $0x6e218eb5  // cmeq.16b	v21, v21, v1
	WORD $0x6e218ed6  // cmeq.16b	v22, v22, v1
	WORD $0x6e218ef7  // cmeq.16b	v23, v23, v1
	WORD $0x4e221f18  // and.16b	v24, v24, v2
	WORD $0x4e030318  // tbl.16b	v24, { v24 }, v3
	WORD $0x4e71bb18  // addv.8h	h24, v24
	WORD $0x1e260311  // fmov	w17, s24
	WORD $0x4e221f38  // and.16b	v24, v25, v2
	WORD $0x4e030318  // tbl.16b	v24, { v24 }, v3
	WORD $0x4e71bb18  // addv.8h	h24, v24
	WORD $0x1e260304  // fmov	w4, s24
	WORD $0x4e221f58  // and.16b	v24, v26, v2
	WORD $0x4e030318  // tbl.16b	v24, { v24 }, v3
	WORD $0x4e71bb18  // addv.8h	h24, v24
	WORD $0x1e260307  // fmov	w7, s24
	WORD $0x4e221f78  // and.16b	v24, v27, v2
	WORD $0x4e030318  // tbl.16b	v24, { v24 }, v3
	WORD $0x4e71bb18  // addv.8h	h24, v24
	WORD $0x1e260315  // fmov	w21, s24
	WORD $0x4e221e94  // and.16b	v20, v20, v2
	WORD $0x4e030294  // tbl.16b	v20, { v20 }, v3
	WORD $0x4e71ba94  // addv.8h	h20, v20
	WORD $0x1e260296  // fmov	w22, s20
	WORD $0x4e221eb4  // and.16b	v20, v21, v2
	WORD $0x4e030294  // tbl.16b	v20, { v20 }, v3
	WORD $0x4e71ba94  // addv.8h	h20, v20
	WORD $0x1e260297  // fmov	w23, s20
	WORD $0x4e221ed4  // and.16b	v20, v22, v2
	WORD $0x4e030294  // tbl.16b	v20, { v20 }, v3
	WORD $0x4e71ba94  // addv.8h	h20, v20
	WORD $0x1e260298  // fmov	w24, s20
	WORD $0x4e221ef4  // and.16b	v20, v23, v2
	WORD $0x4e030294  // tbl.16b	v20, { v20 }, v3
	WORD $0x4e71ba94  // addv.8h	h20, v20
	WORD $0x1e260299  // fmov	w25, s20
	WORD $0xd3607ce7  // lsl	x7, x7, #32
	WORD $0xaa15c0e7  // orr	x7, x7, x21, lsl #48
	WORD $0x53103c84  // lsl	w4, w4, #16
	WORD $0xaa0400e4  // orr	x4, x7, x4
	WORD $0xaa110091  // orr	x17, x4, x17
	WORD $0xd3607f04  // lsl	x4, x24, #32
	WORD $0xaa19c084  // orr	x4, x4, x25, lsl #48
	WORD $0x53103ee7  // lsl	w7, w23, #16
	WORD $0xaa070084  // orr	x4, x4, x7
	WORD $0xaa160087  // orr	x7, x4, x22
	WORD $0xb5000107  // cbnz	x7, LBB0_52 $32(%rip)
	WORD $0xb5000185  // cbnz	x5, LBB0_53 $48(%rip)
	WORD $0xb50002d1  // cbnz	x17, LBB0_54 $88(%rip)
LBB0_51:
	WORD $0xd1010294  // sub	x20, x20, #64
	WORD $0x910103de  // add	lr, lr, #64
	WORD $0xf100fe9f  // cmp	x20, #63
	WORD $0x54fff8a8  // b.hi	LBB0_48 $-236(%rip)
	WORD $0x1400043e  // b	LBB0_275 $4344(%rip)
LBB0_52:
	WORD $0xb100051f  // cmn	x8, #1
	WORD $0xdac000e4  // rbit	x4, x7
	WORD $0xdac01084  // clz	x4, x4
	WORD $0x8b1e0084  // add	x4, x4, lr
	WORD $0x9a841108  // csel	x8, x8, x4, ne
LBB0_53:
	WORD $0x8a2500e4  // bic	x4, x7, x5
	WORD $0xaa0404b5  // orr	x21, x5, x4, lsl #1
	WORD $0x8a3500e5  // bic	x5, x7, x21
	WORD $0x9201f0a5  // and	x5, x5, #0xaaaaaaaaaaaaaaaa
	WORD $0xab0400a4  // adds	x4, x5, x4
	WORD $0x1a9f37e5  // cset	w5, hs
	WORD $0xd37ff884  // lsl	x4, x4, #1
	WORD $0xd200f084  // eor	x4, x4, #0x5555555555555555
	WORD $0x8a150084  // and	x4, x4, x21
	WORD $0x8a240231  // bic	x17, x17, x4
	WORD $0xb4fffd91  // cbz	x17, LBB0_51 $-80(%rip)
LBB0_54:
	WORD $0xdac00231  // rbit	x17, x17
	WORD $0xdac01231  // clz	x17, x17
	WORD $0x8b1e0231  // add	x17, x17, lr
	WORD $0x9100063e  // add	lr, x17, #1
	WORD $0xb6f83b1e  // tbz	lr, #63, LBB0_149 $1888(%rip)
	WORD $0x14000956  // b	LBB0_514 $9560(%rip)
LBB0_55:
	WORD $0x7100b2bf  // cmp	w21, #44
	WORD $0x54001760  // b.eq	LBB0_106 $748(%rip)
	WORD $0x710176bf  // cmp	w21, #93
	WORD $0x54001400  // b.eq	LBB0_98 $640(%rip)
	WORD $0x140009ab  // b	LBB0_543 $9900(%rip)
LBB0_57:
	WORD $0x7100eabf  // cmp	w21, #58
	WORD $0x54013521  // b.ne	LBB0_543 $9892(%rip)
	WORD $0xf827795f  // str	xzr, [x10, x7, lsl #3]
	WORD $0x14000392  // b	LBB0_244 $3656(%rip)
LBB0_59:
	WORD $0xf9000047  // str	x7, [x2]
	WORD $0x92800028  // mov	x8, #-2
	WORD $0x71016abf  // cmp	w21, #90
	WORD $0x5400140c  // b.gt	LBB0_100 $640(%rip)
LBB0_60:
	WORD $0x5100c2b1  // sub	w17, w21, #48
	WORD $0x71002a3f  // cmp	w17, #10
	WORD $0x54003a82  // b.hs	LBB0_152 $1872(%rip)
	WORD $0xf9400408  // ldr	x8, [x0, #8]
	WORD $0xeb1b0116  // subs	x22, x8, x27
	WORD $0x54012940  // b.eq	LBB0_517 $9512(%rip)
	WORD $0x394000a8  // ldrb	w8, [x5]
	WORD $0x7100c11f  // cmp	w8, #48
	WORD $0x54000181  // b.ne	LBB0_66 $48(%rip)
	WORD $0xf10006df  // cmp	x22, #1
	WORD $0x54006fc0  // b.eq	LBB0_243 $3576(%rip)
	WORD $0x387e6928  // ldrb	w8, [x9, lr]
	WORD $0x5100b908  // sub	w8, w8, #46
	WORD $0x7100dd1f  // cmp	w8, #55
	WORD $0x54006f48  // b.hi	LBB0_243 $3560(%rip)
	WORD $0x9ac821e8  // lsl	x8, x15, x8
	WORD $0xb20903f1  // mov	x17, #36028797027352576
	WORD $0xf2800031  // movk	x17, #1
	WORD $0xea11011f  // tst	x8, x17
	WORD $0x54006ea0  // b.eq	LBB0_243 $3540(%rip)
LBB0_66:
	WORD $0xf10042df  // cmp	x22, #16
	WORD $0x5400a303  // b.lo	LBB0_337 $5216(%rip)
	WORD $0xd2800017  // mov	x23, #0
	WORD $0xd280001e  // mov	lr, #0
	WORD $0x92800015  // mov	x21, #-1
	WORD $0x92800014  // mov	x20, #-1
	WORD $0x92800008  // mov	x8, #-1
LBB0_68:
	WORD $0x3cfe68b4  // ldr	q20, [x5, lr]
	WORD $0x6e258e95  // cmeq.16b	v21, v20, v5
	WORD $0x6e268e96  // cmeq.16b	v22, v20, v6
	WORD $0x6e278e97  // cmeq.16b	v23, v20, v7
	WORD $0x4e308698  // add.16b	v24, v20, v16
	WORD $0x6e383638  // cmhi.16b	v24, v17, v24
	WORD $0x4e321e94  // and.16b	v20, v20, v18
	WORD $0x6e338e94  // cmeq.16b	v20, v20, v19
	WORD $0x4eb71ed6  // orr.16b	v22, v22, v23
	WORD $0x4eb51f17  // orr.16b	v23, v24, v21
	WORD $0x4eb61e98  // orr.16b	v24, v20, v22
	WORD $0x4eb81ef7  // orr.16b	v23, v23, v24
	WORD $0x4e221eb5  // and.16b	v21, v21, v2
	WORD $0x4e0302b5  // tbl.16b	v21, { v21 }, v3
	WORD $0x4e71bab5  // addv.8h	h21, v21
	WORD $0x1e2602a4  // fmov	w4, s21
	WORD $0x4e221e94  // and.16b	v20, v20, v2
	WORD $0x4e030294  // tbl.16b	v20, { v20 }, v3
	WORD $0x4e71ba94  // addv.8h	h20, v20
	WORD $0x1e260287  // fmov	w7, s20
	WORD $0x4e221ed4  // and.16b	v20, v22, v2
	WORD $0x4e030294  // tbl.16b	v20, { v20 }, v3
	WORD $0x4e71ba94  // addv.8h	h20, v20
	WORD $0x1e260299  // fmov	w25, s20
	WORD $0x4e221ef4  // and.16b	v20, v23, v2
	WORD $0x4e030294  // tbl.16b	v20, { v20 }, v3
	WORD $0x4e71ba94  // addv.8h	h20, v20
	WORD $0x1e260291  // fmov	w17, s20
	WORD $0x2a3103f1  // mvn	w17, w17
	WORD $0x32103e31  // orr	w17, w17, #0xffff0000
	WORD $0x5ac00231  // rbit	w17, w17
	WORD $0x5ac01231  // clz	w17, w17
	WORD $0x12800006  // mov	w6, #-1
	WORD $0x1ad120d8  // lsl	w24, w6, w17
	WORD $0x0a38009a  // bic	w26, w4, w24
	WORD $0x0a3800e6  // bic	w6, w7, w24
	WORD $0x0a380333  // bic	w19, w25, w24
	WORD $0x7100423f  // cmp	w17, #16
	WORD $0x1a9a009a  // csel	w26, w4, w26, eq
	WORD $0x1a8600f8  // csel	w24, w7, w6, eq
	WORD $0x1a930327  // csel	w7, w25, w19, eq
	WORD $0x51000744  // sub	w4, w26, #1
	WORD $0x6a1a0084  // ands	w4, w4, w26
	WORD $0x54006961  // b.ne	LBB0_245 $3372(%rip)
	WORD $0x51000704  // sub	w4, w24, #1
	WORD $0x6a180084  // ands	w4, w4, w24
	WORD $0x54006901  // b.ne	LBB0_245 $3360(%rip)
	WORD $0x510004e4  // sub	w4, w7, #1
	WORD $0x6a070084  // ands	w4, w4, w7
	WORD $0x540068a1  // b.ne	LBB0_245 $3348(%rip)
	WORD $0x340000da  // cbz	w26, LBB0_74 $24(%rip)
	WORD $0x5ac00344  // rbit	w4, w26
	WORD $0x5ac01099  // clz	w25, w4
	WORD $0xb100051f  // cmn	x8, #1
	WORD $0x540079a1  // b.ne	LBB0_277 $3892(%rip)
	WORD $0x8b1903c8  // add	x8, lr, x25
LBB0_74:
	WORD $0x340000d8  // cbz	w24, LBB0_77 $24(%rip)
	WORD $0x5ac00304  // rbit	w4, w24
	WORD $0x5ac01098  // clz	w24, w4
	WORD $0xb100069f  // cmn	x20, #1
	WORD $0x54007941  // b.ne	LBB0_278 $3880(%rip)
	WORD $0x8b1803d4  // add	x20, lr, x24
LBB0_77:
	WORD $0x340000c7  // cbz	w7, LBB0_80 $24(%rip)
	WORD $0x5ac000e4  // rbit	w4, w7
	WORD $0x5ac01087  // clz	w7, w4
	WORD $0xb10006bf  // cmn	x21, #1
	WORD $0x540078e1  // b.ne	LBB0_279 $3868(%rip)
	WORD $0x8b0703d5  // add	x21, lr, x7
LBB0_80:
	WORD $0x7100423f  // cmp	w17, #16
	WORD $0x54001a41  // b.ne	LBB0_122 $840(%rip)
	WORD $0x910043de  // add	lr, lr, #16
	WORD $0xd10042f7  // sub	x23, x23, #16
	WORD $0x8b1702c7  // add	x7, x22, x23
	WORD $0xf1003cff  // cmp	x7, #15
	WORD $0x54fff6c8  // b.hi	LBB0_68 $-296(%rip)
	WORD $0x8b1e00b7  // add	x23, x5, lr
	WORD $0xeb1e02df  // cmp	x22, lr
	WORD $0x54001980  // b.eq	LBB0_123 $816(%rip)
LBB0_83:
	WORD $0x8b0702f8  // add	x24, x23, x7
	WORD $0x8b1b01b1  // add	x17, x13, x27
	WORD $0xcb170236  // sub	x22, x17, x23
	WORD $0xcb0502f1  // sub	x17, x23, x5
	WORD $0xaa1703fe  // mov	lr, x23
	WORD $0x14000009  // b	LBB0_86 $36(%rip)
LBB0_84:
	WORD $0xb100069f  // cmn	x20, #1
	WORD $0xaa1103f4  // mov	x20, x17
	WORD $0x54001c01  // b.ne	LBB0_135 $896(%rip)
LBB0_85:
	WORD $0xd10006d6  // sub	x22, x22, #1
	WORD $0x91000631  // add	x17, x17, #1
	WORD $0xaa1e03f7  // mov	x23, lr
	WORD $0xd10004e7  // sub	x7, x7, #1
	WORD $0xb4003e67  // cbz	x7, LBB0_181 $1996(%rip)
LBB0_86:
	WORD $0x384017da  // ldrb	w26, [lr], #1
	WORD $0x5100c344  // sub	w4, w26, #48
	WORD $0x7100289f  // cmp	w4, #10
	WORD $0x54ffff03  // b.lo	LBB0_85 $-32(%rip)
	WORD $0x7100b75f  // cmp	w26, #45
	WORD $0x5400016d  // b.le	LBB0_92 $44(%rip)
	WORD $0x7101975f  // cmp	w26, #101
	WORD $0x54fffe20  // b.eq	LBB0_84 $-60(%rip)
	WORD $0x7101175f  // cmp	w26, #69
	WORD $0x54fffde0  // b.eq	LBB0_84 $-68(%rip)
	WORD $0x7100bb5f  // cmp	w26, #46
	WORD $0x54001641  // b.ne	LBB0_123 $712(%rip)
	WORD $0xb100051f  // cmn	x8, #1
	WORD $0xaa1103e8  // mov	x8, x17
	WORD $0x54fffda0  // b.eq	LBB0_85 $-76(%rip)
	WORD $0x140000cb  // b	LBB0_135 $812(%rip)
LBB0_92:
	WORD $0x7100af5f  // cmp	w26, #43
	WORD $0x54000060  // b.eq	LBB0_94 $12(%rip)
	WORD $0x7100b75f  // cmp	w26, #45
	WORD $0x54001541  // b.ne	LBB0_123 $680(%rip)
LBB0_94:
	WORD $0xb10006bf  // cmn	x21, #1
	WORD $0xaa1103f5  // mov	x21, x17
	WORD $0x54fffca0  // b.eq	LBB0_85 $-108(%rip)
	WORD $0x140000c3  // b	LBB0_135 $780(%rip)
LBB0_95:
	WORD $0x7100b2bf  // cmp	w21, #44
	WORD $0x540004a0  // b.eq	LBB0_108 $148(%rip)
LBB0_96:
	WORD $0x7101f6bf  // cmp	w21, #125
	WORD $0x54000080  // b.eq	LBB0_98 $16(%rip)
	WORD $0x1400090f  // b	LBB0_543 $9276(%rip)
LBB0_97:
	WORD $0x710176bf  // cmp	w21, #93
	WORD $0x540000c1  // b.ne	LBB0_99 $24(%rip)
LBB0_98:
	WORD $0xf9000047  // str	x7, [x2]
	WORD $0xaa0703f4  // mov	x20, x7
	WORD $0xaa0e03e8  // mov	x8, x14
	WORD $0xb5ffc907  // cbnz	x7, LBB0_2 $-1760(%rip)
	WORD $0x1400061c  // b	LBB0_438 $6256(%rip)
LBB0_99:
	WORD $0xf827794f  // str	x15, [x10, x7, lsl #3]
	WORD $0x92800028  // mov	x8, #-2
	WORD $0x71016abf  // cmp	w21, #90
	WORD $0x54ffec4d  // b.le	LBB0_60 $-632(%rip)
LBB0_100:
	WORD $0x7101b6bf  // cmp	w21, #109
	WORD $0x540028ad  // b.le	LBB0_158 $1300(%rip)
	WORD $0x7101babf  // cmp	w21, #110
	WORD $0x54002b80  // b.eq	LBB0_165 $1392(%rip)
	WORD $0x7101d2bf  // cmp	w21, #116
	WORD $0x54002a00  // b.eq	LBB0_163 $1344(%rip)
	WORD $0x7101eebf  // cmp	w21, #123
	WORD $0x5400c201  // b.ne	LBB0_438 $6208(%rip)
	WORD $0xf9400048  // ldr	x8, [x2]
	WORD $0xf13ffd1f  // cmp	x8, #4095
	WORD $0x540113ac  // b.gt	LBB0_513 $8820(%rip)
	WORD $0x91000511  // add	x17, x8, #1
	WORD $0xf9000051  // str	x17, [x2]
	WORD $0x528000d1  // mov	w17, #6
	WORD $0xf8287951  // str	x17, [x10, x8, lsl #3]
	WORD $0x140002df  // b	LBB0_244 $2940(%rip)
LBB0_106:
	WORD $0xf13ffe9f  // cmp	x20, #4095
	WORD $0x540112cc  // b.gt	LBB0_513 $8792(%rip)
	WORD $0x91000688  // add	x8, x20, #1
	WORD $0xf9000048  // str	x8, [x2]
	WORD $0xf834795f  // str	xzr, [x10, x20, lsl #3]
	WORD $0x140002d9  // b	LBB0_244 $2916(%rip)
LBB0_108:
	WORD $0xf13ffe9f  // cmp	x20, #4095
	WORD $0x5401120c  // b.gt	LBB0_513 $8768(%rip)
	WORD $0x91000688  // add	x8, x20, #1
	WORD $0xf9000048  // str	x8, [x2]
	WORD $0x52800068  // mov	w8, #3
	WORD $0xf8347948  // str	x8, [x10, x20, lsl #3]
	WORD $0x140002d2  // b	LBB0_244 $2888(%rip)
LBB0_110:
	WORD $0x540111c0  // b.eq	LBB0_515 $8760(%rip)
	WORD $0xf101029f  // cmp	x20, #64
	WORD $0x54006063  // b.lo	LBB0_255 $3084(%rip)
	WORD $0xd2800005  // mov	x5, #0
	WORD $0x92800008  // mov	x8, #-1
LBB0_113:
	WORD $0x8b1e0131  // add	x17, x9, lr
	WORD $0xad405a37  // ldp	q23, q22, [x17]
	WORD $0xad415235  // ldp	q21, q20, [x17, #32]
	WORD $0x6e208ef8  // cmeq.16b	v24, v23, v0
	WORD $0x4e221f18  // and.16b	v24, v24, v2
	WORD $0x4e030318  // tbl.16b	v24, { v24 }, v3
	WORD $0x4e71bb18  // addv.8h	h24, v24
	WORD $0x1e260311  // fmov	w17, s24
	WORD $0x6e208ed8  // cmeq.16b	v24, v22, v0
	WORD $0x4e221f18  // and.16b	v24, v24, v2
	WORD $0x4e030318  // tbl.16b	v24, { v24 }, v3
	WORD $0x4e71bb18  // addv.8h	h24, v24
	WORD $0x1e260304  // fmov	w4, s24
	WORD $0x6e208eb8  // cmeq.16b	v24, v21, v0
	WORD $0x4e221f18  // and.16b	v24, v24, v2
	WORD $0x4e030318  // tbl.16b	v24, { v24 }, v3
	WORD $0x4e71bb18  // addv.8h	h24, v24
	WORD $0x1e260307  // fmov	w7, s24
	WORD $0x6e208e98  // cmeq.16b	v24, v20, v0
	WORD $0x4e221f18  // and.16b	v24, v24, v2
	WORD $0x4e030318  // tbl.16b	v24, { v24 }, v3
	WORD $0x4e71bb18  // addv.8h	h24, v24
	WORD $0x1e260315  // fmov	w21, s24
	WORD $0x6e218ef8  // cmeq.16b	v24, v23, v1
	WORD $0x4e221f18  // and.16b	v24, v24, v2
	WORD $0x4e030318  // tbl.16b	v24, { v24 }, v3
	WORD $0x4e71bb18  // addv.8h	h24, v24
	WORD $0x1e260316  // fmov	w22, s24
	WORD $0x6e218ed8  // cmeq.16b	v24, v22, v1
	WORD $0x4e221f18  // and.16b	v24, v24, v2
	WORD $0x4e030318  // tbl.16b	v24, { v24 }, v3
	WORD $0x4e71bb18  // addv.8h	h24, v24
	WORD $0x1e260317  // fmov	w23, s24
	WORD $0x6e218eb8  // cmeq.16b	v24, v21, v1
	WORD $0x4e221f18  // and.16b	v24, v24, v2
	WORD $0x4e030318  // tbl.16b	v24, { v24 }, v3
	WORD $0x4e71bb18  // addv.8h	h24, v24
	WORD $0x1e260318  // fmov	w24, s24
	WORD $0x6e218e98  // cmeq.16b	v24, v20, v1
	WORD $0x4e221f18  // and.16b	v24, v24, v2
	WORD $0x4e030318  // tbl.16b	v24, { v24 }, v3
	WORD $0x4e71bb18  // addv.8h	h24, v24
	WORD $0x1e260319  // fmov	w25, s24
	WORD $0xd3607ce7  // lsl	x7, x7, #32
	WORD $0xaa15c0e7  // orr	x7, x7, x21, lsl #48
	WORD $0x53103c84  // lsl	w4, w4, #16
	WORD $0xaa0400e4  // orr	x4, x7, x4
	WORD $0xaa110091  // orr	x17, x4, x17
	WORD $0xd3607f04  // lsl	x4, x24, #32
	WORD $0xaa19c084  // orr	x4, x4, x25, lsl #48
	WORD $0x53103ee7  // lsl	w7, w23, #16
	WORD $0xaa070084  // orr	x4, x4, x7
	WORD $0xaa160087  // orr	x7, x4, x22
	WORD $0xb5000447  // cbnz	x7, LBB0_118 $136(%rip)
	WORD $0xb50004c5  // cbnz	x5, LBB0_119 $152(%rip)
LBB0_115:
	WORD $0x6e373497  // cmhi.16b	v23, v4, v23
	WORD $0x4e221ef7  // and.16b	v23, v23, v2
	WORD $0x4e0302f7  // tbl.16b	v23, { v23 }, v3
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602e4  // fmov	w4, s23
	WORD $0x6e363496  // cmhi.16b	v22, v4, v22
	WORD $0x4e221ed6  // and.16b	v22, v22, v2
	WORD $0x4e0302d6  // tbl.16b	v22, { v22 }, v3
	WORD $0x4e71bad6  // addv.8h	h22, v22
	WORD $0x1e2602c7  // fmov	w7, s22
	WORD $0x6e353495  // cmhi.16b	v21, v4, v21
	WORD $0x4e221eb5  // and.16b	v21, v21, v2
	WORD $0x4e0302b5  // tbl.16b	v21, { v21 }, v3
	WORD $0x4e71bab5  // addv.8h	h21, v21
	WORD $0x1e2602b5  // fmov	w21, s21
	WORD $0x6e343494  // cmhi.16b	v20, v4, v20
	WORD $0x4e221e94  // and.16b	v20, v20, v2
	WORD $0x4e030294  // tbl.16b	v20, { v20 }, v3
	WORD $0x4e71ba94  // addv.8h	h20, v20
	WORD $0x1e260296  // fmov	w22, s20
	WORD $0xd3607eb5  // lsl	x21, x21, #32
	WORD $0xaa16c2b5  // orr	x21, x21, x22, lsl #48
	WORD $0x53103ce7  // lsl	w7, w7, #16
	WORD $0xaa0702a7  // orr	x7, x21, x7
	WORD $0xaa0400e7  // orr	x7, x7, x4
	WORD $0xb50002f1  // cbnz	x17, LBB0_120 $92(%rip)
	WORD $0xb5010867  // cbnz	x7, LBB0_521 $8460(%rip)
	WORD $0xd1010294  // sub	x20, x20, #64
	WORD $0x910103de  // add	lr, lr, #64
	WORD $0xf100fe9f  // cmp	x20, #63
	WORD $0x54fff568  // b.hi	LBB0_113 $-340(%rip)
	WORD $0x14000281  // b	LBB0_247 $2564(%rip)
LBB0_118:
	WORD $0xb100051f  // cmn	x8, #1
	WORD $0xdac000e4  // rbit	x4, x7
	WORD $0xdac01084  // clz	x4, x4
	WORD $0x8b1e0084  // add	x4, x4, lr
	WORD $0x9a841108  // csel	x8, x8, x4, ne
LBB0_119:
	WORD $0x8a2500e4  // bic	x4, x7, x5
	WORD $0xaa0404b5  // orr	x21, x5, x4, lsl #1
	WORD $0x8a3500e5  // bic	x5, x7, x21
	WORD $0x9201f0a5  // and	x5, x5, #0xaaaaaaaaaaaaaaaa
	WORD $0xab0400a4  // adds	x4, x5, x4
	WORD $0x1a9f37e5  // cset	w5, hs
	WORD $0xd37ff884  // lsl	x4, x4, #1
	WORD $0xd200f084  // eor	x4, x4, #0x5555555555555555
	WORD $0x8a150084  // and	x4, x4, x21
	WORD $0x8a240231  // bic	x17, x17, x4
	WORD $0x17ffffd1  // b	LBB0_115 $-188(%rip)
LBB0_120:
	WORD $0xdac00231  // rbit	x17, x17
	WORD $0xdac01231  // clz	x17, x17
	WORD $0xdac000e4  // rbit	x4, x7
	WORD $0xdac01085  // clz	x5, x4
	WORD $0xeb1100bf  // cmp	x5, x17
	WORD $0x54010fc3  // b.lo	LBB0_546 $8696(%rip)
	WORD $0x8b1e0231  // add	x17, x17, lr
	WORD $0x9100063e  // add	lr, x17, #1
	WORD $0xb6f84b1e  // tbz	lr, #63, LBB0_243 $2400(%rip)
	WORD $0x14000817  // b	LBB0_514 $8284(%rip)
LBB0_122:
	WORD $0x8b3140b1  // add	x17, x5, w17, uxtw
	WORD $0x8b1e0237  // add	x23, x17, lr
LBB0_123:
	WORD $0x92800016  // mov	x22, #-1
	WORD $0xb4010368  // cbz	x8, LBB0_518 $8300(%rip)
LBB0_124:
	WORD $0xb4010355  // cbz	x21, LBB0_518 $8296(%rip)
	WORD $0xb4010334  // cbz	x20, LBB0_518 $8292(%rip)
	WORD $0xcb0502f1  // sub	x17, x23, x5
	WORD $0xd1000625  // sub	x5, x17, #1
	WORD $0xeb05011f  // cmp	x8, x5
	WORD $0x540002a0  // b.eq	LBB0_134 $84(%rip)
	WORD $0xeb0502bf  // cmp	x21, x5
	WORD $0x54000260  // b.eq	LBB0_134 $76(%rip)
	WORD $0xeb05029f  // cmp	x20, x5
	WORD $0x54000220  // b.eq	LBB0_134 $68(%rip)
	WORD $0xf10006a4  // subs	x4, x21, #1
	WORD $0x5400006b  // b.lt	LBB0_131 $12(%rip)
	WORD $0xeb04029f  // cmp	x20, x4
	WORD $0x540101c1  // b.ne	LBB0_519 $8248(%rip)
LBB0_131:
	WORD $0xaa140105  // orr	x5, x8, x20
	WORD $0xb7f80065  // tbnz	x5, #63, LBB0_133 $12(%rip)
	WORD $0xeb14011f  // cmp	x8, x20
	WORD $0x540102ea  // b.ge	LBB0_525 $8284(%rip)
LBB0_133:
	WORD $0xd37ffca4  // lsr	x4, x5, #63
	WORD $0x52000084  // eor	w4, w4, #0x1
	WORD $0xd1000685  // sub	x5, x20, #1
	WORD $0xeb05011f  // cmp	x8, x5
	WORD $0x1a9f17e8  // cset	w8, eq
	WORD $0x6a08009f  // tst	w4, w8
	WORD $0xda940236  // csinv	x22, x17, x20, eq
	WORD $0x14000002  // b	LBB0_135 $8(%rip)
LBB0_134:
	WORD $0xcb1103f6  // neg	x22, x17
LBB0_135:
	WORD $0xb7f8fff6  // tbnz	x22, #63, LBB0_518 $8188(%rip)
	WORD $0x8b16037e  // add	lr, x27, x22
	WORD $0x14000235  // b	LBB0_243 $2260(%rip)
LBB0_137:
	WORD $0x5400fec0  // b.eq	LBB0_515 $8152(%rip)
	WORD $0xf101029f  // cmp	x20, #64
	WORD $0x54005e83  // b.lo	LBB0_286 $3024(%rip)
	WORD $0xd2800005  // mov	x5, #0
	WORD $0x92800008  // mov	x8, #-1
LBB0_140:
	WORD $0x8b1e0131  // add	x17, x9, lr
	WORD $0xad405a37  // ldp	q23, q22, [x17]
	WORD $0xad415235  // ldp	q21, q20, [x17, #32]
	WORD $0x6e208ef8  // cmeq.16b	v24, v23, v0
	WORD $0x4e221f18  // and.16b	v24, v24, v2
	WORD $0x4e030318  // tbl.16b	v24, { v24 }, v3
	WORD $0x4e71bb18  // addv.8h	h24, v24
	WORD $0x1e260311  // fmov	w17, s24
	WORD $0x6e208ed8  // cmeq.16b	v24, v22, v0
	WORD $0x4e221f18  // and.16b	v24, v24, v2
	WORD $0x4e030318  // tbl.16b	v24, { v24 }, v3
	WORD $0x4e71bb18  // addv.8h	h24, v24
	WORD $0x1e260304  // fmov	w4, s24
	WORD $0x6e208eb8  // cmeq.16b	v24, v21, v0
	WORD $0x4e221f18  // and.16b	v24, v24, v2
	WORD $0x4e030318  // tbl.16b	v24, { v24 }, v3
	WORD $0x4e71bb18  // addv.8h	h24, v24
	WORD $0x1e260307  // fmov	w7, s24
	WORD $0x6e208e98  // cmeq.16b	v24, v20, v0
	WORD $0x4e221f18  // and.16b	v24, v24, v2
	WORD $0x4e030318  // tbl.16b	v24, { v24 }, v3
	WORD $0x4e71bb18  // addv.8h	h24, v24
	WORD $0x1e260315  // fmov	w21, s24
	WORD $0x6e218ef8  // cmeq.16b	v24, v23, v1
	WORD $0x4e221f18  // and.16b	v24, v24, v2
	WORD $0x4e030318  // tbl.16b	v24, { v24 }, v3
	WORD $0x4e71bb18  // addv.8h	h24, v24
	WORD $0x1e260316  // fmov	w22, s24
	WORD $0x6e218ed8  // cmeq.16b	v24, v22, v1
	WORD $0x4e221f18  // and.16b	v24, v24, v2
	WORD $0x4e030318  // tbl.16b	v24, { v24 }, v3
	WORD $0x4e71bb18  // addv.8h	h24, v24
	WORD $0x1e260317  // fmov	w23, s24
	WORD $0x6e218eb8  // cmeq.16b	v24, v21, v1
	WORD $0x4e221f18  // and.16b	v24, v24, v2
	WORD $0x4e030318  // tbl.16b	v24, { v24 }, v3
	WORD $0x4e71bb18  // addv.8h	h24, v24
	WORD $0x1e260318  // fmov	w24, s24
	WORD $0x6e218e98  // cmeq.16b	v24, v20, v1
	WORD $0x4e221f18  // and.16b	v24, v24, v2
	WORD $0x4e030318  // tbl.16b	v24, { v24 }, v3
	WORD $0x4e71bb18  // addv.8h	h24, v24
	WORD $0x1e260319  // fmov	w25, s24
	WORD $0xd3607ce7  // lsl	x7, x7, #32
	WORD $0xaa15c0e7  // orr	x7, x7, x21, lsl #48
	WORD $0x53103c84  // lsl	w4, w4, #16
	WORD $0xaa0400e4  // orr	x4, x7, x4
	WORD $0xaa110091  // orr	x17, x4, x17
	WORD $0xd3607f04  // lsl	x4, x24, #32
	WORD $0xaa19c084  // orr	x4, x4, x25, lsl #48
	WORD $0x53103ee7  // lsl	w7, w23, #16
	WORD $0xaa070084  // orr	x4, x4, x7
	WORD $0xaa160087  // orr	x7, x4, x22
	WORD $0xb5000447  // cbnz	x7, LBB0_145 $136(%rip)
	WORD $0xb50004c5  // cbnz	x5, LBB0_146 $152(%rip)
LBB0_142:
	WORD $0x6e373497  // cmhi.16b	v23, v4, v23
	WORD $0x4e221ef7  // and.16b	v23, v23, v2
	WORD $0x4e0302f7  // tbl.16b	v23, { v23 }, v3
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602e4  // fmov	w4, s23
	WORD $0x6e363496  // cmhi.16b	v22, v4, v22
	WORD $0x4e221ed6  // and.16b	v22, v22, v2
	WORD $0x4e0302d6  // tbl.16b	v22, { v22 }, v3
	WORD $0x4e71bad6  // addv.8h	h22, v22
	WORD $0x1e2602c7  // fmov	w7, s22
	WORD $0x6e353495  // cmhi.16b	v21, v4, v21
	WORD $0x4e221eb5  // and.16b	v21, v21, v2
	WORD $0x4e0302b5  // tbl.16b	v21, { v21 }, v3
	WORD $0x4e71bab5  // addv.8h	h21, v21
	WORD $0x1e2602b5  // fmov	w21, s21
	WORD $0x6e343494  // cmhi.16b	v20, v4, v20
	WORD $0x4e221e94  // and.16b	v20, v20, v2
	WORD $0x4e030294  // tbl.16b	v20, { v20 }, v3
	WORD $0x4e71ba94  // addv.8h	h20, v20
	WORD $0x1e260296  // fmov	w22, s20
	WORD $0xd3607eb5  // lsl	x21, x21, #32
	WORD $0xaa16c2b5  // orr	x21, x21, x22, lsl #48
	WORD $0x53103ce7  // lsl	w7, w7, #16
	WORD $0xaa0702a7  // orr	x7, x21, x7
	WORD $0xaa0400e7  // orr	x7, x7, x4
	WORD $0xb50002f1  // cbnz	x17, LBB0_147 $92(%rip)
	WORD $0xb500f567  // cbnz	x7, LBB0_521 $7852(%rip)
	WORD $0xd1010294  // sub	x20, x20, #64
	WORD $0x910103de  // add	lr, lr, #64
	WORD $0xf100fe9f  // cmp	x20, #63
	WORD $0x54fff568  // b.hi	LBB0_140 $-340(%rip)
	WORD $0x1400026d  // b	LBB0_276 $2484(%rip)
LBB0_145:
	WORD $0xb100051f  // cmn	x8, #1
	WORD $0xdac000e4  // rbit	x4, x7
	WORD $0xdac01084  // clz	x4, x4
	WORD $0x8b1e0084  // add	x4, x4, lr
	WORD $0x9a841108  // csel	x8, x8, x4, ne
LBB0_146:
	WORD $0x8a2500e4  // bic	x4, x7, x5
	WORD $0xaa0404b5  // orr	x21, x5, x4, lsl #1
	WORD $0x8a3500e5  // bic	x5, x7, x21
	WORD $0x9201f0a5  // and	x5, x5, #0xaaaaaaaaaaaaaaaa
	WORD $0xab0400a4  // adds	x4, x5, x4
	WORD $0x1a9f37e5  // cset	w5, hs
	WORD $0xd37ff884  // lsl	x4, x4, #1
	WORD $0xd200f084  // eor	x4, x4, #0x5555555555555555
	WORD $0x8a150084  // and	x4, x4, x21
	WORD $0x8a240231  // bic	x17, x17, x4
	WORD $0x17ffffd1  // b	LBB0_142 $-188(%rip)
LBB0_147:
	WORD $0xdac00231  // rbit	x17, x17
	WORD $0xdac01231  // clz	x17, x17
	WORD $0xdac000e4  // rbit	x4, x7
	WORD $0xdac01085  // clz	x5, x4
	WORD $0xeb1100bf  // cmp	x5, x17
	WORD $0x5400fcc3  // b.lo	LBB0_546 $8088(%rip)
	WORD $0x8b1e0231  // add	x17, x17, lr
	WORD $0x9100063e  // add	lr, x17, #1
	WORD $0xb7f8f01e  // tbnz	lr, #63, LBB0_514 $7680(%rip)
LBB0_149:
	WORD $0xf900003e  // str	lr, [x1]
	WORD $0xaa1b03e8  // mov	x8, x27
	WORD $0xb27ff7f1  // mov	x17, #9223372036854775806
	WORD $0xeb11037f  // cmp	x27, x17
	WORD $0x54009d28  // b.hi	LBB0_438 $5028(%rip)
	WORD $0xf9400048  // ldr	x8, [x2]
	WORD $0xf13ffd1f  // cmp	x8, #4095
	WORD $0x5400eecc  // b.gt	LBB0_513 $7640(%rip)
	WORD $0x91000511  // add	x17, x8, #1
	WORD $0xf9000051  // str	x17, [x2]
	WORD $0x52800091  // mov	w17, #4
	WORD $0xf8287951  // str	x17, [x10, x8, lsl #3]
	WORD $0x140001b8  // b	LBB0_244 $1760(%rip)
LBB0_152:
	WORD $0x71008abf  // cmp	w21, #34
	WORD $0x54000740  // b.eq	LBB0_170 $232(%rip)
	WORD $0x7100b6bf  // cmp	w21, #45
	WORD $0x54009ba1  // b.ne	LBB0_438 $4980(%rip)
	WORD $0xf9400408  // ldr	x8, [x0, #8]
	WORD $0xeb1e0111  // subs	x17, x8, lr
	WORD $0x5400f080  // b.eq	LBB0_526 $7696(%rip)
	WORD $0x8b1e0128  // add	x8, x9, lr
	WORD $0x39400104  // ldrb	w4, [x8]
	WORD $0x7100c09f  // cmp	w4, #48
	WORD $0x540021a1  // b.ne	LBB0_196 $1076(%rip)
	WORD $0xf100063f  // cmp	x17, #1
	WORD $0x54002021  // b.ne	LBB0_194 $1028(%rip)
LBB0_157:
	WORD $0x52800036  // mov	w22, #1
	WORD $0x140001a3  // b	LBB0_242 $1676(%rip)
LBB0_158:
	WORD $0x71016ebf  // cmp	w21, #91
	WORD $0x54000460  // b.eq	LBB0_168 $140(%rip)
	WORD $0x71019abf  // cmp	w21, #102
	WORD $0x540099c1  // b.ne	LBB0_438 $4920(%rip)
	WORD $0xf9400408  // ldr	x8, [x0, #8]
	WORD $0xd1001111  // sub	x17, x8, #4
	WORD $0xeb11037f  // cmp	x27, x17
	WORD $0x5400f842  // b.hs	LBB0_548 $7944(%rip)
	WORD $0xb87e6928  // ldr	w8, [x9, lr]
	WORD $0x528d8c31  // mov	w17, #27745
	WORD $0x72acae71  // movk	w17, #25971, lsl #16
	WORD $0x6b11011f  // cmp	w8, w17
	WORD $0x5400ee61  // b.ne	LBB0_529 $7628(%rip)
	WORD $0x9100177e  // add	lr, x27, #5
	WORD $0x14000195  // b	LBB0_243 $1620(%rip)
LBB0_163:
	WORD $0xf9400408  // ldr	x8, [x0, #8]
	WORD $0xd1000d11  // sub	x17, x8, #3
	WORD $0xeb11037f  // cmp	x27, x17
	WORD $0x5400f6e2  // b.hs	LBB0_548 $7900(%rip)
	WORD $0xb87b6928  // ldr	w8, [x9, x27]
	WORD $0x528e4e91  // mov	w17, #29300
	WORD $0x72acaeb1  // movk	w17, #25973, lsl #16
	WORD $0x6b11011f  // cmp	w8, w17
	WORD $0x54000160  // b.eq	LBB0_167 $44(%rip)
	WORD $0x1400077c  // b	LBB0_534 $7664(%rip)
LBB0_165:
	WORD $0xf9400408  // ldr	x8, [x0, #8]
	WORD $0xd1000d11  // sub	x17, x8, #3
	WORD $0xeb11037f  // cmp	x27, x17
	WORD $0x5400f5a2  // b.hs	LBB0_548 $7860(%rip)
	WORD $0xb87b6928  // ldr	w8, [x9, x27]
	WORD $0x528eadd1  // mov	w17, #30062
	WORD $0x72ad8d91  // movk	w17, #27756, lsl #16
	WORD $0x6b11011f  // cmp	w8, w17
	WORD $0x5400f0e1  // b.ne	LBB0_538 $7708(%rip)
LBB0_167:
	WORD $0x9100137e  // add	lr, x27, #4
	WORD $0x14000180  // b	LBB0_243 $1536(%rip)
LBB0_168:
	WORD $0xf9400048  // ldr	x8, [x2]
	WORD $0xf13ffd1f  // cmp	x8, #4095
	WORD $0x5400e76c  // b.gt	LBB0_513 $7404(%rip)
	WORD $0x91000511  // add	x17, x8, #1
	WORD $0xf9000051  // str	x17, [x2]
	WORD $0x528000b1  // mov	w17, #5
	WORD $0xf8287951  // str	x17, [x10, x8, lsl #3]
	WORD $0x1400017d  // b	LBB0_244 $1524(%rip)
LBB0_170:
	WORD $0xf9400408  // ldr	x8, [x0, #8]
	WORD $0xeb1e0114  // subs	x20, x8, lr
	WORD $0x37280b43  // tbnz	w3, #5, LBB0_182 $360(%rip)
	WORD $0x5400e6c0  // b.eq	LBB0_515 $7384(%rip)
	WORD $0xf101029f  // cmp	x20, #64
	WORD $0x54005363  // b.lo	LBB0_311 $2668(%rip)
	WORD $0xd2800005  // mov	x5, #0
	WORD $0x92800008  // mov	x8, #-1
LBB0_174:
	WORD $0x8b1e0131  // add	x17, x9, lr
	WORD $0xad405634  // ldp	q20, q21, [x17]
	WORD $0xad415e36  // ldp	q22, q23, [x17, #32]
	WORD $0x6e208e98  // cmeq.16b	v24, v20, v0
	WORD $0x6e208eb9  // cmeq.16b	v25, v21, v0
	WORD $0x6e208eda  // cmeq.16b	v26, v22, v0
	WORD $0x6e208efb  // cmeq.16b	v27, v23, v0
	WORD $0x6e218e94  // cmeq.16b	v20, v20, v1
	WORD $0x6e218eb5  // cmeq.16b	v21, v21, v1
	WORD $0x6e218ed6  // cmeq.16b	v22, v22, v1
	WORD $0x6e218ef7  // cmeq.16b	v23, v23, v1
	WORD $0x4e221f18  // and.16b	v24, v24, v2
	WORD $0x4e030318  // tbl.16b	v24, { v24 }, v3
	WORD $0x4e71bb18  // addv.8h	h24, v24
	WORD $0x1e260311  // fmov	w17, s24
	WORD $0x4e221f38  // and.16b	v24, v25, v2
	WORD $0x4e030318  // tbl.16b	v24, { v24 }, v3
	WORD $0x4e71bb18  // addv.8h	h24, v24
	WORD $0x1e260304  // fmov	w4, s24
	WORD $0x4e221f58  // and.16b	v24, v26, v2
	WORD $0x4e030318  // tbl.16b	v24, { v24 }, v3
	WORD $0x4e71bb18  // addv.8h	h24, v24
	WORD $0x1e260306  // fmov	w6, s24
	WORD $0x4e221f78  // and.16b	v24, v27, v2
	WORD $0x4e030318  // tbl.16b	v24, { v24 }, v3
	WORD $0x4e71bb18  // addv.8h	h24, v24
	WORD $0x1e260307  // fmov	w7, s24
	WORD $0x4e221e94  // and.16b	v20, v20, v2
	WORD $0x4e030294  // tbl.16b	v20, { v20 }, v3
	WORD $0x4e71ba94  // addv.8h	h20, v20
	WORD $0x1e260293  // fmov	w19, s20
	WORD $0x4e221eb4  // and.16b	v20, v21, v2
	WORD $0x4e030294  // tbl.16b	v20, { v20 }, v3
	WORD $0x4e71ba94  // addv.8h	h20, v20
	WORD $0x1e260295  // fmov	w21, s20
	WORD $0x4e221ed4  // and.16b	v20, v22, v2
	WORD $0x4e030294  // tbl.16b	v20, { v20 }, v3
	WORD $0x4e71ba94  // addv.8h	h20, v20
	WORD $0x1e260296  // fmov	w22, s20
	WORD $0x4e221ef4  // and.16b	v20, v23, v2
	WORD $0x4e030294  // tbl.16b	v20, { v20 }, v3
	WORD $0x4e71ba94  // addv.8h	h20, v20
	WORD $0x1e260297  // fmov	w23, s20
	WORD $0xd3607cc6  // lsl	x6, x6, #32
	WORD $0xaa07c0c6  // orr	x6, x6, x7, lsl #48
	WORD $0x53103c84  // lsl	w4, w4, #16
	WORD $0xaa0400c4  // orr	x4, x6, x4
	WORD $0xaa110091  // orr	x17, x4, x17
	WORD $0xd3607ec4  // lsl	x4, x22, #32
	WORD $0xaa17c084  // orr	x4, x4, x23, lsl #48
	WORD $0x53103ea6  // lsl	w6, w21, #16
	WORD $0xaa060084  // orr	x4, x4, x6
	WORD $0xaa130087  // orr	x7, x4, x19
	WORD $0xb5000107  // cbnz	x7, LBB0_178 $32(%rip)
	WORD $0xb5000185  // cbnz	x5, LBB0_179 $48(%rip)
	WORD $0xb50002d1  // cbnz	x17, LBB0_180 $88(%rip)
LBB0_177:
	WORD $0xd1010294  // sub	x20, x20, #64
	WORD $0x910103de  // add	lr, lr, #64
	WORD $0xf100fe9f  // cmp	x20, #63
	WORD $0x54fff8a8  // b.hi	LBB0_174 $-236(%rip)
	WORD $0x14000255  // b	LBB0_308 $2388(%rip)
LBB0_178:
	WORD $0xb100051f  // cmn	x8, #1
	WORD $0xdac000e4  // rbit	x4, x7
	WORD $0xdac01084  // clz	x4, x4
	WORD $0x8b1e0084  // add	x4, x4, lr
	WORD $0x9a841108  // csel	x8, x8, x4, ne
LBB0_179:
	WORD $0x8a2500e4  // bic	x4, x7, x5
	WORD $0xaa0404a6  // orr	x6, x5, x4, lsl #1
	WORD $0x8a2600e5  // bic	x5, x7, x6
	WORD $0x9201f0a5  // and	x5, x5, #0xaaaaaaaaaaaaaaaa
	WORD $0xab0400a4  // adds	x4, x5, x4
	WORD $0x1a9f37e5  // cset	w5, hs
	WORD $0xd37ff884  // lsl	x4, x4, #1
	WORD $0xd200f084  // eor	x4, x4, #0x5555555555555555
	WORD $0x8a060084  // and	x4, x4, x6
	WORD $0x8a240231  // bic	x17, x17, x4
	WORD $0xb4fffd91  // cbz	x17, LBB0_177 $-80(%rip)
LBB0_180:
	WORD $0xdac00231  // rbit	x17, x17
	WORD $0xdac01231  // clz	x17, x17
	WORD $0x14000077  // b	LBB0_193 $476(%rip)
LBB0_181:
	WORD $0xaa1803f7  // mov	x23, x24
	WORD $0x92800016  // mov	x22, #-1
	WORD $0xb5ffd968  // cbnz	x8, LBB0_124 $-1236(%rip)
	WORD $0x140006e4  // b	LBB0_518 $7056(%rip)
LBB0_182:
	WORD $0x5400dba0  // b.eq	LBB0_515 $7028(%rip)
	WORD $0xf101029f  // cmp	x20, #64
	WORD $0x54004ca3  // b.lo	LBB0_317 $2452(%rip)
	WORD $0xd2800005  // mov	x5, #0
	WORD $0x92800008  // mov	x8, #-1
LBB0_185:
	WORD $0x8b1e0131  // add	x17, x9, lr
	WORD $0xad405a37  // ldp	q23, q22, [x17]
	WORD $0xad415235  // ldp	q21, q20, [x17, #32]
	WORD $0x6e208ef8  // cmeq.16b	v24, v23, v0
	WORD $0x4e221f18  // and.16b	v24, v24, v2
	WORD $0x4e030318  // tbl.16b	v24, { v24 }, v3
	WORD $0x4e71bb18  // addv.8h	h24, v24
	WORD $0x1e260311  // fmov	w17, s24
	WORD $0x6e208ed8  // cmeq.16b	v24, v22, v0
	WORD $0x4e221f18  // and.16b	v24, v24, v2
	WORD $0x4e030318  // tbl.16b	v24, { v24 }, v3
	WORD $0x4e71bb18  // addv.8h	h24, v24
	WORD $0x1e260304  // fmov	w4, s24
	WORD $0x6e208eb8  // cmeq.16b	v24, v21, v0
	WORD $0x4e221f18  // and.16b	v24, v24, v2
	WORD $0x4e030318  // tbl.16b	v24, { v24 }, v3
	WORD $0x4e71bb18  // addv.8h	h24, v24
	WORD $0x1e260306  // fmov	w6, s24
	WORD $0x6e208e98  // cmeq.16b	v24, v20, v0
	WORD $0x4e221f18  // and.16b	v24, v24, v2
	WORD $0x4e030318  // tbl.16b	v24, { v24 }, v3
	WORD $0x4e71bb18  // addv.8h	h24, v24
	WORD $0x1e260307  // fmov	w7, s24
	WORD $0x6e218ef8  // cmeq.16b	v24, v23, v1
	WORD $0x4e221f18  // and.16b	v24, v24, v2
	WORD $0x4e030318  // tbl.16b	v24, { v24 }, v3
	WORD $0x4e71bb18  // addv.8h	h24, v24
	WORD $0x1e260313  // fmov	w19, s24
	WORD $0x6e218ed8  // cmeq.16b	v24, v22, v1
	WORD $0x4e221f18  // and.16b	v24, v24, v2
	WORD $0x4e030318  // tbl.16b	v24, { v24 }, v3
	WORD $0x4e71bb18  // addv.8h	h24, v24
	WORD $0x1e260315  // fmov	w21, s24
	WORD $0x6e218eb8  // cmeq.16b	v24, v21, v1
	WORD $0x4e221f18  // and.16b	v24, v24, v2
	WORD $0x4e030318  // tbl.16b	v24, { v24 }, v3
	WORD $0x4e71bb18  // addv.8h	h24, v24
	WORD $0x1e260316  // fmov	w22, s24
	WORD $0x6e218e98  // cmeq.16b	v24, v20, v1
	WORD $0x4e221f18  // and.16b	v24, v24, v2
	WORD $0x4e030318  // tbl.16b	v24, { v24 }, v3
	WORD $0x4e71bb18  // addv.8h	h24, v24
	WORD $0x1e260317  // fmov	w23, s24
	WORD $0xd3607cc6  // lsl	x6, x6, #32
	WORD $0xaa07c0c6  // orr	x6, x6, x7, lsl #48
	WORD $0x53103c84  // lsl	w4, w4, #16
	WORD $0xaa0400c4  // orr	x4, x6, x4
	WORD $0xaa110091  // orr	x17, x4, x17
	WORD $0xd3607ec4  // lsl	x4, x22, #32
	WORD $0xaa17c084  // orr	x4, x4, x23, lsl #48
	WORD $0x53103ea6  // lsl	w6, w21, #16
	WORD $0xaa060084  // orr	x4, x4, x6
	WORD $0xaa130087  // orr	x7, x4, x19
	WORD $0xb5000447  // cbnz	x7, LBB0_190 $136(%rip)
	WORD $0xb50004c5  // cbnz	x5, LBB0_191 $152(%rip)
LBB0_187:
	WORD $0x6e373497  // cmhi.16b	v23, v4, v23
	WORD $0x4e221ef7  // and.16b	v23, v23, v2
	WORD $0x4e0302f7  // tbl.16b	v23, { v23 }, v3
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602e4  // fmov	w4, s23
	WORD $0x6e363496  // cmhi.16b	v22, v4, v22
	WORD $0x4e221ed6  // and.16b	v22, v22, v2
	WORD $0x4e0302d6  // tbl.16b	v22, { v22 }, v3
	WORD $0x4e71bad6  // addv.8h	h22, v22
	WORD $0x1e2602c6  // fmov	w6, s22
	WORD $0x6e353495  // cmhi.16b	v21, v4, v21
	WORD $0x4e221eb5  // and.16b	v21, v21, v2
	WORD $0x4e0302b5  // tbl.16b	v21, { v21 }, v3
	WORD $0x4e71bab5  // addv.8h	h21, v21
	WORD $0x1e2602a7  // fmov	w7, s21
	WORD $0x6e343494  // cmhi.16b	v20, v4, v20
	WORD $0x4e221e94  // and.16b	v20, v20, v2
	WORD $0x4e030294  // tbl.16b	v20, { v20 }, v3
	WORD $0x4e71ba94  // addv.8h	h20, v20
	WORD $0x1e260293  // fmov	w19, s20
	WORD $0xd3607ce7  // lsl	x7, x7, #32
	WORD $0xaa13c0e7  // orr	x7, x7, x19, lsl #48
	WORD $0x53103cc6  // lsl	w6, w6, #16
	WORD $0xaa0600e6  // orr	x6, x7, x6
	WORD $0xaa0400c7  // orr	x7, x6, x4
	WORD $0xb50002f1  // cbnz	x17, LBB0_192 $92(%rip)
	WORD $0xb500d247  // cbnz	x7, LBB0_521 $6728(%rip)
	WORD $0xd1010294  // sub	x20, x20, #64
	WORD $0x910103de  // add	lr, lr, #64
	WORD $0xf100fe9f  // cmp	x20, #63
	WORD $0x54fff568  // b.hi	LBB0_185 $-340(%rip)
	WORD $0x140001e4  // b	LBB0_309 $1936(%rip)
LBB0_190:
	WORD $0xb100051f  // cmn	x8, #1
	WORD $0xdac000e4  // rbit	x4, x7
	WORD $0xdac01084  // clz	x4, x4
	WORD $0x8b1e0084  // add	x4, x4, lr
	WORD $0x9a841108  // csel	x8, x8, x4, ne
LBB0_191:
	WORD $0x8a2500e4  // bic	x4, x7, x5
	WORD $0xaa0404a6  // orr	x6, x5, x4, lsl #1
	WORD $0x8a2600e5  // bic	x5, x7, x6
	WORD $0x9201f0a5  // and	x5, x5, #0xaaaaaaaaaaaaaaaa
	WORD $0xab0400a4  // adds	x4, x5, x4
	WORD $0x1a9f37e5  // cset	w5, hs
	WORD $0xd37ff884  // lsl	x4, x4, #1
	WORD $0xd200f084  // eor	x4, x4, #0x5555555555555555
	WORD $0x8a060084  // and	x4, x4, x6
	WORD $0x8a240231  // bic	x17, x17, x4
	WORD $0x17ffffd1  // b	LBB0_187 $-188(%rip)
LBB0_192:
	WORD $0xdac00231  // rbit	x17, x17
	WORD $0xdac01231  // clz	x17, x17
	WORD $0xdac000e4  // rbit	x4, x7
	WORD $0xdac01085  // clz	x5, x4
	WORD $0xeb1100bf  // cmp	x5, x17
	WORD $0x5400d9a3  // b.lo	LBB0_546 $6964(%rip)
LBB0_193:
	WORD $0x8b1e0231  // add	x17, x17, lr
	WORD $0x9100063e  // add	lr, x17, #1
	WORD $0xb6f814fe  // tbz	lr, #63, LBB0_243 $668(%rip)
	WORD $0x14000666  // b	LBB0_514 $6552(%rip)
LBB0_194:
	WORD $0x39400504  // ldrb	w4, [x8, #1]
	WORD $0x5100b885  // sub	w5, w4, #46
	WORD $0x7100dcbf  // cmp	w5, #55
	WORD $0x54ffdfa8  // b.hi	LBB0_157 $-1036(%rip)
	WORD $0x9ac521e4  // lsl	x4, x15, x5
	WORD $0x52800036  // mov	w22, #1
	WORD $0xb20903e5  // mov	x5, #36028797027352576
	WORD $0xf2800025  // movk	x5, #1
	WORD $0xea05009f  // tst	x4, x5
	WORD $0x54001360  // b.eq	LBB0_242 $620(%rip)
LBB0_196:
	WORD $0xf100423f  // cmp	x17, #16
	WORD $0x54004903  // b.lo	LBB0_339 $2336(%rip)
	WORD $0xd2800018  // mov	x24, #0
	WORD $0xd2800017  // mov	x23, #0
	WORD $0x92800005  // mov	x5, #-1
	WORD $0x92800014  // mov	x20, #-1
	WORD $0x92800015  // mov	x21, #-1
LBB0_198:
	WORD $0x3cf76914  // ldr	q20, [x8, x23]
	WORD $0x6e258e95  // cmeq.16b	v21, v20, v5
	WORD $0x6e268e96  // cmeq.16b	v22, v20, v6
	WORD $0x6e278e97  // cmeq.16b	v23, v20, v7
	WORD $0x4e308698  // add.16b	v24, v20, v16
	WORD $0x6e383638  // cmhi.16b	v24, v17, v24
	WORD $0x4e321e94  // and.16b	v20, v20, v18
	WORD $0x6e338e94  // cmeq.16b	v20, v20, v19
	WORD $0x4eb71ed6  // orr.16b	v22, v22, v23
	WORD $0x4eb51f17  // orr.16b	v23, v24, v21
	WORD $0x4eb61e98  // orr.16b	v24, v20, v22
	WORD $0x4eb81ef7  // orr.16b	v23, v23, v24
	WORD $0x4e221eb5  // and.16b	v21, v21, v2
	WORD $0x4e0302b5  // tbl.16b	v21, { v21 }, v3
	WORD $0x4e71bab5  // addv.8h	h21, v21
	WORD $0x1e2602a4  // fmov	w4, s21
	WORD $0x4e221e94  // and.16b	v20, v20, v2
	WORD $0x4e030294  // tbl.16b	v20, { v20 }, v3
	WORD $0x4e71ba94  // addv.8h	h20, v20
	WORD $0x1e260286  // fmov	w6, s20
	WORD $0x4e221ed4  // and.16b	v20, v22, v2
	WORD $0x4e030294  // tbl.16b	v20, { v20 }, v3
	WORD $0x4e71ba94  // addv.8h	h20, v20
	WORD $0x1e260293  // fmov	w19, s20
	WORD $0x4e221ef4  // and.16b	v20, v23, v2
	WORD $0x4e030294  // tbl.16b	v20, { v20 }, v3
	WORD $0x4e71ba94  // addv.8h	h20, v20
	WORD $0x1e260287  // fmov	w7, s20
	WORD $0x2a2703e7  // mvn	w7, w7
	WORD $0x32103ce7  // orr	w7, w7, #0xffff0000
	WORD $0x5ac000e7  // rbit	w7, w7
	WORD $0x5ac010e7  // clz	w7, w7
	WORD $0x12800016  // mov	w22, #-1
	WORD $0x1ac722d6  // lsl	w22, w22, w7
	WORD $0x0a360099  // bic	w25, w4, w22
	WORD $0x0a3600da  // bic	w26, w6, w22
	WORD $0x0a360276  // bic	w22, w19, w22
	WORD $0x710040ff  // cmp	w7, #16
	WORD $0x1a990099  // csel	w25, w4, w25, eq
	WORD $0x1a9a00da  // csel	w26, w6, w26, eq
	WORD $0x1a960276  // csel	w22, w19, w22, eq
	WORD $0x51000724  // sub	w4, w25, #1
	WORD $0x6a190084  // ands	w4, w4, w25
	WORD $0x540030c1  // b.ne	LBB0_307 $1560(%rip)
	WORD $0x51000744  // sub	w4, w26, #1
	WORD $0x6a1a0084  // ands	w4, w4, w26
	WORD $0x54003061  // b.ne	LBB0_307 $1548(%rip)
	WORD $0x510006c4  // sub	w4, w22, #1
	WORD $0x6a160084  // ands	w4, w4, w22
	WORD $0x54003001  // b.ne	LBB0_307 $1536(%rip)
	WORD $0x340000d9  // cbz	w25, LBB0_204 $24(%rip)
	WORD $0x5ac00324  // rbit	w4, w25
	WORD $0x5ac01099  // clz	w25, w4
	WORD $0xb10006bf  // cmn	x21, #1
	WORD $0x54003081  // b.ne	LBB0_310 $1552(%rip)
	WORD $0x8b1902f5  // add	x21, x23, x25
LBB0_204:
	WORD $0x340000da  // cbz	w26, LBB0_207 $24(%rip)
	WORD $0x5ac00344  // rbit	w4, w26
	WORD $0x5ac01099  // clz	w25, w4
	WORD $0xb100069f  // cmn	x20, #1
	WORD $0x54002fc1  // b.ne	LBB0_310 $1528(%rip)
	WORD $0x8b1902f4  // add	x20, x23, x25
LBB0_207:
	WORD $0x340000d6  // cbz	w22, LBB0_210 $24(%rip)
	WORD $0x5ac002c4  // rbit	w4, w22
	WORD $0x5ac01096  // clz	w22, w4
	WORD $0xb10004bf  // cmn	x5, #1
	WORD $0x54003fa1  // b.ne	LBB0_338 $2036(%rip)
	WORD $0x8b1602e5  // add	x5, x23, x22
LBB0_210:
	WORD $0x710040ff  // cmp	w7, #16
	WORD $0x540005c1  // b.ne	LBB0_228 $184(%rip)
	WORD $0x910042f7  // add	x23, x23, #16
	WORD $0xd1004318  // sub	x24, x24, #16
	WORD $0x8b180236  // add	x22, x17, x24
	WORD $0xf1003edf  // cmp	x22, #15
	WORD $0x54fff6c8  // b.hi	LBB0_198 $-296(%rip)
	WORD $0x8b170107  // add	x7, x8, x23
	WORD $0xeb17023f  // cmp	x17, x23
	WORD $0x54000500  // b.eq	LBB0_229 $160(%rip)
LBB0_213:
	WORD $0x8b1600f1  // add	x17, x7, x22
	WORD $0x8b070184  // add	x4, x12, x7
	WORD $0xcb1b0097  // sub	x23, x4, x27
	WORD $0xaa0703f8  // mov	x24, x7
	WORD $0x14000006  // b	LBB0_216 $24(%rip)
LBB0_214:
	WORD $0xd10006f5  // sub	x21, x23, #1
LBB0_215:
	WORD $0x910006f7  // add	x23, x23, #1
	WORD $0xaa1803e7  // mov	x7, x24
	WORD $0xd10006d6  // sub	x22, x22, #1
	WORD $0xb40009f6  // cbz	x22, LBB0_248 $316(%rip)
LBB0_216:
	WORD $0x3840171a  // ldrb	w26, [x24], #1
	WORD $0x5100c344  // sub	w4, w26, #48
	WORD $0x7100289f  // cmp	w4, #10
	WORD $0x54ffff23  // b.lo	LBB0_215 $-28(%rip)
	WORD $0x7100b75f  // cmp	w26, #45
	WORD $0x5400014d  // b.le	LBB0_222 $40(%rip)
	WORD $0x7101975f  // cmp	w26, #101
	WORD $0x54000200  // b.eq	LBB0_226 $64(%rip)
	WORD $0x7101175f  // cmp	w26, #69
	WORD $0x540001c0  // b.eq	LBB0_226 $56(%rip)
	WORD $0x7100bb5f  // cmp	w26, #46
	WORD $0x54000241  // b.ne	LBB0_229 $72(%rip)
	WORD $0xb10006bf  // cmn	x21, #1
	WORD $0x54fffdc0  // b.eq	LBB0_214 $-72(%rip)
	WORD $0x14000149  // b	LBB0_306 $1316(%rip)
LBB0_222:
	WORD $0x7100af5f  // cmp	w26, #43
	WORD $0x54000060  // b.eq	LBB0_224 $12(%rip)
	WORD $0x7100b75f  // cmp	w26, #45
	WORD $0x54000161  // b.ne	LBB0_229 $44(%rip)
LBB0_224:
	WORD $0xb10004bf  // cmn	x5, #1
	WORD $0x54002861  // b.ne	LBB0_306 $1292(%rip)
	WORD $0xd10006e5  // sub	x5, x23, #1
	WORD $0x17ffffe6  // b	LBB0_215 $-104(%rip)
LBB0_226:
	WORD $0xb100069f  // cmn	x20, #1
	WORD $0x540027e1  // b.ne	LBB0_306 $1276(%rip)
	WORD $0xd10006f4  // sub	x20, x23, #1
	WORD $0x17ffffe2  // b	LBB0_215 $-120(%rip)
LBB0_228:
	WORD $0x8b274111  // add	x17, x8, w7, uxtw
	WORD $0x8b170227  // add	x7, x17, x23
LBB0_229:
	WORD $0x92800016  // mov	x22, #-1
	WORD $0xb400bef5  // cbz	x21, LBB0_527 $6108(%rip)
LBB0_230:
	WORD $0xb400bec5  // cbz	x5, LBB0_527 $6104(%rip)
	WORD $0xb400beb4  // cbz	x20, LBB0_527 $6100(%rip)
	WORD $0xcb0800e8  // sub	x8, x7, x8
	WORD $0xd1000511  // sub	x17, x8, #1
	WORD $0xeb1102bf  // cmp	x21, x17
	WORD $0x540002a0  // b.eq	LBB0_240 $84(%rip)
	WORD $0xeb1100bf  // cmp	x5, x17
	WORD $0x54000260  // b.eq	LBB0_240 $76(%rip)
	WORD $0xeb11029f  // cmp	x20, x17
	WORD $0x54000220  // b.eq	LBB0_240 $68(%rip)
	WORD $0xf10004b1  // subs	x17, x5, #1
	WORD $0x5400006b  // b.lt	LBB0_237 $12(%rip)
	WORD $0xeb11029f  // cmp	x20, x17
	WORD $0x5400bd41  // b.ne	LBB0_528 $6056(%rip)
LBB0_237:
	WORD $0xaa1402b1  // orr	x17, x21, x20
	WORD $0xb7f80071  // tbnz	x17, #63, LBB0_239 $12(%rip)
	WORD $0xeb1402bf  // cmp	x21, x20
	WORD $0x5400c58a  // b.ge	LBB0_545 $6320(%rip)
LBB0_239:
	WORD $0xd37ffe31  // lsr	x17, x17, #63
	WORD $0x52000231  // eor	w17, w17, #0x1
	WORD $0xd1000684  // sub	x4, x20, #1
	WORD $0xeb0402bf  // cmp	x21, x4
	WORD $0x1a9f17e4  // cset	w4, eq
	WORD $0x6a04023f  // tst	w17, w4
	WORD $0xda940116  // csinv	x22, x8, x20, eq
	WORD $0x14000002  // b	LBB0_241 $8(%rip)
LBB0_240:
	WORD $0xcb0803f6  // neg	x22, x8
LBB0_241:
	WORD $0xb7f8bb76  // tbnz	x22, #63, LBB0_527 $5996(%rip)
LBB0_242:
	WORD $0x8b1e02de  // add	lr, x22, lr
LBB0_243:
	WORD $0xf900003e  // str	lr, [x1]
	WORD $0xaa1b03e8  // mov	x8, x27
	WORD $0x92f00011  // mov	x17, #9223372036854775807
	WORD $0xeb11037f  // cmp	x27, x17
	WORD $0x54006542  // b.hs	LBB0_438 $3240(%rip)
LBB0_244:
	WORD $0xf9400054  // ldr	x20, [x2]
	WORD $0xaa0e03e8  // mov	x8, x14
	WORD $0xb5ff6a54  // cbnz	x20, LBB0_2 $-4792(%rip)
	WORD $0x14000326  // b	LBB0_438 $3224(%rip)
LBB0_245:
	WORD $0x5ac00088  // rbit	w8, w4
	WORD $0x5ac01108  // clz	w8, w8
	WORD $0xaa3e03f1  // mvn	x17, lr
	WORD $0xcb080236  // sub	x22, x17, x8
	WORD $0x17fffdbc  // b	LBB0_135 $-2320(%rip)
LBB0_246:
	WORD $0x8b1e0135  // add	x21, x9, lr
	WORD $0x1400000a  // b	LBB0_250 $40(%rip)
LBB0_247:
	WORD $0x8b1e0135  // add	x21, x9, lr
	WORD $0x1400002b  // b	LBB0_256 $172(%rip)
LBB0_248:
	WORD $0xaa1103e7  // mov	x7, x17
	WORD $0x92800016  // mov	x22, #-1
	WORD $0xb5fff9f5  // cbnz	x21, LBB0_230 $-196(%rip)
	WORD $0x140005c4  // b	LBB0_527 $5904(%rip)
LBB0_249:
	WORD $0xd2800005  // mov	x5, #0
	WORD $0x8b1e0135  // add	x21, x9, lr
	WORD $0x92800008  // mov	x8, #-1
LBB0_250:
	WORD $0xf1008291  // subs	x17, x20, #32
	WORD $0x540035e3  // b.lo	LBB0_343 $1724(%rip)
	WORD $0xad4056b4  // ldp	q20, q21, [x21]
	WORD $0x6e208e96  // cmeq.16b	v22, v20, v0
	WORD $0x6e208eb7  // cmeq.16b	v23, v21, v0
	WORD $0x6e218e94  // cmeq.16b	v20, v20, v1
	WORD $0x6e218eb5  // cmeq.16b	v21, v21, v1
	WORD $0x4e221ed6  // and.16b	v22, v22, v2
	WORD $0x4e0302d6  // tbl.16b	v22, { v22 }, v3
	WORD $0x4e71bad6  // addv.8h	h22, v22
	WORD $0x1e2602c7  // fmov	w7, s22
	WORD $0x4e221ef6  // and.16b	v22, v23, v2
	WORD $0x4e0302d6  // tbl.16b	v22, { v22 }, v3
	WORD $0x4e71bad6  // addv.8h	h22, v22
	WORD $0x1e2602c4  // fmov	w4, s22
	WORD $0x4e221e94  // and.16b	v20, v20, v2
	WORD $0x4e030294  // tbl.16b	v20, { v20 }, v3
	WORD $0x4e71ba94  // addv.8h	h20, v20
	WORD $0x1e260294  // fmov	w20, s20
	WORD $0x4e221eb4  // and.16b	v20, v21, v2
	WORD $0x4e030294  // tbl.16b	v20, { v20 }, v3
	WORD $0x4e71ba94  // addv.8h	h20, v20
	WORD $0x1e260296  // fmov	w22, s20
	WORD $0x33103c87  // bfi	w7, w4, #16, #16
	WORD $0x33103ed4  // bfi	w20, w22, #16, #16
	WORD $0x35003054  // cbnz	w20, LBB0_340 $1544(%rip)
	WORD $0xb50030e5  // cbnz	x5, LBB0_341 $1564(%rip)
	WORD $0xb4003267  // cbz	x7, LBB0_342 $1612(%rip)
LBB0_254:
	WORD $0xdac000f1  // rbit	x17, x7
	WORD $0xdac01231  // clz	x17, x17
	WORD $0xcb0902a4  // sub	x4, x21, x9
	WORD $0x14000056  // b	LBB0_274 $344(%rip)
LBB0_255:
	WORD $0xd2800005  // mov	x5, #0
	WORD $0x8b1e0135  // add	x21, x9, lr
	WORD $0x92800008  // mov	x8, #-1
LBB0_256:
	WORD $0xf1008287  // subs	x7, x20, #32
	WORD $0x54000683  // b.lo	LBB0_264 $208(%rip)
	WORD $0xad4052b5  // ldp	q21, q20, [x21]
	WORD $0x6e208eb6  // cmeq.16b	v22, v21, v0
	WORD $0x4e221ed6  // and.16b	v22, v22, v2
	WORD $0x4e0302d6  // tbl.16b	v22, { v22 }, v3
	WORD $0x4e71bad6  // addv.8h	h22, v22
	WORD $0x1e2602d4  // fmov	w20, s22
	WORD $0x6e208e96  // cmeq.16b	v22, v20, v0
	WORD $0x4e221ed6  // and.16b	v22, v22, v2
	WORD $0x4e0302d6  // tbl.16b	v22, { v22 }, v3
	WORD $0x4e71bad6  // addv.8h	h22, v22
	WORD $0x1e2602c4  // fmov	w4, s22
	WORD $0x6e218eb6  // cmeq.16b	v22, v21, v1
	WORD $0x4e221ed6  // and.16b	v22, v22, v2
	WORD $0x4e0302d6  // tbl.16b	v22, { v22 }, v3
	WORD $0x4e71bad6  // addv.8h	h22, v22
	WORD $0x1e2602d1  // fmov	w17, s22
	WORD $0x6e218e96  // cmeq.16b	v22, v20, v1
	WORD $0x4e221ed6  // and.16b	v22, v22, v2
	WORD $0x4e0302d6  // tbl.16b	v22, { v22 }, v3
	WORD $0x4e71bad6  // addv.8h	h22, v22
	WORD $0x1e2602d6  // fmov	w22, s22
	WORD $0x33103c94  // bfi	w20, w4, #16, #16
	WORD $0x33103ed1  // bfi	w17, w22, #16, #16
	WORD $0x35003211  // cbnz	w17, LBB0_353 $1600(%rip)
	WORD $0xb50032a5  // cbnz	x5, LBB0_354 $1620(%rip)
LBB0_259:
	WORD $0x6e353495  // cmhi.16b	v21, v4, v21
	WORD $0x4e221eb5  // and.16b	v21, v21, v2
	WORD $0x4e0302b5  // tbl.16b	v21, { v21 }, v3
	WORD $0x4e71bab5  // addv.8h	h21, v21
	WORD $0x1e2602b6  // fmov	w22, s21
	WORD $0x6e343494  // cmhi.16b	v20, v4, v20
	WORD $0x4e221e94  // and.16b	v20, v20, v2
	WORD $0x4e030294  // tbl.16b	v20, { v20 }, v3
	WORD $0x4e71ba94  // addv.8h	h20, v20
	WORD $0x1e260291  // fmov	w17, s20
	WORD $0x33103e36  // bfi	w22, w17, #16, #16
	WORD $0xdac002d1  // rbit	x17, x22
	WORD $0xdac01231  // clz	x17, x17
	WORD $0xb4000154  // cbz	x20, LBB0_262 $40(%rip)
	WORD $0xdac00284  // rbit	x4, x20
	WORD $0xdac01087  // clz	x7, x4
	WORD $0xcb0902a5  // sub	x5, x21, x9
	WORD $0xeb07023f  // cmp	x17, x7
	WORD $0x5400b863  // b.lo	LBB0_550 $5900(%rip)
	WORD $0x8b0700b1  // add	x17, x5, x7
	WORD $0x9100063e  // add	lr, x17, #1
	WORD $0xb6fff29e  // tbz	lr, #63, LBB0_243 $-432(%rip)
	WORD $0x14000553  // b	LBB0_514 $5452(%rip)
LBB0_262:
	WORD $0x3500b776  // cbnz	w22, LBB0_549 $5868(%rip)
	WORD $0x910082b5  // add	x21, x21, #32
	WORD $0xaa0703f4  // mov	x20, x7
LBB0_264:
	WORD $0xb5003a65  // cbnz	x5, LBB0_372 $1868(%rip)
	WORD $0xb400aa14  // cbz	x20, LBB0_515 $5440(%rip)
LBB0_266:
	WORD $0xd2800011  // mov	x17, #0
LBB0_267:
	WORD $0x38716aa5  // ldrb	w5, [x21, x17]
	WORD $0x710088bf  // cmp	w5, #34
	WORD $0x540002e0  // b.eq	LBB0_273 $92(%rip)
	WORD $0x710170bf  // cmp	w5, #92
	WORD $0x540000e0  // b.eq	LBB0_271 $28(%rip)
	WORD $0x71007cbf  // cmp	w5, #31
	WORD $0x5400b5e9  // b.ls	LBB0_549 $5820(%rip)
	WORD $0x91000631  // add	x17, x17, #1
	WORD $0xeb11029f  // cmp	x20, x17
	WORD $0x54fffee1  // b.ne	LBB0_267 $-36(%rip)
	WORD $0x14000544  // b	LBB0_515 $5392(%rip)
LBB0_271:
	WORD $0xd1000684  // sub	x4, x20, #1
	WORD $0xeb11009f  // cmp	x4, x17
	WORD $0x5400a820  // b.eq	LBB0_515 $5380(%rip)
	WORD $0x8b1102a4  // add	x4, x21, x17
	WORD $0x8b0c0085  // add	x5, x4, x12
	WORD $0xb100051f  // cmn	x8, #1
	WORD $0x9a8800a8  // csel	x8, x5, x8, eq
	WORD $0x91000895  // add	x21, x4, #2
	WORD $0xcb110284  // sub	x4, x20, x17
	WORD $0xd1000a85  // sub	x5, x20, #2
	WORD $0xd1000894  // sub	x20, x4, #2
	WORD $0xeb1100bf  // cmp	x5, x17
	WORD $0x54fffd01  // b.ne	LBB0_266 $-96(%rip)
	WORD $0x14000536  // b	LBB0_515 $5336(%rip)
LBB0_273:
	WORD $0x8b150184  // add	x4, x12, x21
LBB0_274:
	WORD $0x8b110091  // add	x17, x4, x17
	WORD $0x9100063e  // add	lr, x17, #1
	WORD $0xb6ffee1e  // tbz	lr, #63, LBB0_243 $-576(%rip)
	WORD $0x1400052f  // b	LBB0_514 $5308(%rip)
LBB0_275:
	WORD $0x8b1e0135  // add	x21, x9, lr
	WORD $0x1400000f  // b	LBB0_281 $60(%rip)
LBB0_276:
	WORD $0x8b1e0135  // add	x21, x9, lr
	WORD $0x14000030  // b	LBB0_287 $192(%rip)
LBB0_277:
	WORD $0xaa3e03e8  // mvn	x8, lr
	WORD $0xcb394116  // sub	x22, x8, w25, uxtw
	WORD $0x17fffd31  // b	LBB0_135 $-2876(%rip)
LBB0_278:
	WORD $0xaa3e03e8  // mvn	x8, lr
	WORD $0xcb384116  // sub	x22, x8, w24, uxtw
	WORD $0x17fffd2e  // b	LBB0_135 $-2888(%rip)
LBB0_279:
	WORD $0xaa3e03e8  // mvn	x8, lr
	WORD $0xcb274116  // sub	x22, x8, w7, uxtw
	WORD $0x17fffd2b  // b	LBB0_135 $-2900(%rip)
LBB0_280:
	WORD $0xd2800005  // mov	x5, #0
	WORD $0x8b1e0135  // add	x21, x9, lr
	WORD $0x92800008  // mov	x8, #-1
LBB0_281:
	WORD $0xf1008291  // subs	x17, x20, #32
	WORD $0x54002d43  // b.lo	LBB0_358 $1448(%rip)
	WORD $0xad4056b4  // ldp	q20, q21, [x21]
	WORD $0x6e208e96  // cmeq.16b	v22, v20, v0
	WORD $0x6e208eb7  // cmeq.16b	v23, v21, v0
	WORD $0x6e218e94  // cmeq.16b	v20, v20, v1
	WORD $0x6e218eb5  // cmeq.16b	v21, v21, v1
	WORD $0x4e221ed6  // and.16b	v22, v22, v2
	WORD $0x4e0302d6  // tbl.16b	v22, { v22 }, v3
	WORD $0x4e71bad6  // addv.8h	h22, v22
	WORD $0x1e2602c7  // fmov	w7, s22
	WORD $0x4e221ef6  // and.16b	v22, v23, v2
	WORD $0x4e0302d6  // tbl.16b	v22, { v22 }, v3
	WORD $0x4e71bad6  // addv.8h	h22, v22
	WORD $0x1e2602c4  // fmov	w4, s22
	WORD $0x4e221e94  // and.16b	v20, v20, v2
	WORD $0x4e030294  // tbl.16b	v20, { v20 }, v3
	WORD $0x4e71ba94  // addv.8h	h20, v20
	WORD $0x1e260294  // fmov	w20, s20
	WORD $0x4e221eb4  // and.16b	v20, v21, v2
	WORD $0x4e030294  // tbl.16b	v20, { v20 }, v3
	WORD $0x4e71ba94  // addv.8h	h20, v20
	WORD $0x1e260296  // fmov	w22, s20
	WORD $0x33103c87  // bfi	w7, w4, #16, #16
	WORD $0x33103ed4  // bfi	w20, w22, #16, #16
	WORD $0x350027b4  // cbnz	w20, LBB0_355 $1268(%rip)
	WORD $0xb5002845  // cbnz	x5, LBB0_356 $1288(%rip)
	WORD $0xb40029c7  // cbz	x7, LBB0_357 $1336(%rip)
LBB0_285:
	WORD $0xdac000f1  // rbit	x17, x7
	WORD $0xdac01231  // clz	x17, x17
	WORD $0xcb0902a4  // sub	x4, x21, x9
	WORD $0x14000056  // b	LBB0_305 $344(%rip)
LBB0_286:
	WORD $0xd2800005  // mov	x5, #0
	WORD $0x8b1e0135  // add	x21, x9, lr
	WORD $0x92800008  // mov	x8, #-1
LBB0_287:
	WORD $0xf1008287  // subs	x7, x20, #32
	WORD $0x54000683  // b.lo	LBB0_295 $208(%rip)
	WORD $0xad4052b5  // ldp	q21, q20, [x21]
	WORD $0x6e208eb6  // cmeq.16b	v22, v21, v0
	WORD $0x4e221ed6  // and.16b	v22, v22, v2
	WORD $0x4e0302d6  // tbl.16b	v22, { v22 }, v3
	WORD $0x4e71bad6  // addv.8h	h22, v22
	WORD $0x1e2602d4  // fmov	w20, s22
	WORD $0x6e208e96  // cmeq.16b	v22, v20, v0
	WORD $0x4e221ed6  // and.16b	v22, v22, v2
	WORD $0x4e0302d6  // tbl.16b	v22, { v22 }, v3
	WORD $0x4e71bad6  // addv.8h	h22, v22
	WORD $0x1e2602c4  // fmov	w4, s22
	WORD $0x6e218eb6  // cmeq.16b	v22, v21, v1
	WORD $0x4e221ed6  // and.16b	v22, v22, v2
	WORD $0x4e0302d6  // tbl.16b	v22, { v22 }, v3
	WORD $0x4e71bad6  // addv.8h	h22, v22
	WORD $0x1e2602d1  // fmov	w17, s22
	WORD $0x6e218e96  // cmeq.16b	v22, v20, v1
	WORD $0x4e221ed6  // and.16b	v22, v22, v2
	WORD $0x4e0302d6  // tbl.16b	v22, { v22 }, v3
	WORD $0x4e71bad6  // addv.8h	h22, v22
	WORD $0x1e2602d6  // fmov	w22, s22
	WORD $0x33103c94  // bfi	w20, w4, #16, #16
	WORD $0x33103ed1  // bfi	w17, w22, #16, #16
	WORD $0x35002971  // cbnz	w17, LBB0_368 $1324(%rip)
	WORD $0xb5002a05  // cbnz	x5, LBB0_369 $1344(%rip)
LBB0_290:
	WORD $0x6e353495  // cmhi.16b	v21, v4, v21
	WORD $0x4e221eb5  // and.16b	v21, v21, v2
	WORD $0x4e0302b5  // tbl.16b	v21, { v21 }, v3
	WORD $0x4e71bab5  // addv.8h	h21, v21
	WORD $0x1e2602b6  // fmov	w22, s21
	WORD $0x6e343494  // cmhi.16b	v20, v4, v20
	WORD $0x4e221e94  // and.16b	v20, v20, v2
	WORD $0x4e030294  // tbl.16b	v20, { v20 }, v3
	WORD $0x4e71ba94  // addv.8h	h20, v20
	WORD $0x1e260291  // fmov	w17, s20
	WORD $0x33103e36  // bfi	w22, w17, #16, #16
	WORD $0xdac002d1  // rbit	x17, x22
	WORD $0xdac01231  // clz	x17, x17
	WORD $0xb4000154  // cbz	x20, LBB0_293 $40(%rip)
	WORD $0xdac00284  // rbit	x4, x20
	WORD $0xdac01087  // clz	x7, x4
	WORD $0xcb0902a5  // sub	x5, x21, x9
	WORD $0xeb07023f  // cmp	x17, x7
	WORD $0x5400a743  // b.lo	LBB0_550 $5352(%rip)
	WORD $0x8b0700b1  // add	x17, x5, x7
	WORD $0x9100063e  // add	lr, x17, #1
	WORD $0xb6ffa99e  // tbz	lr, #63, LBB0_149 $-2768(%rip)
	WORD $0x140004ca  // b	LBB0_514 $4904(%rip)
LBB0_293:
	WORD $0x3500a656  // cbnz	w22, LBB0_549 $5320(%rip)
	WORD $0x910082b5  // add	x21, x21, #32
	WORD $0xaa0703f4  // mov	x20, x7
LBB0_295:
	WORD $0xb5002b45  // cbnz	x5, LBB0_376 $1384(%rip)
	WORD $0xb40098f4  // cbz	x20, LBB0_515 $4892(%rip)
LBB0_297:
	WORD $0xd2800011  // mov	x17, #0
LBB0_298:
	WORD $0x38716aa5  // ldrb	w5, [x21, x17]
	WORD $0x710088bf  // cmp	w5, #34
	WORD $0x540002e0  // b.eq	LBB0_304 $92(%rip)
	WORD $0x710170bf  // cmp	w5, #92
	WORD $0x540000e0  // b.eq	LBB0_302 $28(%rip)
	WORD $0x71007cbf  // cmp	w5, #31
	WORD $0x5400a4c9  // b.ls	LBB0_549 $5272(%rip)
	WORD $0x91000631  // add	x17, x17, #1
	WORD $0xeb11029f  // cmp	x20, x17
	WORD $0x54fffee1  // b.ne	LBB0_298 $-36(%rip)
	WORD $0x140004bb  // b	LBB0_515 $4844(%rip)
LBB0_302:
	WORD $0xd1000684  // sub	x4, x20, #1
	WORD $0xeb11009f  // cmp	x4, x17
	WORD $0x54009700  // b.eq	LBB0_515 $4832(%rip)
	WORD $0x8b1102a4  // add	x4, x21, x17
	WORD $0x8b0c0085  // add	x5, x4, x12
	WORD $0xb100051f  // cmn	x8, #1
	WORD $0x9a8800a8  // csel	x8, x5, x8, eq
	WORD $0x91000895  // add	x21, x4, #2
	WORD $0xcb110284  // sub	x4, x20, x17
	WORD $0xd1000a85  // sub	x5, x20, #2
	WORD $0xd1000894  // sub	x20, x4, #2
	WORD $0xeb1100bf  // cmp	x5, x17
	WORD $0x54fffd01  // b.ne	LBB0_297 $-96(%rip)
	WORD $0x140004ad  // b	LBB0_515 $4788(%rip)
LBB0_304:
	WORD $0x8b150184  // add	x4, x12, x21
LBB0_305:
	WORD $0x8b110091  // add	x17, x4, x17
	WORD $0x9100063e  // add	lr, x17, #1
	WORD $0xb6ffa51e  // tbz	lr, #63, LBB0_149 $-2912(%rip)
	WORD $0x140004a6  // b	LBB0_514 $4760(%rip)
LBB0_306:
	WORD $0xcb1703f6  // neg	x22, x23
	WORD $0x17fffee2  // b	LBB0_241 $-1144(%rip)
LBB0_307:
	WORD $0x5ac00088  // rbit	w8, w4
	WORD $0x5ac01108  // clz	w8, w8
	WORD $0xaa3703f1  // mvn	x17, x23
	WORD $0xcb080236  // sub	x22, x17, x8
	WORD $0x17fffedd  // b	LBB0_241 $-1164(%rip)
LBB0_308:
	WORD $0x8b1e0135  // add	x21, x9, lr
	WORD $0x14000009  // b	LBB0_312 $36(%rip)
LBB0_309:
	WORD $0x8b1e0135  // add	x21, x9, lr
	WORD $0x1400002a  // b	LBB0_318 $168(%rip)
LBB0_310:
	WORD $0xaa3703e8  // mvn	x8, x23
	WORD $0xcb394116  // sub	x22, x8, w25, uxtw
	WORD $0x17fffed6  // b	LBB0_241 $-1192(%rip)
LBB0_311:
	WORD $0xd2800005  // mov	x5, #0
	WORD $0x8b1e0135  // add	x21, x9, lr
	WORD $0x92800008  // mov	x8, #-1
LBB0_312:
	WORD $0xf1008291  // subs	x17, x20, #32
	WORD $0x54002883  // b.lo	LBB0_381 $1296(%rip)
	WORD $0xad4056b4  // ldp	q20, q21, [x21]
	WORD $0x6e208e96  // cmeq.16b	v22, v20, v0
	WORD $0x6e208eb7  // cmeq.16b	v23, v21, v0
	WORD $0x6e218e94  // cmeq.16b	v20, v20, v1
	WORD $0x6e218eb5  // cmeq.16b	v21, v21, v1
	WORD $0x4e221ed6  // and.16b	v22, v22, v2
	WORD $0x4e0302d6  // tbl.16b	v22, { v22 }, v3
	WORD $0x4e71bad6  // addv.8h	h22, v22
	WORD $0x1e2602c7  // fmov	w7, s22
	WORD $0x4e221ef6  // and.16b	v22, v23, v2
	WORD $0x4e0302d6  // tbl.16b	v22, { v22 }, v3
	WORD $0x4e71bad6  // addv.8h	h22, v22
	WORD $0x1e2602c4  // fmov	w4, s22
	WORD $0x4e221e94  // and.16b	v20, v20, v2
	WORD $0x4e030294  // tbl.16b	v20, { v20 }, v3
	WORD $0x4e71ba94  // addv.8h	h20, v20
	WORD $0x1e260294  // fmov	w20, s20
	WORD $0x4e221eb4  // and.16b	v20, v21, v2
	WORD $0x4e030294  // tbl.16b	v20, { v20 }, v3
	WORD $0x4e71ba94  // addv.8h	h20, v20
	WORD $0x1e260286  // fmov	w6, s20
	WORD $0x33103c87  // bfi	w7, w4, #16, #16
	WORD $0x33103cd4  // bfi	w20, w6, #16, #16
	WORD $0x350022f4  // cbnz	w20, LBB0_378 $1116(%rip)
	WORD $0xb5002385  // cbnz	x5, LBB0_379 $1136(%rip)
	WORD $0xb4002507  // cbz	x7, LBB0_380 $1184(%rip)
LBB0_316:
	WORD $0xdac000f1  // rbit	x17, x7
	WORD $0xdac01231  // clz	x17, x17
	WORD $0xcb0902a4  // sub	x4, x21, x9
	WORD $0x14000056  // b	LBB0_336 $344(%rip)
LBB0_317:
	WORD $0xd2800005  // mov	x5, #0
	WORD $0x8b1e0135  // add	x21, x9, lr
	WORD $0x92800008  // mov	x8, #-1
LBB0_318:
	WORD $0xf1008287  // subs	x7, x20, #32
	WORD $0x54000683  // b.lo	LBB0_326 $208(%rip)
	WORD $0xad4052b5  // ldp	q21, q20, [x21]
	WORD $0x6e208eb6  // cmeq.16b	v22, v21, v0
	WORD $0x4e221ed6  // and.16b	v22, v22, v2
	WORD $0x4e0302d6  // tbl.16b	v22, { v22 }, v3
	WORD $0x4e71bad6  // addv.8h	h22, v22
	WORD $0x1e2602d4  // fmov	w20, s22
	WORD $0x6e208e96  // cmeq.16b	v22, v20, v0
	WORD $0x4e221ed6  // and.16b	v22, v22, v2
	WORD $0x4e0302d6  // tbl.16b	v22, { v22 }, v3
	WORD $0x4e71bad6  // addv.8h	h22, v22
	WORD $0x1e2602c4  // fmov	w4, s22
	WORD $0x6e218eb6  // cmeq.16b	v22, v21, v1
	WORD $0x4e221ed6  // and.16b	v22, v22, v2
	WORD $0x4e0302d6  // tbl.16b	v22, { v22 }, v3
	WORD $0x4e71bad6  // addv.8h	h22, v22
	WORD $0x1e2602d1  // fmov	w17, s22
	WORD $0x6e218e96  // cmeq.16b	v22, v20, v1
	WORD $0x4e221ed6  // and.16b	v22, v22, v2
	WORD $0x4e0302d6  // tbl.16b	v22, { v22 }, v3
	WORD $0x4e71bad6  // addv.8h	h22, v22
	WORD $0x1e2602c6  // fmov	w6, s22
	WORD $0x33103c94  // bfi	w20, w4, #16, #16
	WORD $0x33103cd1  // bfi	w17, w6, #16, #16
	WORD $0x350024b1  // cbnz	w17, LBB0_391 $1172(%rip)
	WORD $0xb5002545  // cbnz	x5, LBB0_392 $1192(%rip)
LBB0_321:
	WORD $0x6e353495  // cmhi.16b	v21, v4, v21
	WORD $0x4e221eb5  // and.16b	v21, v21, v2
	WORD $0x4e0302b5  // tbl.16b	v21, { v21 }, v3
	WORD $0x4e71bab5  // addv.8h	h21, v21
	WORD $0x1e2602b6  // fmov	w22, s21
	WORD $0x6e343494  // cmhi.16b	v20, v4, v20
	WORD $0x4e221e94  // and.16b	v20, v20, v2
	WORD $0x4e030294  // tbl.16b	v20, { v20 }, v3
	WORD $0x4e71ba94  // addv.8h	h20, v20
	WORD $0x1e260291  // fmov	w17, s20
	WORD $0x33103e36  // bfi	w22, w17, #16, #16
	WORD $0xdac002d1  // rbit	x17, x22
	WORD $0xdac01231  // clz	x17, x17
	WORD $0xb4000154  // cbz	x20, LBB0_324 $40(%rip)
	WORD $0xdac00284  // rbit	x4, x20
	WORD $0xdac01087  // clz	x7, x4
	WORD $0xcb0902a5  // sub	x5, x21, x9
	WORD $0xeb07023f  // cmp	x17, x7
	WORD $0x54009603  // b.lo	LBB0_550 $4800(%rip)
	WORD $0x8b0700b1  // add	x17, x5, x7
	WORD $0x9100063e  // add	lr, x17, #1
	WORD $0xb6ffd03e  // tbz	lr, #63, LBB0_243 $-1532(%rip)
	WORD $0x14000440  // b	LBB0_514 $4352(%rip)
LBB0_324:
	WORD $0x35009516  // cbnz	w22, LBB0_549 $4768(%rip)
	WORD $0x910082b5  // add	x21, x21, #32
	WORD $0xaa0703f4  // mov	x20, x7
LBB0_326:
	WORD $0xb5002485  // cbnz	x5, LBB0_395 $1168(%rip)
	WORD $0xb40087b4  // cbz	x20, LBB0_515 $4340(%rip)
LBB0_328:
	WORD $0xd2800011  // mov	x17, #0
LBB0_329:
	WORD $0x38716aa5  // ldrb	w5, [x21, x17]
	WORD $0x710088bf  // cmp	w5, #34
	WORD $0x540002e0  // b.eq	LBB0_335 $92(%rip)
	WORD $0x710170bf  // cmp	w5, #92
	WORD $0x540000e0  // b.eq	LBB0_333 $28(%rip)
	WORD $0x71007cbf  // cmp	w5, #31
	WORD $0x54009389  // b.ls	LBB0_549 $4720(%rip)
	WORD $0x91000631  // add	x17, x17, #1
	WORD $0xeb11029f  // cmp	x20, x17
	WORD $0x54fffee1  // b.ne	LBB0_329 $-36(%rip)
	WORD $0x14000431  // b	LBB0_515 $4292(%rip)
LBB0_333:
	WORD $0xd1000684  // sub	x4, x20, #1
	WORD $0xeb11009f  // cmp	x4, x17
	WORD $0x540085c0  // b.eq	LBB0_515 $4280(%rip)
	WORD $0x8b1102a4  // add	x4, x21, x17
	WORD $0x8b0c0085  // add	x5, x4, x12
	WORD $0xb100051f  // cmn	x8, #1
	WORD $0x9a8800a8  // csel	x8, x5, x8, eq
	WORD $0x91000895  // add	x21, x4, #2
	WORD $0xcb110284  // sub	x4, x20, x17
	WORD $0xd1000a85  // sub	x5, x20, #2
	WORD $0xd1000894  // sub	x20, x4, #2
	WORD $0xeb1100bf  // cmp	x5, x17
	WORD $0x54fffd01  // b.ne	LBB0_328 $-96(%rip)
	WORD $0x14000423  // b	LBB0_515 $4236(%rip)
LBB0_335:
	WORD $0x8b150184  // add	x4, x12, x21
LBB0_336:
	WORD $0x8b110091  // add	x17, x4, x17
	WORD $0x9100063e  // add	lr, x17, #1
	WORD $0xb6ffcbbe  // tbz	lr, #63, LBB0_243 $-1676(%rip)
	WORD $0x1400041c  // b	LBB0_514 $4208(%rip)
LBB0_337:
	WORD $0x92800008  // mov	x8, #-1
	WORD $0xaa0503f7  // mov	x23, x5
	WORD $0xaa1603e7  // mov	x7, x22
	WORD $0x92800014  // mov	x20, #-1
	WORD $0x92800015  // mov	x21, #-1
	WORD $0x17fffb37  // b	LBB0_83 $-4900(%rip)
LBB0_338:
	WORD $0xaa3703e8  // mvn	x8, x23
	WORD $0xcb364116  // sub	x22, x8, w22, uxtw
	WORD $0x17fffe51  // b	LBB0_241 $-1724(%rip)
LBB0_339:
	WORD $0x92800015  // mov	x21, #-1
	WORD $0xaa0803e7  // mov	x7, x8
	WORD $0xaa1103f6  // mov	x22, x17
	WORD $0x92800014  // mov	x20, #-1
	WORD $0x92800005  // mov	x5, #-1
	WORD $0x17fffe07  // b	LBB0_213 $-2020(%rip)
LBB0_340:
	WORD $0xdac00284  // rbit	x4, x20
	WORD $0xdac01084  // clz	x4, x4
	WORD $0xcb0902b6  // sub	x22, x21, x9
	WORD $0x8b0402c4  // add	x4, x22, x4
	WORD $0xb100051f  // cmn	x8, #1
	WORD $0x9a841108  // csel	x8, x8, x4, ne
LBB0_341:
	WORD $0x0a250284  // bic	w4, w20, w5
	WORD $0x531f7896  // lsl	w22, w4, #1
	WORD $0x331f7885  // bfi	w5, w4, #1, #31
	WORD $0x0a360294  // bic	w20, w20, w22
	WORD $0x1201f294  // and	w20, w20, #0xaaaaaaaa
	WORD $0x2b040284  // adds	w4, w20, w4
	WORD $0x3200f3e6  // mov	w6, #1431655765
	WORD $0x4a0404c4  // eor	w4, w6, w4, lsl #1
	WORD $0x0a050084  // and	w4, w4, w5
	WORD $0x1a9f37e5  // cset	w5, hs
	WORD $0x2a2403e4  // mvn	w4, w4
	WORD $0x8a070087  // and	x7, x4, x7
	WORD $0xb5ffcde7  // cbnz	x7, LBB0_254 $-1604(%rip)
LBB0_342:
	WORD $0x910082b5  // add	x21, x21, #32
	WORD $0xaa1103f4  // mov	x20, x17
LBB0_343:
	WORD $0xb5000e65  // cbnz	x5, LBB0_370 $460(%rip)
	WORD $0xb4000314  // cbz	x20, LBB0_352 $96(%rip)
LBB0_345:
	WORD $0xaa1503e7  // mov	x7, x21
	WORD $0x384014f1  // ldrb	w17, [x7], #1
	WORD $0x71008a3f  // cmp	w17, #34
	WORD $0x54000260  // b.eq	LBB0_351 $76(%rip)
	WORD $0xd1000685  // sub	x5, x20, #1
	WORD $0x7101723f  // cmp	w17, #92
	WORD $0x540000a0  // b.eq	LBB0_348 $20(%rip)
	WORD $0xaa0703f5  // mov	x21, x7
	WORD $0xaa0503f4  // mov	x20, x5
	WORD $0xb5fffee5  // cbnz	x5, LBB0_345 $-36(%rip)
	WORD $0x14000009  // b	LBB0_350 $36(%rip)
LBB0_348:
	WORD $0xb4007d85  // cbz	x5, LBB0_515 $4016(%rip)
	WORD $0x8b0b00e4  // add	x4, x7, x11
	WORD $0xb100051f  // cmn	x8, #1
	WORD $0x9a880088  // csel	x8, x4, x8, eq
	WORD $0x91000ab5  // add	x21, x21, #2
	WORD $0xd1000a85  // sub	x5, x20, #2
	WORD $0xaa0503f4  // mov	x20, x5
	WORD $0xb5fffdc5  // cbnz	x5, LBB0_345 $-72(%rip)
LBB0_350:
	WORD $0x71008a3f  // cmp	w17, #34
	WORD $0x54000060  // b.eq	LBB0_352 $12(%rip)
	WORD $0x140003e2  // b	LBB0_515 $3976(%rip)
LBB0_351:
	WORD $0xaa0703f5  // mov	x21, x7
LBB0_352:
	WORD $0xcb0902be  // sub	lr, x21, x9
	WORD $0xb6ffc3be  // tbz	lr, #63, LBB0_243 $-1932(%rip)
	WORD $0x140003dc  // b	LBB0_514 $3952(%rip)
LBB0_353:
	WORD $0xdac00224  // rbit	x4, x17
	WORD $0xdac01084  // clz	x4, x4
	WORD $0xcb0902b6  // sub	x22, x21, x9
	WORD $0x8b0402c4  // add	x4, x22, x4
	WORD $0xb100051f  // cmn	x8, #1
	WORD $0x9a841108  // csel	x8, x8, x4, ne
LBB0_354:
	WORD $0x0a250224  // bic	w4, w17, w5
	WORD $0x531f7896  // lsl	w22, w4, #1
	WORD $0x331f7885  // bfi	w5, w4, #1, #31
	WORD $0x0a360231  // bic	w17, w17, w22
	WORD $0x1201f231  // and	w17, w17, #0xaaaaaaaa
	WORD $0x2b040231  // adds	w17, w17, w4
	WORD $0x3200f3e4  // mov	w4, #1431655765
	WORD $0x4a110491  // eor	w17, w4, w17, lsl #1
	WORD $0x0a050231  // and	w17, w17, w5
	WORD $0x1a9f37e5  // cset	w5, hs
	WORD $0x2a3103f1  // mvn	w17, w17
	WORD $0x8a140234  // and	x20, x17, x20
	WORD $0x17fffe60  // b	LBB0_259 $-1664(%rip)
LBB0_355:
	WORD $0xdac00284  // rbit	x4, x20
	WORD $0xdac01084  // clz	x4, x4
	WORD $0xcb0902b6  // sub	x22, x21, x9
	WORD $0x8b0402c4  // add	x4, x22, x4
	WORD $0xb100051f  // cmn	x8, #1
	WORD $0x9a841108  // csel	x8, x8, x4, ne
LBB0_356:
	WORD $0x0a250284  // bic	w4, w20, w5
	WORD $0x531f7896  // lsl	w22, w4, #1
	WORD $0x331f7885  // bfi	w5, w4, #1, #31
	WORD $0x0a360294  // bic	w20, w20, w22
	WORD $0x1201f294  // and	w20, w20, #0xaaaaaaaa
	WORD $0x2b040284  // adds	w4, w20, w4
	WORD $0x3200f3e6  // mov	w6, #1431655765
	WORD $0x4a0404c4  // eor	w4, w6, w4, lsl #1
	WORD $0x0a050084  // and	w4, w4, w5
	WORD $0x1a9f37e5  // cset	w5, hs
	WORD $0x2a2403e4  // mvn	w4, w4
	WORD $0x8a070087  // and	x7, x4, x7
	WORD $0xb5ffd687  // cbnz	x7, LBB0_285 $-1328(%rip)
LBB0_357:
	WORD $0x910082b5  // add	x21, x21, #32
	WORD $0xaa1103f4  // mov	x20, x17
LBB0_358:
	WORD $0xb50007e5  // cbnz	x5, LBB0_374 $252(%rip)
	WORD $0xb4000314  // cbz	x20, LBB0_367 $96(%rip)
LBB0_360:
	WORD $0xaa1503e7  // mov	x7, x21
	WORD $0x384014f1  // ldrb	w17, [x7], #1
	WORD $0x71008a3f  // cmp	w17, #34
	WORD $0x54000260  // b.eq	LBB0_366 $76(%rip)
	WORD $0xd1000685  // sub	x5, x20, #1
	WORD $0x7101723f  // cmp	w17, #92
	WORD $0x540000a0  // b.eq	LBB0_363 $20(%rip)
	WORD $0xaa0703f5  // mov	x21, x7
	WORD $0xaa0503f4  // mov	x20, x5
	WORD $0xb5fffee5  // cbnz	x5, LBB0_360 $-36(%rip)
	WORD $0x14000009  // b	LBB0_365 $36(%rip)
LBB0_363:
	WORD $0xb4007505  // cbz	x5, LBB0_515 $3744(%rip)
	WORD $0x8b0b00e4  // add	x4, x7, x11
	WORD $0xb100051f  // cmn	x8, #1
	WORD $0x9a880088  // csel	x8, x4, x8, eq
	WORD $0x91000ab5  // add	x21, x21, #2
	WORD $0xd1000a85  // sub	x5, x20, #2
	WORD $0xaa0503f4  // mov	x20, x5
	WORD $0xb5fffdc5  // cbnz	x5, LBB0_360 $-72(%rip)
LBB0_365:
	WORD $0x71008a3f  // cmp	w17, #34
	WORD $0x54000060  // b.eq	LBB0_367 $12(%rip)
	WORD $0x1400039e  // b	LBB0_515 $3704(%rip)
LBB0_366:
	WORD $0xaa0703f5  // mov	x21, x7
LBB0_367:
	WORD $0xcb0902be  // sub	lr, x21, x9
	WORD $0xb6ff835e  // tbz	lr, #63, LBB0_149 $-3992(%rip)
	WORD $0x14000398  // b	LBB0_514 $3680(%rip)
LBB0_368:
	WORD $0xdac00224  // rbit	x4, x17
	WORD $0xdac01084  // clz	x4, x4
	WORD $0xcb0902b6  // sub	x22, x21, x9
	WORD $0x8b0402c4  // add	x4, x22, x4
	WORD $0xb100051f  // cmn	x8, #1
	WORD $0x9a841108  // csel	x8, x8, x4, ne
LBB0_369:
	WORD $0x0a250224  // bic	w4, w17, w5
	WORD $0x531f7896  // lsl	w22, w4, #1
	WORD $0x331f7885  // bfi	w5, w4, #1, #31
	WORD $0x0a360231  // bic	w17, w17, w22
	WORD $0x1201f231  // and	w17, w17, #0xaaaaaaaa
	WORD $0x2b040231  // adds	w17, w17, w4
	WORD $0x3200f3e4  // mov	w4, #1431655765
	WORD $0x4a110491  // eor	w17, w4, w17, lsl #1
	WORD $0x0a050231  // and	w17, w17, w5
	WORD $0x1a9f37e5  // cset	w5, hs
	WORD $0x2a3103f1  // mvn	w17, w17
	WORD $0x8a140234  // and	x20, x17, x20
	WORD $0x17fffea5  // b	LBB0_290 $-1388(%rip)
LBB0_370:
	WORD $0xb40070d4  // cbz	x20, LBB0_515 $3608(%rip)
	WORD $0x8b0b02b1  // add	x17, x21, x11
	WORD $0xb100051f  // cmn	x8, #1
	WORD $0x9a880228  // csel	x8, x17, x8, eq
	WORD $0x910006b5  // add	x21, x21, #1
	WORD $0xd1000694  // sub	x20, x20, #1
	WORD $0xb5fff134  // cbnz	x20, LBB0_345 $-476(%rip)
	WORD $0x17ffff9f  // b	LBB0_352 $-388(%rip)
LBB0_372:
	WORD $0xb4006fd4  // cbz	x20, LBB0_515 $3576(%rip)
	WORD $0x8b0b02b1  // add	x17, x21, x11
	WORD $0xb100051f  // cmn	x8, #1
	WORD $0x9a880228  // csel	x8, x17, x8, eq
	WORD $0x910006b5  // add	x21, x21, #1
	WORD $0xd1000694  // sub	x20, x20, #1
	WORD $0xb5ffc534  // cbnz	x20, LBB0_266 $-1884(%rip)
	WORD $0x14000377  // b	LBB0_515 $3548(%rip)
LBB0_374:
	WORD $0xb4006ed4  // cbz	x20, LBB0_515 $3544(%rip)
	WORD $0x8b0b02b1  // add	x17, x21, x11
	WORD $0xb100051f  // cmn	x8, #1
	WORD $0x9a880228  // csel	x8, x17, x8, eq
	WORD $0x910006b5  // add	x21, x21, #1
	WORD $0xd1000694  // sub	x20, x20, #1
	WORD $0xb5fff7b4  // cbnz	x20, LBB0_360 $-268(%rip)
	WORD $0x17ffffd3  // b	LBB0_367 $-180(%rip)
LBB0_376:
	WORD $0xb4006dd4  // cbz	x20, LBB0_515 $3512(%rip)
	WORD $0x8b0b02b1  // add	x17, x21, x11
	WORD $0xb100051f  // cmn	x8, #1
	WORD $0x9a880228  // csel	x8, x17, x8, eq
	WORD $0x910006b5  // add	x21, x21, #1
	WORD $0xd1000694  // sub	x20, x20, #1
	WORD $0xb5ffd454  // cbnz	x20, LBB0_297 $-1400(%rip)
	WORD $0x14000367  // b	LBB0_515 $3484(%rip)
LBB0_378:
	WORD $0xdac00284  // rbit	x4, x20
	WORD $0xdac01084  // clz	x4, x4
	WORD $0xcb0902a6  // sub	x6, x21, x9
	WORD $0x8b0400c4  // add	x4, x6, x4
	WORD $0xb100051f  // cmn	x8, #1
	WORD $0x9a841108  // csel	x8, x8, x4, ne
LBB0_379:
	WORD $0x0a250284  // bic	w4, w20, w5
	WORD $0x531f7886  // lsl	w6, w4, #1
	WORD $0x331f7885  // bfi	w5, w4, #1, #31
	WORD $0x0a260286  // bic	w6, w20, w6
	WORD $0x1201f0c6  // and	w6, w6, #0xaaaaaaaa
	WORD $0x2b0400c4  // adds	w4, w6, w4
	WORD $0x3200f3e6  // mov	w6, #1431655765
	WORD $0x4a0404c4  // eor	w4, w6, w4, lsl #1
	WORD $0x0a050084  // and	w4, w4, w5
	WORD $0x1a9f37e5  // cset	w5, hs
	WORD $0x2a2403e4  // mvn	w4, w4
	WORD $0x8a070087  // and	x7, x4, x7
	WORD $0xb5ffdb47  // cbnz	x7, LBB0_316 $-1176(%rip)
LBB0_380:
	WORD $0x910082b5  // add	x21, x21, #32
	WORD $0xaa1103f4  // mov	x20, x17
LBB0_381:
	WORD $0xb50005e5  // cbnz	x5, LBB0_393 $188(%rip)
	WORD $0xb4000314  // cbz	x20, LBB0_390 $96(%rip)
LBB0_383:
	WORD $0xaa1503e7  // mov	x7, x21
	WORD $0x384014f1  // ldrb	w17, [x7], #1
	WORD $0x71008a3f  // cmp	w17, #34
	WORD $0x54000260  // b.eq	LBB0_389 $76(%rip)
	WORD $0xd1000685  // sub	x5, x20, #1
	WORD $0x7101723f  // cmp	w17, #92
	WORD $0x540000a0  // b.eq	LBB0_386 $20(%rip)
	WORD $0xaa0703f5  // mov	x21, x7
	WORD $0xaa0503f4  // mov	x20, x5
	WORD $0xb5fffee5  // cbnz	x5, LBB0_383 $-36(%rip)
	WORD $0x14000009  // b	LBB0_388 $36(%rip)
LBB0_386:
	WORD $0xb4006885  // cbz	x5, LBB0_515 $3344(%rip)
	WORD $0x8b0b00e4  // add	x4, x7, x11
	WORD $0xb100051f  // cmn	x8, #1
	WORD $0x9a880088  // csel	x8, x4, x8, eq
	WORD $0x91000ab5  // add	x21, x21, #2
	WORD $0xd1000a85  // sub	x5, x20, #2
	WORD $0xaa0503f4  // mov	x20, x5
	WORD $0xb5fffdc5  // cbnz	x5, LBB0_383 $-72(%rip)
LBB0_388:
	WORD $0x71008a3f  // cmp	w17, #34
	WORD $0x54000060  // b.eq	LBB0_390 $12(%rip)
	WORD $0x1400033a  // b	LBB0_515 $3304(%rip)
LBB0_389:
	WORD $0xaa0703f5  // mov	x21, x7
LBB0_390:
	WORD $0xcb0902be  // sub	lr, x21, x9
	WORD $0xb6ffaebe  // tbz	lr, #63, LBB0_243 $-2604(%rip)
	WORD $0x14000334  // b	LBB0_514 $3280(%rip)
LBB0_391:
	WORD $0xdac00224  // rbit	x4, x17
	WORD $0xdac01084  // clz	x4, x4
	WORD $0xcb0902a6  // sub	x6, x21, x9
	WORD $0x8b0400c4  // add	x4, x6, x4
	WORD $0xb100051f  // cmn	x8, #1
	WORD $0x9a841108  // csel	x8, x8, x4, ne
LBB0_392:
	WORD $0x0a250224  // bic	w4, w17, w5
	WORD $0x531f7886  // lsl	w6, w4, #1
	WORD $0x331f7885  // bfi	w5, w4, #1, #31
	WORD $0x0a260231  // bic	w17, w17, w6
	WORD $0x1201f231  // and	w17, w17, #0xaaaaaaaa
	WORD $0x2b040231  // adds	w17, w17, w4
	WORD $0x3200f3e4  // mov	w4, #1431655765
	WORD $0x4a110491  // eor	w17, w4, w17, lsl #1
	WORD $0x0a050231  // and	w17, w17, w5
	WORD $0x1a9f37e5  // cset	w5, hs
	WORD $0x2a3103f1  // mvn	w17, w17
	WORD $0x8a140234  // and	x20, x17, x20
	WORD $0x17fffecb  // b	LBB0_321 $-1236(%rip)
LBB0_393:
	WORD $0xb4006454  // cbz	x20, LBB0_515 $3208(%rip)
	WORD $0x8b0b02b1  // add	x17, x21, x11
	WORD $0xb100051f  // cmn	x8, #1
	WORD $0x9a880228  // csel	x8, x17, x8, eq
	WORD $0x910006b5  // add	x21, x21, #1
	WORD $0xd1000694  // sub	x20, x20, #1
	WORD $0xb5fff9b4  // cbnz	x20, LBB0_383 $-204(%rip)
	WORD $0x17ffffe3  // b	LBB0_390 $-116(%rip)
LBB0_395:
	WORD $0xb4006354  // cbz	x20, LBB0_515 $3176(%rip)
	WORD $0x8b0b02b1  // add	x17, x21, x11
	WORD $0xb100051f  // cmn	x8, #1
	WORD $0x9a880228  // csel	x8, x17, x8, eq
	WORD $0x910006b5  // add	x21, x21, #1
	WORD $0xd1000694  // sub	x20, x20, #1
	WORD $0xb5ffdb14  // cbnz	x20, LBB0_328 $-1184(%rip)
	WORD $0x14000313  // b	LBB0_515 $3148(%rip)
LBB0_397:
	WORD $0xf940002b  // ldr	x11, [x1]
	WORD $0xa940200a  // ldp	x10, x8, [x0]
	WORD $0xeb08017f  // cmp	x11, x8
	WORD $0x54000142  // b.hs	LBB0_401 $40(%rip)
	WORD $0x386b6949  // ldrb	w9, [x10, x11]
	WORD $0x7100353f  // cmp	w9, #13
	WORD $0x540000e0  // b.eq	LBB0_401 $28(%rip)
	WORD $0x7100813f  // cmp	w9, #32
	WORD $0x540000a0  // b.eq	LBB0_401 $20(%rip)
	WORD $0x51002d2c  // sub	w12, w9, #11
	WORD $0xaa0b03e9  // mov	x9, x11
	WORD $0x3100099f  // cmn	w12, #2
	WORD $0x54000703  // b.lo	LBB0_420 $224(%rip)
LBB0_401:
	WORD $0x91000569  // add	x9, x11, #1
	WORD $0xeb08013f  // cmp	x9, x8
	WORD $0x54000122  // b.hs	LBB0_405 $36(%rip)
	WORD $0x3869694c  // ldrb	w12, [x10, x9]
	WORD $0x7100359f  // cmp	w12, #13
	WORD $0x540000c0  // b.eq	LBB0_405 $24(%rip)
	WORD $0x7100819f  // cmp	w12, #32
	WORD $0x54000080  // b.eq	LBB0_405 $16(%rip)
	WORD $0x51002d8c  // sub	w12, w12, #11
	WORD $0x3100099f  // cmn	w12, #2
	WORD $0x540005a3  // b.lo	LBB0_420 $180(%rip)
LBB0_405:
	WORD $0x91000969  // add	x9, x11, #2
	WORD $0xeb08013f  // cmp	x9, x8
	WORD $0x54000122  // b.hs	LBB0_409 $36(%rip)
	WORD $0x3869694c  // ldrb	w12, [x10, x9]
	WORD $0x7100359f  // cmp	w12, #13
	WORD $0x540000c0  // b.eq	LBB0_409 $24(%rip)
	WORD $0x7100819f  // cmp	w12, #32
	WORD $0x54000080  // b.eq	LBB0_409 $16(%rip)
	WORD $0x51002d8c  // sub	w12, w12, #11
	WORD $0x3100099f  // cmn	w12, #2
	WORD $0x54000443  // b.lo	LBB0_420 $136(%rip)
LBB0_409:
	WORD $0x91000d69  // add	x9, x11, #3
	WORD $0xeb08013f  // cmp	x9, x8
	WORD $0x54000122  // b.hs	LBB0_413 $36(%rip)
	WORD $0x3869694c  // ldrb	w12, [x10, x9]
	WORD $0x7100359f  // cmp	w12, #13
	WORD $0x540000c0  // b.eq	LBB0_413 $24(%rip)
	WORD $0x7100819f  // cmp	w12, #32
	WORD $0x54000080  // b.eq	LBB0_413 $16(%rip)
	WORD $0x51002d8c  // sub	w12, w12, #11
	WORD $0x3100099f  // cmn	w12, #2
	WORD $0x540002e3  // b.lo	LBB0_420 $92(%rip)
LBB0_413:
	WORD $0x91001169  // add	x9, x11, #4
	WORD $0xeb08013f  // cmp	x9, x8
	WORD $0x540001e2  // b.hs	LBB0_418 $60(%rip)
	WORD $0x5280002b  // mov	w11, #1
	WORD $0xd284c00c  // mov	x12, #9728
	WORD $0xf2c0002c  // movk	x12, #1, lsl #32
LBB0_415:
	WORD $0x3869694d  // ldrb	w13, [x10, x9]
	WORD $0x710081bf  // cmp	w13, #32
	WORD $0x9acd216d  // lsl	x13, x11, x13
	WORD $0x8a0c01ad  // and	x13, x13, x12
	WORD $0xfa4099a4  // ccmp	x13, #0, #4, ls
	WORD $0x54000120  // b.eq	LBB0_419 $36(%rip)
	WORD $0x91000529  // add	x9, x9, #1
	WORD $0xeb09011f  // cmp	x8, x9
	WORD $0x54ffff01  // b.ne	LBB0_415 $-32(%rip)
LBB0_417:
	WORD $0x92800008  // mov	x8, #-1
	WORD $0x14000040  // b	LBB0_438 $256(%rip)
LBB0_418:
	WORD $0xf9000029  // str	x9, [x1]
	WORD $0x92800008  // mov	x8, #-1
	WORD $0x1400003d  // b	LBB0_438 $244(%rip)
LBB0_419:
	WORD $0xeb08013f  // cmp	x9, x8
	WORD $0x54ffff42  // b.hs	LBB0_417 $-24(%rip)
LBB0_420:
	WORD $0x91000530  // add	x16, x9, #1
	WORD $0xf9000030  // str	x16, [x1]
	WORD $0x38696948  // ldrb	w8, [x10, x9]
	WORD $0x7101691f  // cmp	w8, #90
	WORD $0x540007ec  // b.gt	LBB0_439 $252(%rip)
	WORD $0x7100bd1f  // cmp	w8, #47
	WORD $0x54000c4d  // b.le	LBB0_444 $392(%rip)
	WORD $0x5100c108  // sub	w8, w8, #48
	WORD $0x7100291f  // cmp	w8, #10
	WORD $0x54005702  // b.hs	LBB0_511 $2784(%rip)
LBB0_423:
	WORD $0xf9400408  // ldr	x8, [x0, #8]
	WORD $0xcb100108  // sub	x8, x8, x16
	WORD $0xf100411f  // cmp	x8, #16
	WORD $0x540002c3  // b.lo	LBB0_427 $88(%rip)
	WORD $0x4f01e580  // movi.16b	v0, #44
	WORD $0x4f06e7e1  // movi.16b	v1, #223
	WORD $0x4f02e7a2  // movi.16b	v2, #93
Lloh6:
	WORD $0x10ff044b  // adr	x11, lCPI0_0 $-8056(%rip)
Lloh7:
	WORD $0x3dc00163  // ldr	q3, [x11, lCPI0_0@PAGEOFF] $0(%rip)
Lloh8:
	WORD $0x10ff048b  // adr	x11, lCPI0_1 $-8048(%rip)
Lloh9:
	WORD $0x3dc00164  // ldr	q4, [x11, lCPI0_1@PAGEOFF] $0(%rip)
LBB0_425:
	WORD $0x3cf06945  // ldr	q5, [x10, x16]
	WORD $0x6e208ca6  // cmeq.16b	v6, v5, v0
	WORD $0x4e211ca5  // and.16b	v5, v5, v1
	WORD $0x6e228ca5  // cmeq.16b	v5, v5, v2
	WORD $0x4ea61ca5  // orr.16b	v5, v5, v6
	WORD $0x4e231ca5  // and.16b	v5, v5, v3
	WORD $0x4e0400a5  // tbl.16b	v5, { v5 }, v4
	WORD $0x4e71b8a5  // addv.8h	h5, v5
	WORD $0x1e2600ab  // fmov	w11, s5
	WORD $0x350002eb  // cbnz	w11, LBB0_435 $92(%rip)
	WORD $0xd1004108  // sub	x8, x8, #16
	WORD $0x91004210  // add	x16, x16, #16
	WORD $0xf1003d1f  // cmp	x8, #15
	WORD $0x54fffe68  // b.hi	LBB0_425 $-52(%rip)
LBB0_427:
	WORD $0x8b10014b  // add	x11, x10, x16
	WORD $0xb40001e8  // cbz	x8, LBB0_434 $60(%rip)
	WORD $0x8b08016c  // add	x12, x11, x8
	WORD $0xcb0a016d  // sub	x13, x11, x10
LBB0_429:
	WORD $0x3940016e  // ldrb	w14, [x11]
	WORD $0x7100b1df  // cmp	w14, #44
	WORD $0x54005500  // b.eq	LBB0_516 $2720(%rip)
	WORD $0x7101f5df  // cmp	w14, #125
	WORD $0x540054c0  // b.eq	LBB0_516 $2712(%rip)
	WORD $0x710175df  // cmp	w14, #93
	WORD $0x54005480  // b.eq	LBB0_516 $2704(%rip)
	WORD $0x9100056b  // add	x11, x11, #1
	WORD $0x910005ad  // add	x13, x13, #1
	WORD $0xf1000508  // subs	x8, x8, #1
	WORD $0x54fffec1  // b.ne	LBB0_429 $-40(%rip)
	WORD $0xaa0c03eb  // mov	x11, x12
LBB0_434:
	WORD $0xcb0a0168  // sub	x8, x11, x10
	WORD $0x14000004  // b	LBB0_436 $16(%rip)
LBB0_435:
	WORD $0x5ac00168  // rbit	w8, w11
	WORD $0x5ac01108  // clz	w8, w8
	WORD $0x8b100108  // add	x8, x8, x16
LBB0_436:
	WORD $0xf9000028  // str	x8, [x1]
LBB0_437:
	WORD $0xaa0903e8  // mov	x8, x9
LBB0_438:
	WORD $0xaa0803e0  // mov	x0, x8
	WORD $0xa94afbfd  // ldp	fp, lr, [sp, #168]
	WORD $0xa949cff4  // ldp	x20, x19, [sp, #152]
	WORD $0xa948d7f6  // ldp	x22, x21, [sp, #136]
	WORD $0xa947dff8  // ldp	x24, x23, [sp, #120]
	WORD $0xa946e7fa  // ldp	x26, x25, [sp, #104]
	WORD $0xa945effc  // ldp	x28, x27, [sp, #88]
	WORD $0x910303ff  // add	sp, sp, #192
	WORD $0xd65f03c0  // ret
LBB0_439:
	WORD $0x7101b51f  // cmp	w8, #109
	WORD $0x5400054d  // b.le	LBB0_447 $168(%rip)
	WORD $0x7101b91f  // cmp	w8, #110
	WORD $0x54002380  // b.eq	LBB0_475 $1136(%rip)
	WORD $0x7101d11f  // cmp	w8, #116
	WORD $0x54002340  // b.eq	LBB0_475 $1128(%rip)
	WORD $0x7101ed1f  // cmp	w8, #123
	WORD $0x54004ee1  // b.ne	LBB0_511 $2524(%rip)
	WORD $0xd2800007  // mov	x7, #0
	WORD $0xd280000f  // mov	x15, #0
	WORD $0xd2800008  // mov	x8, #0
	WORD $0xd280000b  // mov	x11, #0
	WORD $0xb201e3ec  // mov	x12, #-8608480567731124088
	WORD $0xf2e1110c  // movk	x12, #2184, lsl #48
	WORD $0xb202e3ed  // mov	x13, #4919131752989213764
	WORD $0xf2e0888d  // movk	x13, #1092, lsl #48
	WORD $0xb203e3ee  // mov	x14, #2459565876494606882
	WORD $0xf2e0444e  // movk	x14, #546, lsl #48
	WORD $0xf9400411  // ldr	x17, [x0, #8]
	WORD $0xcb100225  // sub	x5, x17, x16
	WORD $0x8b100150  // add	x16, x10, x16
	WORD $0x910043ea  // add	x10, sp, #16
	WORD $0x9100814a  // add	x10, x10, #32
	WORD $0x4f01e440  // movi.16b	v0, #34
Lloh10:
	WORD $0x10fefb11  // adr	x17, lCPI0_0 $-8352(%rip)
Lloh11:
	WORD $0x3dc00221  // ldr	q1, [x17, lCPI0_0@PAGEOFF] $0(%rip)
Lloh12:
	WORD $0x10fefb51  // adr	x17, lCPI0_1 $-8344(%rip)
Lloh13:
	WORD $0x3dc00222  // ldr	q2, [x17, lCPI0_1@PAGEOFF] $0(%rip)
	WORD $0x4f02e783  // movi.16b	v3, #92
	WORD $0xb200e3f1  // mov	x17, #1229782938247303441
	WORD $0xb203e3e2  // mov	x2, #2459565876494606882
	WORD $0xb202e3e3  // mov	x3, #4919131752989213764
	WORD $0xb201e3e4  // mov	x4, #-8608480567731124088
	WORD $0x4f03e764  // movi.16b	v4, #123
	WORD $0x4f03e7a5  // movi.16b	v5, #125
	WORD $0x6f00e406  // movi.2d	v6, #0000000000000000
	WORD $0x14000018  // b	LBB0_451 $96(%rip)
LBB0_444:
	WORD $0x34fff228  // cbz	w8, LBB0_417 $-444(%rip)
	WORD $0x7100891f  // cmp	w8, #34
	WORD $0x54001fa0  // b.eq	LBB0_476 $1012(%rip)
	WORD $0x7100b51f  // cmp	w8, #45
	WORD $0x54fff3c0  // b.eq	LBB0_423 $-392(%rip)
	WORD $0x14000254  // b	LBB0_511 $2384(%rip)
LBB0_447:
	WORD $0x71016d1f  // cmp	w8, #91
	WORD $0x54002800  // b.eq	LBB0_485 $1280(%rip)
	WORD $0x7101991f  // cmp	w8, #102
	WORD $0x54004a01  // b.ne	LBB0_511 $2368(%rip)
	WORD $0x91001528  // add	x8, x9, #5
	WORD $0xf940040a  // ldr	x10, [x0, #8]
	WORD $0xeb0a011f  // cmp	x8, x10
	WORD $0x54fff088  // b.hi	LBB0_417 $-496(%rip)
	WORD $0x17ffffc2  // b	LBB0_436 $-248(%rip)
LBB0_450:
	WORD $0x937ffce7  // asr	x7, x7, #63
	WORD $0x9e670267  // fmov	d7, x19
	WORD $0x0e2058e7  // cnt.8b	v7, v7
	WORD $0x2e3038e7  // uaddlv.8b	h7, v7
	WORD $0x1e2600e5  // fmov	w5, s7
	WORD $0x8b0800a8  // add	x8, x5, x8
	WORD $0x91010210  // add	x16, x16, #64
	WORD $0xaa0603e5  // mov	x5, x6
LBB0_451:
	WORD $0xf10100a6  // subs	x6, x5, #64
	WORD $0x540015cb  // b.lt	LBB0_458 $696(%rip)
LBB0_452:
	WORD $0xad404612  // ldp	q18, q17, [x16]
	WORD $0xad411e10  // ldp	q16, q7, [x16, #32]
	WORD $0x6e238e53  // cmeq.16b	v19, v18, v3
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260273  // fmov	w19, s19
	WORD $0x6e238e33  // cmeq.16b	v19, v17, v3
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260274  // fmov	w20, s19
	WORD $0x6e238e13  // cmeq.16b	v19, v16, v3
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260275  // fmov	w21, s19
	WORD $0x6e238cf3  // cmeq.16b	v19, v7, v3
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260276  // fmov	w22, s19
	WORD $0xd3607eb5  // lsl	x21, x21, #32
	WORD $0xaa16c2b5  // orr	x21, x21, x22, lsl #48
	WORD $0x53103e94  // lsl	w20, w20, #16
	WORD $0xaa1402b4  // orr	x20, x21, x20
	WORD $0xaa130293  // orr	x19, x20, x19
	WORD $0xaa0f0274  // orr	x20, x19, x15
	WORD $0xb5000094  // cbnz	x20, LBB0_454 $16(%rip)
	WORD $0xd280000f  // mov	x15, #0
	WORD $0xd2800013  // mov	x19, #0
	WORD $0x1400000a  // b	LBB0_455 $40(%rip)
LBB0_454:
	WORD $0x8a2f0274  // bic	x20, x19, x15
	WORD $0xaa1405f5  // orr	x21, x15, x20, lsl #1
	WORD $0x8a35026f  // bic	x15, x19, x21
	WORD $0x9201f1ef  // and	x15, x15, #0xaaaaaaaaaaaaaaaa
	WORD $0xab1401f3  // adds	x19, x15, x20
	WORD $0x1a9f37ef  // cset	w15, hs
	WORD $0xd37ffa73  // lsl	x19, x19, #1
	WORD $0xd200f273  // eor	x19, x19, #0x5555555555555555
	WORD $0x8a150273  // and	x19, x19, x21
LBB0_455:
	WORD $0x6e208e53  // cmeq.16b	v19, v18, v0
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260274  // fmov	w20, s19
	WORD $0x6e208e33  // cmeq.16b	v19, v17, v0
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260275  // fmov	w21, s19
	WORD $0x6e208e13  // cmeq.16b	v19, v16, v0
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260276  // fmov	w22, s19
	WORD $0x6e208cf3  // cmeq.16b	v19, v7, v0
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260277  // fmov	w23, s19
	WORD $0xd3607ed6  // lsl	x22, x22, #32
	WORD $0xaa17c2d6  // orr	x22, x22, x23, lsl #48
	WORD $0x53103eb5  // lsl	w21, w21, #16
	WORD $0xaa1502d5  // orr	x21, x22, x21
	WORD $0xaa1402b4  // orr	x20, x21, x20
	WORD $0x8a330293  // bic	x19, x20, x19
	WORD $0x9200e274  // and	x20, x19, #0x1111111111111111
	WORD $0x9203e275  // and	x21, x19, #0x2222222222222222
	WORD $0x9202e276  // and	x22, x19, #0x4444444444444444
	WORD $0x9201e273  // and	x19, x19, #0x8888888888888888
	WORD $0x9b117e97  // mul	x23, x20, x17
	WORD $0x9b0c7eb8  // mul	x24, x21, x12
	WORD $0xca1802f7  // eor	x23, x23, x24
	WORD $0x9b0d7ed8  // mul	x24, x22, x13
	WORD $0x9b0e7e79  // mul	x25, x19, x14
	WORD $0xca190318  // eor	x24, x24, x25
	WORD $0xca1802f7  // eor	x23, x23, x24
	WORD $0x9b027e98  // mul	x24, x20, x2
	WORD $0x9b117eb9  // mul	x25, x21, x17
	WORD $0xca190318  // eor	x24, x24, x25
	WORD $0x9b0c7ed9  // mul	x25, x22, x12
	WORD $0x9b0d7e7a  // mul	x26, x19, x13
	WORD $0xca1a0339  // eor	x25, x25, x26
	WORD $0xca190318  // eor	x24, x24, x25
	WORD $0x9b037e99  // mul	x25, x20, x3
	WORD $0x9b027eba  // mul	x26, x21, x2
	WORD $0xca1a0339  // eor	x25, x25, x26
	WORD $0x9b117eda  // mul	x26, x22, x17
	WORD $0x9b0c7e7b  // mul	x27, x19, x12
	WORD $0xca1b035a  // eor	x26, x26, x27
	WORD $0xca1a0339  // eor	x25, x25, x26
	WORD $0x9b047e94  // mul	x20, x20, x4
	WORD $0x9b037eb5  // mul	x21, x21, x3
	WORD $0xca150294  // eor	x20, x20, x21
	WORD $0x9b027ed5  // mul	x21, x22, x2
	WORD $0x9b117e73  // mul	x19, x19, x17
	WORD $0xca1302b3  // eor	x19, x21, x19
	WORD $0xca130293  // eor	x19, x20, x19
	WORD $0x9200e2f4  // and	x20, x23, #0x1111111111111111
	WORD $0x9203e315  // and	x21, x24, #0x2222222222222222
	WORD $0x9202e336  // and	x22, x25, #0x4444444444444444
	WORD $0x9201e273  // and	x19, x19, #0x8888888888888888
	WORD $0xaa150294  // orr	x20, x20, x21
	WORD $0xaa1302d3  // orr	x19, x22, x19
	WORD $0xaa130293  // orr	x19, x20, x19
	WORD $0xca070267  // eor	x7, x19, x7
	WORD $0x6e248e53  // cmeq.16b	v19, v18, v4
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260273  // fmov	w19, s19
	WORD $0x6e248e33  // cmeq.16b	v19, v17, v4
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260274  // fmov	w20, s19
	WORD $0x6e248e13  // cmeq.16b	v19, v16, v4
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260275  // fmov	w21, s19
	WORD $0x6e248cf3  // cmeq.16b	v19, v7, v4
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260276  // fmov	w22, s19
	WORD $0xd3607eb5  // lsl	x21, x21, #32
	WORD $0xaa16c2b5  // orr	x21, x21, x22, lsl #48
	WORD $0x53103e94  // lsl	w20, w20, #16
	WORD $0xaa1402b4  // orr	x20, x21, x20
	WORD $0xaa130293  // orr	x19, x20, x19
	WORD $0x8a270273  // bic	x19, x19, x7
	WORD $0x6e258e52  // cmeq.16b	v18, v18, v5
	WORD $0x4e211e52  // and.16b	v18, v18, v1
	WORD $0x4e020252  // tbl.16b	v18, { v18 }, v2
	WORD $0x4e71ba52  // addv.8h	h18, v18
	WORD $0x1e260254  // fmov	w20, s18
	WORD $0x6e258e31  // cmeq.16b	v17, v17, v5
	WORD $0x4e211e31  // and.16b	v17, v17, v1
	WORD $0x4e020231  // tbl.16b	v17, { v17 }, v2
	WORD $0x4e71ba31  // addv.8h	h17, v17
	WORD $0x1e260235  // fmov	w21, s17
	WORD $0x6e258e10  // cmeq.16b	v16, v16, v5
	WORD $0x4e211e10  // and.16b	v16, v16, v1
	WORD $0x4e020210  // tbl.16b	v16, { v16 }, v2
	WORD $0x4e71ba10  // addv.8h	h16, v16
	WORD $0x1e260216  // fmov	w22, s16
	WORD $0x6e258ce7  // cmeq.16b	v7, v7, v5
	WORD $0x4e211ce7  // and.16b	v7, v7, v1
	WORD $0x4e0200e7  // tbl.16b	v7, { v7 }, v2
	WORD $0x4e71b8e7  // addv.8h	h7, v7
	WORD $0x1e2600f7  // fmov	w23, s7
	WORD $0xd3607ed6  // lsl	x22, x22, #32
	WORD $0xaa17c2d6  // orr	x22, x22, x23, lsl #48
	WORD $0x53103eb5  // lsl	w21, w21, #16
	WORD $0xaa1502d5  // orr	x21, x22, x21
	WORD $0xaa1402b4  // orr	x20, x21, x20
	WORD $0xea270294  // bics	x20, x20, x7
	WORD $0x54ffeae0  // b.eq	LBB0_450 $-676(%rip)
LBB0_456:
	WORD $0xd1000695  // sub	x21, x20, #1
	WORD $0x8a1302b6  // and	x22, x21, x19
	WORD $0x9e6702c7  // fmov	d7, x22
	WORD $0x0e2058e7  // cnt.8b	v7, v7
	WORD $0x2e3038e7  // uaddlv.8b	h7, v7
	WORD $0x1e2600f6  // fmov	w22, s7
	WORD $0x8b0802d6  // add	x22, x22, x8
	WORD $0xeb0b02df  // cmp	x22, x11
	WORD $0x54003169  // b.ls	LBB0_510 $1580(%rip)
	WORD $0x9100056b  // add	x11, x11, #1
	WORD $0xea1402b4  // ands	x20, x21, x20
	WORD $0x54fffea1  // b.ne	LBB0_456 $-44(%rip)
	WORD $0x17ffff4a  // b	LBB0_450 $-728(%rip)
LBB0_458:
	WORD $0xf10000bf  // cmp	x5, #0
	WORD $0x5400404d  // b.le	LBB0_547 $2056(%rip)
	WORD $0xad019be6  // stp	q6, q6, [sp, #48]
	WORD $0xad009be6  // stp	q6, q6, [sp, #16]
	WORD $0x92402e13  // and	x19, x16, #0xfff
	WORD $0xf13f067f  // cmp	x19, #4033
	WORD $0x54ffe9a3  // b.lo	LBB0_452 $-716(%rip)
	WORD $0xf10080b4  // subs	x20, x5, #32
	WORD $0x540000a3  // b.lo	LBB0_462 $20(%rip)
	WORD $0xacc14207  // ldp	q7, q16, [x16], #32
	WORD $0xad00c3e7  // stp	q7, q16, [sp, #16]
	WORD $0xaa0a03f3  // mov	x19, x10
	WORD $0x14000003  // b	LBB0_463 $12(%rip)
LBB0_462:
	WORD $0x910043f3  // add	x19, sp, #16
	WORD $0xaa0503f4  // mov	x20, x5
LBB0_463:
	WORD $0xf1004295  // subs	x21, x20, #16
	WORD $0x54000243  // b.lo	LBB0_469 $72(%rip)
	WORD $0x3cc10607  // ldr	q7, [x16], #16
	WORD $0x3c810667  // str	q7, [x19], #16
	WORD $0xaa1503f4  // mov	x20, x21
	WORD $0xf10022b5  // subs	x21, x21, #8
	WORD $0x540001e2  // b.hs	LBB0_470 $60(%rip)
LBB0_465:
	WORD $0xf1001295  // subs	x21, x20, #4
	WORD $0x54000243  // b.lo	LBB0_471 $72(%rip)
LBB0_466:
	WORD $0xb8404614  // ldr	w20, [x16], #4
	WORD $0xb8004674  // str	w20, [x19], #4
	WORD $0xaa1503f4  // mov	x20, x21
	WORD $0xf1000ab5  // subs	x21, x21, #2
	WORD $0x540001e2  // b.hs	LBB0_472 $60(%rip)
LBB0_467:
	WORD $0xb4000254  // cbz	x20, LBB0_473 $72(%rip)
LBB0_468:
	WORD $0x39400210  // ldrb	w16, [x16]
	WORD $0x39000270  // strb	w16, [x19]
	WORD $0x910043f0  // add	x16, sp, #16
	WORD $0x17ffff32  // b	LBB0_452 $-824(%rip)
LBB0_469:
	WORD $0xf1002295  // subs	x21, x20, #8
	WORD $0x54fffe63  // b.lo	LBB0_465 $-52(%rip)
LBB0_470:
	WORD $0xf8408614  // ldr	x20, [x16], #8
	WORD $0xf8008674  // str	x20, [x19], #8
	WORD $0xaa1503f4  // mov	x20, x21
	WORD $0xf10012b5  // subs	x21, x21, #4
	WORD $0x54fffe02  // b.hs	LBB0_466 $-64(%rip)
LBB0_471:
	WORD $0xf1000a95  // subs	x21, x20, #2
	WORD $0x54fffe63  // b.lo	LBB0_467 $-52(%rip)
LBB0_472:
	WORD $0x78402614  // ldrh	w20, [x16], #2
	WORD $0x78002674  // strh	w20, [x19], #2
	WORD $0xaa1503f4  // mov	x20, x21
	WORD $0xb5fffe15  // cbnz	x21, LBB0_468 $-64(%rip)
LBB0_473:
	WORD $0x910043f0  // add	x16, sp, #16
	WORD $0x17ffff23  // b	LBB0_452 $-884(%rip)
LBB0_474:
	WORD $0xf900003b  // str	x27, [x1]
	WORD $0x92800008  // mov	x8, #-1
	WORD $0x17fffed9  // b	LBB0_438 $-1180(%rip)
LBB0_475:
	WORD $0x91001128  // add	x8, x9, #4
	WORD $0xf940040a  // ldr	x10, [x0, #8]
	WORD $0xeb0a011f  // cmp	x8, x10
	WORD $0x54ffd288  // b.hi	LBB0_417 $-1456(%rip)
	WORD $0x17fffed2  // b	LBB0_436 $-1208(%rip)
LBB0_476:
	WORD $0xf9400411  // ldr	x17, [x0, #8]
	WORD $0xcb10022b  // sub	x11, x17, x16
	WORD $0xf100817f  // cmp	x11, #32
	WORD $0x540037cb  // b.lt	LBB0_544 $1784(%rip)
	WORD $0xd2800008  // mov	x8, #0
	WORD $0xd280000e  // mov	x14, #0
	WORD $0x3200f3ec  // mov	w12, #1431655765
	WORD $0x3201f3ed  // mov	w13, #-1431655766
	WORD $0x4f01e440  // movi.16b	v0, #34
	WORD $0x8b09014f  // add	x15, x10, x9
Lloh14:
	WORD $0x10fed84b  // adr	x11, lCPI0_0 $-9464(%rip)
Lloh15:
	WORD $0x3dc00161  // ldr	q1, [x11, lCPI0_0@PAGEOFF] $0(%rip)
Lloh16:
	WORD $0x10fed88b  // adr	x11, lCPI0_1 $-9456(%rip)
Lloh17:
	WORD $0x3dc00162  // ldr	q2, [x11, lCPI0_1@PAGEOFF] $0(%rip)
	WORD $0xcb09022b  // sub	x11, x17, x9
	WORD $0x4f02e783  // movi.16b	v3, #92
	WORD $0x528003f0  // mov	w16, #31
LBB0_478:
	WORD $0x8b0801f1  // add	x17, x15, x8
	WORD $0x3cc01224  // ldur	q4, [x17, #1]
	WORD $0x3cc11225  // ldur	q5, [x17, #17]
	WORD $0x6e208c86  // cmeq.16b	v6, v4, v0
	WORD $0x4e211cc6  // and.16b	v6, v6, v1
	WORD $0x4e0200c6  // tbl.16b	v6, { v6 }, v2
	WORD $0x4e71b8c6  // addv.8h	h6, v6
	WORD $0x1e2600d1  // fmov	w17, s6
	WORD $0x6e208ca6  // cmeq.16b	v6, v5, v0
	WORD $0x4e211cc6  // and.16b	v6, v6, v1
	WORD $0x4e0200c6  // tbl.16b	v6, { v6 }, v2
	WORD $0x4e71b8c6  // addv.8h	h6, v6
	WORD $0x1e2600c0  // fmov	w0, s6
	WORD $0x33103c11  // bfi	w17, w0, #16, #16
	WORD $0x6e238c84  // cmeq.16b	v4, v4, v3
	WORD $0x4e211c84  // and.16b	v4, v4, v1
	WORD $0x4e020084  // tbl.16b	v4, { v4 }, v2
	WORD $0x4e71b884  // addv.8h	h4, v4
	WORD $0x1e260080  // fmov	w0, s4
	WORD $0x6e238ca4  // cmeq.16b	v4, v5, v3
	WORD $0x4e211c84  // and.16b	v4, v4, v1
	WORD $0x4e020084  // tbl.16b	v4, { v4 }, v2
	WORD $0x4e71b884  // addv.8h	h4, v4
	WORD $0x1e260082  // fmov	w2, s4
	WORD $0x33103c40  // bfi	w0, w2, #16, #16
	WORD $0x7100001f  // cmp	w0, #0
	WORD $0xfa4009c0  // ccmp	x14, #0, #0, eq
	WORD $0x54000180  // b.eq	LBB0_480 $48(%rip)
	WORD $0x0a2e0002  // bic	w2, w0, w14
	WORD $0x2a0205c3  // orr	w3, w14, w2, lsl #1
	WORD $0x0a0d000e  // and	w14, w0, w13
	WORD $0x0a2301ce  // bic	w14, w14, w3
	WORD $0x2b0201c0  // adds	w0, w14, w2
	WORD $0x1a9f37ee  // cset	w14, hs
	WORD $0x4a000580  // eor	w0, w12, w0, lsl #1
	WORD $0x0a030000  // and	w0, w0, w3
	WORD $0x2a2003e0  // mvn	w0, w0
	WORD $0x8a110011  // and	x17, x0, x17
	WORD $0x14000002  // b	LBB0_481 $8(%rip)
LBB0_480:
	WORD $0xd280000e  // mov	x14, #0
LBB0_481:
	WORD $0xb5002491  // cbnz	x17, LBB0_512 $1168(%rip)
	WORD $0x91008108  // add	x8, x8, #32
	WORD $0xd1008210  // sub	x16, x16, #32
	WORD $0x8b100171  // add	x17, x11, x16
	WORD $0xf100fe3f  // cmp	x17, #63
	WORD $0x54fffa6c  // b.gt	LBB0_478 $-180(%rip)
	WORD $0xb500328e  // cbnz	x14, LBB0_551 $1616(%rip)
	WORD $0x8b09014c  // add	x12, x10, x9
	WORD $0x8b08018c  // add	x12, x12, x8
	WORD $0x9100058c  // add	x12, x12, #1
	WORD $0xaa2803e8  // mvn	x8, x8
	WORD $0x8b0b010b  // add	x11, x8, x11
	WORD $0xf100057f  // cmp	x11, #1
	WORD $0x540033ca  // b.ge	LBB0_554 $1656(%rip)
	WORD $0x17fffe4b  // b	LBB0_417 $-1748(%rip)
LBB0_485:
	WORD $0xd2800007  // mov	x7, #0
	WORD $0xd280000f  // mov	x15, #0
	WORD $0xd2800008  // mov	x8, #0
	WORD $0xd280000b  // mov	x11, #0
	WORD $0xb201e3ec  // mov	x12, #-8608480567731124088
	WORD $0xf2e1110c  // movk	x12, #2184, lsl #48
	WORD $0xb202e3ed  // mov	x13, #4919131752989213764
	WORD $0xf2e0888d  // movk	x13, #1092, lsl #48
	WORD $0xb203e3ee  // mov	x14, #2459565876494606882
	WORD $0xf2e0444e  // movk	x14, #546, lsl #48
	WORD $0xf9400411  // ldr	x17, [x0, #8]
	WORD $0xcb100225  // sub	x5, x17, x16
	WORD $0x8b100150  // add	x16, x10, x16
	WORD $0x910043ea  // add	x10, sp, #16
	WORD $0x9100814a  // add	x10, x10, #32
	WORD $0x4f01e440  // movi.16b	v0, #34
Lloh18:
	WORD $0x10fece91  // adr	x17, lCPI0_0 $-9776(%rip)
Lloh19:
	WORD $0x3dc00221  // ldr	q1, [x17, lCPI0_0@PAGEOFF] $0(%rip)
Lloh20:
	WORD $0x10feced1  // adr	x17, lCPI0_1 $-9768(%rip)
Lloh21:
	WORD $0x3dc00222  // ldr	q2, [x17, lCPI0_1@PAGEOFF] $0(%rip)
	WORD $0x4f02e783  // movi.16b	v3, #92
	WORD $0xb200e3f1  // mov	x17, #1229782938247303441
	WORD $0xb203e3e2  // mov	x2, #2459565876494606882
	WORD $0xb202e3e3  // mov	x3, #4919131752989213764
	WORD $0xb201e3e4  // mov	x4, #-8608480567731124088
	WORD $0x4f02e764  // movi.16b	v4, #91
	WORD $0x4f02e7a5  // movi.16b	v5, #93
	WORD $0x6f00e406  // movi.2d	v6, #0000000000000000
	WORD $0x14000009  // b	LBB0_487 $36(%rip)
LBB0_486:
	WORD $0x937ffce7  // asr	x7, x7, #63
	WORD $0x9e670267  // fmov	d7, x19
	WORD $0x0e2058e7  // cnt.8b	v7, v7
	WORD $0x2e3038e7  // uaddlv.8b	h7, v7
	WORD $0x1e2600e5  // fmov	w5, s7
	WORD $0x8b0800a8  // add	x8, x5, x8
	WORD $0x91010210  // add	x16, x16, #64
	WORD $0xaa0603e5  // mov	x5, x6
LBB0_487:
	WORD $0xf10100a6  // subs	x6, x5, #64
	WORD $0x540015cb  // b.lt	LBB0_494 $696(%rip)
LBB0_488:
	WORD $0xad404612  // ldp	q18, q17, [x16]
	WORD $0xad411e10  // ldp	q16, q7, [x16, #32]
	WORD $0x6e238e53  // cmeq.16b	v19, v18, v3
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260273  // fmov	w19, s19
	WORD $0x6e238e33  // cmeq.16b	v19, v17, v3
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260274  // fmov	w20, s19
	WORD $0x6e238e13  // cmeq.16b	v19, v16, v3
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260275  // fmov	w21, s19
	WORD $0x6e238cf3  // cmeq.16b	v19, v7, v3
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260276  // fmov	w22, s19
	WORD $0xd3607eb5  // lsl	x21, x21, #32
	WORD $0xaa16c2b5  // orr	x21, x21, x22, lsl #48
	WORD $0x53103e94  // lsl	w20, w20, #16
	WORD $0xaa1402b4  // orr	x20, x21, x20
	WORD $0xaa130293  // orr	x19, x20, x19
	WORD $0xaa0f0274  // orr	x20, x19, x15
	WORD $0xb5000094  // cbnz	x20, LBB0_490 $16(%rip)
	WORD $0xd280000f  // mov	x15, #0
	WORD $0xd2800013  // mov	x19, #0
	WORD $0x1400000a  // b	LBB0_491 $40(%rip)
LBB0_490:
	WORD $0x8a2f0274  // bic	x20, x19, x15
	WORD $0xaa1405f5  // orr	x21, x15, x20, lsl #1
	WORD $0x8a35026f  // bic	x15, x19, x21
	WORD $0x9201f1ef  // and	x15, x15, #0xaaaaaaaaaaaaaaaa
	WORD $0xab1401f3  // adds	x19, x15, x20
	WORD $0x1a9f37ef  // cset	w15, hs
	WORD $0xd37ffa73  // lsl	x19, x19, #1
	WORD $0xd200f273  // eor	x19, x19, #0x5555555555555555
	WORD $0x8a150273  // and	x19, x19, x21
LBB0_491:
	WORD $0x6e208e53  // cmeq.16b	v19, v18, v0
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260274  // fmov	w20, s19
	WORD $0x6e208e33  // cmeq.16b	v19, v17, v0
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260275  // fmov	w21, s19
	WORD $0x6e208e13  // cmeq.16b	v19, v16, v0
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260276  // fmov	w22, s19
	WORD $0x6e208cf3  // cmeq.16b	v19, v7, v0
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260277  // fmov	w23, s19
	WORD $0xd3607ed6  // lsl	x22, x22, #32
	WORD $0xaa17c2d6  // orr	x22, x22, x23, lsl #48
	WORD $0x53103eb5  // lsl	w21, w21, #16
	WORD $0xaa1502d5  // orr	x21, x22, x21
	WORD $0xaa1402b4  // orr	x20, x21, x20
	WORD $0x8a330293  // bic	x19, x20, x19
	WORD $0x9200e274  // and	x20, x19, #0x1111111111111111
	WORD $0x9203e275  // and	x21, x19, #0x2222222222222222
	WORD $0x9202e276  // and	x22, x19, #0x4444444444444444
	WORD $0x9201e273  // and	x19, x19, #0x8888888888888888
	WORD $0x9b117e97  // mul	x23, x20, x17
	WORD $0x9b0c7eb8  // mul	x24, x21, x12
	WORD $0xca1802f7  // eor	x23, x23, x24
	WORD $0x9b0d7ed8  // mul	x24, x22, x13
	WORD $0x9b0e7e79  // mul	x25, x19, x14
	WORD $0xca190318  // eor	x24, x24, x25
	WORD $0xca1802f7  // eor	x23, x23, x24
	WORD $0x9b027e98  // mul	x24, x20, x2
	WORD $0x9b117eb9  // mul	x25, x21, x17
	WORD $0xca190318  // eor	x24, x24, x25
	WORD $0x9b0c7ed9  // mul	x25, x22, x12
	WORD $0x9b0d7e7a  // mul	x26, x19, x13
	WORD $0xca1a0339  // eor	x25, x25, x26
	WORD $0xca190318  // eor	x24, x24, x25
	WORD $0x9b037e99  // mul	x25, x20, x3
	WORD $0x9b027eba  // mul	x26, x21, x2
	WORD $0xca1a0339  // eor	x25, x25, x26
	WORD $0x9b117eda  // mul	x26, x22, x17
	WORD $0x9b0c7e7b  // mul	x27, x19, x12
	WORD $0xca1b035a  // eor	x26, x26, x27
	WORD $0xca1a0339  // eor	x25, x25, x26
	WORD $0x9b047e94  // mul	x20, x20, x4
	WORD $0x9b037eb5  // mul	x21, x21, x3
	WORD $0xca150294  // eor	x20, x20, x21
	WORD $0x9b027ed5  // mul	x21, x22, x2
	WORD $0x9b117e73  // mul	x19, x19, x17
	WORD $0xca1302b3  // eor	x19, x21, x19
	WORD $0xca130293  // eor	x19, x20, x19
	WORD $0x9200e2f4  // and	x20, x23, #0x1111111111111111
	WORD $0x9203e315  // and	x21, x24, #0x2222222222222222
	WORD $0x9202e336  // and	x22, x25, #0x4444444444444444
	WORD $0x9201e273  // and	x19, x19, #0x8888888888888888
	WORD $0xaa150294  // orr	x20, x20, x21
	WORD $0xaa1302d3  // orr	x19, x22, x19
	WORD $0xaa130293  // orr	x19, x20, x19
	WORD $0xca070267  // eor	x7, x19, x7
	WORD $0x6e248e53  // cmeq.16b	v19, v18, v4
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260273  // fmov	w19, s19
	WORD $0x6e248e33  // cmeq.16b	v19, v17, v4
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260274  // fmov	w20, s19
	WORD $0x6e248e13  // cmeq.16b	v19, v16, v4
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260275  // fmov	w21, s19
	WORD $0x6e248cf3  // cmeq.16b	v19, v7, v4
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260276  // fmov	w22, s19
	WORD $0xd3607eb5  // lsl	x21, x21, #32
	WORD $0xaa16c2b5  // orr	x21, x21, x22, lsl #48
	WORD $0x53103e94  // lsl	w20, w20, #16
	WORD $0xaa1402b4  // orr	x20, x21, x20
	WORD $0xaa130293  // orr	x19, x20, x19
	WORD $0x8a270273  // bic	x19, x19, x7
	WORD $0x6e258e52  // cmeq.16b	v18, v18, v5
	WORD $0x4e211e52  // and.16b	v18, v18, v1
	WORD $0x4e020252  // tbl.16b	v18, { v18 }, v2
	WORD $0x4e71ba52  // addv.8h	h18, v18
	WORD $0x1e260254  // fmov	w20, s18
	WORD $0x6e258e31  // cmeq.16b	v17, v17, v5
	WORD $0x4e211e31  // and.16b	v17, v17, v1
	WORD $0x4e020231  // tbl.16b	v17, { v17 }, v2
	WORD $0x4e71ba31  // addv.8h	h17, v17
	WORD $0x1e260235  // fmov	w21, s17
	WORD $0x6e258e10  // cmeq.16b	v16, v16, v5
	WORD $0x4e211e10  // and.16b	v16, v16, v1
	WORD $0x4e020210  // tbl.16b	v16, { v16 }, v2
	WORD $0x4e71ba10  // addv.8h	h16, v16
	WORD $0x1e260216  // fmov	w22, s16
	WORD $0x6e258ce7  // cmeq.16b	v7, v7, v5
	WORD $0x4e211ce7  // and.16b	v7, v7, v1
	WORD $0x4e0200e7  // tbl.16b	v7, { v7 }, v2
	WORD $0x4e71b8e7  // addv.8h	h7, v7
	WORD $0x1e2600f7  // fmov	w23, s7
	WORD $0xd3607ed6  // lsl	x22, x22, #32
	WORD $0xaa17c2d6  // orr	x22, x22, x23, lsl #48
	WORD $0x53103eb5  // lsl	w21, w21, #16
	WORD $0xaa1502d5  // orr	x21, x22, x21
	WORD $0xaa1402b4  // orr	x20, x21, x20
	WORD $0xea270294  // bics	x20, x20, x7
	WORD $0x54ffeae0  // b.eq	LBB0_486 $-676(%rip)
LBB0_492:
	WORD $0xd1000695  // sub	x21, x20, #1
	WORD $0x8a1302b6  // and	x22, x21, x19
	WORD $0x9e6702c7  // fmov	d7, x22
	WORD $0x0e2058e7  // cnt.8b	v7, v7
	WORD $0x2e3038e7  // uaddlv.8b	h7, v7
	WORD $0x1e2600f6  // fmov	w22, s7
	WORD $0x8b0802d6  // add	x22, x22, x8
	WORD $0xeb0b02df  // cmp	x22, x11
	WORD $0x540006c9  // b.ls	LBB0_510 $216(%rip)
	WORD $0x9100056b  // add	x11, x11, #1
	WORD $0xea1402b4  // ands	x20, x21, x20
	WORD $0x54fffea1  // b.ne	LBB0_492 $-44(%rip)
	WORD $0x17ffff4a  // b	LBB0_486 $-728(%rip)
LBB0_494:
	WORD $0xf10000bf  // cmp	x5, #0
	WORD $0x540015ad  // b.le	LBB0_547 $692(%rip)
	WORD $0xad019be6  // stp	q6, q6, [sp, #48]
	WORD $0xad009be6  // stp	q6, q6, [sp, #16]
	WORD $0x92402e13  // and	x19, x16, #0xfff
	WORD $0xf13f067f  // cmp	x19, #4033
	WORD $0x54ffe9a3  // b.lo	LBB0_488 $-716(%rip)
	WORD $0xf10080b4  // subs	x20, x5, #32
	WORD $0x540000a3  // b.lo	LBB0_498 $20(%rip)
	WORD $0xacc14207  // ldp	q7, q16, [x16], #32
	WORD $0xad00c3e7  // stp	q7, q16, [sp, #16]
	WORD $0xaa0a03f3  // mov	x19, x10
	WORD $0x14000003  // b	LBB0_499 $12(%rip)
LBB0_498:
	WORD $0x910043f3  // add	x19, sp, #16
	WORD $0xaa0503f4  // mov	x20, x5
LBB0_499:
	WORD $0xf1004295  // subs	x21, x20, #16
	WORD $0x54000243  // b.lo	LBB0_505 $72(%rip)
	WORD $0x3cc10607  // ldr	q7, [x16], #16
	WORD $0x3c810667  // str	q7, [x19], #16
	WORD $0xaa1503f4  // mov	x20, x21
	WORD $0xf10022b5  // subs	x21, x21, #8
	WORD $0x540001e2  // b.hs	LBB0_506 $60(%rip)
LBB0_501:
	WORD $0xf1001295  // subs	x21, x20, #4
	WORD $0x54000243  // b.lo	LBB0_507 $72(%rip)
LBB0_502:
	WORD $0xb8404614  // ldr	w20, [x16], #4
	WORD $0xb8004674  // str	w20, [x19], #4
	WORD $0xaa1503f4  // mov	x20, x21
	WORD $0xf1000ab5  // subs	x21, x21, #2
	WORD $0x540001e2  // b.hs	LBB0_508 $60(%rip)
LBB0_503:
	WORD $0xb4000254  // cbz	x20, LBB0_509 $72(%rip)
LBB0_504:
	WORD $0x39400210  // ldrb	w16, [x16]
	WORD $0x39000270  // strb	w16, [x19]
	WORD $0x910043f0  // add	x16, sp, #16
	WORD $0x17ffff32  // b	LBB0_488 $-824(%rip)
LBB0_505:
	WORD $0xf1002295  // subs	x21, x20, #8
	WORD $0x54fffe63  // b.lo	LBB0_501 $-52(%rip)
LBB0_506:
	WORD $0xf8408614  // ldr	x20, [x16], #8
	WORD $0xf8008674  // str	x20, [x19], #8
	WORD $0xaa1503f4  // mov	x20, x21
	WORD $0xf10012b5  // subs	x21, x21, #4
	WORD $0x54fffe02  // b.hs	LBB0_502 $-64(%rip)
LBB0_507:
	WORD $0xf1000a95  // subs	x21, x20, #2
	WORD $0x54fffe63  // b.lo	LBB0_503 $-52(%rip)
LBB0_508:
	WORD $0x78402614  // ldrh	w20, [x16], #2
	WORD $0x78002674  // strh	w20, [x19], #2
	WORD $0xaa1503f4  // mov	x20, x21
	WORD $0xb5fffe15  // cbnz	x21, LBB0_504 $-64(%rip)
LBB0_509:
	WORD $0x910043f0  // add	x16, sp, #16
	WORD $0x17ffff23  // b	LBB0_488 $-884(%rip)
LBB0_510:
	WORD $0xf9400408  // ldr	x8, [x0, #8]
	WORD $0xdac0028a  // rbit	x10, x20
	WORD $0xdac0114a  // clz	x10, x10
	WORD $0xcb05014a  // sub	x10, x10, x5
	WORD $0x8b080148  // add	x8, x10, x8
	WORD $0x9100050a  // add	x10, x8, #1
	WORD $0xf900002a  // str	x10, [x1]
	WORD $0xf940040b  // ldr	x11, [x0, #8]
	WORD $0xeb0b015f  // cmp	x10, x11
	WORD $0x9a882568  // csinc	x8, x11, x8, hs
	WORD $0xf9000028  // str	x8, [x1]
	WORD $0xda9f9128  // csinv	x8, x9, xzr, ls
	WORD $0x17fffd7a  // b	LBB0_438 $-2584(%rip)
LBB0_511:
	WORD $0xf9000029  // str	x9, [x1]
	WORD $0x92800028  // mov	x8, #-2
	WORD $0x17fffd77  // b	LBB0_438 $-2596(%rip)
LBB0_512:
	WORD $0xdac0022a  // rbit	x10, x17
	WORD $0xdac0114a  // clz	x10, x10
	WORD $0x8b080128  // add	x8, x9, x8
	WORD $0x8b080148  // add	x8, x10, x8
	WORD $0x91000908  // add	x8, x8, #2
	WORD $0x17fffd6f  // b	LBB0_436 $-2628(%rip)
LBB0_513:
	WORD $0x928000c8  // mov	x8, #-7
	WORD $0x17fffd6f  // b	LBB0_438 $-2628(%rip)
LBB0_514:
	WORD $0xb10007df  // cmn	lr, #1
	WORD $0x54000241  // b.ne	LBB0_524 $72(%rip)
LBB0_515:
	WORD $0xf9400408  // ldr	x8, [x0, #8]
	WORD $0x9280001e  // mov	lr, #-1
	WORD $0x1400000f  // b	LBB0_524 $60(%rip)
LBB0_516:
	WORD $0xf900002d  // str	x13, [x1]
	WORD $0x17fffd67  // b	LBB0_437 $-2660(%rip)
LBB0_517:
	WORD $0x92800016  // mov	x22, #-1
LBB0_518:
	WORD $0xaa3603f5  // mvn	x21, x22
LBB0_519:
	WORD $0x8b1b02a8  // add	x8, x21, x27
LBB0_520:
	WORD $0xf9000028  // str	x8, [x1]
	WORD $0x92800028  // mov	x8, #-2
	WORD $0x17fffd62  // b	LBB0_438 $-2680(%rip)
LBB0_521:
	WORD $0xb100051f  // cmn	x8, #1
	WORD $0x54000081  // b.ne	LBB0_523 $16(%rip)
	WORD $0xdac000e8  // rbit	x8, x7
	WORD $0xdac01108  // clz	x8, x8
	WORD $0x8b1e0108  // add	x8, x8, lr
LBB0_523:
	WORD $0x9280003e  // mov	lr, #-2
LBB0_524:
	WORD $0xf9000028  // str	x8, [x1]
	WORD $0xaa1e03e8  // mov	x8, lr
	WORD $0x17fffd59  // b	LBB0_438 $-2716(%rip)
LBB0_525:
	WORD $0xaa0803f5  // mov	x21, x8
	WORD $0x17fffff2  // b	LBB0_519 $-56(%rip)
LBB0_526:
	WORD $0x92800016  // mov	x22, #-1
LBB0_527:
	WORD $0xaa3603e5  // mvn	x5, x22
LBB0_528:
	WORD $0x8b0503c8  // add	x8, lr, x5
	WORD $0x17ffffef  // b	LBB0_520 $-68(%rip)
LBB0_529:
	WORD $0xf900003e  // str	lr, [x1]
	WORD $0x387e6928  // ldrb	w8, [x9, lr]
	WORD $0x7101851f  // cmp	w8, #97
	WORD $0x54000761  // b.ne	LBB0_543 $236(%rip)
	WORD $0x91000b68  // add	x8, x27, #2
	WORD $0xf9000028  // str	x8, [x1]
	WORD $0x38686928  // ldrb	w8, [x9, x8]
	WORD $0x7101b11f  // cmp	w8, #108
	WORD $0x540006c1  // b.ne	LBB0_543 $216(%rip)
	WORD $0x91000f68  // add	x8, x27, #3
	WORD $0xf9000028  // str	x8, [x1]
	WORD $0x38686928  // ldrb	w8, [x9, x8]
	WORD $0x7101cd1f  // cmp	w8, #115
	WORD $0x54000621  // b.ne	LBB0_543 $196(%rip)
	WORD $0x91001368  // add	x8, x27, #4
	WORD $0xf9000028  // str	x8, [x1]
	WORD $0x38686928  // ldrb	w8, [x9, x8]
	WORD $0x7101951f  // cmp	w8, #101
	WORD $0x54000581  // b.ne	LBB0_543 $176(%rip)
	WORD $0x91001768  // add	x8, x27, #5
	WORD $0x17ffffda  // b	LBB0_520 $-152(%rip)
LBB0_534:
	WORD $0xf900003b  // str	x27, [x1]
	WORD $0x394000a8  // ldrb	w8, [x5]
	WORD $0x7101d11f  // cmp	w8, #116
	WORD $0x540004c1  // b.ne	LBB0_543 $152(%rip)
	WORD $0x91000768  // add	x8, x27, #1
	WORD $0xf9000028  // str	x8, [x1]
	WORD $0x38686928  // ldrb	w8, [x9, x8]
	WORD $0x7101c91f  // cmp	w8, #114
	WORD $0x54000421  // b.ne	LBB0_543 $132(%rip)
	WORD $0x91000b68  // add	x8, x27, #2
	WORD $0xf9000028  // str	x8, [x1]
	WORD $0x38686928  // ldrb	w8, [x9, x8]
	WORD $0x7101d51f  // cmp	w8, #117
	WORD $0x54000381  // b.ne	LBB0_543 $112(%rip)
	WORD $0x91000f68  // add	x8, x27, #3
	WORD $0xf9000028  // str	x8, [x1]
	WORD $0x38686928  // ldrb	w8, [x9, x8]
	WORD $0x7101951f  // cmp	w8, #101
	WORD $0x540002e1  // b.ne	LBB0_543 $92(%rip)
	WORD $0x14000014  // b	LBB0_542 $80(%rip)
LBB0_538:
	WORD $0xf900003b  // str	x27, [x1]
	WORD $0x394000a8  // ldrb	w8, [x5]
	WORD $0x7101b91f  // cmp	w8, #110
	WORD $0x54000241  // b.ne	LBB0_543 $72(%rip)
	WORD $0x91000768  // add	x8, x27, #1
	WORD $0xf9000028  // str	x8, [x1]
	WORD $0x38686928  // ldrb	w8, [x9, x8]
	WORD $0x7101d51f  // cmp	w8, #117
	WORD $0x540001a1  // b.ne	LBB0_543 $52(%rip)
	WORD $0x91000b68  // add	x8, x27, #2
	WORD $0xf9000028  // str	x8, [x1]
	WORD $0x38686928  // ldrb	w8, [x9, x8]
	WORD $0x7101b11f  // cmp	w8, #108
	WORD $0x54000101  // b.ne	LBB0_543 $32(%rip)
	WORD $0x91000f68  // add	x8, x27, #3
	WORD $0xf9000028  // str	x8, [x1]
	WORD $0x38686928  // ldrb	w8, [x9, x8]
	WORD $0x7101b11f  // cmp	w8, #108
	WORD $0x54000061  // b.ne	LBB0_543 $12(%rip)
LBB0_542:
	WORD $0x91001368  // add	x8, x27, #4
	WORD $0x17ffffb1  // b	LBB0_520 $-316(%rip)
LBB0_543:
	WORD $0x92800028  // mov	x8, #-2
	WORD $0x17fffd13  // b	LBB0_438 $-2996(%rip)
LBB0_544:
	WORD $0x8b10014c  // add	x12, x10, x16
	WORD $0xf100057f  // cmp	x11, #1
	WORD $0x5400042a  // b.ge	LBB0_554 $132(%rip)
	WORD $0x17fffcce  // b	LBB0_417 $-3272(%rip)
LBB0_545:
	WORD $0xaa1503e5  // mov	x5, x21
	WORD $0x8b1503c8  // add	x8, lr, x21
	WORD $0x17ffffa8  // b	LBB0_520 $-352(%rip)
LBB0_546:
	WORD $0x8b1e00a8  // add	x8, x5, lr
	WORD $0x17ffffae  // b	LBB0_523 $-328(%rip)
LBB0_547:
	WORD $0xf9400408  // ldr	x8, [x0, #8]
LBB0_548:
	WORD $0xf9000028  // str	x8, [x1]
	WORD $0x92800008  // mov	x8, #-1
	WORD $0x17fffd06  // b	LBB0_438 $-3048(%rip)
LBB0_549:
	WORD $0xcb0902a8  // sub	x8, x21, x9
	WORD $0x8b110108  // add	x8, x8, x17
	WORD $0x17ffffa7  // b	LBB0_523 $-356(%rip)
LBB0_550:
	WORD $0x8b1100a8  // add	x8, x5, x17
	WORD $0x17ffffa5  // b	LBB0_523 $-364(%rip)
LBB0_551:
	WORD $0xd100056c  // sub	x12, x11, #1
	WORD $0xeb08019f  // cmp	x12, x8
	WORD $0x54ff97a0  // b.eq	LBB0_417 $-3340(%rip)
	WORD $0x8b09014c  // add	x12, x10, x9
	WORD $0x8b08018c  // add	x12, x12, x8
	WORD $0x9100098c  // add	x12, x12, #2
	WORD $0xcb080168  // sub	x8, x11, x8
	WORD $0xd100090b  // sub	x11, x8, #2
	WORD $0xf100057f  // cmp	x11, #1
	WORD $0x5400010a  // b.ge	LBB0_554 $32(%rip)
	WORD $0x17fffcb5  // b	LBB0_417 $-3372(%rip)
LBB0_553:
	WORD $0x9280002d  // mov	x13, #-2
	WORD $0x52800048  // mov	w8, #2
	WORD $0x8b08018c  // add	x12, x12, x8
	WORD $0x92800008  // mov	x8, #-1
	WORD $0xab0d016b  // adds	x11, x11, x13
	WORD $0x54ff9e0d  // b.le	LBB0_438 $-3136(%rip)
LBB0_554:
	WORD $0x39400188  // ldrb	w8, [x12]
	WORD $0x7101711f  // cmp	w8, #92
	WORD $0x54ffff00  // b.eq	LBB0_553 $-32(%rip)
	WORD $0x7100891f  // cmp	w8, #34
	WORD $0x54000100  // b.eq	LBB0_557 $32(%rip)
	WORD $0x9280000d  // mov	x13, #-1
	WORD $0x52800028  // mov	w8, #1
	WORD $0x8b08018c  // add	x12, x12, x8
	WORD $0x92800008  // mov	x8, #-1
	WORD $0xab0d016b  // adds	x11, x11, x13
	WORD $0x54fffecc  // b.gt	LBB0_554 $-40(%rip)
	WORD $0x17fffce4  // b	LBB0_438 $-3184(%rip)
LBB0_557:
	WORD $0xcb0a0188  // sub	x8, x12, x10
	WORD $0x91000508  // add	x8, x8, #1
	WORD $0x17fffcdf  // b	LBB0_436 $-3204(%rip)
	  // .p2align 2, 0x00
_MASK_USE_NUMBER:
	WORD $0x00000002  // .long 2

TEXT ·__skip_one(SB), NOSPLIT, $0-40
	NO_LOCAL_POINTERS

_entry:
	MOVD 16(g), R16
	SUB $256, RSP, R17
	CMP  R16, R17
	BLS  _stack_grow

_skip_one:
	MOVD s+0(FP), R0
	MOVD p+8(FP), R1
	MOVD m+16(FP), R2
	MOVD flags+24(FP), R3
	MOVD ·_subr__skip_one(SB), R11
	WORD $0x1000005e // adr x30, .+8
	JMP (R11)
	MOVD R0, ret+32(FP)
	RET

_stack_grow:
	MOVD R30, R3
	CALL runtime·morestack_noctxt<>(SB)
	JMP  _entry
