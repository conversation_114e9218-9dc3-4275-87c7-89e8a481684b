// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

#include "go_asm.h"
#include "funcdata.h"
#include "textflag.h"

TEXT ·__lookup_small_key_entry__(SB), NOSPLIT, $16
	NO_LOCAL_POINTERS
	WORD $0x100000a0 // adr x0, .+20
	MOVD R0, ret(FP)
	RET
	  // .p2align 4, 0x00
lCPI0_0:
	WORD $0x08040201
	WORD $0x80402010
	WORD $0x08040201
	WORD $0x80402010
	// // .byte 1
// .byte 2
// .byte 4
// .byte 8
// .byte 16
// .byte 32
// .byte 64
// .byte 128
// .byte 1
// .byte 2
// .byte 4
// .byte 8
// .byte 16
// .byte 32
// .byte 64
// .byte 128

lCPI0_1:
	WORD $0x09010800
	WORD $0x0b030a02
	WORD $0x0d050c04
	WORD $0x0f070e06
	// // .byte 0
// .byte 8
// .byte 1
// .byte 9
// .byte 2
// .byte 10
// .byte 3
// .byte 11
// .byte 4
// .byte 12
// .byte 5
// .byte 13
// .byte 6
// .byte 14
// .byte 7
// .byte 15

_lookup_small_key:
	WORD $0xd10083ff  // sub	sp, sp, #32
	WORD $0xa900fbfd  // stp	fp, lr, [sp, #8]
	WORD $0xa93ffbfd  // stp	fp, lr, [sp, #-8]
	WORD $0xd10023fd  // sub	fp, sp, #8
	WORD $0xf940040a  // ldr	x10, [x0, #8]
	WORD $0xf940002b  // ldr	x11, [x1]
	WORD $0x12001d49  // and	w9, w10, #0xff
	WORD $0x8b294928  // add	x8, x9, w9, uxtw #2
	WORD $0x8b08016d  // add	x13, x11, x8
	WORD $0x394001a8  // ldrb	w8, [x13]
	WORD $0x340019e8  // cbz	w8, LBB0_46 $828(%rip)
	WORD $0xf940000c  // ldr	x12, [x0]
	WORD $0xb84011ad  // ldur	w13, [x13, #1]
	WORD $0x110295ae  // add	w14, w13, #165
	WORD $0x8b0e016e  // add	x14, x11, x14
	WORD $0x92401d4f  // and	x15, x10, #0xff
	WORD $0x7100253f  // cmp	w9, #9
	WORD $0x54000942  // b.hs	LBB0_20 $296(%rip)
	WORD $0x11000530  // add	w16, w9, #1
	WORD $0x39400191  // ldrb	w17, [x12]
	WORD $0x528000e0  // mov	w0, #7
	WORD $0xaa0803e1  // mov	x1, x8
	WORD $0x14000007  // b	LBB0_5 $28(%rip)
LBB0_3:
	WORD $0x52800003  // mov	w3, #0
	WORD $0x6b0f007f  // cmp	w3, w15
	WORD $0x540007a2  // b.hs	LBB0_19 $244(%rip)
LBB0_4:
	WORD $0x8b1001ce  // add	x14, x14, x16
	WORD $0x71000421  // subs	w1, w1, #1
	WORD $0x54000b40  // b.eq	LBB0_23 $360(%rip)
LBB0_5:
	WORD $0x394001c3  // ldrb	w3, [x14]
	WORD $0x6b11007f  // cmp	w3, w17
	WORD $0x54ffff01  // b.ne	LBB0_3 $-32(%rip)
	WORD $0x394005c3  // ldrb	w3, [x14, #1]
	WORD $0x39400584  // ldrb	w4, [x12, #1]
	WORD $0x6b04007f  // cmp	w3, w4
	WORD $0x54000381  // b.ne	LBB0_13 $112(%rip)
	WORD $0x394009c3  // ldrb	w3, [x14, #2]
	WORD $0x39400984  // ldrb	w4, [x12, #2]
	WORD $0x6b04007f  // cmp	w3, w4
	WORD $0x54000381  // b.ne	LBB0_14 $112(%rip)
	WORD $0x39400dc3  // ldrb	w3, [x14, #3]
	WORD $0x39400d84  // ldrb	w4, [x12, #3]
	WORD $0x6b04007f  // cmp	w3, w4
	WORD $0x54000381  // b.ne	LBB0_15 $112(%rip)
	WORD $0x394011c3  // ldrb	w3, [x14, #4]
	WORD $0x39401184  // ldrb	w4, [x12, #4]
	WORD $0x6b04007f  // cmp	w3, w4
	WORD $0x54000381  // b.ne	LBB0_16 $112(%rip)
	WORD $0x394015c3  // ldrb	w3, [x14, #5]
	WORD $0x39401584  // ldrb	w4, [x12, #5]
	WORD $0x6b04007f  // cmp	w3, w4
	WORD $0x54000381  // b.ne	LBB0_17 $112(%rip)
	WORD $0x394019c3  // ldrb	w3, [x14, #6]
	WORD $0x39401984  // ldrb	w4, [x12, #6]
	WORD $0x6b04007f  // cmp	w3, w4
	WORD $0x54000381  // b.ne	LBB0_18 $112(%rip)
	WORD $0x39401dc3  // ldrb	w3, [x14, #7]
	WORD $0x39401d84  // ldrb	w4, [x12, #7]
	WORD $0x6b04007f  // cmp	w3, w4
	WORD $0x1a801403  // cinc	w3, w0, eq
	WORD $0x6b0f007f  // cmp	w3, w15
	WORD $0x54fffba3  // b.lo	LBB0_4 $-140(%rip)
	WORD $0x14000018  // b	LBB0_19 $96(%rip)
LBB0_13:
	WORD $0x52800023  // mov	w3, #1
	WORD $0x6b0f007f  // cmp	w3, w15
	WORD $0x54fffb23  // b.lo	LBB0_4 $-156(%rip)
	WORD $0x14000014  // b	LBB0_19 $80(%rip)
LBB0_14:
	WORD $0x52800043  // mov	w3, #2
	WORD $0x6b0f007f  // cmp	w3, w15
	WORD $0x54fffaa3  // b.lo	LBB0_4 $-172(%rip)
	WORD $0x14000010  // b	LBB0_19 $64(%rip)
LBB0_15:
	WORD $0x52800063  // mov	w3, #3
	WORD $0x6b0f007f  // cmp	w3, w15
	WORD $0x54fffa23  // b.lo	LBB0_4 $-188(%rip)
	WORD $0x1400000c  // b	LBB0_19 $48(%rip)
LBB0_16:
	WORD $0x52800083  // mov	w3, #4
	WORD $0x6b0f007f  // cmp	w3, w15
	WORD $0x54fff9a3  // b.lo	LBB0_4 $-204(%rip)
	WORD $0x14000008  // b	LBB0_19 $32(%rip)
LBB0_17:
	WORD $0x528000a3  // mov	w3, #5
	WORD $0x6b0f007f  // cmp	w3, w15
	WORD $0x54fff923  // b.lo	LBB0_4 $-220(%rip)
	WORD $0x14000004  // b	LBB0_19 $16(%rip)
LBB0_18:
	WORD $0x528000c3  // mov	w3, #6
	WORD $0x6b0f007f  // cmp	w3, w15
	WORD $0x54fff8a3  // b.lo	LBB0_4 $-236(%rip)
LBB0_19:
	WORD $0x8b0f01c8  // add	x8, x14, x15
	WORD $0x39400100  // ldrb	w0, [x8]
	WORD $0xa940fbfd  // ldp	fp, lr, [sp, #8]
	WORD $0x910083ff  // add	sp, sp, #32
	WORD $0xd65f03c0  // ret
LBB0_20:
	WORD $0xad400580  // ldp	q0, q1, [x12]
	WORD $0x92800010  // mov	x16, #-1
	WORD $0x9acf2210  // lsl	x16, x16, x15
	WORD $0x11000531  // add	w17, w9, #1
Lloh0:
	WORD $0x10fff320  // adr	x0, lCPI0_0 $-412(%rip)
Lloh1:
	WORD $0x3dc00002  // ldr	q2, [x0, lCPI0_0@PAGEOFF] $0(%rip)
Lloh2:
	WORD $0x10fff360  // adr	x0, lCPI0_1 $-404(%rip)
Lloh3:
	WORD $0x3dc00003  // ldr	q3, [x0, lCPI0_1@PAGEOFF] $0(%rip)
	WORD $0xaa0803e0  // mov	x0, x8
LBB0_21:
	WORD $0xad4015c4  // ldp	q4, q5, [x14]
	WORD $0x6e248c04  // cmeq.16b	v4, v0, v4
	WORD $0x6e258c25  // cmeq.16b	v5, v1, v5
	WORD $0x4e221c84  // and.16b	v4, v4, v2
	WORD $0x4e030084  // tbl.16b	v4, { v4 }, v3
	WORD $0x4e71b884  // addv.8h	h4, v4
	WORD $0x1e260081  // fmov	w1, s4
	WORD $0x4e221ca4  // and.16b	v4, v5, v2
	WORD $0x4e030084  // tbl.16b	v4, { v4 }, v3
	WORD $0x4e71b884  // addv.8h	h4, v4
	WORD $0x1e260083  // fmov	w3, s4
	WORD $0x33103c61  // bfi	w1, w3, #16, #16
	WORD $0x2a100021  // orr	w1, w1, w16
	WORD $0x3100043f  // cmn	w1, #1
	WORD $0x54fffc80  // b.eq	LBB0_19 $-112(%rip)
	WORD $0x8b1101ce  // add	x14, x14, x17
	WORD $0x71000400  // subs	w0, w0, #1
	WORD $0x54fffde1  // b.ne	LBB0_21 $-68(%rip)
LBB0_23:
	WORD $0xb100045f  // cmn	x2, #1
	WORD $0x54000c40  // b.eq	LBB0_46 $392(%rip)
	WORD $0x3dc00180  // ldr	q0, [x12]
	WORD $0x4f05e7e1  // movi.16b	v1, #191
	WORD $0x4e218403  // add.16b	v3, v0, v1
	WORD $0x4f00e742  // movi.16b	v2, #26
	WORD $0x6e233444  // cmhi.16b	v4, v2, v3
	WORD $0x4f01e403  // movi.16b	v3, #32
	WORD $0x4e231c84  // and.16b	v4, v4, v3
	WORD $0x4e208480  // add.16b	v0, v4, v0
	WORD $0x8b0d016b  // add	x11, x11, x13
	WORD $0x8b02016b  // add	x11, x11, x2
	WORD $0x92401d4a  // and	x10, x10, #0xff
	WORD $0x7100253f  // cmp	w9, #9
	WORD $0x540006e2  // b.hs	LBB0_43 $220(%rip)
	WORD $0x0e013c0c  // umov.b	w12, v0[0]
	WORD $0x0e033c0d  // umov.b	w13, v0[1]
	WORD $0x0e053c0e  // umov.b	w14, v0[2]
	WORD $0x0e073c0f  // umov.b	w15, v0[3]
	WORD $0x0e093c10  // umov.b	w16, v0[4]
	WORD $0x0e0b3c11  // umov.b	w17, v0[5]
	WORD $0x11000529  // add	w9, w9, #1
	WORD $0x0e0d3c00  // umov.b	w0, v0[6]
	WORD $0x528000e1  // mov	w1, #7
	WORD $0x0e0f3c02  // umov.b	w2, v0[7]
LBB0_26:
	WORD $0x39400163  // ldrb	w3, [x11]
	WORD $0x6b2c007f  // cmp	w3, w12, uxtb
	WORD $0x540002e1  // b.ne	LBB0_34 $92(%rip)
	WORD $0x39400563  // ldrb	w3, [x11, #1]
	WORD $0x6b2d007f  // cmp	w3, w13, uxtb
	WORD $0x540002c1  // b.ne	LBB0_35 $88(%rip)
	WORD $0x39400963  // ldrb	w3, [x11, #2]
	WORD $0x6b2e007f  // cmp	w3, w14, uxtb
	WORD $0x540002a1  // b.ne	LBB0_36 $84(%rip)
	WORD $0x39400d63  // ldrb	w3, [x11, #3]
	WORD $0x6b2f007f  // cmp	w3, w15, uxtb
	WORD $0x54000281  // b.ne	LBB0_37 $80(%rip)
	WORD $0x39401163  // ldrb	w3, [x11, #4]
	WORD $0x6b30007f  // cmp	w3, w16, uxtb
	WORD $0x54000261  // b.ne	LBB0_38 $76(%rip)
	WORD $0x39401563  // ldrb	w3, [x11, #5]
	WORD $0x6b31007f  // cmp	w3, w17, uxtb
	WORD $0x54000241  // b.ne	LBB0_39 $72(%rip)
	WORD $0x39401963  // ldrb	w3, [x11, #6]
	WORD $0x6b20007f  // cmp	w3, w0, uxtb
	WORD $0x54000221  // b.ne	LBB0_40 $68(%rip)
	WORD $0x39401d63  // ldrb	w3, [x11, #7]
	WORD $0x6b22007f  // cmp	w3, w2, uxtb
	WORD $0x1a811423  // cinc	w3, w1, eq
	WORD $0x1400000e  // b	LBB0_41 $56(%rip)
LBB0_34:
	WORD $0x52800003  // mov	w3, #0
	WORD $0x1400000c  // b	LBB0_41 $48(%rip)
LBB0_35:
	WORD $0x52800023  // mov	w3, #1
	WORD $0x1400000a  // b	LBB0_41 $40(%rip)
LBB0_36:
	WORD $0x52800043  // mov	w3, #2
	WORD $0x14000008  // b	LBB0_41 $32(%rip)
LBB0_37:
	WORD $0x52800063  // mov	w3, #3
	WORD $0x14000006  // b	LBB0_41 $24(%rip)
LBB0_38:
	WORD $0x52800083  // mov	w3, #4
	WORD $0x14000004  // b	LBB0_41 $16(%rip)
LBB0_39:
	WORD $0x528000a3  // mov	w3, #5
	WORD $0x14000002  // b	LBB0_41 $8(%rip)
LBB0_40:
	WORD $0x528000c3  // mov	w3, #6
LBB0_41:
	WORD $0x6b0a007f  // cmp	w3, w10
	WORD $0x540004e2  // b.hs	LBB0_47 $156(%rip)
	WORD $0x8b09016b  // add	x11, x11, x9
	WORD $0x71000508  // subs	w8, w8, #1
	WORD $0x54fffac1  // b.ne	LBB0_26 $-168(%rip)
	WORD $0x1400001f  // b	LBB0_46 $124(%rip)
LBB0_43:
	WORD $0x3dc00584  // ldr	q4, [x12, #16]
	WORD $0x4e218481  // add.16b	v1, v4, v1
	WORD $0x6e213441  // cmhi.16b	v1, v2, v1
	WORD $0x4e231c21  // and.16b	v1, v1, v3
	WORD $0x4e248421  // add.16b	v1, v1, v4
	WORD $0x9280000c  // mov	x12, #-1
	WORD $0x9aca218c  // lsl	x12, x12, x10
	WORD $0x11000529  // add	w9, w9, #1
Lloh4:
	WORD $0x10ffe6ad  // adr	x13, lCPI0_0 $-812(%rip)
Lloh5:
	WORD $0x3dc001a2  // ldr	q2, [x13, lCPI0_0@PAGEOFF] $0(%rip)
Lloh6:
	WORD $0x10ffe6ed  // adr	x13, lCPI0_1 $-804(%rip)
Lloh7:
	WORD $0x3dc001a3  // ldr	q3, [x13, lCPI0_1@PAGEOFF] $0(%rip)
LBB0_44:
	WORD $0xad401564  // ldp	q4, q5, [x11]
	WORD $0x6e248c04  // cmeq.16b	v4, v0, v4
	WORD $0x6e258c25  // cmeq.16b	v5, v1, v5
	WORD $0x4e221c84  // and.16b	v4, v4, v2
	WORD $0x4e030084  // tbl.16b	v4, { v4 }, v3
	WORD $0x4e71b884  // addv.8h	h4, v4
	WORD $0x1e26008d  // fmov	w13, s4
	WORD $0x4e221ca4  // and.16b	v4, v5, v2
	WORD $0x4e030084  // tbl.16b	v4, { v4 }, v3
	WORD $0x4e71b884  // addv.8h	h4, v4
	WORD $0x1e26008e  // fmov	w14, s4
	WORD $0x33103dcd  // bfi	w13, w14, #16, #16
	WORD $0x2a0c01ad  // orr	w13, w13, w12
	WORD $0x310005bf  // cmn	w13, #1
	WORD $0x54000100  // b.eq	LBB0_47 $32(%rip)
	WORD $0x8b09016b  // add	x11, x11, x9
	WORD $0x71000508  // subs	w8, w8, #1
	WORD $0x54fffde1  // b.ne	LBB0_44 $-68(%rip)
LBB0_46:
	WORD $0x92800000  // mov	x0, #-1
	WORD $0xa940fbfd  // ldp	fp, lr, [sp, #8]
	WORD $0x910083ff  // add	sp, sp, #32
	WORD $0xd65f03c0  // ret
LBB0_47:
	WORD $0x8b0a0168  // add	x8, x11, x10
	WORD $0x39400100  // ldrb	w0, [x8]
	WORD $0xa940fbfd  // ldp	fp, lr, [sp, #8]
	WORD $0x910083ff  // add	sp, sp, #32
	WORD $0xd65f03c0  // ret

TEXT ·__lookup_small_key(SB), NOSPLIT, $0-32
	NO_LOCAL_POINTERS

_entry:
	MOVD 16(g), R16
	SUB $96, RSP, R17
	CMP  R16, R17
	BLS  _stack_grow

_lookup_small_key:
	MOVD key+0(FP), R0
	MOVD table+8(FP), R1
	MOVD lowerOff+16(FP), R2
	MOVD ·_subr__lookup_small_key(SB), R11
	WORD $0x1000005e // adr x30, .+8
	JMP (R11)
	MOVD R0, ret+24(FP)
	RET

_stack_grow:
	MOVD R30, R3
	CALL runtime·morestack_noctxt<>(SB)
	JMP  _entry
