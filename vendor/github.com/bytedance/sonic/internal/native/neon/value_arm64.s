// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

#include "go_asm.h"
#include "funcdata.h"
#include "textflag.h"

TEXT ·__value_entry__(SB), NOSPLIT, $96
	NO_LOCAL_POINTERS
	WORD $0x100000a0 // adr x0, .+20
	MOVD R0, ret(FP)
	RET
	  // .p2align 4, 0x00
lCPI0_0:
	WORD $0x08040201
	WORD $0x80402010
	WORD $0x08040201
	WORD $0x80402010
	// // .byte 1
// .byte 2
// .byte 4
// .byte 8
// .byte 16
// .byte 32
// .byte 64
// .byte 128
// .byte 1
// .byte 2
// .byte 4
// .byte 8
// .byte 16
// .byte 32
// .byte 64
// .byte 128

lCPI0_1:
	WORD $0x09010800
	WORD $0x0b030a02
	WORD $0x0d050c04
	WORD $0x0f070e06
	// // .byte 0
// .byte 8
// .byte 1
// .byte 9
// .byte 2
// .byte 10
// .byte 3
// .byte 11
// .byte 4
// .byte 12
// .byte 5
// .byte 13
// .byte 6
// .byte 14
// .byte 7
// .byte 15

_value:
	WORD $0xd101c3ff  // sub	sp, sp, #112
	WORD $0xa900effc  // stp	x28, x27, [sp, #8]
	WORD $0xa901e7fa  // stp	x26, x25, [sp, #24]
	WORD $0xa902dff8  // stp	x24, x23, [sp, #40]
	WORD $0xa903d7f6  // stp	x22, x21, [sp, #56]
	WORD $0xa904cff4  // stp	x20, x19, [sp, #72]
	WORD $0xa905fbfd  // stp	fp, lr, [sp, #88]
	WORD $0xa93ffbfd  // stp	fp, lr, [sp, #-8]
	WORD $0xd10023fd  // sub	fp, sp, #8
	WORD $0xeb01005f  // cmp	x2, x1
	WORD $0x54000162  // b.hs	LBB0_5 $44(%rip)
	WORD $0x38626808  // ldrb	w8, [x0, x2]
	WORD $0x7100351f  // cmp	w8, #13
	WORD $0x54000100  // b.eq	LBB0_5 $32(%rip)
	WORD $0x7100811f  // cmp	w8, #32
	WORD $0x540000c0  // b.eq	LBB0_5 $24(%rip)
	WORD $0x51002d09  // sub	w9, w8, #11
	WORD $0x3100093f  // cmn	w9, #2
	WORD $0x54000062  // b.hs	LBB0_5 $12(%rip)
	WORD $0xaa0203ea  // mov	x10, x2
	WORD $0x14000042  // b	LBB0_27 $264(%rip)
LBB0_5:
	WORD $0x9100044a  // add	x10, x2, #1
	WORD $0xeb01015f  // cmp	x10, x1
	WORD $0x54000122  // b.hs	LBB0_9 $36(%rip)
	WORD $0x386a6808  // ldrb	w8, [x0, x10]
	WORD $0x7100351f  // cmp	w8, #13
	WORD $0x540000c0  // b.eq	LBB0_9 $24(%rip)
	WORD $0x7100811f  // cmp	w8, #32
	WORD $0x54000080  // b.eq	LBB0_9 $16(%rip)
	WORD $0x51002d09  // sub	w9, w8, #11
	WORD $0x3100093f  // cmn	w9, #2
	WORD $0x540006e3  // b.lo	LBB0_27 $220(%rip)
LBB0_9:
	WORD $0x9100084a  // add	x10, x2, #2
	WORD $0xeb01015f  // cmp	x10, x1
	WORD $0x54000122  // b.hs	LBB0_13 $36(%rip)
	WORD $0x386a6808  // ldrb	w8, [x0, x10]
	WORD $0x7100351f  // cmp	w8, #13
	WORD $0x540000c0  // b.eq	LBB0_13 $24(%rip)
	WORD $0x7100811f  // cmp	w8, #32
	WORD $0x54000080  // b.eq	LBB0_13 $16(%rip)
	WORD $0x51002d09  // sub	w9, w8, #11
	WORD $0x3100093f  // cmn	w9, #2
	WORD $0x54000583  // b.lo	LBB0_27 $176(%rip)
LBB0_13:
	WORD $0x91000c4a  // add	x10, x2, #3
	WORD $0xeb01015f  // cmp	x10, x1
	WORD $0x54000122  // b.hs	LBB0_17 $36(%rip)
	WORD $0x386a6808  // ldrb	w8, [x0, x10]
	WORD $0x7100351f  // cmp	w8, #13
	WORD $0x540000c0  // b.eq	LBB0_17 $24(%rip)
	WORD $0x7100811f  // cmp	w8, #32
	WORD $0x54000080  // b.eq	LBB0_17 $16(%rip)
	WORD $0x51002d09  // sub	w9, w8, #11
	WORD $0x3100093f  // cmn	w9, #2
	WORD $0x54000423  // b.lo	LBB0_27 $132(%rip)
LBB0_17:
	WORD $0x9100104a  // add	x10, x2, #4
	WORD $0xeb01015f  // cmp	x10, x1
	WORD $0x540001c2  // b.hs	LBB0_21 $56(%rip)
	WORD $0x52800028  // mov	w8, #1
	WORD $0xd284c009  // mov	x9, #9728
	WORD $0xf2c00029  // movk	x9, #1, lsl #32
LBB0_19:
	WORD $0x386a680b  // ldrb	w11, [x0, x10]
	WORD $0x7100817f  // cmp	w11, #32
	WORD $0x9acb210b  // lsl	x11, x8, x11
	WORD $0x8a09016b  // and	x11, x11, x9
	WORD $0xfa409964  // ccmp	x11, #0, #4, ls
	WORD $0x54000240  // b.eq	LBB0_25 $72(%rip)
	WORD $0x9100054a  // add	x10, x10, #1
	WORD $0xeb0a003f  // cmp	x1, x10
	WORD $0x54ffff01  // b.ne	LBB0_19 $-32(%rip)
	WORD $0x14000002  // b	LBB0_22 $8(%rip)
LBB0_21:
	WORD $0xaa0a03e2  // mov	x2, x10
LBB0_22:
	WORD $0x52800028  // mov	w8, #1
LBB0_23:
	WORD $0xf9000068  // str	x8, [x3]
	WORD $0xaa0203e1  // mov	x1, x2
LBB0_24:
	WORD $0xaa0103e0  // mov	x0, x1
	WORD $0xa945fbfd  // ldp	fp, lr, [sp, #88]
	WORD $0xa944cff4  // ldp	x20, x19, [sp, #72]
	WORD $0xa943d7f6  // ldp	x22, x21, [sp, #56]
	WORD $0xa942dff8  // ldp	x24, x23, [sp, #40]
	WORD $0xa941e7fa  // ldp	x26, x25, [sp, #24]
	WORD $0xa940effc  // ldp	x28, x27, [sp, #8]
	WORD $0x9101c3ff  // add	sp, sp, #112
	WORD $0xd65f03c0  // ret
LBB0_25:
	WORD $0xeb01015f  // cmp	x10, x1
	WORD $0x54fffe62  // b.hs	LBB0_22 $-52(%rip)
	WORD $0x386a6808  // ldrb	w8, [x0, x10]
LBB0_27:
	WORD $0x7101f51f  // cmp	w8, #125
	WORD $0x54003028  // b.hi	LBB0_109 $1540(%rip)
	WORD $0x91000542  // add	x2, x10, #1
	WORD $0x2a0803e9  // mov	w9, w8
Lloh0:
	WORD $0x1001142b  // adr	x11, LJTI0_0 $8836(%rip)
Lloh1:
	WORD $0x9100016b  // add	x11, x11, LJTI0_0@PAGEOFF $0(%rip)
	WORD $0x10fffd6c  // adr	x12, LBB0_22 $-84(%rip)
	WORD $0x7869796d  // ldrh	w13, [x11, x9, lsl #1]
	WORD $0x8b0d098c  // add	x12, x12, x13, lsl #2
	WORD $0xd61f0180  // br	x12
LBB0_29:
	WORD $0x8b0a000d  // add	x13, x0, x10
	WORD $0x370801e4  // tbnz	w4, #1, LBB0_34 $60(%rip)
	WORD $0xa9422468  // ldp	x8, x9, [x3, #32]
	WORD $0x5280012b  // mov	w11, #9
	WORD $0xa9007c6b  // stp	x11, xzr, [x3]
	WORD $0xa901287f  // stp	xzr, x10, [x3, #16]
	WORD $0xeb0a003f  // cmp	x1, x10
	WORD $0x54005be9  // b.ls	LBB0_194 $2940(%rip)
	WORD $0x394001ab  // ldrb	w11, [x13]
	WORD $0x7100b57f  // cmp	w11, #45
	WORD $0x540002c1  // b.ne	LBB0_39 $88(%rip)
	WORD $0xeb02003f  // cmp	x1, x2
	WORD $0x54005b49  // b.ls	LBB0_194 $2920(%rip)
	WORD $0x3862680c  // ldrb	w12, [x0, x2]
	WORD $0x1280000e  // mov	w14, #-1
	WORD $0x14000014  // b	LBB0_40 $80(%rip)
LBB0_34:
	WORD $0x7100b51f  // cmp	w8, #45
	WORD $0x1a9f17ee  // cset	w14, eq
	WORD $0x9a8d15a8  // cinc	x8, x13, eq
	WORD $0xcb0a0029  // sub	x9, x1, x10
	WORD $0xeb0e012d  // subs	x13, x9, x14
	WORD $0x5400e220  // b.eq	LBB0_478 $7236(%rip)
	WORD $0x39400109  // ldrb	w9, [x8]
	WORD $0x5100e92b  // sub	w11, w9, #58
	WORD $0x31002d7f  // cmn	w11, #11
	WORD $0x54004489  // b.ls	LBB0_143 $2192(%rip)
	WORD $0x7100c13f  // cmp	w9, #48
	WORD $0x54000301  // b.ne	LBB0_44 $96(%rip)
	WORD $0xf10005bf  // cmp	x13, #1
	WORD $0x54000161  // b.ne	LBB0_42 $44(%rip)
	WORD $0x5280002f  // mov	w15, #1
	WORD $0x1400017b  // b	LBB0_120 $1516(%rip)
LBB0_39:
	WORD $0x5280002e  // mov	w14, #1
	WORD $0xaa0b03ec  // mov	x12, x11
	WORD $0xaa0a03e2  // mov	x2, x10
LBB0_40:
	WORD $0x5100e98f  // sub	w15, w12, #58
	WORD $0x31002dff  // cmn	w15, #11
	WORD $0x54001548  // b.hi	LBB0_83 $680(%rip)
	WORD $0x92800028  // mov	x8, #-2
	WORD $0x17ffffc1  // b	LBB0_23 $-252(%rip)
LBB0_42:
	WORD $0x39400509  // ldrb	w9, [x8, #1]
	WORD $0x5280002f  // mov	w15, #1
	WORD $0x5100b929  // sub	w9, w9, #46
	WORD $0x7100dd3f  // cmp	w9, #55
	WORD $0x54002dc8  // b.hi	LBB0_120 $1464(%rip)
	WORD $0x5280002b  // mov	w11, #1
	WORD $0x9ac92169  // lsl	x9, x11, x9
	WORD $0xb20903eb  // mov	x11, #36028797027352576
	WORD $0xf280002b  // movk	x11, #1
	WORD $0xea0b013f  // tst	x9, x11
	WORD $0x54002d00  // b.eq	LBB0_120 $1440(%rip)
LBB0_44:
	WORD $0xf10041bf  // cmp	x13, #16
	WORD $0x5400e1a3  // b.lo	LBB0_486 $7220(%rip)
	WORD $0xd2800010  // mov	x16, #0
	WORD $0xd280000f  // mov	x15, #0
	WORD $0x92800009  // mov	x9, #-1
	WORD $0x4f01e5c0  // movi.16b	v0, #46
	WORD $0x4f01e561  // movi.16b	v1, #43
	WORD $0x4f01e5a2  // movi.16b	v2, #45
	WORD $0x4f06e603  // movi.16b	v3, #208
	WORD $0x4f00e544  // movi.16b	v4, #10
Lloh2:
	WORD $0x10ffeb6b  // adr	x11, lCPI0_0 $-660(%rip)
Lloh3:
	WORD $0x3dc00165  // ldr	q5, [x11, lCPI0_0@PAGEOFF] $0(%rip)
	WORD $0x4f06e7e6  // movi.16b	v6, #223
	WORD $0x4f02e4a7  // movi.16b	v7, #69
Lloh4:
	WORD $0x10ffeb6b  // adr	x11, lCPI0_1 $-660(%rip)
Lloh5:
	WORD $0x3dc00170  // ldr	q16, [x11, lCPI0_1@PAGEOFF] $0(%rip)
	WORD $0x12800011  // mov	w17, #-1
	WORD $0x9280000c  // mov	x12, #-1
	WORD $0x9280000b  // mov	x11, #-1
LBB0_46:
	WORD $0x3cef6911  // ldr	q17, [x8, x15]
	WORD $0x6e208e32  // cmeq.16b	v18, v17, v0
	WORD $0x6e218e33  // cmeq.16b	v19, v17, v1
	WORD $0x6e228e34  // cmeq.16b	v20, v17, v2
	WORD $0x4e238635  // add.16b	v21, v17, v3
	WORD $0x6e353495  // cmhi.16b	v21, v4, v21
	WORD $0x4e261e31  // and.16b	v17, v17, v6
	WORD $0x6e278e31  // cmeq.16b	v17, v17, v7
	WORD $0x4eb41e73  // orr.16b	v19, v19, v20
	WORD $0x4eb21eb4  // orr.16b	v20, v21, v18
	WORD $0x4eb31e35  // orr.16b	v21, v17, v19
	WORD $0x4eb51e94  // orr.16b	v20, v20, v21
	WORD $0x4e251e52  // and.16b	v18, v18, v5
	WORD $0x4e100252  // tbl.16b	v18, { v18 }, v16
	WORD $0x4e71ba52  // addv.8h	h18, v18
	WORD $0x1e260242  // fmov	w2, s18
	WORD $0x4e251e31  // and.16b	v17, v17, v5
	WORD $0x4e100231  // tbl.16b	v17, { v17 }, v16
	WORD $0x4e71ba31  // addv.8h	h17, v17
	WORD $0x1e260224  // fmov	w4, s17
	WORD $0x4e251e71  // and.16b	v17, v19, v5
	WORD $0x4e100231  // tbl.16b	v17, { v17 }, v16
	WORD $0x4e71ba31  // addv.8h	h17, v17
	WORD $0x1e260226  // fmov	w6, s17
	WORD $0x4e251e91  // and.16b	v17, v20, v5
	WORD $0x4e100231  // tbl.16b	v17, { v17 }, v16
	WORD $0x4e71ba31  // addv.8h	h17, v17
	WORD $0x1e260221  // fmov	w1, s17
	WORD $0x2a2103e1  // mvn	w1, w1
	WORD $0x32103c21  // orr	w1, w1, #0xffff0000
	WORD $0x5ac00021  // rbit	w1, w1
	WORD $0x5ac01021  // clz	w1, w1
	WORD $0x1ac12225  // lsl	w5, w17, w1
	WORD $0x0a250047  // bic	w7, w2, w5
	WORD $0x0a250093  // bic	w19, w4, w5
	WORD $0x0a2500d4  // bic	w20, w6, w5
	WORD $0x7100403f  // cmp	w1, #16
	WORD $0x1a870045  // csel	w5, w2, w7, eq
	WORD $0x1a930084  // csel	w4, w4, w19, eq
	WORD $0x1a9400c2  // csel	w2, w6, w20, eq
	WORD $0x510004a6  // sub	w6, w5, #1
	WORD $0x6a0500c6  // ands	w6, w6, w5
	WORD $0x54004aa1  // b.ne	LBB0_185 $2388(%rip)
	WORD $0x51000486  // sub	w6, w4, #1
	WORD $0x6a0400c6  // ands	w6, w6, w4
	WORD $0x54004a41  // b.ne	LBB0_185 $2376(%rip)
	WORD $0x51000446  // sub	w6, w2, #1
	WORD $0x6a0200c6  // ands	w6, w6, w2
	WORD $0x540049e1  // b.ne	LBB0_185 $2364(%rip)
	WORD $0x340000c5  // cbz	w5, LBB0_52 $24(%rip)
	WORD $0x5ac000a5  // rbit	w5, w5
	WORD $0x5ac010a5  // clz	w5, w5
	WORD $0xb100057f  // cmn	x11, #1
	WORD $0x54007b01  // b.ne	LBB0_283 $3936(%rip)
	WORD $0x8b0501eb  // add	x11, x15, x5
LBB0_52:
	WORD $0x340000c4  // cbz	w4, LBB0_55 $24(%rip)
	WORD $0x5ac00084  // rbit	w4, w4
	WORD $0x5ac01084  // clz	w4, w4
	WORD $0xb100059f  // cmn	x12, #1
	WORD $0x54007aa1  // b.ne	LBB0_284 $3924(%rip)
	WORD $0x8b0401ec  // add	x12, x15, x4
LBB0_55:
	WORD $0x340000c2  // cbz	w2, LBB0_58 $24(%rip)
	WORD $0x5ac00042  // rbit	w2, w2
	WORD $0x5ac01042  // clz	w2, w2
	WORD $0xb100053f  // cmn	x9, #1
	WORD $0x54007a41  // b.ne	LBB0_285 $3912(%rip)
	WORD $0x8b0201e9  // add	x9, x15, x2
LBB0_58:
	WORD $0x7100403f  // cmp	w1, #16
	WORD $0x54000621  // b.ne	LBB0_73 $196(%rip)
	WORD $0x910041ef  // add	x15, x15, #16
	WORD $0xd1004210  // sub	x16, x16, #16
	WORD $0x8b1001a1  // add	x1, x13, x16
	WORD $0xf1003c3f  // cmp	x1, #15
	WORD $0x54fff6e8  // b.hi	LBB0_46 $-292(%rip)
	WORD $0x8b0f0110  // add	x16, x8, x15
	WORD $0xeb0f01bf  // cmp	x13, x15
	WORD $0x54000560  // b.eq	LBB0_74 $172(%rip)
LBB0_61:
	WORD $0x8b01020d  // add	x13, x16, x1
	WORD $0xaa3003ef  // mvn	x15, x16
	WORD $0x8b000151  // add	x17, x10, x0
	WORD $0x8b1101ef  // add	x15, x15, x17
	WORD $0x8b0e01ef  // add	x15, x15, x14
	WORD $0xcb08020e  // sub	x14, x16, x8
	WORD $0xaa1003f1  // mov	x17, x16
	WORD $0x14000009  // b	LBB0_64 $36(%rip)
LBB0_62:
	WORD $0xb100059f  // cmn	x12, #1
	WORD $0xaa0e03ec  // mov	x12, x14
	WORD $0x54001f81  // b.ne	LBB0_119 $1008(%rip)
LBB0_63:
	WORD $0xd10005ef  // sub	x15, x15, #1
	WORD $0x910005ce  // add	x14, x14, #1
	WORD $0xaa1103f0  // mov	x16, x17
	WORD $0xd1000421  // sub	x1, x1, #1
	WORD $0xb40033e1  // cbz	x1, LBB0_145 $1660(%rip)
LBB0_64:
	WORD $0x38401622  // ldrb	w2, [x17], #1
	WORD $0x5100c044  // sub	w4, w2, #48
	WORD $0x7100289f  // cmp	w4, #10
	WORD $0x54ffff03  // b.lo	LBB0_63 $-32(%rip)
	WORD $0x7100b45f  // cmp	w2, #45
	WORD $0x5400016d  // b.le	LBB0_70 $44(%rip)
	WORD $0x7101945f  // cmp	w2, #101
	WORD $0x54fffe20  // b.eq	LBB0_62 $-60(%rip)
	WORD $0x7101145f  // cmp	w2, #69
	WORD $0x54fffde0  // b.eq	LBB0_62 $-68(%rip)
	WORD $0x7100b85f  // cmp	w2, #46
	WORD $0x540001e1  // b.ne	LBB0_74 $60(%rip)
	WORD $0xb100057f  // cmn	x11, #1
	WORD $0xaa0e03eb  // mov	x11, x14
	WORD $0x54fffda0  // b.eq	LBB0_63 $-76(%rip)
	WORD $0x140000e7  // b	LBB0_119 $924(%rip)
LBB0_70:
	WORD $0x7100ac5f  // cmp	w2, #43
	WORD $0x54000060  // b.eq	LBB0_72 $12(%rip)
	WORD $0x7100b45f  // cmp	w2, #45
	WORD $0x540000e1  // b.ne	LBB0_74 $28(%rip)
LBB0_72:
	WORD $0xb100053f  // cmn	x9, #1
	WORD $0xaa0e03e9  // mov	x9, x14
	WORD $0x54fffca0  // b.eq	LBB0_63 $-108(%rip)
	WORD $0x140000df  // b	LBB0_119 $892(%rip)
LBB0_73:
	WORD $0x8b21410d  // add	x13, x8, w1, uxtw
	WORD $0x8b0f01b0  // add	x16, x13, x15
LBB0_74:
	WORD $0x9280000f  // mov	x15, #-1
	WORD $0xb4002fab  // cbz	x11, LBB0_142 $1524(%rip)
LBB0_75:
	WORD $0xb4002f89  // cbz	x9, LBB0_142 $1520(%rip)
	WORD $0xb4002f6c  // cbz	x12, LBB0_142 $1516(%rip)
	WORD $0xcb08020d  // sub	x13, x16, x8
	WORD $0xd10005ae  // sub	x14, x13, #1
	WORD $0xeb0e017f  // cmp	x11, x14
	WORD $0x54001a80  // b.eq	LBB0_118 $848(%rip)
	WORD $0xeb0e013f  // cmp	x9, x14
	WORD $0x54001a40  // b.eq	LBB0_118 $840(%rip)
	WORD $0xeb0e019f  // cmp	x12, x14
	WORD $0x54001a00  // b.eq	LBB0_118 $832(%rip)
	WORD $0xf100052e  // subs	x14, x9, #1
	WORD $0x54002d8b  // b.lt	LBB0_139 $1456(%rip)
	WORD $0xeb0e019f  // cmp	x12, x14
	WORD $0x54002d40  // b.eq	LBB0_139 $1448(%rip)
	WORD $0xaa2903ef  // mvn	x15, x9
	WORD $0x1400016d  // b	LBB0_142 $1460(%rip)
LBB0_83:
	WORD $0x7100c19f  // cmp	w12, #48
	WORD $0x54000181  // b.ne	LBB0_86 $48(%rip)
	WORD $0x8b02000c  // add	x12, x0, x2
	WORD $0x3940058c  // ldrb	w12, [x12, #1]
	WORD $0x5100b98c  // sub	w12, w12, #46
	WORD $0x7100dd9f  // cmp	w12, #55
	WORD $0x54003d48  // b.hi	LBB0_183 $1960(%rip)
	WORD $0x5280002f  // mov	w15, #1
	WORD $0x9acc21ec  // lsl	x12, x15, x12
	WORD $0xb20903ef  // mov	x15, #36028797027352576
	WORD $0xf280002f  // movk	x15, #1
	WORD $0xea0f019f  // tst	x12, x15
	WORD $0x54003c80  // b.eq	LBB0_183 $1936(%rip)
LBB0_86:
	WORD $0xd280000c  // mov	x12, #0
	WORD $0x52800004  // mov	w4, #0
	WORD $0x52800010  // mov	w16, #0
	WORD $0x5280014f  // mov	w15, #10
LBB0_87:
	WORD $0x38626805  // ldrb	w5, [x0, x2]
	WORD $0x5100c0b1  // sub	w17, w5, #48
	WORD $0x7100263f  // cmp	w17, #9
	WORD $0x54002868  // b.hi	LBB0_135 $1292(%rip)
	WORD $0x71004c9f  // cmp	w4, #19
	WORD $0x9b0f7d85  // mul	x5, x12, x15
	WORD $0x8b3100b1  // add	x17, x5, w17, uxtb
	WORD $0x1a842485  // cinc	w5, w4, lo
	WORD $0x7100489f  // cmp	w4, #18
	WORD $0x1a909610  // cinc	w16, w16, hi
	WORD $0xaa0503e4  // mov	x4, x5
	WORD $0x9a91818c  // csel	x12, x12, x17, hi
	WORD $0x91000442  // add	x2, x2, #1
	WORD $0xeb02003f  // cmp	x1, x2
	WORD $0x54fffe41  // b.ne	LBB0_87 $-56(%rip)
	WORD $0x52800031  // mov	w17, #1
	WORD $0xaa0103e2  // mov	x2, x1
	WORD $0x7100021f  // cmp	w16, #0
	WORD $0x1a9fd7ef  // cset	w15, gt
	WORD $0xb40031ac  // cbz	x12, LBB0_162 $1588(%rip)
	WORD $0x140001a5  // b	LBB0_171 $1684(%rip)
LBB0_90:
	WORD $0xd1000c28  // sub	x8, x1, #3
	WORD $0xeb08015f  // cmp	x10, x8
	WORD $0x54003de2  // b.hs	LBB0_194 $1980(%rip)
	WORD $0x8b020008  // add	x8, x0, x2
	WORD $0xb85ff108  // ldur	w8, [x8, #-1]
	WORD $0x528eadc9  // mov	w9, #30062
	WORD $0x72ad8d89  // movk	w9, #27756, lsl #16
	WORD $0x6b09011f  // cmp	w8, w9
	WORD $0x54002901  // b.ne	LBB0_146 $1312(%rip)
	WORD $0x91001141  // add	x1, x10, #4
	WORD $0x52800048  // mov	w8, #2
	WORD $0xf9000068  // str	x8, [x3]
	WORD $0x17fffee9  // b	LBB0_24 $-1116(%rip)
LBB0_93:
	WORD $0xf261009f  // tst	x4, #0x80000000
	WORD $0x1a9f17e8  // cset	w8, eq
	WORD $0x528001a9  // mov	w9, #13
	WORD $0x1400008c  // b	LBB0_116 $560(%rip)
LBB0_94:
	WORD $0xf261009f  // tst	x4, #0x80000000
	WORD $0x1a9f17e8  // cset	w8, eq
	WORD $0x52800169  // mov	w9, #11
	WORD $0x14000088  // b	LBB0_116 $544(%rip)
LBB0_95:
	WORD $0x372812e4  // tbnz	w4, #5, LBB0_122 $604(%rip)
	WORD $0xeb020029  // subs	x9, x1, x2
	WORD $0x5400f180  // b.eq	LBB0_580 $7728(%rip)
	WORD $0xf101013f  // cmp	x9, #64
	WORD $0x5400b683  // b.lo	LBB0_465 $5840(%rip)
	WORD $0xd280000a  // mov	x10, #0
	WORD $0x92800008  // mov	x8, #-1
	WORD $0x4f01e440  // movi.16b	v0, #34
	WORD $0x4f02e781  // movi.16b	v1, #92
Lloh6:
	WORD $0x10ffd0ab  // adr	x11, lCPI0_0 $-1516(%rip)
Lloh7:
	WORD $0x3dc00162  // ldr	q2, [x11, lCPI0_0@PAGEOFF] $0(%rip)
Lloh8:
	WORD $0x10ffd0eb  // adr	x11, lCPI0_1 $-1508(%rip)
Lloh9:
	WORD $0x3dc00163  // ldr	q3, [x11, lCPI0_1@PAGEOFF] $0(%rip)
	WORD $0xaa0203eb  // mov	x11, x2
LBB0_99:
	WORD $0x8b0b000c  // add	x12, x0, x11
	WORD $0xad401584  // ldp	q4, q5, [x12]
	WORD $0xad411d86  // ldp	q6, q7, [x12, #32]
	WORD $0x6e208c90  // cmeq.16b	v16, v4, v0
	WORD $0x6e208cb1  // cmeq.16b	v17, v5, v0
	WORD $0x6e208cd2  // cmeq.16b	v18, v6, v0
	WORD $0x6e208cf3  // cmeq.16b	v19, v7, v0
	WORD $0x6e218c84  // cmeq.16b	v4, v4, v1
	WORD $0x6e218ca5  // cmeq.16b	v5, v5, v1
	WORD $0x6e218cc6  // cmeq.16b	v6, v6, v1
	WORD $0x6e218ce7  // cmeq.16b	v7, v7, v1
	WORD $0x4e221e10  // and.16b	v16, v16, v2
	WORD $0x4e030210  // tbl.16b	v16, { v16 }, v3
	WORD $0x4e71ba10  // addv.8h	h16, v16
	WORD $0x1e26020c  // fmov	w12, s16
	WORD $0x4e221e30  // and.16b	v16, v17, v2
	WORD $0x4e030210  // tbl.16b	v16, { v16 }, v3
	WORD $0x4e71ba10  // addv.8h	h16, v16
	WORD $0x1e26020d  // fmov	w13, s16
	WORD $0x4e221e50  // and.16b	v16, v18, v2
	WORD $0x4e030210  // tbl.16b	v16, { v16 }, v3
	WORD $0x4e71ba10  // addv.8h	h16, v16
	WORD $0x1e26020e  // fmov	w14, s16
	WORD $0x4e221e70  // and.16b	v16, v19, v2
	WORD $0x4e030210  // tbl.16b	v16, { v16 }, v3
	WORD $0x4e71ba10  // addv.8h	h16, v16
	WORD $0x1e26020f  // fmov	w15, s16
	WORD $0x4e221c84  // and.16b	v4, v4, v2
	WORD $0x4e030084  // tbl.16b	v4, { v4 }, v3
	WORD $0x4e71b884  // addv.8h	h4, v4
	WORD $0x1e260090  // fmov	w16, s4
	WORD $0x4e221ca4  // and.16b	v4, v5, v2
	WORD $0x4e030084  // tbl.16b	v4, { v4 }, v3
	WORD $0x4e71b884  // addv.8h	h4, v4
	WORD $0x1e260091  // fmov	w17, s4
	WORD $0x4e221cc4  // and.16b	v4, v6, v2
	WORD $0x4e030084  // tbl.16b	v4, { v4 }, v3
	WORD $0x4e71b884  // addv.8h	h4, v4
	WORD $0x1e260084  // fmov	w4, s4
	WORD $0x4e221ce4  // and.16b	v4, v7, v2
	WORD $0x4e030084  // tbl.16b	v4, { v4 }, v3
	WORD $0x4e71b884  // addv.8h	h4, v4
	WORD $0x1e260085  // fmov	w5, s4
	WORD $0xd3607dce  // lsl	x14, x14, #32
	WORD $0xaa0fc1ce  // orr	x14, x14, x15, lsl #48
	WORD $0x53103dad  // lsl	w13, w13, #16
	WORD $0xaa0d01cd  // orr	x13, x14, x13
	WORD $0xaa0c01ac  // orr	x12, x13, x12
	WORD $0xd3607c8d  // lsl	x13, x4, #32
	WORD $0xaa05c1ad  // orr	x13, x13, x5, lsl #48
	WORD $0x53103e2e  // lsl	w14, w17, #16
	WORD $0xaa0e01ad  // orr	x13, x13, x14
	WORD $0xaa1001ad  // orr	x13, x13, x16
	WORD $0xb500010d  // cbnz	x13, LBB0_103 $32(%rip)
	WORD $0xb500018a  // cbnz	x10, LBB0_104 $48(%rip)
	WORD $0xb50002cc  // cbnz	x12, LBB0_105 $88(%rip)
LBB0_102:
	WORD $0xd1010129  // sub	x9, x9, #64
	WORD $0x9101016b  // add	x11, x11, #64
	WORD $0xf100fd3f  // cmp	x9, #63
	WORD $0x54fff8a8  // b.hi	LBB0_99 $-236(%rip)
	WORD $0x14000532  // b	LBB0_450 $5320(%rip)
LBB0_103:
	WORD $0xb100051f  // cmn	x8, #1
	WORD $0xdac001ae  // rbit	x14, x13
	WORD $0xdac011ce  // clz	x14, x14
	WORD $0x8b0b01ce  // add	x14, x14, x11
	WORD $0x9a8e1108  // csel	x8, x8, x14, ne
LBB0_104:
	WORD $0x8a2a01ae  // bic	x14, x13, x10
	WORD $0xaa0e054f  // orr	x15, x10, x14, lsl #1
	WORD $0x8a2f01aa  // bic	x10, x13, x15
	WORD $0x9201f14a  // and	x10, x10, #0xaaaaaaaaaaaaaaaa
	WORD $0xab0e014d  // adds	x13, x10, x14
	WORD $0x1a9f37ea  // cset	w10, hs
	WORD $0xd37ff9ad  // lsl	x13, x13, #1
	WORD $0xd200f1ad  // eor	x13, x13, #0x5555555555555555
	WORD $0x8a0f01ad  // and	x13, x13, x15
	WORD $0x8a2d018c  // bic	x12, x12, x13
	WORD $0xb4fffd8c  // cbz	x12, LBB0_102 $-80(%rip)
LBB0_105:
	WORD $0xdac00189  // rbit	x9, x12
	WORD $0xdac01129  // clz	x9, x9
	WORD $0x8b0b0129  // add	x9, x9, x11
	WORD $0x9100052c  // add	x12, x9, #1
	WORD $0xb6f816cc  // tbz	x12, #63, LBB0_134 $728(%rip)
	WORD $0x1400071b  // b	LBB0_575 $7276(%rip)
LBB0_106:
	WORD $0xd1000c28  // sub	x8, x1, #3
	WORD $0xeb08015f  // cmp	x10, x8
	WORD $0x54002f22  // b.hs	LBB0_194 $1508(%rip)
	WORD $0x8b020008  // add	x8, x0, x2
	WORD $0xb85ff108  // ldur	w8, [x8, #-1]
	WORD $0x528e4e89  // mov	w9, #29300
	WORD $0x72acaea9  // movk	w9, #25973, lsl #16
	WORD $0x6b09011f  // cmp	w8, w9
	WORD $0x54001c41  // b.ne	LBB0_150 $904(%rip)
	WORD $0x91001141  // add	x1, x10, #4
	WORD $0x52800068  // mov	w8, #3
	WORD $0xf9000068  // str	x8, [x3]
	WORD $0x17fffe73  // b	LBB0_24 $-1588(%rip)
LBB0_109:
	WORD $0x92800028  // mov	x8, #-2
	WORD $0xf9000068  // str	x8, [x3]
	WORD $0xaa0a03e1  // mov	x1, x10
	WORD $0x17fffe6f  // b	LBB0_24 $-1604(%rip)
LBB0_110:
	WORD $0xf261009f  // tst	x4, #0x80000000
	WORD $0x1a9f17e8  // cset	w8, eq
	WORD $0x52800149  // mov	w9, #10
	WORD $0x14000012  // b	LBB0_116 $72(%rip)
LBB0_111:
	WORD $0x528000a8  // mov	w8, #5
	WORD $0x17fffe67  // b	LBB0_23 $-1636(%rip)
LBB0_112:
	WORD $0xd1001028  // sub	x8, x1, #4
	WORD $0xeb08015f  // cmp	x10, x8
	WORD $0x54002c42  // b.hs	LBB0_194 $1416(%rip)
	WORD $0xb8626808  // ldr	w8, [x0, x2]
	WORD $0x528d8c29  // mov	w9, #27745
	WORD $0x72acae69  // movk	w9, #25971, lsl #16
	WORD $0x6b09011f  // cmp	w8, w9
	WORD $0x54001c01  // b.ne	LBB0_155 $896(%rip)
	WORD $0x91001541  // add	x1, x10, #5
	WORD $0x52800088  // mov	w8, #4
	WORD $0xf9000068  // str	x8, [x3]
	WORD $0x17fffe5d  // b	LBB0_24 $-1676(%rip)
LBB0_115:
	WORD $0xf261009f  // tst	x4, #0x80000000
	WORD $0x1a9f17e8  // cset	w8, eq
	WORD $0x52800189  // mov	w9, #12
LBB0_116:
	WORD $0x9280002a  // mov	x10, #-2
	WORD $0x9a890149  // csel	x9, x10, x9, eq
	WORD $0xf9000069  // str	x9, [x3]
	WORD $0xcb080041  // sub	x1, x2, x8
	WORD $0x17fffe55  // b	LBB0_24 $-1708(%rip)
LBB0_117:
	WORD $0x528000c8  // mov	w8, #6
	WORD $0x17fffe51  // b	LBB0_23 $-1724(%rip)
LBB0_118:
	WORD $0xcb0d03ef  // neg	x15, x13
LBB0_119:
	WORD $0xb7f8144f  // tbnz	x15, #63, LBB0_142 $648(%rip)
LBB0_120:
	WORD $0x8b0f0108  // add	x8, x8, x15
	WORD $0xcb000101  // sub	x1, x8, x0
	WORD $0xb7f8146a  // tbnz	x10, #63, LBB0_144 $652(%rip)
	WORD $0x52800108  // mov	w8, #8
	WORD $0xf9000068  // str	x8, [x3]
	WORD $0xf9000c6a  // str	x10, [x3, #24]
	WORD $0x17fffe4a  // b	LBB0_24 $-1752(%rip)
LBB0_122:
	WORD $0xeb02002a  // subs	x10, x1, x2
	WORD $0x5400dec0  // b.eq	LBB0_580 $7128(%rip)
	WORD $0xf101015f  // cmp	x10, #64
	WORD $0x5400a8c3  // b.lo	LBB0_471 $5400(%rip)
	WORD $0xd2800009  // mov	x9, #0
	WORD $0x92800008  // mov	x8, #-1
	WORD $0x4f01e440  // movi.16b	v0, #34
Lloh10:
	WORD $0x10ffbe0b  // adr	x11, lCPI0_0 $-2112(%rip)
Lloh11:
	WORD $0x3dc00161  // ldr	q1, [x11, lCPI0_0@PAGEOFF] $0(%rip)
Lloh12:
	WORD $0x10ffbe4b  // adr	x11, lCPI0_1 $-2104(%rip)
Lloh13:
	WORD $0x3dc00162  // ldr	q2, [x11, lCPI0_1@PAGEOFF] $0(%rip)
	WORD $0x4f02e783  // movi.16b	v3, #92
	WORD $0x4f01e404  // movi.16b	v4, #32
	WORD $0xaa0203eb  // mov	x11, x2
LBB0_125:
	WORD $0x8b0b000c  // add	x12, x0, x11
	WORD $0xad401d90  // ldp	q16, q7, [x12]
	WORD $0xad411586  // ldp	q6, q5, [x12, #32]
	WORD $0x6e208e11  // cmeq.16b	v17, v16, v0
	WORD $0x4e211e31  // and.16b	v17, v17, v1
	WORD $0x4e020231  // tbl.16b	v17, { v17 }, v2
	WORD $0x4e71ba31  // addv.8h	h17, v17
	WORD $0x1e26022c  // fmov	w12, s17
	WORD $0x6e208cf1  // cmeq.16b	v17, v7, v0
	WORD $0x4e211e31  // and.16b	v17, v17, v1
	WORD $0x4e020231  // tbl.16b	v17, { v17 }, v2
	WORD $0x4e71ba31  // addv.8h	h17, v17
	WORD $0x1e26022d  // fmov	w13, s17
	WORD $0x6e208cd1  // cmeq.16b	v17, v6, v0
	WORD $0x4e211e31  // and.16b	v17, v17, v1
	WORD $0x4e020231  // tbl.16b	v17, { v17 }, v2
	WORD $0x4e71ba31  // addv.8h	h17, v17
	WORD $0x1e26022e  // fmov	w14, s17
	WORD $0x6e208cb1  // cmeq.16b	v17, v5, v0
	WORD $0x4e211e31  // and.16b	v17, v17, v1
	WORD $0x4e020231  // tbl.16b	v17, { v17 }, v2
	WORD $0x4e71ba31  // addv.8h	h17, v17
	WORD $0x1e26022f  // fmov	w15, s17
	WORD $0x6e238e11  // cmeq.16b	v17, v16, v3
	WORD $0x4e211e31  // and.16b	v17, v17, v1
	WORD $0x4e020231  // tbl.16b	v17, { v17 }, v2
	WORD $0x4e71ba31  // addv.8h	h17, v17
	WORD $0x1e260230  // fmov	w16, s17
	WORD $0x6e238cf1  // cmeq.16b	v17, v7, v3
	WORD $0x4e211e31  // and.16b	v17, v17, v1
	WORD $0x4e020231  // tbl.16b	v17, { v17 }, v2
	WORD $0x4e71ba31  // addv.8h	h17, v17
	WORD $0x1e260231  // fmov	w17, s17
	WORD $0x6e238cd1  // cmeq.16b	v17, v6, v3
	WORD $0x4e211e31  // and.16b	v17, v17, v1
	WORD $0x4e020231  // tbl.16b	v17, { v17 }, v2
	WORD $0x4e71ba31  // addv.8h	h17, v17
	WORD $0x1e260224  // fmov	w4, s17
	WORD $0x6e238cb1  // cmeq.16b	v17, v5, v3
	WORD $0x4e211e31  // and.16b	v17, v17, v1
	WORD $0x4e020231  // tbl.16b	v17, { v17 }, v2
	WORD $0x4e71ba31  // addv.8h	h17, v17
	WORD $0x1e260225  // fmov	w5, s17
	WORD $0xd3607dce  // lsl	x14, x14, #32
	WORD $0xaa0fc1ce  // orr	x14, x14, x15, lsl #48
	WORD $0x53103dad  // lsl	w13, w13, #16
	WORD $0xaa0d01cd  // orr	x13, x14, x13
	WORD $0xaa0c01ac  // orr	x12, x13, x12
	WORD $0xd3607c8d  // lsl	x13, x4, #32
	WORD $0xaa05c1ad  // orr	x13, x13, x5, lsl #48
	WORD $0x53103e2e  // lsl	w14, w17, #16
	WORD $0xaa0e01ad  // orr	x13, x13, x14
	WORD $0xaa1001ad  // orr	x13, x13, x16
	WORD $0xb500044d  // cbnz	x13, LBB0_130 $136(%rip)
	WORD $0xb50004c9  // cbnz	x9, LBB0_131 $152(%rip)
LBB0_127:
	WORD $0x6e303490  // cmhi.16b	v16, v4, v16
	WORD $0x4e211e10  // and.16b	v16, v16, v1
	WORD $0x4e020210  // tbl.16b	v16, { v16 }, v2
	WORD $0x4e71ba10  // addv.8h	h16, v16
	WORD $0x1e26020d  // fmov	w13, s16
	WORD $0x6e273487  // cmhi.16b	v7, v4, v7
	WORD $0x4e211ce7  // and.16b	v7, v7, v1
	WORD $0x4e0200e7  // tbl.16b	v7, { v7 }, v2
	WORD $0x4e71b8e7  // addv.8h	h7, v7
	WORD $0x1e2600ee  // fmov	w14, s7
	WORD $0x6e263486  // cmhi.16b	v6, v4, v6
	WORD $0x4e211cc6  // and.16b	v6, v6, v1
	WORD $0x4e0200c6  // tbl.16b	v6, { v6 }, v2
	WORD $0x4e71b8c6  // addv.8h	h6, v6
	WORD $0x1e2600cf  // fmov	w15, s6
	WORD $0x6e253485  // cmhi.16b	v5, v4, v5
	WORD $0x4e211ca5  // and.16b	v5, v5, v1
	WORD $0x4e0200a5  // tbl.16b	v5, { v5 }, v2
	WORD $0x4e71b8a5  // addv.8h	h5, v5
	WORD $0x1e2600b0  // fmov	w16, s5
	WORD $0xd3607def  // lsl	x15, x15, #32
	WORD $0xaa10c1ef  // orr	x15, x15, x16, lsl #48
	WORD $0x53103dce  // lsl	w14, w14, #16
	WORD $0xaa0e01ee  // orr	x14, x15, x14
	WORD $0xaa0d01cd  // orr	x13, x14, x13
	WORD $0xb50002ec  // cbnz	x12, LBB0_132 $92(%rip)
	WORD $0xb500cfcd  // cbnz	x13, LBB0_573 $6648(%rip)
	WORD $0xd101014a  // sub	x10, x10, #64
	WORD $0x9101016b  // add	x11, x11, #64
	WORD $0xf100fd5f  // cmp	x10, #63
	WORD $0x54fff568  // b.hi	LBB0_125 $-340(%rip)
	WORD $0x14000483  // b	LBB0_451 $4620(%rip)
LBB0_130:
	WORD $0xb100051f  // cmn	x8, #1
	WORD $0xdac001ae  // rbit	x14, x13
	WORD $0xdac011ce  // clz	x14, x14
	WORD $0x8b0b01ce  // add	x14, x14, x11
	WORD $0x9a8e1108  // csel	x8, x8, x14, ne
LBB0_131:
	WORD $0x8a2901ae  // bic	x14, x13, x9
	WORD $0xaa0e052f  // orr	x15, x9, x14, lsl #1
	WORD $0x8a2f01a9  // bic	x9, x13, x15
	WORD $0x9201f129  // and	x9, x9, #0xaaaaaaaaaaaaaaaa
	WORD $0xab0e012d  // adds	x13, x9, x14
	WORD $0x1a9f37e9  // cset	w9, hs
	WORD $0xd37ff9ad  // lsl	x13, x13, #1
	WORD $0xd200f1ad  // eor	x13, x13, #0x5555555555555555
	WORD $0x8a0f01ad  // and	x13, x13, x15
	WORD $0x8a2d018c  // bic	x12, x12, x13
	WORD $0x17ffffd1  // b	LBB0_127 $-188(%rip)
LBB0_132:
	WORD $0xdac00189  // rbit	x9, x12
	WORD $0xdac01129  // clz	x9, x9
	WORD $0xdac001aa  // rbit	x10, x13
	WORD $0xdac0114a  // clz	x10, x10
	WORD $0xeb09015f  // cmp	x10, x9
	WORD $0x5400cc63  // b.lo	LBB0_573 $6540(%rip)
	WORD $0x8b0b0129  // add	x9, x9, x11
	WORD $0x9100052c  // add	x12, x9, #1
	WORD $0xb7f8ccec  // tbnz	x12, #63, LBB0_575 $6556(%rip)
LBB0_134:
	WORD $0x528000e9  // mov	w9, #7
	WORD $0xf9000069  // str	x9, [x3]
	WORD $0xeb0c011f  // cmp	x8, x12
	WORD $0xda9fb108  // csinv	x8, x8, xzr, lt
	WORD $0xa9012062  // stp	x2, x8, [x3, #16]
	WORD $0xaa0c03e1  // mov	x1, x12
	WORD $0x17fffdc5  // b	LBB0_24 $-2284(%rip)
LBB0_135:
	WORD $0x7100b8bf  // cmp	w5, #46
	WORD $0x54000aa1  // b.ne	LBB0_161 $340(%rip)
	WORD $0x9100044f  // add	x15, x2, #1
	WORD $0x52800111  // mov	w17, #8
	WORD $0xf9000071  // str	x17, [x3]
	WORD $0xeb0101ff  // cmp	x15, x1
	WORD $0x54001742  // b.hs	LBB0_194 $744(%rip)
	WORD $0x8b020011  // add	x17, x0, x2
	WORD $0x39400631  // ldrb	w17, [x17, #1]
	WORD $0x5100ea31  // sub	w17, w17, #58
	WORD $0x31002e3f  // cmn	w17, #11
	WORD $0x54001328  // b.hi	LBB0_186 $612(%rip)
	WORD $0x92800028  // mov	x8, #-2
	WORD $0xf9000068  // str	x8, [x3]
	WORD $0xaa0f03e1  // mov	x1, x15
	WORD $0x17fffdb5  // b	LBB0_24 $-2348(%rip)
LBB0_139:
	WORD $0xaa0c0169  // orr	x9, x11, x12
	WORD $0xb7f80ae9  // tbnz	x9, #63, LBB0_168 $348(%rip)
	WORD $0xeb0c017f  // cmp	x11, x12
	WORD $0x54000aab  // b.lt	LBB0_168 $340(%rip)
	WORD $0xaa2b03ef  // mvn	x15, x11
LBB0_142:
	WORD $0xaa2f03e9  // mvn	x9, x15
	WORD $0x8b090108  // add	x8, x8, x9
LBB0_143:
	WORD $0xcb000101  // sub	x1, x8, x0
	WORD $0x9280002a  // mov	x10, #-2
LBB0_144:
	WORD $0xf900006a  // str	x10, [x3]
	WORD $0x17fffdaa  // b	LBB0_24 $-2392(%rip)
LBB0_145:
	WORD $0xaa0d03f0  // mov	x16, x13
	WORD $0x9280000f  // mov	x15, #-1
	WORD $0xb5ffcf8b  // cbnz	x11, LBB0_75 $-1552(%rip)
	WORD $0x17fffff7  // b	LBB0_142 $-36(%rip)
LBB0_146:
	WORD $0x12001d08  // and	w8, w8, #0xff
	WORD $0x7101b91f  // cmp	w8, #110
	WORD $0x540003c1  // b.ne	LBB0_154 $120(%rip)
	WORD $0x91000541  // add	x1, x10, #1
	WORD $0x38616808  // ldrb	w8, [x0, x1]
	WORD $0x7101d51f  // cmp	w8, #117
	WORD $0x540005c1  // b.ne	LBB0_160 $184(%rip)
	WORD $0x91000941  // add	x1, x10, #2
	WORD $0x38616808  // ldrb	w8, [x0, x1]
	WORD $0x7101b11f  // cmp	w8, #108
	WORD $0x54000541  // b.ne	LBB0_160 $168(%rip)
	WORD $0x91000d48  // add	x8, x10, #3
	WORD $0x38686809  // ldrb	w9, [x0, x8]
	WORD $0x9100114a  // add	x10, x10, #4
	WORD $0x7101b13f  // cmp	w9, #108
	WORD $0x14000024  // b	LBB0_159 $144(%rip)
LBB0_150:
	WORD $0x12001d08  // and	w8, w8, #0xff
	WORD $0x7101d11f  // cmp	w8, #116
	WORD $0x540001c1  // b.ne	LBB0_154 $56(%rip)
	WORD $0x91000541  // add	x1, x10, #1
	WORD $0x38616808  // ldrb	w8, [x0, x1]
	WORD $0x7101c91f  // cmp	w8, #114
	WORD $0x540003c1  // b.ne	LBB0_160 $120(%rip)
	WORD $0x91000941  // add	x1, x10, #2
	WORD $0x38616808  // ldrb	w8, [x0, x1]
	WORD $0x7101d51f  // cmp	w8, #117
	WORD $0x54000341  // b.ne	LBB0_160 $104(%rip)
	WORD $0x91000d48  // add	x8, x10, #3
	WORD $0x38686809  // ldrb	w9, [x0, x8]
	WORD $0x9100114a  // add	x10, x10, #4
	WORD $0x7101953f  // cmp	w9, #101
	WORD $0x14000014  // b	LBB0_159 $80(%rip)
LBB0_154:
	WORD $0x92800028  // mov	x8, #-2
	WORD $0xaa0a03e1  // mov	x1, x10
	WORD $0xf9000068  // str	x8, [x3]
	WORD $0x17fffd82  // b	LBB0_24 $-2552(%rip)
LBB0_155:
	WORD $0x12001d08  // and	w8, w8, #0xff
	WORD $0x7101851f  // cmp	w8, #97
	WORD $0x54000ac1  // b.ne	LBB0_184 $344(%rip)
	WORD $0x91000941  // add	x1, x10, #2
	WORD $0x38616808  // ldrb	w8, [x0, x1]
	WORD $0x7101b11f  // cmp	w8, #108
	WORD $0x54000141  // b.ne	LBB0_160 $40(%rip)
	WORD $0x91000d41  // add	x1, x10, #3
	WORD $0x38616808  // ldrb	w8, [x0, x1]
	WORD $0x7101cd1f  // cmp	w8, #115
	WORD $0x540000c1  // b.ne	LBB0_160 $24(%rip)
	WORD $0x91001148  // add	x8, x10, #4
	WORD $0x38686809  // ldrb	w9, [x0, x8]
	WORD $0x9100154a  // add	x10, x10, #5
	WORD $0x7101953f  // cmp	w9, #101
LBB0_159:
	WORD $0x9a880141  // csel	x1, x10, x8, eq
LBB0_160:
	WORD $0x92800028  // mov	x8, #-2
	WORD $0xf9000068  // str	x8, [x3]
	WORD $0x17fffd6f  // b	LBB0_24 $-2628(%rip)
LBB0_161:
	WORD $0x52800031  // mov	w17, #1
	WORD $0x7100021f  // cmp	w16, #0
	WORD $0x1a9fd7ef  // cset	w15, gt
	WORD $0xb500034c  // cbnz	x12, LBB0_171 $104(%rip)
LBB0_162:
	WORD $0x35000330  // cbnz	w16, LBB0_171 $100(%rip)
	WORD $0xeb01005f  // cmp	x2, x1
	WORD $0x54000282  // b.hs	LBB0_169 $80(%rip)
	WORD $0x52800010  // mov	w16, #0
	WORD $0x4b010044  // sub	w4, w2, w1
LBB0_165:
	WORD $0x3862680c  // ldrb	w12, [x0, x2]
	WORD $0x7100c19f  // cmp	w12, #48
	WORD $0x54000201  // b.ne	LBB0_170 $64(%rip)
	WORD $0x91000442  // add	x2, x2, #1
	WORD $0x51000610  // sub	w16, w16, #1
	WORD $0xeb02003f  // cmp	x1, x2
	WORD $0x54ffff41  // b.ne	LBB0_165 $-24(%rip)
	WORD $0xd280000c  // mov	x12, #0
	WORD $0x1400005e  // b	LBB0_196 $376(%rip)
LBB0_168:
	WORD $0xd37ffd29  // lsr	x9, x9, #63
	WORD $0x52000129  // eor	w9, w9, #0x1
	WORD $0xd100058e  // sub	x14, x12, #1
	WORD $0xeb0e017f  // cmp	x11, x14
	WORD $0x1a9f17eb  // cset	w11, eq
	WORD $0x6a0b013f  // tst	w9, w11
	WORD $0xda8c01af  // csinv	x15, x13, x12, eq
	WORD $0x17ffff04  // b	LBB0_119 $-1008(%rip)
LBB0_169:
	WORD $0x52800010  // mov	w16, #0
LBB0_170:
	WORD $0x52800004  // mov	w4, #0
	WORD $0xd280000c  // mov	x12, #0
LBB0_171:
	WORD $0xeb01005f  // cmp	x2, x1
	WORD $0x54000242  // b.hs	LBB0_177 $72(%rip)
	WORD $0x7100489f  // cmp	w4, #18
	WORD $0x5400020c  // b.gt	LBB0_177 $64(%rip)
	WORD $0x52800145  // mov	w5, #10
LBB0_174:
	WORD $0x38626806  // ldrb	w6, [x0, x2]
	WORD $0x5100c0c6  // sub	w6, w6, #48
	WORD $0x710024df  // cmp	w6, #9
	WORD $0x54000168  // b.hi	LBB0_177 $44(%rip)
	WORD $0x9b057d8c  // mul	x12, x12, x5
	WORD $0x8b26018c  // add	x12, x12, w6, uxtb
	WORD $0x51000610  // sub	w16, w16, #1
	WORD $0x91000442  // add	x2, x2, #1
	WORD $0xeb01005f  // cmp	x2, x1
	WORD $0x540000a2  // b.hs	LBB0_177 $20(%rip)
	WORD $0x11000486  // add	w6, w4, #1
	WORD $0x7100489f  // cmp	w4, #18
	WORD $0xaa0603e4  // mov	x4, x6
	WORD $0x54fffe6b  // b.lt	LBB0_174 $-52(%rip)
LBB0_177:
	WORD $0xeb01005f  // cmp	x2, x1
	WORD $0x54000782  // b.hs	LBB0_195 $240(%rip)
	WORD $0x38626804  // ldrb	w4, [x0, x2]
	WORD $0x5100c085  // sub	w5, w4, #48
	WORD $0x710024bf  // cmp	w5, #9
	WORD $0x54000408  // b.hi	LBB0_188 $128(%rip)
	WORD $0x91000442  // add	x2, x2, #1
	WORD $0xeb01005f  // cmp	x2, x1
	WORD $0x54000100  // b.eq	LBB0_182 $32(%rip)
LBB0_180:
	WORD $0x38626804  // ldrb	w4, [x0, x2]
	WORD $0x5100c08f  // sub	w15, w4, #48
	WORD $0x710025ff  // cmp	w15, #9
	WORD $0x54000308  // b.hi	LBB0_187 $96(%rip)
	WORD $0x91000442  // add	x2, x2, #1
	WORD $0xeb02003f  // cmp	x1, x2
	WORD $0x54ffff41  // b.ne	LBB0_180 $-24(%rip)
LBB0_182:
	WORD $0x5280002f  // mov	w15, #1
	WORD $0xaa1003e4  // mov	x4, x16
	WORD $0x1400002d  // b	LBB0_196 $180(%rip)
LBB0_183:
	WORD $0x91000441  // add	x1, x2, #1
	WORD $0x17fffd2a  // b	LBB0_24 $-2904(%rip)
LBB0_184:
	WORD $0x92800028  // mov	x8, #-2
	WORD $0xaa0203e1  // mov	x1, x2
	WORD $0xf9000068  // str	x8, [x3]
	WORD $0x17fffd26  // b	LBB0_24 $-2920(%rip)
LBB0_185:
	WORD $0x5ac000c9  // rbit	w9, w6
	WORD $0x5ac01129  // clz	w9, w9
	WORD $0xaa2f03eb  // mvn	x11, x15
	WORD $0xcb09016f  // sub	x15, x11, x9
	WORD $0x17fffed0  // b	LBB0_119 $-1216(%rip)
LBB0_186:
	WORD $0x52800011  // mov	w17, #0
	WORD $0xaa0f03e2  // mov	x2, x15
	WORD $0x7100021f  // cmp	w16, #0
	WORD $0x1a9fd7ef  // cset	w15, gt
	WORD $0xb4fff64c  // cbz	x12, LBB0_162 $-312(%rip)
	WORD $0x17ffffca  // b	LBB0_171 $-216(%rip)
LBB0_187:
	WORD $0x5280002f  // mov	w15, #1
LBB0_188:
	WORD $0x321b0084  // orr	w4, w4, #0x20
	WORD $0x7101949f  // cmp	w4, #101
	WORD $0x540002c1  // b.ne	LBB0_195 $88(%rip)
	WORD $0x91000451  // add	x17, x2, #1
	WORD $0x52800104  // mov	w4, #8
	WORD $0xf9000064  // str	x4, [x3]
	WORD $0xeb01023f  // cmp	x17, x1
	WORD $0x540001c2  // b.hs	LBB0_194 $56(%rip)
	WORD $0x38716804  // ldrb	w4, [x0, x17]
	WORD $0x7100b49f  // cmp	w4, #45
	WORD $0x54000060  // b.eq	LBB0_192 $12(%rip)
	WORD $0x7100ac9f  // cmp	w4, #43
	WORD $0x54002701  // b.ne	LBB0_263 $1248(%rip)
LBB0_192:
	WORD $0x91000851  // add	x17, x2, #2
	WORD $0xeb01023f  // cmp	x17, x1
	WORD $0x540000c2  // b.hs	LBB0_194 $24(%rip)
	WORD $0x7100ac9f  // cmp	w4, #43
	WORD $0x52800022  // mov	w2, #1
	WORD $0x5a820442  // cneg	w2, w2, ne
	WORD $0x38716804  // ldrb	w4, [x0, x17]
	WORD $0x14000131  // b	LBB0_264 $1220(%rip)
LBB0_194:
	WORD $0x92800008  // mov	x8, #-1
	WORD $0xf9000068  // str	x8, [x3]
	WORD $0x17fffd02  // b	LBB0_24 $-3064(%rip)
LBB0_195:
	WORD $0xaa1003e4  // mov	x4, x16
	WORD $0xaa0203e1  // mov	x1, x2
LBB0_196:
	WORD $0x34000151  // cbz	w17, LBB0_201 $40(%rip)
	WORD $0x350000e4  // cbnz	w4, LBB0_200 $28(%rip)
	WORD $0x93407dd0  // sxtw	x16, w14
	WORD $0xb6f80e6c  // tbz	x12, #63, LBB0_223 $460(%rip)
	WORD $0x8a100191  // and	x17, x12, x16
	WORD $0xd2f00000  // mov	x0, #-9223372036854775808
	WORD $0xeb00023f  // cmp	x17, x0
	WORD $0x54000de0  // b.eq	LBB0_223 $444(%rip)
LBB0_200:
	WORD $0x52800110  // mov	w16, #8
	WORD $0xf9000070  // str	x16, [x3]
LBB0_201:
	WORD $0xd374fd90  // lsr	x16, x12, #52
	WORD $0xb5000590  // cbnz	x16, LBB0_211 $176(%rip)
	WORD $0x9e630180  // ucvtf	d0, x12
	WORD $0x531f7dce  // lsr	w14, w14, #31
	WORD $0x9e660010  // fmov	x16, d0
	WORD $0xaa0efe0e  // orr	x14, x16, x14, lsl #63
	WORD $0x9e6701c0  // fmov	d0, x14
	WORD $0xb400284c  // cbz	x12, LBB0_278 $1288(%rip)
	WORD $0x34002824  // cbz	w4, LBB0_278 $1284(%rip)
	WORD $0x5100048e  // sub	w14, w4, #1
	WORD $0x710091df  // cmp	w14, #36
	WORD $0x54000348  // b.hi	LBB0_209 $104(%rip)
	WORD $0xaa0403ee  // mov	x14, x4
	WORD $0x71005c9f  // cmp	w4, #23
	WORD $0x540000e3  // b.lo	LBB0_207 $28(%rip)
	WORD $0x5100588e  // sub	w14, w4, #22
Lloh14:
	WORD $0x1000baf0  // adr	x16, _P10_TAB $5980(%rip)
Lloh15:
	WORD $0x91000210  // add	x16, x16, _P10_TAB@PAGEOFF $0(%rip)
	WORD $0xfc6e5a01  // ldr	d1, [x16, w14, uxtw #3]
	WORD $0x1e600820  // fmul	d0, d1, d0
	WORD $0x528002ce  // mov	w14, #22
LBB0_207:
	WORD $0xd2a4c690  // mov	x16, #640942080
	WORD $0xf2cd7eb0  // movk	x16, #27637, lsl #32
	WORD $0xf2e86190  // movk	x16, #17164, lsl #48
	WORD $0x9e670201  // fmov	d1, x16
	WORD $0x1e612000  // fcmp	d0, d1
	WORD $0xd2a4c690  // mov	x16, #640942080
	WORD $0xf2cd7eb0  // movk	x16, #27637, lsl #32
	WORD $0xf2f86190  // movk	x16, #49932, lsl #48
	WORD $0x9e670201  // fmov	d1, x16
	WORD $0x1e61d408  // fccmp	d0, d1, #8, le
	WORD $0x54000224  // b.mi	LBB0_212 $68(%rip)
Lloh16:
	WORD $0x1000b8e8  // adr	x8, _P10_TAB $5916(%rip)
Lloh17:
	WORD $0x91000108  // add	x8, x8, _P10_TAB@PAGEOFF $0(%rip)
	WORD $0xfc6e5901  // ldr	d1, [x8, w14, uxtw #3]
	WORD $0x1e610800  // fmul	d0, d0, d1
	WORD $0x14000125  // b	LBB0_278 $1172(%rip)
LBB0_209:
	WORD $0x3100589f  // cmn	w4, #22
	WORD $0x540000e3  // b.lo	LBB0_211 $28(%rip)
	WORD $0x4b0403e8  // neg	w8, w4
Lloh18:
	WORD $0x1000b7e9  // adr	x9, _P10_TAB $5884(%rip)
Lloh19:
	WORD $0x91000129  // add	x9, x9, _P10_TAB@PAGEOFF $0(%rip)
	WORD $0xfc685921  // ldr	d1, [x9, w8, uxtw #3]
	WORD $0x1e611800  // fdiv	d0, d0, d1
	WORD $0x1400011d  // b	LBB0_278 $1140(%rip)
LBB0_211:
	WORD $0x5105708e  // sub	w14, w4, #348
	WORD $0x310ae1df  // cmn	w14, #696
	WORD $0x54000663  // b.lo	LBB0_218 $204(%rip)
LBB0_212:
	WORD $0xdac01180  // clz	x0, x12
	WORD $0x9ac02186  // lsl	x6, x12, x0
	WORD $0x1105708e  // add	w14, w4, #348
Lloh20:
	WORD $0x1000bc50  // adr	x16, _POW10_M128_TAB $6024(%rip)
Lloh21:
	WORD $0x91000210  // add	x16, x16, _POW10_M128_TAB@PAGEOFF $0(%rip)
	WORD $0x8b2e520e  // add	x14, x16, w14, uxtw #4
	WORD $0xf94005d0  // ldr	x16, [x14, #8]
	WORD $0x9b067e11  // mul	x17, x16, x6
	WORD $0x9bc67e02  // umulh	x2, x16, x6
	WORD $0x92402045  // and	x5, x2, #0x1ff
	WORD $0xaa2603e7  // mvn	x7, x6
	WORD $0xeb07023f  // cmp	x17, x7
	WORD $0x540001c9  // b.ls	LBB0_216 $56(%rip)
	WORD $0xf107fcbf  // cmp	x5, #511
	WORD $0x54000181  // b.ne	LBB0_216 $48(%rip)
	WORD $0xf94001c5  // ldr	x5, [x14]
	WORD $0x9b067cb3  // mul	x19, x5, x6
	WORD $0x9bc67ca5  // umulh	x5, x5, x6
	WORD $0xab1100b1  // adds	x17, x5, x17
	WORD $0x9a823442  // cinc	x2, x2, hs
	WORD $0x92402045  // and	x5, x2, #0x1ff
	WORD $0xeb07027f  // cmp	x19, x7
	WORD $0xba418a20  // ccmn	x17, #1, #0, hi
	WORD $0x54000061  // b.ne	LBB0_216 $12(%rip)
	WORD $0xf107fcbf  // cmp	x5, #511
	WORD $0x54000320  // b.eq	LBB0_218 $100(%rip)
LBB0_216:
	WORD $0xd37ffc46  // lsr	x6, x2, #63
	WORD $0x910024c7  // add	x7, x6, #9
	WORD $0x9ac72442  // lsr	x2, x2, x7
	WORD $0xaa1100b1  // orr	x17, x5, x17
	WORD $0x92400445  // and	x5, x2, #0x3
	WORD $0xf100023f  // cmp	x17, #0
	WORD $0xfa4108a0  // ccmp	x5, #1, #0, eq
	WORD $0x54000220  // b.eq	LBB0_218 $68(%rip)
	WORD $0x528a4d51  // mov	w17, #21098
	WORD $0x72a00071  // movk	w17, #3, lsl #16
	WORD $0x1b117c91  // mul	w17, w4, w17
	WORD $0x13107e31  // asr	w17, w17, #16
	WORD $0x1110fe31  // add	w17, w17, #1087
	WORD $0x93407e31  // sxtw	x17, w17
	WORD $0xcb000220  // sub	x0, x17, x0
	WORD $0x8b0000c4  // add	x4, x6, x0
	WORD $0x92400040  // and	x0, x2, #0x1
	WORD $0x8b020000  // add	x0, x0, x2
	WORD $0xd376fc02  // lsr	x2, x0, #54
	WORD $0xf100005f  // cmp	x2, #0
	WORD $0x9a840484  // cinc	x4, x4, ne
	WORD $0xd1200085  // sub	x5, x4, #2048
	WORD $0xb11ff8bf  // cmn	x5, #2046
	WORD $0x54000fc2  // b.hs	LBB0_253 $504(%rip)
LBB0_218:
	WORD $0xcb0a002e  // sub	x14, x1, x10
	WORD $0xb4000569  // cbz	x9, LBB0_233 $172(%rip)
	WORD $0xf100213f  // cmp	x9, #8
	WORD $0x54000062  // b.hs	LBB0_221 $12(%rip)
	WORD $0xd280000a  // mov	x10, #0
	WORD $0x14000022  // b	LBB0_231 $136(%rip)
LBB0_221:
	WORD $0xf101013f  // cmp	x9, #64
	WORD $0x54000142  // b.hs	LBB0_224 $40(%rip)
	WORD $0xd280000a  // mov	x10, #0
	WORD $0x14000014  // b	LBB0_228 $80(%rip)
LBB0_223:
	WORD $0x9e630180  // ucvtf	d0, x12
	WORD $0x9b107d88  // mul	x8, x12, x16
	WORD $0x92410209  // and	x9, x16, #0x8000000000000000
	WORD $0x9e66000a  // fmov	x10, d0
	WORD $0xaa0a0129  // orr	x9, x9, x10
	WORD $0xa900a069  // stp	x9, x8, [x3, #8]
	WORD $0x17fffc83  // b	LBB0_24 $-3572(%rip)
LBB0_224:
	WORD $0x927ae52a  // and	x10, x9, #0xffffffffffffffc0
	WORD $0x9100810b  // add	x11, x8, #32
	WORD $0x6f00e400  // movi.2d	v0, #0000000000000000
	WORD $0xaa0a03ec  // mov	x12, x10
LBB0_225:
	WORD $0xad3f0160  // stp	q0, q0, [x11, #-32]
	WORD $0xac820160  // stp	q0, q0, [x11], #64
	WORD $0xf101018c  // subs	x12, x12, #64
	WORD $0x54ffffa1  // b.ne	LBB0_225 $-12(%rip)
	WORD $0xeb0a013f  // cmp	x9, x10
	WORD $0x54000240  // b.eq	LBB0_233 $72(%rip)
	WORD $0xf27d093f  // tst	x9, #0x38
	WORD $0x54000160  // b.eq	LBB0_231 $44(%rip)
LBB0_228:
	WORD $0xaa0a03ec  // mov	x12, x10
	WORD $0x927df12a  // and	x10, x9, #0xfffffffffffffff8
	WORD $0x8b0c010b  // add	x11, x8, x12
	WORD $0xcb0a018c  // sub	x12, x12, x10
	WORD $0x6f00e400  // movi.2d	v0, #0000000000000000
LBB0_229:
	WORD $0xfc008560  // str	d0, [x11], #8
	WORD $0xb100218c  // adds	x12, x12, #8
	WORD $0x54ffffc1  // b.ne	LBB0_229 $-8(%rip)
	WORD $0xeb0a013f  // cmp	x9, x10
	WORD $0x540000c0  // b.eq	LBB0_233 $24(%rip)
LBB0_231:
	WORD $0x8b0a010b  // add	x11, x8, x10
	WORD $0xcb0a012a  // sub	x10, x9, x10
LBB0_232:
	WORD $0x3800157f  // strb	wzr, [x11], #1
	WORD $0xf100054a  // subs	x10, x10, #1
	WORD $0x54ffffc1  // b.ne	LBB0_232 $-8(%rip)
LBB0_233:
	WORD $0x394001aa  // ldrb	w10, [x13]
	WORD $0x7100b55f  // cmp	w10, #45
	WORD $0x1a9f17ef  // cset	w15, eq
	WORD $0xeb0f01df  // cmp	x14, x15
	WORD $0x5400160d  // b.le	LBB0_276 $704(%rip)
	WORD $0x5280000c  // mov	w12, #0
	WORD $0x5280000b  // mov	w11, #0
	WORD $0x52800007  // mov	w7, #0
	WORD $0x52800010  // mov	w16, #0
	WORD $0x14000006  // b	LBB0_237 $24(%rip)
LBB0_235:
	WORD $0x38206911  // strb	w17, [x8, x0]
	WORD $0x110004e7  // add	w7, w7, #1
LBB0_236:
	WORD $0x910005ef  // add	x15, x15, #1
	WORD $0xeb0f01df  // cmp	x14, x15
	WORD $0x5400032d  // b.le	LBB0_246 $100(%rip)
LBB0_237:
	WORD $0x386f69b1  // ldrb	w17, [x13, x15]
	WORD $0x5100c220  // sub	w0, w17, #48
	WORD $0x7100241f  // cmp	w0, #9
	WORD $0x54000108  // b.hi	LBB0_241 $32(%rip)
	WORD $0x7100c23f  // cmp	w17, #48
	WORD $0x54000161  // b.ne	LBB0_243 $44(%rip)
	WORD $0x34000207  // cbz	w7, LBB0_245 $64(%rip)
	WORD $0x93407ce0  // sxtw	x0, w7
	WORD $0xeb00013f  // cmp	x9, x0
	WORD $0x54fffe48  // b.hi	LBB0_235 $-56(%rip)
	WORD $0x17fffff3  // b	LBB0_236 $-52(%rip)
LBB0_241:
	WORD $0x7100ba3f  // cmp	w17, #46
	WORD $0x540001e1  // b.ne	LBB0_247 $60(%rip)
	WORD $0x5280002c  // mov	w12, #1
	WORD $0xaa0703f0  // mov	x16, x7
	WORD $0x17ffffee  // b	LBB0_236 $-72(%rip)
LBB0_243:
	WORD $0xaa0703e0  // mov	x0, x7
	WORD $0x93407ce0  // sxtw	x0, w7
	WORD $0xeb00013f  // cmp	x9, x0
	WORD $0x54fffd08  // b.hi	LBB0_235 $-96(%rip)
	WORD $0x5280002b  // mov	w11, #1
	WORD $0x17ffffe8  // b	LBB0_236 $-96(%rip)
LBB0_245:
	WORD $0x51000610  // sub	w16, w16, #1
	WORD $0x17ffffe6  // b	LBB0_236 $-104(%rip)
LBB0_246:
	WORD $0x7100019f  // cmp	w12, #0
	WORD $0x1a9000ec  // csel	w12, w7, w16, eq
	WORD $0x14000083  // b	LBB0_272 $524(%rip)
LBB0_247:
	WORD $0x7100019f  // cmp	w12, #0
	WORD $0x1a9000ec  // csel	w12, w7, w16, eq
	WORD $0x386f69b0  // ldrb	w16, [x13, x15]
	WORD $0x321b0210  // orr	w16, w16, #0x20
	WORD $0x7101961f  // cmp	w16, #101
	WORD $0x54000fa1  // b.ne	LBB0_272 $500(%rip)
	WORD $0x910005f0  // add	x16, x15, #1
	WORD $0x387049b1  // ldrb	w17, [x13, w16, uxtw]
	WORD $0x110009e0  // add	w0, w15, #2
	WORD $0x52800022  // mov	w2, #1
	WORD $0x110009ef  // add	w15, w15, #2
	WORD $0x12800004  // mov	w4, #-1
	WORD $0x52800025  // mov	w5, #1
	WORD $0x7100b63f  // cmp	w17, #45
	WORD $0x1a9001ef  // csel	w15, w15, w16, eq
	WORD $0x1a850090  // csel	w16, w4, w5, eq
	WORD $0x7100ae3f  // cmp	w17, #43
	WORD $0x1a8f0011  // csel	w17, w0, w15, eq
	WORD $0x1a90004f  // csel	w15, w2, w16, eq
	WORD $0x93407e31  // sxtw	x17, w17
	WORD $0xeb1101df  // cmp	x14, x17
	WORD $0x54000d6d  // b.le	LBB0_270 $428(%rip)
	WORD $0x52800010  // mov	w16, #0
	WORD $0x5284e1e0  // mov	w0, #9999
	WORD $0x52800142  // mov	w2, #10
LBB0_250:
	WORD $0x38f169a4  // ldrsb	w4, [x13, x17]
	WORD $0x7100c09f  // cmp	w4, #48
	WORD $0x54000ccb  // b.lt	LBB0_271 $408(%rip)
	WORD $0x12001c84  // and	w4, w4, #0xff
	WORD $0x7100e49f  // cmp	w4, #57
	WORD $0x7a409200  // ccmp	w16, w0, #0, ls
	WORD $0x54000c4c  // b.gt	LBB0_271 $392(%rip)
	WORD $0x1b027e10  // mul	w16, w16, w2
	WORD $0x5100c084  // sub	w4, w4, #48
	WORD $0x0b240210  // add	w16, w16, w4, uxtb
	WORD $0x91000631  // add	x17, x17, #1
	WORD $0xeb1101df  // cmp	x14, x17
	WORD $0x54fffe8c  // b.gt	LBB0_250 $-48(%rip)
	WORD $0x1400005b  // b	LBB0_271 $364(%rip)
LBB0_253:
	WORD $0xd1000484  // sub	x4, x4, #1
	WORD $0xf100005f  // cmp	x2, #0
	WORD $0x52800022  // mov	w2, #1
	WORD $0x9a820442  // cinc	x2, x2, ne
	WORD $0x9ac22400  // lsr	x0, x0, x2
	WORD $0xb34c2c80  // bfi	x0, x4, #52, #12
	WORD $0xb2410002  // orr	x2, x0, #0x8000000000000000
	WORD $0x7100b57f  // cmp	w11, #45
	WORD $0x9a800040  // csel	x0, x2, x0, eq
	WORD $0x9e670000  // fmov	d0, x0
	WORD $0x34000c0f  // cbz	w15, LBB0_278 $384(%rip)
	WORD $0x9100058f  // add	x15, x12, #1
	WORD $0xdac011ec  // clz	x12, x15
	WORD $0x9acc21e2  // lsl	x2, x15, x12
	WORD $0x9b027e0f  // mul	x15, x16, x2
	WORD $0x9bc27e10  // umulh	x16, x16, x2
	WORD $0x92402200  // and	x0, x16, #0x1ff
	WORD $0xaa2203e4  // mvn	x4, x2
	WORD $0xeb0401ff  // cmp	x15, x4
	WORD $0x540001e9  // b.ls	LBB0_259 $60(%rip)
	WORD $0xf107fc1f  // cmp	x0, #511
	WORD $0x540001a1  // b.ne	LBB0_259 $52(%rip)
	WORD $0xf94001ce  // ldr	x14, [x14]
	WORD $0x9b027dc5  // mul	x5, x14, x2
	WORD $0x9bc27dce  // umulh	x14, x14, x2
	WORD $0xab0f01cf  // adds	x15, x14, x15
	WORD $0x9a903610  // cinc	x16, x16, hs
	WORD $0x92402200  // and	x0, x16, #0x1ff
	WORD $0xeb0400bf  // cmp	x5, x4
	WORD $0x540000a9  // b.ls	LBB0_259 $20(%rip)
	WORD $0xb10005ff  // cmn	x15, #1
	WORD $0x54000061  // b.ne	LBB0_259 $12(%rip)
	WORD $0xf107fc1f  // cmp	x0, #511
	WORD $0x54ffec40  // b.eq	LBB0_218 $-632(%rip)
LBB0_259:
	WORD $0xd37ffe0e  // lsr	x14, x16, #63
	WORD $0x910025c2  // add	x2, x14, #9
	WORD $0x9ac22610  // lsr	x16, x16, x2
	WORD $0xaa0f000f  // orr	x15, x0, x15
	WORD $0xb500008f  // cbnz	x15, LBB0_261 $16(%rip)
	WORD $0x9240060f  // and	x15, x16, #0x3
	WORD $0xf10005ff  // cmp	x15, #1
	WORD $0x54ffeb40  // b.eq	LBB0_218 $-664(%rip)
LBB0_261:
	WORD $0xcb0c022c  // sub	x12, x17, x12
	WORD $0x8b0c01cf  // add	x15, x14, x12
	WORD $0x9240020c  // and	x12, x16, #0x1
	WORD $0x8b10018c  // add	x12, x12, x16
	WORD $0xd376fd8e  // lsr	x14, x12, #54
	WORD $0xf10001df  // cmp	x14, #0
	WORD $0x9a8f05ef  // cinc	x15, x15, ne
	WORD $0xd12001f0  // sub	x16, x15, #2048
	WORD $0xb11ffa1f  // cmn	x16, #2046
	WORD $0x54ffea03  // b.lo	LBB0_218 $-704(%rip)
	WORD $0xd10005ef  // sub	x15, x15, #1
	WORD $0xf10001df  // cmp	x14, #0
	WORD $0x5280002e  // mov	w14, #1
	WORD $0x9a8e05ce  // cinc	x14, x14, ne
	WORD $0x9ace258c  // lsr	x12, x12, x14
	WORD $0xb34c2dec  // bfi	x12, x15, #52, #12
	WORD $0xb241018e  // orr	x14, x12, #0x8000000000000000
	WORD $0x7100b57f  // cmp	w11, #45
	WORD $0x9a8c01cb  // csel	x11, x14, x12, eq
	WORD $0x9e670161  // fmov	d1, x11
	WORD $0x1e602020  // fcmp	d1, d0
	WORD $0x54000560  // b.eq	LBB0_278 $172(%rip)
	WORD $0x17ffff43  // b	LBB0_218 $-756(%rip)
LBB0_263:
	WORD $0x52800022  // mov	w2, #1
LBB0_264:
	WORD $0x5100e884  // sub	w4, w4, #58
	WORD $0x3100289f  // cmn	w4, #10
	WORD $0x540000a2  // b.hs	LBB0_266 $20(%rip)
	WORD $0x92800028  // mov	x8, #-2
	WORD $0xf9000068  // str	x8, [x3]
	WORD $0xaa1103e1  // mov	x1, x17
	WORD $0x17fffbce  // b	LBB0_24 $-4296(%rip)
LBB0_266:
	WORD $0x52800004  // mov	w4, #0
	WORD $0x52800145  // mov	w5, #10
	WORD $0x5284e206  // mov	w6, #10000
LBB0_267:
	WORD $0x38716807  // ldrb	w7, [x0, x17]
	WORD $0x5100c0e7  // sub	w7, w7, #48
	WORD $0x710024ff  // cmp	w7, #9
	WORD $0x54001d28  // b.hi	LBB0_339 $932(%rip)
	WORD $0x1b057c93  // mul	w19, w4, w5
	WORD $0x0b270267  // add	w7, w19, w7, uxtb
	WORD $0x6b06009f  // cmp	w4, w6
	WORD $0x1a84b0e4  // csel	w4, w7, w4, lt
	WORD $0x91000631  // add	x17, x17, #1
	WORD $0xeb11003f  // cmp	x1, x17
	WORD $0x54fffec1  // b.ne	LBB0_267 $-40(%rip)
	WORD $0x1b024084  // madd	w4, w4, w2, w16
	WORD $0x17fffec9  // b	LBB0_201 $-1244(%rip)
LBB0_270:
	WORD $0x52800010  // mov	w16, #0
LBB0_271:
	WORD $0x1b0f320c  // madd	w12, w16, w15, w12
LBB0_272:
	WORD $0x34000107  // cbz	w7, LBB0_276 $32(%rip)
	WORD $0x7104d99f  // cmp	w12, #310
	WORD $0x5400008d  // b.le	LBB0_275 $16(%rip)
LBB0_274:
	WORD $0xd2800008  // mov	x8, #0
	WORD $0xd2effe09  // mov	x9, #9218868437227405312
	WORD $0x14000005  // b	LBB0_277 $20(%rip)
LBB0_275:
	WORD $0x3105299f  // cmn	w12, #330
	WORD $0x5400022a  // b.ge	LBB0_281 $68(%rip)
LBB0_276:
	WORD $0xd2800009  // mov	x9, #0
	WORD $0xd2800008  // mov	x8, #0
LBB0_277:
	WORD $0xaa090108  // orr	x8, x8, x9
	WORD $0xb2410109  // orr	x9, x8, #0x8000000000000000
	WORD $0x7100b55f  // cmp	w10, #45
	WORD $0x9a880128  // csel	x8, x9, x8, eq
	WORD $0x9e670100  // fmov	d0, x8
LBB0_278:
	WORD $0x9e660008  // fmov	x8, d0
	WORD $0x9240f908  // and	x8, x8, #0x7fffffffffffffff
	WORD $0xd2effe09  // mov	x9, #9218868437227405312
	WORD $0xeb09011f  // cmp	x8, x9
	WORD $0x54000061  // b.ne	LBB0_280 $12(%rip)
	WORD $0x928000e8  // mov	x8, #-8
	WORD $0xf9000068  // str	x8, [x3]
LBB0_280:
	WORD $0xfd000460  // str	d0, [x3, #8]
	WORD $0x17fffba4  // b	LBB0_24 $-4464(%rip)
LBB0_281:
	WORD $0xb201e7ed  // mov	x13, #-7378697629483820647
	WORD $0xf293334d  // movk	x13, #39322
	WORD $0xf2e0332d  // movk	x13, #409, lsl #48
Lloh22:
	WORD $0x1001f490  // adr	x16, _POW_TAB $16016(%rip)
Lloh23:
	WORD $0x91000210  // add	x16, x16, _POW_TAB@PAGEOFF $0(%rip)
	WORD $0x7100059f  // cmp	w12, #1
	WORD $0x5400184b  // b.lt	LBB0_340 $776(%rip)
	WORD $0x5280000f  // mov	w15, #0
	WORD $0xd100050e  // sub	x14, x8, #1
	WORD $0x92800011  // mov	x17, #-1
	WORD $0x52800140  // mov	w0, #10
	WORD $0x1400000e  // b	LBB0_288 $56(%rip)
LBB0_283:
	WORD $0xaa2f03e9  // mvn	x9, x15
	WORD $0xcb25412f  // sub	x15, x9, w5, uxtw
	WORD $0x17fffd44  // b	LBB0_119 $-2800(%rip)
LBB0_284:
	WORD $0xaa2f03e9  // mvn	x9, x15
	WORD $0xcb24412f  // sub	x15, x9, w4, uxtw
	WORD $0x17fffd41  // b	LBB0_119 $-2812(%rip)
LBB0_285:
	WORD $0xaa2f03e9  // mvn	x9, x15
	WORD $0xcb22412f  // sub	x15, x9, w2, uxtw
	WORD $0x17fffd3e  // b	LBB0_119 $-2824(%rip)
LBB0_286:
	WORD $0x340015a7  // cbz	w7, LBB0_338 $692(%rip)
LBB0_287:
	WORD $0x0b0f004f  // add	w15, w2, w15
	WORD $0x7100019f  // cmp	w12, #0
	WORD $0x5400162d  // b.le	LBB0_341 $708(%rip)
LBB0_288:
	WORD $0x7100219f  // cmp	w12, #8
	WORD $0x540000a9  // b.ls	LBB0_291 $20(%rip)
	WORD $0x52800362  // mov	w2, #27
	WORD $0x34ffff47  // cbz	w7, LBB0_287 $-24(%rip)
	WORD $0x12800346  // mov	w6, #-27
	WORD $0x14000006  // b	LBB0_293 $24(%rip)
LBB0_291:
	WORD $0xb86c5a02  // ldr	w2, [x16, w12, uxtw #2]
	WORD $0x34fffec7  // cbz	w7, LBB0_287 $-40(%rip)
	WORD $0x4b0203e6  // neg	w6, w2
	WORD $0x3100f4df  // cmn	w6, #61
	WORD $0x54000469  // b.ls	LBB0_302 $140(%rip)
LBB0_293:
	WORD $0xd2800013  // mov	x19, #0
	WORD $0xd2800005  // mov	x5, #0
	WORD $0x4b0603e4  // neg	w4, w6
	WORD $0x0aa77ce6  // bic	w6, w7, w7, asr #31
LBB0_294:
	WORD $0xeb1300df  // cmp	x6, x19
	WORD $0x54000c80  // b.eq	LBB0_321 $400(%rip)
	WORD $0x38b36914  // ldrsb	x20, [x8, x19]
	WORD $0x9b0050a5  // madd	x5, x5, x0, x20
	WORD $0xd100c0a5  // sub	x5, x5, #48
	WORD $0x91000673  // add	x19, x19, #1
	WORD $0x9ac424b4  // lsr	x20, x5, x4
	WORD $0xb4ffff34  // cbz	x20, LBB0_294 $-28(%rip)
LBB0_297:
	WORD $0x9ac42226  // lsl	x6, x17, x4
	WORD $0xaa2603e6  // mvn	x6, x6
	WORD $0x6b1300e7  // subs	w7, w7, w19
	WORD $0x54000c6d  // b.le	LBB0_324 $396(%rip)
	WORD $0xaa0703f4  // mov	x20, x7
	WORD $0xaa0803f5  // mov	x21, x8
LBB0_299:
	WORD $0x9ac424b6  // lsr	x22, x5, x4
	WORD $0x8a0600a5  // and	x5, x5, x6
	WORD $0x1100c2d6  // add	w22, w22, #48
	WORD $0x390002b6  // strb	w22, [x21]
	WORD $0x38b3cab6  // ldrsb	x22, [x21, w19, sxtw]
	WORD $0x9b0058a5  // madd	x5, x5, x0, x22
	WORD $0xd100c0a5  // sub	x5, x5, #48
	WORD $0x910006b5  // add	x21, x21, #1
	WORD $0xf1000694  // subs	x20, x20, #1
	WORD $0x54fffee1  // b.ne	LBB0_299 $-36(%rip)
	WORD $0x14000057  // b	LBB0_325 $348(%rip)
LBB0_300:
	WORD $0x710000ff  // cmp	w7, #0
	WORD $0x1a8c03ec  // csel	w12, wzr, w12, eq
LBB0_301:
	WORD $0x1100f086  // add	w6, w4, #60
	WORD $0x3101e09f  // cmn	w4, #120
	WORD $0x54fffbea  // b.ge	LBB0_293 $-132(%rip)
LBB0_302:
	WORD $0xd2800014  // mov	x20, #0
	WORD $0xd2800013  // mov	x19, #0
	WORD $0xaa0603e4  // mov	x4, x6
	WORD $0x0aa77ce6  // bic	w6, w7, w7, asr #31
	WORD $0xaa0603e5  // mov	x5, x6
LBB0_303:
	WORD $0xeb1400df  // cmp	x6, x20
	WORD $0x54000140  // b.eq	LBB0_306 $40(%rip)
	WORD $0x38b46915  // ldrsb	x21, [x8, x20]
	WORD $0x9b005673  // madd	x19, x19, x0, x21
	WORD $0xd100c273  // sub	x19, x19, #48
	WORD $0x91000694  // add	x20, x20, #1
	WORD $0xd37cfe75  // lsr	x21, x19, #60
	WORD $0xb4ffff35  // cbz	x21, LBB0_303 $-28(%rip)
	WORD $0xaa1303e6  // mov	x6, x19
	WORD $0xaa1403e5  // mov	x5, x20
	WORD $0x14000008  // b	LBB0_308 $32(%rip)
LBB0_306:
	WORD $0xb40006b3  // cbz	x19, LBB0_320 $212(%rip)
LBB0_307:
	WORD $0x8b130a66  // add	x6, x19, x19, lsl #2
	WORD $0xd37ff8c6  // lsl	x6, x6, #1
	WORD $0x110004a5  // add	w5, w5, #1
	WORD $0xeb0d027f  // cmp	x19, x13
	WORD $0xaa0603f3  // mov	x19, x6
	WORD $0x54ffff63  // b.lo	LBB0_307 $-20(%rip)
LBB0_308:
	WORD $0x6b0500e7  // subs	w7, w7, w5
	WORD $0x540001cd  // b.le	LBB0_311 $56(%rip)
	WORD $0xaa0703f3  // mov	x19, x7
	WORD $0xaa0803f4  // mov	x20, x8
LBB0_310:
	WORD $0xd37cfcd5  // lsr	x21, x6, #60
	WORD $0x9240ecc6  // and	x6, x6, #0xfffffffffffffff
	WORD $0x321c06b5  // orr	w21, w21, #0x30
	WORD $0x39000295  // strb	w21, [x20]
	WORD $0x38a5ca95  // ldrsb	x21, [x20, w5, sxtw]
	WORD $0x9b0054c6  // madd	x6, x6, x0, x21
	WORD $0xd100c0c6  // sub	x6, x6, #48
	WORD $0x91000694  // add	x20, x20, #1
	WORD $0xf1000673  // subs	x19, x19, #1
	WORD $0x54fffee1  // b.ne	LBB0_310 $-36(%rip)
	WORD $0x14000002  // b	LBB0_312 $8(%rip)
LBB0_311:
	WORD $0x52800007  // mov	w7, #0
LBB0_312:
	WORD $0xb5000126  // cbnz	x6, LBB0_314 $36(%rip)
	WORD $0x14000013  // b	LBB0_316 $76(%rip)
LBB0_313:
	WORD $0xd37cfcd3  // lsr	x19, x6, #60
	WORD $0xf100027f  // cmp	x19, #0
	WORD $0x1a9f056b  // csinc	w11, w11, wzr, eq
	WORD $0x9240ecd3  // and	x19, x6, #0xfffffffffffffff
	WORD $0x8b130a66  // add	x6, x19, x19, lsl #2
	WORD $0xd37ff8c6  // lsl	x6, x6, #1
	WORD $0xb4000193  // cbz	x19, LBB0_316 $48(%rip)
LBB0_314:
	WORD $0x93407cf3  // sxtw	x19, w7
	WORD $0xeb13013f  // cmp	x9, x19
	WORD $0x54fffee9  // b.ls	LBB0_313 $-36(%rip)
	WORD $0xd37cfcd4  // lsr	x20, x6, #60
	WORD $0x321c0694  // orr	w20, w20, #0x30
	WORD $0x38336914  // strb	w20, [x8, x19]
	WORD $0x110004e7  // add	w7, w7, #1
	WORD $0x9240ecd3  // and	x19, x6, #0xfffffffffffffff
	WORD $0x8b130a66  // add	x6, x19, x19, lsl #2
	WORD $0xd37ff8c6  // lsl	x6, x6, #1
	WORD $0xb5fffed3  // cbnz	x19, LBB0_314 $-40(%rip)
LBB0_316:
	WORD $0x4b05018c  // sub	w12, w12, w5
	WORD $0x1100058c  // add	w12, w12, #1
	WORD $0x710004ff  // cmp	w7, #1
	WORD $0x54fff7ab  // b.lt	LBB0_300 $-268(%rip)
LBB0_317:
	WORD $0x386749c5  // ldrb	w5, [x14, w7, uxtw]
	WORD $0x7100c0bf  // cmp	w5, #48
	WORD $0x54fff781  // b.ne	LBB0_301 $-272(%rip)
	WORD $0x710004e7  // subs	w7, w7, #1
	WORD $0x54ffff8c  // b.gt	LBB0_317 $-16(%rip)
	WORD $0x5280000c  // mov	w12, #0
LBB0_320:
	WORD $0x52800007  // mov	w7, #0
	WORD $0x17ffffb7  // b	LBB0_301 $-292(%rip)
LBB0_321:
	WORD $0xb4000565  // cbz	x5, LBB0_334 $172(%rip)
	WORD $0x9ac424b3  // lsr	x19, x5, x4
	WORD $0xb4000573  // cbz	x19, LBB0_335 $172(%rip)
	WORD $0x52800007  // mov	w7, #0
	WORD $0x4b06018c  // sub	w12, w12, w6
	WORD $0x1100058c  // add	w12, w12, #1
	WORD $0x9ac42226  // lsl	x6, x17, x4
	WORD $0xaa2603e6  // mvn	x6, x6
	WORD $0x14000005  // b	LBB0_326 $20(%rip)
LBB0_324:
	WORD $0x52800007  // mov	w7, #0
LBB0_325:
	WORD $0x4b13018c  // sub	w12, w12, w19
	WORD $0x1100058c  // add	w12, w12, #1
	WORD $0xb40002a5  // cbz	x5, LBB0_330 $84(%rip)
LBB0_326:
	WORD $0xaa0703f3  // mov	x19, x7
	WORD $0x14000007  // b	LBB0_328 $28(%rip)
LBB0_327:
	WORD $0xf10000ff  // cmp	x7, #0
	WORD $0x1a9f056b  // csinc	w11, w11, wzr, eq
	WORD $0x8a0600a7  // and	x7, x5, x6
	WORD $0x8b0708e5  // add	x5, x7, x7, lsl #2
	WORD $0xd37ff8a5  // lsl	x5, x5, #1
	WORD $0xb40001c7  // cbz	x7, LBB0_331 $56(%rip)
LBB0_328:
	WORD $0x9ac424a7  // lsr	x7, x5, x4
	WORD $0x93407e74  // sxtw	x20, w19
	WORD $0xeb14013f  // cmp	x9, x20
	WORD $0x54fffee9  // b.ls	LBB0_327 $-36(%rip)
	WORD $0x1100c0e7  // add	w7, w7, #48
	WORD $0x38346907  // strb	w7, [x8, x20]
	WORD $0x11000673  // add	w19, w19, #1
	WORD $0x8a0600a7  // and	x7, x5, x6
	WORD $0x8b0708e5  // add	x5, x7, x7, lsl #2
	WORD $0xd37ff8a5  // lsl	x5, x5, #1
	WORD $0xb5fffec7  // cbnz	x7, LBB0_328 $-40(%rip)
	WORD $0x14000002  // b	LBB0_331 $8(%rip)
LBB0_330:
	WORD $0xaa0703f3  // mov	x19, x7
LBB0_331:
	WORD $0xaa1303e7  // mov	x7, x19
	WORD $0x7100067f  // cmp	w19, #1
	WORD $0x54ffec8b  // b.lt	LBB0_286 $-624(%rip)
LBB0_332:
	WORD $0x386749c4  // ldrb	w4, [x14, w7, uxtw]
	WORD $0x7100c09f  // cmp	w4, #48
	WORD $0x54ffec41  // b.ne	LBB0_287 $-632(%rip)
	WORD $0x710004e7  // subs	w7, w7, #1
	WORD $0x54ffff8c  // b.gt	LBB0_332 $-16(%rip)
	WORD $0x1400000a  // b	LBB0_337 $40(%rip)
LBB0_334:
	WORD $0x52800007  // mov	w7, #0
	WORD $0x17ffff5d  // b	LBB0_287 $-652(%rip)
LBB0_335:
	WORD $0xaa0603f3  // mov	x19, x6
LBB0_336:
	WORD $0x8b0508a5  // add	x5, x5, x5, lsl #2
	WORD $0xd37ff8a5  // lsl	x5, x5, #1
	WORD $0x11000673  // add	w19, w19, #1
	WORD $0x9ac424a6  // lsr	x6, x5, x4
	WORD $0xb4ffff86  // cbz	x6, LBB0_336 $-16(%rip)
	WORD $0x17ffff70  // b	LBB0_297 $-576(%rip)
LBB0_337:
	WORD $0x52800007  // mov	w7, #0
LBB0_338:
	WORD $0x5280000c  // mov	w12, #0
	WORD $0x0b0f004f  // add	w15, w2, w15
	WORD $0x14000005  // b	LBB0_341 $20(%rip)
LBB0_339:
	WORD $0xaa1103e1  // mov	x1, x17
	WORD $0x1b024084  // madd	w4, w4, w2, w16
	WORD $0x17fffde7  // b	LBB0_201 $-2148(%rip)
LBB0_340:
	WORD $0x5280000f  // mov	w15, #0
LBB0_341:
	WORD $0xd100050e  // sub	x14, x8, #1
	WORD $0x52800d00  // mov	w0, #104
	WORD $0xb202e7e2  // mov	x2, #-3689348814741910324
	WORD $0xf29999a2  // movk	x2, #52429
	WORD $0x92800124  // mov	x4, #-10
	WORD $0x52800145  // mov	w5, #10
	WORD $0x92800006  // mov	x6, #-1
	WORD $0xaa0703f3  // mov	x19, x7
Lloh24:
	WORD $0x1001dbf1  // adr	x17, _LSHIFT_TAB $15228(%rip)
Lloh25:
	WORD $0x91000231  // add	x17, x17, _LSHIFT_TAB@PAGEOFF $0(%rip)
	WORD $0x14000004  // b	LBB0_344 $16(%rip)
LBB0_342:
	WORD $0x7100027f  // cmp	w19, #0
	WORD $0x1a8c03ec  // csel	w12, wzr, w12, eq
LBB0_343:
	WORD $0x4b0701ef  // sub	w15, w15, w7
LBB0_344:
	WORD $0x37f800cc  // tbnz	w12, #31, LBB0_347 $24(%rip)
	WORD $0x350021cc  // cbnz	w12, LBB0_422 $1080(%rip)
	WORD $0x39c00107  // ldrsb	w7, [x8]
	WORD $0x7100d4ff  // cmp	w7, #53
	WORD $0x540000eb  // b.lt	LBB0_349 $28(%rip)
	WORD $0x1400010a  // b	LBB0_422 $1064(%rip)
LBB0_347:
	WORD $0x3100219f  // cmn	w12, #8
	WORD $0x54000082  // b.hs	LBB0_349 $16(%rip)
	WORD $0x52800367  // mov	w7, #27
	WORD $0x350000b3  // cbnz	w19, LBB0_350 $20(%rip)
	WORD $0x17fffff5  // b	LBB0_343 $-44(%rip)
LBB0_349:
	WORD $0x4b0c03e7  // neg	w7, w12
	WORD $0xb8675a07  // ldr	w7, [x16, w7, uxtw #2]
	WORD $0x34fffe53  // cbz	w19, LBB0_343 $-56(%rip)
LBB0_350:
	WORD $0x2a0703f5  // mov	w21, w7
	WORD $0x9ba044f6  // umaddl	x22, w7, w0, x17
	WORD $0xb84046d4  // ldr	w20, [x22], #4
	WORD $0x2a1303f7  // mov	w23, w19
	WORD $0xaa1703f8  // mov	x24, x23
	WORD $0xaa1603f9  // mov	x25, x22
	WORD $0xaa0803fa  // mov	x26, x8
LBB0_351:
	WORD $0x3840173b  // ldrb	w27, [x25], #1
	WORD $0x3400015b  // cbz	w27, LBB0_356 $40(%rip)
	WORD $0x3940035e  // ldrb	w30, [x26]
	WORD $0x6b1b03df  // cmp	w30, w27
	WORD $0x540013e1  // b.ne	LBB0_396 $636(%rip)
	WORD $0x9100075a  // add	x26, x26, #1
	WORD $0xf1000718  // subs	x24, x24, #1
	WORD $0x54ffff21  // b.ne	LBB0_351 $-28(%rip)
	WORD $0x38776ad6  // ldrb	w22, [x22, x23]
	WORD $0x34000056  // cbz	w22, LBB0_356 $8(%rip)
LBB0_355:
	WORD $0x51000694  // sub	w20, w20, #1
LBB0_356:
	WORD $0x0b130296  // add	w22, w20, w19
	WORD $0x7100067f  // cmp	w19, #1
	WORD $0x5400068b  // b.lt	LBB0_366 $208(%rip)
	WORD $0xd2800013  // mov	x19, #0
	WORD $0x93407ed8  // sxtw	x24, w22
	WORD $0xd100071a  // sub	x26, x24, #1
	WORD $0xd10006f7  // sub	x23, x23, #1
	WORD $0x14000008  // b	LBB0_359 $32(%rip)
LBB0_358:
	WORD $0xf100035f  // cmp	x26, #0
	WORD $0x1a9f056b  // csinc	w11, w11, wzr, eq
	WORD $0xd100071a  // sub	x26, x24, #1
	WORD $0x910006fb  // add	x27, x23, #1
	WORD $0xd10006f7  // sub	x23, x23, #1
	WORD $0xf100077f  // cmp	x27, #1
	WORD $0x54000249  // b.ls	LBB0_361 $72(%rip)
LBB0_359:
	WORD $0xaa1a03f8  // mov	x24, x26
	WORD $0x38b76919  // ldrsb	x25, [x8, x23]
	WORD $0xd100c339  // sub	x25, x25, #48
	WORD $0x9ad52339  // lsl	x25, x25, x21
	WORD $0x8b130339  // add	x25, x25, x19
	WORD $0x9bc27f33  // umulh	x19, x25, x2
	WORD $0xd343fe73  // lsr	x19, x19, #3
	WORD $0x9b04667a  // madd	x26, x19, x4, x25
	WORD $0xeb18013f  // cmp	x9, x24
	WORD $0x54fffe09  // b.ls	LBB0_358 $-64(%rip)
	WORD $0x1100c35a  // add	w26, w26, #48
	WORD $0x3838691a  // strb	w26, [x8, x24]
	WORD $0xd100071a  // sub	x26, x24, #1
	WORD $0x910006fb  // add	x27, x23, #1
	WORD $0xd10006f7  // sub	x23, x23, #1
	WORD $0xf100077f  // cmp	x27, #1
	WORD $0x54fffe08  // b.hi	LBB0_359 $-64(%rip)
LBB0_361:
	WORD $0xf1002b3f  // cmp	x25, #10
	WORD $0x540002a3  // b.lo	LBB0_366 $84(%rip)
	WORD $0x93407f15  // sxtw	x21, w24
	WORD $0xd10006b5  // sub	x21, x21, #1
	WORD $0x14000007  // b	LBB0_364 $28(%rip)
LBB0_363:
	WORD $0xf100031f  // cmp	x24, #0
	WORD $0x1a9f056b  // csinc	w11, w11, wzr, eq
	WORD $0xd10006b5  // sub	x21, x21, #1
	WORD $0xf100267f  // cmp	x19, #9
	WORD $0xaa1703f3  // mov	x19, x23
	WORD $0x54000189  // b.ls	LBB0_366 $48(%rip)
LBB0_364:
	WORD $0x9bc27e77  // umulh	x23, x19, x2
	WORD $0xd343fef7  // lsr	x23, x23, #3
	WORD $0x9b044ef8  // madd	x24, x23, x4, x19
	WORD $0xeb15013f  // cmp	x9, x21
	WORD $0x54fffec9  // b.ls	LBB0_363 $-40(%rip)
	WORD $0x1100c318  // add	w24, w24, #48
	WORD $0x38356918  // strb	w24, [x8, x21]
	WORD $0xd10006b5  // sub	x21, x21, #1
	WORD $0xf100267f  // cmp	x19, #9
	WORD $0xaa1703f3  // mov	x19, x23
	WORD $0x54fffec8  // b.hi	LBB0_364 $-40(%rip)
LBB0_366:
	WORD $0xeb36c13f  // cmp	x9, w22, sxtw
	WORD $0x1a8982d3  // csel	w19, w22, w9, hi
	WORD $0x0b0c028c  // add	w12, w20, w12
	WORD $0x7100067f  // cmp	w19, #1
	WORD $0x5400012b  // b.lt	LBB0_370 $36(%rip)
LBB0_367:
	WORD $0x387349d4  // ldrb	w20, [x14, w19, uxtw]
	WORD $0x7100c29f  // cmp	w20, #48
	WORD $0x54000101  // b.ne	LBB0_371 $32(%rip)
	WORD $0x71000673  // subs	w19, w19, #1
	WORD $0x54ffff8c  // b.gt	LBB0_367 $-16(%rip)
	WORD $0x5280000c  // mov	w12, #0
	WORD $0x52800013  // mov	w19, #0
	WORD $0x14000003  // b	LBB0_371 $12(%rip)
LBB0_370:
	WORD $0x7100027f  // cmp	w19, #0
	WORD $0x1a8c03ec  // csel	w12, wzr, w12, eq
LBB0_371:
	WORD $0x37f80067  // tbnz	w7, #31, LBB0_373 $12(%rip)
	WORD $0x4b0701ef  // sub	w15, w15, w7
	WORD $0x17ffff99  // b	LBB0_344 $-412(%rip)
LBB0_373:
	WORD $0x3100f4ff  // cmn	w7, #61
	WORD $0x54000a68  // b.hi	LBB0_397 $332(%rip)
	WORD $0xaa0703f4  // mov	x20, x7
	WORD $0x14000007  // b	LBB0_377 $28(%rip)
LBB0_375:
	WORD $0x7100027f  // cmp	w19, #0
	WORD $0x1a8c03ec  // csel	w12, wzr, w12, eq
LBB0_376:
	WORD $0x1100f296  // add	w22, w20, #60
	WORD $0x3101e29f  // cmn	w20, #120
	WORD $0xaa1603f4  // mov	x20, x22
	WORD $0x5400098a  // b.ge	LBB0_398 $304(%rip)
LBB0_377:
	WORD $0xd2800018  // mov	x24, #0
	WORD $0xd2800017  // mov	x23, #0
	WORD $0x0ab37e76  // bic	w22, w19, w19, asr #31
	WORD $0xaa1603f5  // mov	x21, x22
LBB0_378:
	WORD $0xeb1802df  // cmp	x22, x24
	WORD $0x54000140  // b.eq	LBB0_381 $40(%rip)
	WORD $0x38b86919  // ldrsb	x25, [x8, x24]
	WORD $0x9b0566f7  // madd	x23, x23, x5, x25
	WORD $0xd100c2f7  // sub	x23, x23, #48
	WORD $0x91000718  // add	x24, x24, #1
	WORD $0xd37cfef9  // lsr	x25, x23, #60
	WORD $0xb4ffff39  // cbz	x25, LBB0_378 $-28(%rip)
	WORD $0xaa1703f6  // mov	x22, x23
	WORD $0xaa1803f5  // mov	x21, x24
	WORD $0x14000008  // b	LBB0_383 $32(%rip)
LBB0_381:
	WORD $0xb40006b7  // cbz	x23, LBB0_395 $212(%rip)
LBB0_382:
	WORD $0x8b170af6  // add	x22, x23, x23, lsl #2
	WORD $0xd37ffad6  // lsl	x22, x22, #1
	WORD $0x110006b5  // add	w21, w21, #1
	WORD $0xeb0d02ff  // cmp	x23, x13
	WORD $0xaa1603f7  // mov	x23, x22
	WORD $0x54ffff63  // b.lo	LBB0_382 $-20(%rip)
LBB0_383:
	WORD $0x6b150273  // subs	w19, w19, w21
	WORD $0x540001cd  // b.le	LBB0_386 $56(%rip)
	WORD $0xaa1303f7  // mov	x23, x19
	WORD $0xaa0803f8  // mov	x24, x8
LBB0_385:
	WORD $0xd37cfed9  // lsr	x25, x22, #60
	WORD $0x9240eed6  // and	x22, x22, #0xfffffffffffffff
	WORD $0x321c0739  // orr	w25, w25, #0x30
	WORD $0x39000319  // strb	w25, [x24]
	WORD $0x38b5cb19  // ldrsb	x25, [x24, w21, sxtw]
	WORD $0x9b0566d6  // madd	x22, x22, x5, x25
	WORD $0xd100c2d6  // sub	x22, x22, #48
	WORD $0x91000718  // add	x24, x24, #1
	WORD $0xf10006f7  // subs	x23, x23, #1
	WORD $0x54fffee1  // b.ne	LBB0_385 $-36(%rip)
	WORD $0x14000002  // b	LBB0_387 $8(%rip)
LBB0_386:
	WORD $0x52800013  // mov	w19, #0
LBB0_387:
	WORD $0xb5000136  // cbnz	x22, LBB0_389 $36(%rip)
	WORD $0x14000013  // b	LBB0_391 $76(%rip)
LBB0_388:
	WORD $0xd37cfed7  // lsr	x23, x22, #60
	WORD $0xf10002ff  // cmp	x23, #0
	WORD $0x1a9f056b  // csinc	w11, w11, wzr, eq
	WORD $0x9240eed7  // and	x23, x22, #0xfffffffffffffff
	WORD $0x8b170af6  // add	x22, x23, x23, lsl #2
	WORD $0xd37ffad6  // lsl	x22, x22, #1
	WORD $0xb4000197  // cbz	x23, LBB0_391 $48(%rip)
LBB0_389:
	WORD $0x93407e77  // sxtw	x23, w19
	WORD $0xeb17013f  // cmp	x9, x23
	WORD $0x54fffee9  // b.ls	LBB0_388 $-36(%rip)
	WORD $0xd37cfed8  // lsr	x24, x22, #60
	WORD $0x321c0718  // orr	w24, w24, #0x30
	WORD $0x38376918  // strb	w24, [x8, x23]
	WORD $0x11000673  // add	w19, w19, #1
	WORD $0x9240eed7  // and	x23, x22, #0xfffffffffffffff
	WORD $0x8b170af6  // add	x22, x23, x23, lsl #2
	WORD $0xd37ffad6  // lsl	x22, x22, #1
	WORD $0xb5fffed7  // cbnz	x23, LBB0_389 $-40(%rip)
LBB0_391:
	WORD $0x4b15018c  // sub	w12, w12, w21
	WORD $0x1100058c  // add	w12, w12, #1
	WORD $0x7100067f  // cmp	w19, #1
	WORD $0x54fff7ab  // b.lt	LBB0_375 $-268(%rip)
LBB0_392:
	WORD $0x387349d5  // ldrb	w21, [x14, w19, uxtw]
	WORD $0x7100c2bf  // cmp	w21, #48
	WORD $0x54fff781  // b.ne	LBB0_376 $-272(%rip)
	WORD $0x71000673  // subs	w19, w19, #1
	WORD $0x54ffff8c  // b.gt	LBB0_392 $-16(%rip)
	WORD $0x5280000c  // mov	w12, #0
LBB0_395:
	WORD $0x52800013  // mov	w19, #0
	WORD $0x17ffffb7  // b	LBB0_376 $-292(%rip)
LBB0_396:
	WORD $0x13001fd6  // sxtb	w22, w30
	WORD $0x6b3b82df  // cmp	w22, w27, sxtb
	WORD $0x54ffecab  // b.lt	LBB0_355 $-620(%rip)
	WORD $0x17ffff65  // b	LBB0_356 $-620(%rip)
LBB0_397:
	WORD $0xaa0703f6  // mov	x22, x7
LBB0_398:
	WORD $0xd2800017  // mov	x23, #0
	WORD $0xd2800015  // mov	x21, #0
	WORD $0x4b1603f4  // neg	w20, w22
	WORD $0x0ab37e76  // bic	w22, w19, w19, asr #31
LBB0_399:
	WORD $0xeb1702df  // cmp	x22, x23
	WORD $0x54000300  // b.eq	LBB0_405 $96(%rip)
	WORD $0x38b76918  // ldrsb	x24, [x8, x23]
	WORD $0x9b0562b5  // madd	x21, x21, x5, x24
	WORD $0xd100c2b5  // sub	x21, x21, #48
	WORD $0x910006f7  // add	x23, x23, #1
	WORD $0x9ad426b8  // lsr	x24, x21, x20
	WORD $0xb4ffff38  // cbz	x24, LBB0_399 $-28(%rip)
LBB0_402:
	WORD $0x9ad420d6  // lsl	x22, x6, x20
	WORD $0xaa3603f6  // mvn	x22, x22
	WORD $0x6b170273  // subs	w19, w19, w23
	WORD $0x540002ed  // b.le	LBB0_408 $92(%rip)
	WORD $0xaa1303f8  // mov	x24, x19
	WORD $0xaa0803f9  // mov	x25, x8
LBB0_404:
	WORD $0x9ad426ba  // lsr	x26, x21, x20
	WORD $0x8a1602b5  // and	x21, x21, x22
	WORD $0x1100c35a  // add	w26, w26, #48
	WORD $0x3900033a  // strb	w26, [x25]
	WORD $0x38b7cb3a  // ldrsb	x26, [x25, w23, sxtw]
	WORD $0x9b056ab5  // madd	x21, x21, x5, x26
	WORD $0xd100c2b5  // sub	x21, x21, #48
	WORD $0x91000739  // add	x25, x25, #1
	WORD $0xf1000718  // subs	x24, x24, #1
	WORD $0x54fffee1  // b.ne	LBB0_404 $-36(%rip)
	WORD $0x1400000b  // b	LBB0_409 $44(%rip)
LBB0_405:
	WORD $0xb4000575  // cbz	x21, LBB0_419 $172(%rip)
	WORD $0x9ad426b7  // lsr	x23, x21, x20
	WORD $0xb4000597  // cbz	x23, LBB0_420 $176(%rip)
	WORD $0x52800013  // mov	w19, #0
	WORD $0x4b16018c  // sub	w12, w12, w22
	WORD $0x1100058c  // add	w12, w12, #1
	WORD $0x9ad420d6  // lsl	x22, x6, x20
	WORD $0xaa3603f6  // mvn	x22, x22
	WORD $0x14000005  // b	LBB0_410 $20(%rip)
LBB0_408:
	WORD $0x52800013  // mov	w19, #0
LBB0_409:
	WORD $0x4b17018c  // sub	w12, w12, w23
	WORD $0x1100058c  // add	w12, w12, #1
	WORD $0xb4000295  // cbz	x21, LBB0_414 $80(%rip)
LBB0_410:
	WORD $0x14000007  // b	LBB0_412 $28(%rip)
LBB0_411:
	WORD $0xf10002ff  // cmp	x23, #0
	WORD $0x1a9f056b  // csinc	w11, w11, wzr, eq
	WORD $0x8a1602b7  // and	x23, x21, x22
	WORD $0x8b170af5  // add	x21, x23, x23, lsl #2
	WORD $0xd37ffab5  // lsl	x21, x21, #1
	WORD $0xb40001b7  // cbz	x23, LBB0_415 $52(%rip)
LBB0_412:
	WORD $0x9ad426b7  // lsr	x23, x21, x20
	WORD $0x93407e78  // sxtw	x24, w19
	WORD $0xeb18013f  // cmp	x9, x24
	WORD $0x54fffee9  // b.ls	LBB0_411 $-36(%rip)
	WORD $0x1100c2f7  // add	w23, w23, #48
	WORD $0x38386917  // strb	w23, [x8, x24]
	WORD $0x11000673  // add	w19, w19, #1
	WORD $0x8a1602b7  // and	x23, x21, x22
	WORD $0x8b170af5  // add	x21, x23, x23, lsl #2
	WORD $0xd37ffab5  // lsl	x21, x21, #1
	WORD $0xb5fffed7  // cbnz	x23, LBB0_412 $-40(%rip)
	WORD $0x14000001  // b	LBB0_415 $4(%rip)
LBB0_414:
LBB0_415:
	WORD $0x7100067f  // cmp	w19, #1
	WORD $0x54ffe04b  // b.lt	LBB0_342 $-1016(%rip)
LBB0_416:
	WORD $0x387349d4  // ldrb	w20, [x14, w19, uxtw]
	WORD $0x7100c29f  // cmp	w20, #48
	WORD $0x54ffe021  // b.ne	LBB0_343 $-1020(%rip)
	WORD $0x71000673  // subs	w19, w19, #1
	WORD $0x54ffff8c  // b.gt	LBB0_416 $-16(%rip)
	WORD $0x5280000c  // mov	w12, #0
	WORD $0x52800013  // mov	w19, #0
	WORD $0x4b0701ef  // sub	w15, w15, w7
	WORD $0x17fffefc  // b	LBB0_344 $-1040(%rip)
LBB0_419:
	WORD $0x52800013  // mov	w19, #0
	WORD $0x4b0701ef  // sub	w15, w15, w7
	WORD $0x17fffef9  // b	LBB0_344 $-1052(%rip)
LBB0_420:
	WORD $0xaa1603f7  // mov	x23, x22
LBB0_421:
	WORD $0x8b150ab5  // add	x21, x21, x21, lsl #2
	WORD $0xd37ffab5  // lsl	x21, x21, #1
	WORD $0x110006f7  // add	w23, w23, #1
	WORD $0x9ad426b6  // lsr	x22, x21, x20
	WORD $0xb4ffff96  // cbz	x22, LBB0_421 $-16(%rip)
	WORD $0x17ffffbb  // b	LBB0_402 $-276(%rip)
LBB0_422:
	WORD $0x310ff9ff  // cmn	w15, #1022
	WORD $0x54000a6c  // b.gt	LBB0_447 $332(%rip)
	WORD $0x34001213  // cbz	w19, LBB0_464 $576(%rip)
	WORD $0x110ff5f0  // add	w16, w15, #1021
	WORD $0x3110e9ff  // cmn	w15, #1082
	WORD $0x54000b08  // b.hi	LBB0_452 $352(%rip)
	WORD $0x52800140  // mov	w0, #10
	WORD $0xaa1303ef  // mov	x15, x19
	WORD $0x14000007  // b	LBB0_428 $28(%rip)
LBB0_426:
	WORD $0x710001ff  // cmp	w15, #0
	WORD $0x1a8c03ec  // csel	w12, wzr, w12, eq
LBB0_427:
	WORD $0x1100f202  // add	w2, w16, #60
	WORD $0x3101e21f  // cmn	w16, #120
	WORD $0xaa0203f0  // mov	x16, x2
	WORD $0x54000a2a  // b.ge	LBB0_453 $324(%rip)
LBB0_428:
	WORD $0xd2800006  // mov	x6, #0
	WORD $0xd2800005  // mov	x5, #0
	WORD $0x0aaf7de4  // bic	w4, w15, w15, asr #31
	WORD $0xaa0403e2  // mov	x2, x4
LBB0_429:
	WORD $0xeb06009f  // cmp	x4, x6
	WORD $0x54000140  // b.eq	LBB0_432 $40(%rip)
	WORD $0x38a66907  // ldrsb	x7, [x8, x6]
	WORD $0x9b001ca5  // madd	x5, x5, x0, x7
	WORD $0xd100c0a5  // sub	x5, x5, #48
	WORD $0x910004c6  // add	x6, x6, #1
	WORD $0xd37cfca7  // lsr	x7, x5, #60
	WORD $0xb4ffff27  // cbz	x7, LBB0_429 $-28(%rip)
	WORD $0xaa0503e4  // mov	x4, x5
	WORD $0xaa0603e2  // mov	x2, x6
	WORD $0x14000008  // b	LBB0_434 $32(%rip)
LBB0_432:
	WORD $0xb4000685  // cbz	x5, LBB0_446 $208(%rip)
LBB0_433:
	WORD $0x8b0508a4  // add	x4, x5, x5, lsl #2
	WORD $0xd37ff884  // lsl	x4, x4, #1
	WORD $0x11000442  // add	w2, w2, #1
	WORD $0xeb0d00bf  // cmp	x5, x13
	WORD $0xaa0403e5  // mov	x5, x4
	WORD $0x54ffff63  // b.lo	LBB0_433 $-20(%rip)
LBB0_434:
	WORD $0x6b0201ef  // subs	w15, w15, w2
	WORD $0x540001cd  // b.le	LBB0_437 $56(%rip)
	WORD $0xaa0f03e5  // mov	x5, x15
	WORD $0xaa0803e6  // mov	x6, x8
LBB0_436:
	WORD $0xd37cfc87  // lsr	x7, x4, #60
	WORD $0x9240ec84  // and	x4, x4, #0xfffffffffffffff
	WORD $0x321c04e7  // orr	w7, w7, #0x30
	WORD $0x390000c7  // strb	w7, [x6]
	WORD $0x38a2c8c7  // ldrsb	x7, [x6, w2, sxtw]
	WORD $0x9b001c84  // madd	x4, x4, x0, x7
	WORD $0xd100c084  // sub	x4, x4, #48
	WORD $0x910004c6  // add	x6, x6, #1
	WORD $0xf10004a5  // subs	x5, x5, #1
	WORD $0x54fffee1  // b.ne	LBB0_436 $-36(%rip)
	WORD $0x14000002  // b	LBB0_438 $8(%rip)
LBB0_437:
	WORD $0x5280000f  // mov	w15, #0
LBB0_438:
	WORD $0xb5000104  // cbnz	x4, LBB0_440 $32(%rip)
	WORD $0x14000012  // b	LBB0_442 $72(%rip)
LBB0_439:
	WORD $0xf10000df  // cmp	x6, #0
	WORD $0x1a9f056b  // csinc	w11, w11, wzr, eq
	WORD $0x9240ec85  // and	x5, x4, #0xfffffffffffffff
	WORD $0x8b0508a4  // add	x4, x5, x5, lsl #2
	WORD $0xd37ff884  // lsl	x4, x4, #1
	WORD $0xb4000185  // cbz	x5, LBB0_442 $48(%rip)
LBB0_440:
	WORD $0x93407de5  // sxtw	x5, w15
	WORD $0xd37cfc86  // lsr	x6, x4, #60
	WORD $0xeb05013f  // cmp	x9, x5
	WORD $0x54fffee9  // b.ls	LBB0_439 $-36(%rip)
	WORD $0x321c04c6  // orr	w6, w6, #0x30
	WORD $0x38256906  // strb	w6, [x8, x5]
	WORD $0x110005ef  // add	w15, w15, #1
	WORD $0x9240ec85  // and	x5, x4, #0xfffffffffffffff
	WORD $0x8b0508a4  // add	x4, x5, x5, lsl #2
	WORD $0xd37ff884  // lsl	x4, x4, #1
	WORD $0xb5fffec5  // cbnz	x5, LBB0_440 $-40(%rip)
LBB0_442:
	WORD $0x4b02018c  // sub	w12, w12, w2
	WORD $0x1100058c  // add	w12, w12, #1
	WORD $0x710005ff  // cmp	w15, #1
	WORD $0x54fff7cb  // b.lt	LBB0_426 $-264(%rip)
LBB0_443:
	WORD $0x386f49c2  // ldrb	w2, [x14, w15, uxtw]
	WORD $0x7100c05f  // cmp	w2, #48
	WORD $0x54fff7a1  // b.ne	LBB0_427 $-268(%rip)
	WORD $0x710005ef  // subs	w15, w15, #1
	WORD $0x54ffff8c  // b.gt	LBB0_443 $-16(%rip)
	WORD $0x5280000c  // mov	w12, #0
LBB0_446:
	WORD $0x5280000f  // mov	w15, #0
	WORD $0x17ffffb8  // b	LBB0_427 $-288(%rip)
LBB0_447:
	WORD $0x711001ff  // cmp	w15, #1024
	WORD $0x54ffb60c  // b.gt	LBB0_274 $-2368(%rip)
	WORD $0x510005ed  // sub	w13, w15, #1
	WORD $0x34001773  // cbz	w19, LBB0_485 $748(%rip)
	WORD $0x140000d1  // b	LBB0_494 $836(%rip)
LBB0_450:
	WORD $0x8b0b000b  // add	x11, x0, x11
	WORD $0x1400003e  // b	LBB0_466 $248(%rip)
LBB0_451:
	WORD $0x8b0b000b  // add	x11, x0, x11
	WORD $0x14000064  // b	LBB0_472 $400(%rip)
LBB0_452:
	WORD $0xaa1303ef  // mov	x15, x19
	WORD $0xaa1003e2  // mov	x2, x16
LBB0_453:
	WORD $0xd2800000  // mov	x0, #0
	WORD $0xd2800010  // mov	x16, #0
	WORD $0x4b0203ed  // neg	w13, w2
	WORD $0x52800144  // mov	w4, #10
	WORD $0x0aaf7de2  // bic	w2, w15, w15, asr #31
LBB0_454:
	WORD $0xeb00005f  // cmp	x2, x0
	WORD $0x540003a0  // b.eq	LBB0_460 $116(%rip)
	WORD $0x38a06905  // ldrsb	x5, [x8, x0]
	WORD $0x9b041610  // madd	x16, x16, x4, x5
	WORD $0xd100c210  // sub	x16, x16, #48
	WORD $0x91000400  // add	x0, x0, #1
	WORD $0x9acd2605  // lsr	x5, x16, x13
	WORD $0xb4ffff25  // cbz	x5, LBB0_454 $-28(%rip)
	WORD $0xaa0003e2  // mov	x2, x0
LBB0_457:
	WORD $0x4b02018c  // sub	w12, w12, w2
	WORD $0x1100058c  // add	w12, w12, #1
	WORD $0x92800000  // mov	x0, #-1
	WORD $0x9acd2000  // lsl	x0, x0, x13
	WORD $0xaa2003e0  // mvn	x0, x0
	WORD $0x6b0201f3  // subs	w19, w15, w2
	WORD $0x5400114d  // b.le	LBB0_479 $552(%rip)
	WORD $0x5280014f  // mov	w15, #10
	WORD $0xaa1303e4  // mov	x4, x19
	WORD $0xaa0803e5  // mov	x5, x8
LBB0_459:
	WORD $0x9acd2606  // lsr	x6, x16, x13
	WORD $0x8a000210  // and	x16, x16, x0
	WORD $0x1100c0c6  // add	w6, w6, #48
	WORD $0x390000a6  // strb	w6, [x5]
	WORD $0x38a2c8a6  // ldrsb	x6, [x5, w2, sxtw]
	WORD $0x9b0f1a10  // madd	x16, x16, x15, x6
	WORD $0xd100c210  // sub	x16, x16, #48
	WORD $0x910004a5  // add	x5, x5, #1
	WORD $0xf1000484  // subs	x4, x4, #1
	WORD $0x54fffee1  // b.ne	LBB0_459 $-36(%rip)
	WORD $0x1400007d  // b	LBB0_480 $500(%rip)
LBB0_460:
	WORD $0xb4000210  // cbz	x16, LBB0_464 $64(%rip)
	WORD $0x9acd2600  // lsr	x0, x16, x13
	WORD $0xb4000100  // cbz	x0, LBB0_463 $32(%rip)
	WORD $0x52800013  // mov	w19, #0
	WORD $0x4b02018c  // sub	w12, w12, w2
	WORD $0x1100058c  // add	w12, w12, #1
	WORD $0x9280000f  // mov	x15, #-1
	WORD $0x9acd21ef  // lsl	x15, x15, x13
	WORD $0xaa2f03e0  // mvn	x0, x15
	WORD $0x14000074  // b	LBB0_481 $464(%rip)
LBB0_463:
	WORD $0x8b100a10  // add	x16, x16, x16, lsl #2
	WORD $0xd37ffa10  // lsl	x16, x16, #1
	WORD $0x11000442  // add	w2, w2, #1
	WORD $0x9acd2600  // lsr	x0, x16, x13
	WORD $0xb4ffff80  // cbz	x0, LBB0_463 $-16(%rip)
	WORD $0x17ffffdc  // b	LBB0_457 $-144(%rip)
LBB0_464:
	WORD $0x52800009  // mov	w9, #0
	WORD $0x12807fad  // mov	w13, #-1022
	WORD $0x140000f8  // b	LBB0_517 $992(%rip)
LBB0_465:
	WORD $0xd280000a  // mov	x10, #0
	WORD $0x8b02000b  // add	x11, x0, x2
	WORD $0x92800008  // mov	x8, #-1
LBB0_466:
	WORD $0xf100812c  // subs	x12, x9, #32
	WORD $0x54002ba3  // b.lo	LBB0_546 $1396(%rip)
	WORD $0xad400560  // ldp	q0, q1, [x11]
	WORD $0x4f01e442  // movi.16b	v2, #34
	WORD $0x6e228c03  // cmeq.16b	v3, v0, v2
	WORD $0x6e228c22  // cmeq.16b	v2, v1, v2
	WORD $0x4f02e784  // movi.16b	v4, #92
	WORD $0x6e248c00  // cmeq.16b	v0, v0, v4
	WORD $0x6e248c21  // cmeq.16b	v1, v1, v4
Lloh26:
	WORD $0x10ff1949  // adr	x9, lCPI0_0 $-7384(%rip)
Lloh27:
	WORD $0x3dc00124  // ldr	q4, [x9, lCPI0_0@PAGEOFF] $0(%rip)
	WORD $0x4e241c63  // and.16b	v3, v3, v4
Lloh28:
	WORD $0x10ff1969  // adr	x9, lCPI0_1 $-7380(%rip)
Lloh29:
	WORD $0x3dc00125  // ldr	q5, [x9, lCPI0_1@PAGEOFF] $0(%rip)
	WORD $0x4e050063  // tbl.16b	v3, { v3 }, v5
	WORD $0x4e71b863  // addv.8h	h3, v3
	WORD $0x1e260069  // fmov	w9, s3
	WORD $0x4e241c42  // and.16b	v2, v2, v4
	WORD $0x4e050042  // tbl.16b	v2, { v2 }, v5
	WORD $0x4e71b842  // addv.8h	h2, v2
	WORD $0x1e26004e  // fmov	w14, s2
	WORD $0x4e241c00  // and.16b	v0, v0, v4
	WORD $0x4e050000  // tbl.16b	v0, { v0 }, v5
	WORD $0x4e71b800  // addv.8h	h0, v0
	WORD $0x1e26000d  // fmov	w13, s0
	WORD $0x4e241c20  // and.16b	v0, v1, v4
	WORD $0x4e050000  // tbl.16b	v0, { v0 }, v5
	WORD $0x4e71b800  // addv.8h	h0, v0
	WORD $0x1e26000f  // fmov	w15, s0
	WORD $0x33103dc9  // bfi	w9, w14, #16, #16
	WORD $0x33103ded  // bfi	w13, w15, #16, #16
	WORD $0x3500252d  // cbnz	w13, LBB0_543 $1188(%rip)
	WORD $0xb50025ca  // cbnz	x10, LBB0_544 $1208(%rip)
	WORD $0xb4002769  // cbz	x9, LBB0_545 $1260(%rip)
LBB0_470:
	WORD $0xdac00129  // rbit	x9, x9
	WORD $0xdac01129  // clz	x9, x9
	WORD $0x14000038  // b	LBB0_477 $224(%rip)
LBB0_471:
	WORD $0xd2800009  // mov	x9, #0
	WORD $0x8b02000b  // add	x11, x0, x2
	WORD $0x92800008  // mov	x8, #-1
LBB0_472:
	WORD $0xf100814c  // subs	x12, x10, #32
	WORD $0x54002e23  // b.lo	LBB0_563 $1476(%rip)
	WORD $0xad400560  // ldp	q0, q1, [x11]
	WORD $0x4f01e442  // movi.16b	v2, #34
	WORD $0x6e228c03  // cmeq.16b	v3, v0, v2
Lloh30:
	WORD $0x10ff14ca  // adr	x10, lCPI0_0 $-7528(%rip)
Lloh31:
	WORD $0x3dc00144  // ldr	q4, [x10, lCPI0_0@PAGEOFF] $0(%rip)
	WORD $0x4e241c63  // and.16b	v3, v3, v4
Lloh32:
	WORD $0x10ff14ea  // adr	x10, lCPI0_1 $-7524(%rip)
Lloh33:
	WORD $0x3dc00145  // ldr	q5, [x10, lCPI0_1@PAGEOFF] $0(%rip)
	WORD $0x4e050063  // tbl.16b	v3, { v3 }, v5
	WORD $0x4e71b863  // addv.8h	h3, v3
	WORD $0x1e26006a  // fmov	w10, s3
	WORD $0x6e228c22  // cmeq.16b	v2, v1, v2
	WORD $0x4e241c42  // and.16b	v2, v2, v4
	WORD $0x4e050042  // tbl.16b	v2, { v2 }, v5
	WORD $0x4e71b842  // addv.8h	h2, v2
	WORD $0x1e26004f  // fmov	w15, s2
	WORD $0x4f02e782  // movi.16b	v2, #92
	WORD $0x6e228c03  // cmeq.16b	v3, v0, v2
	WORD $0x4e241c63  // and.16b	v3, v3, v4
	WORD $0x4e050063  // tbl.16b	v3, { v3 }, v5
	WORD $0x4e71b863  // addv.8h	h3, v3
	WORD $0x1e26006e  // fmov	w14, s3
	WORD $0x6e228c22  // cmeq.16b	v2, v1, v2
	WORD $0x4e241c42  // and.16b	v2, v2, v4
	WORD $0x4e050042  // tbl.16b	v2, { v2 }, v5
	WORD $0x4e71b842  // addv.8h	h2, v2
	WORD $0x1e260050  // fmov	w16, s2
	WORD $0x4f01e402  // movi.16b	v2, #32
	WORD $0x6e203440  // cmhi.16b	v0, v2, v0
	WORD $0x4e241c00  // and.16b	v0, v0, v4
	WORD $0x4e050000  // tbl.16b	v0, { v0 }, v5
	WORD $0x4e71b800  // addv.8h	h0, v0
	WORD $0x1e26000d  // fmov	w13, s0
	WORD $0x6e213440  // cmhi.16b	v0, v2, v1
	WORD $0x4e241c00  // and.16b	v0, v0, v4
	WORD $0x4e050000  // tbl.16b	v0, { v0 }, v5
	WORD $0x4e71b800  // addv.8h	h0, v0
	WORD $0x1e260011  // fmov	w17, s0
	WORD $0x33103dea  // bfi	w10, w15, #16, #16
	WORD $0x33103e0e  // bfi	w14, w16, #16, #16
	WORD $0x33103e2d  // bfi	w13, w17, #16, #16
	WORD $0x3500260e  // cbnz	w14, LBB0_559 $1216(%rip)
	WORD $0xb50026a9  // cbnz	x9, LBB0_560 $1236(%rip)
	WORD $0xb400284a  // cbz	x10, LBB0_561 $1288(%rip)
LBB0_476:
	WORD $0xdac00149  // rbit	x9, x10
	WORD $0xdac01129  // clz	x9, x9
	WORD $0xdac001aa  // rbit	x10, x13
	WORD $0xdac0114a  // clz	x10, x10
	WORD $0xeb09015f  // cmp	x10, x9
	WORD $0x54002bc3  // b.lo	LBB0_573 $1400(%rip)
LBB0_477:
	WORD $0xcb00016a  // sub	x10, x11, x0
	WORD $0x8b090149  // add	x9, x10, x9
	WORD $0x9100052c  // add	x12, x9, #1
	WORD $0xb6ff5f6c  // tbz	x12, #63, LBB0_134 $-5140(%rip)
	WORD $0x14000160  // b	LBB0_575 $1408(%rip)
LBB0_478:
	WORD $0xcb000101  // sub	x1, x8, x0
	WORD $0x9280000a  // mov	x10, #-1
	WORD $0xf900006a  // str	x10, [x3]
	WORD $0x17fff8c1  // b	LBB0_24 $-7420(%rip)
LBB0_479:
	WORD $0x52800013  // mov	w19, #0
LBB0_480:
	WORD $0xb4000390  // cbz	x16, LBB0_487 $112(%rip)
LBB0_481:
	WORD $0x14000007  // b	LBB0_483 $28(%rip)
LBB0_482:
	WORD $0xf10001ff  // cmp	x15, #0
	WORD $0x1a9f056b  // csinc	w11, w11, wzr, eq
	WORD $0x8a00020f  // and	x15, x16, x0
	WORD $0x8b0f09f0  // add	x16, x15, x15, lsl #2
	WORD $0xd37ffa10  // lsl	x16, x16, #1
	WORD $0xb40002af  // cbz	x15, LBB0_488 $84(%rip)
LBB0_483:
	WORD $0x9acd260f  // lsr	x15, x16, x13
	WORD $0x93407e62  // sxtw	x2, w19
	WORD $0xeb02013f  // cmp	x9, x2
	WORD $0x54fffee9  // b.ls	LBB0_482 $-36(%rip)
	WORD $0x1100c1ef  // add	w15, w15, #48
	WORD $0x3822690f  // strb	w15, [x8, x2]
	WORD $0x11000673  // add	w19, w19, #1
	WORD $0x8a00020f  // and	x15, x16, x0
	WORD $0x8b0f09f0  // add	x16, x15, x15, lsl #2
	WORD $0xd37ffa10  // lsl	x16, x16, #1
	WORD $0xb5fffecf  // cbnz	x15, LBB0_483 $-40(%rip)
	WORD $0x14000009  // b	LBB0_488 $36(%rip)
LBB0_485:
	WORD $0x52800009  // mov	w9, #0
	WORD $0x14000079  // b	LBB0_517 $484(%rip)
LBB0_486:
	WORD $0x9280000b  // mov	x11, #-1
	WORD $0xaa0803f0  // mov	x16, x8
	WORD $0xaa0d03e1  // mov	x1, x13
	WORD $0x9280000c  // mov	x12, #-1
	WORD $0x92800009  // mov	x9, #-1
	WORD $0x17fff94d  // b	LBB0_61 $-6860(%rip)
LBB0_487:
LBB0_488:
	WORD $0x7100067f  // cmp	w19, #1
	WORD $0x5400014b  // b.lt	LBB0_492 $40(%rip)
LBB0_489:
	WORD $0x387349cd  // ldrb	w13, [x14, w19, uxtw]
	WORD $0x7100c1bf  // cmp	w13, #48
	WORD $0x54000141  // b.ne	LBB0_493 $40(%rip)
	WORD $0x71000673  // subs	w19, w19, #1
	WORD $0x54ffff8c  // b.gt	LBB0_489 $-16(%rip)
	WORD $0x52800009  // mov	w9, #0
	WORD $0x5280000c  // mov	w12, #0
	WORD $0x12807fad  // mov	w13, #-1022
	WORD $0x1400006d  // b	LBB0_520 $436(%rip)
LBB0_492:
	WORD $0x12807fad  // mov	w13, #-1022
	WORD $0x35000073  // cbnz	w19, LBB0_494 $12(%rip)
	WORD $0x14000062  // b	LBB0_515 $392(%rip)
LBB0_493:
	WORD $0x12807fad  // mov	w13, #-1022
LBB0_494:
	WORD $0xd280000f  // mov	x15, #0
	WORD $0x2a1303e0  // mov	w0, w19
	WORD $0x5282b190  // mov	w16, #5516
LBB0_495:
	WORD $0xf10099ff  // cmp	x15, #38
	WORD $0x54000260  // b.eq	LBB0_501 $76(%rip)
	WORD $0x8b0f0222  // add	x2, x17, x15
	WORD $0x38706842  // ldrb	w2, [x2, x16]
	WORD $0x386f6904  // ldrb	w4, [x8, x15]
	WORD $0x6b02009f  // cmp	w4, w2
	WORD $0x54000141  // b.ne	LBB0_500 $40(%rip)
	WORD $0x910005ef  // add	x15, x15, #1
	WORD $0xeb0f001f  // cmp	x0, x15
	WORD $0x54fffee1  // b.ne	LBB0_495 $-36(%rip)
	WORD $0x8b00022f  // add	x15, x17, x0
	WORD $0x5282b190  // mov	w16, #5516
	WORD $0x387069ef  // ldrb	w15, [x15, x16]
	WORD $0x340000ef  // cbz	w15, LBB0_501 $28(%rip)
LBB0_499:
	WORD $0x528001ef  // mov	w15, #15
	WORD $0x14000006  // b	LBB0_502 $24(%rip)
LBB0_500:
	WORD $0x13001c4f  // sxtb	w15, w2
	WORD $0x13001c90  // sxtb	w16, w4
	WORD $0x6b0f021f  // cmp	w16, w15
	WORD $0x54ffff6b  // b.lt	LBB0_499 $-20(%rip)
LBB0_501:
	WORD $0x5280020f  // mov	w15, #16
LBB0_502:
	WORD $0x0b0001f0  // add	w16, w15, w0
	WORD $0x7100041f  // cmp	w0, #1
	WORD $0x5400078b  // b.lt	LBB0_512 $240(%rip)
	WORD $0xd2800011  // mov	x17, #0
	WORD $0x93407e02  // sxtw	x2, w16
	WORD $0xd1000442  // sub	x2, x2, #1
	WORD $0xd1000400  // sub	x0, x0, #1
	WORD $0xd2ff4005  // mov	x5, #-432345564227567616
	WORD $0xb202e7e6  // mov	x6, #-3689348814741910324
	WORD $0xf29999a6  // movk	x6, #52429
	WORD $0x92800127  // mov	x7, #-10
	WORD $0xaa1003e4  // mov	x4, x16
	WORD $0x14000009  // b	LBB0_505 $36(%rip)
LBB0_504:
	WORD $0xf100029f  // cmp	x20, #0
	WORD $0x1a9f056b  // csinc	w11, w11, wzr, eq
	WORD $0x51000484  // sub	w4, w4, #1
	WORD $0xd1000442  // sub	x2, x2, #1
	WORD $0x91000414  // add	x20, x0, #1
	WORD $0xd1000400  // sub	x0, x0, #1
	WORD $0xf100069f  // cmp	x20, #1
	WORD $0x54000229  // b.ls	LBB0_507 $68(%rip)
LBB0_505:
	WORD $0x38a06913  // ldrsb	x19, [x8, x0]
	WORD $0x8b13d631  // add	x17, x17, x19, lsl #53
	WORD $0x8b050233  // add	x19, x17, x5
	WORD $0x9bc67e71  // umulh	x17, x19, x6
	WORD $0xd343fe31  // lsr	x17, x17, #3
	WORD $0x9b074e34  // madd	x20, x17, x7, x19
	WORD $0xeb02013f  // cmp	x9, x2
	WORD $0x54fffe29  // b.ls	LBB0_504 $-60(%rip)
	WORD $0x1100c294  // add	w20, w20, #48
	WORD $0x38226914  // strb	w20, [x8, x2]
	WORD $0x51000484  // sub	w4, w4, #1
	WORD $0xd1000442  // sub	x2, x2, #1
	WORD $0x91000414  // add	x20, x0, #1
	WORD $0xd1000400  // sub	x0, x0, #1
	WORD $0xf100069f  // cmp	x20, #1
	WORD $0x54fffe28  // b.hi	LBB0_505 $-60(%rip)
LBB0_507:
	WORD $0xf1002a7f  // cmp	x19, #10
	WORD $0x54000303  // b.lo	LBB0_512 $96(%rip)
	WORD $0x93407c80  // sxtw	x0, w4
	WORD $0xd1000400  // sub	x0, x0, #1
	WORD $0xb202e7e2  // mov	x2, #-3689348814741910324
	WORD $0xf29999a2  // movk	x2, #52429
	WORD $0x92800124  // mov	x4, #-10
	WORD $0x14000007  // b	LBB0_510 $28(%rip)
LBB0_509:
	WORD $0xf10000df  // cmp	x6, #0
	WORD $0x1a9f056b  // csinc	w11, w11, wzr, eq
	WORD $0xd1000400  // sub	x0, x0, #1
	WORD $0xf100263f  // cmp	x17, #9
	WORD $0xaa0503f1  // mov	x17, x5
	WORD $0x54000189  // b.ls	LBB0_512 $48(%rip)
LBB0_510:
	WORD $0x9bc27e25  // umulh	x5, x17, x2
	WORD $0xd343fca5  // lsr	x5, x5, #3
	WORD $0x9b0444a6  // madd	x6, x5, x4, x17
	WORD $0xeb00013f  // cmp	x9, x0
	WORD $0x54fffec9  // b.ls	LBB0_509 $-40(%rip)
	WORD $0x1100c0c6  // add	w6, w6, #48
	WORD $0x38206906  // strb	w6, [x8, x0]
	WORD $0xd1000400  // sub	x0, x0, #1
	WORD $0xf100263f  // cmp	x17, #9
	WORD $0xaa0503f1  // mov	x17, x5
	WORD $0x54fffec8  // b.hi	LBB0_510 $-40(%rip)
LBB0_512:
	WORD $0xeb30c13f  // cmp	x9, w16, sxtw
	WORD $0x1a898209  // csel	w9, w16, w9, hi
	WORD $0x0b0c01ec  // add	w12, w15, w12
	WORD $0x7100053f  // cmp	w9, #1
	WORD $0x5400010b  // b.lt	LBB0_516 $32(%rip)
LBB0_513:
	WORD $0x386949cf  // ldrb	w15, [x14, w9, uxtw]
	WORD $0x7100c1ff  // cmp	w15, #48
	WORD $0x540000c1  // b.ne	LBB0_517 $24(%rip)
	WORD $0x71000529  // subs	w9, w9, #1
	WORD $0x54ffff8c  // b.gt	LBB0_513 $-16(%rip)
LBB0_515:
	WORD $0x52800009  // mov	w9, #0
	WORD $0x14000006  // b	LBB0_519 $24(%rip)
LBB0_516:
	WORD $0x340000a9  // cbz	w9, LBB0_519 $20(%rip)
LBB0_517:
	WORD $0x7100519f  // cmp	w12, #20
	WORD $0x5400008d  // b.le	LBB0_520 $16(%rip)
	WORD $0x9280000e  // mov	x14, #-1
	WORD $0x14000046  // b	LBB0_540 $280(%rip)
LBB0_519:
	WORD $0x5280000c  // mov	w12, #0
LBB0_520:
	WORD $0x6b09019f  // cmp	w12, w9
	WORD $0x1a89b18f  // csel	w15, w12, w9, lt
	WORD $0x710005ff  // cmp	w15, #1
	WORD $0x5400016b  // b.lt	LBB0_523 $44(%rip)
	WORD $0xd280000e  // mov	x14, #0
	WORD $0x52800150  // mov	w16, #10
	WORD $0xaa0f03f1  // mov	x17, x15
	WORD $0xaa0803e0  // mov	x0, x8
LBB0_522:
	WORD $0x38801402  // ldrsb	x2, [x0], #1
	WORD $0x9b1009ce  // madd	x14, x14, x16, x2
	WORD $0xd100c1ce  // sub	x14, x14, #48
	WORD $0xf1000631  // subs	x17, x17, #1
	WORD $0x54ffff81  // b.ne	LBB0_522 $-16(%rip)
	WORD $0x14000003  // b	LBB0_524 $12(%rip)
LBB0_523:
	WORD $0x5280000f  // mov	w15, #0
	WORD $0xd280000e  // mov	x14, #0
LBB0_524:
	WORD $0x6b0f0190  // subs	w16, w12, w15
	WORD $0x540003ad  // b.le	LBB0_531 $116(%rip)
	WORD $0x7100121f  // cmp	w16, #4
	WORD $0x540002c3  // b.lo	LBB0_529 $88(%rip)
	WORD $0x121e7611  // and	w17, w16, #0xfffffffc
	WORD $0x0b1101ef  // add	w15, w15, w17
	WORD $0x52800020  // mov	w0, #1
	WORD $0xaa1103e2  // mov	x2, x17
	WORD $0x52800024  // mov	w4, #1
	WORD $0x52800025  // mov	w5, #1
LBB0_527:
	WORD $0x8b0e09ce  // add	x14, x14, x14, lsl #2
	WORD $0xd37ff9ce  // lsl	x14, x14, #1
	WORD $0x8b000800  // add	x0, x0, x0, lsl #2
	WORD $0xd37ff800  // lsl	x0, x0, #1
	WORD $0x8b040884  // add	x4, x4, x4, lsl #2
	WORD $0xd37ff884  // lsl	x4, x4, #1
	WORD $0x8b0508a5  // add	x5, x5, x5, lsl #2
	WORD $0xd37ff8a5  // lsl	x5, x5, #1
	WORD $0x71001042  // subs	w2, w2, #4
	WORD $0x54fffee1  // b.ne	LBB0_527 $-36(%rip)
	WORD $0x9b0e7c0e  // mul	x14, x0, x14
	WORD $0x9b0e7c8e  // mul	x14, x4, x14
	WORD $0x9b0e7cae  // mul	x14, x5, x14
	WORD $0x6b11021f  // cmp	w16, w17
	WORD $0x540000c0  // b.eq	LBB0_531 $24(%rip)
LBB0_529:
	WORD $0x4b0f018f  // sub	w15, w12, w15
LBB0_530:
	WORD $0x8b0e09ce  // add	x14, x14, x14, lsl #2
	WORD $0xd37ff9ce  // lsl	x14, x14, #1
	WORD $0x710005ef  // subs	w15, w15, #1
	WORD $0x54ffffa1  // b.ne	LBB0_530 $-12(%rip)
LBB0_531:
	WORD $0x37f801ec  // tbnz	w12, #31, LBB0_537 $60(%rip)
	WORD $0x6b0c013f  // cmp	w9, w12
	WORD $0x540001ad  // b.le	LBB0_537 $52(%rip)
	WORD $0x8b2c4108  // add	x8, x8, w12, uxtw
	WORD $0x39c0010f  // ldrsb	w15, [x8]
	WORD $0x11000590  // add	w16, w12, #1
	WORD $0x7100d5ff  // cmp	w15, #53
	WORD $0x7a490200  // ccmp	w16, w9, #0, eq
	WORD $0x540000a0  // b.eq	LBB0_535 $20(%rip)
	WORD $0x7100d1ff  // cmp	w15, #52
	WORD $0x1a9fd7e8  // cset	w8, gt
	WORD $0x35000068  // cbnz	w8, LBB0_536 $12(%rip)
	WORD $0x14000003  // b	LBB0_537 $12(%rip)
LBB0_535:
	WORD $0x340001eb  // cbz	w11, LBB0_541 $60(%rip)
LBB0_536:
	WORD $0x910005ce  // add	x14, x14, #1
LBB0_537:
	WORD $0xd2e00408  // mov	x8, #9007199254740992
	WORD $0xeb0801df  // cmp	x14, x8
	WORD $0x540000a1  // b.ne	LBB0_540 $20(%rip)
	WORD $0x710ffdbf  // cmp	w13, #1023
	WORD $0x54ff8660  // b.eq	LBB0_274 $-3892(%rip)
	WORD $0x110005ad  // add	w13, w13, #1
	WORD $0xd2e0020e  // mov	x14, #4503599627370496
LBB0_540:
	WORD $0x9374d1c9  // sbfx	x9, x14, #52, #1
	WORD $0x110ffdab  // add	w11, w13, #1023
	WORD $0x9240cdc8  // and	x8, x14, #0xfffffffffffff
	WORD $0x1200296b  // and	w11, w11, #0x7ff
	WORD $0x8a0bd129  // and	x9, x9, x11, lsl #52
	WORD $0x17fffc32  // b	LBB0_277 $-3896(%rip)
LBB0_541:
	WORD $0x34fffe6c  // cbz	w12, LBB0_537 $-52(%rip)
	WORD $0x385ff108  // ldurb	w8, [x8, #-1]
	WORD $0x12000108  // and	w8, w8, #0x1
	WORD $0x35fffde8  // cbnz	w8, LBB0_536 $-68(%rip)
	WORD $0x17ffffef  // b	LBB0_537 $-68(%rip)
LBB0_543:
	WORD $0xdac001ae  // rbit	x14, x13
	WORD $0xdac011ce  // clz	x14, x14
	WORD $0xcb00016f  // sub	x15, x11, x0
	WORD $0x8b0e01ee  // add	x14, x15, x14
	WORD $0xb100051f  // cmn	x8, #1
	WORD $0x9a8e1108  // csel	x8, x8, x14, ne
LBB0_544:
	WORD $0x0a2a01ae  // bic	w14, w13, w10
	WORD $0x531f79cf  // lsl	w15, w14, #1
	WORD $0x331f79ca  // bfi	w10, w14, #1, #31
	WORD $0x0a2f01ad  // bic	w13, w13, w15
	WORD $0x1201f1ad  // and	w13, w13, #0xaaaaaaaa
	WORD $0x2b0e01ad  // adds	w13, w13, w14
	WORD $0x3200f3ee  // mov	w14, #1431655765
	WORD $0x4a0d05cd  // eor	w13, w14, w13, lsl #1
	WORD $0x0a0a01aa  // and	w10, w13, w10
	WORD $0x1a9f37ed  // cset	w13, hs
	WORD $0x2a2a03ea  // mvn	w10, w10
	WORD $0x8a090149  // and	x9, x10, x9
	WORD $0xaa0d03ea  // mov	x10, x13
	WORD $0xb5ffd8e9  // cbnz	x9, LBB0_470 $-1252(%rip)
LBB0_545:
	WORD $0x9100816b  // add	x11, x11, #32
	WORD $0xaa0c03e9  // mov	x9, x12
LBB0_546:
	WORD $0xb5000c8a  // cbnz	x10, LBB0_576 $400(%rip)
	WORD $0xb40003a9  // cbz	x9, LBB0_556 $116(%rip)
LBB0_548:
	WORD $0xcb0003ea  // neg	x10, x0
LBB0_549:
	WORD $0xd280000d  // mov	x13, #0
LBB0_550:
	WORD $0x386d696c  // ldrb	w12, [x11, x13]
	WORD $0x7100899f  // cmp	w12, #34
	WORD $0x540002c0  // b.eq	LBB0_555 $88(%rip)
	WORD $0x7101719f  // cmp	w12, #92
	WORD $0x540000a0  // b.eq	LBB0_553 $20(%rip)
	WORD $0x910005ad  // add	x13, x13, #1
	WORD $0xeb0d013f  // cmp	x9, x13
	WORD $0x54ffff21  // b.ne	LBB0_550 $-28(%rip)
	WORD $0x14000015  // b	LBB0_557 $84(%rip)
LBB0_553:
	WORD $0xd100052c  // sub	x12, x9, #1
	WORD $0xeb0d019f  // cmp	x12, x13
	WORD $0x54000cc0  // b.eq	LBB0_580 $408(%rip)
	WORD $0x8b0d016b  // add	x11, x11, x13
	WORD $0x8b0a016c  // add	x12, x11, x10
	WORD $0xb100051f  // cmn	x8, #1
	WORD $0x9a880188  // csel	x8, x12, x8, eq
	WORD $0x9100096b  // add	x11, x11, #2
	WORD $0xcb0d012c  // sub	x12, x9, x13
	WORD $0xd100092e  // sub	x14, x9, #2
	WORD $0xd1000989  // sub	x9, x12, #2
	WORD $0x9280000c  // mov	x12, #-1
	WORD $0xeb0d01df  // cmp	x14, x13
	WORD $0x54fffd21  // b.ne	LBB0_549 $-92(%rip)
	WORD $0x14000047  // b	LBB0_575 $284(%rip)
LBB0_555:
	WORD $0x8b0d0169  // add	x9, x11, x13
	WORD $0x9100052b  // add	x11, x9, #1
LBB0_556:
	WORD $0xcb00016c  // sub	x12, x11, x0
	WORD $0xb6ff3bac  // tbz	x12, #63, LBB0_134 $-6284(%rip)
	WORD $0x14000042  // b	LBB0_575 $264(%rip)
LBB0_557:
	WORD $0x7100899f  // cmp	w12, #34
	WORD $0x54000a61  // b.ne	LBB0_580 $332(%rip)
	WORD $0x8b0d016b  // add	x11, x11, x13
	WORD $0x17fffffa  // b	LBB0_556 $-24(%rip)
LBB0_559:
	WORD $0xdac001cf  // rbit	x15, x14
	WORD $0xdac011ef  // clz	x15, x15
	WORD $0xcb000170  // sub	x16, x11, x0
	WORD $0x8b0f020f  // add	x15, x16, x15
	WORD $0xb100051f  // cmn	x8, #1
	WORD $0x9a8f1108  // csel	x8, x8, x15, ne
LBB0_560:
	WORD $0x0a2901cf  // bic	w15, w14, w9
	WORD $0x531f79f0  // lsl	w16, w15, #1
	WORD $0x331f79e9  // bfi	w9, w15, #1, #31
	WORD $0x0a3001ce  // bic	w14, w14, w16
	WORD $0x1201f1ce  // and	w14, w14, #0xaaaaaaaa
	WORD $0x2b0f01ce  // adds	w14, w14, w15
	WORD $0x3200f3ef  // mov	w15, #1431655765
	WORD $0x4a0e05ee  // eor	w14, w15, w14, lsl #1
	WORD $0x0a0901c9  // and	w9, w14, w9
	WORD $0x1a9f37ee  // cset	w14, hs
	WORD $0x2a2903e9  // mvn	w9, w9
	WORD $0x8a0a012a  // and	x10, x9, x10
	WORD $0xaa0e03e9  // mov	x9, x14
	WORD $0xb5ffd80a  // cbnz	x10, LBB0_476 $-1280(%rip)
LBB0_561:
	WORD $0x3500044d  // cbnz	w13, LBB0_573 $136(%rip)
	WORD $0x9100816b  // add	x11, x11, #32
	WORD $0xaa0c03ea  // mov	x10, x12
LBB0_563:
	WORD $0xb5000629  // cbnz	x9, LBB0_578 $196(%rip)
	WORD $0xb400070a  // cbz	x10, LBB0_580 $224(%rip)
LBB0_565:
	WORD $0xcb0003e9  // neg	x9, x0
LBB0_566:
	WORD $0xd280000d  // mov	x13, #0
LBB0_567:
	WORD $0x386d696c  // ldrb	w12, [x11, x13]
	WORD $0x7100899f  // cmp	w12, #34
	WORD $0x54000380  // b.eq	LBB0_574 $112(%rip)
	WORD $0x7101719f  // cmp	w12, #92
	WORD $0x54000100  // b.eq	LBB0_571 $32(%rip)
	WORD $0x7100819f  // cmp	w12, #32
	WORD $0x540002a3  // b.lo	LBB0_573 $84(%rip)
	WORD $0x910005ad  // add	x13, x13, #1
	WORD $0x9280000c  // mov	x12, #-1
	WORD $0xeb0d015f  // cmp	x10, x13
	WORD $0x54fffec1  // b.ne	LBB0_567 $-40(%rip)
	WORD $0x14000017  // b	LBB0_575 $92(%rip)
LBB0_571:
	WORD $0xd100054c  // sub	x12, x10, #1
	WORD $0xeb0d019f  // cmp	x12, x13
	WORD $0x540004e0  // b.eq	LBB0_580 $156(%rip)
	WORD $0x8b0d016b  // add	x11, x11, x13
	WORD $0x8b09016c  // add	x12, x11, x9
	WORD $0xb100051f  // cmn	x8, #1
	WORD $0x9a880188  // csel	x8, x12, x8, eq
	WORD $0x9100096b  // add	x11, x11, #2
	WORD $0xd100094e  // sub	x14, x10, #2
	WORD $0xcb0d014a  // sub	x10, x10, x13
	WORD $0xd100094a  // sub	x10, x10, #2
	WORD $0x9280000c  // mov	x12, #-1
	WORD $0xeb0d01df  // cmp	x14, x13
	WORD $0x54fffcc1  // b.ne	LBB0_566 $-104(%rip)
	WORD $0x14000008  // b	LBB0_575 $32(%rip)
LBB0_573:
	WORD $0x9280002c  // mov	x12, #-2
	WORD $0xf900006c  // str	x12, [x3]
	WORD $0x17fff76a  // b	LBB0_24 $-8792(%rip)
LBB0_574:
	WORD $0xcb000169  // sub	x9, x11, x0
	WORD $0x8b0d0129  // add	x9, x9, x13
	WORD $0x9100052c  // add	x12, x9, #1
	WORD $0xb6ff336c  // tbz	x12, #63, LBB0_134 $-6548(%rip)
LBB0_575:
	WORD $0xf900006c  // str	x12, [x3]
	WORD $0x17fff764  // b	LBB0_24 $-8816(%rip)
LBB0_576:
	WORD $0xb4000229  // cbz	x9, LBB0_580 $68(%rip)
	WORD $0xaa2003ea  // mvn	x10, x0
	WORD $0x8b0a016a  // add	x10, x11, x10
	WORD $0xb100051f  // cmn	x8, #1
	WORD $0x9a880148  // csel	x8, x10, x8, eq
	WORD $0x9100056b  // add	x11, x11, #1
	WORD $0xd1000529  // sub	x9, x9, #1
	WORD $0xb5fff2e9  // cbnz	x9, LBB0_548 $-420(%rip)
	WORD $0x17ffffb2  // b	LBB0_556 $-312(%rip)
LBB0_578:
	WORD $0xb400010a  // cbz	x10, LBB0_580 $32(%rip)
	WORD $0xaa2003e9  // mvn	x9, x0
	WORD $0x8b090169  // add	x9, x11, x9
	WORD $0xb100051f  // cmn	x8, #1
	WORD $0x9a880128  // csel	x8, x9, x8, eq
	WORD $0x9100056b  // add	x11, x11, #1
	WORD $0xd100054a  // sub	x10, x10, #1
	WORD $0xb5fff94a  // cbnz	x10, LBB0_565 $-216(%rip)
LBB0_580:
	WORD $0x9280000c  // mov	x12, #-1
	WORD $0xf900006c  // str	x12, [x3]
	WORD $0x17fff750  // b	LBB0_24 $-8896(%rip)
	  // .p2align 1, 0x00
LJTI0_0:
	WORD $0x01910000
	WORD $0x01910191
	WORD $0x01910191
	WORD $0x01910191
	WORD $0x01910191
	WORD $0x01910191
	WORD $0x01910191
	WORD $0x01910191
	WORD $0x01910191
	WORD $0x01910191
	WORD $0x01910191
	WORD $0x01910191
	WORD $0x01910191
	WORD $0x01910191
	WORD $0x01910191
	WORD $0x01910191
	WORD $0x01910191
	WORD $0x01910123
	WORD $0x01910191
	WORD $0x01910191
	WORD $0x01910191
	WORD $0x01910191
	WORD $0x0019011f
	WORD $0x01910191
	WORD $0x00190019
	WORD $0x00190019
	WORD $0x00190019
	WORD $0x00190019
	WORD $0x00190019
	WORD $0x01910195
	WORD $0x01910191
	WORD $0x01910191
	WORD $0x01910191
	WORD $0x01910191
	WORD $0x01910191
	WORD $0x01910191
	WORD $0x01910191
	WORD $0x01910191
	WORD $0x01910191
	WORD $0x01910191
	WORD $0x01910191
	WORD $0x01910191
	WORD $0x01910191
	WORD $0x01910191
	WORD $0x01910191
	WORD $0x01990191
	WORD $0x01a70191
	WORD $0x01910191
	WORD $0x01910191
	WORD $0x01910191
	WORD $0x01910191
	WORD $0x0191019b
	WORD $0x01910191
	WORD $0x01910191
	WORD $0x01910191
	WORD $0x0191010e
	WORD $0x01910191
	WORD $0x01910191
	WORD $0x01910184
	WORD $0x01910191
	WORD $0x01910191
	WORD $0x01af0191
	WORD $0x011b0191
	// // .word (LBB0_22-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_95-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_94-LBB0_22)>>2
// .word (LBB0_29-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_29-LBB0_22)>>2
// .word (LBB0_29-LBB0_22)>>2
// .word (LBB0_29-LBB0_22)>>2
// .word (LBB0_29-LBB0_22)>>2
// .word (LBB0_29-LBB0_22)>>2
// .word (LBB0_29-LBB0_22)>>2
// .word (LBB0_29-LBB0_22)>>2
// .word (LBB0_29-LBB0_22)>>2
// .word (LBB0_29-LBB0_22)>>2
// .word (LBB0_29-LBB0_22)>>2
// .word (LBB0_110-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_111-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_115-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_112-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_90-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_106-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_117-LBB0_22)>>2
// .word (LBB0_109-LBB0_22)>>2
// .word (LBB0_93-LBB0_22)>>2

_MASK_USE_NUMBER:
	WORD $0x00000002  // .long 2
	WORD $0x00000000  // .p2align 3, 0x00
_P10_TAB:
	WORD $0x00000000; WORD $0x3ff00000  // .quad 0x3ff0000000000000
	WORD $0x00000000; WORD $0x40240000  // .quad 0x4024000000000000
	WORD $0x00000000; WORD $0x40590000  // .quad 0x4059000000000000
	WORD $0x00000000; WORD $0x408f4000  // .quad 0x408f400000000000
	WORD $0x00000000; WORD $0x40c38800  // .quad 0x40c3880000000000
	WORD $0x00000000; WORD $0x40f86a00  // .quad 0x40f86a0000000000
	WORD $0x00000000; WORD $0x412e8480  // .quad 0x412e848000000000
	WORD $0x00000000; WORD $0x416312d0  // .quad 0x416312d000000000
	WORD $0x00000000; WORD $0x4197d784  // .quad 0x4197d78400000000
	WORD $0x00000000; WORD $0x41cdcd65  // .quad 0x41cdcd6500000000
	WORD $0x20000000; WORD $0x4202a05f  // .quad 0x4202a05f20000000
	WORD $0xe8000000; WORD $0x42374876  // .quad 0x42374876e8000000
	WORD $0xa2000000; WORD $0x426d1a94  // .quad 0x426d1a94a2000000
	WORD $0xe5400000; WORD $0x42a2309c  // .quad 0x42a2309ce5400000
	WORD $0x1e900000; WORD $0x42d6bcc4  // .quad 0x42d6bcc41e900000
	WORD $0x26340000; WORD $0x430c6bf5  // .quad 0x430c6bf526340000
	WORD $0x37e08000; WORD $0x4341c379  // .quad 0x4341c37937e08000
	WORD $0x85d8a000; WORD $0x43763457  // .quad 0x4376345785d8a000
	WORD $0x674ec800; WORD $0x43abc16d  // .quad 0x43abc16d674ec800
	WORD $0x60913d00; WORD $0x43e158e4  // .quad 0x43e158e460913d00
	WORD $0x78b58c40; WORD $0x4415af1d  // .quad 0x4415af1d78b58c40
	WORD $0xd6e2ef50; WORD $0x444b1ae4  // .quad 0x444b1ae4d6e2ef50
	WORD $0x064dd592; WORD $0x4480f0cf  // .quad 0x4480f0cf064dd592
	  // .p2align 3, 0x00
_POW10_M128_TAB:
	WORD $0xcd60e453; WORD $0x1732c869  // .quad 1671618768450675795
	WORD $0x081c0288; WORD $0xfa8fd5a0  // .quad -391859759250406776
	WORD $0x205c8eb4; WORD $0x0e7fbd42  // .quad 1044761730281672372
	WORD $0x05118195; WORD $0x9c99e584  // .quad -7162441377172586091
	WORD $0xa873b261; WORD $0x521fac92  // .quad 5917638181279478369
	WORD $0x0655e1fa; WORD $0xc3c05ee5  // .quad -4341365703038344710
	WORD $0x52909ef9; WORD $0xe6a797b7  // .quad -1826324310255427847
	WORD $0x47eb5a78; WORD $0xf4b0769e  // .quad -815021110370542984
	WORD $0x939a635c; WORD $0x9028bed2  // .quad -8058981721550724260
	WORD $0xecf3188b; WORD $0x98ee4a22  // .quad -7426917221622671221
	WORD $0x3880fc33; WORD $0x7432ee87  // .quad 8373016921771146291
	WORD $0xa82fdeae; WORD $0xbf29dcab  // .quad -4671960508600951122
	WORD $0x06a13b3f; WORD $0x113faa29  // .quad 1242899115359157055
	WORD $0x923bd65a; WORD $0xeef453d6  // .quad -1228264617323800998
	WORD $0xa424c507; WORD $0x4ac7ca59  // .quad 5388497965526861063
	WORD $0x1b6565f8; WORD $0x9558b466  // .quad -7685194413468457480
	WORD $0x0d2df649; WORD $0x5d79bcf0  // .quad 6735622456908576329
	WORD $0xa23ebf76; WORD $0xbaaee17f  // .quad -4994806998408183946
	WORD $0x107973dc; WORD $0xf4d82c2c  // .quad -803843965719055396
	WORD $0x8ace6f53; WORD $0xe95a99df  // .quad -1631822729582842029
	WORD $0x8a4be869; WORD $0x79071b9b  // .quad 8720969558280366185
	WORD $0xb6c10594; WORD $0x91d8a02b  // .quad -7937418233630358124
	WORD $0x6cdee284; WORD $0x9748e282  // .quad -7545532125859093884
	WORD $0xa47146f9; WORD $0xb64ec836  // .quad -5310086773610559751
	WORD $0x08169b25; WORD $0xfd1b1b23  // .quad -208543120469091547
	WORD $0x4d8d98b7; WORD $0xe3e27a44  // .quad -2025922448585811785
	WORD $0xe50e20f7; WORD $0xfe30f0f5  // .quad -130339450293182217
	WORD $0xb0787f72; WORD $0x8e6d8c6a  // .quad -8183730558007214222
	WORD $0x5e51a935; WORD $0xbdbd2d33  // .quad -4774610331293865675
	WORD $0x5c969f4f; WORD $0xb208ef85  // .quad -5617977179081629873
	WORD $0x35e61382; WORD $0xad2c7880  // .quad -5968262914117332094
	WORD $0xb3bc4723; WORD $0xde8b2b66  // .quad -2410785455424649437
	WORD $0x21afcc31; WORD $0x4c3bcb50  // .quad 5493207715531443249
	WORD $0x3055ac76; WORD $0x8b16fb20  // .quad -8424269937281487754
	WORD $0x2a1bbf3d; WORD $0xdf4abe24  // .quad -2356862392440471747
	WORD $0x3c6b1793; WORD $0xaddcb9e8  // .quad -5918651403174471789
	WORD $0x34a2af0d; WORD $0xd71d6dad  // .quad -2946077990550589683
	WORD $0x4b85dd78; WORD $0xd953e862  // .quad -2786628235540701832
	WORD $0x40e5ad68; WORD $0x8672648c  // .quad -8758827771735200408
	WORD $0x6f33aa6b; WORD $0x87d4713d  // .quad -8659171674854020501
	WORD $0x511f18c2; WORD $0x680efdaf  // .quad 7498209359040551106
	WORD $0xcb009506; WORD $0xa9c98d8c  // .quad -6212278575140137722
	WORD $0x2566def2; WORD $0x0212bd1b  // .quad 149389661945913074
	WORD $0xfdc0ba48; WORD $0xd43bf0ef  // .quad -3153662200497784248
	WORD $0xf7604b57; WORD $0x014bb630  // .quad 93368538716195671
	WORD $0xfe98746d; WORD $0x84a57695  // .quad -8888567902952197011
	WORD $0x35385e2d; WORD $0x419ea3bd  // .quad 4728396691822632493
	WORD $0x7e3e9188; WORD $0xa5ced43b  // .quad -6499023860262858360
	WORD $0x828675b9; WORD $0x52064cac  // .quad 5910495864778290617
	WORD $0x5dce35ea; WORD $0xcf42894a  // .quad -3512093806901185046
	WORD $0xd1940993; WORD $0x7343efeb  // .quad 8305745933913819539
	WORD $0x7aa0e1b2; WORD $0x818995ce  // .quad -9112587656954322510
	WORD $0xc5f90bf8; WORD $0x1014ebe6  // .quad 1158810380537498616
	WORD $0x19491a1f; WORD $0xa1ebfb42  // .quad -6779048552765515233
	WORD $0x77774ef6; WORD $0xd41a26e0  // .quad -3163173042755514634
	WORD $0x9f9b60a6; WORD $0xca66fa12  // .quad -3862124672529506138
	WORD $0x955522b4; WORD $0x8920b098  // .quad -8565652321871781196
	WORD $0x478238d0; WORD $0xfd00b897  // .quad -215969822234494768
	WORD $0x5d5535b0; WORD $0x55b46e5f  // .quad 6175682344898606512
	WORD $0x8cb16382; WORD $0x9e20735e  // .quad -7052510166537641086
	WORD $0x34aa831d; WORD $0xeb2189f7  // .quad -1503769105731517667
	WORD $0x2fddbc62; WORD $0xc5a89036  // .quad -4203951689744663454
	WORD $0x01d523e4; WORD $0xa5e9ec75  // .quad -6491397400591784988
	WORD $0xbbd52b7b; WORD $0xf712b443  // .quad -643253593753441413
	WORD $0x2125366e; WORD $0x47b233c9  // .quad 5166248661484910190
	WORD $0x55653b2d; WORD $0x9a6bb0aa  // .quad -7319562523736982739
	WORD $0x696e840a; WORD $0x999ec0bb  // .quad -7377247228426025974
	WORD $0xeabe89f8; WORD $0xc1069cd4  // .quad -4537767136243840520
	WORD $0x43ca250d; WORD $0xc00670ea  // .quad -4609873017105144563
	WORD $0x256e2c76; WORD $0xf148440a  // .quad -1060522901877412746
	WORD $0x6a5e5728; WORD $0x38040692  // .quad 4036358391950366504
	WORD $0x5764dbca; WORD $0x96cd2a86  // .quad -7580355841314464822
	WORD $0x04f5ecf2; WORD $0xc6050837  // .quad -4177924046916817678
	WORD $0xed3e12bc; WORD $0xbc807527  // .quad -4863758783215693124
	WORD $0xc633682e; WORD $0xf7864a44  // .quad -610719040218634194
	WORD $0xe88d976b; WORD $0xeba09271  // .quad -1468012460592228501
	WORD $0xfbe0211d; WORD $0x7ab3ee6a  // .quad 8841672636718129437
	WORD $0x31587ea3; WORD $0x93445b87  // .quad -7835036815511224669
	WORD $0xbad82964; WORD $0x5960ea05  // .quad 6440404777470273892
	WORD $0xfdae9e4c; WORD $0xb8157268  // .quad -5182110000961642932
	WORD $0x298e33bd; WORD $0x6fb92487  // .quad 8050505971837842365
	WORD $0x3d1a45df; WORD $0xe61acf03  // .quad -1865951482774665761
	WORD $0x79f8e056; WORD $0xa5d3b6d4  // .quad -6497648813669818282
	WORD $0x06306bab; WORD $0x8fd0c162  // .quad -8083748704375247957
	WORD $0x9877186c; WORD $0x8f48a489  // .quad -8122061017087272852
	WORD $0x87bc8696; WORD $0xb3c4f1ba  // .quad -5492999862041672042
	WORD $0xfe94de87; WORD $0x331acdab  // .quad 3682481783923072647
	WORD $0x29aba83c; WORD $0xe0b62e29  // .quad -2254563809124702148
	WORD $0x7f1d0b14; WORD $0x9ff0c08b  // .quad -6921820921902855404
	WORD $0xba0b4925; WORD $0x8c71dcd9  // .quad -8326631408344020699
	WORD $0x5ee44dd9; WORD $0x07ecf0ae  // .quad 571095884476206553
	WORD $0x288e1b6f; WORD $0xaf8e5410  // .quad -5796603242002637969
	WORD $0xf69d6150; WORD $0xc9e82cd9  // .quad -3897816162832129712
	WORD $0x32b1a24a; WORD $0xdb71e914  // .quad -2634068034075909558
	WORD $0x3a225cd2; WORD $0xbe311c08  // .quad -4741978110983775022
	WORD $0x9faf056e; WORD $0x892731ac  // .quad -8563821548938525330
	WORD $0x48aaf406; WORD $0x6dbd630a  // .quad 7907585416552444934
	WORD $0xc79ac6ca; WORD $0xab70fe17  // .quad -6093090917745768758
	WORD $0xdad5b108; WORD $0x092cbbcc  // .quad 661109733835780360
	WORD $0xb981787d; WORD $0xd64d3d9d  // .quad -3004677628754823043
	WORD $0x08c58ea5; WORD $0x25bbf560  // .quad 2719036592861056677
	WORD $0x93f0eb4e; WORD $0x85f04682  // .quad -8795452545612846258
	WORD $0x0af6f24e; WORD $0xaf2af2b8  // .quad -5824576295778454962
	WORD $0x38ed2621; WORD $0xa76c5823  // .quad -6382629663588669919
	WORD $0x0db4aee1; WORD $0x1af5af66  // .quad 1942651667131707105
	WORD $0x07286faa; WORD $0xd1476e2c  // .quad -3366601061058449494
	WORD $0xc890ed4d; WORD $0x50d98d9f  // .quad 5825843310384704845
	WORD $0x847945ca; WORD $0x82cca4db  // .quad -9021654690802612790
	WORD $0xbab528a0; WORD $0xe50ff107  // .quad -1941067898873894752
	WORD $0x6597973c; WORD $0xa37fce12  // .quad -6665382345075878084
	WORD $0xa96272c8; WORD $0x1e53ed49  // .quad 2185351144835019464
	WORD $0xfefd7d0c; WORD $0xcc5fc196  // .quad -3720041912917459700
	WORD $0x13bb0f7a; WORD $0x25e8e89c  // .quad 2731688931043774330
	WORD $0xbebcdc4f; WORD $0xff77b1fc  // .quad -38366372719436721
	WORD $0x8c54e9ac; WORD $0x77b19161  // .quad 8624834609543440812
	WORD $0xf73609b1; WORD $0x9faacf3d  // .quad -6941508010590729807
	WORD $0xef6a2417; WORD $0xd59df5b9  // .quad -3054014793352862697
	WORD $0x75038c1d; WORD $0xc795830d  // .quad -4065198994811024355
	WORD $0x6b44ad1d; WORD $0x4b057328  // .quad 5405853545163697437
	WORD $0xd2446f25; WORD $0xf97ae3d0  // .quad -469812725086392539
	WORD $0x430aec32; WORD $0x4ee367f9  // .quad 5684501474941004850
	WORD $0x836ac577; WORD $0x9becce62  // .quad -7211161980820077193
	WORD $0x93cda73f; WORD $0x229c41f7  // .quad 2493940825248868159
	WORD $0x244576d5; WORD $0xc2e801fb  // .quad -4402266457597708587
	WORD $0x78c1110f; WORD $0x6b435275  // .quad 7729112049988473103
	WORD $0xed56d48a; WORD $0xf3a20279  // .quad -891147053569747830
	WORD $0x6b78aaa9; WORD $0x830a1389  // .quad -9004363024039368023
	WORD $0x345644d6; WORD $0x9845418c  // .quad -7474495936122174250
	WORD $0xc656d553; WORD $0x23cc986b  // .quad 2579604275232953683
	WORD $0x416bd60c; WORD $0xbe5691ef  // .quad -4731433901725329908
	WORD $0xb7ec8aa8; WORD $0x2cbfbe86  // .quad 3224505344041192104
	WORD $0x11c6cb8f; WORD $0xedec366b  // .quad -1302606358729274481
	WORD $0x32f3d6a9; WORD $0x7bf7d714  // .quad 8932844867666826921
	WORD $0xeb1c3f39; WORD $0x94b3a202  // .quad -7731658001846878407
	WORD $0x3fb0cc53; WORD $0xdaf5ccd9  // .quad -2669001970698630061
	WORD $0xa5e34f07; WORD $0xb9e08a83  // .quad -5052886483881210105
	WORD $0x8f9cff68; WORD $0xd1b3400f  // .quad -3336252463373287576
	WORD $0x8f5c22c9; WORD $0xe858ad24  // .quad -1704422086424124727
	WORD $0xb9c21fa1; WORD $0x23100809  // .quad 2526528228819083169
	WORD $0xd99995be; WORD $0x91376c36  // .quad -7982792831656159810
	WORD $0x2832a78a; WORD $0xabd40a0c  // .quad -6065211750830921846
	WORD $0x8ffffb2d; WORD $0xb5854744  // .quad -5366805021142811859
	WORD $0x323f516c; WORD $0x16c90c8f  // .quad 1641857348316123500
	WORD $0xb3fff9f9; WORD $0xe2e69915  // .quad -2096820258001126919
	WORD $0x7f6792e3; WORD $0xae3da7d9  // .quad -5891368184943504669
	WORD $0x907ffc3b; WORD $0x8dd01fad  // .quad -8228041688891786181
	WORD $0xdf41779c; WORD $0x99cd11cf  // .quad -7364210231179380836
	WORD $0xf49ffb4a; WORD $0xb1442798  // .quad -5673366092687344822
	WORD $0xd711d583; WORD $0x40405643  // .quad 4629795266307937667
	WORD $0x31c7fa1d; WORD $0xdd95317f  // .quad -2480021597431793123
	WORD $0x666b2572; WORD $0x482835ea  // .quad 5199465050656154994
	WORD $0x7f1cfc52; WORD $0x8a7d3eef  // .quad -8467542526035952558
	WORD $0x0005eecf; WORD $0xda324365  // .quad -2724040723534582065
	WORD $0x5ee43b66; WORD $0xad1c8eab  // .quad -5972742139117552794
	WORD $0x40076a82; WORD $0x90bed43e  // .quad -8016736922845615486
	WORD $0x369d4a40; WORD $0xd863b256  // .quad -2854241655469553088
	WORD $0xe804a291; WORD $0x5a7744a6  // .quad 6518754469289960081
	WORD $0xe2224e68; WORD $0x873e4f75  // .quad -8701430062309552536
	WORD $0xa205cb36; WORD $0x711515d0  // .quad 8148443086612450102
	WORD $0x5aaae202; WORD $0xa90de353  // .quad -6265101559459552766
	WORD $0xca873e03; WORD $0x0d5a5b44  // .quad 962181821410786819
	WORD $0x31559a83; WORD $0xd3515c28  // .quad -3219690930897053053
	WORD $0xfe9486c2; WORD $0xe858790a  // .quad -1704479370831952190
	WORD $0x1ed58091; WORD $0x8412d999  // .quad -8929835859451740015
	WORD $0xbe39a872; WORD $0x626e974d  // .quad 7092772823314835570
	WORD $0x668ae0b6; WORD $0xa5178fff  // .quad -6550608805887287114
	WORD $0x2dc8128f; WORD $0xfb0a3d21  // .quad -357406007711231345
	WORD $0x402d98e3; WORD $0xce5d73ff  // .quad -3576574988931720989
	WORD $0xbc9d0b99; WORD $0x7ce66634  // .quad 8999993282035256217
	WORD $0x881c7f8e; WORD $0x80fa687f  // .quad -9152888395723407474
	WORD $0xebc44e80; WORD $0x1c1fffc1  // .quad 2026619565689294464
	WORD $0x6a239f72; WORD $0xa139029f  // .quad -6829424476226871438
	WORD $0x66b56220; WORD $0xa327ffb2  // .quad -6690097579743157728
	WORD $0x44ac874e; WORD $0xc9874347  // .quad -3925094576856201394
	WORD $0x0062baa8; WORD $0x4bf1ff9f  // .quad 5472436080603216552
	WORD $0x15d7a922; WORD $0xfbe91419  // .quad -294682202642863838
	WORD $0x603db4a9; WORD $0x6f773fc3  // .quad 8031958568804398249
	WORD $0xada6c9b5; WORD $0x9d71ac8f  // .quad -7101705404292871755
	WORD $0x384d21d3; WORD $0xcb550fb4  // .quad -3795109844276665901
	WORD $0x99107c22; WORD $0xc4ce17b3  // .quad -4265445736938701790
	WORD $0x46606a48; WORD $0x7e2a53a1  // .quad 9091170749936331336
	WORD $0x7f549b2b; WORD $0xf6019da0  // .quad -720121152745989333
	WORD $0xcbfc426d; WORD $0x2eda7444  // .quad 3376138709496513133
	WORD $0x4f94e0fb; WORD $0x99c10284  // .quad -7367604748107325189
	WORD $0xfefb5308; WORD $0xfa911155  // .quad -391512631556746488
	WORD $0x637a1939; WORD $0xc0314325  // .quad -4597819916706768583
	WORD $0x7eba27ca; WORD $0x793555ab  // .quad 8733981247408842698
	WORD $0xbc589f88; WORD $0xf03d93ee  // .quad -1135588877456072824
	WORD $0x2f3458de; WORD $0x4bc1558b  // .quad 5458738279630526686
	WORD $0x35b763b5; WORD $0x96267c75  // .quad -7627272076051127371
	WORD $0xfb016f16; WORD $0x9eb1aaed  // .quad -7011635205744005354
	WORD $0x83253ca2; WORD $0xbbb01b92  // .quad -4922404076636521310
	WORD $0x79c1cadc; WORD $0x465e15a9  // .quad 5070514048102157020
	WORD $0x23ee8bcb; WORD $0xea9c2277  // .quad -1541319077368263733
	WORD $0xec191ec9; WORD $0x0bfacd89  // .quad 863228270850154185
	WORD $0x7675175f; WORD $0x92a1958a  // .quad -7880853450996246689
	WORD $0x671f667b; WORD $0xcef980ec  // .quad -3532650679864695173
	WORD $0x14125d36; WORD $0xb749faed  // .quad -5239380795317920458
	WORD $0x80e7401a; WORD $0x82b7e127  // .quad -9027499368258256870
	WORD $0x5916f484; WORD $0xe51c79a8  // .quad -1937539975720012668
	WORD $0xb0908810; WORD $0xd1b2ecb8  // .quad -3336344095947716592
	WORD $0x37ae58d2; WORD $0x8f31cc09  // .quad -8128491512466089774
	WORD $0xdcb4aa15; WORD $0x861fa7e6  // .quad -8782116138362033643
	WORD $0x8599ef07; WORD $0xb2fe3f0b  // .quad -5548928372155224313
	WORD $0x93e1d49a; WORD $0x67a791e0  // .quad 7469098900757009562
	WORD $0x67006ac9; WORD $0xdfbdcece  // .quad -2324474446766642487
	WORD $0x5c6d24e0; WORD $0xe0c8bb2c  // .quad -2249342214667950880
	WORD $0x006042bd; WORD $0x8bd6a141  // .quad -8370325556870233411
	WORD $0x73886e18; WORD $0x58fae9f7  // .quad 6411694268519837208
	WORD $0x4078536d; WORD $0xaecc4991  // .quad -5851220927660403859
	WORD $0x506a899e; WORD $0xaf39a475  // .quad -5820440219632367202
	WORD $0x90966848; WORD $0xda7f5bf5  // .quad -2702340141148116920
	WORD $0x52429603; WORD $0x6d8406c9  // .quad 7891439908798240259
	WORD $0x7a5e012d; WORD $0x888f9979  // .quad -8606491615858654931
	WORD $0xa6d33b83; WORD $0xc8e5087b  // .quad -3970758169284363389
	WORD $0xd8f58178; WORD $0xaab37fd7  // .quad -6146428501395930760
	WORD $0x90880a64; WORD $0xfb1e4a9a  // .quad -351761693178066332
	WORD $0xcf32e1d6; WORD $0xd5605fcd  // .quad -3071349608317525546
	WORD $0x9a55067f; WORD $0x5cf2eea0  // .quad 6697677969404790399
	WORD $0xa17fcd26; WORD $0x855c3be0  // .quad -8837122532839535322
	WORD $0xc0ea481e; WORD $0xf42faa48  // .quad -851274575098787810
	WORD $0xc9dfc06f; WORD $0xa6b34ad8  // .quad -6434717147622031249
	WORD $0xf124da26; WORD $0xf13b94da  // .quad -1064093218873484762
	WORD $0xfc57b08b; WORD $0xd0601d8e  // .quad -3431710416100151157
	WORD $0xd6b70858; WORD $0x76c53d08  // .quad 8558313775058847832
	WORD $0x5db6ce57; WORD $0x823c1279  // .quad -9062348037703676329
	WORD $0x0c64ca6e; WORD $0x54768c4b  // .quad 6086206200396171886
	WORD $0xb52481ed; WORD $0xa2cb1717  // .quad -6716249028702207507
	WORD $0xcf7dfd09; WORD $0xa9942f5d  // .quad -6227300304786948855
	WORD $0xa26da268; WORD $0xcb7ddcdd  // .quad -3783625267450371480
	WORD $0x435d7c4c; WORD $0xd3f93b35  // .quad -3172439362556298164
	WORD $0x0b090b02; WORD $0xfe5d5415  // .quad -117845565885576446
	WORD $0x4a1a6daf; WORD $0xc47bc501  // .quad -4288617610811380305
	WORD $0x26e5a6e1; WORD $0x9efa548d  // .quad -6991182506319567135
	WORD $0x9ca1091b; WORD $0x359ab641  // .quad 3862600023340550427
	WORD $0x709f109a; WORD $0xc6b8e9b0  // .quad -4127292114472071014
	WORD $0x03c94b62; WORD $0xc30163d2  // .quad -4395122007679087774
	WORD $0x8cc6d4c0; WORD $0xf867241c  // .quad -547429124662700864
	WORD $0x425dcf1d; WORD $0x79e0de63  // .quad 8782263791269039901
	WORD $0xd7fc44f8; WORD $0x9b407691  // .quad -7259672230555269896
	WORD $0x12f542e4; WORD $0x985915fc  // .quad -7468914334623251740
	WORD $0x4dfb5636; WORD $0xc2109436  // .quad -4462904269766699466
	WORD $0x17b2939d; WORD $0x3e6f5b7b  // .quad 4498915137003099037
	WORD $0xe17a2bc4; WORD $0xf294b943  // .quad -966944318780986428
	WORD $0xeecf9c42; WORD $0xa705992c  // .quad -6411550076227838910
	WORD $0x6cec5b5a; WORD $0x979cf3ca  // .quad -7521869226879198374
	WORD $0x2a838353; WORD $0x50c6ff78  // .quad 5820620459997365075
	WORD $0x08277231; WORD $0xbd8430bd  // .quad -4790650515171610063
	WORD $0x35246428; WORD $0xa4f8bf56  // .quad -6559282480285457368
	WORD $0x4a314ebd; WORD $0xece53cec  // .quad -1376627125537124675
	WORD $0xe136be99; WORD $0x871b7795  // .quad -8711237568605798759
	WORD $0xae5ed136; WORD $0x940f4613  // .quad -7777920981101784778
	WORD $0x59846e3f; WORD $0x28e2557b  // .quad 2946011094524915263
	WORD $0x99f68584; WORD $0xb9131798  // .quad -5110715207949843068
	WORD $0x2fe589cf; WORD $0x331aeada  // .quad 3682513868156144079
	WORD $0xc07426e5; WORD $0xe757dd7e  // .quad -1776707991509915931
	WORD $0x5def7621; WORD $0x3ff0d2c8  // .quad 4607414176811284001
	WORD $0x3848984f; WORD $0x9096ea6f  // .quad -8027971522334779313
	WORD $0x756b53a9; WORD $0x0fed077a  // .quad 1147581702586717097
	WORD $0x065abe63; WORD $0xb4bca50b  // .quad -5423278384491086237
	WORD $0x12c62894; WORD $0xd3e84959  // .quad -3177208890193991532
	WORD $0xc7f16dfb; WORD $0xe1ebce4d  // .quad -2167411962186469893
	WORD $0xabbbd95c; WORD $0x64712dd7  // .quad 7237616480483531100
	WORD $0x9cf6e4bd; WORD $0x8d3360f0  // .quad -8272161504007625539
	WORD $0x96aacfb3; WORD $0xbd8d794d  // .quad -4788037454677749837
	WORD $0xc4349dec; WORD $0xb080392c  // .quad -5728515861582144020
	WORD $0xfc5583a0; WORD $0xecf0d7a0  // .quad -1373360799919799392
	WORD $0xf541c567; WORD $0xdca04777  // .quad -2548958808550292121
	WORD $0x9db57244; WORD $0xf41686c4  // .quad -858350499949874620
	WORD $0xf9491b60; WORD $0x89e42caa  // .quad -8510628282985014432
	WORD $0xc522ced5; WORD $0x311c2875  // .quad 3538747893490044629
	WORD $0xb79b6239; WORD $0xac5d37d5  // .quad -6026599335303880135
	WORD $0x366b828b; WORD $0x7d633293  // .quad 9035120885289943691
	WORD $0x25823ac7; WORD $0xd77485cb  // .quad -2921563150702462265
	WORD $0x02033197; WORD $0xae5dff9c  // .quad -5882264492762254953
	WORD $0xf77164bc; WORD $0x86a8d39e  // .quad -8743505996830120772
	WORD $0x0283fdfc; WORD $0xd9f57f83  // .quad -2741144597525430788
	WORD $0xb54dbdeb; WORD $0xa8530886  // .quad -6317696477610263061
	WORD $0xc324fd7b; WORD $0xd072df63  // .quad -3426430746906788485
	WORD $0x62a12d66; WORD $0xd267caa8  // .quad -3285434578585440922
	WORD $0x59f71e6d; WORD $0x4247cb9e  // .quad 4776009810824339053
	WORD $0x3da4bc60; WORD $0x8380dea9  // .quad -8970925639256982432
	WORD $0xf074e608; WORD $0x52d9be85  // .quad 5970012263530423816
	WORD $0x8d0deb78; WORD $0xa4611653  // .quad -6601971030643840136
	WORD $0x6c921f8b; WORD $0x67902e27  // .quad 7462515329413029771
	WORD $0x70516656; WORD $0xcd795be8  // .quad -3640777769877412266
	WORD $0xa3db53b6; WORD $0x00ba1cd8  // .quad 52386062455755702
	WORD $0x4632dff6; WORD $0x806bd971  // .quad -9193015133814464522
	WORD $0xccd228a4; WORD $0x80e8a40e  // .quad -9157889458785081180
	WORD $0x97bf97f3; WORD $0xa086cfcd  // .quad -6879582898840692749
	WORD $0x8006b2cd; WORD $0x6122cd12  // .quad 6999382250228200141
	WORD $0xfdaf7df0; WORD $0xc8a883c0  // .quad -3987792605123478032
	WORD $0x20085f81; WORD $0x796b8057  // .quad 8749227812785250177
	WORD $0x3d1b5d6c; WORD $0xfad2a4b1  // .quad -373054737976959636
	WORD $0x74053bb0; WORD $0xcbe33036  // .quad -3755104653863994448
	WORD $0xc6311a63; WORD $0x9cc3a6ee  // .quad -7150688238876681629
	WORD $0x11068a9c; WORD $0xbedbfc44  // .quad -4693880817329993060
	WORD $0x77bd60fc; WORD $0xc3f490aa  // .quad -4326674280168464132
	WORD $0x15482d44; WORD $0xee92fb55  // .quad -1255665003235103420
	WORD $0x15acb93b; WORD $0xf4f1b4d5  // .quad -796656831783192261
	WORD $0x2d4d1c4a; WORD $0x751bdd15  // .quad 8438581409832836170
	WORD $0x2d8bf3c5; WORD $0x99171105  // .quad -7415439547505577019
	WORD $0x78a0635d; WORD $0xd262d45a  // .quad -3286831292991118499
	WORD $0x78eef0b6; WORD $0xbf5cd546  // .quad -4657613415954583370
	WORD $0x16c87c34; WORD $0x86fb8971  // .quad -8720225134666286028
	WORD $0x172aace4; WORD $0xef340a98  // .quad -1210330751515841308
	WORD $0xae3d4da0; WORD $0xd45d35e6  // .quad -3144297699952734816
	WORD $0x0e7aac0e; WORD $0x9580869f  // .quad -7673985747338482674
	WORD $0x59cca109; WORD $0x89748360  // .quad -8542058143368306423
	WORD $0xd2195712; WORD $0xbae0a846  // .quad -4980796165745715438
	WORD $0x703fc94b; WORD $0x2bd1a438  // .quad 3157485376071780683
	WORD $0x869facd7; WORD $0xe998d258  // .quad -1614309188754756393
	WORD $0x4627ddcf; WORD $0x7b6306a3  // .quad 8890957387685944783
	WORD $0x5423cc06; WORD $0x91ff8377  // .quad -7926472270612804602
	WORD $0x17b1d542; WORD $0x1a3bc84c  // .quad 1890324697752655170
	WORD $0x292cbf08; WORD $0xb67f6455  // .quad -5296404319838617848
	WORD $0x1d9e4a93; WORD $0x20caba5f  // .quad 2362905872190818963
	WORD $0x7377eeca; WORD $0xe41f3d6a  // .quad -2008819381370884406
	WORD $0x7282ee9c; WORD $0x547eb47b  // .quad 6088502188546649756
	WORD $0x882af53e; WORD $0x8e938662  // .quad -8173041140997884610
	WORD $0x4f23aa43; WORD $0xe99e619a  // .quad -1612744301171463613
	WORD $0x2a35b28d; WORD $0xb23867fb  // .quad -5604615407819967859
	WORD $0xe2ec94d4; WORD $0x6405fa00  // .quad 7207441660390446292
	WORD $0xf4c31f31; WORD $0xdec681f9  // .quad -2394083241347571919
	WORD $0x8dd3dd04; WORD $0xde83bc40  // .quad -2412877989897052924
	WORD $0x38f9f37e; WORD $0x8b3c113c  // .quad -8413831053483314306
	WORD $0xb148d445; WORD $0x9624ab50  // .quad -7627783505798704059
	WORD $0x4738705e; WORD $0xae0b158b  // .quad -5905602798426754978
	WORD $0xdd9b0957; WORD $0x3badd624  // .quad 4300328673033783639
	WORD $0x19068c76; WORD $0xd98ddaee  // .quad -2770317479606055818
	WORD $0x0a80e5d6; WORD $0xe54ca5d7  // .quad -1923980597781273130
	WORD $0xcfa417c9; WORD $0x87f8a8d4  // .quad -8648977452394866743
	WORD $0xcd211f4c; WORD $0x5e9fcf4c  // .quad 6818396289628184396
	WORD $0x038d1dbc; WORD $0xa9f6d30a  // .quad -6199535797066195524
	WORD $0x0069671f; WORD $0x7647c320  // .quad 8522995362035230495
	WORD $0x8470652b; WORD $0xd47487cc  // .quad -3137733727905356501
	WORD $0x0041e073; WORD $0x29ecd9f4  // .quad 3021029092058325107
	WORD $0xd2c63f3b; WORD $0x84c8d4df  // .quad -8878612607581929669
	WORD $0x00525890; WORD $0xf4681071  // .quad -835399653354481520
	WORD $0xc777cf09; WORD $0xa5fb0a17  // .quad -6486579741050024183
	WORD $0x4066eeb4; WORD $0x7182148d  // .quad 8179122470161673908
	WORD $0xb955c2cc; WORD $0xcf79cc9d  // .quad -3496538657885142324
	WORD $0x48405530; WORD $0xc6f14cd8  // .quad -4111420493003729616
	WORD $0x93d599bf; WORD $0x81ac1fe2  // .quad -9102865688819295809
	WORD $0x5a506a7c; WORD $0xb8ada00e  // .quad -5139275616254662020
	WORD $0x38cb002f; WORD $0xa21727db  // .quad -6766896092596731857
	WORD $0xf0e4851c; WORD $0xa6d90811  // .quad -6424094520318327524
	WORD $0x06fdc03b; WORD $0xca9cf1d2  // .quad -3846934097318526917
	WORD $0x6d1da663; WORD $0x908f4a16  // .quad -8030118150397909405
	WORD $0x88bd304a; WORD $0xfd442e46  // .quad -196981603220770742
	WORD $0x043287fe; WORD $0x9a598e4e  // .quad -7324666853212387330
	WORD $0x15763e2e; WORD $0x9e4a9cec  // .quad -7040642529654063570
	WORD $0x853f29fd; WORD $0x40eff1e1  // .quad 4679224488766679549
	WORD $0x1ad3cdba; WORD $0xc5dd4427  // .quad -4189117143640191558
	WORD $0xe68ef47c; WORD $0xd12bee59  // .quad -3374341425896426372
	WORD $0xe188c128; WORD $0xf7549530  // .quad -624710411122851544
	WORD $0x301958ce; WORD $0x82bb74f8  // .quad -9026492418826348338
	WORD $0x8cf578b9; WORD $0x9a94dd3e  // .quad -7307973034592864071
	WORD $0x3c1faf01; WORD $0xe36a5236  // .quad -2059743486678159615
	WORD $0x3032d6e7; WORD $0xc13a148e  // .quad -4523280274813692185
	WORD $0xcb279ac1; WORD $0xdc44e6c3  // .quad -2574679358347699519
	WORD $0xbc3f8ca1; WORD $0xf18899b1  // .quad -1042414325089727327
	WORD $0x5ef8c0b9; WORD $0x29ab103a  // .quad 3002511419460075705
	WORD $0x15a7b7e5; WORD $0x96f5600f  // .quad -7569037980822161435
	WORD $0xf6b6f0e7; WORD $0x7415d448  // .quad 8364825292752482535
	WORD $0xdb11a5de; WORD $0xbcb2b812  // .quad -4849611457600313890
	WORD $0x3464ad21; WORD $0x111b495b  // .quad 1232659579085827361
	WORD $0x91d60f56; WORD $0xebdf6617  // .quad -1450328303573004458
	WORD $0x00beec34; WORD $0xcab10dd9  // .quad -3841273781498745804
	WORD $0xbb25c995; WORD $0x936b9fce  // .quad -7823984217374209643
	WORD $0x40eea742; WORD $0x3d5d514f  // .quad 4421779809981343554
	WORD $0x69ef3bfb; WORD $0xb84687c2  // .quad -5168294253290374149
	WORD $0x112a5112; WORD $0x0cb4a5a3  // .quad 915538744049291538
	WORD $0x046b0afa; WORD $0xe65829b3  // .quad -1848681798185579782
	WORD $0xeaba72ab; WORD $0x47f0e785  // .quad 5183897733458195115
	WORD $0xe2c2e6dc; WORD $0x8ff71a0f  // .quad -8072955151507069220
	WORD $0x65690f56; WORD $0x59ed2167  // .quad 6479872166822743894
	WORD $0xdb73a093; WORD $0xb3f4e093  // .quad -5479507920956448621
	WORD $0x3ec3532c; WORD $0x306869c1  // .quad 3488154190101041964
	WORD $0xd25088b8; WORD $0xe0f218b8  // .quad -2237698882768172872
	WORD $0xc73a13fb; WORD $0x1e414218  // .quad 2180096368813151227
	WORD $0x83725573; WORD $0x8c974f73  // .quad -8316090829371189901
	WORD $0xf90898fa; WORD $0xe5d1929e  // .quad -1886565557410948870
	WORD $0x644eeacf; WORD $0xafbd2350  // .quad -5783427518286599473
	WORD $0xb74abf39; WORD $0xdf45f746  // .quad -2358206946763686087
	WORD $0x7d62a583; WORD $0xdbac6c24  // .quad -2617598379430861437
	WORD $0x328eb783; WORD $0x6b8bba8c  // .quad 7749492695127472003
	WORD $0xce5da772; WORD $0x894bc396  // .quad -8553528014785370254
	WORD $0x3f326564; WORD $0x066ea92f  // .quad 463493832054564196
	WORD $0x81f5114f; WORD $0xab9eb47c  // .quad -6080224000054324913
	WORD $0x0efefebd; WORD $0xc80a537b  // .quad -4032318728359182659
	WORD $0xa27255a2; WORD $0xd686619b  // .quad -2988593981640518238
	WORD $0xe95f5f36; WORD $0xbd06742c  // .quad -4826042214438183114
	WORD $0x45877585; WORD $0x8613fd01  // .quad -8785400266166405755
	WORD $0x23b73704; WORD $0x2c481138  // .quad 3190819268807046916
	WORD $0x96e952e7; WORD $0xa798fc41  // .quad -6370064314280619289
	WORD $0x2ca504c5; WORD $0xf75a1586  // .quad -623161932418579259
	WORD $0xfca3a7a0; WORD $0xd17f3b51  // .quad -3350894374423386208
	WORD $0xdbe722fb; WORD $0x9a984d73  // .quad -7307005235402693893
	WORD $0x3de648c4; WORD $0x82ef8513  // .quad -9011838011655698236
	WORD $0xd2e0ebba; WORD $0xc13e60d0  // .quad -4522070525825979462
	WORD $0x0d5fdaf5; WORD $0xa3ab6658  // .quad -6653111496142234891
	WORD $0x079926a8; WORD $0x318df905  // .quad 3570783879572301480
	WORD $0x10b7d1b3; WORD $0xcc963fee  // .quad -3704703351750405709
	WORD $0x497f7052; WORD $0xfdf17746  // .quad -148206168962011054
	WORD $0x94e5c61f; WORD $0xffbbcfe9  // .quad -19193171260619233
	WORD $0xedefa633; WORD $0xfeb6ea8b  // .quad -92628855601256909
	WORD $0xfd0f9bd3; WORD $0x9fd561f1  // .quad -6929524759678968877
	WORD $0xe96b8fc0; WORD $0xfe64a52e  // .quad -115786069501571136
	WORD $0x7c5382c8; WORD $0xc7caba6e  // .quad -4050219931171323192
	WORD $0xa3c673b0; WORD $0x3dfdce7a  // .quad 4466953431550423984
	WORD $0x1b68637b; WORD $0xf9bd690a  // .quad -451088895536766085
	WORD $0xa65c084e; WORD $0x06bea10c  // .quad 486002885505321038
	WORD $0x51213e2d; WORD $0x9c1661a6  // .quad -7199459587351560659
	WORD $0xcff30a62; WORD $0x486e494f  // .quad 5219189625309039202
	WORD $0xe5698db8; WORD $0xc31bfa0f  // .quad -4387638465762062920
	WORD $0xc3efccfa; WORD $0x5a89dba3  // .quad 6523987031636299002
	WORD $0xdec3f126; WORD $0xf3e2f893  // .quad -872862063775190746
	WORD $0x5a75e01c; WORD $0xf8962946  // .quad -534194123654701028
	WORD $0x6b3a76b7; WORD $0x986ddb5c  // .quad -7463067817500576073
	WORD $0xf1135823; WORD $0xf6bbb397  // .quad -667742654568376285
	WORD $0x86091465; WORD $0xbe895233  // .quad -4717148753448332187
	WORD $0xed582e2c; WORD $0x746aa07d  // .quad 8388693718644305452
	WORD $0x678b597f; WORD $0xee2ba6c0  // .quad -1284749923383027329
	WORD $0xb4571cdc; WORD $0xa8c2a44e  // .quad -6286281471915778852
	WORD $0x40b717ef; WORD $0x94db4838  // .quad -7720497729755473937
	WORD $0x616ce413; WORD $0x92f34d62  // .quad -7857851839894723565
	WORD $0x50e4ddeb; WORD $0xba121a46  // .quad -5038936143766954517
	WORD $0xf9c81d17; WORD $0x77b020ba  // .quad 8624429273841147159
	WORD $0xe51e1566; WORD $0xe896a0d7  // .quad -1686984161281305242
	WORD $0xdc1d122e; WORD $0x0ace1474  // .quad 778582277723329070
	WORD $0xef32cd60; WORD $0x915e2486  // .quad -7971894128441897632
	WORD $0x132456ba; WORD $0x0d819992  // .quad 973227847154161338
	WORD $0xaaff80b8; WORD $0xb5b5ada8  // .quad -5353181642124984136
	WORD $0x97ed6c69; WORD $0x10e1fff6  // .quad 1216534808942701673
	WORD $0xd5bf60e6; WORD $0xe3231912  // .quad -2079791034228842266
	WORD $0x1ef463c1; WORD $0xca8d3ffa  // .quad -3851351762838199359
	WORD $0xc5979c8f; WORD $0x8df5efab  // .quad -8217398424034108273
	WORD $0xa6b17cb2; WORD $0xbd308ff8  // .quad -4814189703547749198
	WORD $0xb6fd83b3; WORD $0xb1736b96  // .quad -5660062011615247437
	WORD $0xd05ddbde; WORD $0xac7cb3f6  // .quad -6017737129434686498
	WORD $0x64bce4a0; WORD $0xddd0467c  // .quad -2463391496091671392
	WORD $0x423aa96b; WORD $0x6bcdf07a  // .quad 7768129340171790699
	WORD $0xbef60ee4; WORD $0x8aa22c0d  // .quad -8457148712698376476
	WORD $0xd2c953c6; WORD $0x86c16c98  // .quad -8736582398494813242
	WORD $0x2eb3929d; WORD $0xad4ab711  // .quad -5959749872445582691
	WORD $0x077ba8b7; WORD $0xe871c7bf  // .quad -1697355961263740745
	WORD $0x7a607744; WORD $0xd89d64d5  // .quad -2838001322129590460
	WORD $0x64ad4972; WORD $0x11471cd7  // .quad 1244995533423855986
	WORD $0x6c7c4a8b; WORD $0x87625f05  // .quad -8691279853972075893
	WORD $0x3dd89bcf; WORD $0xd598e40d  // .quad -3055441601647567921
	WORD $0xc79b5d2d; WORD $0xa93af6c6  // .quad -6252413799037706963
	WORD $0x8d4ec2c3; WORD $0x4aff1d10  // .quad 5404070034795315907
	WORD $0x79823479; WORD $0xd389b478  // .quad -3203831230369745799
	WORD $0x585139ba; WORD $0xcedf722a  // .quad -3539985255894009414
	WORD $0x4bf160cb; WORD $0x843610cb  // .quad -8919923546622172981
	WORD $0xee658828; WORD $0xc2974eb4  // .quad -4424981569867511768
	WORD $0x1eedb8fe; WORD $0xa54394fe  // .quad -6538218414850328322
	WORD $0x29feea32; WORD $0x733d2262  // .quad 8303831092947774002
	WORD $0xa6a9273e; WORD $0xce947a3d  // .quad -3561087000135522498
	WORD $0x5a3f525f; WORD $0x0806357d  // .quad 578208414664970847
	WORD $0x8829b887; WORD $0x811ccc66  // .quad -9143208402725783417
	WORD $0xb0cf26f7; WORD $0xca07c2dc  // .quad -3888925500096174345
	WORD $0x2a3426a8; WORD $0xa163ff80  // .quad -6817324484979841368
	WORD $0xdd02f0b5; WORD $0xfc89b393  // .quad -249470856692830027
	WORD $0x34c13052; WORD $0xc9bcff60  // .quad -3909969587797413806
	WORD $0xd443ace2; WORD $0xbbac2078  // .quad -4923524589293425438
	WORD $0x41f17c67; WORD $0xfc2c3f38  // .quad -275775966319379353
	WORD $0x84aa4c0d; WORD $0xd54b944b  // .quad -3077202868308390899
	WORD $0x2936edc0; WORD $0x9d9ba783  // .quad -7089889006590693952
	WORD $0x65d4df11; WORD $0x0a9e795e  // .quad 765182433041899281
	WORD $0xf384a931; WORD $0xc5029163  // .quad -4250675239810979535
	WORD $0xff4a16d5; WORD $0x4d4617b5  // .quad 5568164059729762005
	WORD $0xf065d37d; WORD $0xf64335bc  // .quad -701658031336336515
	WORD $0xbf8e4e45; WORD $0x504bced1  // .quad 5785945546544795205
	WORD $0x163fa42e; WORD $0x99ea0196  // .quad -7356065297226292178
	WORD $0x2f71e1d6; WORD $0xe45ec286  // .quad -1990940103673781802
	WORD $0x9bcf8d39; WORD $0xc06481fb  // .quad -4583395603105477319
	WORD $0xbb4e5a4c; WORD $0x5d767327  // .quad 6734696907262548556
	WORD $0x82c37088; WORD $0xf07da27a  // .quad -1117558485454458744
	WORD $0xd510f86f; WORD $0x3a6a07f8  // .quad 4209185567039092847
	WORD $0x91ba2655; WORD $0x964e858c  // .quad -7616003081050118571
	WORD $0x0a55368b; WORD $0x890489f7  // .quad -8573576096483297653
	WORD $0xb628afea; WORD $0xbbe226ef  // .quad -4908317832885260310
	WORD $0xccea842e; WORD $0x2b45ac74  // .quad 3118087934678041646
	WORD $0xa3b2dbe5; WORD $0xeadab0ab  // .quad -1523711272679187483
	WORD $0x0012929d; WORD $0x3b0b8bc9  // .quad 4254647968387469981
	WORD $0x464fc96f; WORD $0x92c8ae6b  // .quad -7869848573065574033
	WORD $0x40173744; WORD $0x09ce6ebb  // .quad 706623942056949572
	WORD $0x17e3bbcb; WORD $0xb77ada06  // .quad -5225624697904579637
	WORD $0x101d0515; WORD $0xcc420a6a  // .quad -3728406090856200939
	WORD $0x9ddcaabd; WORD $0xe5599087  // .quad -1920344853953336643
	WORD $0x4a12232d; WORD $0x9fa94682  // .quad -6941939825212513491
	WORD $0xc2a9eab6; WORD $0x8f57fa54  // .quad -8117744561361917258
	WORD $0xdc96abf9; WORD $0x47939822  // .quad 5157633273766521849
	WORD $0xf3546564; WORD $0xb32df8e9  // .quad -5535494683275008668
	WORD $0x93bc56f7; WORD $0x59787e2b  // .quad 6447041592208152311
	WORD $0x70297ebd; WORD $0xdff97724  // .quad -2307682335666372931
	WORD $0x3c55b65a; WORD $0x57eb4edb  // .quad 6335244004343789146
	WORD $0xc619ef36; WORD $0x8bfbea76  // .quad -8359830487432564938
	WORD $0x0b6b23f1; WORD $0xede62292  // .quad -1304317031425039375
	WORD $0x77a06b03; WORD $0xaefae514  // .quad -5838102090863318269
	WORD $0x8e45eced; WORD $0xe95fab36  // .quad -1630396289281299219
	WORD $0x958885c4; WORD $0xdab99e59  // .quad -2685941595151759932
	WORD $0x18ebb414; WORD $0x11dbcb02  // .quad 1286845328412881940
	WORD $0xfd75539b; WORD $0x88b402f7  // .quad -8596242524610931813
	WORD $0x9f26a119; WORD $0xd652bdc2  // .quad -3003129357911285479
	WORD $0xfcd2a881; WORD $0xaae103b5  // .quad -6133617137336276863
	WORD $0x46f0495f; WORD $0x4be76d33  // .quad 5469460339465668959
	WORD $0x7c0752a2; WORD $0xd59944a3  // .quad -3055335403242958174
	WORD $0x0c562ddb; WORD $0x6f70a440  // .quad 8030098730593431003
	WORD $0x2d8493a5; WORD $0x857fcae6  // .quad -8827113654667930715
	WORD $0x0f6bb952; WORD $0xcb4ccd50  // .quad -3797434642040374958
	WORD $0xb8e5b88e; WORD $0xa6dfbd9f  // .quad -6422206049907525490
	WORD $0x1346a7a7; WORD $0x7e2000a4  // .quad 9088264752731695015
	WORD $0xa71f26b2; WORD $0xd097ad07  // .quad -3416071543957018958
	WORD $0x8c0c28c8; WORD $0x8ed40066  // .quad -8154892584824854328
	WORD $0xc873782f; WORD $0x825ecc24  // .quad -9052573742614218705
	WORD $0x2f0f32fa; WORD $0x72890080  // .quad 8253128342678483706
	WORD $0xfa90563b; WORD $0xa2f67f2d  // .quad -6704031159840385477
	WORD $0x3ad2ffb9; WORD $0x4f2b40a0  // .quad 5704724409920716729
	WORD $0x79346bca; WORD $0xcbb41ef9  // .quad -3768352931373093942
	WORD $0x4987bfa8; WORD $0xe2f610c8  // .quad -2092466524453879896
	WORD $0xd78186bc; WORD $0xfea126b7  // .quad -98755145788979524
	WORD $0x2df4d7c9; WORD $0x0dd9ca7d  // .quad 998051431430019017
	WORD $0xe6b0f436; WORD $0x9f24b832  // .quad -6979250993759194058
	WORD $0x79720dbb; WORD $0x91503d1c  // .quad -7975807747567252037
	WORD $0xa05d3143; WORD $0xc6ede63f  // .quad -4112377723771604669
	WORD $0x97ce912a; WORD $0x75a44c63  // .quad 8476984389250486570
	WORD $0x88747d94; WORD $0xf8a95fcf  // .quad -528786136287117932
	WORD $0x3ee11aba; WORD $0xc986afbe  // .quad -3925256793573221702
	WORD $0xb548ce7c; WORD $0x9b69dbe1  // .quad -7248020362820530564
	WORD $0xce996168; WORD $0xfbe85bad  // .quad -294884973539139224
	WORD $0x229b021b; WORD $0xc24452da  // .quad -4448339435098275301
	WORD $0x423fb9c3; WORD $0xfae27299  // .quad -368606216923924029
	WORD $0xab41c2a2; WORD $0xf2d56790  // .quad -948738275445456222
	WORD $0xc967d41a; WORD $0xdccd879f  // .quad -2536221894791146470
	WORD $0x6b0919a5; WORD $0x97c560ba  // .quad -7510490449794491995
	WORD $0xbbc1c920; WORD $0x5400e987  // .quad 6053094668365842720
	WORD $0x05cb600f; WORD $0xbdb6b8e9  // .quad -4776427043815727089
	WORD $0xaab23b68; WORD $0x290123e9  // .quad 2954682317029915496
	WORD $0x473e3813; WORD $0xed246723  // .quad -1358847786342270957
	WORD $0x0aaf6521; WORD $0xf9a0b672  // .quad -459166561069996767
	WORD $0x0c86e30b; WORD $0x9436c076  // .quad -7766808894105001205
	WORD $0x8d5b3e69; WORD $0xf808e40e  // .quad -573958201337495959
	WORD $0x8fa89bce; WORD $0xb9447093  // .quad -5096825099203863602
	WORD $0x30b20e04; WORD $0xb60b1d12  // .quad -5329133770099257852
	WORD $0x7392c2c2; WORD $0xe7958cb8  // .quad -1759345355577441598
	WORD $0x5e6f48c2; WORD $0xb1c6f22b  // .quad -5636551615525730110
	WORD $0x483bb9b9; WORD $0x90bd77f3  // .quad -8017119874876982855
	WORD $0x360b1af3; WORD $0x1e38aeb6  // .quad 2177682517447613171
	WORD $0x1a4aa828; WORD $0xb4ecd5f0  // .quad -5409713825168840664
	WORD $0xc38de1b0; WORD $0x25c6da63  // .quad 2722103146809516464
	WORD $0x20dd5232; WORD $0xe2280b6c  // .quad -2150456263033662926
	WORD $0x5a38ad0e; WORD $0x579c487e  // .quad 6313000485183335694
	WORD $0x948a535f; WORD $0x8d590723  // .quad -8261564192037121185
	WORD $0xf0c6d851; WORD $0x2d835a9d  // .quad 3279564588051781713
	WORD $0x79ace837; WORD $0xb0af48ec  // .quad -5715269221619013577
	WORD $0x6cf88e65; WORD $0xf8e43145  // .quad -512230283362660763
	WORD $0x98182244; WORD $0xdcdb1b27  // .quad -2532400508596379068
	WORD $0x641b58ff; WORD $0x1b8e9ecb  // .quad 1985699082112030975
	WORD $0xbf0f156b; WORD $0x8a08f0f8  // .quad -8500279345513818773
	WORD $0x3d222f3f; WORD $0xe272467e  // .quad -2129562165787349185
	WORD $0xeed2dac5; WORD $0xac8b2d36  // .quad -6013663163464885563
	WORD $0xcc6abb0f; WORD $0x5b0ed81d  // .quad 6561419329620589327
	WORD $0xaa879177; WORD $0xd7adf884  // .quad -2905392935903719049
	WORD $0x9fc2b4e9; WORD $0x98e94712  // .quad -7428327965055601431
	WORD $0xea94baea; WORD $0x86ccbb52  // .quad -8733399612580906262
	WORD $0x47b36224; WORD $0x3f2398d7  // .quad 4549648098962661924
	WORD $0xa539e9a5; WORD $0xa87fea27  // .quad -6305063497298744923
	WORD $0x19a03aad; WORD $0x8eec7f0d  // .quad -8147997931578836307
	WORD $0x8e88640e; WORD $0xd29fe4b1  // .quad -3269643353196043250
	WORD $0x300424ac; WORD $0x1953cf68  // .quad 1825030320404309164
	WORD $0xf9153e89; WORD $0x83a3eeee  // .quad -8961056123388608887
	WORD $0x3c052dd7; WORD $0x5fa8c342  // .quad 6892973918932774359
	WORD $0xb75a8e2b; WORD $0xa48ceaaa  // .quad -6589634135808373205
	WORD $0xcb06794d; WORD $0x3792f412  // .quad 4004531380238580045
	WORD $0x653131b6; WORD $0xcdb02555  // .quad -3625356651333078602
	WORD $0xbee40bd0; WORD $0xe2bbd88b  // .quad -2108853905778275376
	WORD $0x5f3ebf11; WORD $0x808e1755  // .quad -9183376934724255983
	WORD $0xae9d0ec4; WORD $0x5b6aceae  // .quad 6587304654631931588
	WORD $0xb70e6ed6; WORD $0xa0b19d2a  // .quad -6867535149977932074
	WORD $0x5a445275; WORD $0xf245825a  // .quad -989241218564861323
	WORD $0x64d20a8b; WORD $0xc8de0475  // .quad -3972732919045027189
	WORD $0xf0d56712; WORD $0xeed6e2f0  // .quad -1236551523206076654
	WORD $0xbe068d2e; WORD $0xfb158592  // .quad -354230130378896082
	WORD $0x9685606b; WORD $0x55464dd6  // .quad 6144684325637283947
	WORD $0xb6c4183d; WORD $0x9ced737b  // .quad -7138922859127891907
	WORD $0x3c26b886; WORD $0xaa97e14c  // .quad -6154202648235558778
	WORD $0xa4751e4c; WORD $0xc428d05a  // .quad -4311967555482476980
	WORD $0x4b3066a8; WORD $0xd53dd99f  // .quad -3081067291867060568
	WORD $0x4d9265df; WORD $0xf5330471  // .quad -778273425925708321
	WORD $0x8efe4029; WORD $0xe546a803  // .quad -1925667057416912855
	WORD $0xd07b7fab; WORD $0x993fe2c6  // .quad -7403949918844649557
	WORD $0x72bdd033; WORD $0xde985204  // .quad -2407083821771141069
	WORD $0x849a5f96; WORD $0xbf8fdb78  // .quad -4643251380128424042
	WORD $0x8f6d4440; WORD $0x963e6685  // .quad -7620540795641314240
	WORD $0xa5c0f77c; WORD $0xef73d256  // .quad -1192378206733142148
	WORD $0x79a44aa8; WORD $0xdde70013  // .quad -2456994988062127448
	WORD $0x27989aad; WORD $0x95a86376  // .quad -7662765406849295699
	WORD $0x580d5d52; WORD $0x5560c018  // .quad 6152128301777116498
	WORD $0xb17ec159; WORD $0xbb127c53  // .quad -4966770740134231719
	WORD $0x6e10b4a6; WORD $0xaab8f01e  // .quad -6144897678060768090
	WORD $0x9dde71af; WORD $0xe9d71b68  // .quad -1596777406740401745
	WORD $0x04ca70e8; WORD $0xcab39613  // .quad -3840561048787980056
	WORD $0x62ab070d; WORD $0x92267121  // .quad -7915514906853832947
	WORD $0xc5fd0d22; WORD $0x3d607b97  // .quad 4422670725869800738
	WORD $0xbb55c8d1; WORD $0xb6b00d69  // .quad -5282707615139903279
	WORD $0xb77c506a; WORD $0x8cb89a7d  // .quad -8306719647944912790
	WORD $0x2a2b3b05; WORD $0xe45c10c4  // .quad -1991698500497491195
	WORD $0x92adb242; WORD $0x77f3608e  // .quad 8643358275316593218
	WORD $0x9a5b04e3; WORD $0x8eb98a7a  // .quad -8162340590452013853
	WORD $0x37591ed3; WORD $0x55f038b2  // .quad 6192511825718353619
	WORD $0x40f1c61c; WORD $0xb267ed19  // .quad -5591239719637629412
	WORD $0xc52f6688; WORD $0x6b6c46de  // .quad 7740639782147942024
	WORD $0x912e37a3; WORD $0xdf01e85f  // .quad -2377363631119648861
	WORD $0x3b3da015; WORD $0x2323ac4b  // .quad 2532056854628769813
	WORD $0xbabce2c6; WORD $0x8b61313b  // .quad -8403381297090862394
	WORD $0x0a0d081a; WORD $0xabec975e  // .quad -6058300968568813542
	WORD $0xa96c1b77; WORD $0xae397d8a  // .quad -5892540602936190089
	WORD $0x8c904a21; WORD $0x96e7bd35  // .quad -7572876210711016927
	WORD $0x53c72255; WORD $0xd9c7dced  // .quad -2753989735242849707
	WORD $0x77da2e54; WORD $0x7e50d641  // .quad 9102010423587778132
	WORD $0x545c7575; WORD $0x881cea14  // .quad -8638772612167862923
	WORD $0xd5d0b9e9; WORD $0xdde50bd1  // .quad -2457545025797441047
	WORD $0x697392d2; WORD $0xaa242499  // .quad -6186779746782440750
	WORD $0x4b44e864; WORD $0x955e4ec6  // .quad -7683617300674189212
	WORD $0xc3d07787; WORD $0xd4ad2dbf  // .quad -3121788665050663033
	WORD $0xef0b113e; WORD $0xbd5af13b  // .quad -4802260812921368258
	WORD $0xda624ab4; WORD $0x84ec3c97  // .quad -8868646943297746252
	WORD $0xeacdd58e; WORD $0xecb1ad8a  // .quad -1391139997724322418
	WORD $0xd0fadd61; WORD $0xa6274bbd  // .quad -6474122660694794911
	WORD $0xa5814af2; WORD $0x67de18ed  // .quad 7484447039699372786
	WORD $0x453994ba; WORD $0xcfb11ead  // .quad -3480967307441105734
	WORD $0x8770ced7; WORD $0x80eacf94  // .quad -9157278655470055721
	WORD $0x4b43fcf4; WORD $0x81ceb32c  // .quad -9093133594791772940
	WORD $0xa94d028d; WORD $0xa1258379  // .quad -6834912300910181747
	WORD $0x5e14fc31; WORD $0xa2425ff7  // .quad -6754730975062328271
	WORD $0x13a04330; WORD $0x096ee458  // .quad 679731660717048624
	WORD $0x359a3b3e; WORD $0xcad2f7f5  // .quad -3831727700400522434
	WORD $0x188853fc; WORD $0x8bca9d6e  // .quad -8373707460958465028
	WORD $0x8300ca0d; WORD $0xfd87b5f2  // .quad -177973607073265139
	WORD $0xcf55347d; WORD $0x775ea264  // .quad 8601490892183123069
	WORD $0x91e07e48; WORD $0x9e74d1b7  // .quad -7028762532061872568
	WORD $0x032a819d; WORD $0x95364afe  // .quad -7694880458480647779
	WORD $0x76589dda; WORD $0xc6120625  // .quad -4174267146649952806
	WORD $0x83f52204; WORD $0x3a83ddbd  // .quad 4216457482181353988
	WORD $0xd3eec551; WORD $0xf79687ae  // .quad -606147914885053103
	WORD $0x72793542; WORD $0xc4926a96  // .quad -4282243101277735614
	WORD $0x44753b52; WORD $0x9abe14cd  // .quad -7296371474444240046
	WORD $0x0f178293; WORD $0x75b7053c  // .quad 8482254178684994195
	WORD $0x95928a27; WORD $0xc16d9a00  // .quad -4508778324627912153
	WORD $0x12dd6338; WORD $0x5324c68b  // .quad 5991131704928854840
	WORD $0xbaf72cb1; WORD $0xf1c90080  // .quad -1024286887357502287
	WORD $0xebca5e03; WORD $0xd3f6fc16  // .quad -3173071712060547581
	WORD $0x74da7bee; WORD $0x971da050  // .quad -7557708332239520786
	WORD $0xa6bcf584; WORD $0x88f4bb1c  // .quad -8578025658503072380
	WORD $0x92111aea; WORD $0xbce50864  // .quad -4835449396872013078
	WORD $0xd06c32e5; WORD $0x2b31e9e3  // .quad 3112525982153323237
	WORD $0xb69561a5; WORD $0xec1e4a7d  // .quad -1432625727662628443
	WORD $0x62439fcf; WORD $0x3aff322e  // .quad 4251171748059520975
	WORD $0x921d5d07; WORD $0x9392ee8e  // .quad -7812920107430224633
	WORD $0xfad487c2; WORD $0x09befeb9  // .quad 702278666647013314
	WORD $0x36a4b449; WORD $0xb877aa32  // .quad -5154464115860392887
	WORD $0x7989a9b3; WORD $0x4c2ebe68  // .quad 5489534351736154547
	WORD $0xc44de15b; WORD $0xe69594be  // .quad -1831394126398103205
	WORD $0x4bf60a10; WORD $0x0f9d3701  // .quad 1125115960621402640
	WORD $0x3ab0acd9; WORD $0x901d7cf7  // .quad -8062150356639896359
	WORD $0x9ef38c94; WORD $0x538484c1  // .quad 6018080969204141204
	WORD $0x095cd80f; WORD $0xb424dc35  // .quad -5466001927372482545
	WORD $0x06b06fb9; WORD $0x2865a5f2  // .quad 2910915193077788601
	WORD $0x4bb40e13; WORD $0xe12e1342  // .quad -2220816390788215277
	WORD $0x442e45d3; WORD $0xf93f87b7  // .quad -486521013540076077
	WORD $0x6f5088cb; WORD $0x8cbccc09  // .quad -8305539271883716405
	WORD $0x1539d748; WORD $0xf78f69a5  // .quad -608151266925095096
	WORD $0xcb24aafe; WORD $0xafebff0b  // .quad -5770238071427257602
	WORD $0x5a884d1b; WORD $0xb573440e  // .quad -5371875102083756773
	WORD $0xbdedd5be; WORD $0xdbe6fece  // .quad -2601111570856684098
	WORD $0xf8953030; WORD $0x31680a88  // .quad 3560107088838733872
	WORD $0x36b4a597; WORD $0x89705f41  // .quad -8543223759426509417
	WORD $0x36ba7c3d; WORD $0xfdc20d2b  // .quad -161552157378970563
	WORD $0x8461cefc; WORD $0xabcc7711  // .quad -6067343680855748868
	WORD $0x04691b4c; WORD $0x3d329076  // .quad 4409745821703674700
	WORD $0xe57a42bc; WORD $0xd6bf94d5  // .quad -2972493582642298180
	WORD $0xc2c1b10f; WORD $0xa63f9a49  // .quad -6467280898289979121
	WORD $0xaf6c69b5; WORD $0x8637bd05  // .quad -8775337516792518219
	WORD $0x33721d53; WORD $0x0fcf80dc  // .quad 1139270913992301907
	WORD $0x1b478423; WORD $0xa7c5ac47  // .quad -6357485877563259869
	WORD $0x404ea4a8; WORD $0xd3c36113  // .quad -3187597375937010520
	WORD $0xe219652b; WORD $0xd1b71758  // .quad -3335171328526686933
	WORD $0x083126e9; WORD $0x645a1cac  // .quad 7231123676894144233
	WORD $0x8d4fdf3b; WORD $0x83126e97  // .quad -9002011107970261189
	WORD $0x0a3d70a3; WORD $0x3d70a3d7  // .quad 4427218577690292387
	WORD $0x70a3d70a; WORD $0xa3d70a3d  // .quad -6640827866535438582
	WORD $0xcccccccc  // .space 4, '\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc'
	WORD $0xcccccccc  // .space 4, '\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc'
	WORD $0xcccccccc  // .space 4, '\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc'
	WORD $0xcccccccc  // .space 4, '\xcc\xcc\xcc\xcc'
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0x00000000; WORD $0x80000000  // .quad -9223372036854775808
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0x00000000; WORD $0xa0000000  // .quad -6917529027641081856
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0x00000000; WORD $0xc8000000  // .quad -4035225266123964416
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0x00000000; WORD $0xfa000000  // .quad -432345564227567616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0x00000000; WORD $0x9c400000  // .quad -7187745005283311616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0x00000000; WORD $0xc3500000  // .quad -4372995238176751616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0x00000000; WORD $0xf4240000  // .quad -854558029293551616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0x00000000; WORD $0x98968000  // .quad -7451627795949551616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0x00000000; WORD $0xbebc2000  // .quad -4702848726509551616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0x00000000; WORD $0xee6b2800  // .quad -1266874889709551616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0x00000000; WORD $0x9502f900  // .quad -7709325833709551616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0x00000000; WORD $0xba43b740  // .quad -5024971273709551616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0x00000000; WORD $0xe8d4a510  // .quad -1669528073709551616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0x00000000; WORD $0x9184e72a  // .quad -7960984073709551616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0x80000000; WORD $0xb5e620f4  // .quad -5339544073709551616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0xa0000000; WORD $0xe35fa931  // .quad -2062744073709551616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0x04000000; WORD $0x8e1bc9bf  // .quad -8206744073709551616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0xc5000000; WORD $0xb1a2bc2e  // .quad -5646744073709551616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0x76400000; WORD $0xde0b6b3a  // .quad -2446744073709551616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0x89e80000; WORD $0x8ac72304  // .quad -8446744073709551616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0xac620000; WORD $0xad78ebc5  // .quad -5946744073709551616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0x177a8000; WORD $0xd8d726b7  // .quad -2821744073709551616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0x6eac9000; WORD $0x87867832  // .quad -8681119073709551616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0x0a57b400; WORD $0xa968163f  // .quad -6239712823709551616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0xcceda100; WORD $0xd3c21bce  // .quad -3187955011209551616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0x401484a0; WORD $0x84595161  // .quad -8910000909647051616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0x9019a5c8; WORD $0xa56fa5b9  // .quad -6525815118631426616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0xf4200f3a; WORD $0xcecb8f27  // .quad -3545582879861895366
	WORD $0x00000000; WORD $0x40000000  // .quad 4611686018427387904
	WORD $0xf8940984; WORD $0x813f3978  // .quad -9133518327554766460
	WORD $0x00000000; WORD $0x50000000  // .quad 5764607523034234880
	WORD $0x36b90be5; WORD $0xa18f07d7  // .quad -6805211891016070171
	WORD $0x00000000; WORD $0xa4000000  // .quad -6629298651489370112
	WORD $0x04674ede; WORD $0xc9f2c9cd  // .quad -3894828845342699810
	WORD $0x00000000; WORD $0x4d000000  // .quad 5548434740920451072
	WORD $0x45812296; WORD $0xfc6f7c40  // .quad -256850038250986858
	WORD $0x00000000; WORD $0xf0200000  // .quad -1143914305352105984
	WORD $0x2b70b59d; WORD $0x9dc5ada8  // .quad -7078060301547948643
	WORD $0x00000000; WORD $0x6c280000  // .quad 7793479155164643328
	WORD $0x364ce305; WORD $0xc5371912  // .quad -4235889358507547899
	WORD $0x00000000; WORD $0xc7320000  // .quad -4093209111326359552
	WORD $0xc3e01bc6; WORD $0xf684df56  // .quad -683175679707046970
	WORD $0x00000000; WORD $0x3c7f4000  // .quad 4359273333062107136
	WORD $0x3a6c115c; WORD $0x9a130b96  // .quad -7344513827457986212
	WORD $0x00000000; WORD $0x4b9f1000  // .quad 5449091666327633920
	WORD $0xc90715b3; WORD $0xc097ce7b  // .quad -4568956265895094861
	WORD $0x00000000; WORD $0x1e86d400  // .quad 2199678564482154496
	WORD $0xbb48db20; WORD $0xf0bdc21a  // .quad -1099509313941480672
	WORD $0x00000000; WORD $0x13144480  // .quad 1374799102801346560
	WORD $0xb50d88f4; WORD $0x96769950  // .quad -7604722348854507276
	WORD $0x00000000; WORD $0x17d955a0  // .quad 1718498878501683200
	WORD $0xe250eb31; WORD $0xbc143fa4  // .quad -4894216917640746191
	WORD $0x00000000; WORD $0x5dcfab08  // .quad 6759809616554491904
	WORD $0x1ae525fd; WORD $0xeb194f8e  // .quad -1506085128623544835
	WORD $0x00000000; WORD $0x5aa1cae5  // .quad 6530724019560251392
	WORD $0xd0cf37be; WORD $0x92efd1b8  // .quad -7858832233030797378
	WORD $0x40000000; WORD $0xf14a3d9e  // .quad -1059967012404461568
	WORD $0x050305ad; WORD $0xb7abc627  // .quad -5211854272861108819
	WORD $0xd0000000; WORD $0x6d9ccd05  // .quad 7898413271349198848
	WORD $0xc643c719; WORD $0xe596b7b0  // .quad -1903131822648998119
	WORD $0xa2000000; WORD $0xe4820023  // .quad -1981020733047832576
	WORD $0x7bea5c6f; WORD $0x8f7e32ce  // .quad -8106986416796705681
	WORD $0x8a800000; WORD $0xdda2802c  // .quad -2476275916309790720
	WORD $0x1ae4f38b; WORD $0xb35dbf82  // .quad -5522047002568494197
	WORD $0xad200000; WORD $0xd50b2037  // .quad -3095344895387238400
	WORD $0xa19e306e; WORD $0xe0352f62  // .quad -2290872734783229842
	WORD $0xcc340000; WORD $0x4526f422  // .quad 4982938468024057856
	WORD $0xa502de45; WORD $0x8c213d9d  // .quad -8349324486880600507
	WORD $0x7f410000; WORD $0x9670b12b  // .quad -7606384970252091392
	WORD $0x0e4395d6; WORD $0xaf298d05  // .quad -5824969590173362730
	WORD $0x5f114000; WORD $0x3c0cdd76  // .quad 4327076842467049472
	WORD $0x51d47b4c; WORD $0xdaf3f046  // .quad -2669525969289315508
	WORD $0xfb6ac800; WORD $0xa5880a69  // .quad -6518949010312869888
	WORD $0xf324cd0f; WORD $0x88d8762b  // .quad -8585982758446904049
	WORD $0x7a457a00; WORD $0x8eea0d04  // .quad -8148686262891087360
	WORD $0xefee0053; WORD $0xab0e93b6  // .quad -6120792429631242157
	WORD $0x98d6d880; WORD $0x72a49045  // .quad 8260886245095692416
	WORD $0xabe98068; WORD $0xd5d238a4  // .quad -3039304518611664792
	WORD $0x7f864750; WORD $0x47a6da2b  // .quad 5163053903184807760
	WORD $0xeb71f041; WORD $0x85a36366  // .quad -8817094351773372351
	WORD $0x5f67d924; WORD $0x999090b6  // .quad -7381240676301154012
	WORD $0xa64e6c51; WORD $0xa70c3c40  // .quad -6409681921289327535
	WORD $0xf741cf6d; WORD $0xfff4b4e3  // .quad -3178808521666707
	WORD $0xcfe20765; WORD $0xd0cf4b50  // .quad -3400416383184271515
	WORD $0x7a8921a4; WORD $0xbff8f10e  // .quad -4613672773753429596
	WORD $0x81ed449f; WORD $0x82818f12  // .quad -9042789267131251553
	WORD $0x192b6a0d; WORD $0xaff72d52  // .quad -5767090967191786995
	WORD $0x226895c7; WORD $0xa321f2d7  // .quad -6691800565486676537
	WORD $0x9f764490; WORD $0x9bf4f8a6  // .quad -7208863708989733744
	WORD $0xeb02bb39; WORD $0xcbea6f8c  // .quad -3753064688430957767
	WORD $0x4753d5b4; WORD $0x02f236d0  // .quad 212292400617608628
	WORD $0x25c36a08; WORD $0xfee50b70  // .quad -79644842111309304
	WORD $0x2c946590; WORD $0x01d76242  // .quad 132682750386005392
	WORD $0x179a2245; WORD $0x9f4f2726  // .quad -6967307053960650171
	WORD $0xb7b97ef5; WORD $0x424d3ad2  // .quad 4777539456409894645
	WORD $0x9d80aad6; WORD $0xc722f0ef  // .quad -4097447799023424810
	WORD $0x65a7deb2; WORD $0xd2e08987  // .quad -3251447716342407502
	WORD $0x84e0d58b; WORD $0xf8ebad2b  // .quad -510123730351893109
	WORD $0x9f88eb2f; WORD $0x63cc55f4  // .quad 7191217214140771119
	WORD $0x330c8577; WORD $0x9b934c3b  // .quad -7236356359111015049
	WORD $0xc76b25fb; WORD $0x3cbf6b71  // .quad 4377335499248575995
	WORD $0xffcfa6d5; WORD $0xc2781f49  // .quad -4433759430461380907
	WORD $0x3945ef7a; WORD $0x8bef464e  // .quad -8363388681221443718
	WORD $0x7fc3908a; WORD $0xf316271c  // .quad -930513269649338230
	WORD $0xe3cbb5ac; WORD $0x97758bf0  // .quad -7532960934977096276
	WORD $0xcfda3a56; WORD $0x97edd871  // .quad -7499099821171918250
	WORD $0x1cbea317; WORD $0x3d52eeed  // .quad 4418856886560793367
	WORD $0x43d0c8ec; WORD $0xbde94e8e  // .quad -4762188758037509908
	WORD $0x63ee4bdd; WORD $0x4ca7aaa8  // .quad 5523571108200991709
	WORD $0xd4c4fb27; WORD $0xed63a231  // .quad -1341049929119499481
	WORD $0x3e74ef6a; WORD $0x8fe8caa9  // .quad -8076983103442849942
	WORD $0x24fb1cf8; WORD $0x945e455f  // .quad -7755685233340769032
	WORD $0x8e122b44; WORD $0xb3e2fd53  // .quad -5484542860876174524
	WORD $0xee39e436; WORD $0xb975d6b6  // .quad -5082920523248573386
	WORD $0x7196b616; WORD $0x60dbbca8  // .quad 6979379479186945558
	WORD $0xa9c85d44; WORD $0xe7d34c64  // .quad -1741964635633328828
	WORD $0x46fe31cd; WORD $0xbc8955e9  // .quad -4861259862362934835
	WORD $0xea1d3a4a; WORD $0x90e40fbe  // .quad -8006256924911912374
	WORD $0x98bdbe41; WORD $0x6babab63  // .quad 7758483227328495169
	WORD $0xa4a488dd; WORD $0xb51d13ae  // .quad -5396135137712502563
	WORD $0x7eed2dd1; WORD $0xc696963c  // .quad -4136954021121544751
	WORD $0x4dcdab14; WORD $0xe264589a  // .quad -2133482903713240300
	WORD $0xcf543ca2; WORD $0xfc1e1de5  // .quad -279753253987271518
	WORD $0x70a08aec; WORD $0x8d7eb760  // .quad -8250955842461857044
	WORD $0x43294bcb; WORD $0x3b25a55f  // .quad 4261994450943298507
	WORD $0x8cc8ada8; WORD $0xb0de6538  // .quad -5702008784649933400
	WORD $0x13f39ebe; WORD $0x49ef0eb7  // .quad 5327493063679123134
	WORD $0xaffad912; WORD $0xdd15fe86  // .quad -2515824962385028846
	WORD $0x6c784337; WORD $0x6e356932  // .quad 7941369183226839863
	WORD $0x2dfcc7ab; WORD $0x8a2dbf14  // .quad -8489919629131724885
	WORD $0x07965404; WORD $0x49c2c37f  // .quad 5315025460606161924
	WORD $0x397bf996; WORD $0xacb92ed9  // .quad -6000713517987268202
	WORD $0xc97be906; WORD $0xdc33745e  // .quad -2579590211097073402
	WORD $0x87daf7fb; WORD $0xd7e77a8f  // .quad -2889205879056697349
	WORD $0x3ded71a3; WORD $0x69a028bb  // .quad 7611128154919104931
	WORD $0xb4e8dafd; WORD $0x86f0ac99  // .quad -8723282702051517699
	WORD $0x0d68ce0c; WORD $0xc40832ea  // .quad -4321147861633282548
	WORD $0x222311bc; WORD $0xa8acd7c0  // .quad -6292417359137009220
	WORD $0x90c30190; WORD $0xf50a3fa4  // .quad -789748808614215280
	WORD $0x2aabd62b; WORD $0xd2d80db0  // .quad -3253835680493873621
	WORD $0xda79e0fa; WORD $0x792667c6  // .quad 8729779031470891258
	WORD $0x1aab65db; WORD $0x83c7088e  // .quad -8951176327949752869
	WORD $0x91185938; WORD $0x577001b8  // .quad 6300537770911226168
	WORD $0xa1563f52; WORD $0xa4b8cab1  // .quad -6577284391509803182
	WORD $0xb55e6f86; WORD $0xed4c0226  // .quad -1347699823215743098
	WORD $0x09abcf26; WORD $0xcde6fd5e  // .quad -3609919470959866074
	WORD $0x315b05b4; WORD $0x544f8158  // .quad 6075216638131242420
	WORD $0xc60b6178; WORD $0x80b05e5a  // .quad -9173728696990998152
	WORD $0x3db1c721; WORD $0x696361ae  // .quad 7594020797664053025
	WORD $0x778e39d6; WORD $0xa0dc75f1  // .quad -6855474852811359786
	WORD $0xcd1e38e9; WORD $0x03bc3a19  // .quad 269153960225290473
	WORD $0xd571c84c; WORD $0xc913936d  // .quad -3957657547586811828
	WORD $0x4065c723; WORD $0x04ab48a0  // .quad 336442450281613091
	WORD $0x4ace3a5f; WORD $0xfb587849  // .quad -335385916056126881
	WORD $0x283f9c76; WORD $0x62eb0d64  // .quad 7127805559067090038
	WORD $0xcec0e47b; WORD $0x9d174b2d  // .quad -7127145225176161157
	WORD $0x324f8394; WORD $0x3ba5d0bd  // .quad 4298070930406474644
	WORD $0x42711d9a; WORD $0xc45d1df9  // .quad -4297245513042813542
	WORD $0x7ee36479; WORD $0xca8f44ec  // .quad -3850783373846682503
	WORD $0x930d6500; WORD $0xf5746577  // .quad -759870872876129024
	WORD $0xcf4e1ecb; WORD $0x7e998b13  // .quad 9122475437414293195
	WORD $0xbbe85f20; WORD $0x9968bf6a  // .quad -7392448323188662496
	WORD $0xc321a67e; WORD $0x9e3fedd8  // .quad -7043649776941685122
	WORD $0x6ae276e8; WORD $0xbfc2ef45  // .quad -4628874385558440216
	WORD $0xf3ea101e; WORD $0xc5cfe94e  // .quad -4192876202749718498
	WORD $0xc59b14a2; WORD $0xefb3ab16  // .quad -1174406963520662366
	WORD $0x58724a12; WORD $0xbba1f1d1  // .quad -4926390635932268014
	WORD $0x3b80ece5; WORD $0x95d04aee  // .quad -7651533379841495835
	WORD $0xae8edc97; WORD $0x2a8a6e45  // .quad 3065383741939440791
	WORD $0xca61281f; WORD $0xbb445da9  // .quad -4952730706374481889
	WORD $0x1a3293bd; WORD $0xf52d09d7  // .quad -779956341003086915
	WORD $0x3cf97226; WORD $0xea157514  // .quad -1579227364540714458
	WORD $0x705f9c56; WORD $0x593c2626  // .quad 6430056314514152534
	WORD $0xa61be758; WORD $0x924d692c  // .quad -7904546130479028392
	WORD $0x0c77836c; WORD $0x6f8b2fb0  // .quad 8037570393142690668
	WORD $0xcfa2e12e; WORD $0xb6e0c377  // .quad -5268996644671397586
	WORD $0x0f956447; WORD $0x0b6dfb9c  // .quad 823590954573587527
	WORD $0xc38b997a; WORD $0xe498f455  // .quad -1974559787411859078
	WORD $0x89bd5eac; WORD $0x4724bd41  // .quad 5126430365035880108
	WORD $0x9a373fec; WORD $0x8edf98b5  // .quad -8151628894773493780
	WORD $0xec2cb657; WORD $0x58edec91  // .quad 6408037956294850135
	WORD $0x00c50fe7; WORD $0xb2977ee3  // .quad -5577850100039479321
	WORD $0x6737e3ed; WORD $0x2f2967b6  // .quad 3398361426941174765
	WORD $0xc0f653e1; WORD $0xdf3d5e9b  // .quad -2360626606621961247
	WORD $0x0082ee74; WORD $0xbd79e0d2  // .quad -4793553135802847628
	WORD $0x5899f46c; WORD $0x8b865b21  // .quad -8392920656779807636
	WORD $0x80a3aa11; WORD $0xecd85906  // .quad -1380255401326171631
	WORD $0xaec07187; WORD $0xae67f1e9  // .quad -5879464802547371641
	WORD $0x20cc9495; WORD $0xe80e6f48  // .quad -1725319251657714539
	WORD $0x1a708de9; WORD $0xda01ee64  // .quad -2737644984756826647
	WORD $0x147fdcdd; WORD $0x3109058d  // .quad 3533361486141316317
	WORD $0x908658b2; WORD $0x884134fe  // .quad -8628557143114098510
	WORD $0x599fd415; WORD $0xbd4b46f0  // .quad -4806670179178130411
	WORD $0x34a7eede; WORD $0xaa51823e  // .quad -6174010410465235234
	WORD $0x7007c91a; WORD $0x6c9e18ac  // .quad 7826720331309500698
	WORD $0xc1d1ea96; WORD $0xd4e5e2cd  // .quad -3105826994654156138
	WORD $0xc604ddb0; WORD $0x03e2cf6b  // .quad 280014188641050032
	WORD $0x9923329e; WORD $0x850fadc0  // .quad -8858670899299929442
	WORD $0xb786151c; WORD $0x84db8346  // .quad -8873354301053463268
	WORD $0xbf6bff45; WORD $0xa6539930  // .quad -6461652605697523899
	WORD $0x65679a63; WORD $0xe6126418  // .quad -1868320839462053277
	WORD $0xef46ff16; WORD $0xcfe87f7c  // .quad -3465379738694516970
	WORD $0x3f60c07e; WORD $0x4fcb7e8f  // .quad 5749828502977298558
	WORD $0x158c5f6e; WORD $0x81f14fae  // .quad -9083391364325154962
	WORD $0x0f38f09d; WORD $0xe3be5e33  // .quad -2036086408133152611
	WORD $0x9aef7749; WORD $0xa26da399  // .quad -6742553186979055799
	WORD $0xd3072cc5; WORD $0x5cadf5bf  // .quad 6678264026688335045
	WORD $0x01ab551c; WORD $0xcb090c80  // .quad -3816505465296431844
	WORD $0xc7c8f7f6; WORD $0x73d9732f  // .quad 8347830033360418806
	WORD $0x02162a63; WORD $0xfdcb4fa0  // .quad -158945813193151901
	WORD $0xdcdd9afa; WORD $0x2867e7fd  // .quad 2911550761636567802
	WORD $0x014dda7e; WORD $0x9e9f11c4  // .quad -7016870160886801794
	WORD $0x541501b8; WORD $0xb281e1fd  // .quad -5583933584809066056
	WORD $0x01a1511d; WORD $0xc646d635  // .quad -4159401682681114339
	WORD $0xa91a4226; WORD $0x1f225a7c  // .quad 2243455055843443238
	WORD $0x4209a565; WORD $0xf7d88bc2  // .quad -587566084924005019
	WORD $0xe9b06958; WORD $0x3375788d  // .quad 3708002419115845976
	WORD $0x6946075f; WORD $0x9ae75759  // .quad -7284757830718584993
	WORD $0x641c83ae; WORD $0x0052d6b1  // .quad 23317005467419566
	WORD $0xc3978937; WORD $0xc1a12d2f  // .quad -4494261269970843337
	WORD $0xbd23a49a; WORD $0xc0678c5d  // .quad -4582539761593113446
	WORD $0xb47d6b84; WORD $0xf209787b  // .quad -1006140569036166268
	WORD $0x963646e0; WORD $0xf840b7ba  // .quad -558244341782001952
	WORD $0x50ce6332; WORD $0x9745eb4d  // .quad -7546366883288685774
	WORD $0x3bc3d898; WORD $0xb650e5a9  // .quad -5309491445654890344
	WORD $0xa501fbff; WORD $0xbd176620  // .quad -4821272585683469313
	WORD $0x8ab4cebe; WORD $0xa3e51f13  // .quad -6636864307068612930
	WORD $0xce427aff; WORD $0xec5d3fa8  // .quad -1414904713676948737
	WORD $0x36b10137; WORD $0xc66f336c  // .quad -4148040191917883081
	WORD $0x80e98cdf; WORD $0x93ba47c9  // .quad -7801844473689174817
	WORD $0x445d4184; WORD $0xb80b0047  // .quad -5185050239897353852
	WORD $0xe123f017; WORD $0xb8a8d9bb  // .quad -5140619573684080617
	WORD $0x157491e5; WORD $0xa60dc059  // .quad -6481312799871692315
	WORD $0xd96cec1d; WORD $0xe6d3102a  // .quad -1814088448677712867
	WORD $0xad68db2f; WORD $0x87c89837  // .quad -8662506518347195601
	WORD $0xc7e41392; WORD $0x9043ea1a  // .quad -8051334308064652398
	WORD $0x98c311fb; WORD $0x29babe45  // .quad 3006924907348169211
	WORD $0x79dd1877; WORD $0xb454e4a1  // .quad -5452481866653427593
	WORD $0xfef3d67a; WORD $0xf4296dd6  // .quad -853029884242176390
	WORD $0xd8545e94; WORD $0xe16a1dc9  // .quad -2203916314889396588
	WORD $0x5f58660c; WORD $0x1899e4a6  // .quad 1772699331562333708
	WORD $0x2734bb1d; WORD $0x8ce2529e  // .quad -8294976724446954723
	WORD $0xf72e7f8f; WORD $0x5ec05dcf  // .quad 6827560182880305039
	WORD $0xb101e9e4; WORD $0xb01ae745  // .quad -5757034887131305500
	WORD $0xf4fa1f73; WORD $0x76707543  // .quad 8534450228600381299
	WORD $0x1d42645d; WORD $0xdc21a117  // .quad -2584607590486743971
	WORD $0x791c53a8; WORD $0x6a06494a  // .quad 7639874402088932264
	WORD $0x72497eba; WORD $0x899504ae  // .quad -8532908771695296838
	WORD $0x17636892; WORD $0x0487db9d  // .quad 326470965756389522
	WORD $0x0edbde69; WORD $0xabfa45da  // .quad -6054449946191733143
	WORD $0x5d3c42b6; WORD $0x45a9d284  // .quad 5019774725622874806
	WORD $0x9292d603; WORD $0xd6f8d750  // .quad -2956376414312278525
	WORD $0xba45a9b2; WORD $0x0b8a2392  // .quad 831516194300602802
	WORD $0x5b9bc5c2; WORD $0x865b8692  // .quad -8765264286586255934
	WORD $0x68d7141e; WORD $0x8e6cac77  // .quad -8183976793979022306
	WORD $0xf282b732; WORD $0xa7f26836  // .quad -6344894339805432014
	WORD $0x430cd926; WORD $0x3207d795  // .quad 3605087062808385830
	WORD $0xaf2364ff; WORD $0xd1ef0244  // .quad -3319431906329402113
	WORD $0x49e807b8; WORD $0x7f44e6bd  // .quad 9170708441896323000
	WORD $0xed761f1f; WORD $0x8335616a  // .quad -8992173969096958177
	WORD $0x9c6209a6; WORD $0x5f16206c  // .quad 6851699533943015846
	WORD $0xa8d3a6e7; WORD $0xa402b9c5  // .quad -6628531442943809817
	WORD $0xc37a8c0f; WORD $0x36dba887  // .quad 3952938399001381903
	WORD $0x130890a1; WORD $0xcd036837  // .quad -3673978285252374367
	WORD $0xda2c9789; WORD $0xc2494954  // .quad -4446942528265218167
	WORD $0x6be55a64; WORD $0x80222122  // .quad -9213765455923815836
	WORD $0x10b7bd6c; WORD $0xf2db9baa  // .quad -946992141904134804
	WORD $0x06deb0fd; WORD $0xa02aa96b  // .quad -6905520801477381891
	WORD $0x94e5acc7; WORD $0x6f928294  // .quad 8039631859474607303
	WORD $0xc8965d3d; WORD $0xc83553c5  // .quad -4020214983419339459
	WORD $0xba1f17f9; WORD $0xcb772339  // .quad -3785518230938904583
	WORD $0x3abbf48c; WORD $0xfa42a8b7  // .quad -413582710846786420
	WORD $0x14536efb; WORD $0xff2a7604  // .quad -60105885123121413
	WORD $0x84b578d7; WORD $0x9c69a972  // .quad -7176018221920323369
	WORD $0x19684aba; WORD $0xfef51385  // .quad -75132356403901766
	WORD $0x25e2d70d; WORD $0xc38413cf  // .quad -4358336758973016307
	WORD $0x5fc25d69; WORD $0x7eb25866  // .quad 9129456591349898601
	WORD $0xef5b8cd1; WORD $0xf46518c2  // .quad -836234930288882479
	WORD $0xfbd97a61; WORD $0xef2f773f  // .quad -1211618658047395231
	WORD $0xd5993802; WORD $0x98bf2f79  // .quad -7440175859071633406
	WORD $0xfacfd8fa; WORD $0xaafb550f  // .quad -6126209340986631942
	WORD $0x4aff8603; WORD $0xbeeefb58  // .quad -4688533805412153853
	WORD $0xf983cf38; WORD $0x95ba2a53  // .quad -7657761676233289928
	WORD $0x5dbf6784; WORD $0xeeaaba2e  // .quad -1248981238337804412
	WORD $0x7bf26183; WORD $0xdd945a74  // .quad -2480258038432112253
	WORD $0xfa97a0b2; WORD $0x952ab45c  // .quad -7698142301602209614
	WORD $0x9aeef9e4; WORD $0x94f97111  // .quad -7712008566467528220
	WORD $0x393d88df; WORD $0xba756174  // .quad -5010991858575374113
	WORD $0x01aab85d; WORD $0x7a37cd56  // .quad 8806733365625141341
	WORD $0x478ceb17; WORD $0xe912b9d1  // .quad -1652053804791829737
	WORD $0xc10ab33a; WORD $0xac62e055  // .quad -6025006692552756422
	WORD $0xccb812ee; WORD $0x91abb422  // .quad -7950062655635975442
	WORD $0x314d6009; WORD $0x577b986b  // .quad 6303799689591218185
	WORD $0x7fe617aa; WORD $0xb616a12b  // .quad -5325892301117581398
	WORD $0xfda0b80b; WORD $0xed5a7e85  // .quad -1343622424865753077
	WORD $0x5fdf9d94; WORD $0xe39c4976  // .quad -2045679357969588844
	WORD $0xbe847307; WORD $0x14588f13  // .quad 1466078993672598279
	WORD $0xfbebc27d; WORD $0x8e41ade9  // .quad -8196078626372074883
	WORD $0xae258fc8; WORD $0x596eb2d8  // .quad 6444284760518135752
	WORD $0x7ae6b31c; WORD $0xb1d21964  // .quad -5633412264537705700
	WORD $0xd9aef3bb; WORD $0x6fca5f8e  // .quad 8055355950647669691
	WORD $0x99a05fe3; WORD $0xde469fbd  // .quad -2430079312244744221
	WORD $0x480d5854; WORD $0x25de7bb9  // .quad 2728754459941099604
	WORD $0x80043bee; WORD $0x8aec23d6  // .quad -8436328597794046994
	WORD $0x9a10ae6a; WORD $0xaf561aa7  // .quad -5812428961928401302
	WORD $0x20054ae9; WORD $0xada72ccc  // .quad -5933724728815170839
	WORD $0x8094da04; WORD $0x1b2ba151  // .quad 1957835834444274180
	WORD $0x28069da4; WORD $0xd910f7ff  // .quad -2805469892591575644
	WORD $0xf05d0842; WORD $0x90fb44d2  // .quad -7999724640327104446
	WORD $0x79042286; WORD $0x87aa9aff  // .quad -8670947710510816634
	WORD $0xac744a53; WORD $0x353a1607  // .quad 3835402254873283155
	WORD $0x57452b28; WORD $0xa99541bf  // .quad -6226998619711132888
	WORD $0x97915ce8; WORD $0x42889b89  // .quad 4794252818591603944
	WORD $0x2d1675f2; WORD $0xd3fa922f  // .quad -3172062256211528206
	WORD $0xfebada11; WORD $0x69956135  // .quad 7608094030047140369
	WORD $0x7c2e09b7; WORD $0x847c9b5d  // .quad -8900067937773286985
	WORD $0x7e699095; WORD $0x43fab983  // .quad 4898431519131537557
	WORD $0xdb398c25; WORD $0xa59bc234  // .quad -6513398903789220827
	WORD $0x5e03f4bb; WORD $0x94f967e4  // .quad -7712018656367741765
	WORD $0x1207ef2e; WORD $0xcf02b2c2  // .quad -3530062611309138130
	WORD $0xbac278f5; WORD $0x1d1be0ee  // .quad 2097517367411243253
	WORD $0x4b44f57d; WORD $0x8161afb9  // .quad -9123818159709293187
	WORD $0x69731732; WORD $0x6462d92a  // .quad 7233582727691441970
	WORD $0x9e1632dc; WORD $0xa1ba1ba7  // .quad -6793086681209228580
	WORD $0x03cfdcfe; WORD $0x7d7b8f75  // .quad 9041978409614302462
	WORD $0x859bbf93; WORD $0xca28a291  // .quad -3879672333084147821
	WORD $0x44c3d43e; WORD $0x5cda7352  // .quad 6690786993590490174
	WORD $0xe702af78; WORD $0xfcb2cb35  // .quad -237904397927796872
	WORD $0x6afa64a7; WORD $0x3a088813  // .quad 4181741870994056359
	WORD $0xb061adab; WORD $0x9defbf01  // .quad -7066219276345954901
	WORD $0x45b8fdd0; WORD $0x088aaa18  // .quad 615491320315182544
	WORD $0x1c7a1916; WORD $0xc56baec2  // .quad -4221088077005055722
	WORD $0x57273d45; WORD $0x8aad549e  // .quad -8454007886460797627
	WORD $0xa3989f5b; WORD $0xf6c69a72  // .quad -664674077828931749
	WORD $0xf678864b; WORD $0x36ac54e2  // .quad 3939617107816777291
	WORD $0xa63f6399; WORD $0x9a3c2087  // .quad -7332950326284164199
	WORD $0xb416a7dd; WORD $0x84576a1b  // .quad -8910536670511192099
	WORD $0x8fcf3c7f; WORD $0xc0cb28a9  // .quad -4554501889427817345
	WORD $0xa11c51d5; WORD $0x656d44a2  // .quad 7308573235570561493
	WORD $0xf3c30b9f; WORD $0xf0fdf2d3  // .quad -1081441343357383777
	WORD $0xa4b1b325; WORD $0x9f644ae5  // .quad -6961356773836868827
	WORD $0x7859e743; WORD $0x969eb7c4  // .quad -7593429867239446717
	WORD $0x0dde1fee; WORD $0x873d5d9f  // .quad -8701695967296086034
	WORD $0x96706114; WORD $0xbc4665b5  // .quad -4880101315621920492
	WORD $0xd155a7ea; WORD $0xa90cb506  // .quad -6265433940692719638
	WORD $0xfc0c7959; WORD $0xeb57ff22  // .quad -1488440626100012711
	WORD $0x42d588f2; WORD $0x09a7f124  // .quad 695789805494438130
	WORD $0xdd87cbd8; WORD $0x9316ff75  // .quad -7847804418953589800
	WORD $0x538aeb2f; WORD $0x0c11ed6d  // .quad 869737256868047663
	WORD $0x54e9bece; WORD $0xb7dcbf53  // .quad -5198069505264599346
	WORD $0xa86da5fa; WORD $0x8f1668c8  // .quad -8136200465769716230
	WORD $0x2a242e81; WORD $0xe5d3ef28  // .quad -1885900863153361279
	WORD $0x694487bc; WORD $0xf96e017d  // .quad -473439272678684740
	WORD $0x1a569d10; WORD $0x8fa47579  // .quad -8096217067111932656
	WORD $0xc395a9ac; WORD $0x37c981dc  // .quad 4019886927579031980
	WORD $0x60ec4455; WORD $0xb38d92d7  // .quad -5508585315462527915
	WORD $0xf47b1417; WORD $0x85bbe253  // .quad -8810199395808373737
	WORD $0x3927556a; WORD $0xe070f78d  // .quad -2274045625900771990
	WORD $0x78ccec8e; WORD $0x93956d74  // .quad -7812217631593927538
	WORD $0x43b89562; WORD $0x8c469ab8  // .quad -8338807543829064350
	WORD $0x970027b2; WORD $0x387ac8d1  // .quad 4069786015789754290
	WORD $0x54a6babb; WORD $0xaf584166  // .quad -5811823411358942533
	WORD $0xfcc0319e; WORD $0x06997b05  // .quad 475546501309804958
	WORD $0xe9d0696a; WORD $0xdb2e51bf  // .quad -2653093245771290262
	WORD $0xbdf81f03; WORD $0x441fece3  // .quad 4908902581746016003
	WORD $0xf22241e2; WORD $0x88fcf317  // .quad -8575712306248138270
	WORD $0xad7626c3; WORD $0xd527e81c  // .quad -3087243809672255805
	WORD $0xeeaad25a; WORD $0xab3c2fdd  // .quad -6107954364382784934
	WORD $0xd8d3b074; WORD $0x8a71e223  // .quad -8470740780517707660
	WORD $0x6a5586f1; WORD $0xd60b3bd5  // .quad -3023256937051093263
	WORD $0x67844e49; WORD $0xf6872d56  // .quad -682526969396179383
	WORD $0x62757456; WORD $0x85c70565  // .quad -8807064613298015146
	WORD $0x016561db; WORD $0xb428f8ac  // .quad -5464844730172612133
	WORD $0xbb12d16c; WORD $0xa738c6be  // .quad -6397144748195131028
	WORD $0x01beba52; WORD $0xe13336d7  // .quad -2219369894288377262
	WORD $0x69d785c7; WORD $0xd106f86e  // .quad -3384744916816525881
	WORD $0x61173473; WORD $0xecc00246  // .quad -1387106183930235789
	WORD $0x0226b39c; WORD $0x82a45b45  // .quad -9032994600651410532
	WORD $0xf95d0190; WORD $0x27f002d7  // .quad 2877803288514593168
	WORD $0x42b06084; WORD $0xa34d7216  // .quad -6679557232386875260
	WORD $0xf7b441f4; WORD $0x31ec038d  // .quad 3597254110643241460
	WORD $0xd35c78a5; WORD $0xcc20ce9b  // .quad -3737760522056206171
	WORD $0x75a15271; WORD $0x7e670471  // .quad 9108253656731439729
	WORD $0xc83396ce; WORD $0xff290242  // .quad -60514634142869810
	WORD $0xe984d386; WORD $0x0f0062c6  // .quad 1080972517029761926
	WORD $0xbd203e41; WORD $0x9f79a169  // .quad -6955350673980375487
	WORD $0xa3e60868; WORD $0x52c07b78  // .quad 5962901664714590312
	WORD $0x2c684dd1; WORD $0xc75809c4  // .quad -4082502324048081455
	WORD $0xccdf8a82; WORD $0xa7709a56  // .quad -6381430974388925822
	WORD $0x37826145; WORD $0xf92e0c35  // .quad -491441886632713915
	WORD $0x400bb691; WORD $0x88a66076  // .quad -8600080377420466543
	WORD $0x42b17ccb; WORD $0x9bbcc7a1  // .quad -7224680206786528053
	WORD $0xd00ea435; WORD $0x6acff893  // .quad 7696643601933968437
	WORD $0x935ddbfe; WORD $0xc2abf989  // .quad -4419164240055772162
	WORD $0xc4124d43; WORD $0x0583f6b8  // .quad 397432465562684739
	WORD $0xf83552fe; WORD $0xf356f7eb  // .quad -912269281642327298
	WORD $0x7a8b704a; WORD $0xc3727a33  // .quad -4363290727450709942
	WORD $0x7b2153de; WORD $0x98165af3  // .quad -7487697328667536418
	WORD $0x592e4c5c; WORD $0x744f18c0  // .quad 8380944645968776284
	WORD $0x59e9a8d6; WORD $0xbe1bf1b0  // .quad -4747935642407032618
	WORD $0x6f79df73; WORD $0x1162def0  // .quad 1252808770606194547
	WORD $0x7064130c; WORD $0xeda2ee1c  // .quad -1323233534581402868
	WORD $0x45ac2ba8; WORD $0x8addcb56  // .quad -8440366555225904216
	WORD $0xc63e8be7; WORD $0x9485d4d1  // .quad -7744549986754458649
	WORD $0xd7173692; WORD $0x6d953e2b  // .quad 7896285879677171346
	WORD $0x37ce2ee1; WORD $0xb9a74a06  // .quad -5069001465015685407
	WORD $0xccdd0437; WORD $0xc8fa8db6  // .quad -3964700705685699529
	WORD $0xc5c1ba99; WORD $0xe8111c87  // .quad -1724565812842218855
	WORD $0x400a22a2; WORD $0x1d9c9892  // .quad 2133748077373825698
	WORD $0xdb9914a0; WORD $0x910ab1d4  // .quad -7995382660667468640
	WORD $0xd00cab4b; WORD $0x2503beb6  // .quad 2667185096717282123
	WORD $0x127f59c8; WORD $0xb54d5e4a  // .quad -5382542307406947896
	WORD $0x840fd61d; WORD $0x2e44ae64  // .quad 3333981370896602653
	WORD $0x971f303a; WORD $0xe2a0b5dc  // .quad -2116491865831296966
	WORD $0xd289e5d2; WORD $0x5ceaecfe  // .quad 6695424375237764562
	WORD $0xde737e24; WORD $0x8da471a9  // .quad -8240336443785642460
	WORD $0x872c5f47; WORD $0x7425a83e  // .quad 8369280469047205703
	WORD $0x56105dad; WORD $0xb10d8e14  // .quad -5688734536304665171
	WORD $0x28f77719; WORD $0xd12f124e  // .quad -3373457468973156583
	WORD $0x6b947518; WORD $0xdd50f199  // .quad -2499232151953443560
	WORD $0xd99aaa6f; WORD $0x82bd6b70  // .quad -9025939945749304721
	WORD $0xe33cc92f; WORD $0x8a5296ff  // .quad -8479549122611984081
	WORD $0x1001550b; WORD $0x636cc64d  // .quad 7164319141522920715
	WORD $0xdc0bfb7b; WORD $0xace73cbf  // .quad -5987750384837592197
	WORD $0x5401aa4e; WORD $0x3c47f7e0  // .quad 4343712908476262990
	WORD $0xd30efa5a; WORD $0xd8210bef  // .quad -2873001962619602342
	WORD $0x34810a71; WORD $0x65acfaec  // .quad 7326506586225052273
	WORD $0xe3e95c78; WORD $0x8714a775  // .quad -8713155254278333320
	WORD $0x41a14d0d; WORD $0x7f1839a7  // .quad 9158133232781315341
	WORD $0x5ce3b396; WORD $0xa8d9d153  // .quad -6279758049420528746
	WORD $0x1209a050; WORD $0x1ede4811  // .quad 2224294504121868368
	WORD $0x341ca07c; WORD $0xd31045a8  // .quad -3238011543348273028
	WORD $0xab460432; WORD $0x934aed0a  // .quad -7833187971778608078
	WORD $0x2091e44d; WORD $0x83ea2b89  // .quad -8941286242233752499
	WORD $0x5617853f; WORD $0xf81da84d  // .quad -568112927868484289
	WORD $0x68b65d60; WORD $0xa4e4b66b  // .quad -6564921784364802720
	WORD $0xab9d668e; WORD $0x36251260  // .quad 3901544858591782542
	WORD $0x42e3f4b9; WORD $0xce1de406  // .quad -3594466212028615495
	WORD $0x6b426019; WORD $0xc1d72b7c  // .quad -4479063491021217767
	WORD $0xe9ce78f3; WORD $0x80d2ae83  // .quad -9164070410158966541
	WORD $0x8612f81f; WORD $0xb24cf65b  // .quad -5598829363776522209
	WORD $0xe4421730; WORD $0xa1075a24  // .quad -6843401994271320272
	WORD $0x6797b627; WORD $0xdee033f2  // .quad -2386850686293264857
	WORD $0x1d529cfc; WORD $0xc94930ae  // .quad -3942566474411762436
	WORD $0x017da3b1; WORD $0x169840ef  // .quad 1628122660560806833
	WORD $0xa4a7443c; WORD $0xfb9b7cd9  // .quad -316522074587315140
	WORD $0x60ee864e; WORD $0x8e1f2895  // .quad -8205795374004271538
	WORD $0x06e88aa5; WORD $0x9d412e08  // .quad -7115355324258153819
	WORD $0xb92a27e2; WORD $0xf1a6f2ba  // .quad -1033872180650563614
	WORD $0x08a2ad4e; WORD $0xc491798a  // .quad -4282508136895304370
	WORD $0x6774b1db; WORD $0xae10af69  // .quad -5904026244240592421
	WORD $0x8acb58a2; WORD $0xf5b5d7ec  // .quad -741449152691742558
	WORD $0xe0a8ef29; WORD $0xacca6da1  // .quad -5995859411864064215
	WORD $0xd6bf1765; WORD $0x9991a6f3  // .quad -7380934748073420955
	WORD $0x58d32af3; WORD $0x17fd090a  // .quad 1728547772024695539
	WORD $0xcc6edd3f; WORD $0xbff610b0  // .quad -4614482416664388289
	WORD $0xef07f5b0; WORD $0xddfc4b4c  // .quad -2451001303396518480
	WORD $0xff8a948e; WORD $0xeff394dc  // .quad -1156417002403097458
	WORD $0x1564f98e; WORD $0x4abdaf10  // .quad 5385653213018257806
	WORD $0x1fb69cd9; WORD $0x95f83d0a  // .quad -7640289654143017767
	WORD $0x1abe37f1; WORD $0x9d6d1ad4  // .quad -7102991539009341455
	WORD $0xa7a4440f; WORD $0xbb764c4c  // .quad -4938676049251384305
	WORD $0x216dc5ed; WORD $0x84c86189  // .quad -8878739423761676819
	WORD $0xd18d5513; WORD $0xea53df5f  // .quad -1561659043136842477
	WORD $0xb4e49bb4; WORD $0x32fd3cf5  // .quad 3674159897003727796
	WORD $0xe2f8552c; WORD $0x92746b9b  // .quad -7893565929601608404
	WORD $0x221dc2a1; WORD $0x3fbc8c33  // .quad 4592699871254659745
	WORD $0xdbb66a77; WORD $0xb7118682  // .quad -5255271393574622601
	WORD $0xeaa5334a; WORD $0x0fabaf3f  // .quad 1129188820640936778
	WORD $0x92a40515; WORD $0xe4d5e823  // .quad -1957403223540890347
	WORD $0xf2a7400e; WORD $0x29cb4d87  // .quad 3011586022114279438
	WORD $0x3ba6832d; WORD $0x8f05b116  // .quad -8140906042354138323
	WORD $0xef511012; WORD $0x743e20e9  // .quad 8376168546070237202
	WORD $0xca9023f8; WORD $0xb2c71d5b  // .quad -5564446534515285000
	WORD $0x6b255416; WORD $0x914da924  // .quad -7976533391121755114
	WORD $0xbd342cf6; WORD $0xdf78e4b2  // .quad -2343872149716718346
	WORD $0xc2f7548e; WORD $0x1ad089b6  // .quad 1932195658189984910
	WORD $0xb6409c1a; WORD $0x8bab8eef  // .quad -8382449121214030822
	WORD $0x73b529b1; WORD $0xa184ac24  // .quad -6808127464117294671
	WORD $0xa3d0c320; WORD $0xae9672ab  // .quad -5866375383090150624
	WORD $0x90a2741e; WORD $0xc9e5d72d  // .quad -3898473311719230434
	WORD $0x8cc4f3e8; WORD $0xda3c0f56  // .quad -2721283210435300376
	WORD $0x7a658892; WORD $0x7e2fa67c  // .quad 9092669226243950738
	WORD $0x17fb1871; WORD $0x88658996  // .quad -8618331034163144591
	WORD $0x98feeab7; WORD $0xddbb901b  // .quad -2469221522477225289
	WORD $0x9df9de8d; WORD $0xaa7eebfb  // .quad -6161227774276542835
	WORD $0x7f3ea565; WORD $0x552a7422  // .quad 6136845133758244197
	WORD $0x85785631; WORD $0xd51ea6fa  // .quad -3089848699418290639
	WORD $0x8f87275f; WORD $0xd53a8895  // .quad -3082000819042179233
	WORD $0x936b35de; WORD $0x8533285c  // .quad -8848684464777513506
	WORD $0xf368f137; WORD $0x8a892aba  // .quad -8464187042230111945
	WORD $0xb8460356; WORD $0xa67ff273  // .quad -6449169562544503978
	WORD $0xb0432d85; WORD $0x2d2b7569  // .quad 3254824252494523781
	WORD $0xa657842c; WORD $0xd01fef10  // .quad -3449775934753242068
	WORD $0x0e29fc73; WORD $0x9c3b2962  // .quad -7189106879045698445
	WORD $0x67f6b29b; WORD $0x8213f56a  // .quad -9073638986861858149
	WORD $0x91b47b8f; WORD $0x8349f3ba  // .quad -8986383598807123057
	WORD $0x01f45f42; WORD $0xa298f2c5  // .quad -6730362715149934782
	WORD $0x36219a73; WORD $0x241c70a9  // .quad 2602078556773259891
	WORD $0x42717713; WORD $0xcb3f2f76  // .quad -3801267375510030573
	WORD $0x83aa0110; WORD $0xed238cd3  // .quad -1359087822460813040
	WORD $0xd30dd4d7; WORD $0xfe0efb53  // .quad -139898200960150313
	WORD $0x324a40aa; WORD $0xf4363804  // .quad -849429889038008150
	WORD $0x63e8a506; WORD $0x9ec95d14  // .quad -7004965403241175802
	WORD $0x3edcd0d5; WORD $0xb143c605  // .quad -5673473379724898091
	WORD $0x7ce2ce48; WORD $0xc67bb459  // .quad -4144520735624081848
	WORD $0x8e94050a; WORD $0xdd94b786  // .quad -2480155706228734710
	WORD $0xdc1b81da; WORD $0xf81aa16f  // .quad -568964901102714406
	WORD $0x191c8326; WORD $0xca7cf2b4  // .quad -3855940325606653146
	WORD $0xe9913128; WORD $0x9b10a4e5  // .quad -7273132090830278360
	WORD $0x1f63a3f0; WORD $0xfd1c2f61  // .quad -208239388580928528
	WORD $0x63f57d72; WORD $0xc1d4ce1f  // .quad -4479729095110460046
	WORD $0x673c8cec; WORD $0xbc633b39  // .quad -4871985254153548564
	WORD $0x3cf2dccf; WORD $0xf24a01a7  // .quad -987975350460687153
	WORD $0xe085d813; WORD $0xd5be0503  // .quad -3044990783845967853
	WORD $0x8617ca01; WORD $0x976e4108  // .quad -7535013621679011327
	WORD $0xd8a74e18; WORD $0x4b2d8644  // .quad 5417133557047315992
	WORD $0xa79dbc82; WORD $0xbd49d14a  // .quad -4807081008671376254
	WORD $0x0ed1219e; WORD $0xddf8e7d6  // .quad -2451955090545630818
	WORD $0x51852ba2; WORD $0xec9c459d  // .quad -1397165242411832414
	WORD $0xc942b503; WORD $0xcabb90e5  // .quad -3838314940804713213
	WORD $0x52f33b45; WORD $0x93e1ab82  // .quad -7790757304148477115
	WORD $0x3b936243; WORD $0x3d6a751f  // .quad 4425478360848884291
	WORD $0xe7b00a17; WORD $0xb8da1662  // .quad -5126760611758208489
	WORD $0x0a783ad4; WORD $0x0cc51267  // .quad 920161932633717460
	WORD $0xa19c0c9d; WORD $0xe7109bfb  // .quad -1796764746270372707
	WORD $0x668b24c5; WORD $0x27fb2b80  // .quad 2880944217109767365
	WORD $0x450187e2; WORD $0x906a617d  // .quad -8040506994060064798
	WORD $0x802dedf6; WORD $0xb1f9f660  // .quad -5622191765467566602
	WORD $0x9641e9da; WORD $0xb484f9dc  // .quad -5438947724147693094
	WORD $0xa0396973; WORD $0x5e7873f8  // .quad 6807318348447705459
	WORD $0xbbd26451; WORD $0xe1a63853  // .quad -2186998636757228463
	WORD $0x6423e1e8; WORD $0xdb0b487b  // .quad -2662955059861265944
	WORD $0x55637eb2; WORD $0x8d07e334  // .quad -8284403175614349646
	WORD $0x3d2cda62; WORD $0x91ce1a9a  // .quad -7940379843253970334
	WORD $0x6abc5e5f; WORD $0xb049dc01  // .quad -5743817951090549153
	WORD $0xcc7810fb; WORD $0x7641a140  // .quad 8521269269642088699
	WORD $0xc56b75f7; WORD $0xdc5c5301  // .quad -2568086420435798537
	WORD $0x7fcb0a9d; WORD $0xa9e904c8  // .quad -6203421752542164323
	WORD $0x1b6329ba; WORD $0x89b9b3e1  // .quad -8522583040413455942
	WORD $0x9fbdcd44; WORD $0x546345fa  // .quad 6080780864604458308
	WORD $0x623bf429; WORD $0xac2820d9  // .quad -6041542782089432023
	WORD $0x47ad4095; WORD $0xa97c1779  // .quad -6234081974526590827
	WORD $0xbacaf133; WORD $0xd732290f  // .quad -2940242459184402125
	WORD $0xcccc485d; WORD $0x49ed8eab  // .quad 5327070802775656541
	WORD $0xd4bed6c0; WORD $0x867f59a9  // .quad -8755180564631333184
	WORD $0xbfff5a74; WORD $0x5c68f256  // .quad 6658838503469570676
	WORD $0x49ee8c70; WORD $0xa81f3014  // .quad -6332289687361778576
	WORD $0x6fff3111; WORD $0x73832eec  // .quad 8323548129336963345
	WORD $0x5c6a2f8c; WORD $0xd226fc19  // .quad -3303676090774835316
	WORD $0xc5ff7eab; WORD $0xc831fd53  // .quad -4021154456019173717
	WORD $0xd9c25db7; WORD $0x83585d8f  // .quad -8982326584375353929
	WORD $0xb77f5e55; WORD $0xba3e7ca8  // .quad -5026443070023967147
	WORD $0xd032f525; WORD $0xa42e74f3  // .quad -6616222212041804507
	WORD $0xe55f35eb; WORD $0x28ce1bd2  // .quad 2940318199324816875
	WORD $0xc43fb26f; WORD $0xcd3a1230  // .quad -3658591746624867729
	WORD $0xcf5b81b3; WORD $0x7980d163  // .quad 8755227902219092403
	WORD $0x7aa7cf85; WORD $0x80444b5e  // .quad -9204148869281624187
	WORD $0xc332621f; WORD $0xd7e105bc  // .quad -2891023177508298209
	WORD $0x1951c366; WORD $0xa0555e36  // .quad -6893500068174642330
	WORD $0xf3fefaa7; WORD $0x8dd9472b  // .quad -8225464990312760665
	WORD $0x9fa63440; WORD $0xc86ab5c3  // .quad -4005189066790915008
	WORD $0xf0feb951; WORD $0xb14f98f6  // .quad -5670145219463562927
	WORD $0x878fc150; WORD $0xfa856334  // .quad -394800315061255856
	WORD $0x569f33d3; WORD $0x6ed1bf9a  // .quad 7985374283903742931
	WORD $0xd4b9d8d2; WORD $0x9c935e00  // .quad -7164279224554366766
	WORD $0xec4700c8; WORD $0x0a862f80  // .quad 758345818024902856
	WORD $0x09e84f07; WORD $0xc3b83581  // .quad -4343663012265570553
	WORD $0x2758c0fa; WORD $0xcd27bb61  // .quad -3663753745896259334
	WORD $0x4c6262c8; WORD $0xf4a642e1  // .quad -817892746904575288
	WORD $0xb897789c; WORD $0x8038d51c  // .quad -9207375118826243940
	WORD $0xcfbd7dbd; WORD $0x98e7e9cc  // .quad -7428711994456441411
	WORD $0xe6bd56c3; WORD $0xe0470a63  // .quad -2285846861678029117
	WORD $0x03acdd2c; WORD $0xbf21e440  // .quad -4674203974643163860
	WORD $0xe06cac74; WORD $0x1858ccfc  // .quad 1754377441329851508
	WORD $0x04981478; WORD $0xeeea5d50  // .quad -1231068949876566920
	WORD $0x0c43ebc8; WORD $0x0f37801e  // .quad 1096485900831157192
	WORD $0x02df0ccb; WORD $0x95527a52  // .quad -7686947121313936181
	WORD $0x8f54e6ba; WORD $0xd3056025  // .quad -3241078642388441414
	WORD $0x8396cffd; WORD $0xbaa718e6  // .quad -4996997883215032323
	WORD $0xf32a2069; WORD $0x47c6b82e  // .quad 5172023733869224041
	WORD $0x247c83fd; WORD $0xe950df20  // .quad -1634561335591402499
	WORD $0x57fa5441; WORD $0x4cdc331d  // .quad 5538357842881958977
	WORD $0x16cdd27e; WORD $0x91d28b74  // .quad -7939129862385708418
	WORD $0xadf8e952; WORD $0xe0133fe4  // .quad -2300424733252327086
	WORD $0x1c81471d; WORD $0xb6472e51  // .quad -5312226309554747619
	WORD $0xd97723a6; WORD $0x58180fdd  // .quad 6347841120289366950
	WORD $0x63a198e5; WORD $0xe3d8f9e5  // .quad -2028596868516046619
	WORD $0xa7ea7648; WORD $0x570f09ea  // .quad 6273243709394548296
	WORD $0x5e44ff8f; WORD $0x8e679c2f  // .quad -8185402070463610993
	WORD $0x51e513da; WORD $0x2cd2cc65  // .quad 3229868618315797466
	WORD $0x35d63f73; WORD $0xb201833b  // .quad -5620066569652125837
	WORD $0xa65e58d1; WORD $0xf8077f7e  // .quad -574350245532641071
	WORD $0x034bcf4f; WORD $0xde81e40a  // .quad -2413397193637769393
	WORD $0x27faf782; WORD $0xfb04afaf  // .quad -358968903457900670
	WORD $0x420f6191; WORD $0x8b112e86  // .quad -8425902273664687727
	WORD $0xf1f9b563; WORD $0x79c5db9a  // .quad 8774660907532399971
	WORD $0xd29339f6; WORD $0xadd57a27  // .quad -5920691823653471754
	WORD $0xae7822bc; WORD $0x18375281  // .quad 1744954097560724156
	WORD $0xc7380874; WORD $0xd94ad8b1  // .quad -2789178761139451788
	WORD $0x0d0b15b5; WORD $0x8f229391  // .quad -8132775725879323211
	WORD $0x1c830548; WORD $0x87cec76f  // .quad -8660765753353239224
	WORD $0x504ddb22; WORD $0xb2eb3875  // .quad -5554283638921766110
	WORD $0xe3a3c69a; WORD $0xa9c2794a  // .quad -6214271173264161126
	WORD $0xa46151eb; WORD $0x5fa60692  // .quad 6892203506629956075
	WORD $0x9c8cb841; WORD $0xd433179d  // .quad -3156152948152813503
	WORD $0xa6bcd333; WORD $0xdbc7c41b  // .quad -2609901835997359309
	WORD $0x81d7f328; WORD $0x849feec2  // .quad -8890124620236590296
	WORD $0x906c0800; WORD $0x12b9b522  // .quad 1349308723430688768
	WORD $0x224deff3; WORD $0xa5c7ea73  // .quad -6500969756868349965
	WORD $0x34870a00; WORD $0xd768226b  // .quad -2925050114139026944
	WORD $0xeae16bef; WORD $0xcf39e50f  // .quad -3514526177658049553
	WORD $0x00d46640; WORD $0xe6a11583  // .quad -1828156321336891840
	WORD $0xf2cce375; WORD $0x81842f29  // .quad -9114107888677362827
	WORD $0xc1097fd0; WORD $0x60495ae3  // .quad 6938176635183661008
	WORD $0x6f801c53; WORD $0xa1e53af4  // .quad -6780948842419315629
	WORD $0xb14bdfc4; WORD $0x385bb19c  // .quad 4061034775552188356
	WORD $0x8b602368; WORD $0xca5e89b1  // .quad -3864500034596756632
	WORD $0xdd9ed7b5; WORD $0x46729e03  // .quad 5076293469440235445
	WORD $0xee382c42; WORD $0xfcf62c1d  // .quad -218939024818557886
	WORD $0x6a8346d1; WORD $0x6c07a2c2  // .quad 7784369436827535057
	WORD $0xb4e31ba9; WORD $0x9e19db92  // .quad -7054365918152680535
	WORD $0x05241885; WORD $0xc7098b73  // .quad -4104596259247744891
	WORD $0x621be293; WORD $0xc5a05277  // .quad -4206271379263462765
	WORD $0xc66d1ea7; WORD $0xb8cbee4f  // .quad -5130745324059681113
	WORD $0x3aa2db38; WORD $0xf7086715  // .quad -646153205651940552
	WORD $0xdc043328; WORD $0x737f74f1  // .quad 8322499218531169064
	WORD $0x44a5c903; WORD $0x9a65406d  // .quad -7321374781173544701
	WORD $0x53053ff2; WORD $0x505f522e  // .quad 5791438004736573426
	WORD $0x95cf3b44; WORD $0xc0fe9088  // .quad -4540032458039542972
	WORD $0xe7c68fef; WORD $0x647726b9  // .quad 7239297505920716783
	WORD $0xbb430a15; WORD $0xf13e34aa  // .quad -1063354554122040811
	WORD $0x30dc19f5; WORD $0x5eca7834  // .quad 6830403950414141941
	WORD $0xb509e64d; WORD $0x96c6e0ea  // .quad -7582125623967357363
	WORD $0x3d132072; WORD $0xb67d1641  // .quad -5297053117264486286
	WORD $0x624c5fe0; WORD $0xbc789925  // .quad -4865971011531808800
	WORD $0x8c57e88f; WORD $0xe41c5bd1  // .quad -2009630378153219953
	WORD $0xbadf77d8; WORD $0xeb96bf6e  // .quad -1470777745987373096
	WORD $0xf7b6f159; WORD $0x8e91b962  // .quad -8173548013986844327
	WORD $0x34cbaae7; WORD $0x933e37a5  // .quad -7836765118883190041
	WORD $0xb5a4adb0; WORD $0x723627bb  // .quad 8229809056225996208
	WORD $0x81fe95a1; WORD $0xb80dc58e  // .quad -5184270380176599647
	WORD $0xa30dd91c; WORD $0xcec3b1aa  // .quad -3547796734999668452
	WORD $0x227e3b09; WORD $0xe61136f2  // .quad -1868651956793361655
	WORD $0xa5e8a7b1; WORD $0x213a4f0a  // .quad 2394313059052595121
	WORD $0x558ee4e6; WORD $0x8fcac257  // .quad -8085436500636932890
	WORD $0x4f62d19d; WORD $0xa988e2cd  // .quad -6230480713039031907
	WORD $0x2af29e1f; WORD $0xb3bd72ed  // .quad -5495109607368778209
	WORD $0xa33b8605; WORD $0x93eb1b80  // .quad -7788100891298789883
	WORD $0x75af45a7; WORD $0xe0accfa8  // .quad -2257200990783584857
	WORD $0x660533c3; WORD $0xbc72f130  // .quad -4867563057061743677
	WORD $0x498d8b88; WORD $0x8c6c01c9  // .quad -8328279646880822392
	WORD $0x7f8680b4; WORD $0xeb8fad7c  // .quad -1472767802899791692
	WORD $0x9bf0ee6a; WORD $0xaf87023b  // .quad -5798663540173640086
	WORD $0x9f6820e1; WORD $0xa67398db  // .quad -6452645772052127519
	WORD $0x82ed2a05; WORD $0xdb68c2ca  // .quad -2636643406789662203
	WORD $0x43a1148c; WORD $0x88083f89  // .quad -8644589625959967604
	WORD $0x91d43a43; WORD $0x892179be  // .quad -8565431156884620733
	WORD $0x948959b0; WORD $0x6a0a4f6b  // .quad 7641007041259592112
	WORD $0x364948d4; WORD $0xab69d82e  // .quad -6095102927678388012
	WORD $0x79abb01c; WORD $0x848ce346  // .quad -8895485272135061476
	WORD $0xc3db9b09; WORD $0xd6444e39  // .quad -3007192641170597111
	WORD $0x0c0b4e11; WORD $0xf2d80e0c  // .quad -947992276657025519
	WORD $0x1a6940e5; WORD $0x85eab0e4  // .quad -8797024428372705051
	WORD $0x0f0e2195; WORD $0x6f8e118f  // .quad 8038381691033493909
	WORD $0x2103911f; WORD $0xa7655d1d  // .quad -6384594517038493409
	WORD $0xd2d1a9fb; WORD $0x4b7195f2  // .quad 5436291095364479483
	WORD $0x69447567; WORD $0xd13eb464  // .quad -3369057127870728857
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00'
	  // .p2align 2, 0x00
_POW_TAB:
	WORD $0x00000001  // .long 1
	WORD $0x00000003  // .long 3
	WORD $0x00000006  // .long 6
	WORD $0x00000009  // .long 9
	WORD $0x0000000d  // .long 13
	WORD $0x00000010  // .long 16
	WORD $0x00000013  // .long 19
	WORD $0x00000017  // .long 23
	WORD $0x0000001a  // .long 26
	  // .p2align 2, 0x00
_LSHIFT_TAB:
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00'
	WORD $0x00000001  // .long 1
	WORD $0x00000035  // .asciz 4, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x00000001  // .long 1
	WORD $0x00003532  // .asciz 4, '25\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x00000001  // .long 1
	WORD $0x00353231  // .asciz 4, '125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x00000002  // .long 2
	WORD $0x00353236  // .asciz 4, '625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x00000002  // .long 2
	WORD $0x35323133  // .asciz 4, '3125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x00000002  // .long 2
	WORD $0x32363531  // .asciz 4, '15625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000035  // .asciz 4, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x00000003  // .long 3
	WORD $0x32313837  // .asciz 4, '78125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000035  // .asciz 4, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x00000003  // .long 3
	WORD $0x36303933  // .asciz 4, '390625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00003532  // .asciz 4, '25\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x00000003  // .long 3
	WORD $0x33353931  // .asciz 4, '1953125\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00353231  // .asciz 4, '125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x00000004  // .long 4
	WORD $0x35363739  // .asciz 4, '9765625\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00353236  // .asciz 4, '625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x00000004  // .long 4
	WORD $0x32383834  // .asciz 4, '48828125\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x35323138  // .asciz 4, '8125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x00000004  // .long 4
	WORD $0x31343432  // .asciz 4, '244140625\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x32363034  // .asciz 4, '40625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000035  // .asciz 4, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x00000004  // .long 4
	WORD $0x30323231  // .asciz 4, '1220703125\x00\x00\x00\x00\x00\x00'
	WORD $0x31333037  // .asciz 4, '703125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00003532  // .asciz 4, '25\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x00000005  // .long 5
	WORD $0x33303136  // .asciz 4, '6103515625\x00\x00\x00\x00\x00\x00'
	WORD $0x36353135  // .asciz 4, '515625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00003532  // .asciz 4, '25\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x00000005  // .long 5
	WORD $0x31353033  // .asciz 4, '30517578125\x00\x00\x00\x00\x00'
	WORD $0x38373537  // .asciz 4, '7578125\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00353231  // .asciz 4, '125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x00000005  // .long 5
	WORD $0x35323531  // .asciz 4, '152587890625\x00\x00\x00\x00'
	WORD $0x39383738  // .asciz 4, '87890625\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x35323630  // .asciz 4, '0625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x00000006  // .long 6
	WORD $0x39323637  // .asciz 4, '762939453125\x00\x00\x00\x00'
	WORD $0x35343933  // .asciz 4, '39453125\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x35323133  // .asciz 4, '3125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x00000006  // .long 6
	WORD $0x34313833  // .asciz 4, '3814697265625\x00\x00\x00'
	WORD $0x32373936  // .asciz 4, '697265625\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x32363536  // .asciz 4, '65625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000035  // .asciz 4, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x00000006  // .long 6
	WORD $0x37303931  // .asciz 4, '19073486328125\x00\x00'
	WORD $0x36383433  // .asciz 4, '3486328125\x00\x00\x00\x00\x00\x00'
	WORD $0x31383233  // .asciz 4, '328125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00003532  // .asciz 4, '25\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x00000007  // .long 7
	WORD $0x36333539  // .asciz 4, '95367431640625\x00\x00'
	WORD $0x31333437  // .asciz 4, '7431640625\x00\x00\x00\x00\x00\x00'
	WORD $0x36303436  // .asciz 4, '640625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00003532  // .asciz 4, '25\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x00000007  // .long 7
	WORD $0x38363734  // .asciz 4, '476837158203125\x00'
	WORD $0x35313733  // .asciz 4, '37158203125\x00\x00\x00\x00\x00'
	WORD $0x33303238  // .asciz 4, '8203125\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00353231  // .asciz 4, '125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x00000007  // .long 7
	WORD $0x34383332  // .asciz 4, '2384185791015625'
	WORD $0x37353831  // .asciz 4, '185791015625\x00\x00\x00\x00'
	WORD $0x31303139  // .asciz 4, '91015625\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x35323635  // .asciz 4, '5625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x00000007  // .long 7
	WORD $0x32393131  // .asciz 4, '1192092895507812'
	WORD $0x38323930  // .asciz 4, '0928955078125\x00\x00\x00'
	WORD $0x30353539  // .asciz 4, '955078125\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x32313837  // .asciz 4, '78125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000035  // .asciz 4, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x00000008  // .long 8
	WORD $0x30363935  // .asciz 4, '5960464477539062'
	WORD $0x34343634  // .asciz 4, '4644775390625\x00\x00\x00'
	WORD $0x33353737  // .asciz 4, '775390625\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x32363039  // .asciz 4, '90625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000035  // .asciz 4, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x00000008  // .long 8
	WORD $0x30383932  // .asciz 4, '2980232238769531'
	WORD $0x32323332  // .asciz 4, '23223876953125\x00\x00'
	WORD $0x36373833  // .asciz 4, '3876953125\x00\x00\x00\x00\x00\x00'
	WORD $0x31333539  // .asciz 4, '953125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00003532  // .asciz 4, '25\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x00000008  // .long 8
	WORD $0x30393431  // .asciz 4, '1490116119384765'
	WORD $0x31363131  // .asciz 4, '116119384765625\x00'
	WORD $0x38333931  // .asciz 4, '19384765625\x00\x00\x00\x00\x00'
	WORD $0x35363734  // .asciz 4, '4765625\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00353236  // .asciz 4, '625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x00000009  // .long 9
	WORD $0x30353437  // .asciz 4, '7450580596923828'
	WORD $0x35303835  // .asciz 4, '580596923828125\x00'
	WORD $0x32393639  // .asciz 4, '96923828125\x00\x00\x00\x00\x00'
	WORD $0x38323833  // .asciz 4, '3828125\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00353231  // .asciz 4, '125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x00000009  // .long 9
	WORD $0x35323733  // .asciz 4, '3725290298461914'
	WORD $0x32303932  // .asciz 4, '2902984619140625'
	WORD $0x36343839  // .asciz 4, '984619140625\x00\x00\x00\x00'
	WORD $0x34313931  // .asciz 4, '19140625\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x35323630  // .asciz 4, '0625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x00000009  // .long 9
	WORD $0x32363831  // .asciz 4, '1862645149230957'
	WORD $0x31353436  // .asciz 4, '6451492309570312'
	WORD $0x33323934  // .asciz 4, '4923095703125\x00\x00\x00'
	WORD $0x37353930  // .asciz 4, '095703125\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x32313330  // .asciz 4, '03125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000035  // .asciz 4, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x0000000a  // .long 10
	WORD $0x33313339  // .asciz 4, '9313225746154785'
	WORD $0x37353232  // .asciz 4, '2257461547851562'
	WORD $0x35313634  // .asciz 4, '4615478515625\x00\x00\x00'
	WORD $0x35383734  // .asciz 4, '478515625\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x32363531  // .asciz 4, '15625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000035  // .asciz 4, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x0000000a  // .long 10
	WORD $0x36353634  // .asciz 4, '4656612873077392'
	WORD $0x38323136  // .asciz 4, '6128730773925781'
	WORD $0x37303337  // .asciz 4, '73077392578125\x00\x00'
	WORD $0x32393337  // .asciz 4, '7392578125\x00\x00\x00\x00\x00\x00'
	WORD $0x31383735  // .asciz 4, '578125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00003532  // .asciz 4, '25\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x0000000a  // .long 10
	WORD $0x38323332  // .asciz 4, '2328306436538696'
	WORD $0x34363033  // .asciz 4, '3064365386962890'
	WORD $0x33353633  // .asciz 4, '365386962890625\x00'
	WORD $0x36393638  // .asciz 4, '86962890625\x00\x00\x00\x00\x00'
	WORD $0x30393832  // .asciz 4, '2890625\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00353236  // .asciz 4, '625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x0000000a  // .long 10
	WORD $0x34363131  // .asciz 4, '1164153218269348'
	WORD $0x32333531  // .asciz 4, '1532182693481445'
	WORD $0x36323831  // .asciz 4, '1826934814453125'
	WORD $0x38343339  // .asciz 4, '934814453125\x00\x00\x00\x00'
	WORD $0x35343431  // .asciz 4, '14453125\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x35323133  // .asciz 4, '3125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x0000000b  // .long 11
	WORD $0x30323835  // .asciz 4, '5820766091346740'
	WORD $0x30363637  // .asciz 4, '7660913467407226'
	WORD $0x34333139  // .asciz 4, '9134674072265625'
	WORD $0x30343736  // .asciz 4, '674072265625\x00\x00\x00\x00'
	WORD $0x36323237  // .asciz 4, '72265625\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x35323635  // .asciz 4, '5625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x0000000b  // .long 11
	WORD $0x30313932  // .asciz 4, '2910383045673370'
	WORD $0x30333833  // .asciz 4, '3830456733703613'
	WORD $0x37363534  // .asciz 4, '4567337036132812'
	WORD $0x30373333  // .asciz 4, '3370361328125\x00\x00\x00'
	WORD $0x33313633  // .asciz 4, '361328125\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x32313832  // .asciz 4, '28125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000035  // .asciz 4, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x0000000b  // .long 11
	WORD $0x35353431  // .asciz 4, '1455191522836685'
	WORD $0x35313931  // .asciz 4, '1915228366851806'
	WORD $0x33383232  // .asciz 4, '2283668518066406'
	WORD $0x35383636  // .asciz 4, '66851806640625\x00\x00'
	WORD $0x36303831  // .asciz 4, '1806640625\x00\x00\x00\x00\x00\x00'
	WORD $0x36303436  // .asciz 4, '640625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00003532  // .asciz 4, '25\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x0000000c  // .long 12
	WORD $0x35373237  // .asciz 4, '7275957614183425'
	WORD $0x36373539  // .asciz 4, '9576141834259033'
	WORD $0x38313431  // .asciz 4, '1418342590332031'
	WORD $0x35323433  // .asciz 4, '34259033203125\x00\x00'
	WORD $0x33333039  // .asciz 4, '9033203125\x00\x00\x00\x00\x00\x00'
	WORD $0x31333032  // .asciz 4, '203125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00003532  // .asciz 4, '25\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x0000000c  // .long 12
	WORD $0x37333633  // .asciz 4, '3637978807091712'
	WORD $0x38383739  // .asciz 4, '9788070917129516'
	WORD $0x39303730  // .asciz 4, '0709171295166015'
	WORD $0x32313731  // .asciz 4, '171295166015625\x00'
	WORD $0x36313539  // .asciz 4, '95166015625\x00\x00\x00\x00\x00'
	WORD $0x35313036  // .asciz 4, '6015625\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00353236  // .asciz 4, '625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x0000000c  // .long 12
	WORD $0x38313831  // .asciz 4, '1818989403545856'
	WORD $0x34393839  // .asciz 4, '9894035458564758'
	WORD $0x34353330  // .asciz 4, '0354585647583007'
	WORD $0x36353835  // .asciz 4, '5856475830078125'
	WORD $0x38353734  // .asciz 4, '475830078125\x00\x00\x00\x00'
	WORD $0x37303033  // .asciz 4, '30078125\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x35323138  // .asciz 4, '8125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x0000000d  // .long 13
	WORD $0x34393039  // .asciz 4, '9094947017729282'
	WORD $0x30373439  // .asciz 4, '9470177292823791'
	WORD $0x32373731  // .asciz 4, '1772928237915039'
	WORD $0x32383239  // .asciz 4, '9282379150390625'
	WORD $0x31393733  // .asciz 4, '379150390625\x00\x00\x00\x00'
	WORD $0x39333035  // .asciz 4, '50390625\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x35323630  // .asciz 4, '0625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x0000000d  // .long 13
	WORD $0x37343534  // .asciz 4, '4547473508864641'
	WORD $0x35333734  // .asciz 4, '4735088646411895'
	WORD $0x36383830  // .asciz 4, '0886464118957519'
	WORD $0x31343634  // .asciz 4, '4641189575195312'
	WORD $0x35393831  // .asciz 4, '1895751953125\x00\x00\x00'
	WORD $0x39313537  // .asciz 4, '751953125\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x32313335  // .asciz 4, '53125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000035  // .asciz 4, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x0000000d  // .long 13
	WORD $0x33373232  // .asciz 4, '2273736754432320'
	WORD $0x37363337  // .asciz 4, '7367544323205947'
	WORD $0x33343435  // .asciz 4, '5443232059478759'
	WORD $0x30323332  // .asciz 4, '2320594787597656'
	WORD $0x37343935  // .asciz 4, '59478759765625\x00\x00'
	WORD $0x39353738  // .asciz 4, '8759765625\x00\x00\x00\x00\x00\x00'
	WORD $0x36353637  // .asciz 4, '765625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00003532  // .asciz 4, '25\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x0000000d  // .long 13
	WORD $0x36333131  // .asciz 4, '1136868377216160'
	WORD $0x33383638  // .asciz 4, '8683772161602973'
	WORD $0x31323737  // .asciz 4, '7721616029739379'
	WORD $0x30363136  // .asciz 4, '6160297393798828'
	WORD $0x33373932  // .asciz 4, '297393798828125\x00'
	WORD $0x39373339  // .asciz 4, '93798828125\x00\x00\x00\x00\x00'
	WORD $0x38323838  // .asciz 4, '8828125\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00353231  // .asciz 4, '125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x0000000e  // .long 14
	WORD $0x34383635  // .asciz 4, '5684341886080801'
	WORD $0x38313433  // .asciz 4, '3418860808014869'
	WORD $0x38303638  // .asciz 4, '8608080148696899'
	WORD $0x31303830  // .asciz 4, '0801486968994140'
	WORD $0x39363834  // .asciz 4, '486968994140625\x00'
	WORD $0x39393836  // .asciz 4, '68994140625\x00\x00\x00\x00\x00'
	WORD $0x30343134  // .asciz 4, '4140625\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00353236  // .asciz 4, '625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x0000000e  // .long 14
	WORD $0x32343832  // .asciz 4, '2842170943040400'
	WORD $0x39303731  // .asciz 4, '1709430404007434'
	WORD $0x34303334  // .asciz 4, '4304040074348449'
	WORD $0x30303430  // .asciz 4, '0400743484497070'
	WORD $0x34333437  // .asciz 4, '7434844970703125'
	WORD $0x39343438  // .asciz 4, '844970703125\x00\x00\x00\x00'
	WORD $0x30373037  // .asciz 4, '70703125\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x35323133  // .asciz 4, '3125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x0000000e  // .long 14
	WORD $0x31323431  // .asciz 4, '1421085471520200'
	WORD $0x34353830  // .asciz 4, '0854715202003717'
	WORD $0x32353137  // .asciz 4, '7152020037174224'
	WORD $0x30303230  // .asciz 4, '0200371742248535'
	WORD $0x37313733  // .asciz 4, '3717422485351562'
	WORD $0x34323234  // .asciz 4, '4224853515625\x00\x00\x00'
	WORD $0x35333538  // .asciz 4, '853515625\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x32363531  // .asciz 4, '15625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000035  // .asciz 4, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x0000000f  // .long 15
	WORD $0x35303137  // .asciz 4, '7105427357601001'
	WORD $0x33373234  // .asciz 4, '4273576010018587'
	WORD $0x30363735  // .asciz 4, '5760100185871124'
	WORD $0x31303031  // .asciz 4, '1001858711242675'
	WORD $0x37383538  // .asciz 4, '8587112426757812'
	WORD $0x34323131  // .asciz 4, '1124267578125\x00\x00\x00'
	WORD $0x35373632  // .asciz 4, '267578125\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x32313837  // .asciz 4, '78125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000035  // .asciz 4, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x0000000f  // .long 15
	WORD $0x32353533  // .asciz 4, '3552713678800500'
	WORD $0x36333137  // .asciz 4, '7136788005009293'
	WORD $0x30383837  // .asciz 4, '7880050092935562'
	WORD $0x30303530  // .asciz 4, '0500929355621337'
	WORD $0x33393239  // .asciz 4, '9293556213378906'
	WORD $0x32363535  // .asciz 4, '55621337890625\x00\x00'
	WORD $0x37333331  // .asciz 4, '1337890625\x00\x00\x00\x00\x00\x00'
	WORD $0x36303938  // .asciz 4, '890625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00003532  // .asciz 4, '25\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x0000000f  // .long 15
	WORD $0x36373731  // .asciz 4, '1776356839400250'
	WORD $0x38363533  // .asciz 4, '3568394002504646'
	WORD $0x30343933  // .asciz 4, '3940025046467781'
	WORD $0x30353230  // .asciz 4, '0250464677810668'
	WORD $0x36343634  // .asciz 4, '4646778106689453'
	WORD $0x31383737  // .asciz 4, '778106689453125\x00'
	WORD $0x38363630  // .asciz 4, '06689453125\x00\x00\x00\x00\x00'
	WORD $0x33353439  // .asciz 4, '9453125\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00353231  // .asciz 4, '125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x00000010  // .long 16
	WORD $0x31383838  // .asciz 4, '8881784197001252'
	WORD $0x31343837  // .asciz 4, '7841970012523233'
	WORD $0x30303739  // .asciz 4, '9700125232338905'
	WORD $0x32353231  // .asciz 4, '1252323389053344'
	WORD $0x33333233  // .asciz 4, '3233890533447265'
	WORD $0x35303938  // .asciz 4, '890533447265625\x00'
	WORD $0x34343333  // .asciz 4, '33447265625\x00\x00\x00\x00\x00'
	WORD $0x35363237  // .asciz 4, '7265625\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00353236  // .asciz 4, '625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x00000010  // .long 16
	WORD $0x30343434  // .asciz 4, '4440892098500626'
	WORD $0x30323938  // .asciz 4, '8920985006261616'
	WORD $0x30353839  // .asciz 4, '9850062616169452'
	WORD $0x36323630  // .asciz 4, '0626161694526672'
	WORD $0x36313631  // .asciz 4, '1616945266723632'
	WORD $0x32353439  // .asciz 4, '9452667236328125'
	WORD $0x32373636  // .asciz 4, '667236328125\x00\x00\x00\x00'
	WORD $0x32333633  // .asciz 4, '36328125\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x35323138  // .asciz 4, '8125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x00000010  // .long 16
	WORD $0x30323232  // .asciz 4, '2220446049250313'
	WORD $0x30363434  // .asciz 4, '4460492503130808'
	WORD $0x35323934  // .asciz 4, '4925031308084726'
	WORD $0x33313330  // .asciz 4, '0313080847263336'
	WORD $0x38303830  // .asciz 4, '0808472633361816'
	WORD $0x36323734  // .asciz 4, '4726333618164062'
	WORD $0x36333333  // .asciz 4, '3336181640625\x00\x00\x00'
	WORD $0x36313831  // .asciz 4, '181640625\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x32363034  // .asciz 4, '40625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000035  // .asciz 4, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x00000010  // .long 16
	WORD $0x30313131  // .asciz 4, '1110223024625156'
	WORD $0x30333232  // .asciz 4, '2230246251565404'
	WORD $0x32363432  // .asciz 4, '2462515654042363'
	WORD $0x36353135  // .asciz 4, '5156540423631668'
	WORD $0x34303435  // .asciz 4, '5404236316680908'
	WORD $0x33363332  // .asciz 4, '2363166809082031'
	WORD $0x38363631  // .asciz 4, '16680908203125\x00\x00'
	WORD $0x38303930  // .asciz 4, '0908203125\x00\x00\x00\x00\x00\x00'
	WORD $0x31333032  // .asciz 4, '203125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00003532  // .asciz 4, '25\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x00000011  // .long 17
	WORD $0x31353535  // .asciz 4, '5551115123125782'
	WORD $0x31353131  // .asciz 4, '1151231257827021'
	WORD $0x32313332  // .asciz 4, '2312578270211815'
	WORD $0x32383735  // .asciz 4, '5782702118158340'
	WORD $0x31323037  // .asciz 4, '7021181583404541'
	WORD $0x35313831  // .asciz 4, '1815834045410156'
	WORD $0x30343338  // .asciz 4, '83404541015625\x00\x00'
	WORD $0x31343534  // .asciz 4, '4541015625\x00\x00\x00\x00\x00\x00'
	WORD $0x36353130  // .asciz 4, '015625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00003532  // .asciz 4, '25\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x00000011  // .long 17
	WORD $0x35373732  // .asciz 4, '2775557561562891'
	WORD $0x35373535  // .asciz 4, '5575615628913510'
	WORD $0x36353136  // .asciz 4, '6156289135105907'
	WORD $0x31393832  // .asciz 4, '2891351059079170'
	WORD $0x30313533  // .asciz 4, '3510590791702270'
	WORD $0x37303935  // .asciz 4, '5907917022705078'
	WORD $0x30373139  // .asciz 4, '917022705078125\x00'
	WORD $0x30373232  // .asciz 4, '22705078125\x00\x00\x00\x00\x00'
	WORD $0x38373035  // .asciz 4, '5078125\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00353231  // .asciz 4, '125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x00000011  // .long 17
	WORD $0x37383331  // .asciz 4, '1387778780781445'
	WORD $0x37383737  // .asciz 4, '7787807814456755'
	WORD $0x38373038  // .asciz 4, '8078144567552953'
	WORD $0x35343431  // .asciz 4, '1445675529539585'
	WORD $0x35353736  // .asciz 4, '6755295395851135'
	WORD $0x33353932  // .asciz 4, '2953958511352539'
	WORD $0x35383539  // .asciz 4, '9585113525390625'
	WORD $0x35333131  // .asciz 4, '113525390625\x00\x00\x00\x00'
	WORD $0x39333532  // .asciz 4, '25390625\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x35323630  // .asciz 4, '0625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x00000012  // .long 18
	WORD $0x38333936  // .asciz 4, '6938893903907228'
	WORD $0x39333938  // .asciz 4, '8939039072283776'
	WORD $0x30393330  // .asciz 4, '0390722837764769'
	WORD $0x38323237  // .asciz 4, '7228377647697925'
	WORD $0x36373733  // .asciz 4, '3776476979255676'
	WORD $0x39363734  // .asciz 4, '4769792556762695'
	WORD $0x35323937  // .asciz 4, '7925567626953125'
	WORD $0x36373635  // .asciz 4, '567626953125\x00\x00\x00\x00'
	WORD $0x35393632  // .asciz 4, '26953125\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x35323133  // .asciz 4, '3125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x00000012  // .long 18
	WORD $0x39363433  // .asciz 4, '3469446951953614'
	WORD $0x39363434  // .asciz 4, '4469519536141888'
	WORD $0x35393135  // .asciz 4, '5195361418882384'
	WORD $0x34313633  // .asciz 4, '3614188823848962'
	WORD $0x38383831  // .asciz 4, '1888238489627838'
	WORD $0x34383332  // .asciz 4, '2384896278381347'
	WORD $0x32363938  // .asciz 4, '8962783813476562'
	WORD $0x38333837  // .asciz 4, '7838134765625\x00\x00\x00'
	WORD $0x37343331  // .asciz 4, '134765625\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x32363536  // .asciz 4, '65625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000035  // .asciz 4, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x00000012  // .long 18
	WORD $0x34333731  // .asciz 4, '1734723475976807'
	WORD $0x34333237  // .asciz 4, '7234759768070944'
	WORD $0x37393537  // .asciz 4, '7597680709441192'
	WORD $0x37303836  // .asciz 4, '6807094411924481'
	WORD $0x34343930  // .asciz 4, '0944119244813919'
	WORD $0x32393131  // .asciz 4, '1192448139190673'
	WORD $0x31383434  // .asciz 4, '4481391906738281'
	WORD $0x39313933  // .asciz 4, '39190673828125\x00\x00'
	WORD $0x33373630  // .asciz 4, '0673828125\x00\x00\x00\x00\x00\x00'
	WORD $0x31383238  // .asciz 4, '828125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00003532  // .asciz 4, '25\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	WORD $0x00000013  // .long 19
	WORD $0x33373638  // .asciz 4, '8673617379884035'
	WORD $0x33373136  // .asciz 4, '6173798840354720'
	WORD $0x38383937  // .asciz 4, '7988403547205962'
	WORD $0x35333034  // .asciz 4, '4035472059622406'
	WORD $0x30323734  // .asciz 4, '4720596224069595'
	WORD $0x32363935  // .asciz 4, '5962240695953369'
	WORD $0x36303432  // .asciz 4, '2406959533691406'
	WORD $0x35393539  // .asciz 4, '95953369140625\x00\x00'
	WORD $0x39363333  // .asciz 4, '3369140625\x00\x00\x00\x00\x00\x00'
	WORD $0x36303431  // .asciz 4, '140625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00003532  // .asciz 4, '25\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .asciz 4, '\x00\x00\x00\x00'

TEXT ·__value(SB), NOSPLIT, $0-48
	NO_LOCAL_POINTERS

_entry:
	MOVD 16(g), R16
	SUB $176, RSP, R17
	CMP  R16, R17
	BLS  _stack_grow

_value:
	MOVD s+0(FP), R0
	MOVD n+8(FP), R1
	MOVD p+16(FP), R2
	MOVD v+24(FP), R3
	MOVD flags+32(FP), R4
	MOVD ·_subr__value(SB), R11
	WORD $0x1000005e // adr x30, .+8
	JMP (R11)
	MOVD R0, ret+40(FP)
	RET

_stack_grow:
	MOVD R30, R3
	CALL runtime·morestack_noctxt<>(SB)
	JMP  _entry
