// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package avx2

var _text_skip_object = []byte{
	// .p2align 4, 0x00
	// LCPI0_0
	0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // .quad 1
	0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000008 .quad 6
	//0x00000010 LCPI0_11
	0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, //0x00000010 QUAD $0x2c2c2c2c2c2c2c2c; QUAD $0x2c2c2c2c2c2c2c2c  // .space 16, ',,,,,,,,,,,,,,,,'
	//0x00000020 LCPI0_12
	0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, //0x00000020 QUAD $0xdfdfdfdfdfdfdfdf; QUAD $0xdfdfdfdfdfdfdfdf  // .space 16, '\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf'
	//0x00000030 LCPI0_13
	0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, //0x00000030 QUAD $0x5d5d5d5d5d5d5d5d; QUAD $0x5d5d5d5d5d5d5d5d  // .space 16, ']]]]]]]]]]]]]]]]'
	//0x00000040 LCPI0_20
	0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, //0x00000040 QUAD $0x2f2f2f2f2f2f2f2f; QUAD $0x2f2f2f2f2f2f2f2f  // .space 16, '////////////////'
	//0x00000050 LCPI0_21
	0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, //0x00000050 QUAD $0x3a3a3a3a3a3a3a3a; QUAD $0x3a3a3a3a3a3a3a3a  // .space 16, '::::::::::::::::'
	//0x00000060 LCPI0_22
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x00000060 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	//0x00000070 LCPI0_23
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x00000070 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	//0x00000080 LCPI0_24
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x00000080 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	//0x00000090 LCPI0_25
	0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, //0x00000090 QUAD $0x4545454545454545; QUAD $0x4545454545454545  // .space 16, 'EEEEEEEEEEEEEEEE'
	//0x000000a0 LCPI0_26
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x000000a0 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .space 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000000b0 .p2align 5, 0x00
	//0x000000c0 LCPI0_1
	0x20, //0x000000c0 .byte 32
	0x00, //0x000000c1 .byte 0
	0x00, //0x000000c2 .byte 0
	0x00, //0x000000c3 .byte 0
	0x00, //0x000000c4 .byte 0
	0x00, //0x000000c5 .byte 0
	0x00, //0x000000c6 .byte 0
	0x00, //0x000000c7 .byte 0
	0x00, //0x000000c8 .byte 0
	0x09, //0x000000c9 .byte 9
	0x0a, //0x000000ca .byte 10
	0x00, //0x000000cb .byte 0
	0x00, //0x000000cc .byte 0
	0x0d, //0x000000cd .byte 13
	0x00, //0x000000ce .byte 0
	0x00, //0x000000cf .byte 0
	0x20, //0x000000d0 .byte 32
	0x00, //0x000000d1 .byte 0
	0x00, //0x000000d2 .byte 0
	0x00, //0x000000d3 .byte 0
	0x00, //0x000000d4 .byte 0
	0x00, //0x000000d5 .byte 0
	0x00, //0x000000d6 .byte 0
	0x00, //0x000000d7 .byte 0
	0x00, //0x000000d8 .byte 0
	0x09, //0x000000d9 .byte 9
	0x0a, //0x000000da .byte 10
	0x00, //0x000000db .byte 0
	0x00, //0x000000dc .byte 0
	0x0d, //0x000000dd .byte 13
	0x00, //0x000000de .byte 0
	0x00, //0x000000df .byte 0
	//0x000000e0 LCPI0_2
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, //0x000000e0 QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, //0x000000f0 QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	//0x00000100 LCPI0_3
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x00000100 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x00000110 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	//0x00000120 LCPI0_4
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, //0x00000120 QUAD $0x2020202020202020; QUAD $0x2020202020202020  // .space 16, '                '
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, //0x00000130 QUAD $0x2020202020202020; QUAD $0x2020202020202020  // .space 16, '                '
	//0x00000140 LCPI0_5
	0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, //0x00000140 QUAD $0x7b7b7b7b7b7b7b7b; QUAD $0x7b7b7b7b7b7b7b7b  // .space 16, '{{{{{{{{{{{{{{{{'
	0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, //0x00000150 QUAD $0x7b7b7b7b7b7b7b7b; QUAD $0x7b7b7b7b7b7b7b7b  // .space 16, '{{{{{{{{{{{{{{{{'
	//0x00000160 LCPI0_6
	0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, //0x00000160 QUAD $0x7d7d7d7d7d7d7d7d; QUAD $0x7d7d7d7d7d7d7d7d  // .space 16, '}}}}}}}}}}}}}}}}'
	0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, //0x00000170 QUAD $0x7d7d7d7d7d7d7d7d; QUAD $0x7d7d7d7d7d7d7d7d  // .space 16, '}}}}}}}}}}}}}}}}'
	//0x00000180 LCPI0_7
	0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, //0x00000180 QUAD $0x5b5b5b5b5b5b5b5b; QUAD $0x5b5b5b5b5b5b5b5b  // .space 16, '[[[[[[[[[[[[[[[['
	0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, //0x00000190 QUAD $0x5b5b5b5b5b5b5b5b; QUAD $0x5b5b5b5b5b5b5b5b  // .space 16, '[[[[[[[[[[[[[[[['
	//0x000001a0 LCPI0_8
	0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, //0x000001a0 QUAD $0x5d5d5d5d5d5d5d5d; QUAD $0x5d5d5d5d5d5d5d5d  // .space 16, ']]]]]]]]]]]]]]]]'
	0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, //0x000001b0 QUAD $0x5d5d5d5d5d5d5d5d; QUAD $0x5d5d5d5d5d5d5d5d  // .space 16, ']]]]]]]]]]]]]]]]'
	//0x000001c0 LCPI0_9
	0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, //0x000001c0 QUAD $0x2c2c2c2c2c2c2c2c; QUAD $0x2c2c2c2c2c2c2c2c  // .space 16, ',,,,,,,,,,,,,,,,'
	0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, //0x000001d0 QUAD $0x2c2c2c2c2c2c2c2c; QUAD $0x2c2c2c2c2c2c2c2c  // .space 16, ',,,,,,,,,,,,,,,,'
	//0x000001e0 LCPI0_10
	0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, //0x000001e0 QUAD $0xdfdfdfdfdfdfdfdf; QUAD $0xdfdfdfdfdfdfdfdf  // .space 16, '\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf'
	0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, //0x000001f0 QUAD $0xdfdfdfdfdfdfdfdf; QUAD $0xdfdfdfdfdfdfdfdf  // .space 16, '\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf'
	//0x00000200 LCPI0_14
	0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, //0x00000200 QUAD $0x2f2f2f2f2f2f2f2f; QUAD $0x2f2f2f2f2f2f2f2f  // .space 16, '////////////////'
	0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, //0x00000210 QUAD $0x2f2f2f2f2f2f2f2f; QUAD $0x2f2f2f2f2f2f2f2f  // .space 16, '////////////////'
	//0x00000220 LCPI0_15
	0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, //0x00000220 QUAD $0x3a3a3a3a3a3a3a3a; QUAD $0x3a3a3a3a3a3a3a3a  // .space 16, '::::::::::::::::'
	0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, //0x00000230 QUAD $0x3a3a3a3a3a3a3a3a; QUAD $0x3a3a3a3a3a3a3a3a  // .space 16, '::::::::::::::::'
	//0x00000240 LCPI0_16
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x00000240 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x00000250 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	//0x00000260 LCPI0_17
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x00000260 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x00000270 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	//0x00000280 LCPI0_18
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x00000280 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x00000290 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	//0x000002a0 LCPI0_19
	0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, //0x000002a0 QUAD $0x4545454545454545; QUAD $0x4545454545454545  // .space 16, 'EEEEEEEEEEEEEEEE'
	0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, //0x000002b0 QUAD $0x4545454545454545; QUAD $0x4545454545454545  // .space 16, 'EEEEEEEEEEEEEEEE'
	//0x000002c0 .p2align 4, 0x90
	//0x000002c0 _skip_object
	0x55, //0x000002c0 pushq        %rbp
	0x48, 0x89, 0xe5, //0x000002c1 movq         %rsp, %rbp
	0x41, 0x57, //0x000002c4 pushq        %r15
	0x41, 0x56, //0x000002c6 pushq        %r14
	0x41, 0x55, //0x000002c8 pushq        %r13
	0x41, 0x54, //0x000002ca pushq        %r12
	0x53, //0x000002cc pushq        %rbx
	0x48, 0x81, 0xec, 0xa0, 0x00, 0x00, 0x00, //0x000002cd subq         $160, %rsp
	0x49, 0x89, 0xcb, //0x000002d4 movq         %rcx, %r11
	0x49, 0x89, 0xd7, //0x000002d7 movq         %rdx, %r15
	0x49, 0x89, 0xf5, //0x000002da movq         %rsi, %r13
	0xc5, 0xfa, 0x6f, 0x05, 0x1b, 0xfd, 0xff, 0xff, //0x000002dd vmovdqu      $-741(%rip), %xmm0  /* LCPI0_0+0(%rip) */
	0xc5, 0xfa, 0x7f, 0x02, //0x000002e5 vmovdqu      %xmm0, (%rdx)
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x000002e9 movq         $-1, %r12
	0xc5, 0xfe, 0x6f, 0x2d, 0xc8, 0xfd, 0xff, 0xff, //0x000002f0 vmovdqu      $-568(%rip), %ymm5  /* LCPI0_1+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0xe0, 0xfd, 0xff, 0xff, //0x000002f8 vmovdqu      $-544(%rip), %ymm6  /* LCPI0_2+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0xf8, 0xfd, 0xff, 0xff, //0x00000300 vmovdqu      $-520(%rip), %ymm7  /* LCPI0_3+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0x10, 0xfe, 0xff, 0xff, //0x00000308 vmovdqu      $-496(%rip), %ymm8  /* LCPI0_4+0(%rip) */
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00000310 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0x7e, 0x6f, 0x15, 0xe3, 0xfe, 0xff, 0xff, //0x00000315 vmovdqu      $-285(%rip), %ymm10  /* LCPI0_14+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0xfb, 0xfe, 0xff, 0xff, //0x0000031d vmovdqu      $-261(%rip), %ymm13  /* LCPI0_15+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x3d, 0x13, 0xff, 0xff, 0xff, //0x00000325 vmovdqu      $-237(%rip), %ymm15  /* LCPI0_16+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0xab, 0xfe, 0xff, 0xff, //0x0000032d vmovdqu      $-341(%rip), %ymm14  /* LCPI0_10+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0x63, 0xff, 0xff, 0xff, //0x00000335 vmovdqu      $-157(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x1d, 0x7b, 0xfe, 0xff, 0xff, //0x0000033d vmovdqu      $-389(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0xf3, 0xfd, 0xff, 0xff, //0x00000345 vmovdqu      $-525(%rip), %ymm11  /* LCPI0_5+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0x0b, 0xfe, 0xff, 0xff, //0x0000034d vmovdqu      $-501(%rip), %ymm12  /* LCPI0_6+0(%rip) */
	0x48, 0x89, 0x4c, 0x24, 0x18, //0x00000355 movq         %rcx, $24(%rsp)
	0x48, 0x89, 0x7c, 0x24, 0x10, //0x0000035a movq         %rdi, $16(%rsp)
	0x48, 0x89, 0x54, 0x24, 0x08, //0x0000035f movq         %rdx, $8(%rsp)
	0xe9, 0x77, 0x00, 0x00, 0x00, //0x00000364 jmp          LBB0_5
	//0x00000369 LBB0_153
	0x41, 0x0f, 0xbc, 0xc3, //0x00000369 bsfl         %r11d, %eax
	0x4c, 0x01, 0xc0, //0x0000036d addq         %r8, %rax
	0x4c, 0x8d, 0x0c, 0x06, //0x00000370 leaq         (%rsi,%rax), %r9
	0x49, 0x83, 0xc1, 0x01, //0x00000374 addq         $1, %r9
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x00000378 movq         $24(%rsp), %r11
	0x48, 0x8b, 0x7c, 0x24, 0x10, //0x0000037d movq         $16(%rsp), %rdi
	0x4c, 0x8b, 0x7c, 0x24, 0x08, //0x00000382 movq         $8(%rsp), %r15
	0x4d, 0x89, 0x4d, 0x00, //0x00000387 movq         %r9, (%r13)
	0x4d, 0x85, 0xc0, //0x0000038b testq        %r8, %r8
	0x0f, 0x8e, 0x98, 0x37, 0x00, 0x00, //0x0000038e jle          LBB0_679
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000394 .p2align 4, 0x90
	//0x000003a0 LBB0_3
	0x4d, 0x8b, 0x07, //0x000003a0 movq         (%r15), %r8
	0x4c, 0x89, 0x24, 0x24, //0x000003a3 movq         %r12, (%rsp)
	0x4d, 0x85, 0xc0, //0x000003a7 testq        %r8, %r8
	0x0f, 0x85, 0x30, 0x00, 0x00, 0x00, //0x000003aa jne          LBB0_5
	0xe9, 0xe0, 0x37, 0x00, 0x00, //0x000003b0 jmp          LBB0_711
	//0x000003b5 LBB0_1
	0x48, 0x89, 0xd1, //0x000003b5 movq         %rdx, %rcx
	//0x000003b8 LBB0_2
	0x4c, 0x01, 0xe9, //0x000003b8 addq         %r13, %rcx
	0x4c, 0x8b, 0x6c, 0x24, 0x30, //0x000003bb movq         $48(%rsp), %r13
	0x49, 0x89, 0x4d, 0x00, //0x000003c0 movq         %rcx, (%r13)
	0x48, 0x85, 0xdb, //0x000003c4 testq        %rbx, %rbx
	0x0f, 0x8f, 0xd3, 0xff, 0xff, 0xff, //0x000003c7 jg           LBB0_3
	0xe9, 0xc3, 0x37, 0x00, 0x00, //0x000003cd jmp          LBB0_711
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000003d2 .p2align 4, 0x90
	//0x000003e0 LBB0_5
	0x4d, 0x89, 0xe1, //0x000003e0 movq         %r12, %r9
	0x4c, 0x8b, 0x37, //0x000003e3 movq         (%rdi), %r14
	0x48, 0x8b, 0x5f, 0x08, //0x000003e6 movq         $8(%rdi), %rbx
	0x49, 0x8b, 0x75, 0x00, //0x000003ea movq         (%r13), %rsi
	0x48, 0x39, 0xde, //0x000003ee cmpq         %rbx, %rsi
	0x0f, 0x83, 0x29, 0x00, 0x00, 0x00, //0x000003f1 jae          LBB0_10
	0x41, 0x8a, 0x04, 0x36, //0x000003f7 movb         (%r14,%rsi), %al
	0x3c, 0x0d, //0x000003fb cmpb         $13, %al
	0x0f, 0x84, 0x1d, 0x00, 0x00, 0x00, //0x000003fd je           LBB0_10
	0x3c, 0x20, //0x00000403 cmpb         $32, %al
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x00000405 je           LBB0_10
	0x04, 0xf7, //0x0000040b addb         $-9, %al
	0x3c, 0x01, //0x0000040d cmpb         $1, %al
	0x0f, 0x86, 0x0b, 0x00, 0x00, 0x00, //0x0000040f jbe          LBB0_10
	0x49, 0x89, 0xf4, //0x00000415 movq         %rsi, %r12
	0xe9, 0x75, 0x01, 0x00, 0x00, //0x00000418 jmp          LBB0_35
	0x90, 0x90, 0x90, //0x0000041d .p2align 4, 0x90
	//0x00000420 LBB0_10
	0x4c, 0x8d, 0x66, 0x01, //0x00000420 leaq         $1(%rsi), %r12
	0x49, 0x39, 0xdc, //0x00000424 cmpq         %rbx, %r12
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000427 jae          LBB0_14
	0x43, 0x8a, 0x14, 0x26, //0x0000042d movb         (%r14,%r12), %dl
	0x80, 0xfa, 0x0d, //0x00000431 cmpb         $13, %dl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00000434 je           LBB0_14
	0x80, 0xfa, 0x20, //0x0000043a cmpb         $32, %dl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000043d je           LBB0_14
	0x80, 0xc2, 0xf7, //0x00000443 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x00000446 cmpb         $1, %dl
	0x0f, 0x87, 0x43, 0x01, 0x00, 0x00, //0x00000449 ja           LBB0_35
	0x90, //0x0000044f .p2align 4, 0x90
	//0x00000450 LBB0_14
	0x4c, 0x8d, 0x66, 0x02, //0x00000450 leaq         $2(%rsi), %r12
	0x49, 0x39, 0xdc, //0x00000454 cmpq         %rbx, %r12
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000457 jae          LBB0_18
	0x43, 0x8a, 0x14, 0x26, //0x0000045d movb         (%r14,%r12), %dl
	0x80, 0xfa, 0x0d, //0x00000461 cmpb         $13, %dl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00000464 je           LBB0_18
	0x80, 0xfa, 0x20, //0x0000046a cmpb         $32, %dl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000046d je           LBB0_18
	0x80, 0xc2, 0xf7, //0x00000473 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x00000476 cmpb         $1, %dl
	0x0f, 0x87, 0x13, 0x01, 0x00, 0x00, //0x00000479 ja           LBB0_35
	0x90, //0x0000047f .p2align 4, 0x90
	//0x00000480 LBB0_18
	0x4c, 0x8d, 0x66, 0x03, //0x00000480 leaq         $3(%rsi), %r12
	0x49, 0x39, 0xdc, //0x00000484 cmpq         %rbx, %r12
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000487 jae          LBB0_22
	0x43, 0x8a, 0x14, 0x26, //0x0000048d movb         (%r14,%r12), %dl
	0x80, 0xfa, 0x0d, //0x00000491 cmpb         $13, %dl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00000494 je           LBB0_22
	0x80, 0xfa, 0x20, //0x0000049a cmpb         $32, %dl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000049d je           LBB0_22
	0x80, 0xc2, 0xf7, //0x000004a3 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x000004a6 cmpb         $1, %dl
	0x0f, 0x87, 0xe3, 0x00, 0x00, 0x00, //0x000004a9 ja           LBB0_35
	0x90, //0x000004af .p2align 4, 0x90
	//0x000004b0 LBB0_22
	0x4c, 0x8d, 0x66, 0x04, //0x000004b0 leaq         $4(%rsi), %r12
	0x48, 0x89, 0xd8, //0x000004b4 movq         %rbx, %rax
	0x4c, 0x29, 0xe0, //0x000004b7 subq         %r12, %rax
	0x0f, 0x86, 0x4e, 0x36, 0x00, 0x00, //0x000004ba jbe          LBB0_675
	0x48, 0x83, 0xf8, 0x20, //0x000004c0 cmpq         $32, %rax
	0x0f, 0x82, 0x69, 0x23, 0x00, 0x00, //0x000004c4 jb           LBB0_488
	0x48, 0xc7, 0xc0, 0xfc, 0xff, 0xff, 0xff, //0x000004ca movq         $-4, %rax
	0x48, 0x29, 0xf0, //0x000004d1 subq         %rsi, %rax
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000004d4 .p2align 4, 0x90
	//0x000004e0 LBB0_25
	0xc4, 0x81, 0x7e, 0x6f, 0x04, 0x26, //0x000004e0 vmovdqu      (%r14,%r12), %ymm0
	0xc4, 0xe2, 0x55, 0x00, 0xc8, //0x000004e6 vpshufb      %ymm0, %ymm5, %ymm1
	0xc5, 0xfd, 0x74, 0xc1, //0x000004eb vpcmpeqb     %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xd0, //0x000004ef vpmovmskb    %ymm0, %edx
	0x83, 0xfa, 0xff, //0x000004f3 cmpl         $-1, %edx
	0x0f, 0x85, 0x84, 0x00, 0x00, 0x00, //0x000004f6 jne          LBB0_34
	0x49, 0x83, 0xc4, 0x20, //0x000004fc addq         $32, %r12
	0x48, 0x8d, 0x14, 0x03, //0x00000500 leaq         (%rbx,%rax), %rdx
	0x48, 0x83, 0xc2, 0xe0, //0x00000504 addq         $-32, %rdx
	0x48, 0x83, 0xc0, 0xe0, //0x00000508 addq         $-32, %rax
	0x48, 0x83, 0xfa, 0x1f, //0x0000050c cmpq         $31, %rdx
	0x0f, 0x87, 0xca, 0xff, 0xff, 0xff, //0x00000510 ja           LBB0_25
	0x4d, 0x89, 0xf4, //0x00000516 movq         %r14, %r12
	0x49, 0x29, 0xc4, //0x00000519 subq         %rax, %r12
	0x48, 0x01, 0xd8, //0x0000051c addq         %rbx, %rax
	0x48, 0x85, 0xc0, //0x0000051f testq        %rax, %rax
	0x0f, 0x84, 0x3a, 0x00, 0x00, 0x00, //0x00000522 je           LBB0_33
	//0x00000528 LBB0_28
	0x4d, 0x8d, 0x04, 0x04, //0x00000528 leaq         (%r12,%rax), %r8
	0x31, 0xd2, //0x0000052c xorl         %edx, %edx
	0x90, 0x90, //0x0000052e .p2align 4, 0x90
	//0x00000530 LBB0_29
	0x41, 0x0f, 0xbe, 0x34, 0x14, //0x00000530 movsbl       (%r12,%rdx), %esi
	0x83, 0xfe, 0x20, //0x00000535 cmpl         $32, %esi
	0x0f, 0x87, 0x78, 0x1f, 0x00, 0x00, //0x00000538 ja           LBB0_676
	0x48, 0xb9, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x0000053e movabsq      $4294977024, %rcx
	0x48, 0x0f, 0xa3, 0xf1, //0x00000548 btq          %rsi, %rcx
	0x0f, 0x83, 0x64, 0x1f, 0x00, 0x00, //0x0000054c jae          LBB0_676
	0x48, 0x83, 0xc2, 0x01, //0x00000552 addq         $1, %rdx
	0x48, 0x39, 0xd0, //0x00000556 cmpq         %rdx, %rax
	0x0f, 0x85, 0xd1, 0xff, 0xff, 0xff, //0x00000559 jne          LBB0_29
	0x4d, 0x89, 0xc4, //0x0000055f movq         %r8, %r12
	//0x00000562 LBB0_33
	0x4d, 0x29, 0xf4, //0x00000562 subq         %r14, %r12
	0x49, 0x39, 0xdc, //0x00000565 cmpq         %rbx, %r12
	0x0f, 0x82, 0x24, 0x00, 0x00, 0x00, //0x00000568 jb           LBB0_35
	0xe9, 0x9f, 0x35, 0x00, 0x00, //0x0000056e jmp          LBB0_677
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000573 .p2align 4, 0x90
	//0x00000580 LBB0_34
	0xf7, 0xd2, //0x00000580 notl         %edx
	0x44, 0x0f, 0xbc, 0xe2, //0x00000582 bsfl         %edx, %r12d
	0x49, 0x29, 0xc4, //0x00000586 subq         %rax, %r12
	0x49, 0x39, 0xdc, //0x00000589 cmpq         %rbx, %r12
	0x0f, 0x83, 0x80, 0x35, 0x00, 0x00, //0x0000058c jae          LBB0_677
	//0x00000592 LBB0_35
	0x49, 0x8d, 0x54, 0x24, 0x01, //0x00000592 leaq         $1(%r12), %rdx
	0x49, 0x89, 0x55, 0x00, //0x00000597 movq         %rdx, (%r13)
	0x43, 0x0f, 0xbe, 0x04, 0x26, //0x0000059b movsbl       (%r14,%r12), %eax
	0x85, 0xc0, //0x000005a0 testl        %eax, %eax
	0x0f, 0x84, 0x6a, 0x35, 0x00, 0x00, //0x000005a2 je           LBB0_677
	0x49, 0x8b, 0x37, //0x000005a8 movq         (%r15), %rsi
	0x4c, 0x8d, 0x46, 0xff, //0x000005ab leaq         $-1(%rsi), %r8
	0x41, 0x8b, 0x1c, 0xf7, //0x000005af movl         (%r15,%rsi,8), %ebx
	0x49, 0x83, 0xf9, 0xff, //0x000005b3 cmpq         $-1, %r9
	0x4d, 0x0f, 0x45, 0xe1, //0x000005b7 cmovneq      %r9, %r12
	0x83, 0xc3, 0xff, //0x000005bb addl         $-1, %ebx
	0x83, 0xfb, 0x05, //0x000005be cmpl         $5, %ebx
	0x0f, 0x87, 0x27, 0x00, 0x00, 0x00, //0x000005c1 ja           LBB0_41
	0x48, 0x8d, 0x15, 0x12, 0x38, 0x00, 0x00, //0x000005c7 leaq         $14354(%rip), %rdx  /* LJTI0_0+0(%rip) */
	0x48, 0x63, 0x0c, 0x9a, //0x000005ce movslq       (%rdx,%rbx,4), %rcx
	0x48, 0x01, 0xd1, //0x000005d2 addq         %rdx, %rcx
	0xff, 0xe1, //0x000005d5 jmpq         *%rcx
	//0x000005d7 LBB0_38
	0x83, 0xf8, 0x2c, //0x000005d7 cmpl         $44, %eax
	0x0f, 0x84, 0x41, 0x06, 0x00, 0x00, //0x000005da je           LBB0_126
	0x83, 0xf8, 0x5d, //0x000005e0 cmpl         $93, %eax
	0x0f, 0x84, 0x23, 0x06, 0x00, 0x00, //0x000005e3 je           LBB0_40
	0xe9, 0x9f, 0x35, 0x00, 0x00, //0x000005e9 jmp          LBB0_710
	//0x000005ee LBB0_41
	0x48, 0xc7, 0x04, 0x24, 0xff, 0xff, 0xff, 0xff, //0x000005ee movq         $-1, (%rsp)
	0x4d, 0x89, 0x07, //0x000005f6 movq         %r8, (%r15)
	0x83, 0xf8, 0x7b, //0x000005f9 cmpl         $123, %eax
	0x0f, 0x86, 0xaf, 0x01, 0x00, 0x00, //0x000005fc jbe          LBB0_67
	0xe9, 0x86, 0x35, 0x00, 0x00, //0x00000602 jmp          LBB0_710
	//0x00000607 LBB0_42
	0x83, 0xf8, 0x2c, //0x00000607 cmpl         $44, %eax
	0x0f, 0x85, 0xf3, 0x05, 0x00, 0x00, //0x0000060a jne          LBB0_43
	0x48, 0x81, 0xfe, 0xff, 0x0f, 0x00, 0x00, //0x00000610 cmpq         $4095, %rsi
	0x0f, 0x8f, 0x02, 0x35, 0x00, 0x00, //0x00000617 jg           LBB0_705
	0x48, 0x8d, 0x4e, 0x01, //0x0000061d leaq         $1(%rsi), %rcx
	0x49, 0x89, 0x0f, //0x00000621 movq         %rcx, (%r15)
	0x49, 0xc7, 0x44, 0xf7, 0x08, 0x03, 0x00, 0x00, 0x00, //0x00000624 movq         $3, $8(%r15,%rsi,8)
	0xe9, 0x6e, 0xfd, 0xff, 0xff, //0x0000062d jmp          LBB0_3
	//0x00000632 LBB0_44
	0x3c, 0x22, //0x00000632 cmpb         $34, %al
	0x0f, 0x85, 0x53, 0x35, 0x00, 0x00, //0x00000634 jne          LBB0_710
	0x49, 0xc7, 0x04, 0xf7, 0x04, 0x00, 0x00, 0x00, //0x0000063a movq         $4, (%r15,%rsi,8)
	0x41, 0xf6, 0xc3, 0x40, //0x00000642 testb        $64, %r11b
	0x0f, 0x85, 0x2f, 0x07, 0x00, 0x00, //0x00000646 jne          LBB0_148
	0x4d, 0x8b, 0x4d, 0x00, //0x0000064c movq         (%r13), %r9
	0x4c, 0x8b, 0x47, 0x08, //0x00000650 movq         $8(%rdi), %r8
	0x4c, 0x89, 0xc3, //0x00000654 movq         %r8, %rbx
	0x41, 0xf6, 0xc3, 0x20, //0x00000657 testb        $32, %r11b
	0x0f, 0x85, 0xf6, 0x09, 0x00, 0x00, //0x0000065b jne          LBB0_190
	0x4c, 0x29, 0xcb, //0x00000661 subq         %r9, %rbx
	0x0f, 0x84, 0xb2, 0x36, 0x00, 0x00, //0x00000664 je           LBB0_717
	0x48, 0x83, 0xfb, 0x40, //0x0000066a cmpq         $64, %rbx
	0x0f, 0x82, 0x29, 0x24, 0x00, 0x00, //0x0000066e jb           LBB0_504
	0x4c, 0x89, 0xce, //0x00000674 movq         %r9, %rsi
	0x48, 0xf7, 0xd6, //0x00000677 notq         %rsi
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x0000067a movq         $-1, %r11
	0x4c, 0x89, 0xc9, //0x00000681 movq         %r9, %rcx
	0x45, 0x31, 0xd2, //0x00000684 xorl         %r10d, %r10d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000687 .p2align 4, 0x90
	//0x00000690 LBB0_50
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x0e, //0x00000690 vmovdqu      (%r14,%rcx), %ymm0
	0x49, 0x89, 0xcf, //0x00000696 movq         %rcx, %r15
	0xc4, 0xc1, 0x7e, 0x6f, 0x4c, 0x0e, 0x20, //0x00000699 vmovdqu      $32(%r14,%rcx), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x000006a0 vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xfa, //0x000006a4 vpmovmskb    %ymm2, %edi
	0xc5, 0xf5, 0x74, 0xd6, //0x000006a8 vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xc2, //0x000006ac vpmovmskb    %ymm2, %eax
	0xc5, 0xfd, 0x74, 0xc7, //0x000006b0 vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xd0, //0x000006b4 vpmovmskb    %ymm0, %edx
	0xc5, 0xf5, 0x74, 0xc7, //0x000006b8 vpcmpeqb     %ymm7, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xc8, //0x000006bc vpmovmskb    %ymm0, %ecx
	0x48, 0xc1, 0xe0, 0x20, //0x000006c0 shlq         $32, %rax
	0x48, 0x09, 0xc7, //0x000006c4 orq          %rax, %rdi
	0x48, 0xc1, 0xe1, 0x20, //0x000006c7 shlq         $32, %rcx
	0x48, 0x09, 0xca, //0x000006cb orq          %rcx, %rdx
	0x0f, 0x85, 0x33, 0x00, 0x00, 0x00, //0x000006ce jne          LBB0_59
	0x4d, 0x85, 0xd2, //0x000006d4 testq        %r10, %r10
	0x0f, 0x85, 0x3b, 0x00, 0x00, 0x00, //0x000006d7 jne          LBB0_61
	0x45, 0x31, 0xd2, //0x000006dd xorl         %r10d, %r10d
	0x48, 0x85, 0xff, //0x000006e0 testq        %rdi, %rdi
	0x0f, 0x85, 0x83, 0x00, 0x00, 0x00, //0x000006e3 jne          LBB0_62
	//0x000006e9 LBB0_53
	0x48, 0x83, 0xc3, 0xc0, //0x000006e9 addq         $-64, %rbx
	0x48, 0x83, 0xc6, 0xc0, //0x000006ed addq         $-64, %rsi
	0x4c, 0x89, 0xf9, //0x000006f1 movq         %r15, %rcx
	0x48, 0x83, 0xc1, 0x40, //0x000006f4 addq         $64, %rcx
	0x48, 0x83, 0xfb, 0x3f, //0x000006f8 cmpq         $63, %rbx
	0x0f, 0x87, 0x8e, 0xff, 0xff, 0xff, //0x000006fc ja           LBB0_50
	0xe9, 0x42, 0x20, 0x00, 0x00, //0x00000702 jmp          LBB0_54
	//0x00000707 LBB0_59
	0x49, 0x83, 0xfb, 0xff, //0x00000707 cmpq         $-1, %r11
	0x0f, 0x85, 0x07, 0x00, 0x00, 0x00, //0x0000070b jne          LBB0_61
	0x4c, 0x0f, 0xbc, 0xda, //0x00000711 bsfq         %rdx, %r11
	0x4d, 0x01, 0xfb, //0x00000715 addq         %r15, %r11
	//0x00000718 LBB0_61
	0x4c, 0x89, 0xd0, //0x00000718 movq         %r10, %rax
	0x48, 0xf7, 0xd0, //0x0000071b notq         %rax
	0x48, 0x21, 0xd0, //0x0000071e andq         %rdx, %rax
	0x48, 0x8d, 0x0c, 0x00, //0x00000721 leaq         (%rax,%rax), %rcx
	0x4c, 0x09, 0xd1, //0x00000725 orq          %r10, %rcx
	0x48, 0x89, 0x0c, 0x24, //0x00000728 movq         %rcx, (%rsp)
	0x48, 0xf7, 0xd1, //0x0000072c notq         %rcx
	0x48, 0x21, 0xd1, //0x0000072f andq         %rdx, %rcx
	0x48, 0xba, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000732 movabsq      $-6148914691236517206, %rdx
	0x48, 0x21, 0xd1, //0x0000073c andq         %rdx, %rcx
	0x45, 0x31, 0xd2, //0x0000073f xorl         %r10d, %r10d
	0x48, 0x01, 0xc1, //0x00000742 addq         %rax, %rcx
	0x41, 0x0f, 0x92, 0xc2, //0x00000745 setb         %r10b
	0x48, 0x01, 0xc9, //0x00000749 addq         %rcx, %rcx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x0000074c movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc1, //0x00000756 xorq         %rax, %rcx
	0x48, 0x23, 0x0c, 0x24, //0x00000759 andq         (%rsp), %rcx
	0x48, 0xf7, 0xd1, //0x0000075d notq         %rcx
	0x48, 0x21, 0xcf, //0x00000760 andq         %rcx, %rdi
	0x48, 0x85, 0xff, //0x00000763 testq        %rdi, %rdi
	0x0f, 0x84, 0x7d, 0xff, 0xff, 0xff, //0x00000766 je           LBB0_53
	//0x0000076c LBB0_62
	0x4c, 0x0f, 0xbc, 0xd7, //0x0000076c bsfq         %rdi, %r10
	0x49, 0x29, 0xf2, //0x00000770 subq         %rsi, %r10
	0x4d, 0x89, 0xdf, //0x00000773 movq         %r11, %r15
	0xe9, 0xa5, 0x0b, 0x00, 0x00, //0x00000776 jmp          LBB0_232
	//0x0000077b LBB0_63
	0x3c, 0x3a, //0x0000077b cmpb         $58, %al
	0x0f, 0x85, 0x0a, 0x34, 0x00, 0x00, //0x0000077d jne          LBB0_710
	0x49, 0xc7, 0x04, 0xf7, 0x00, 0x00, 0x00, 0x00, //0x00000783 movq         $0, (%r15,%rsi,8)
	0xe9, 0x10, 0xfc, 0xff, 0xff, //0x0000078b jmp          LBB0_3
	//0x00000790 LBB0_65
	0x3c, 0x5d, //0x00000790 cmpb         $93, %al
	0x0f, 0x84, 0x74, 0x04, 0x00, 0x00, //0x00000792 je           LBB0_40
	0x48, 0xc7, 0x04, 0x24, 0xff, 0xff, 0xff, 0xff, //0x00000798 movq         $-1, (%rsp)
	0x49, 0xc7, 0x04, 0xf7, 0x01, 0x00, 0x00, 0x00, //0x000007a0 movq         $1, (%r15,%rsi,8)
	0x83, 0xf8, 0x7b, //0x000007a8 cmpl         $123, %eax
	0x0f, 0x87, 0xdc, 0x33, 0x00, 0x00, //0x000007ab ja           LBB0_710
	//0x000007b1 LBB0_67
	0x89, 0xc0, //0x000007b1 movl         %eax, %eax
	0x48, 0x8d, 0x0d, 0x3e, 0x36, 0x00, 0x00, //0x000007b3 leaq         $13886(%rip), %rcx  /* LJTI0_1+0(%rip) */
	0x48, 0x63, 0x04, 0x81, //0x000007ba movslq       (%rcx,%rax,4), %rax
	0x48, 0x01, 0xc8, //0x000007be addq         %rcx, %rax
	0xff, 0xe0, //0x000007c1 jmpq         *%rax
	//0x000007c3 LBB0_70
	0x4c, 0x8b, 0x47, 0x08, //0x000007c3 movq         $8(%rdi), %r8
	0x49, 0x8b, 0x5d, 0x00, //0x000007c7 movq         (%r13), %rbx
	0x41, 0xf6, 0xc3, 0x40, //0x000007cb testb        $64, %r11b
	0x0f, 0x85, 0x67, 0x06, 0x00, 0x00, //0x000007cf jne          LBB0_159
	0x4c, 0x89, 0x6c, 0x24, 0x30, //0x000007d5 movq         %r13, $48(%rsp)
	0x48, 0x8d, 0x53, 0xff, //0x000007da leaq         $-1(%rbx), %rdx
	0x49, 0x29, 0xd0, //0x000007de subq         %rdx, %r8
	0x0f, 0x84, 0x78, 0x33, 0x00, 0x00, //0x000007e1 je           LBB0_682
	0x49, 0x8d, 0x04, 0x1e, //0x000007e7 leaq         (%r14,%rbx), %rax
	0x48, 0x83, 0xc0, 0xff, //0x000007eb addq         $-1, %rax
	0x48, 0x89, 0x44, 0x24, 0x38, //0x000007ef movq         %rax, $56(%rsp)
	0x80, 0x38, 0x30, //0x000007f4 cmpb         $48, (%rax)
	0x48, 0x89, 0x14, 0x24, //0x000007f7 movq         %rdx, (%rsp)
	0x0f, 0x85, 0x3a, 0x00, 0x00, 0x00, //0x000007fb jne          LBB0_76
	0x41, 0xbd, 0x01, 0x00, 0x00, 0x00, //0x00000801 movl         $1, %r13d
	0x49, 0x83, 0xf8, 0x01, //0x00000807 cmpq         $1, %r8
	0x0f, 0x84, 0xa4, 0xfb, 0xff, 0xff, //0x0000080b je           LBB0_1
	0x41, 0x8a, 0x0c, 0x1e, //0x00000811 movb         (%r14,%rbx), %cl
	0x80, 0xc1, 0xd2, //0x00000815 addb         $-46, %cl
	0x80, 0xf9, 0x37, //0x00000818 cmpb         $55, %cl
	0x0f, 0x87, 0x94, 0xfb, 0xff, 0xff, //0x0000081b ja           LBB0_1
	0x0f, 0xb6, 0xc1, //0x00000821 movzbl       %cl, %eax
	0x48, 0xb9, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x00000824 movabsq      $36028797027352577, %rcx
	0x48, 0x0f, 0xa3, 0xc1, //0x0000082e btq          %rax, %rcx
	0x48, 0x89, 0xd1, //0x00000832 movq         %rdx, %rcx
	0x0f, 0x83, 0x7d, 0xfb, 0xff, 0xff, //0x00000835 jae          LBB0_2
	//0x0000083b LBB0_76
	0x48, 0x89, 0x5c, 0x24, 0x20, //0x0000083b movq         %rbx, $32(%rsp)
	0x49, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00000840 movq         $-1, %r10
	0x49, 0x83, 0xf8, 0x20, //0x00000847 cmpq         $32, %r8
	0x0f, 0x82, 0x21, 0x22, 0x00, 0x00, //0x0000084b jb           LBB0_502
	0x45, 0x31, 0xed, //0x00000851 xorl         %r13d, %r13d
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00000854 movq         $-1, %r9
	0x48, 0xc7, 0x44, 0x24, 0x28, 0xff, 0xff, 0xff, 0xff, //0x0000085b movq         $-1, $40(%rsp)
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000864 .p2align 4, 0x90
	//0x00000870 LBB0_78
	0x48, 0x8b, 0x44, 0x24, 0x38, //0x00000870 movq         $56(%rsp), %rax
	0xc4, 0xa1, 0x7e, 0x6f, 0x04, 0x28, //0x00000875 vmovdqu      (%rax,%r13), %ymm0
	0xc4, 0xc1, 0x7d, 0x64, 0xca, //0x0000087b vpcmpgtb     %ymm10, %ymm0, %ymm1
	0xc5, 0x95, 0x64, 0xd0, //0x00000880 vpcmpgtb     %ymm0, %ymm13, %ymm2
	0xc5, 0xed, 0xdb, 0xc9, //0x00000884 vpand        %ymm1, %ymm2, %ymm1
	0xc5, 0x85, 0x74, 0xd0, //0x00000888 vpcmpeqb     %ymm0, %ymm15, %ymm2
	0xc5, 0xfd, 0x74, 0x1d, 0xcc, 0xf9, 0xff, 0xff, //0x0000088c vpcmpeqb     $-1588(%rip), %ymm0, %ymm3  /* LCPI0_17+0(%rip) */
	0xc5, 0xe5, 0xeb, 0xd2, //0x00000894 vpor         %ymm2, %ymm3, %ymm2
	0xc5, 0x8d, 0xdb, 0xd8, //0x00000898 vpand        %ymm0, %ymm14, %ymm3
	0xc5, 0xfd, 0x74, 0x05, 0xdc, 0xf9, 0xff, 0xff, //0x0000089c vpcmpeqb     $-1572(%rip), %ymm0, %ymm0  /* LCPI0_18+0(%rip) */
	0xc5, 0xfd, 0xd7, 0xd0, //0x000008a4 vpmovmskb    %ymm0, %edx
	0xc5, 0xe5, 0x74, 0xdc, //0x000008a8 vpcmpeqb     %ymm4, %ymm3, %ymm3
	0xc5, 0xfd, 0xd7, 0xf3, //0x000008ac vpmovmskb    %ymm3, %esi
	0xc5, 0x7d, 0xd7, 0xfa, //0x000008b0 vpmovmskb    %ymm2, %r15d
	0xc5, 0xf5, 0xeb, 0xc0, //0x000008b4 vpor         %ymm0, %ymm1, %ymm0
	0xc5, 0xe5, 0xeb, 0xca, //0x000008b8 vpor         %ymm2, %ymm3, %ymm1
	0xc5, 0xfd, 0xeb, 0xc1, //0x000008bc vpor         %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xc0, //0x000008c0 vpmovmskb    %ymm0, %eax
	0x48, 0xf7, 0xd0, //0x000008c4 notq         %rax
	0x48, 0x0f, 0xbc, 0xc8, //0x000008c7 bsfq         %rax, %rcx
	0x83, 0xf9, 0x20, //0x000008cb cmpl         $32, %ecx
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x000008ce je           LBB0_80
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x000008d4 movl         $-1, %eax
	0xd3, 0xe0, //0x000008d9 shll         %cl, %eax
	0xf7, 0xd0, //0x000008db notl         %eax
	0x21, 0xc2, //0x000008dd andl         %eax, %edx
	0x21, 0xc6, //0x000008df andl         %eax, %esi
	0x44, 0x21, 0xf8, //0x000008e1 andl         %r15d, %eax
	0x41, 0x89, 0xc7, //0x000008e4 movl         %eax, %r15d
	//0x000008e7 LBB0_80
	0x8d, 0x5a, 0xff, //0x000008e7 leal         $-1(%rdx), %ebx
	0x21, 0xd3, //0x000008ea andl         %edx, %ebx
	0xc5, 0xfe, 0x6f, 0x1d, 0xcc, 0xf8, 0xff, 0xff, //0x000008ec vmovdqu      $-1844(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0x0f, 0x85, 0x4e, 0x1c, 0x00, 0x00, //0x000008f4 jne          LBB0_464
	0x8d, 0x5e, 0xff, //0x000008fa leal         $-1(%rsi), %ebx
	0x21, 0xf3, //0x000008fd andl         %esi, %ebx
	0x0f, 0x85, 0x43, 0x1c, 0x00, 0x00, //0x000008ff jne          LBB0_464
	0x41, 0x8d, 0x5f, 0xff, //0x00000905 leal         $-1(%r15), %ebx
	0x44, 0x21, 0xfb, //0x00000909 andl         %r15d, %ebx
	0x0f, 0x85, 0x36, 0x1c, 0x00, 0x00, //0x0000090c jne          LBB0_464
	0x85, 0xd2, //0x00000912 testl        %edx, %edx
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00000914 je           LBB0_86
	0x0f, 0xbc, 0xd2, //0x0000091a bsfl         %edx, %edx
	0x48, 0x83, 0x7c, 0x24, 0x28, 0xff, //0x0000091d cmpq         $-1, $40(%rsp)
	0x0f, 0x85, 0xde, 0x1d, 0x00, 0x00, //0x00000923 jne          LBB0_483
	0x4c, 0x01, 0xea, //0x00000929 addq         %r13, %rdx
	0x48, 0x89, 0x54, 0x24, 0x28, //0x0000092c movq         %rdx, $40(%rsp)
	//0x00000931 LBB0_86
	0x85, 0xf6, //0x00000931 testl        %esi, %esi
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x00000933 je           LBB0_89
	0x0f, 0xbc, 0xd6, //0x00000939 bsfl         %esi, %edx
	0x49, 0x83, 0xf9, 0xff, //0x0000093c cmpq         $-1, %r9
	0x0f, 0x85, 0xc1, 0x1d, 0x00, 0x00, //0x00000940 jne          LBB0_483
	0x4c, 0x01, 0xea, //0x00000946 addq         %r13, %rdx
	0x49, 0x89, 0xd1, //0x00000949 movq         %rdx, %r9
	//0x0000094c LBB0_89
	0x45, 0x85, 0xff, //0x0000094c testl        %r15d, %r15d
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x0000094f je           LBB0_92
	0x41, 0x0f, 0xbc, 0xd7, //0x00000955 bsfl         %r15d, %edx
	0x49, 0x83, 0xfa, 0xff, //0x00000959 cmpq         $-1, %r10
	0x0f, 0x85, 0xa4, 0x1d, 0x00, 0x00, //0x0000095d jne          LBB0_483
	0x4c, 0x01, 0xea, //0x00000963 addq         %r13, %rdx
	0x49, 0x89, 0xd2, //0x00000966 movq         %rdx, %r10
	//0x00000969 LBB0_92
	0x83, 0xf9, 0x20, //0x00000969 cmpl         $32, %ecx
	0x0f, 0x85, 0x39, 0x08, 0x00, 0x00, //0x0000096c jne          LBB0_210
	0x49, 0x83, 0xc0, 0xe0, //0x00000972 addq         $-32, %r8
	0x49, 0x83, 0xc5, 0x20, //0x00000976 addq         $32, %r13
	0x49, 0x83, 0xf8, 0x1f, //0x0000097a cmpq         $31, %r8
	0x0f, 0x87, 0xec, 0xfe, 0xff, 0xff, //0x0000097e ja           LBB0_78
	0xc5, 0xf8, 0x77, //0x00000984 vzeroupper   
	0xc5, 0x7e, 0x6f, 0x35, 0x51, 0xf8, 0xff, 0xff, //0x00000987 vmovdqu      $-1967(%rip), %ymm14  /* LCPI0_10+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x3d, 0xa9, 0xf8, 0xff, 0xff, //0x0000098f vmovdqu      $-1879(%rip), %ymm15  /* LCPI0_16+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0x81, 0xf8, 0xff, 0xff, //0x00000997 vmovdqu      $-1919(%rip), %ymm13  /* LCPI0_15+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x15, 0x59, 0xf8, 0xff, 0xff, //0x0000099f vmovdqu      $-1959(%rip), %ymm10  /* LCPI0_14+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0xb1, 0xf7, 0xff, 0xff, //0x000009a7 vmovdqu      $-2127(%rip), %ymm12  /* LCPI0_6+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0x89, 0xf7, 0xff, 0xff, //0x000009af vmovdqu      $-2167(%rip), %ymm11  /* LCPI0_5+0(%rip) */
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x000009b7 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0x7e, 0x6f, 0x05, 0x5c, 0xf7, 0xff, 0xff, //0x000009bc vmovdqu      $-2212(%rip), %ymm8  /* LCPI0_4+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0x34, 0xf7, 0xff, 0xff, //0x000009c4 vmovdqu      $-2252(%rip), %ymm7  /* LCPI0_3+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0x0c, 0xf7, 0xff, 0xff, //0x000009cc vmovdqu      $-2292(%rip), %ymm6  /* LCPI0_2+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x2d, 0xe4, 0xf6, 0xff, 0xff, //0x000009d4 vmovdqu      $-2332(%rip), %ymm5  /* LCPI0_1+0(%rip) */
	0x4c, 0x03, 0x6c, 0x24, 0x38, //0x000009dc addq         $56(%rsp), %r13
	0x49, 0x83, 0xf8, 0x10, //0x000009e1 cmpq         $16, %r8
	0x0f, 0x82, 0x57, 0x01, 0x00, 0x00, //0x000009e5 jb           LBB0_113
	//0x000009eb LBB0_95
	0x4c, 0x89, 0xef, //0x000009eb movq         %r13, %rdi
	0x48, 0x2b, 0x7c, 0x24, 0x20, //0x000009ee subq         $32(%rsp), %rdi
	0x4c, 0x29, 0xf7, //0x000009f3 subq         %r14, %rdi
	0x48, 0x83, 0xc7, 0x01, //0x000009f6 addq         $1, %rdi
	0x45, 0x31, 0xff, //0x000009fa xorl         %r15d, %r15d
	0x90, 0x90, 0x90, //0x000009fd .p2align 4, 0x90
	//0x00000a00 LBB0_96
	0xc4, 0x81, 0x7a, 0x6f, 0x44, 0x3d, 0x00, //0x00000a00 vmovdqu      (%r13,%r15), %xmm0
	0xc5, 0xf9, 0x64, 0x0d, 0x31, 0xf6, 0xff, 0xff, //0x00000a07 vpcmpgtb     $-2511(%rip), %xmm0, %xmm1  /* LCPI0_20+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x15, 0x39, 0xf6, 0xff, 0xff, //0x00000a0f vmovdqu      $-2503(%rip), %xmm2  /* LCPI0_21+0(%rip) */
	0xc5, 0xe9, 0x64, 0xd0, //0x00000a17 vpcmpgtb     %xmm0, %xmm2, %xmm2
	0xc5, 0xf1, 0xdb, 0xca, //0x00000a1b vpand        %xmm2, %xmm1, %xmm1
	0xc5, 0xf9, 0x74, 0x15, 0x39, 0xf6, 0xff, 0xff, //0x00000a1f vpcmpeqb     $-2503(%rip), %xmm0, %xmm2  /* LCPI0_22+0(%rip) */
	0xc5, 0xf9, 0x74, 0x1d, 0x41, 0xf6, 0xff, 0xff, //0x00000a27 vpcmpeqb     $-2495(%rip), %xmm0, %xmm3  /* LCPI0_23+0(%rip) */
	0xc5, 0xe1, 0xeb, 0xd2, //0x00000a2f vpor         %xmm2, %xmm3, %xmm2
	0xc5, 0xf9, 0xdb, 0x1d, 0xe5, 0xf5, 0xff, 0xff, //0x00000a33 vpand        $-2587(%rip), %xmm0, %xmm3  /* LCPI0_12+0(%rip) */
	0xc5, 0xf9, 0x74, 0x05, 0x3d, 0xf6, 0xff, 0xff, //0x00000a3b vpcmpeqb     $-2499(%rip), %xmm0, %xmm0  /* LCPI0_24+0(%rip) */
	0xc5, 0xe1, 0x74, 0x1d, 0x45, 0xf6, 0xff, 0xff, //0x00000a43 vpcmpeqb     $-2491(%rip), %xmm3, %xmm3  /* LCPI0_25+0(%rip) */
	0xc5, 0xe1, 0xeb, 0xe0, //0x00000a4b vpor         %xmm0, %xmm3, %xmm4
	0xc5, 0xe9, 0xeb, 0xc9, //0x00000a4f vpor         %xmm1, %xmm2, %xmm1
	0xc5, 0xd9, 0xeb, 0xc9, //0x00000a53 vpor         %xmm1, %xmm4, %xmm1
	0xc5, 0xf9, 0xd7, 0xd0, //0x00000a57 vpmovmskb    %xmm0, %edx
	0xc5, 0x79, 0xd7, 0xdb, //0x00000a5b vpmovmskb    %xmm3, %r11d
	0xc5, 0xf9, 0xd7, 0xf2, //0x00000a5f vpmovmskb    %xmm2, %esi
	0xc5, 0xf9, 0xd7, 0xc1, //0x00000a63 vpmovmskb    %xmm1, %eax
	0xf7, 0xd0, //0x00000a67 notl         %eax
	0x0f, 0xbc, 0xc8, //0x00000a69 bsfl         %eax, %ecx
	0x83, 0xf9, 0x10, //0x00000a6c cmpl         $16, %ecx
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00000a6f je           LBB0_98
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x00000a75 movl         $-1, %eax
	0xd3, 0xe0, //0x00000a7a shll         %cl, %eax
	0xf7, 0xd0, //0x00000a7c notl         %eax
	0x21, 0xc2, //0x00000a7e andl         %eax, %edx
	0x41, 0x21, 0xc3, //0x00000a80 andl         %eax, %r11d
	0x21, 0xf0, //0x00000a83 andl         %esi, %eax
	0x89, 0xc6, //0x00000a85 movl         %eax, %esi
	//0x00000a87 LBB0_98
	0xc5, 0xfe, 0x6f, 0x25, 0x11, 0xf8, 0xff, 0xff, //0x00000a87 vmovdqu      $-2031(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x1d, 0x29, 0xf7, 0xff, 0xff, //0x00000a8f vmovdqu      $-2263(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0x8d, 0x5a, 0xff, //0x00000a97 leal         $-1(%rdx), %ebx
	0x21, 0xd3, //0x00000a9a andl         %edx, %ebx
	0x0f, 0x85, 0x62, 0x1d, 0x00, 0x00, //0x00000a9c jne          LBB0_486
	0x41, 0x8d, 0x5b, 0xff, //0x00000aa2 leal         $-1(%r11), %ebx
	0x44, 0x21, 0xdb, //0x00000aa6 andl         %r11d, %ebx
	0x0f, 0x85, 0x55, 0x1d, 0x00, 0x00, //0x00000aa9 jne          LBB0_486
	0x8d, 0x5e, 0xff, //0x00000aaf leal         $-1(%rsi), %ebx
	0x21, 0xf3, //0x00000ab2 andl         %esi, %ebx
	0x0f, 0x85, 0x4a, 0x1d, 0x00, 0x00, //0x00000ab4 jne          LBB0_486
	0x85, 0xd2, //0x00000aba testl        %edx, %edx
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x00000abc je           LBB0_104
	0x0f, 0xbc, 0xd2, //0x00000ac2 bsfl         %edx, %edx
	0x48, 0x83, 0x7c, 0x24, 0x28, 0xff, //0x00000ac5 cmpq         $-1, $40(%rsp)
	0x0f, 0x85, 0x73, 0x1d, 0x00, 0x00, //0x00000acb jne          LBB0_489
	0x48, 0x01, 0xfa, //0x00000ad1 addq         %rdi, %rdx
	0x4c, 0x01, 0xfa, //0x00000ad4 addq         %r15, %rdx
	0x48, 0x89, 0x54, 0x24, 0x28, //0x00000ad7 movq         %rdx, $40(%rsp)
	//0x00000adc LBB0_104
	0x45, 0x85, 0xdb, //0x00000adc testl        %r11d, %r11d
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00000adf je           LBB0_107
	0x41, 0x0f, 0xbc, 0xd3, //0x00000ae5 bsfl         %r11d, %edx
	0x49, 0x83, 0xf9, 0xff, //0x00000ae9 cmpq         $-1, %r9
	0x0f, 0x85, 0x51, 0x1d, 0x00, 0x00, //0x00000aed jne          LBB0_489
	0x48, 0x01, 0xfa, //0x00000af3 addq         %rdi, %rdx
	0x4c, 0x01, 0xfa, //0x00000af6 addq         %r15, %rdx
	0x49, 0x89, 0xd1, //0x00000af9 movq         %rdx, %r9
	//0x00000afc LBB0_107
	0x85, 0xf6, //0x00000afc testl        %esi, %esi
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00000afe je           LBB0_110
	0x0f, 0xbc, 0xd6, //0x00000b04 bsfl         %esi, %edx
	0x49, 0x83, 0xfa, 0xff, //0x00000b07 cmpq         $-1, %r10
	0x0f, 0x85, 0x33, 0x1d, 0x00, 0x00, //0x00000b0b jne          LBB0_489
	0x48, 0x01, 0xfa, //0x00000b11 addq         %rdi, %rdx
	0x4c, 0x01, 0xfa, //0x00000b14 addq         %r15, %rdx
	0x49, 0x89, 0xd2, //0x00000b17 movq         %rdx, %r10
	//0x00000b1a LBB0_110
	0x83, 0xf9, 0x10, //0x00000b1a cmpl         $16, %ecx
	0x0f, 0x85, 0xbe, 0x0a, 0x00, 0x00, //0x00000b1d jne          LBB0_277
	0x49, 0x83, 0xc0, 0xf0, //0x00000b23 addq         $-16, %r8
	0x49, 0x83, 0xc7, 0x10, //0x00000b27 addq         $16, %r15
	0x49, 0x83, 0xf8, 0x0f, //0x00000b2b cmpq         $15, %r8
	0x0f, 0x87, 0xcb, 0xfe, 0xff, 0xff, //0x00000b2f ja           LBB0_96
	0x4d, 0x01, 0xfd, //0x00000b35 addq         %r15, %r13
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x00000b38 movq         $24(%rsp), %r11
	0x48, 0x8b, 0x7c, 0x24, 0x10, //0x00000b3d movq         $16(%rsp), %rdi
	//0x00000b42 LBB0_113
	0x4d, 0x85, 0xc0, //0x00000b42 testq        %r8, %r8
	0xc5, 0xfe, 0x6f, 0x25, 0x53, 0xf7, 0xff, 0xff, //0x00000b45 vmovdqu      $-2221(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0x0f, 0x84, 0xa8, 0x0a, 0x00, 0x00, //0x00000b4d je           LBB0_279
	0x4f, 0x8d, 0x3c, 0x28, //0x00000b53 leaq         (%r8,%r13), %r15
	0x4c, 0x89, 0xee, //0x00000b57 movq         %r13, %rsi
	0x48, 0x2b, 0x74, 0x24, 0x20, //0x00000b5a subq         $32(%rsp), %rsi
	0x4c, 0x29, 0xf6, //0x00000b5f subq         %r14, %rsi
	0x48, 0x83, 0xc6, 0x01, //0x00000b62 addq         $1, %rsi
	0x31, 0xc9, //0x00000b66 xorl         %ecx, %ecx
	0xc5, 0xfe, 0x6f, 0x1d, 0x50, 0xf6, 0xff, 0xff, //0x00000b68 vmovdqu      $-2480(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xe9, 0x28, 0x00, 0x00, 0x00, //0x00000b70 jmp          LBB0_118
	//0x00000b75 LBB0_115
	0x48, 0x83, 0x7c, 0x24, 0x28, 0xff, //0x00000b75 cmpq         $-1, $40(%rsp)
	0x0f, 0x85, 0x8f, 0x1c, 0x00, 0x00, //0x00000b7b jne          LBB0_487
	0x48, 0x8d, 0x04, 0x0e, //0x00000b81 leaq         (%rsi,%rcx), %rax
	0x48, 0x89, 0x44, 0x24, 0x28, //0x00000b85 movq         %rax, $40(%rsp)
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000b8a .p2align 4, 0x90
	//0x00000b90 LBB0_117
	0x48, 0x83, 0xc1, 0x01, //0x00000b90 addq         $1, %rcx
	0x49, 0x39, 0xc8, //0x00000b94 cmpq         %rcx, %r8
	0x0f, 0x84, 0x2d, 0x19, 0x00, 0x00, //0x00000b97 je           LBB0_457
	//0x00000b9d LBB0_118
	0x41, 0x0f, 0xbe, 0x54, 0x0d, 0x00, //0x00000b9d movsbl       (%r13,%rcx), %edx
	0x8d, 0x42, 0xd0, //0x00000ba3 leal         $-48(%rdx), %eax
	0x83, 0xf8, 0x0a, //0x00000ba6 cmpl         $10, %eax
	0x0f, 0x82, 0xe1, 0xff, 0xff, 0xff, //0x00000ba9 jb           LBB0_117
	0x8d, 0x5a, 0xd5, //0x00000baf leal         $-43(%rdx), %ebx
	0x83, 0xfb, 0x1a, //0x00000bb2 cmpl         $26, %ebx
	0x0f, 0x87, 0x23, 0x00, 0x00, 0x00, //0x00000bb5 ja           LBB0_123
	0x48, 0x8d, 0x15, 0x92, 0x34, 0x00, 0x00, //0x00000bbb leaq         $13458(%rip), %rdx  /* LJTI0_3+0(%rip) */
	0x48, 0x63, 0x04, 0x9a, //0x00000bc2 movslq       (%rdx,%rbx,4), %rax
	0x48, 0x01, 0xd0, //0x00000bc6 addq         %rdx, %rax
	0xff, 0xe0, //0x00000bc9 jmpq         *%rax
	//0x00000bcb LBB0_121
	0x49, 0x83, 0xfa, 0xff, //0x00000bcb cmpq         $-1, %r10
	0x0f, 0x85, 0x3b, 0x1c, 0x00, 0x00, //0x00000bcf jne          LBB0_487
	0x4c, 0x8d, 0x14, 0x0e, //0x00000bd5 leaq         (%rsi,%rcx), %r10
	0xe9, 0xb2, 0xff, 0xff, 0xff, //0x00000bd9 jmp          LBB0_117
	//0x00000bde LBB0_123
	0x83, 0xfa, 0x65, //0x00000bde cmpl         $101, %edx
	0x0f, 0x85, 0x11, 0x0a, 0x00, 0x00, //0x00000be1 jne          LBB0_278
	//0x00000be7 LBB0_124
	0x49, 0x83, 0xf9, 0xff, //0x00000be7 cmpq         $-1, %r9
	0x0f, 0x85, 0x1f, 0x1c, 0x00, 0x00, //0x00000beb jne          LBB0_487
	0x4c, 0x8d, 0x0c, 0x0e, //0x00000bf1 leaq         (%rsi,%rcx), %r9
	0xe9, 0x96, 0xff, 0xff, 0xff, //0x00000bf5 jmp          LBB0_117
	//0x00000bfa LBB0_68
	0x83, 0xf8, 0x22, //0x00000bfa cmpl         $34, %eax
	0x0f, 0x84, 0x40, 0x00, 0x00, 0x00, //0x00000bfd je           LBB0_130
	//0x00000c03 LBB0_43
	0x83, 0xf8, 0x7d, //0x00000c03 cmpl         $125, %eax
	0x0f, 0x85, 0x81, 0x2f, 0x00, 0x00, //0x00000c06 jne          LBB0_710
	//0x00000c0c LBB0_40
	0x4d, 0x89, 0x07, //0x00000c0c movq         %r8, (%r15)
	0x4c, 0x89, 0x24, 0x24, //0x00000c0f movq         %r12, (%rsp)
	0x4d, 0x85, 0xc0, //0x00000c13 testq        %r8, %r8
	0x0f, 0x85, 0xc4, 0xf7, 0xff, 0xff, //0x00000c16 jne          LBB0_5
	0xe9, 0x74, 0x2f, 0x00, 0x00, //0x00000c1c jmp          LBB0_711
	//0x00000c21 LBB0_126
	0x48, 0x81, 0xfe, 0xff, 0x0f, 0x00, 0x00, //0x00000c21 cmpq         $4095, %rsi
	0x0f, 0x8f, 0xf1, 0x2e, 0x00, 0x00, //0x00000c28 jg           LBB0_705
	0x48, 0x8d, 0x4e, 0x01, //0x00000c2e leaq         $1(%rsi), %rcx
	0x49, 0x89, 0x0f, //0x00000c32 movq         %rcx, (%r15)
	0x49, 0xc7, 0x44, 0xf7, 0x08, 0x00, 0x00, 0x00, 0x00, //0x00000c35 movq         $0, $8(%r15,%rsi,8)
	0xe9, 0x5d, 0xf7, 0xff, 0xff, //0x00000c3e jmp          LBB0_3
	//0x00000c43 LBB0_130
	0x49, 0xc7, 0x04, 0xf7, 0x02, 0x00, 0x00, 0x00, //0x00000c43 movq         $2, (%r15,%rsi,8)
	0x41, 0xf6, 0xc3, 0x40, //0x00000c4b testb        $64, %r11b
	0x0f, 0x85, 0x17, 0x03, 0x00, 0x00, //0x00000c4f jne          LBB0_179
	0x4d, 0x8b, 0x4d, 0x00, //0x00000c55 movq         (%r13), %r9
	0x4c, 0x8b, 0x47, 0x08, //0x00000c59 movq         $8(%rdi), %r8
	0x4c, 0x89, 0xc1, //0x00000c5d movq         %r8, %rcx
	0x41, 0xf6, 0xc3, 0x20, //0x00000c60 testb        $32, %r11b
	0x0f, 0x85, 0x54, 0x05, 0x00, 0x00, //0x00000c64 jne          LBB0_211
	0x4c, 0x29, 0xc9, //0x00000c6a subq         %r9, %rcx
	0x0f, 0x84, 0xa9, 0x30, 0x00, 0x00, //0x00000c6d je           LBB0_717
	0x48, 0x83, 0xf9, 0x40, //0x00000c73 cmpq         $64, %rcx
	0x0f, 0x82, 0xc8, 0x1e, 0x00, 0x00, //0x00000c77 jb           LBB0_511
	0x4c, 0x89, 0xce, //0x00000c7d movq         %r9, %rsi
	0x48, 0xf7, 0xd6, //0x00000c80 notq         %rsi
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00000c83 movq         $-1, %r11
	0x4c, 0x89, 0xcb, //0x00000c8a movq         %r9, %rbx
	0x45, 0x31, 0xd2, //0x00000c8d xorl         %r10d, %r10d
	//0x00000c90 .p2align 4, 0x90
	//0x00000c90 LBB0_135
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x1e, //0x00000c90 vmovdqu      (%r14,%rbx), %ymm0
	0x49, 0x89, 0xdf, //0x00000c96 movq         %rbx, %r15
	0xc4, 0xc1, 0x7e, 0x6f, 0x4c, 0x1e, 0x20, //0x00000c99 vmovdqu      $32(%r14,%rbx), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x00000ca0 vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xfa, //0x00000ca4 vpmovmskb    %ymm2, %edi
	0xc5, 0xf5, 0x74, 0xd6, //0x00000ca8 vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xc2, //0x00000cac vpmovmskb    %ymm2, %eax
	0xc5, 0xfd, 0x74, 0xc7, //0x00000cb0 vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xd0, //0x00000cb4 vpmovmskb    %ymm0, %edx
	0xc5, 0xf5, 0x74, 0xc7, //0x00000cb8 vpcmpeqb     %ymm7, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xd8, //0x00000cbc vpmovmskb    %ymm0, %ebx
	0x48, 0xc1, 0xe0, 0x20, //0x00000cc0 shlq         $32, %rax
	0x48, 0x09, 0xc7, //0x00000cc4 orq          %rax, %rdi
	0x48, 0xc1, 0xe3, 0x20, //0x00000cc7 shlq         $32, %rbx
	0x48, 0x09, 0xda, //0x00000ccb orq          %rbx, %rdx
	0x0f, 0x85, 0x33, 0x00, 0x00, 0x00, //0x00000cce jne          LBB0_144
	0x4d, 0x85, 0xd2, //0x00000cd4 testq        %r10, %r10
	0x0f, 0x85, 0x3b, 0x00, 0x00, 0x00, //0x00000cd7 jne          LBB0_146
	0x45, 0x31, 0xd2, //0x00000cdd xorl         %r10d, %r10d
	0x48, 0x85, 0xff, //0x00000ce0 testq        %rdi, %rdi
	0x0f, 0x85, 0x83, 0x00, 0x00, 0x00, //0x00000ce3 jne          LBB0_147
	//0x00000ce9 LBB0_138
	0x48, 0x83, 0xc1, 0xc0, //0x00000ce9 addq         $-64, %rcx
	0x48, 0x83, 0xc6, 0xc0, //0x00000ced addq         $-64, %rsi
	0x4c, 0x89, 0xfb, //0x00000cf1 movq         %r15, %rbx
	0x48, 0x83, 0xc3, 0x40, //0x00000cf4 addq         $64, %rbx
	0x48, 0x83, 0xf9, 0x3f, //0x00000cf8 cmpq         $63, %rcx
	0x0f, 0x87, 0x8e, 0xff, 0xff, 0xff, //0x00000cfc ja           LBB0_135
	0xe9, 0x6f, 0x1b, 0x00, 0x00, //0x00000d02 jmp          LBB0_139
	//0x00000d07 LBB0_144
	0x49, 0x83, 0xfb, 0xff, //0x00000d07 cmpq         $-1, %r11
	0x0f, 0x85, 0x07, 0x00, 0x00, 0x00, //0x00000d0b jne          LBB0_146
	0x4c, 0x0f, 0xbc, 0xda, //0x00000d11 bsfq         %rdx, %r11
	0x4d, 0x01, 0xfb, //0x00000d15 addq         %r15, %r11
	//0x00000d18 LBB0_146
	0x4c, 0x89, 0xd0, //0x00000d18 movq         %r10, %rax
	0x48, 0xf7, 0xd0, //0x00000d1b notq         %rax
	0x48, 0x21, 0xd0, //0x00000d1e andq         %rdx, %rax
	0x48, 0x8d, 0x1c, 0x00, //0x00000d21 leaq         (%rax,%rax), %rbx
	0x4c, 0x09, 0xd3, //0x00000d25 orq          %r10, %rbx
	0x48, 0x89, 0x1c, 0x24, //0x00000d28 movq         %rbx, (%rsp)
	0x48, 0xf7, 0xd3, //0x00000d2c notq         %rbx
	0x48, 0x21, 0xd3, //0x00000d2f andq         %rdx, %rbx
	0x48, 0xba, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000d32 movabsq      $-6148914691236517206, %rdx
	0x48, 0x21, 0xd3, //0x00000d3c andq         %rdx, %rbx
	0x45, 0x31, 0xd2, //0x00000d3f xorl         %r10d, %r10d
	0x48, 0x01, 0xc3, //0x00000d42 addq         %rax, %rbx
	0x41, 0x0f, 0x92, 0xc2, //0x00000d45 setb         %r10b
	0x48, 0x01, 0xdb, //0x00000d49 addq         %rbx, %rbx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000d4c movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc3, //0x00000d56 xorq         %rax, %rbx
	0x48, 0x23, 0x1c, 0x24, //0x00000d59 andq         (%rsp), %rbx
	0x48, 0xf7, 0xd3, //0x00000d5d notq         %rbx
	0x48, 0x21, 0xdf, //0x00000d60 andq         %rbx, %rdi
	0x48, 0x85, 0xff, //0x00000d63 testq        %rdi, %rdi
	0x0f, 0x84, 0x7d, 0xff, 0xff, 0xff, //0x00000d66 je           LBB0_138
	//0x00000d6c LBB0_147
	0x4c, 0x0f, 0xbc, 0xd7, //0x00000d6c bsfq         %rdi, %r10
	0x49, 0x29, 0xf2, //0x00000d70 subq         %rsi, %r10
	0x4d, 0x89, 0xdf, //0x00000d73 movq         %r11, %r15
	0xe9, 0x11, 0x0a, 0x00, 0x00, //0x00000d76 jmp          LBB0_298
	//0x00000d7b LBB0_148
	0x48, 0xc7, 0x04, 0x24, 0xff, 0xff, 0xff, 0xff, //0x00000d7b movq         $-1, (%rsp)
	0x4c, 0x8b, 0x57, 0x08, //0x00000d83 movq         $8(%rdi), %r10
	0x4d, 0x8b, 0x45, 0x00, //0x00000d87 movq         (%r13), %r8
	0x4f, 0x8d, 0x0c, 0x06, //0x00000d8b leaq         (%r14,%r8), %r9
	0x4d, 0x29, 0xc2, //0x00000d8f subq         %r8, %r10
	0x49, 0x83, 0xfa, 0x20, //0x00000d92 cmpq         $32, %r10
	0x0f, 0x8c, 0x9e, 0x17, 0x00, 0x00, //0x00000d96 jl           LBB0_158
	0xb9, 0x20, 0x00, 0x00, 0x00, //0x00000d9c movl         $32, %ecx
	0x31, 0xf6, //0x00000da1 xorl         %esi, %esi
	0x45, 0x31, 0xff, //0x00000da3 xorl         %r15d, %r15d
	0xe9, 0x59, 0x00, 0x00, 0x00, //0x00000da6 jmp          LBB0_150
	0x90, 0x90, 0x90, 0x90, 0x90, //0x00000dab .p2align 4, 0x90
	//0x00000db0 LBB0_154
	0x44, 0x89, 0xfa, //0x00000db0 movl         %r15d, %edx
	0xf7, 0xd2, //0x00000db3 notl         %edx
	0x21, 0xda, //0x00000db5 andl         %ebx, %edx
	0x8d, 0x04, 0x12, //0x00000db7 leal         (%rdx,%rdx), %eax
	0x44, 0x09, 0xf8, //0x00000dba orl          %r15d, %eax
	0x89, 0xc7, //0x00000dbd movl         %eax, %edi
	0xf7, 0xd7, //0x00000dbf notl         %edi
	0x21, 0xdf, //0x00000dc1 andl         %ebx, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000dc3 andl         $-1431655766, %edi
	0x45, 0x31, 0xff, //0x00000dc9 xorl         %r15d, %r15d
	0x01, 0xd7, //0x00000dcc addl         %edx, %edi
	0x41, 0x0f, 0x92, 0xc7, //0x00000dce setb         %r15b
	0x01, 0xff, //0x00000dd2 addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x00000dd4 xorl         $1431655765, %edi
	0x21, 0xc7, //0x00000dda andl         %eax, %edi
	0xf7, 0xd7, //0x00000ddc notl         %edi
	0x41, 0x21, 0xfb, //0x00000dde andl         %edi, %r11d
	0x4d, 0x85, 0xdb, //0x00000de1 testq        %r11, %r11
	0x0f, 0x85, 0x7f, 0xf5, 0xff, 0xff, //0x00000de4 jne          LBB0_153
	//0x00000dea LBB0_155
	0x48, 0x83, 0xc6, 0x20, //0x00000dea addq         $32, %rsi
	0x49, 0x8d, 0x04, 0x0a, //0x00000dee leaq         (%r10,%rcx), %rax
	0x48, 0x83, 0xc0, 0xe0, //0x00000df2 addq         $-32, %rax
	0x48, 0x83, 0xc1, 0xe0, //0x00000df6 addq         $-32, %rcx
	0x48, 0x83, 0xf8, 0x3f, //0x00000dfa cmpq         $63, %rax
	0x0f, 0x8e, 0x18, 0x17, 0x00, 0x00, //0x00000dfe jle          LBB0_156
	//0x00000e04 LBB0_150
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x31, //0x00000e04 vmovdqu      (%r9,%rsi), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x00000e0a vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0x7d, 0xd7, 0xd9, //0x00000e0e vpmovmskb    %ymm1, %r11d
	0xc5, 0xfd, 0x74, 0xc7, //0x00000e12 vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xd8, //0x00000e16 vpmovmskb    %ymm0, %ebx
	0x85, 0xdb, //0x00000e1a testl        %ebx, %ebx
	0x0f, 0x85, 0x8e, 0xff, 0xff, 0xff, //0x00000e1c jne          LBB0_154
	0x4d, 0x85, 0xff, //0x00000e22 testq        %r15, %r15
	0x0f, 0x85, 0x85, 0xff, 0xff, 0xff, //0x00000e25 jne          LBB0_154
	0x45, 0x31, 0xff, //0x00000e2b xorl         %r15d, %r15d
	0x4d, 0x85, 0xdb, //0x00000e2e testq        %r11, %r11
	0x0f, 0x84, 0xb3, 0xff, 0xff, 0xff, //0x00000e31 je           LBB0_155
	0xe9, 0x2d, 0xf5, 0xff, 0xff, //0x00000e37 jmp          LBB0_153
	//0x00000e3c LBB0_159
	0x4c, 0x89, 0xc6, //0x00000e3c movq         %r8, %rsi
	0x48, 0x29, 0xde, //0x00000e3f subq         %rbx, %rsi
	0x48, 0x83, 0xfe, 0x20, //0x00000e42 cmpq         $32, %rsi
	0x0f, 0x82, 0x13, 0x1c, 0x00, 0x00, //0x00000e46 jb           LBB0_501
	0x48, 0x89, 0xda, //0x00000e4c movq         %rbx, %rdx
	0x48, 0xf7, 0xda, //0x00000e4f negq         %rdx
	0x48, 0x89, 0xd9, //0x00000e52 movq         %rbx, %rcx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000e55 .p2align 4, 0x90
	//0x00000e60 LBB0_161
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x0e, //0x00000e60 vmovdqu      (%r14,%rcx), %ymm0
	0xc5, 0xfd, 0x74, 0xcb, //0x00000e66 vpcmpeqb     %ymm3, %ymm0, %ymm1
	0xc5, 0x8d, 0xdb, 0xc0, //0x00000e6a vpand        %ymm0, %ymm14, %ymm0
	0xc5, 0xfd, 0x74, 0x05, 0x2a, 0xf3, 0xff, 0xff, //0x00000e6e vpcmpeqb     $-3286(%rip), %ymm0, %ymm0  /* LCPI0_8+0(%rip) */
	0xc5, 0xfd, 0xeb, 0xc1, //0x00000e76 vpor         %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x00000e7a vpmovmskb    %ymm0, %esi
	0x85, 0xf6, //0x00000e7e testl        %esi, %esi
	0x0f, 0x85, 0xce, 0x00, 0x00, 0x00, //0x00000e80 jne          LBB0_175
	0x48, 0x83, 0xc1, 0x20, //0x00000e86 addq         $32, %rcx
	0x49, 0x8d, 0x04, 0x10, //0x00000e8a leaq         (%r8,%rdx), %rax
	0x48, 0x83, 0xc0, 0xe0, //0x00000e8e addq         $-32, %rax
	0x48, 0x83, 0xc2, 0xe0, //0x00000e92 addq         $-32, %rdx
	0x48, 0x83, 0xf8, 0x1f, //0x00000e96 cmpq         $31, %rax
	0x0f, 0x87, 0xc0, 0xff, 0xff, 0xff, //0x00000e9a ja           LBB0_161
	0x4c, 0x89, 0xf1, //0x00000ea0 movq         %r14, %rcx
	0x48, 0x29, 0xd1, //0x00000ea3 subq         %rdx, %rcx
	0x49, 0x01, 0xd0, //0x00000ea6 addq         %rdx, %r8
	0x4c, 0x89, 0xc6, //0x00000ea9 movq         %r8, %rsi
	0x48, 0x83, 0xfe, 0x10, //0x00000eac cmpq         $16, %rsi
	0x0f, 0x82, 0x48, 0x00, 0x00, 0x00, //0x00000eb0 jb           LBB0_167
	//0x00000eb6 LBB0_164
	0x4c, 0x89, 0xf2, //0x00000eb6 movq         %r14, %rdx
	0x48, 0x29, 0xca, //0x00000eb9 subq         %rcx, %rdx
	//0x00000ebc LBB0_165
	0xc5, 0xfa, 0x6f, 0x01, //0x00000ebc vmovdqu      (%rcx), %xmm0
	0xc5, 0xf9, 0x74, 0x0d, 0x48, 0xf1, 0xff, 0xff, //0x00000ec0 vpcmpeqb     $-3768(%rip), %xmm0, %xmm1  /* LCPI0_11+0(%rip) */
	0xc5, 0xf9, 0xdb, 0x05, 0x50, 0xf1, 0xff, 0xff, //0x00000ec8 vpand        $-3760(%rip), %xmm0, %xmm0  /* LCPI0_12+0(%rip) */
	0xc5, 0xf9, 0x74, 0x05, 0x58, 0xf1, 0xff, 0xff, //0x00000ed0 vpcmpeqb     $-3752(%rip), %xmm0, %xmm0  /* LCPI0_13+0(%rip) */
	0xc5, 0xf9, 0xeb, 0xc1, //0x00000ed8 vpor         %xmm1, %xmm0, %xmm0
	0xc5, 0xf9, 0xd7, 0xc0, //0x00000edc vpmovmskb    %xmm0, %eax
	0x85, 0xc0, //0x00000ee0 testl        %eax, %eax
	0x0f, 0x85, 0x6c, 0x16, 0x00, 0x00, //0x00000ee2 jne          LBB0_465
	0x48, 0x83, 0xc1, 0x10, //0x00000ee8 addq         $16, %rcx
	0x48, 0x83, 0xc6, 0xf0, //0x00000eec addq         $-16, %rsi
	0x48, 0x83, 0xc2, 0xf0, //0x00000ef0 addq         $-16, %rdx
	0x48, 0x83, 0xfe, 0x0f, //0x00000ef4 cmpq         $15, %rsi
	0x0f, 0x87, 0xbe, 0xff, 0xff, 0xff, //0x00000ef8 ja           LBB0_165
	//0x00000efe LBB0_167
	0x48, 0x89, 0xd8, //0x00000efe movq         %rbx, %rax
	0xc5, 0x7d, 0x7f, 0xe1, //0x00000f01 vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd8, //0x00000f05 vmovdqa      %ymm11, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xdd, //0x00000f09 vmovdqa      %ymm13, %ymm11
	0x48, 0x85, 0xf6, //0x00000f0e testq        %rsi, %rsi
	0x0f, 0x84, 0x35, 0x00, 0x00, 0x00, //0x00000f11 je           LBB0_174
	0x48, 0x8d, 0x3c, 0x31, //0x00000f17 leaq         (%rcx,%rsi), %rdi
	0x31, 0xd2, //0x00000f1b xorl         %edx, %edx
	//0x00000f1d LBB0_169
	0x0f, 0xb6, 0x1c, 0x11, //0x00000f1d movzbl       (%rcx,%rdx), %ebx
	0x80, 0xfb, 0x2c, //0x00000f21 cmpb         $44, %bl
	0x0f, 0x84, 0xb0, 0x1b, 0x00, 0x00, //0x00000f24 je           LBB0_507
	0x80, 0xfb, 0x7d, //0x00000f2a cmpb         $125, %bl
	0x0f, 0x84, 0xa7, 0x1b, 0x00, 0x00, //0x00000f2d je           LBB0_507
	0x80, 0xfb, 0x5d, //0x00000f33 cmpb         $93, %bl
	0x0f, 0x84, 0x9e, 0x1b, 0x00, 0x00, //0x00000f36 je           LBB0_507
	0x48, 0x83, 0xc2, 0x01, //0x00000f3c addq         $1, %rdx
	0x48, 0x39, 0xd6, //0x00000f40 cmpq         %rdx, %rsi
	0x0f, 0x85, 0xd4, 0xff, 0xff, 0xff, //0x00000f43 jne          LBB0_169
	0x48, 0x89, 0xf9, //0x00000f49 movq         %rdi, %rcx
	//0x00000f4c LBB0_174
	0x4c, 0x29, 0xf1, //0x00000f4c subq         %r14, %rcx
	0xe9, 0x8c, 0x1b, 0x00, 0x00, //0x00000f4f jmp          LBB0_508
	//0x00000f54 LBB0_175
	0x0f, 0xbc, 0xce, //0x00000f54 bsfl         %esi, %ecx
	//0x00000f57 LBB0_176
	0x48, 0x29, 0xd1, //0x00000f57 subq         %rdx, %rcx
	0x49, 0x89, 0x4d, 0x00, //0x00000f5a movq         %rcx, (%r13)
	0x48, 0x85, 0xdb, //0x00000f5e testq        %rbx, %rbx
	0x0f, 0x8f, 0x39, 0xf4, 0xff, 0xff, //0x00000f61 jg           LBB0_3
	0xe9, 0xff, 0x2b, 0x00, 0x00, //0x00000f67 jmp          LBB0_178
	//0x00000f6c LBB0_179
	0x48, 0xc7, 0x04, 0x24, 0xff, 0xff, 0xff, 0xff, //0x00000f6c movq         $-1, (%rsp)
	0x4c, 0x8b, 0x57, 0x08, //0x00000f74 movq         $8(%rdi), %r10
	0x4d, 0x8b, 0x45, 0x00, //0x00000f78 movq         (%r13), %r8
	0x4f, 0x8d, 0x0c, 0x06, //0x00000f7c leaq         (%r14,%r8), %r9
	0x4d, 0x29, 0xc2, //0x00000f80 subq         %r8, %r10
	0x49, 0x83, 0xfa, 0x20, //0x00000f83 cmpq         $32, %r10
	0x0f, 0x8c, 0xae, 0x17, 0x00, 0x00, //0x00000f87 jl           LBB0_189
	0xb9, 0x20, 0x00, 0x00, 0x00, //0x00000f8d movl         $32, %ecx
	0x31, 0xf6, //0x00000f92 xorl         %esi, %esi
	0x45, 0x31, 0xff, //0x00000f94 xorl         %r15d, %r15d
	0xe9, 0x58, 0x00, 0x00, 0x00, //0x00000f97 jmp          LBB0_181
	0x90, 0x90, 0x90, 0x90, //0x00000f9c .p2align 4, 0x90
	//0x00000fa0 LBB0_185
	0x44, 0x89, 0xf8, //0x00000fa0 movl         %r15d, %eax
	0xf7, 0xd0, //0x00000fa3 notl         %eax
	0x21, 0xd8, //0x00000fa5 andl         %ebx, %eax
	0x8d, 0x14, 0x00, //0x00000fa7 leal         (%rax,%rax), %edx
	0x44, 0x09, 0xfa, //0x00000faa orl          %r15d, %edx
	0x89, 0xd7, //0x00000fad movl         %edx, %edi
	0xf7, 0xd7, //0x00000faf notl         %edi
	0x21, 0xdf, //0x00000fb1 andl         %ebx, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000fb3 andl         $-1431655766, %edi
	0x45, 0x31, 0xff, //0x00000fb9 xorl         %r15d, %r15d
	0x01, 0xc7, //0x00000fbc addl         %eax, %edi
	0x41, 0x0f, 0x92, 0xc7, //0x00000fbe setb         %r15b
	0x01, 0xff, //0x00000fc2 addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x00000fc4 xorl         $1431655765, %edi
	0x21, 0xd7, //0x00000fca andl         %edx, %edi
	0xf7, 0xd7, //0x00000fcc notl         %edi
	0x41, 0x21, 0xfb, //0x00000fce andl         %edi, %r11d
	0x4d, 0x85, 0xdb, //0x00000fd1 testq        %r11, %r11
	0x0f, 0x85, 0x4d, 0x00, 0x00, 0x00, //0x00000fd4 jne          LBB0_184
	//0x00000fda LBB0_186
	0x48, 0x83, 0xc6, 0x20, //0x00000fda addq         $32, %rsi
	0x49, 0x8d, 0x04, 0x0a, //0x00000fde leaq         (%r10,%rcx), %rax
	0x48, 0x83, 0xc0, 0xe0, //0x00000fe2 addq         $-32, %rax
	0x48, 0x83, 0xc1, 0xe0, //0x00000fe6 addq         $-32, %rcx
	0x48, 0x83, 0xf8, 0x3f, //0x00000fea cmpq         $63, %rax
	0x0f, 0x8e, 0x29, 0x17, 0x00, 0x00, //0x00000fee jle          LBB0_187
	//0x00000ff4 LBB0_181
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x31, //0x00000ff4 vmovdqu      (%r9,%rsi), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x00000ffa vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0x7d, 0xd7, 0xd9, //0x00000ffe vpmovmskb    %ymm1, %r11d
	0xc5, 0xfd, 0x74, 0xc7, //0x00001002 vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xd8, //0x00001006 vpmovmskb    %ymm0, %ebx
	0x85, 0xdb, //0x0000100a testl        %ebx, %ebx
	0x0f, 0x85, 0x8e, 0xff, 0xff, 0xff, //0x0000100c jne          LBB0_185
	0x4d, 0x85, 0xff, //0x00001012 testq        %r15, %r15
	0x0f, 0x85, 0x85, 0xff, 0xff, 0xff, //0x00001015 jne          LBB0_185
	0x45, 0x31, 0xff, //0x0000101b xorl         %r15d, %r15d
	0x4d, 0x85, 0xdb, //0x0000101e testq        %r11, %r11
	0x0f, 0x84, 0xb3, 0xff, 0xff, 0xff, //0x00001021 je           LBB0_186
	//0x00001027 LBB0_184
	0x41, 0x0f, 0xbc, 0xc3, //0x00001027 bsfl         %r11d, %eax
	0x4c, 0x01, 0xc0, //0x0000102b addq         %r8, %rax
	0x4c, 0x8d, 0x0c, 0x06, //0x0000102e leaq         (%rsi,%rax), %r9
	0x49, 0x83, 0xc1, 0x01, //0x00001032 addq         $1, %r9
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x00001036 movq         $24(%rsp), %r11
	0x48, 0x8b, 0x7c, 0x24, 0x10, //0x0000103b movq         $16(%rsp), %rdi
	0x4c, 0x8b, 0x7c, 0x24, 0x08, //0x00001040 movq         $8(%rsp), %r15
	0x4d, 0x89, 0x4d, 0x00, //0x00001045 movq         %r9, (%r13)
	0x4d, 0x85, 0xc0, //0x00001049 testq        %r8, %r8
	0x0f, 0x8f, 0x5f, 0x07, 0x00, 0x00, //0x0000104c jg           LBB0_300
	0xe9, 0xd5, 0x2a, 0x00, 0x00, //0x00001052 jmp          LBB0_679
	//0x00001057 LBB0_190
	0x4c, 0x29, 0xcb, //0x00001057 subq         %r9, %rbx
	0x0f, 0x84, 0xbc, 0x2c, 0x00, 0x00, //0x0000105a je           LBB0_717
	0x48, 0x83, 0xfb, 0x40, //0x00001060 cmpq         $64, %rbx
	0x0f, 0x82, 0x53, 0x1a, 0x00, 0x00, //0x00001064 jb           LBB0_506
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x0000106a movq         $-1, %r15
	0x4d, 0x89, 0xca, //0x00001071 movq         %r9, %r10
	0x45, 0x31, 0xdb, //0x00001074 xorl         %r11d, %r11d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001077 .p2align 4, 0x90
	//0x00001080 LBB0_193
	0xc4, 0x81, 0x7e, 0x6f, 0x04, 0x16, //0x00001080 vmovdqu      (%r14,%r10), %ymm0
	0xc4, 0x81, 0x7e, 0x6f, 0x4c, 0x16, 0x20, //0x00001086 vmovdqu      $32(%r14,%r10), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x0000108d vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xfa, //0x00001091 vpmovmskb    %ymm2, %edi
	0xc5, 0xf5, 0x74, 0xd6, //0x00001095 vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xc2, //0x00001099 vpmovmskb    %ymm2, %eax
	0xc5, 0xfd, 0x74, 0xd7, //0x0000109d vpcmpeqb     %ymm7, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xd2, //0x000010a1 vpmovmskb    %ymm2, %edx
	0xc5, 0xf5, 0x74, 0xd7, //0x000010a5 vpcmpeqb     %ymm7, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xca, //0x000010a9 vpmovmskb    %ymm2, %ecx
	0xc5, 0xbd, 0x64, 0xd1, //0x000010ad vpcmpgtb     %ymm1, %ymm8, %ymm2
	0xc4, 0xc1, 0x75, 0x64, 0xc9, //0x000010b1 vpcmpgtb     %ymm9, %ymm1, %ymm1
	0xc5, 0xed, 0xdb, 0xc9, //0x000010b6 vpand        %ymm1, %ymm2, %ymm1
	0xc5, 0xfd, 0xd7, 0xf1, //0x000010ba vpmovmskb    %ymm1, %esi
	0x48, 0xc1, 0xe0, 0x20, //0x000010be shlq         $32, %rax
	0x48, 0x09, 0xc7, //0x000010c2 orq          %rax, %rdi
	0x48, 0xc1, 0xe1, 0x20, //0x000010c5 shlq         $32, %rcx
	0x48, 0xc1, 0xe6, 0x20, //0x000010c9 shlq         $32, %rsi
	0x48, 0x09, 0xca, //0x000010cd orq          %rcx, %rdx
	0x0f, 0x85, 0x49, 0x00, 0x00, 0x00, //0x000010d0 jne          LBB0_204
	0x4d, 0x85, 0xdb, //0x000010d6 testq        %r11, %r11
	0x0f, 0x85, 0x51, 0x00, 0x00, 0x00, //0x000010d9 jne          LBB0_206
	0x45, 0x31, 0xdb, //0x000010df xorl         %r11d, %r11d
	//0x000010e2 LBB0_196
	0xc5, 0xbd, 0x64, 0xc8, //0x000010e2 vpcmpgtb     %ymm0, %ymm8, %ymm1
	0xc4, 0xc1, 0x7d, 0x64, 0xc1, //0x000010e6 vpcmpgtb     %ymm9, %ymm0, %ymm0
	0xc5, 0xf5, 0xdb, 0xc0, //0x000010eb vpand        %ymm0, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xc0, //0x000010ef vpmovmskb    %ymm0, %eax
	0x48, 0x09, 0xc6, //0x000010f3 orq          %rax, %rsi
	0x48, 0x85, 0xff, //0x000010f6 testq        %rdi, %rdi
	0x0f, 0x85, 0x8d, 0x00, 0x00, 0x00, //0x000010f9 jne          LBB0_207
	0x48, 0x85, 0xf6, //0x000010ff testq        %rsi, %rsi
	0x0f, 0x85, 0xac, 0x2a, 0x00, 0x00, //0x00001102 jne          LBB0_699
	0x48, 0x83, 0xc3, 0xc0, //0x00001108 addq         $-64, %rbx
	0x49, 0x83, 0xc2, 0x40, //0x0000110c addq         $64, %r10
	0x48, 0x83, 0xfb, 0x3f, //0x00001110 cmpq         $63, %rbx
	0x0f, 0x87, 0x66, 0xff, 0xff, 0xff, //0x00001114 ja           LBB0_193
	0xe9, 0x81, 0x16, 0x00, 0x00, //0x0000111a jmp          LBB0_199
	//0x0000111f LBB0_204
	0x49, 0x83, 0xff, 0xff, //0x0000111f cmpq         $-1, %r15
	0x0f, 0x85, 0x07, 0x00, 0x00, 0x00, //0x00001123 jne          LBB0_206
	0x4c, 0x0f, 0xbc, 0xfa, //0x00001129 bsfq         %rdx, %r15
	0x4d, 0x01, 0xd7, //0x0000112d addq         %r10, %r15
	//0x00001130 LBB0_206
	0x4c, 0x89, 0x3c, 0x24, //0x00001130 movq         %r15, (%rsp)
	0x4c, 0x89, 0xd8, //0x00001134 movq         %r11, %rax
	0x48, 0xf7, 0xd0, //0x00001137 notq         %rax
	0x48, 0x21, 0xd0, //0x0000113a andq         %rdx, %rax
	0x4d, 0x89, 0xd7, //0x0000113d movq         %r10, %r15
	0x4c, 0x8d, 0x14, 0x00, //0x00001140 leaq         (%rax,%rax), %r10
	0x4d, 0x09, 0xda, //0x00001144 orq          %r11, %r10
	0x4c, 0x89, 0xd1, //0x00001147 movq         %r10, %rcx
	0x48, 0xf7, 0xd1, //0x0000114a notq         %rcx
	0x48, 0x21, 0xd1, //0x0000114d andq         %rdx, %rcx
	0x48, 0xba, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001150 movabsq      $-6148914691236517206, %rdx
	0x48, 0x21, 0xd1, //0x0000115a andq         %rdx, %rcx
	0x45, 0x31, 0xdb, //0x0000115d xorl         %r11d, %r11d
	0x48, 0x01, 0xc1, //0x00001160 addq         %rax, %rcx
	0x41, 0x0f, 0x92, 0xc3, //0x00001163 setb         %r11b
	0x48, 0x01, 0xc9, //0x00001167 addq         %rcx, %rcx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x0000116a movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc1, //0x00001174 xorq         %rax, %rcx
	0x4c, 0x21, 0xd1, //0x00001177 andq         %r10, %rcx
	0x4d, 0x89, 0xfa, //0x0000117a movq         %r15, %r10
	0x4c, 0x8b, 0x3c, 0x24, //0x0000117d movq         (%rsp), %r15
	0x48, 0xf7, 0xd1, //0x00001181 notq         %rcx
	0x48, 0x21, 0xcf, //0x00001184 andq         %rcx, %rdi
	0xe9, 0x56, 0xff, 0xff, 0xff, //0x00001187 jmp          LBB0_196
	//0x0000118c LBB0_207
	0x48, 0x0f, 0xbc, 0xcf, //0x0000118c bsfq         %rdi, %rcx
	0x48, 0x85, 0xf6, //0x00001190 testq        %rsi, %rsi
	0x0f, 0x84, 0x72, 0x01, 0x00, 0x00, //0x00001193 je           LBB0_230
	0x48, 0x0f, 0xbc, 0xd6, //0x00001199 bsfq         %rsi, %rdx
	0x48, 0x39, 0xca, //0x0000119d cmpq         %rcx, %rdx
	0x0f, 0x83, 0x73, 0x01, 0x00, 0x00, //0x000011a0 jae          LBB0_231
	0xe9, 0x8b, 0x2b, 0x00, 0x00, //0x000011a6 jmp          LBB0_209
	//0x000011ab LBB0_210
	0x4c, 0x01, 0xe9, //0x000011ab addq         %r13, %rcx
	0x48, 0x03, 0x4c, 0x24, 0x38, //0x000011ae addq         $56(%rsp), %rcx
	0xc5, 0xf8, 0x77, //0x000011b3 vzeroupper   
	0x49, 0x89, 0xcd, //0x000011b6 movq         %rcx, %r13
	0xe9, 0x3d, 0x04, 0x00, 0x00, //0x000011b9 jmp          LBB0_279
	//0x000011be LBB0_211
	0x4c, 0x29, 0xc9, //0x000011be subq         %r9, %rcx
	0x0f, 0x84, 0x55, 0x2b, 0x00, 0x00, //0x000011c1 je           LBB0_717
	0x48, 0x83, 0xf9, 0x40, //0x000011c7 cmpq         $64, %rcx
	0x0f, 0x82, 0x94, 0x19, 0x00, 0x00, //0x000011cb jb           LBB0_513
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x000011d1 movq         $-1, %r15
	0x4d, 0x89, 0xca, //0x000011d8 movq         %r9, %r10
	0x45, 0x31, 0xdb, //0x000011db xorl         %r11d, %r11d
	0x90, 0x90, //0x000011de .p2align 4, 0x90
	//0x000011e0 LBB0_214
	0xc4, 0x81, 0x7e, 0x6f, 0x04, 0x16, //0x000011e0 vmovdqu      (%r14,%r10), %ymm0
	0xc4, 0x81, 0x7e, 0x6f, 0x4c, 0x16, 0x20, //0x000011e6 vmovdqu      $32(%r14,%r10), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x000011ed vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xfa, //0x000011f1 vpmovmskb    %ymm2, %edi
	0xc5, 0xf5, 0x74, 0xd6, //0x000011f5 vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xc2, //0x000011f9 vpmovmskb    %ymm2, %eax
	0xc5, 0xfd, 0x74, 0xd7, //0x000011fd vpcmpeqb     %ymm7, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xd2, //0x00001201 vpmovmskb    %ymm2, %edx
	0xc5, 0xf5, 0x74, 0xd7, //0x00001205 vpcmpeqb     %ymm7, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xda, //0x00001209 vpmovmskb    %ymm2, %ebx
	0xc5, 0xbd, 0x64, 0xd1, //0x0000120d vpcmpgtb     %ymm1, %ymm8, %ymm2
	0xc4, 0xc1, 0x75, 0x64, 0xc9, //0x00001211 vpcmpgtb     %ymm9, %ymm1, %ymm1
	0xc5, 0xed, 0xdb, 0xc9, //0x00001216 vpand        %ymm1, %ymm2, %ymm1
	0xc5, 0xfd, 0xd7, 0xf1, //0x0000121a vpmovmskb    %ymm1, %esi
	0x48, 0xc1, 0xe0, 0x20, //0x0000121e shlq         $32, %rax
	0x48, 0x09, 0xc7, //0x00001222 orq          %rax, %rdi
	0x48, 0xc1, 0xe3, 0x20, //0x00001225 shlq         $32, %rbx
	0x48, 0xc1, 0xe6, 0x20, //0x00001229 shlq         $32, %rsi
	0x48, 0x09, 0xda, //0x0000122d orq          %rbx, %rdx
	0x0f, 0x85, 0x49, 0x00, 0x00, 0x00, //0x00001230 jne          LBB0_225
	0x4d, 0x85, 0xdb, //0x00001236 testq        %r11, %r11
	0x0f, 0x85, 0x51, 0x00, 0x00, 0x00, //0x00001239 jne          LBB0_227
	0x45, 0x31, 0xdb, //0x0000123f xorl         %r11d, %r11d
	//0x00001242 LBB0_217
	0xc5, 0xbd, 0x64, 0xc8, //0x00001242 vpcmpgtb     %ymm0, %ymm8, %ymm1
	0xc4, 0xc1, 0x7d, 0x64, 0xc1, //0x00001246 vpcmpgtb     %ymm9, %ymm0, %ymm0
	0xc5, 0xf5, 0xdb, 0xc0, //0x0000124b vpand        %ymm0, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xc0, //0x0000124f vpmovmskb    %ymm0, %eax
	0x48, 0x09, 0xc6, //0x00001253 orq          %rax, %rsi
	0x48, 0x85, 0xff, //0x00001256 testq        %rdi, %rdi
	0x0f, 0x85, 0x8d, 0x00, 0x00, 0x00, //0x00001259 jne          LBB0_228
	0x48, 0x85, 0xf6, //0x0000125f testq        %rsi, %rsi
	0x0f, 0x85, 0x4c, 0x29, 0x00, 0x00, //0x00001262 jne          LBB0_699
	0x48, 0x83, 0xc1, 0xc0, //0x00001268 addq         $-64, %rcx
	0x49, 0x83, 0xc2, 0x40, //0x0000126c addq         $64, %r10
	0x48, 0x83, 0xf9, 0x3f, //0x00001270 cmpq         $63, %rcx
	0x0f, 0x87, 0x66, 0xff, 0xff, 0xff, //0x00001274 ja           LBB0_214
	0xe9, 0x4e, 0x16, 0x00, 0x00, //0x0000127a jmp          LBB0_220
	//0x0000127f LBB0_225
	0x49, 0x83, 0xff, 0xff, //0x0000127f cmpq         $-1, %r15
	0x0f, 0x85, 0x07, 0x00, 0x00, 0x00, //0x00001283 jne          LBB0_227
	0x4c, 0x0f, 0xbc, 0xfa, //0x00001289 bsfq         %rdx, %r15
	0x4d, 0x01, 0xd7, //0x0000128d addq         %r10, %r15
	//0x00001290 LBB0_227
	0x4c, 0x89, 0x3c, 0x24, //0x00001290 movq         %r15, (%rsp)
	0x4c, 0x89, 0xd8, //0x00001294 movq         %r11, %rax
	0x48, 0xf7, 0xd0, //0x00001297 notq         %rax
	0x48, 0x21, 0xd0, //0x0000129a andq         %rdx, %rax
	0x4d, 0x89, 0xd7, //0x0000129d movq         %r10, %r15
	0x4c, 0x8d, 0x14, 0x00, //0x000012a0 leaq         (%rax,%rax), %r10
	0x4d, 0x09, 0xda, //0x000012a4 orq          %r11, %r10
	0x4c, 0x89, 0xd3, //0x000012a7 movq         %r10, %rbx
	0x48, 0xf7, 0xd3, //0x000012aa notq         %rbx
	0x48, 0x21, 0xd3, //0x000012ad andq         %rdx, %rbx
	0x48, 0xba, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x000012b0 movabsq      $-6148914691236517206, %rdx
	0x48, 0x21, 0xd3, //0x000012ba andq         %rdx, %rbx
	0x45, 0x31, 0xdb, //0x000012bd xorl         %r11d, %r11d
	0x48, 0x01, 0xc3, //0x000012c0 addq         %rax, %rbx
	0x41, 0x0f, 0x92, 0xc3, //0x000012c3 setb         %r11b
	0x48, 0x01, 0xdb, //0x000012c7 addq         %rbx, %rbx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000012ca movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc3, //0x000012d4 xorq         %rax, %rbx
	0x4c, 0x21, 0xd3, //0x000012d7 andq         %r10, %rbx
	0x4d, 0x89, 0xfa, //0x000012da movq         %r15, %r10
	0x4c, 0x8b, 0x3c, 0x24, //0x000012dd movq         (%rsp), %r15
	0x48, 0xf7, 0xd3, //0x000012e1 notq         %rbx
	0x48, 0x21, 0xdf, //0x000012e4 andq         %rbx, %rdi
	0xe9, 0x56, 0xff, 0xff, 0xff, //0x000012e7 jmp          LBB0_217
	//0x000012ec LBB0_228
	0x48, 0x0f, 0xbc, 0xcf, //0x000012ec bsfq         %rdi, %rcx
	0x48, 0x85, 0xf6, //0x000012f0 testq        %rsi, %rsi
	0x0f, 0x84, 0x7e, 0x04, 0x00, 0x00, //0x000012f3 je           LBB0_296
	0x48, 0x0f, 0xbc, 0xd6, //0x000012f9 bsfq         %rsi, %rdx
	0x48, 0x39, 0xca, //0x000012fd cmpq         %rcx, %rdx
	0x0f, 0x83, 0x7f, 0x04, 0x00, 0x00, //0x00001300 jae          LBB0_297
	0xe9, 0x2b, 0x2a, 0x00, 0x00, //0x00001306 jmp          LBB0_209
	//0x0000130b LBB0_230
	0xba, 0x40, 0x00, 0x00, 0x00, //0x0000130b movl         $64, %edx
	0x48, 0x39, 0xca, //0x00001310 cmpq         %rcx, %rdx
	0x0f, 0x82, 0x1d, 0x2a, 0x00, 0x00, //0x00001313 jb           LBB0_209
	//0x00001319 LBB0_231
	0x49, 0x01, 0xca, //0x00001319 addq         %rcx, %r10
	0x49, 0x83, 0xc2, 0x01, //0x0000131c addq         $1, %r10
	//0x00001320 LBB0_232
	0x4d, 0x85, 0xd2, //0x00001320 testq        %r10, %r10
	0x0f, 0x88, 0x1d, 0x28, 0x00, 0x00, //0x00001323 js           LBB0_680
	0x4d, 0x89, 0x55, 0x00, //0x00001329 movq         %r10, (%r13)
	0x4d, 0x85, 0xc9, //0x0000132d testq        %r9, %r9
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x00001330 movq         $24(%rsp), %r11
	0x48, 0x8b, 0x7c, 0x24, 0x10, //0x00001335 movq         $16(%rsp), %rdi
	0x4c, 0x8b, 0x7c, 0x24, 0x08, //0x0000133a movq         $8(%rsp), %r15
	0x0f, 0x8f, 0x5b, 0xf0, 0xff, 0xff, //0x0000133f jg           LBB0_3
	0xe9, 0xef, 0x27, 0x00, 0x00, //0x00001345 jmp          LBB0_463
	//0x0000134a LBB0_234
	0x41, 0xf6, 0xc3, 0x40, //0x0000134a testb        $64, %r11b
	0x0f, 0x85, 0x82, 0x04, 0x00, 0x00, //0x0000134e jne          LBB0_302
	0x4d, 0x8b, 0x4d, 0x00, //0x00001354 movq         (%r13), %r9
	0x4c, 0x8b, 0x7f, 0x08, //0x00001358 movq         $8(%rdi), %r15
	0x4d, 0x89, 0xf8, //0x0000135c movq         %r15, %r8
	0x41, 0xf6, 0xc3, 0x20, //0x0000135f testb        $32, %r11b
	0x0f, 0x85, 0x97, 0x0c, 0x00, 0x00, //0x00001363 jne          LBB0_385
	0x4d, 0x29, 0xc8, //0x00001369 subq         %r9, %r8
	0x0f, 0x84, 0xf1, 0x29, 0x00, 0x00, //0x0000136c je           LBB0_718
	0x49, 0x83, 0xf8, 0x40, //0x00001372 cmpq         $64, %r8
	0x0f, 0x82, 0x48, 0x18, 0x00, 0x00, //0x00001376 jb           LBB0_516
	0x4c, 0x89, 0xce, //0x0000137c movq         %r9, %rsi
	0x48, 0xf7, 0xd6, //0x0000137f notq         %rsi
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00001382 movq         $-1, %r11
	0x4c, 0x89, 0xcf, //0x00001389 movq         %r9, %rdi
	0x45, 0x31, 0xd2, //0x0000138c xorl         %r10d, %r10d
	0x90, //0x0000138f .p2align 4, 0x90
	//0x00001390 LBB0_239
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x3e, //0x00001390 vmovdqu      (%r14,%rdi), %ymm0
	0x48, 0x89, 0xf9, //0x00001396 movq         %rdi, %rcx
	0xc4, 0xc1, 0x7e, 0x6f, 0x4c, 0x3e, 0x20, //0x00001399 vmovdqu      $32(%r14,%rdi), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x000013a0 vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xfa, //0x000013a4 vpmovmskb    %ymm2, %edi
	0xc5, 0xf5, 0x74, 0xd6, //0x000013a8 vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xc2, //0x000013ac vpmovmskb    %ymm2, %eax
	0xc5, 0xfd, 0x74, 0xc7, //0x000013b0 vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xd0, //0x000013b4 vpmovmskb    %ymm0, %edx
	0xc5, 0xf5, 0x74, 0xc7, //0x000013b8 vpcmpeqb     %ymm7, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xd8, //0x000013bc vpmovmskb    %ymm0, %ebx
	0x48, 0xc1, 0xe0, 0x20, //0x000013c0 shlq         $32, %rax
	0x48, 0x09, 0xc7, //0x000013c4 orq          %rax, %rdi
	0x48, 0xc1, 0xe3, 0x20, //0x000013c7 shlq         $32, %rbx
	0x48, 0x09, 0xda, //0x000013cb orq          %rbx, %rdx
	0x0f, 0x85, 0x33, 0x00, 0x00, 0x00, //0x000013ce jne          LBB0_248
	0x4d, 0x85, 0xd2, //0x000013d4 testq        %r10, %r10
	0x0f, 0x85, 0x48, 0x00, 0x00, 0x00, //0x000013d7 jne          LBB0_250
	0x45, 0x31, 0xd2, //0x000013dd xorl         %r10d, %r10d
	0x48, 0x85, 0xff, //0x000013e0 testq        %rdi, %rdi
	0x0f, 0x85, 0x96, 0x00, 0x00, 0x00, //0x000013e3 jne          LBB0_252
	//0x000013e9 LBB0_242
	0x49, 0x83, 0xc0, 0xc0, //0x000013e9 addq         $-64, %r8
	0x48, 0x83, 0xc6, 0xc0, //0x000013ed addq         $-64, %rsi
	0x48, 0x89, 0xcf, //0x000013f1 movq         %rcx, %rdi
	0x48, 0x83, 0xc7, 0x40, //0x000013f4 addq         $64, %rdi
	0x49, 0x83, 0xf8, 0x3f, //0x000013f8 cmpq         $63, %r8
	0x0f, 0x87, 0x8e, 0xff, 0xff, 0xff, //0x000013fc ja           LBB0_239
	0xe9, 0x99, 0x15, 0x00, 0x00, //0x00001402 jmp          LBB0_243
	//0x00001407 LBB0_248
	0x4c, 0x89, 0x1c, 0x24, //0x00001407 movq         %r11, (%rsp)
	0x49, 0x83, 0xfb, 0xff, //0x0000140b cmpq         $-1, %r11
	0x0f, 0x85, 0x14, 0x00, 0x00, 0x00, //0x0000140f jne          LBB0_251
	0x48, 0x0f, 0xbc, 0xc2, //0x00001415 bsfq         %rdx, %rax
	0x48, 0x01, 0xc8, //0x00001419 addq         %rcx, %rax
	0x48, 0x89, 0x04, 0x24, //0x0000141c movq         %rax, (%rsp)
	0xe9, 0x04, 0x00, 0x00, 0x00, //0x00001420 jmp          LBB0_251
	//0x00001425 LBB0_250
	0x4c, 0x89, 0x1c, 0x24, //0x00001425 movq         %r11, (%rsp)
	//0x00001429 LBB0_251
	0x4c, 0x89, 0xd0, //0x00001429 movq         %r10, %rax
	0x48, 0xf7, 0xd0, //0x0000142c notq         %rax
	0x48, 0x21, 0xd0, //0x0000142f andq         %rdx, %rax
	0x4c, 0x8d, 0x1c, 0x00, //0x00001432 leaq         (%rax,%rax), %r11
	0x4d, 0x09, 0xd3, //0x00001436 orq          %r10, %r11
	0x4c, 0x89, 0xdb, //0x00001439 movq         %r11, %rbx
	0x48, 0xf7, 0xd3, //0x0000143c notq         %rbx
	0x48, 0x21, 0xd3, //0x0000143f andq         %rdx, %rbx
	0x48, 0xba, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001442 movabsq      $-6148914691236517206, %rdx
	0x48, 0x21, 0xd3, //0x0000144c andq         %rdx, %rbx
	0x45, 0x31, 0xd2, //0x0000144f xorl         %r10d, %r10d
	0x48, 0x01, 0xc3, //0x00001452 addq         %rax, %rbx
	0x41, 0x0f, 0x92, 0xc2, //0x00001455 setb         %r10b
	0x48, 0x01, 0xdb, //0x00001459 addq         %rbx, %rbx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x0000145c movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc3, //0x00001466 xorq         %rax, %rbx
	0x4c, 0x21, 0xdb, //0x00001469 andq         %r11, %rbx
	0x48, 0xf7, 0xd3, //0x0000146c notq         %rbx
	0x48, 0x21, 0xdf, //0x0000146f andq         %rbx, %rdi
	0x4c, 0x8b, 0x1c, 0x24, //0x00001472 movq         (%rsp), %r11
	0x48, 0x85, 0xff, //0x00001476 testq        %rdi, %rdi
	0x0f, 0x84, 0x6a, 0xff, 0xff, 0xff, //0x00001479 je           LBB0_242
	//0x0000147f LBB0_252
	0xc5, 0x7d, 0x7f, 0xea, //0x0000147f vmovdqa      %ymm13, %ymm2
	0x48, 0x0f, 0xbc, 0xcf, //0x00001483 bsfq         %rdi, %rcx
	0x48, 0x29, 0xf1, //0x00001487 subq         %rsi, %rcx
	0xe9, 0x5f, 0x10, 0x00, 0x00, //0x0000148a jmp          LBB0_461
	//0x0000148f LBB0_253
	0x4c, 0x8b, 0x57, 0x08, //0x0000148f movq         $8(%rdi), %r10
	0x49, 0x8b, 0x4d, 0x00, //0x00001493 movq         (%r13), %rcx
	0x41, 0xf6, 0xc3, 0x40, //0x00001497 testb        $64, %r11b
	0x48, 0x89, 0x0c, 0x24, //0x0000149b movq         %rcx, (%rsp)
	0x0f, 0x85, 0x0b, 0x04, 0x00, 0x00, //0x0000149f jne          LBB0_313
	0x49, 0x29, 0xca, //0x000014a5 subq         %rcx, %r10
	0x0f, 0x84, 0x3a, 0x28, 0x00, 0x00, //0x000014a8 je           LBB0_704
	0x49, 0x8d, 0x04, 0x0e, //0x000014ae leaq         (%r14,%rcx), %rax
	0x48, 0x89, 0x44, 0x24, 0x38, //0x000014b2 movq         %rax, $56(%rsp)
	0x80, 0x38, 0x30, //0x000014b7 cmpb         $48, (%rax)
	0x0f, 0x85, 0xab, 0x0c, 0x00, 0x00, //0x000014ba jne          LBB0_406
	0x41, 0xb9, 0x01, 0x00, 0x00, 0x00, //0x000014c0 movl         $1, %r9d
	0x49, 0x83, 0xfa, 0x01, //0x000014c6 cmpq         $1, %r10
	0x0f, 0x85, 0x6c, 0x0c, 0x00, 0x00, //0x000014ca jne          LBB0_404
	//0x000014d0 LBB0_257
	0x48, 0x8b, 0x0c, 0x24, //0x000014d0 movq         (%rsp), %rcx
	0xe9, 0xb0, 0x14, 0x00, 0x00, //0x000014d4 jmp          LBB0_498
	//0x000014d9 LBB0_258
	0x41, 0xf6, 0xc3, 0x40, //0x000014d9 testb        $64, %r11b
	0x0f, 0x85, 0xe2, 0x04, 0x00, 0x00, //0x000014dd jne          LBB0_329
	0x49, 0x8b, 0x0f, //0x000014e3 movq         (%r15), %rcx
	0x48, 0x81, 0xf9, 0xff, 0x0f, 0x00, 0x00, //0x000014e6 cmpq         $4095, %rcx
	0x0f, 0x8f, 0x2c, 0x26, 0x00, 0x00, //0x000014ed jg           LBB0_705
	0x48, 0x8d, 0x41, 0x01, //0x000014f3 leaq         $1(%rcx), %rax
	0x49, 0x89, 0x07, //0x000014f7 movq         %rax, (%r15)
	0x49, 0xc7, 0x44, 0xcf, 0x08, 0x05, 0x00, 0x00, 0x00, //0x000014fa movq         $5, $8(%r15,%rcx,8)
	0xe9, 0x98, 0xee, 0xff, 0xff, //0x00001503 jmp          LBB0_3
	//0x00001508 LBB0_261
	0x49, 0x8b, 0x4d, 0x00, //0x00001508 movq         (%r13), %rcx
	0x48, 0x8b, 0x57, 0x08, //0x0000150c movq         $8(%rdi), %rdx
	0x48, 0x8d, 0x42, 0xfc, //0x00001510 leaq         $-4(%rdx), %rax
	0x48, 0x39, 0xc1, //0x00001514 cmpq         %rax, %rcx
	0x0f, 0x87, 0x8e, 0x26, 0x00, 0x00, //0x00001517 ja           LBB0_686
	0x41, 0x8b, 0x14, 0x0e, //0x0000151d movl         (%r14,%rcx), %edx
	0x81, 0xfa, 0x61, 0x6c, 0x73, 0x65, //0x00001521 cmpl         $1702063201, %edx
	0x0f, 0x85, 0xac, 0x26, 0x00, 0x00, //0x00001527 jne          LBB0_687
	0x48, 0x8d, 0x41, 0x04, //0x0000152d leaq         $4(%rcx), %rax
	0x49, 0x89, 0x45, 0x00, //0x00001531 movq         %rax, (%r13)
	0x48, 0x85, 0xc9, //0x00001535 testq        %rcx, %rcx
	0x0f, 0x8f, 0x62, 0xee, 0xff, 0xff, //0x00001538 jg           LBB0_3
	0xe9, 0x98, 0x27, 0x00, 0x00, //0x0000153e jmp          LBB0_264
	//0x00001543 LBB0_265
	0x49, 0x8b, 0x4d, 0x00, //0x00001543 movq         (%r13), %rcx
	0x48, 0x8b, 0x57, 0x08, //0x00001547 movq         $8(%rdi), %rdx
	0x48, 0x8d, 0x42, 0xfd, //0x0000154b leaq         $-3(%rdx), %rax
	0x48, 0x39, 0xc1, //0x0000154f cmpq         %rax, %rcx
	0x0f, 0x87, 0x53, 0x26, 0x00, 0x00, //0x00001552 ja           LBB0_686
	0x48, 0x8d, 0x41, 0xff, //0x00001558 leaq         $-1(%rcx), %rax
	0x41, 0x81, 0x7c, 0x0e, 0xff, 0x6e, 0x75, 0x6c, 0x6c, //0x0000155c cmpl         $1819047278, $-1(%r14,%rcx)
	0x0f, 0x84, 0x2d, 0x00, 0x00, 0x00, //0x00001565 je           LBB0_273
	0xe9, 0xc3, 0x26, 0x00, 0x00, //0x0000156b jmp          LBB0_267
	//0x00001570 LBB0_271
	0x49, 0x8b, 0x4d, 0x00, //0x00001570 movq         (%r13), %rcx
	0x48, 0x8b, 0x57, 0x08, //0x00001574 movq         $8(%rdi), %rdx
	0x48, 0x8d, 0x42, 0xfd, //0x00001578 leaq         $-3(%rdx), %rax
	0x48, 0x39, 0xc1, //0x0000157c cmpq         %rax, %rcx
	0x0f, 0x87, 0x26, 0x26, 0x00, 0x00, //0x0000157f ja           LBB0_686
	0x48, 0x8d, 0x41, 0xff, //0x00001585 leaq         $-1(%rcx), %rax
	0x41, 0x81, 0x7c, 0x0e, 0xff, 0x74, 0x72, 0x75, 0x65, //0x00001589 cmpl         $1702195828, $-1(%r14,%rcx)
	0x0f, 0x85, 0xe6, 0x26, 0x00, 0x00, //0x00001592 jne          LBB0_692
	//0x00001598 LBB0_273
	0x48, 0x89, 0x04, 0x24, //0x00001598 movq         %rax, (%rsp)
	0x48, 0x8d, 0x41, 0x03, //0x0000159c leaq         $3(%rcx), %rax
	0x49, 0x89, 0x45, 0x00, //0x000015a0 movq         %rax, (%r13)
	0x48, 0x85, 0xc9, //0x000015a4 testq        %rcx, %rcx
	0x0f, 0x8f, 0xf3, 0xed, 0xff, 0xff, //0x000015a7 jg           LBB0_3
	0xe9, 0xe3, 0x25, 0x00, 0x00, //0x000015ad jmp          LBB0_711
	//0x000015b2 LBB0_274
	0x41, 0xf6, 0xc3, 0x40, //0x000015b2 testb        $64, %r11b
	0x0f, 0x85, 0xfb, 0x06, 0x00, 0x00, //0x000015b6 jne          LBB0_355
	0x49, 0x8b, 0x0f, //0x000015bc movq         (%r15), %rcx
	0x48, 0x81, 0xf9, 0xff, 0x0f, 0x00, 0x00, //0x000015bf cmpq         $4095, %rcx
	0x0f, 0x8f, 0x53, 0x25, 0x00, 0x00, //0x000015c6 jg           LBB0_705
	0x48, 0x8d, 0x41, 0x01, //0x000015cc leaq         $1(%rcx), %rax
	0x49, 0x89, 0x07, //0x000015d0 movq         %rax, (%r15)
	0x49, 0xc7, 0x44, 0xcf, 0x08, 0x06, 0x00, 0x00, 0x00, //0x000015d3 movq         $6, $8(%r15,%rcx,8)
	0xe9, 0xbf, 0xed, 0xff, 0xff, //0x000015dc jmp          LBB0_3
	//0x000015e1 LBB0_277
	0x89, 0xc8, //0x000015e1 movl         %ecx, %eax
	0x49, 0x01, 0xc5, //0x000015e3 addq         %rax, %r13
	0x4d, 0x01, 0xfd, //0x000015e6 addq         %r15, %r13
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x000015e9 movq         $24(%rsp), %r11
	0x48, 0x8b, 0x7c, 0x24, 0x10, //0x000015ee movq         $16(%rsp), %rdi
	0xe9, 0x03, 0x00, 0x00, 0x00, //0x000015f3 jmp          LBB0_279
	//0x000015f8 LBB0_278
	0x49, 0x01, 0xcd, //0x000015f8 addq         %rcx, %r13
	//0x000015fb LBB0_279
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x000015fb movq         $-1, %rcx
	0x48, 0x8b, 0x54, 0x24, 0x28, //0x00001602 movq         $40(%rsp), %rdx
	0x48, 0x85, 0xd2, //0x00001607 testq        %rdx, %rdx
	0x4c, 0x8b, 0x7c, 0x24, 0x08, //0x0000160a movq         $8(%rsp), %r15
	0x0f, 0x84, 0x66, 0x25, 0x00, 0x00, //0x0000160f je           LBB0_684
	0x4d, 0x85, 0xd2, //0x00001615 testq        %r10, %r10
	0xc5, 0xfe, 0x6f, 0x1d, 0xa0, 0xeb, 0xff, 0xff, //0x00001618 vmovdqu      $-5216(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0x0f, 0x84, 0x55, 0x25, 0x00, 0x00, //0x00001620 je           LBB0_684
	0x4d, 0x85, 0xc9, //0x00001626 testq        %r9, %r9
	0x0f, 0x84, 0x4c, 0x25, 0x00, 0x00, //0x00001629 je           LBB0_684
	0x4c, 0x2b, 0x6c, 0x24, 0x38, //0x0000162f subq         $56(%rsp), %r13
	0x49, 0x8d, 0x4d, 0xff, //0x00001634 leaq         $-1(%r13), %rcx
	0x48, 0x39, 0xca, //0x00001638 cmpq         %rcx, %rdx
	0x0f, 0x84, 0x80, 0x00, 0x00, 0x00, //0x0000163b je           LBB0_288
	0x49, 0x39, 0xca, //0x00001641 cmpq         %rcx, %r10
	0x0f, 0x84, 0x77, 0x00, 0x00, 0x00, //0x00001644 je           LBB0_288
	0x49, 0x39, 0xc9, //0x0000164a cmpq         %rcx, %r9
	0x0f, 0x84, 0x6e, 0x00, 0x00, 0x00, //0x0000164d je           LBB0_288
	0x4d, 0x85, 0xd2, //0x00001653 testq        %r10, %r10
	0xc5, 0xfe, 0x6f, 0x2d, 0x62, 0xea, 0xff, 0xff, //0x00001656 vmovdqu      $-5534(%rip), %ymm5  /* LCPI0_1+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0x7a, 0xea, 0xff, 0xff, //0x0000165e vmovdqu      $-5510(%rip), %ymm6  /* LCPI0_2+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0x92, 0xea, 0xff, 0xff, //0x00001666 vmovdqu      $-5486(%rip), %ymm7  /* LCPI0_3+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0xaa, 0xea, 0xff, 0xff, //0x0000166e vmovdqu      $-5462(%rip), %ymm8  /* LCPI0_4+0(%rip) */
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00001676 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0x7e, 0x6f, 0x15, 0x7d, 0xeb, 0xff, 0xff, //0x0000167b vmovdqu      $-5251(%rip), %ymm10  /* LCPI0_14+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x05, 0x95, 0xeb, 0xff, 0xff, //0x00001683 vmovdqu      $-5227(%rip), %ymm0  /* LCPI0_15+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x3d, 0xad, 0xeb, 0xff, 0xff, //0x0000168b vmovdqu      $-5203(%rip), %ymm15  /* LCPI0_16+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0x45, 0xeb, 0xff, 0xff, //0x00001693 vmovdqu      $-5307(%rip), %ymm14  /* LCPI0_10+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0xfd, 0xeb, 0xff, 0xff, //0x0000169b vmovdqu      $-5123(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0x0f, 0x8e, 0x9b, 0x00, 0x00, 0x00, //0x000016a3 jle          LBB0_293
	0x49, 0x8d, 0x42, 0xff, //0x000016a9 leaq         $-1(%r10), %rax
	0x49, 0x39, 0xc1, //0x000016ad cmpq         %rax, %r9
	0x0f, 0x84, 0x8e, 0x00, 0x00, 0x00, //0x000016b0 je           LBB0_293
	0x49, 0xf7, 0xd2, //0x000016b6 notq         %r10
	0x4d, 0x89, 0xd5, //0x000016b9 movq         %r10, %r13
	0xe9, 0x50, 0x00, 0x00, 0x00, //0x000016bc jmp          LBB0_289
	//0x000016c1 LBB0_288
	0x49, 0xf7, 0xdd, //0x000016c1 negq         %r13
	0xc5, 0xfe, 0x6f, 0x2d, 0xf4, 0xe9, 0xff, 0xff, //0x000016c4 vmovdqu      $-5644(%rip), %ymm5  /* LCPI0_1+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0x0c, 0xea, 0xff, 0xff, //0x000016cc vmovdqu      $-5620(%rip), %ymm6  /* LCPI0_2+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0x24, 0xea, 0xff, 0xff, //0x000016d4 vmovdqu      $-5596(%rip), %ymm7  /* LCPI0_3+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0x3c, 0xea, 0xff, 0xff, //0x000016dc vmovdqu      $-5572(%rip), %ymm8  /* LCPI0_4+0(%rip) */
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x000016e4 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0x7e, 0x6f, 0x15, 0x0f, 0xeb, 0xff, 0xff, //0x000016e9 vmovdqu      $-5361(%rip), %ymm10  /* LCPI0_14+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x05, 0x27, 0xeb, 0xff, 0xff, //0x000016f1 vmovdqu      $-5337(%rip), %ymm0  /* LCPI0_15+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x3d, 0x3f, 0xeb, 0xff, 0xff, //0x000016f9 vmovdqu      $-5313(%rip), %ymm15  /* LCPI0_16+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0xd7, 0xea, 0xff, 0xff, //0x00001701 vmovdqu      $-5417(%rip), %ymm14  /* LCPI0_10+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0x8f, 0xeb, 0xff, 0xff, //0x00001709 vmovdqu      $-5233(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	//0x00001711 LBB0_289
	0xc5, 0x7e, 0x6f, 0x1d, 0x27, 0xea, 0xff, 0xff, //0x00001711 vmovdqu      $-5593(%rip), %ymm11  /* LCPI0_5+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0x3f, 0xea, 0xff, 0xff, //0x00001719 vmovdqu      $-5569(%rip), %ymm12  /* LCPI0_6+0(%rip) */
	//0x00001721 LBB0_290
	0x48, 0x8b, 0x5c, 0x24, 0x20, //0x00001721 movq         $32(%rsp), %rbx
	//0x00001726 LBB0_291
	0x4d, 0x85, 0xed, //0x00001726 testq        %r13, %r13
	0x0f, 0x88, 0x49, 0x24, 0x00, 0x00, //0x00001729 js           LBB0_683
	0x48, 0x8b, 0x44, 0x24, 0x30, //0x0000172f movq         $48(%rsp), %rax
	0x48, 0x8b, 0x08, //0x00001734 movq         (%rax), %rcx
	0x48, 0x83, 0xc1, 0xff, //0x00001737 addq         $-1, %rcx
	0xc5, 0x7d, 0x6f, 0xe8, //0x0000173b vmovdqa      %ymm0, %ymm13
	0xe9, 0x74, 0xec, 0xff, 0xff, //0x0000173f jmp          LBB0_2
	//0x00001744 LBB0_293
	0x48, 0x89, 0xd0, //0x00001744 movq         %rdx, %rax
	0x4c, 0x09, 0xc8, //0x00001747 orq          %r9, %rax
	0x0f, 0x99, 0xc1, //0x0000174a setns        %cl
	0xc5, 0x7e, 0x6f, 0x1d, 0xeb, 0xe9, 0xff, 0xff, //0x0000174d vmovdqu      $-5653(%rip), %ymm11  /* LCPI0_5+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0x03, 0xea, 0xff, 0xff, //0x00001755 vmovdqu      $-5629(%rip), %ymm12  /* LCPI0_6+0(%rip) */
	0x0f, 0x88, 0x84, 0x08, 0x00, 0x00, //0x0000175d js           LBB0_384
	0x4c, 0x39, 0xca, //0x00001763 cmpq         %r9, %rdx
	0x0f, 0x8c, 0x7b, 0x08, 0x00, 0x00, //0x00001766 jl           LBB0_384
	0x48, 0xf7, 0xd2, //0x0000176c notq         %rdx
	0x49, 0x89, 0xd5, //0x0000176f movq         %rdx, %r13
	0xe9, 0xaa, 0xff, 0xff, 0xff, //0x00001772 jmp          LBB0_290
	//0x00001777 LBB0_296
	0xba, 0x40, 0x00, 0x00, 0x00, //0x00001777 movl         $64, %edx
	0x48, 0x39, 0xca, //0x0000177c cmpq         %rcx, %rdx
	0x0f, 0x82, 0xb1, 0x25, 0x00, 0x00, //0x0000177f jb           LBB0_209
	//0x00001785 LBB0_297
	0x49, 0x01, 0xca, //0x00001785 addq         %rcx, %r10
	0x49, 0x83, 0xc2, 0x01, //0x00001788 addq         $1, %r10
	//0x0000178c LBB0_298
	0x4d, 0x85, 0xd2, //0x0000178c testq        %r10, %r10
	0x0f, 0x88, 0xb1, 0x23, 0x00, 0x00, //0x0000178f js           LBB0_680
	0x4d, 0x89, 0x55, 0x00, //0x00001795 movq         %r10, (%r13)
	0x4d, 0x85, 0xc9, //0x00001799 testq        %r9, %r9
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x0000179c movq         $24(%rsp), %r11
	0x48, 0x8b, 0x7c, 0x24, 0x10, //0x000017a1 movq         $16(%rsp), %rdi
	0x4c, 0x8b, 0x7c, 0x24, 0x08, //0x000017a6 movq         $8(%rsp), %r15
	0x0f, 0x8e, 0x88, 0x23, 0x00, 0x00, //0x000017ab jle          LBB0_463
	//0x000017b1 LBB0_300
	0x49, 0x8b, 0x0f, //0x000017b1 movq         (%r15), %rcx
	0x48, 0x81, 0xf9, 0xff, 0x0f, 0x00, 0x00, //0x000017b4 cmpq         $4095, %rcx
	0x0f, 0x8f, 0x5e, 0x23, 0x00, 0x00, //0x000017bb jg           LBB0_705
	0x48, 0x8d, 0x41, 0x01, //0x000017c1 leaq         $1(%rcx), %rax
	0x49, 0x89, 0x07, //0x000017c5 movq         %rax, (%r15)
	0x49, 0xc7, 0x44, 0xcf, 0x08, 0x04, 0x00, 0x00, 0x00, //0x000017c8 movq         $4, $8(%r15,%rcx,8)
	0xe9, 0xca, 0xeb, 0xff, 0xff, //0x000017d1 jmp          LBB0_3
	//0x000017d6 LBB0_302
	0x4c, 0x8b, 0x57, 0x08, //0x000017d6 movq         $8(%rdi), %r10
	0x4d, 0x8b, 0x45, 0x00, //0x000017da movq         (%r13), %r8
	0x4f, 0x8d, 0x0c, 0x06, //0x000017de leaq         (%r14,%r8), %r9
	0x4d, 0x29, 0xc2, //0x000017e2 subq         %r8, %r10
	0x49, 0x83, 0xfa, 0x20, //0x000017e5 cmpq         $32, %r10
	0x0f, 0x8c, 0xa4, 0x00, 0x00, 0x00, //0x000017e9 jl           LBB0_312
	0xb9, 0x20, 0x00, 0x00, 0x00, //0x000017ef movl         $32, %ecx
	0x31, 0xf6, //0x000017f4 xorl         %esi, %esi
	0x45, 0x31, 0xff, //0x000017f6 xorl         %r15d, %r15d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000017f9 .p2align 4, 0x90
	//0x00001800 LBB0_304
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x31, //0x00001800 vmovdqu      (%r9,%rsi), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x00001806 vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0x7d, 0xd7, 0xd9, //0x0000180a vpmovmskb    %ymm1, %r11d
	0xc5, 0xfd, 0x74, 0xc7, //0x0000180e vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xd8, //0x00001812 vpmovmskb    %ymm0, %ebx
	0x85, 0xdb, //0x00001816 testl        %ebx, %ebx
	0x0f, 0x85, 0x12, 0x00, 0x00, 0x00, //0x00001818 jne          LBB0_307
	0x4d, 0x85, 0xff, //0x0000181e testq        %r15, %r15
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00001821 jne          LBB0_307
	0x45, 0x31, 0xff, //0x00001827 xorl         %r15d, %r15d
	0xe9, 0x32, 0x00, 0x00, 0x00, //0x0000182a jmp          LBB0_308
	0x90, //0x0000182f .p2align 4, 0x90
	//0x00001830 LBB0_307
	0x44, 0x89, 0xf8, //0x00001830 movl         %r15d, %eax
	0xf7, 0xd0, //0x00001833 notl         %eax
	0x21, 0xd8, //0x00001835 andl         %ebx, %eax
	0x8d, 0x14, 0x00, //0x00001837 leal         (%rax,%rax), %edx
	0x44, 0x09, 0xfa, //0x0000183a orl          %r15d, %edx
	0x89, 0xd7, //0x0000183d movl         %edx, %edi
	0xf7, 0xd7, //0x0000183f notl         %edi
	0x21, 0xdf, //0x00001841 andl         %ebx, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001843 andl         $-1431655766, %edi
	0x45, 0x31, 0xff, //0x00001849 xorl         %r15d, %r15d
	0x01, 0xc7, //0x0000184c addl         %eax, %edi
	0x41, 0x0f, 0x92, 0xc7, //0x0000184e setb         %r15b
	0x01, 0xff, //0x00001852 addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x00001854 xorl         $1431655765, %edi
	0x21, 0xd7, //0x0000185a andl         %edx, %edi
	0xf7, 0xd7, //0x0000185c notl         %edi
	0x41, 0x21, 0xfb, //0x0000185e andl         %edi, %r11d
	//0x00001861 LBB0_308
	0x4d, 0x85, 0xdb, //0x00001861 testq        %r11, %r11
	0x0f, 0x85, 0xff, 0xea, 0xff, 0xff, //0x00001864 jne          LBB0_153
	0x48, 0x83, 0xc6, 0x20, //0x0000186a addq         $32, %rsi
	0x49, 0x8d, 0x04, 0x0a, //0x0000186e leaq         (%r10,%rcx), %rax
	0x48, 0x83, 0xc0, 0xe0, //0x00001872 addq         $-32, %rax
	0x48, 0x83, 0xc1, 0xe0, //0x00001876 addq         $-32, %rcx
	0x48, 0x83, 0xf8, 0x3f, //0x0000187a cmpq         $63, %rax
	0x0f, 0x8f, 0x7c, 0xff, 0xff, 0xff, //0x0000187e jg           LBB0_304
	0x4d, 0x85, 0xff, //0x00001884 testq        %r15, %r15
	0x0f, 0x85, 0xd7, 0x1d, 0x00, 0x00, //0x00001887 jne          LBB0_622
	0x49, 0x01, 0xf1, //0x0000188d addq         %rsi, %r9
	0x49, 0x29, 0xf2, //0x00001890 subq         %rsi, %r10
	//0x00001893 LBB0_312
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x00001893 movq         $24(%rsp), %r11
	0x48, 0x8b, 0x7c, 0x24, 0x10, //0x00001898 movq         $16(%rsp), %rdi
	0x4c, 0x8b, 0x7c, 0x24, 0x08, //0x0000189d movq         $8(%rsp), %r15
	0x4d, 0x85, 0xd2, //0x000018a2 testq        %r10, %r10
	0x0f, 0x8f, 0x32, 0x1e, 0x00, 0x00, //0x000018a5 jg           LBB0_626
	0xe9, 0xe5, 0x22, 0x00, 0x00, //0x000018ab jmp          LBB0_711
	//0x000018b0 LBB0_313
	0x4c, 0x89, 0xd6, //0x000018b0 movq         %r10, %rsi
	0x48, 0x29, 0xce, //0x000018b3 subq         %rcx, %rsi
	0x48, 0x83, 0xfe, 0x20, //0x000018b6 cmpq         $32, %rsi
	0x0f, 0x82, 0xc2, 0x12, 0x00, 0x00, //0x000018ba jb           LBB0_514
	0x48, 0x89, 0xca, //0x000018c0 movq         %rcx, %rdx
	0x48, 0xf7, 0xda, //0x000018c3 negq         %rdx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000018c6 .p2align 4, 0x90
	//0x000018d0 LBB0_315
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x0e, //0x000018d0 vmovdqu      (%r14,%rcx), %ymm0
	0xc5, 0xfd, 0x74, 0xcb, //0x000018d6 vpcmpeqb     %ymm3, %ymm0, %ymm1
	0xc5, 0x8d, 0xdb, 0xc0, //0x000018da vpand        %ymm0, %ymm14, %ymm0
	0xc5, 0xfd, 0x74, 0x05, 0xba, 0xe8, 0xff, 0xff, //0x000018de vpcmpeqb     $-5958(%rip), %ymm0, %ymm0  /* LCPI0_8+0(%rip) */
	0xc5, 0xfd, 0xeb, 0xc1, //0x000018e6 vpor         %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x000018ea vpmovmskb    %ymm0, %esi
	0x85, 0xf6, //0x000018ee testl        %esi, %esi
	0x0f, 0x85, 0xd5, 0x06, 0x00, 0x00, //0x000018f0 jne          LBB0_382
	0x48, 0x83, 0xc1, 0x20, //0x000018f6 addq         $32, %rcx
	0x49, 0x8d, 0x04, 0x12, //0x000018fa leaq         (%r10,%rdx), %rax
	0x48, 0x83, 0xc0, 0xe0, //0x000018fe addq         $-32, %rax
	0x48, 0x83, 0xc2, 0xe0, //0x00001902 addq         $-32, %rdx
	0x48, 0x83, 0xf8, 0x1f, //0x00001906 cmpq         $31, %rax
	0x0f, 0x87, 0xc0, 0xff, 0xff, 0xff, //0x0000190a ja           LBB0_315
	0x4c, 0x89, 0xf1, //0x00001910 movq         %r14, %rcx
	0x48, 0x29, 0xd1, //0x00001913 subq         %rdx, %rcx
	0x49, 0x01, 0xd2, //0x00001916 addq         %rdx, %r10
	0x4c, 0x89, 0xd6, //0x00001919 movq         %r10, %rsi
	0x48, 0x83, 0xfe, 0x10, //0x0000191c cmpq         $16, %rsi
	0x48, 0x8b, 0x1c, 0x24, //0x00001920 movq         (%rsp), %rbx
	0x0f, 0x82, 0x48, 0x00, 0x00, 0x00, //0x00001924 jb           LBB0_321
	//0x0000192a LBB0_318
	0x4c, 0x89, 0xf2, //0x0000192a movq         %r14, %rdx
	0x48, 0x29, 0xca, //0x0000192d subq         %rcx, %rdx
	//0x00001930 LBB0_319
	0xc5, 0xfa, 0x6f, 0x01, //0x00001930 vmovdqu      (%rcx), %xmm0
	0xc5, 0xf9, 0x74, 0x0d, 0xd4, 0xe6, 0xff, 0xff, //0x00001934 vpcmpeqb     $-6444(%rip), %xmm0, %xmm1  /* LCPI0_11+0(%rip) */
	0xc5, 0xf9, 0xdb, 0x05, 0xdc, 0xe6, 0xff, 0xff, //0x0000193c vpand        $-6436(%rip), %xmm0, %xmm0  /* LCPI0_12+0(%rip) */
	0xc5, 0xf9, 0x74, 0x05, 0xe4, 0xe6, 0xff, 0xff, //0x00001944 vpcmpeqb     $-6428(%rip), %xmm0, %xmm0  /* LCPI0_13+0(%rip) */
	0xc5, 0xf9, 0xeb, 0xc1, //0x0000194c vpor         %xmm1, %xmm0, %xmm0
	0xc5, 0xf9, 0xd7, 0xc0, //0x00001950 vpmovmskb    %xmm0, %eax
	0x85, 0xc0, //0x00001954 testl        %eax, %eax
	0x0f, 0x85, 0xe5, 0x0f, 0x00, 0x00, //0x00001956 jne          LBB0_697
	0x48, 0x83, 0xc1, 0x10, //0x0000195c addq         $16, %rcx
	0x48, 0x83, 0xc6, 0xf0, //0x00001960 addq         $-16, %rsi
	0x48, 0x83, 0xc2, 0xf0, //0x00001964 addq         $-16, %rdx
	0x48, 0x83, 0xfe, 0x0f, //0x00001968 cmpq         $15, %rsi
	0x0f, 0x87, 0xbe, 0xff, 0xff, 0xff, //0x0000196c ja           LBB0_319
	//0x00001972 LBB0_321
	0xc5, 0x7d, 0x7f, 0xe1, //0x00001972 vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd8, //0x00001976 vmovdqa      %ymm11, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xdd, //0x0000197a vmovdqa      %ymm13, %ymm11
	0x48, 0x85, 0xf6, //0x0000197f testq        %rsi, %rsi
	0x0f, 0x84, 0x35, 0x00, 0x00, 0x00, //0x00001982 je           LBB0_328
	0x48, 0x8d, 0x3c, 0x31, //0x00001988 leaq         (%rcx,%rsi), %rdi
	0x31, 0xd2, //0x0000198c xorl         %edx, %edx
	//0x0000198e LBB0_323
	0x0f, 0xb6, 0x1c, 0x11, //0x0000198e movzbl       (%rcx,%rdx), %ebx
	0x80, 0xfb, 0x2c, //0x00001992 cmpb         $44, %bl
	0x0f, 0x84, 0x85, 0x12, 0x00, 0x00, //0x00001995 je           LBB0_520
	0x80, 0xfb, 0x7d, //0x0000199b cmpb         $125, %bl
	0x0f, 0x84, 0x7c, 0x12, 0x00, 0x00, //0x0000199e je           LBB0_520
	0x80, 0xfb, 0x5d, //0x000019a4 cmpb         $93, %bl
	0x0f, 0x84, 0x73, 0x12, 0x00, 0x00, //0x000019a7 je           LBB0_520
	0x48, 0x83, 0xc2, 0x01, //0x000019ad addq         $1, %rdx
	0x48, 0x39, 0xd6, //0x000019b1 cmpq         %rdx, %rsi
	0x0f, 0x85, 0xd4, 0xff, 0xff, 0xff, //0x000019b4 jne          LBB0_323
	0x48, 0x89, 0xf9, //0x000019ba movq         %rdi, %rcx
	//0x000019bd LBB0_328
	0x4c, 0x29, 0xf1, //0x000019bd subq         %r14, %rcx
	0xe9, 0x61, 0x12, 0x00, 0x00, //0x000019c0 jmp          LBB0_521
	//0x000019c5 LBB0_329
	0x4c, 0x8b, 0x4f, 0x08, //0x000019c5 movq         $8(%rdi), %r9
	0x4d, 0x8b, 0x45, 0x00, //0x000019c9 movq         (%r13), %r8
	0x4d, 0x29, 0xc1, //0x000019cd subq         %r8, %r9
	0x4d, 0x01, 0xc6, //0x000019d0 addq         %r8, %r14
	0x45, 0x31, 0xdb, //0x000019d3 xorl         %r11d, %r11d
	0x45, 0x31, 0xd2, //0x000019d6 xorl         %r10d, %r10d
	0x45, 0x31, 0xff, //0x000019d9 xorl         %r15d, %r15d
	0x31, 0xdb, //0x000019dc xorl         %ebx, %ebx
	0xe9, 0x17, 0x00, 0x00, 0x00, //0x000019de jmp          LBB0_331
	//0x000019e3 LBB0_330
	0x48, 0xc1, 0xff, 0x3f, //0x000019e3 sarq         $63, %rdi
	0xf3, 0x48, 0x0f, 0xb8, 0xc6, //0x000019e7 popcntq      %rsi, %rax
	0x49, 0x01, 0xc7, //0x000019ec addq         %rax, %r15
	0x49, 0x83, 0xc6, 0x40, //0x000019ef addq         $64, %r14
	0x49, 0x83, 0xc1, 0xc0, //0x000019f3 addq         $-64, %r9
	0x49, 0x89, 0xfb, //0x000019f7 movq         %rdi, %r11
	//0x000019fa LBB0_331
	0x49, 0x83, 0xf9, 0x40, //0x000019fa cmpq         $64, %r9
	0x0f, 0x8c, 0x59, 0x01, 0x00, 0x00, //0x000019fe jl           LBB0_339
	//0x00001a04 LBB0_332
	0xc4, 0x41, 0x7d, 0x6f, 0xfd, //0x00001a04 vmovdqa      %ymm13, %ymm15
	0xc4, 0xc1, 0x7e, 0x6f, 0x0e, //0x00001a09 vmovdqu      (%r14), %ymm1
	0xc4, 0xc1, 0x7e, 0x6f, 0x46, 0x20, //0x00001a0e vmovdqu      $32(%r14), %ymm0
	0xc5, 0xf5, 0x74, 0xd7, //0x00001a14 vpcmpeqb     %ymm7, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xd2, //0x00001a18 vpmovmskb    %ymm2, %edx
	0xc5, 0xfd, 0x74, 0xd7, //0x00001a1c vpcmpeqb     %ymm7, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xc2, //0x00001a20 vpmovmskb    %ymm2, %eax
	0x48, 0xc1, 0xe0, 0x20, //0x00001a24 shlq         $32, %rax
	0x48, 0x09, 0xc2, //0x00001a28 orq          %rax, %rdx
	0x48, 0x89, 0xd0, //0x00001a2b movq         %rdx, %rax
	0xc4, 0x41, 0x7d, 0x6f, 0xea, //0x00001a2e vmovdqa      %ymm10, %ymm13
	0x4c, 0x09, 0xd0, //0x00001a33 orq          %r10, %rax
	0x0f, 0x85, 0x0f, 0x00, 0x00, 0x00, //0x00001a36 jne          LBB0_334
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00001a3c movq         $-1, %rdx
	0x45, 0x31, 0xd2, //0x00001a43 xorl         %r10d, %r10d
	0xe9, 0x46, 0x00, 0x00, 0x00, //0x00001a46 jmp          LBB0_335
	//0x00001a4b LBB0_334
	0x4c, 0x89, 0xd0, //0x00001a4b movq         %r10, %rax
	0x48, 0xf7, 0xd0, //0x00001a4e notq         %rax
	0x48, 0x21, 0xd0, //0x00001a51 andq         %rdx, %rax
	0x48, 0x8d, 0x0c, 0x00, //0x00001a54 leaq         (%rax,%rax), %rcx
	0x4c, 0x09, 0xd1, //0x00001a58 orq          %r10, %rcx
	0x48, 0x89, 0xce, //0x00001a5b movq         %rcx, %rsi
	0x48, 0xf7, 0xd6, //0x00001a5e notq         %rsi
	0x48, 0xbf, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001a61 movabsq      $-6148914691236517206, %rdi
	0x48, 0x21, 0xfa, //0x00001a6b andq         %rdi, %rdx
	0x48, 0x21, 0xf2, //0x00001a6e andq         %rsi, %rdx
	0x45, 0x31, 0xd2, //0x00001a71 xorl         %r10d, %r10d
	0x48, 0x01, 0xc2, //0x00001a74 addq         %rax, %rdx
	0x41, 0x0f, 0x92, 0xc2, //0x00001a77 setb         %r10b
	0x48, 0x01, 0xd2, //0x00001a7b addq         %rdx, %rdx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00001a7e movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc2, //0x00001a88 xorq         %rax, %rdx
	0x48, 0x21, 0xca, //0x00001a8b andq         %rcx, %rdx
	0x48, 0xf7, 0xd2, //0x00001a8e notq         %rdx
	//0x00001a91 LBB0_335
	0xc5, 0xfd, 0x74, 0xd6, //0x00001a91 vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xc2, //0x00001a95 vpmovmskb    %ymm2, %eax
	0x48, 0xc1, 0xe0, 0x20, //0x00001a99 shlq         $32, %rax
	0xc5, 0xf5, 0x74, 0xd6, //0x00001a9d vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xca, //0x00001aa1 vpmovmskb    %ymm2, %ecx
	0x48, 0x09, 0xc1, //0x00001aa5 orq          %rax, %rcx
	0x48, 0x21, 0xd1, //0x00001aa8 andq         %rdx, %rcx
	0xc4, 0xe1, 0xf9, 0x6e, 0xd1, //0x00001aab vmovq        %rcx, %xmm2
	0xc4, 0xe3, 0x69, 0x44, 0x15, 0xe6, 0xe5, 0xff, 0xff, 0x00, //0x00001ab0 vpclmulqdq   $0, $-6682(%rip), %xmm2, %xmm2  /* LCPI0_26+0(%rip) */
	0xc4, 0xe1, 0xf9, 0x7e, 0xd7, //0x00001aba vmovq        %xmm2, %rdi
	0x4c, 0x31, 0xdf, //0x00001abf xorq         %r11, %rdi
	0xc5, 0x7e, 0x6f, 0x15, 0xb6, 0xe6, 0xff, 0xff, //0x00001ac2 vmovdqu      $-6474(%rip), %ymm10  /* LCPI0_7+0(%rip) */
	0xc5, 0xad, 0x74, 0xd1, //0x00001aca vpcmpeqb     %ymm1, %ymm10, %ymm2
	0xc5, 0xfd, 0xd7, 0xf2, //0x00001ace vpmovmskb    %ymm2, %esi
	0xc5, 0xad, 0x74, 0xd0, //0x00001ad2 vpcmpeqb     %ymm0, %ymm10, %ymm2
	0xc5, 0xfd, 0xd7, 0xc2, //0x00001ad6 vpmovmskb    %ymm2, %eax
	0x48, 0xc1, 0xe0, 0x20, //0x00001ada shlq         $32, %rax
	0x48, 0x09, 0xc6, //0x00001ade orq          %rax, %rsi
	0x48, 0x89, 0xf8, //0x00001ae1 movq         %rdi, %rax
	0x48, 0xf7, 0xd0, //0x00001ae4 notq         %rax
	0x48, 0x21, 0xc6, //0x00001ae7 andq         %rax, %rsi
	0xc5, 0xfe, 0x6f, 0x15, 0xae, 0xe6, 0xff, 0xff, //0x00001aea vmovdqu      $-6482(%rip), %ymm2  /* LCPI0_8+0(%rip) */
	0xc5, 0xf5, 0x74, 0xca, //0x00001af2 vpcmpeqb     %ymm2, %ymm1, %ymm1
	0xc5, 0xfd, 0xd7, 0xd1, //0x00001af6 vpmovmskb    %ymm1, %edx
	0xc5, 0xfd, 0x74, 0xc2, //0x00001afa vpcmpeqb     %ymm2, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xc8, //0x00001afe vpmovmskb    %ymm0, %ecx
	0x48, 0xc1, 0xe1, 0x20, //0x00001b02 shlq         $32, %rcx
	0x48, 0x09, 0xca, //0x00001b06 orq          %rcx, %rdx
	0xc4, 0x41, 0x7d, 0x6f, 0xd5, //0x00001b09 vmovdqa      %ymm13, %ymm10
	0xc4, 0x41, 0x7d, 0x6f, 0xef, //0x00001b0e vmovdqa      %ymm15, %ymm13
	0x48, 0x21, 0xc2, //0x00001b13 andq         %rax, %rdx
	0x0f, 0x84, 0xc7, 0xfe, 0xff, 0xff, //0x00001b16 je           LBB0_330
	0xc5, 0x7e, 0x6f, 0x3d, 0x1c, 0xe7, 0xff, 0xff, //0x00001b1c vmovdqu      $-6372(%rip), %ymm15  /* LCPI0_16+0(%rip) */
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001b24 .p2align 4, 0x90
	//0x00001b30 LBB0_337
	0x48, 0x8d, 0x4a, 0xff, //0x00001b30 leaq         $-1(%rdx), %rcx
	0x48, 0x89, 0xc8, //0x00001b34 movq         %rcx, %rax
	0x48, 0x21, 0xf0, //0x00001b37 andq         %rsi, %rax
	0xf3, 0x48, 0x0f, 0xb8, 0xc0, //0x00001b3a popcntq      %rax, %rax
	0x4c, 0x01, 0xf8, //0x00001b3f addq         %r15, %rax
	0x48, 0x39, 0xd8, //0x00001b42 cmpq         %rbx, %rax
	0x0f, 0x86, 0x38, 0x04, 0x00, 0x00, //0x00001b45 jbe          LBB0_380
	0x48, 0x83, 0xc3, 0x01, //0x00001b4b addq         $1, %rbx
	0x48, 0x21, 0xca, //0x00001b4f andq         %rcx, %rdx
	0x0f, 0x85, 0xd8, 0xff, 0xff, 0xff, //0x00001b52 jne          LBB0_337
	0xe9, 0x86, 0xfe, 0xff, 0xff, //0x00001b58 jmp          LBB0_330
	//0x00001b5d LBB0_339
	0x4d, 0x85, 0xc9, //0x00001b5d testq        %r9, %r9
	0x0f, 0x8e, 0x17, 0x22, 0x00, 0x00, //0x00001b60 jle          LBB0_720
	0xc5, 0x7d, 0x7f, 0xd9, //0x00001b66 vmovdqa      %ymm11, %ymm1
	0xc4, 0x41, 0x7d, 0x6f, 0xdd, //0x00001b6a vmovdqa      %ymm13, %ymm11
	0xc5, 0xf9, 0xef, 0xc0, //0x00001b6f vpxor        %xmm0, %xmm0, %xmm0
	0xc5, 0xfe, 0x7f, 0x44, 0x24, 0x60, //0x00001b73 vmovdqu      %ymm0, $96(%rsp)
	0xc5, 0xfe, 0x7f, 0x44, 0x24, 0x40, //0x00001b79 vmovdqu      %ymm0, $64(%rsp)
	0x44, 0x89, 0xf0, //0x00001b7f movl         %r14d, %eax
	0x25, 0xff, 0x0f, 0x00, 0x00, //0x00001b82 andl         $4095, %eax
	0x3d, 0xc1, 0x0f, 0x00, 0x00, //0x00001b87 cmpl         $4033, %eax
	0x0f, 0x82, 0x27, 0x00, 0x00, 0x00, //0x00001b8c jb           LBB0_343
	0x49, 0x83, 0xf9, 0x20, //0x00001b92 cmpq         $32, %r9
	0x0f, 0x82, 0x40, 0x00, 0x00, 0x00, //0x00001b96 jb           LBB0_344
	0xc4, 0xc1, 0x7e, 0x6f, 0x06, //0x00001b9c vmovdqu      (%r14), %ymm0
	0xc5, 0xfe, 0x7f, 0x44, 0x24, 0x40, //0x00001ba1 vmovdqu      %ymm0, $64(%rsp)
	0x49, 0x83, 0xc6, 0x20, //0x00001ba7 addq         $32, %r14
	0x49, 0x8d, 0x79, 0xe0, //0x00001bab leaq         $-32(%r9), %rdi
	0x48, 0x8d, 0x74, 0x24, 0x60, //0x00001baf leaq         $96(%rsp), %rsi
	0xe9, 0x2b, 0x00, 0x00, 0x00, //0x00001bb4 jmp          LBB0_345
	//0x00001bb9 LBB0_343
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00001bb9 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc4, 0x41, 0x7d, 0x6f, 0xeb, //0x00001bbe vmovdqa      %ymm11, %ymm13
	0xc5, 0xfe, 0x6f, 0x25, 0xd5, 0xe6, 0xff, 0xff, //0x00001bc3 vmovdqu      $-6443(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x1d, 0xed, 0xe5, 0xff, 0xff, //0x00001bcb vmovdqu      $-6675(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd9, //0x00001bd3 vmovdqa      %ymm1, %ymm11
	0xe9, 0x28, 0xfe, 0xff, 0xff, //0x00001bd7 jmp          LBB0_332
	//0x00001bdc LBB0_344
	0x48, 0x8d, 0x74, 0x24, 0x40, //0x00001bdc leaq         $64(%rsp), %rsi
	0x4c, 0x89, 0xcf, //0x00001be1 movq         %r9, %rdi
	//0x00001be4 LBB0_345
	0x48, 0x83, 0xff, 0x10, //0x00001be4 cmpq         $16, %rdi
	0x0f, 0x82, 0x49, 0x00, 0x00, 0x00, //0x00001be8 jb           LBB0_346
	0xc4, 0xc1, 0x7a, 0x6f, 0x06, //0x00001bee vmovdqu      (%r14), %xmm0
	0xc5, 0xfa, 0x7f, 0x06, //0x00001bf3 vmovdqu      %xmm0, (%rsi)
	0x49, 0x83, 0xc6, 0x10, //0x00001bf7 addq         $16, %r14
	0x48, 0x83, 0xc6, 0x10, //0x00001bfb addq         $16, %rsi
	0x48, 0x83, 0xc7, 0xf0, //0x00001bff addq         $-16, %rdi
	0x48, 0x83, 0xff, 0x08, //0x00001c03 cmpq         $8, %rdi
	0x0f, 0x83, 0x34, 0x00, 0x00, 0x00, //0x00001c07 jae          LBB0_353
	//0x00001c0d LBB0_347
	0x48, 0x83, 0xff, 0x04, //0x00001c0d cmpq         $4, %rdi
	0x0f, 0x8c, 0x46, 0x00, 0x00, 0x00, //0x00001c11 jl           LBB0_348
	//0x00001c17 LBB0_354
	0x41, 0x8b, 0x06, //0x00001c17 movl         (%r14), %eax
	0x89, 0x06, //0x00001c1a movl         %eax, (%rsi)
	0x49, 0x83, 0xc6, 0x04, //0x00001c1c addq         $4, %r14
	0x48, 0x83, 0xc6, 0x04, //0x00001c20 addq         $4, %rsi
	0x48, 0x83, 0xc7, 0xfc, //0x00001c24 addq         $-4, %rdi
	0x48, 0x83, 0xff, 0x02, //0x00001c28 cmpq         $2, %rdi
	0x0f, 0x83, 0x35, 0x00, 0x00, 0x00, //0x00001c2c jae          LBB0_349
	0xe9, 0x43, 0x00, 0x00, 0x00, //0x00001c32 jmp          LBB0_350
	//0x00001c37 LBB0_346
	0x48, 0x83, 0xff, 0x08, //0x00001c37 cmpq         $8, %rdi
	0x0f, 0x82, 0xcc, 0xff, 0xff, 0xff, //0x00001c3b jb           LBB0_347
	//0x00001c41 LBB0_353
	0x49, 0x8b, 0x06, //0x00001c41 movq         (%r14), %rax
	0x48, 0x89, 0x06, //0x00001c44 movq         %rax, (%rsi)
	0x49, 0x83, 0xc6, 0x08, //0x00001c47 addq         $8, %r14
	0x48, 0x83, 0xc6, 0x08, //0x00001c4b addq         $8, %rsi
	0x48, 0x83, 0xc7, 0xf8, //0x00001c4f addq         $-8, %rdi
	0x48, 0x83, 0xff, 0x04, //0x00001c53 cmpq         $4, %rdi
	0x0f, 0x8d, 0xba, 0xff, 0xff, 0xff, //0x00001c57 jge          LBB0_354
	//0x00001c5d LBB0_348
	0x48, 0x83, 0xff, 0x02, //0x00001c5d cmpq         $2, %rdi
	0x0f, 0x82, 0x13, 0x00, 0x00, 0x00, //0x00001c61 jb           LBB0_350
	//0x00001c67 LBB0_349
	0x41, 0x0f, 0xb7, 0x06, //0x00001c67 movzwl       (%r14), %eax
	0x66, 0x89, 0x06, //0x00001c6b movw         %ax, (%rsi)
	0x49, 0x83, 0xc6, 0x02, //0x00001c6e addq         $2, %r14
	0x48, 0x83, 0xc6, 0x02, //0x00001c72 addq         $2, %rsi
	0x48, 0x83, 0xc7, 0xfe, //0x00001c76 addq         $-2, %rdi
	//0x00001c7a LBB0_350
	0x4c, 0x89, 0xf2, //0x00001c7a movq         %r14, %rdx
	0x4c, 0x8d, 0x74, 0x24, 0x40, //0x00001c7d leaq         $64(%rsp), %r14
	0x48, 0x85, 0xff, //0x00001c82 testq        %rdi, %rdi
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00001c85 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc4, 0x41, 0x7d, 0x6f, 0xeb, //0x00001c8a vmovdqa      %ymm11, %ymm13
	0xc5, 0xfe, 0x6f, 0x25, 0x09, 0xe6, 0xff, 0xff, //0x00001c8f vmovdqu      $-6647(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x1d, 0x21, 0xe5, 0xff, 0xff, //0x00001c97 vmovdqu      $-6879(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd9, //0x00001c9f vmovdqa      %ymm1, %ymm11
	0x0f, 0x84, 0x5b, 0xfd, 0xff, 0xff, //0x00001ca3 je           LBB0_332
	0x8a, 0x02, //0x00001ca9 movb         (%rdx), %al
	0x88, 0x06, //0x00001cab movb         %al, (%rsi)
	0x4c, 0x8d, 0x74, 0x24, 0x40, //0x00001cad leaq         $64(%rsp), %r14
	0xe9, 0x4d, 0xfd, 0xff, 0xff, //0x00001cb2 jmp          LBB0_332
	//0x00001cb7 LBB0_355
	0x4c, 0x8b, 0x4f, 0x08, //0x00001cb7 movq         $8(%rdi), %r9
	0x4d, 0x8b, 0x45, 0x00, //0x00001cbb movq         (%r13), %r8
	0x4d, 0x29, 0xc1, //0x00001cbf subq         %r8, %r9
	0x4d, 0x01, 0xc6, //0x00001cc2 addq         %r8, %r14
	0x45, 0x31, 0xdb, //0x00001cc5 xorl         %r11d, %r11d
	0x45, 0x31, 0xd2, //0x00001cc8 xorl         %r10d, %r10d
	0x45, 0x31, 0xff, //0x00001ccb xorl         %r15d, %r15d
	0x31, 0xdb, //0x00001cce xorl         %ebx, %ebx
	0xe9, 0x17, 0x00, 0x00, 0x00, //0x00001cd0 jmp          LBB0_357
	//0x00001cd5 LBB0_356
	0x48, 0xc1, 0xff, 0x3f, //0x00001cd5 sarq         $63, %rdi
	0xf3, 0x48, 0x0f, 0xb8, 0xc6, //0x00001cd9 popcntq      %rsi, %rax
	0x49, 0x01, 0xc7, //0x00001cde addq         %rax, %r15
	0x49, 0x83, 0xc6, 0x40, //0x00001ce1 addq         $64, %r14
	0x49, 0x83, 0xc1, 0xc0, //0x00001ce5 addq         $-64, %r9
	0x49, 0x89, 0xfb, //0x00001ce9 movq         %rdi, %r11
	//0x00001cec LBB0_357
	0x49, 0x83, 0xf9, 0x40, //0x00001cec cmpq         $64, %r9
	0x0f, 0x8c, 0x27, 0x01, 0x00, 0x00, //0x00001cf0 jl           LBB0_364
	//0x00001cf6 LBB0_358
	0xc4, 0xc1, 0x7e, 0x6f, 0x0e, //0x00001cf6 vmovdqu      (%r14), %ymm1
	0xc4, 0xc1, 0x7e, 0x6f, 0x46, 0x20, //0x00001cfb vmovdqu      $32(%r14), %ymm0
	0xc5, 0xf5, 0x74, 0xd7, //0x00001d01 vpcmpeqb     %ymm7, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xd2, //0x00001d05 vpmovmskb    %ymm2, %edx
	0xc5, 0xfd, 0x74, 0xd7, //0x00001d09 vpcmpeqb     %ymm7, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xc2, //0x00001d0d vpmovmskb    %ymm2, %eax
	0x48, 0xc1, 0xe0, 0x20, //0x00001d11 shlq         $32, %rax
	0x48, 0x09, 0xc2, //0x00001d15 orq          %rax, %rdx
	0x48, 0x89, 0xd0, //0x00001d18 movq         %rdx, %rax
	0x4c, 0x09, 0xd0, //0x00001d1b orq          %r10, %rax
	0x0f, 0x85, 0x0f, 0x00, 0x00, 0x00, //0x00001d1e jne          LBB0_360
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00001d24 movq         $-1, %rdx
	0x45, 0x31, 0xd2, //0x00001d2b xorl         %r10d, %r10d
	0xe9, 0x46, 0x00, 0x00, 0x00, //0x00001d2e jmp          LBB0_361
	//0x00001d33 LBB0_360
	0x4c, 0x89, 0xd0, //0x00001d33 movq         %r10, %rax
	0x48, 0xf7, 0xd0, //0x00001d36 notq         %rax
	0x48, 0x21, 0xd0, //0x00001d39 andq         %rdx, %rax
	0x48, 0x8d, 0x0c, 0x00, //0x00001d3c leaq         (%rax,%rax), %rcx
	0x4c, 0x09, 0xd1, //0x00001d40 orq          %r10, %rcx
	0x48, 0x89, 0xce, //0x00001d43 movq         %rcx, %rsi
	0x48, 0xf7, 0xd6, //0x00001d46 notq         %rsi
	0x48, 0xbf, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001d49 movabsq      $-6148914691236517206, %rdi
	0x48, 0x21, 0xfa, //0x00001d53 andq         %rdi, %rdx
	0x48, 0x21, 0xf2, //0x00001d56 andq         %rsi, %rdx
	0x45, 0x31, 0xd2, //0x00001d59 xorl         %r10d, %r10d
	0x48, 0x01, 0xc2, //0x00001d5c addq         %rax, %rdx
	0x41, 0x0f, 0x92, 0xc2, //0x00001d5f setb         %r10b
	0x48, 0x01, 0xd2, //0x00001d63 addq         %rdx, %rdx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00001d66 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc2, //0x00001d70 xorq         %rax, %rdx
	0x48, 0x21, 0xca, //0x00001d73 andq         %rcx, %rdx
	0x48, 0xf7, 0xd2, //0x00001d76 notq         %rdx
	//0x00001d79 LBB0_361
	0xc5, 0xfd, 0x74, 0xd6, //0x00001d79 vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xc2, //0x00001d7d vpmovmskb    %ymm2, %eax
	0x48, 0xc1, 0xe0, 0x20, //0x00001d81 shlq         $32, %rax
	0xc5, 0xf5, 0x74, 0xd6, //0x00001d85 vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xca, //0x00001d89 vpmovmskb    %ymm2, %ecx
	0x48, 0x09, 0xc1, //0x00001d8d orq          %rax, %rcx
	0x48, 0x21, 0xd1, //0x00001d90 andq         %rdx, %rcx
	0xc4, 0xe1, 0xf9, 0x6e, 0xd1, //0x00001d93 vmovq        %rcx, %xmm2
	0xc4, 0xe3, 0x69, 0x44, 0x15, 0xfe, 0xe2, 0xff, 0xff, 0x00, //0x00001d98 vpclmulqdq   $0, $-7426(%rip), %xmm2, %xmm2  /* LCPI0_26+0(%rip) */
	0xc4, 0xe1, 0xf9, 0x7e, 0xd7, //0x00001da2 vmovq        %xmm2, %rdi
	0x4c, 0x31, 0xdf, //0x00001da7 xorq         %r11, %rdi
	0xc5, 0xa5, 0x74, 0xd1, //0x00001daa vpcmpeqb     %ymm1, %ymm11, %ymm2
	0xc5, 0xfd, 0xd7, 0xf2, //0x00001dae vpmovmskb    %ymm2, %esi
	0xc5, 0xa5, 0x74, 0xd0, //0x00001db2 vpcmpeqb     %ymm0, %ymm11, %ymm2
	0xc5, 0xfd, 0xd7, 0xc2, //0x00001db6 vpmovmskb    %ymm2, %eax
	0x48, 0xc1, 0xe0, 0x20, //0x00001dba shlq         $32, %rax
	0x48, 0x09, 0xc6, //0x00001dbe orq          %rax, %rsi
	0x48, 0x89, 0xf8, //0x00001dc1 movq         %rdi, %rax
	0x48, 0xf7, 0xd0, //0x00001dc4 notq         %rax
	0x48, 0x21, 0xc6, //0x00001dc7 andq         %rax, %rsi
	0xc5, 0x9d, 0x74, 0xc9, //0x00001dca vpcmpeqb     %ymm1, %ymm12, %ymm1
	0xc5, 0xfd, 0xd7, 0xd1, //0x00001dce vpmovmskb    %ymm1, %edx
	0xc5, 0x9d, 0x74, 0xc0, //0x00001dd2 vpcmpeqb     %ymm0, %ymm12, %ymm0
	0xc5, 0xfd, 0xd7, 0xc8, //0x00001dd6 vpmovmskb    %ymm0, %ecx
	0x48, 0xc1, 0xe1, 0x20, //0x00001dda shlq         $32, %rcx
	0x48, 0x09, 0xca, //0x00001dde orq          %rcx, %rdx
	0x48, 0x21, 0xc2, //0x00001de1 andq         %rax, %rdx
	0x0f, 0x84, 0xeb, 0xfe, 0xff, 0xff, //0x00001de4 je           LBB0_356
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001dea .p2align 4, 0x90
	//0x00001df0 LBB0_362
	0x48, 0x8d, 0x4a, 0xff, //0x00001df0 leaq         $-1(%rdx), %rcx
	0x48, 0x89, 0xc8, //0x00001df4 movq         %rcx, %rax
	0x48, 0x21, 0xf0, //0x00001df7 andq         %rsi, %rax
	0xf3, 0x48, 0x0f, 0xb8, 0xc0, //0x00001dfa popcntq      %rax, %rax
	0x4c, 0x01, 0xf8, //0x00001dff addq         %r15, %rax
	0x48, 0x39, 0xd8, //0x00001e02 cmpq         %rbx, %rax
	0x0f, 0x86, 0x78, 0x01, 0x00, 0x00, //0x00001e05 jbe          LBB0_380
	0x48, 0x83, 0xc3, 0x01, //0x00001e0b addq         $1, %rbx
	0x48, 0x21, 0xca, //0x00001e0f andq         %rcx, %rdx
	0x0f, 0x85, 0xd8, 0xff, 0xff, 0xff, //0x00001e12 jne          LBB0_362
	0xe9, 0xb8, 0xfe, 0xff, 0xff, //0x00001e18 jmp          LBB0_356
	//0x00001e1d LBB0_364
	0x4d, 0x85, 0xc9, //0x00001e1d testq        %r9, %r9
	0x0f, 0x8e, 0x57, 0x1f, 0x00, 0x00, //0x00001e20 jle          LBB0_720
	0xc5, 0x7d, 0x7f, 0xe2, //0x00001e26 vmovdqa      %ymm12, %ymm2
	0xc5, 0x7d, 0x7f, 0xd9, //0x00001e2a vmovdqa      %ymm11, %ymm1
	0xc4, 0x41, 0x7d, 0x6f, 0xdd, //0x00001e2e vmovdqa      %ymm13, %ymm11
	0xc5, 0xf9, 0xef, 0xc0, //0x00001e33 vpxor        %xmm0, %xmm0, %xmm0
	0xc5, 0xfe, 0x7f, 0x44, 0x24, 0x60, //0x00001e37 vmovdqu      %ymm0, $96(%rsp)
	0xc5, 0xfe, 0x7f, 0x44, 0x24, 0x40, //0x00001e3d vmovdqu      %ymm0, $64(%rsp)
	0x44, 0x89, 0xf0, //0x00001e43 movl         %r14d, %eax
	0x25, 0xff, 0x0f, 0x00, 0x00, //0x00001e46 andl         $4095, %eax
	0x3d, 0xc1, 0x0f, 0x00, 0x00, //0x00001e4b cmpl         $4033, %eax
	0x0f, 0x82, 0x27, 0x00, 0x00, 0x00, //0x00001e50 jb           LBB0_368
	0x49, 0x83, 0xf9, 0x20, //0x00001e56 cmpq         $32, %r9
	0x0f, 0x82, 0x44, 0x00, 0x00, 0x00, //0x00001e5a jb           LBB0_369
	0xc4, 0xc1, 0x7e, 0x6f, 0x06, //0x00001e60 vmovdqu      (%r14), %ymm0
	0xc5, 0xfe, 0x7f, 0x44, 0x24, 0x40, //0x00001e65 vmovdqu      %ymm0, $64(%rsp)
	0x49, 0x83, 0xc6, 0x20, //0x00001e6b addq         $32, %r14
	0x49, 0x8d, 0x79, 0xe0, //0x00001e6f leaq         $-32(%r9), %rdi
	0x48, 0x8d, 0x74, 0x24, 0x60, //0x00001e73 leaq         $96(%rsp), %rsi
	0xe9, 0x2f, 0x00, 0x00, 0x00, //0x00001e78 jmp          LBB0_370
	//0x00001e7d LBB0_368
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00001e7d vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc4, 0x41, 0x7d, 0x6f, 0xeb, //0x00001e82 vmovdqa      %ymm11, %ymm13
	0xc5, 0xfe, 0x6f, 0x25, 0x11, 0xe4, 0xff, 0xff, //0x00001e87 vmovdqu      $-7151(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x1d, 0x29, 0xe3, 0xff, 0xff, //0x00001e8f vmovdqu      $-7383(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd9, //0x00001e97 vmovdqa      %ymm1, %ymm11
	0xc5, 0x7d, 0x6f, 0xe2, //0x00001e9b vmovdqa      %ymm2, %ymm12
	0xe9, 0x52, 0xfe, 0xff, 0xff, //0x00001e9f jmp          LBB0_358
	//0x00001ea4 LBB0_369
	0x48, 0x8d, 0x74, 0x24, 0x40, //0x00001ea4 leaq         $64(%rsp), %rsi
	0x4c, 0x89, 0xcf, //0x00001ea9 movq         %r9, %rdi
	//0x00001eac LBB0_370
	0x48, 0x83, 0xff, 0x10, //0x00001eac cmpq         $16, %rdi
	0x0f, 0x82, 0x49, 0x00, 0x00, 0x00, //0x00001eb0 jb           LBB0_371
	0xc4, 0xc1, 0x7a, 0x6f, 0x06, //0x00001eb6 vmovdqu      (%r14), %xmm0
	0xc5, 0xfa, 0x7f, 0x06, //0x00001ebb vmovdqu      %xmm0, (%rsi)
	0x49, 0x83, 0xc6, 0x10, //0x00001ebf addq         $16, %r14
	0x48, 0x83, 0xc6, 0x10, //0x00001ec3 addq         $16, %rsi
	0x48, 0x83, 0xc7, 0xf0, //0x00001ec7 addq         $-16, %rdi
	0x48, 0x83, 0xff, 0x08, //0x00001ecb cmpq         $8, %rdi
	0x0f, 0x83, 0x34, 0x00, 0x00, 0x00, //0x00001ecf jae          LBB0_378
	//0x00001ed5 LBB0_372
	0x48, 0x83, 0xff, 0x04, //0x00001ed5 cmpq         $4, %rdi
	0x0f, 0x8c, 0x46, 0x00, 0x00, 0x00, //0x00001ed9 jl           LBB0_373
	//0x00001edf LBB0_379
	0x41, 0x8b, 0x06, //0x00001edf movl         (%r14), %eax
	0x89, 0x06, //0x00001ee2 movl         %eax, (%rsi)
	0x49, 0x83, 0xc6, 0x04, //0x00001ee4 addq         $4, %r14
	0x48, 0x83, 0xc6, 0x04, //0x00001ee8 addq         $4, %rsi
	0x48, 0x83, 0xc7, 0xfc, //0x00001eec addq         $-4, %rdi
	0x48, 0x83, 0xff, 0x02, //0x00001ef0 cmpq         $2, %rdi
	0x0f, 0x83, 0x35, 0x00, 0x00, 0x00, //0x00001ef4 jae          LBB0_374
	0xe9, 0x43, 0x00, 0x00, 0x00, //0x00001efa jmp          LBB0_375
	//0x00001eff LBB0_371
	0x48, 0x83, 0xff, 0x08, //0x00001eff cmpq         $8, %rdi
	0x0f, 0x82, 0xcc, 0xff, 0xff, 0xff, //0x00001f03 jb           LBB0_372
	//0x00001f09 LBB0_378
	0x49, 0x8b, 0x06, //0x00001f09 movq         (%r14), %rax
	0x48, 0x89, 0x06, //0x00001f0c movq         %rax, (%rsi)
	0x49, 0x83, 0xc6, 0x08, //0x00001f0f addq         $8, %r14
	0x48, 0x83, 0xc6, 0x08, //0x00001f13 addq         $8, %rsi
	0x48, 0x83, 0xc7, 0xf8, //0x00001f17 addq         $-8, %rdi
	0x48, 0x83, 0xff, 0x04, //0x00001f1b cmpq         $4, %rdi
	0x0f, 0x8d, 0xba, 0xff, 0xff, 0xff, //0x00001f1f jge          LBB0_379
	//0x00001f25 LBB0_373
	0x48, 0x83, 0xff, 0x02, //0x00001f25 cmpq         $2, %rdi
	0x0f, 0x82, 0x13, 0x00, 0x00, 0x00, //0x00001f29 jb           LBB0_375
	//0x00001f2f LBB0_374
	0x41, 0x0f, 0xb7, 0x06, //0x00001f2f movzwl       (%r14), %eax
	0x66, 0x89, 0x06, //0x00001f33 movw         %ax, (%rsi)
	0x49, 0x83, 0xc6, 0x02, //0x00001f36 addq         $2, %r14
	0x48, 0x83, 0xc6, 0x02, //0x00001f3a addq         $2, %rsi
	0x48, 0x83, 0xc7, 0xfe, //0x00001f3e addq         $-2, %rdi
	//0x00001f42 LBB0_375
	0x4c, 0x89, 0xf2, //0x00001f42 movq         %r14, %rdx
	0x4c, 0x8d, 0x74, 0x24, 0x40, //0x00001f45 leaq         $64(%rsp), %r14
	0x48, 0x85, 0xff, //0x00001f4a testq        %rdi, %rdi
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00001f4d vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc4, 0x41, 0x7d, 0x6f, 0xeb, //0x00001f52 vmovdqa      %ymm11, %ymm13
	0xc5, 0xfe, 0x6f, 0x25, 0x41, 0xe3, 0xff, 0xff, //0x00001f57 vmovdqu      $-7359(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x1d, 0x59, 0xe2, 0xff, 0xff, //0x00001f5f vmovdqu      $-7591(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd9, //0x00001f67 vmovdqa      %ymm1, %ymm11
	0xc5, 0x7d, 0x6f, 0xe2, //0x00001f6b vmovdqa      %ymm2, %ymm12
	0x0f, 0x84, 0x81, 0xfd, 0xff, 0xff, //0x00001f6f je           LBB0_358
	0x8a, 0x02, //0x00001f75 movb         (%rdx), %al
	0x88, 0x06, //0x00001f77 movb         %al, (%rsi)
	0x4c, 0x8d, 0x74, 0x24, 0x40, //0x00001f79 leaq         $64(%rsp), %r14
	0xe9, 0x73, 0xfd, 0xff, 0xff, //0x00001f7e jmp          LBB0_358
	//0x00001f83 LBB0_380
	0x48, 0x8b, 0x7c, 0x24, 0x10, //0x00001f83 movq         $16(%rsp), %rdi
	0x48, 0x8b, 0x47, 0x08, //0x00001f88 movq         $8(%rdi), %rax
	0x48, 0x0f, 0xbc, 0xca, //0x00001f8c bsfq         %rdx, %rcx
	0x4c, 0x29, 0xc9, //0x00001f90 subq         %r9, %rcx
	0x48, 0x01, 0xc8, //0x00001f93 addq         %rcx, %rax
	0x48, 0x83, 0xc0, 0x01, //0x00001f96 addq         $1, %rax
	0x49, 0x89, 0x45, 0x00, //0x00001f9a movq         %rax, (%r13)
	0x48, 0x8b, 0x4f, 0x08, //0x00001f9e movq         $8(%rdi), %rcx
	0x48, 0x39, 0xc8, //0x00001fa2 cmpq         %rcx, %rax
	0x48, 0x0f, 0x47, 0xc1, //0x00001fa5 cmovaq       %rcx, %rax
	0x49, 0x89, 0x45, 0x00, //0x00001fa9 movq         %rax, (%r13)
	0x0f, 0x87, 0xe2, 0x1b, 0x00, 0x00, //0x00001fad ja           LBB0_711
	0x4d, 0x85, 0xc0, //0x00001fb3 testq        %r8, %r8
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x00001fb6 movq         $24(%rsp), %r11
	0x4c, 0x8b, 0x7c, 0x24, 0x08, //0x00001fbb movq         $8(%rsp), %r15
	0x0f, 0x8f, 0xda, 0xe3, 0xff, 0xff, //0x00001fc0 jg           LBB0_3
	0xe9, 0x61, 0x1b, 0x00, 0x00, //0x00001fc6 jmp          LBB0_679
	//0x00001fcb LBB0_382
	0x0f, 0xbc, 0xce, //0x00001fcb bsfl         %esi, %ecx
	0x48, 0x29, 0xd1, //0x00001fce subq         %rdx, %rcx
	//0x00001fd1 LBB0_383
	0x48, 0x8b, 0x1c, 0x24, //0x00001fd1 movq         (%rsp), %rbx
	0x49, 0x89, 0x4d, 0x00, //0x00001fd5 movq         %rcx, (%r13)
	0x48, 0x85, 0xdb, //0x00001fd9 testq        %rbx, %rbx
	0x0f, 0x8f, 0xbe, 0xe3, 0xff, 0xff, //0x00001fdc jg           LBB0_3
	0xe9, 0xea, 0x1c, 0x00, 0x00, //0x00001fe2 jmp          LBB0_698
	//0x00001fe7 LBB0_384
	0x49, 0x8d, 0x41, 0xff, //0x00001fe7 leaq         $-1(%r9), %rax
	0x48, 0x39, 0xc2, //0x00001feb cmpq         %rax, %rdx
	0x49, 0xf7, 0xd1, //0x00001fee notq         %r9
	0x4d, 0x0f, 0x45, 0xcd, //0x00001ff1 cmovneq      %r13, %r9
	0x84, 0xc9, //0x00001ff5 testb        %cl, %cl
	0x4d, 0x0f, 0x45, 0xe9, //0x00001ff7 cmovneq      %r9, %r13
	0xe9, 0x21, 0xf7, 0xff, 0xff, //0x00001ffb jmp          LBB0_290
	//0x00002000 LBB0_385
	0x4d, 0x29, 0xc8, //0x00002000 subq         %r9, %r8
	0x0f, 0x84, 0x5a, 0x1d, 0x00, 0x00, //0x00002003 je           LBB0_718
	0x49, 0x83, 0xf8, 0x40, //0x00002009 cmpq         $64, %r8
	0x0f, 0x82, 0xdb, 0x0b, 0x00, 0x00, //0x0000200d jb           LBB0_518
	0x49, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00002013 movq         $-1, %r10
	0x4c, 0x89, 0xc9, //0x0000201a movq         %r9, %rcx
	0x45, 0x31, 0xdb, //0x0000201d xorl         %r11d, %r11d
	//0x00002020 .p2align 4, 0x90
	//0x00002020 LBB0_388
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x0e, //0x00002020 vmovdqu      (%r14,%rcx), %ymm0
	0xc4, 0xc1, 0x7e, 0x6f, 0x4c, 0x0e, 0x20, //0x00002026 vmovdqu      $32(%r14,%rcx), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x0000202d vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xfa, //0x00002031 vpmovmskb    %ymm2, %edi
	0xc5, 0xf5, 0x74, 0xd6, //0x00002035 vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xc2, //0x00002039 vpmovmskb    %ymm2, %eax
	0xc5, 0xfd, 0x74, 0xd7, //0x0000203d vpcmpeqb     %ymm7, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xd2, //0x00002041 vpmovmskb    %ymm2, %edx
	0xc5, 0xf5, 0x74, 0xd7, //0x00002045 vpcmpeqb     %ymm7, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xda, //0x00002049 vpmovmskb    %ymm2, %ebx
	0xc5, 0xbd, 0x64, 0xd1, //0x0000204d vpcmpgtb     %ymm1, %ymm8, %ymm2
	0xc4, 0xc1, 0x75, 0x64, 0xc9, //0x00002051 vpcmpgtb     %ymm9, %ymm1, %ymm1
	0xc5, 0xed, 0xdb, 0xc9, //0x00002056 vpand        %ymm1, %ymm2, %ymm1
	0xc5, 0xfd, 0xd7, 0xf1, //0x0000205a vpmovmskb    %ymm1, %esi
	0x48, 0xc1, 0xe0, 0x20, //0x0000205e shlq         $32, %rax
	0x48, 0x09, 0xc7, //0x00002062 orq          %rax, %rdi
	0x48, 0xc1, 0xe3, 0x20, //0x00002065 shlq         $32, %rbx
	0x48, 0xc1, 0xe6, 0x20, //0x00002069 shlq         $32, %rsi
	0x48, 0x09, 0xda, //0x0000206d orq          %rbx, %rdx
	0x0f, 0x85, 0x49, 0x00, 0x00, 0x00, //0x00002070 jne          LBB0_399
	0x4d, 0x85, 0xdb, //0x00002076 testq        %r11, %r11
	0x0f, 0x85, 0x51, 0x00, 0x00, 0x00, //0x00002079 jne          LBB0_401
	0x45, 0x31, 0xdb, //0x0000207f xorl         %r11d, %r11d
	//0x00002082 LBB0_391
	0xc5, 0xbd, 0x64, 0xc8, //0x00002082 vpcmpgtb     %ymm0, %ymm8, %ymm1
	0xc4, 0xc1, 0x7d, 0x64, 0xc1, //0x00002086 vpcmpgtb     %ymm9, %ymm0, %ymm0
	0xc5, 0xf5, 0xdb, 0xc0, //0x0000208b vpand        %ymm0, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xc0, //0x0000208f vpmovmskb    %ymm0, %eax
	0x48, 0x09, 0xc6, //0x00002093 orq          %rax, %rsi
	0x48, 0x85, 0xff, //0x00002096 testq        %rdi, %rdi
	0x0f, 0x85, 0x87, 0x00, 0x00, 0x00, //0x00002099 jne          LBB0_402
	0x48, 0x85, 0xf6, //0x0000209f testq        %rsi, %rsi
	0x0f, 0x85, 0x7c, 0x1c, 0x00, 0x00, //0x000020a2 jne          LBB0_712
	0x49, 0x83, 0xc0, 0xc0, //0x000020a8 addq         $-64, %r8
	0x48, 0x83, 0xc1, 0x40, //0x000020ac addq         $64, %rcx
	0x49, 0x83, 0xf8, 0x3f, //0x000020b0 cmpq         $63, %r8
	0x0f, 0x87, 0x66, 0xff, 0xff, 0xff, //0x000020b4 ja           LBB0_388
	0xe9, 0x39, 0x09, 0x00, 0x00, //0x000020ba jmp          LBB0_394
	//0x000020bf LBB0_399
	0x49, 0x83, 0xfa, 0xff, //0x000020bf cmpq         $-1, %r10
	0x0f, 0x85, 0x07, 0x00, 0x00, 0x00, //0x000020c3 jne          LBB0_401
	0x4c, 0x0f, 0xbc, 0xd2, //0x000020c9 bsfq         %rdx, %r10
	0x49, 0x01, 0xca, //0x000020cd addq         %rcx, %r10
	//0x000020d0 LBB0_401
	0x4c, 0x89, 0xd8, //0x000020d0 movq         %r11, %rax
	0x48, 0xf7, 0xd0, //0x000020d3 notq         %rax
	0x48, 0x21, 0xd0, //0x000020d6 andq         %rdx, %rax
	0x48, 0x89, 0x0c, 0x24, //0x000020d9 movq         %rcx, (%rsp)
	0x48, 0x8d, 0x0c, 0x00, //0x000020dd leaq         (%rax,%rax), %rcx
	0x4c, 0x09, 0xd9, //0x000020e1 orq          %r11, %rcx
	0x48, 0x89, 0xcb, //0x000020e4 movq         %rcx, %rbx
	0x48, 0xf7, 0xd3, //0x000020e7 notq         %rbx
	0x48, 0x21, 0xd3, //0x000020ea andq         %rdx, %rbx
	0x48, 0xba, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x000020ed movabsq      $-6148914691236517206, %rdx
	0x48, 0x21, 0xd3, //0x000020f7 andq         %rdx, %rbx
	0x45, 0x31, 0xdb, //0x000020fa xorl         %r11d, %r11d
	0x48, 0x01, 0xc3, //0x000020fd addq         %rax, %rbx
	0x41, 0x0f, 0x92, 0xc3, //0x00002100 setb         %r11b
	0x48, 0x01, 0xdb, //0x00002104 addq         %rbx, %rbx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00002107 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc3, //0x00002111 xorq         %rax, %rbx
	0x48, 0x21, 0xcb, //0x00002114 andq         %rcx, %rbx
	0x48, 0x8b, 0x0c, 0x24, //0x00002117 movq         (%rsp), %rcx
	0x48, 0xf7, 0xd3, //0x0000211b notq         %rbx
	0x48, 0x21, 0xdf, //0x0000211e andq         %rbx, %rdi
	0xe9, 0x5c, 0xff, 0xff, 0xff, //0x00002121 jmp          LBB0_391
	//0x00002126 LBB0_402
	0x48, 0x0f, 0xbc, 0xc7, //0x00002126 bsfq         %rdi, %rax
	0x48, 0x85, 0xf6, //0x0000212a testq        %rsi, %rsi
	0x0f, 0x84, 0x9f, 0x03, 0x00, 0x00, //0x0000212d je           LBB0_458
	0x48, 0x0f, 0xbc, 0xd6, //0x00002133 bsfq         %rsi, %rdx
	0xe9, 0x9b, 0x03, 0x00, 0x00, //0x00002137 jmp          LBB0_459
	//0x0000213c LBB0_404
	0x48, 0x8b, 0x44, 0x24, 0x38, //0x0000213c movq         $56(%rsp), %rax
	0x8a, 0x48, 0x01, //0x00002141 movb         $1(%rax), %cl
	0x80, 0xc1, 0xd2, //0x00002144 addb         $-46, %cl
	0x80, 0xf9, 0x37, //0x00002147 cmpb         $55, %cl
	0x0f, 0x87, 0x80, 0xf3, 0xff, 0xff, //0x0000214a ja           LBB0_257
	0x0f, 0xb6, 0xc1, //0x00002150 movzbl       %cl, %eax
	0x48, 0xb9, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x00002153 movabsq      $36028797027352577, %rcx
	0x48, 0x0f, 0xa3, 0xc1, //0x0000215d btq          %rax, %rcx
	0x48, 0x8b, 0x0c, 0x24, //0x00002161 movq         (%rsp), %rcx
	0x0f, 0x83, 0x1e, 0x08, 0x00, 0x00, //0x00002165 jae          LBB0_498
	//0x0000216b LBB0_406
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x0000216b movq         $-1, %r8
	0x49, 0x83, 0xfa, 0x20, //0x00002172 cmpq         $32, %r10
	0x0f, 0x82, 0x1c, 0x0a, 0x00, 0x00, //0x00002176 jb           LBB0_515
	0x45, 0x31, 0xc9, //0x0000217c xorl         %r9d, %r9d
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x0000217f movq         $-1, %r15
	0x48, 0xc7, 0x44, 0x24, 0x28, 0xff, 0xff, 0xff, 0xff, //0x00002186 movq         $-1, $40(%rsp)
	0x4c, 0x8b, 0x5c, 0x24, 0x38, //0x0000218f movq         $56(%rsp), %r11
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002194 .p2align 4, 0x90
	//0x000021a0 LBB0_408
	0xc4, 0x81, 0x7e, 0x6f, 0x04, 0x0b, //0x000021a0 vmovdqu      (%r11,%r9), %ymm0
	0xc4, 0xc1, 0x7d, 0x64, 0xca, //0x000021a6 vpcmpgtb     %ymm10, %ymm0, %ymm1
	0xc5, 0x95, 0x64, 0xd0, //0x000021ab vpcmpgtb     %ymm0, %ymm13, %ymm2
	0xc5, 0xed, 0xdb, 0xc9, //0x000021af vpand        %ymm1, %ymm2, %ymm1
	0xc5, 0x85, 0x74, 0xd0, //0x000021b3 vpcmpeqb     %ymm0, %ymm15, %ymm2
	0xc5, 0xfd, 0x74, 0x1d, 0xa1, 0xe0, 0xff, 0xff, //0x000021b7 vpcmpeqb     $-8031(%rip), %ymm0, %ymm3  /* LCPI0_17+0(%rip) */
	0xc5, 0xe5, 0xeb, 0xd2, //0x000021bf vpor         %ymm2, %ymm3, %ymm2
	0xc5, 0x8d, 0xdb, 0xd8, //0x000021c3 vpand        %ymm0, %ymm14, %ymm3
	0xc5, 0xfd, 0x74, 0x05, 0xb1, 0xe0, 0xff, 0xff, //0x000021c7 vpcmpeqb     $-8015(%rip), %ymm0, %ymm0  /* LCPI0_18+0(%rip) */
	0xc5, 0xfd, 0xd7, 0xd0, //0x000021cf vpmovmskb    %ymm0, %edx
	0xc5, 0xe5, 0x74, 0xdc, //0x000021d3 vpcmpeqb     %ymm4, %ymm3, %ymm3
	0xc5, 0xfd, 0xd7, 0xfb, //0x000021d7 vpmovmskb    %ymm3, %edi
	0xc5, 0xfd, 0xd7, 0xf2, //0x000021db vpmovmskb    %ymm2, %esi
	0xc5, 0xf5, 0xeb, 0xc0, //0x000021df vpor         %ymm0, %ymm1, %ymm0
	0xc5, 0xe5, 0xeb, 0xca, //0x000021e3 vpor         %ymm2, %ymm3, %ymm1
	0xc5, 0xfd, 0xeb, 0xc1, //0x000021e7 vpor         %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xc0, //0x000021eb vpmovmskb    %ymm0, %eax
	0x48, 0xf7, 0xd0, //0x000021ef notq         %rax
	0x48, 0x0f, 0xbc, 0xc8, //0x000021f2 bsfq         %rax, %rcx
	0x83, 0xf9, 0x20, //0x000021f6 cmpl         $32, %ecx
	0x0f, 0x84, 0x11, 0x00, 0x00, 0x00, //0x000021f9 je           LBB0_410
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x000021ff movl         $-1, %eax
	0xd3, 0xe0, //0x00002204 shll         %cl, %eax
	0xf7, 0xd0, //0x00002206 notl         %eax
	0x21, 0xc2, //0x00002208 andl         %eax, %edx
	0x21, 0xc7, //0x0000220a andl         %eax, %edi
	0x21, 0xf0, //0x0000220c andl         %esi, %eax
	0x89, 0xc6, //0x0000220e movl         %eax, %esi
	//0x00002210 LBB0_410
	0x8d, 0x5a, 0xff, //0x00002210 leal         $-1(%rdx), %ebx
	0x21, 0xd3, //0x00002213 andl         %edx, %ebx
	0xc5, 0xfe, 0x6f, 0x1d, 0xa3, 0xdf, 0xff, 0xff, //0x00002215 vmovdqu      $-8285(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0x0f, 0x85, 0x12, 0x07, 0x00, 0x00, //0x0000221d jne          LBB0_492
	0x8d, 0x5f, 0xff, //0x00002223 leal         $-1(%rdi), %ebx
	0x21, 0xfb, //0x00002226 andl         %edi, %ebx
	0x0f, 0x85, 0x07, 0x07, 0x00, 0x00, //0x00002228 jne          LBB0_492
	0x8d, 0x5e, 0xff, //0x0000222e leal         $-1(%rsi), %ebx
	0x21, 0xf3, //0x00002231 andl         %esi, %ebx
	0x0f, 0x85, 0xfc, 0x06, 0x00, 0x00, //0x00002233 jne          LBB0_492
	0x85, 0xd2, //0x00002239 testl        %edx, %edx
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x0000223b je           LBB0_416
	0x0f, 0xbc, 0xd2, //0x00002241 bsfl         %edx, %edx
	0x48, 0x83, 0x7c, 0x24, 0x28, 0xff, //0x00002244 cmpq         $-1, $40(%rsp)
	0x0f, 0x85, 0x0d, 0x07, 0x00, 0x00, //0x0000224a jne          LBB0_493
	0x4c, 0x01, 0xca, //0x00002250 addq         %r9, %rdx
	0x48, 0x89, 0x54, 0x24, 0x28, //0x00002253 movq         %rdx, $40(%rsp)
	//0x00002258 LBB0_416
	0x85, 0xff, //0x00002258 testl        %edi, %edi
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x0000225a je           LBB0_419
	0x0f, 0xbc, 0xd7, //0x00002260 bsfl         %edi, %edx
	0x49, 0x83, 0xff, 0xff, //0x00002263 cmpq         $-1, %r15
	0x0f, 0x85, 0xf0, 0x06, 0x00, 0x00, //0x00002267 jne          LBB0_493
	0x4c, 0x01, 0xca, //0x0000226d addq         %r9, %rdx
	0x49, 0x89, 0xd7, //0x00002270 movq         %rdx, %r15
	//0x00002273 LBB0_419
	0x85, 0xf6, //0x00002273 testl        %esi, %esi
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x00002275 je           LBB0_422
	0x0f, 0xbc, 0xd6, //0x0000227b bsfl         %esi, %edx
	0x49, 0x83, 0xf8, 0xff, //0x0000227e cmpq         $-1, %r8
	0x0f, 0x85, 0xd5, 0x06, 0x00, 0x00, //0x00002282 jne          LBB0_493
	0x4c, 0x01, 0xca, //0x00002288 addq         %r9, %rdx
	0x49, 0x89, 0xd0, //0x0000228b movq         %rdx, %r8
	//0x0000228e LBB0_422
	0x83, 0xf9, 0x20, //0x0000228e cmpl         $32, %ecx
	0x0f, 0x85, 0xf9, 0x01, 0x00, 0x00, //0x00002291 jne          LBB0_456
	0x49, 0x83, 0xc2, 0xe0, //0x00002297 addq         $-32, %r10
	0x49, 0x83, 0xc1, 0x20, //0x0000229b addq         $32, %r9
	0x49, 0x83, 0xfa, 0x1f, //0x0000229f cmpq         $31, %r10
	0x0f, 0x87, 0xf7, 0xfe, 0xff, 0xff, //0x000022a3 ja           LBB0_408
	0xc5, 0xf8, 0x77, //0x000022a9 vzeroupper   
	0x4d, 0x01, 0xd9, //0x000022ac addq         %r11, %r9
	0x4c, 0x89, 0x6c, 0x24, 0x30, //0x000022af movq         %r13, $48(%rsp)
	0x49, 0x83, 0xfa, 0x10, //0x000022b4 cmpq         $16, %r10
	0x0f, 0x82, 0x35, 0x01, 0x00, 0x00, //0x000022b8 jb           LBB0_443
	//0x000022be LBB0_425
	0x4c, 0x89, 0xcb, //0x000022be movq         %r9, %rbx
	0x4c, 0x29, 0xdb, //0x000022c1 subq         %r11, %rbx
	0x45, 0x31, 0xed, //0x000022c4 xorl         %r13d, %r13d
	//0x000022c7 LBB0_426
	0xc4, 0x81, 0x7a, 0x6f, 0x04, 0x29, //0x000022c7 vmovdqu      (%r9,%r13), %xmm0
	0xc5, 0xf9, 0x64, 0x0d, 0x6b, 0xdd, 0xff, 0xff, //0x000022cd vpcmpgtb     $-8853(%rip), %xmm0, %xmm1  /* LCPI0_20+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x15, 0x73, 0xdd, 0xff, 0xff, //0x000022d5 vmovdqu      $-8845(%rip), %xmm2  /* LCPI0_21+0(%rip) */
	0xc5, 0xe9, 0x64, 0xd0, //0x000022dd vpcmpgtb     %xmm0, %xmm2, %xmm2
	0xc5, 0xf1, 0xdb, 0xca, //0x000022e1 vpand        %xmm2, %xmm1, %xmm1
	0xc5, 0xf9, 0x74, 0x15, 0x73, 0xdd, 0xff, 0xff, //0x000022e5 vpcmpeqb     $-8845(%rip), %xmm0, %xmm2  /* LCPI0_22+0(%rip) */
	0xc5, 0xf9, 0x74, 0x1d, 0x7b, 0xdd, 0xff, 0xff, //0x000022ed vpcmpeqb     $-8837(%rip), %xmm0, %xmm3  /* LCPI0_23+0(%rip) */
	0xc5, 0xe1, 0xeb, 0xd2, //0x000022f5 vpor         %xmm2, %xmm3, %xmm2
	0xc5, 0xf9, 0xdb, 0x1d, 0x1f, 0xdd, 0xff, 0xff, //0x000022f9 vpand        $-8929(%rip), %xmm0, %xmm3  /* LCPI0_12+0(%rip) */
	0xc5, 0xf9, 0x74, 0x05, 0x77, 0xdd, 0xff, 0xff, //0x00002301 vpcmpeqb     $-8841(%rip), %xmm0, %xmm0  /* LCPI0_24+0(%rip) */
	0xc5, 0xe1, 0x74, 0x1d, 0x7f, 0xdd, 0xff, 0xff, //0x00002309 vpcmpeqb     $-8833(%rip), %xmm3, %xmm3  /* LCPI0_25+0(%rip) */
	0xc5, 0xe1, 0xeb, 0xe0, //0x00002311 vpor         %xmm0, %xmm3, %xmm4
	0xc5, 0xe9, 0xeb, 0xc9, //0x00002315 vpor         %xmm1, %xmm2, %xmm1
	0xc5, 0xd9, 0xeb, 0xc9, //0x00002319 vpor         %xmm1, %xmm4, %xmm1
	0xc5, 0x79, 0xd7, 0xd8, //0x0000231d vpmovmskb    %xmm0, %r11d
	0xc5, 0xf9, 0xd7, 0xd3, //0x00002321 vpmovmskb    %xmm3, %edx
	0xc5, 0xf9, 0xd7, 0xfa, //0x00002325 vpmovmskb    %xmm2, %edi
	0xc5, 0xf9, 0xd7, 0xc1, //0x00002329 vpmovmskb    %xmm1, %eax
	0xf7, 0xd0, //0x0000232d notl         %eax
	0x0f, 0xbc, 0xc8, //0x0000232f bsfl         %eax, %ecx
	0x83, 0xf9, 0x10, //0x00002332 cmpl         $16, %ecx
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00002335 je           LBB0_428
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x0000233b movl         $-1, %eax
	0xd3, 0xe0, //0x00002340 shll         %cl, %eax
	0xf7, 0xd0, //0x00002342 notl         %eax
	0x41, 0x21, 0xc3, //0x00002344 andl         %eax, %r11d
	0x21, 0xc2, //0x00002347 andl         %eax, %edx
	0x21, 0xf8, //0x00002349 andl         %edi, %eax
	0x89, 0xc7, //0x0000234b movl         %eax, %edi
	//0x0000234d LBB0_428
	0x41, 0x8d, 0x73, 0xff, //0x0000234d leal         $-1(%r11), %esi
	0x44, 0x21, 0xde, //0x00002351 andl         %r11d, %esi
	0x0f, 0x85, 0xe8, 0x06, 0x00, 0x00, //0x00002354 jne          LBB0_499
	0x8d, 0x72, 0xff, //0x0000235a leal         $-1(%rdx), %esi
	0x21, 0xd6, //0x0000235d andl         %edx, %esi
	0x0f, 0x85, 0xdd, 0x06, 0x00, 0x00, //0x0000235f jne          LBB0_499
	0x8d, 0x77, 0xff, //0x00002365 leal         $-1(%rdi), %esi
	0x21, 0xfe, //0x00002368 andl         %edi, %esi
	0x0f, 0x85, 0xd2, 0x06, 0x00, 0x00, //0x0000236a jne          LBB0_499
	0x45, 0x85, 0xdb, //0x00002370 testl        %r11d, %r11d
	0x0f, 0x84, 0x1b, 0x00, 0x00, 0x00, //0x00002373 je           LBB0_434
	0x41, 0x0f, 0xbc, 0xf3, //0x00002379 bsfl         %r11d, %esi
	0x48, 0x83, 0x7c, 0x24, 0x28, 0xff, //0x0000237d cmpq         $-1, $40(%rsp)
	0x0f, 0x85, 0x9d, 0x07, 0x00, 0x00, //0x00002383 jne          LBB0_509
	0x48, 0x01, 0xde, //0x00002389 addq         %rbx, %rsi
	0x4c, 0x01, 0xee, //0x0000238c addq         %r13, %rsi
	0x48, 0x89, 0x74, 0x24, 0x28, //0x0000238f movq         %rsi, $40(%rsp)
	//0x00002394 LBB0_434
	0x4c, 0x8b, 0x5c, 0x24, 0x38, //0x00002394 movq         $56(%rsp), %r11
	0x85, 0xd2, //0x00002399 testl        %edx, %edx
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x0000239b je           LBB0_437
	0x0f, 0xbc, 0xd2, //0x000023a1 bsfl         %edx, %edx
	0x49, 0x83, 0xff, 0xff, //0x000023a4 cmpq         $-1, %r15
	0x0f, 0x85, 0xe8, 0x06, 0x00, 0x00, //0x000023a8 jne          LBB0_503
	0x48, 0x01, 0xda, //0x000023ae addq         %rbx, %rdx
	0x4c, 0x01, 0xea, //0x000023b1 addq         %r13, %rdx
	0x49, 0x89, 0xd7, //0x000023b4 movq         %rdx, %r15
	//0x000023b7 LBB0_437
	0x85, 0xff, //0x000023b7 testl        %edi, %edi
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x000023b9 je           LBB0_440
	0x0f, 0xbc, 0xd7, //0x000023bf bsfl         %edi, %edx
	0x49, 0x83, 0xf8, 0xff, //0x000023c2 cmpq         $-1, %r8
	0x0f, 0x85, 0xca, 0x06, 0x00, 0x00, //0x000023c6 jne          LBB0_503
	0x48, 0x01, 0xda, //0x000023cc addq         %rbx, %rdx
	0x4c, 0x01, 0xea, //0x000023cf addq         %r13, %rdx
	0x49, 0x89, 0xd0, //0x000023d2 movq         %rdx, %r8
	//0x000023d5 LBB0_440
	0x83, 0xf9, 0x10, //0x000023d5 cmpl         $16, %ecx
	0x0f, 0x85, 0x82, 0x01, 0x00, 0x00, //0x000023d8 jne          LBB0_466
	0x49, 0x83, 0xc2, 0xf0, //0x000023de addq         $-16, %r10
	0x49, 0x83, 0xc5, 0x10, //0x000023e2 addq         $16, %r13
	0x49, 0x83, 0xfa, 0x0f, //0x000023e6 cmpq         $15, %r10
	0x0f, 0x87, 0xd7, 0xfe, 0xff, 0xff, //0x000023ea ja           LBB0_426
	0x4d, 0x01, 0xe9, //0x000023f0 addq         %r13, %r9
	//0x000023f3 LBB0_443
	0x4d, 0x85, 0xd2, //0x000023f3 testq        %r10, %r10
	0x4c, 0x8b, 0x6c, 0x24, 0x30, //0x000023f6 movq         $48(%rsp), %r13
	0x0f, 0x84, 0x89, 0x01, 0x00, 0x00, //0x000023fb je           LBB0_468
	0x4b, 0x8d, 0x34, 0x11, //0x00002401 leaq         (%r9,%r10), %rsi
	0x4c, 0x89, 0xcf, //0x00002405 movq         %r9, %rdi
	0x4c, 0x29, 0xdf, //0x00002408 subq         %r11, %rdi
	0x31, 0xc9, //0x0000240b xorl         %ecx, %ecx
	0xe9, 0x1b, 0x00, 0x00, 0x00, //0x0000240d jmp          LBB0_448
	//0x00002412 LBB0_445
	0x49, 0x83, 0xf8, 0xff, //0x00002412 cmpq         $-1, %r8
	0x0f, 0x85, 0x2e, 0x06, 0x00, 0x00, //0x00002416 jne          LBB0_500
	0x4c, 0x8d, 0x04, 0x0f, //0x0000241c leaq         (%rdi,%rcx), %r8
	//0x00002420 .p2align 4, 0x90
	//0x00002420 LBB0_447
	0x48, 0x83, 0xc1, 0x01, //0x00002420 addq         $1, %rcx
	0x49, 0x39, 0xca, //0x00002424 cmpq         %rcx, %r10
	0x0f, 0x84, 0xeb, 0x04, 0x00, 0x00, //0x00002427 je           LBB0_491
	//0x0000242d LBB0_448
	0x41, 0x0f, 0xbe, 0x14, 0x09, //0x0000242d movsbl       (%r9,%rcx), %edx
	0x8d, 0x42, 0xd0, //0x00002432 leal         $-48(%rdx), %eax
	0x83, 0xf8, 0x0a, //0x00002435 cmpl         $10, %eax
	0x0f, 0x82, 0xe2, 0xff, 0xff, 0xff, //0x00002438 jb           LBB0_447
	0x8d, 0x5a, 0xd5, //0x0000243e leal         $-43(%rdx), %ebx
	0x83, 0xfb, 0x1a, //0x00002441 cmpl         $26, %ebx
	0x0f, 0x87, 0x2a, 0x00, 0x00, 0x00, //0x00002444 ja           LBB0_453
	0x48, 0x8d, 0x15, 0x97, 0x1b, 0x00, 0x00, //0x0000244a leaq         $7063(%rip), %rdx  /* LJTI0_2+0(%rip) */
	0x48, 0x63, 0x04, 0x9a, //0x00002451 movslq       (%rdx,%rbx,4), %rax
	0x48, 0x01, 0xd0, //0x00002455 addq         %rdx, %rax
	0xff, 0xe0, //0x00002458 jmpq         *%rax
	//0x0000245a LBB0_451
	0x48, 0x83, 0x7c, 0x24, 0x28, 0xff, //0x0000245a cmpq         $-1, $40(%rsp)
	0x0f, 0x85, 0xe4, 0x05, 0x00, 0x00, //0x00002460 jne          LBB0_500
	0x48, 0x8d, 0x04, 0x0f, //0x00002466 leaq         (%rdi,%rcx), %rax
	0x48, 0x89, 0x44, 0x24, 0x28, //0x0000246a movq         %rax, $40(%rsp)
	0xe9, 0xac, 0xff, 0xff, 0xff, //0x0000246f jmp          LBB0_447
	//0x00002474 LBB0_453
	0x83, 0xfa, 0x65, //0x00002474 cmpl         $101, %edx
	0x0f, 0x85, 0x0a, 0x01, 0x00, 0x00, //0x00002477 jne          LBB0_467
	//0x0000247d LBB0_454
	0x49, 0x83, 0xff, 0xff, //0x0000247d cmpq         $-1, %r15
	0x0f, 0x85, 0xc3, 0x05, 0x00, 0x00, //0x00002481 jne          LBB0_500
	0x4c, 0x8d, 0x3c, 0x0f, //0x00002487 leaq         (%rdi,%rcx), %r15
	0xe9, 0x90, 0xff, 0xff, 0xff, //0x0000248b jmp          LBB0_447
	//0x00002490 LBB0_456
	0x4c, 0x01, 0xc9, //0x00002490 addq         %r9, %rcx
	0x4c, 0x01, 0xd9, //0x00002493 addq         %r11, %rcx
	0xc5, 0xf8, 0x77, //0x00002496 vzeroupper   
	0x49, 0x89, 0xc9, //0x00002499 movq         %rcx, %r9
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x0000249c movq         $-1, %rcx
	0x48, 0x8b, 0x54, 0x24, 0x28, //0x000024a3 movq         $40(%rsp), %rdx
	0x48, 0x85, 0xd2, //0x000024a8 testq        %rdx, %rdx
	0x0f, 0x85, 0xee, 0x00, 0x00, 0x00, //0x000024ab jne          LBB0_469
	0xe9, 0x5a, 0x18, 0x00, 0x00, //0x000024b1 jmp          LBB0_709
	//0x000024b6 LBB0_676
	0x4d, 0x29, 0xf4, //0x000024b6 subq         %r14, %r12
	0x49, 0x01, 0xd4, //0x000024b9 addq         %rdx, %r12
	0x49, 0x39, 0xdc, //0x000024bc cmpq         %rbx, %r12
	0x0f, 0x82, 0xcd, 0xe0, 0xff, 0xff, //0x000024bf jb           LBB0_35
	0xe9, 0x48, 0x16, 0x00, 0x00, //0x000024c5 jmp          LBB0_677
	//0x000024ca LBB0_457
	0x4d, 0x89, 0xfd, //0x000024ca movq         %r15, %r13
	0xe9, 0x29, 0xf1, 0xff, 0xff, //0x000024cd jmp          LBB0_279
	//0x000024d2 LBB0_458
	0xba, 0x40, 0x00, 0x00, 0x00, //0x000024d2 movl         $64, %edx
	//0x000024d7 LBB0_459
	0x4d, 0x89, 0xd3, //0x000024d7 movq         %r10, %r11
	0x48, 0x39, 0xc2, //0x000024da cmpq         %rax, %rdx
	0x0f, 0x82, 0x88, 0x18, 0x00, 0x00, //0x000024dd jb           LBB0_719
	0xc5, 0x7d, 0x7f, 0xea, //0x000024e3 vmovdqa      %ymm13, %ymm2
	0x48, 0x01, 0xc1, //0x000024e7 addq         %rax, %rcx
	0x48, 0x83, 0xc1, 0x01, //0x000024ea addq         $1, %rcx
	//0x000024ee LBB0_461
	0x48, 0x85, 0xc9, //0x000024ee testq        %rcx, %rcx
	0x0f, 0x88, 0xfd, 0x17, 0x00, 0x00, //0x000024f1 js           LBB0_706
	0x49, 0x89, 0x4d, 0x00, //0x000024f7 movq         %rcx, (%r13)
	0x4d, 0x85, 0xc9, //0x000024fb testq        %r9, %r9
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x000024fe movq         $24(%rsp), %r11
	0x48, 0x8b, 0x7c, 0x24, 0x10, //0x00002503 movq         $16(%rsp), %rdi
	0x4c, 0x8b, 0x7c, 0x24, 0x08, //0x00002508 movq         $8(%rsp), %r15
	0xc5, 0x7d, 0x6f, 0xea, //0x0000250d vmovdqa      %ymm2, %ymm13
	0x0f, 0x8f, 0x89, 0xde, 0xff, 0xff, //0x00002511 jg           LBB0_3
	0xe9, 0x1d, 0x16, 0x00, 0x00, //0x00002517 jmp          LBB0_463
	//0x0000251c LBB0_156
	0x4d, 0x85, 0xff, //0x0000251c testq        %r15, %r15
	0x0f, 0x85, 0x37, 0x07, 0x00, 0x00, //0x0000251f jne          LBB0_522
	0x49, 0x01, 0xf1, //0x00002525 addq         %rsi, %r9
	0x49, 0x29, 0xf2, //0x00002528 subq         %rsi, %r10
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x0000252b movq         $24(%rsp), %r11
	0x48, 0x8b, 0x7c, 0x24, 0x10, //0x00002530 movq         $16(%rsp), %rdi
	0x4c, 0x8b, 0x7c, 0x24, 0x08, //0x00002535 movq         $8(%rsp), %r15
	//0x0000253a LBB0_158
	0x4d, 0x85, 0xd2, //0x0000253a testq        %r10, %r10
	0x0f, 0x8f, 0x9d, 0x07, 0x00, 0x00, //0x0000253d jg           LBB0_526
	0xe9, 0x4d, 0x16, 0x00, 0x00, //0x00002543 jmp          LBB0_711
	//0x00002548 LBB0_464
	0xc5, 0x7d, 0x7f, 0xe8, //0x00002548 vmovdqa      %ymm13, %ymm0
	0x0f, 0xbc, 0xc3, //0x0000254c bsfl         %ebx, %eax
	0xe9, 0xb9, 0x01, 0x00, 0x00, //0x0000254f jmp          LBB0_484
	//0x00002554 LBB0_465
	0x66, 0x0f, 0xbc, 0xc0, //0x00002554 bsfw         %ax, %ax
	0x0f, 0xb7, 0xc8, //0x00002558 movzwl       %ax, %ecx
	0xe9, 0xf7, 0xe9, 0xff, 0xff, //0x0000255b jmp          LBB0_176
	//0x00002560 LBB0_466
	0x89, 0xc8, //0x00002560 movl         %ecx, %eax
	0x49, 0x01, 0xc1, //0x00002562 addq         %rax, %r9
	0x4d, 0x01, 0xe9, //0x00002565 addq         %r13, %r9
	0x4c, 0x8b, 0x6c, 0x24, 0x30, //0x00002568 movq         $48(%rsp), %r13
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x0000256d movq         $-1, %rcx
	0x48, 0x8b, 0x54, 0x24, 0x28, //0x00002574 movq         $40(%rsp), %rdx
	0x48, 0x85, 0xd2, //0x00002579 testq        %rdx, %rdx
	0x0f, 0x85, 0x1d, 0x00, 0x00, 0x00, //0x0000257c jne          LBB0_469
	0xe9, 0x89, 0x17, 0x00, 0x00, //0x00002582 jmp          LBB0_709
	//0x00002587 LBB0_467
	0x49, 0x01, 0xc9, //0x00002587 addq         %rcx, %r9
	//0x0000258a LBB0_468
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x0000258a movq         $-1, %rcx
	0x48, 0x8b, 0x54, 0x24, 0x28, //0x00002591 movq         $40(%rsp), %rdx
	0x48, 0x85, 0xd2, //0x00002596 testq        %rdx, %rdx
	0x0f, 0x84, 0x71, 0x17, 0x00, 0x00, //0x00002599 je           LBB0_709
	//0x0000259f LBB0_469
	0x4d, 0x85, 0xc0, //0x0000259f testq        %r8, %r8
	0x0f, 0x84, 0x68, 0x17, 0x00, 0x00, //0x000025a2 je           LBB0_709
	0x4d, 0x85, 0xff, //0x000025a8 testq        %r15, %r15
	0x0f, 0x84, 0x5f, 0x17, 0x00, 0x00, //0x000025ab je           LBB0_709
	0x4d, 0x29, 0xd9, //0x000025b1 subq         %r11, %r9
	0x49, 0x8d, 0x49, 0xff, //0x000025b4 leaq         $-1(%r9), %rcx
	0x48, 0x39, 0xca, //0x000025b8 cmpq         %rcx, %rdx
	0x0f, 0x84, 0x97, 0x00, 0x00, 0x00, //0x000025bb je           LBB0_477
	0x49, 0x39, 0xc8, //0x000025c1 cmpq         %rcx, %r8
	0x0f, 0x84, 0x8e, 0x00, 0x00, 0x00, //0x000025c4 je           LBB0_477
	0x49, 0x39, 0xcf, //0x000025ca cmpq         %rcx, %r15
	0x0f, 0x84, 0x85, 0x00, 0x00, 0x00, //0x000025cd je           LBB0_477
	0x4d, 0x85, 0xc0, //0x000025d3 testq        %r8, %r8
	0xc5, 0xfe, 0x6f, 0x2d, 0xe2, 0xda, 0xff, 0xff, //0x000025d6 vmovdqu      $-9502(%rip), %ymm5  /* LCPI0_1+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0xfa, 0xda, 0xff, 0xff, //0x000025de vmovdqu      $-9478(%rip), %ymm6  /* LCPI0_2+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0x12, 0xdb, 0xff, 0xff, //0x000025e6 vmovdqu      $-9454(%rip), %ymm7  /* LCPI0_3+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0x2a, 0xdb, 0xff, 0xff, //0x000025ee vmovdqu      $-9430(%rip), %ymm8  /* LCPI0_4+0(%rip) */
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x000025f6 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0x7e, 0x6f, 0x15, 0xfd, 0xdb, 0xff, 0xff, //0x000025fb vmovdqu      $-9219(%rip), %ymm10  /* LCPI0_14+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x05, 0x15, 0xdc, 0xff, 0xff, //0x00002603 vmovdqu      $-9195(%rip), %ymm0  /* LCPI0_15+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x3d, 0x2d, 0xdc, 0xff, 0xff, //0x0000260b vmovdqu      $-9171(%rip), %ymm15  /* LCPI0_16+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0xc5, 0xdb, 0xff, 0xff, //0x00002613 vmovdqu      $-9275(%rip), %ymm14  /* LCPI0_10+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0x7d, 0xdc, 0xff, 0xff, //0x0000261b vmovdqu      $-9091(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x1d, 0x95, 0xdb, 0xff, 0xff, //0x00002623 vmovdqu      $-9323(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0x0f, 0x8e, 0xa3, 0x00, 0x00, 0x00, //0x0000262b jle          LBB0_480
	0x49, 0x8d, 0x40, 0xff, //0x00002631 leaq         $-1(%r8), %rax
	0x49, 0x39, 0xc7, //0x00002635 cmpq         %rax, %r15
	0x0f, 0x84, 0x96, 0x00, 0x00, 0x00, //0x00002638 je           LBB0_480
	0x49, 0xf7, 0xd0, //0x0000263e notq         %r8
	0x4d, 0x89, 0xc1, //0x00002641 movq         %r8, %r9
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x00002644 movq         $24(%rsp), %r11
	0x48, 0x8b, 0x7c, 0x24, 0x10, //0x00002649 movq         $16(%rsp), %rdi
	0x4c, 0x8b, 0x7c, 0x24, 0x08, //0x0000264e movq         $8(%rsp), %r15
	0xe9, 0x67, 0x00, 0x00, 0x00, //0x00002653 jmp          LBB0_479
	//0x00002658 LBB0_477
	0x49, 0xf7, 0xd9, //0x00002658 negq         %r9
	//0x0000265b LBB0_478
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x0000265b movq         $24(%rsp), %r11
	0x48, 0x8b, 0x7c, 0x24, 0x10, //0x00002660 movq         $16(%rsp), %rdi
	0x4c, 0x8b, 0x7c, 0x24, 0x08, //0x00002665 movq         $8(%rsp), %r15
	0xc5, 0xfe, 0x6f, 0x2d, 0x4e, 0xda, 0xff, 0xff, //0x0000266a vmovdqu      $-9650(%rip), %ymm5  /* LCPI0_1+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0x66, 0xda, 0xff, 0xff, //0x00002672 vmovdqu      $-9626(%rip), %ymm6  /* LCPI0_2+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0x7e, 0xda, 0xff, 0xff, //0x0000267a vmovdqu      $-9602(%rip), %ymm7  /* LCPI0_3+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0x96, 0xda, 0xff, 0xff, //0x00002682 vmovdqu      $-9578(%rip), %ymm8  /* LCPI0_4+0(%rip) */
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x0000268a vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0x7e, 0x6f, 0x15, 0x69, 0xdb, 0xff, 0xff, //0x0000268f vmovdqu      $-9367(%rip), %ymm10  /* LCPI0_14+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x05, 0x81, 0xdb, 0xff, 0xff, //0x00002697 vmovdqu      $-9343(%rip), %ymm0  /* LCPI0_15+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x3d, 0x99, 0xdb, 0xff, 0xff, //0x0000269f vmovdqu      $-9319(%rip), %ymm15  /* LCPI0_16+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0x31, 0xdb, 0xff, 0xff, //0x000026a7 vmovdqu      $-9423(%rip), %ymm14  /* LCPI0_10+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0xe9, 0xdb, 0xff, 0xff, //0x000026af vmovdqu      $-9239(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x1d, 0x01, 0xdb, 0xff, 0xff, //0x000026b7 vmovdqu      $-9471(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	//0x000026bf LBB0_479
	0xc5, 0x7e, 0x6f, 0x1d, 0x79, 0xda, 0xff, 0xff, //0x000026bf vmovdqu      $-9607(%rip), %ymm11  /* LCPI0_5+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0x91, 0xda, 0xff, 0xff, //0x000026c7 vmovdqu      $-9583(%rip), %ymm12  /* LCPI0_6+0(%rip) */
	0xe9, 0xa4, 0x02, 0x00, 0x00, //0x000026cf jmp          LBB0_496
	//0x000026d4 LBB0_480
	0x48, 0x89, 0xd0, //0x000026d4 movq         %rdx, %rax
	0x4c, 0x09, 0xf8, //0x000026d7 orq          %r15, %rax
	0x0f, 0x99, 0xc1, //0x000026da setns        %cl
	0xc5, 0x7e, 0x6f, 0x1d, 0x5b, 0xda, 0xff, 0xff, //0x000026dd vmovdqu      $-9637(%rip), %ymm11  /* LCPI0_5+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0x73, 0xda, 0xff, 0xff, //0x000026e5 vmovdqu      $-9613(%rip), %ymm12  /* LCPI0_6+0(%rip) */
	0x0f, 0x88, 0xf8, 0x00, 0x00, 0x00, //0x000026ed js           LBB0_485
	0x4c, 0x39, 0xfa, //0x000026f3 cmpq         %r15, %rdx
	0x0f, 0x8c, 0xef, 0x00, 0x00, 0x00, //0x000026f6 jl           LBB0_485
	0x48, 0xf7, 0xd2, //0x000026fc notq         %rdx
	0x49, 0x89, 0xd1, //0x000026ff movq         %rdx, %r9
	0xe9, 0x62, 0x02, 0x00, 0x00, //0x00002702 jmp          LBB0_495
	//0x00002707 LBB0_483
	0xc5, 0x7d, 0x7f, 0xe8, //0x00002707 vmovdqa      %ymm13, %ymm0
	0x89, 0xd0, //0x0000270b movl         %edx, %eax
	//0x0000270d LBB0_484
	0x49, 0xf7, 0xd5, //0x0000270d notq         %r13
	0x49, 0x29, 0xc5, //0x00002710 subq         %rax, %r13
	0x4c, 0x8b, 0x7c, 0x24, 0x08, //0x00002713 movq         $8(%rsp), %r15
	0xe9, 0x04, 0xf0, 0xff, 0xff, //0x00002718 jmp          LBB0_290
	//0x0000271d LBB0_187
	0x4d, 0x85, 0xff, //0x0000271d testq        %r15, %r15
	0x0f, 0x85, 0x9f, 0x07, 0x00, 0x00, //0x00002720 jne          LBB0_547
	0x49, 0x01, 0xf1, //0x00002726 addq         %rsi, %r9
	0x49, 0x29, 0xf2, //0x00002729 subq         %rsi, %r10
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x0000272c movq         $24(%rsp), %r11
	0x48, 0x8b, 0x7c, 0x24, 0x10, //0x00002731 movq         $16(%rsp), %rdi
	0x4c, 0x8b, 0x7c, 0x24, 0x08, //0x00002736 movq         $8(%rsp), %r15
	//0x0000273b LBB0_189
	0x4d, 0x85, 0xd2, //0x0000273b testq        %r10, %r10
	0x0f, 0x8f, 0xfa, 0x07, 0x00, 0x00, //0x0000273e jg           LBB0_551
	0xe9, 0x4c, 0x14, 0x00, 0x00, //0x00002744 jmp          LBB0_711
	//0x00002749 LBB0_54
	0x4c, 0x01, 0xf1, //0x00002749 addq         %r14, %rcx
	0x48, 0x83, 0xfb, 0x20, //0x0000274c cmpq         $32, %rbx
	0x0f, 0x82, 0x5f, 0x03, 0x00, 0x00, //0x00002750 jb           LBB0_505
	//0x00002756 LBB0_55
	0xc5, 0xfe, 0x6f, 0x01, //0x00002756 vmovdqu      (%rcx), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x0000275a vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xf1, //0x0000275e vpmovmskb    %ymm1, %esi
	0xc5, 0xfd, 0x74, 0xc7, //0x00002762 vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xd0, //0x00002766 vpmovmskb    %ymm0, %edx
	0x85, 0xd2, //0x0000276a testl        %edx, %edx
	0x4d, 0x89, 0xdf, //0x0000276c movq         %r11, %r15
	0x0f, 0x85, 0xa6, 0x05, 0x00, 0x00, //0x0000276f jne          LBB0_529
	0x4d, 0x85, 0xd2, //0x00002775 testq        %r10, %r10
	0x0f, 0x85, 0xcb, 0x05, 0x00, 0x00, //0x00002778 jne          LBB0_531
	0x45, 0x31, 0xd2, //0x0000277e xorl         %r10d, %r10d
	0x48, 0x85, 0xf6, //0x00002781 testq        %rsi, %rsi
	0x0f, 0x84, 0x34, 0x06, 0x00, 0x00, //0x00002784 je           LBB0_533
	//0x0000278a LBB0_58
	0x48, 0x0f, 0xbc, 0xc6, //0x0000278a bsfq         %rsi, %rax
	0x49, 0x89, 0xca, //0x0000278e movq         %rcx, %r10
	0x4d, 0x29, 0xf2, //0x00002791 subq         %r14, %r10
	0x49, 0x01, 0xc2, //0x00002794 addq         %rax, %r10
	0x49, 0x83, 0xc2, 0x01, //0x00002797 addq         $1, %r10
	0xe9, 0x80, 0xeb, 0xff, 0xff, //0x0000279b jmp          LBB0_232
	//0x000027a0 LBB0_199
	0x4d, 0x01, 0xf2, //0x000027a0 addq         %r14, %r10
	0x48, 0x83, 0xfb, 0x20, //0x000027a3 cmpq         $32, %rbx
	0x0f, 0x82, 0xef, 0x08, 0x00, 0x00, //0x000027a7 jb           LBB0_564
	//0x000027ad LBB0_200
	0x4c, 0x89, 0xd1, //0x000027ad movq         %r10, %rcx
	0xc4, 0xc1, 0x7e, 0x6f, 0x02, //0x000027b0 vmovdqu      (%r10), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x000027b5 vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0x7d, 0xd7, 0xd1, //0x000027b9 vpmovmskb    %ymm1, %r10d
	0xc5, 0xfd, 0x74, 0xcf, //0x000027bd vpcmpeqb     %ymm7, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xd1, //0x000027c1 vpmovmskb    %ymm1, %edx
	0x85, 0xd2, //0x000027c5 testl        %edx, %edx
	0x0f, 0x85, 0xc5, 0x07, 0x00, 0x00, //0x000027c7 jne          LBB0_554
	0x4d, 0x85, 0xdb, //0x000027cd testq        %r11, %r11
	0x0f, 0x85, 0xf2, 0x07, 0x00, 0x00, //0x000027d0 jne          LBB0_556
	0x45, 0x31, 0xdb, //0x000027d6 xorl         %r11d, %r11d
	0x4d, 0x85, 0xd2, //0x000027d9 testq        %r10, %r10
	0x0f, 0x84, 0x60, 0x08, 0x00, 0x00, //0x000027dc je           LBB0_558
	//0x000027e2 LBB0_203
	0x49, 0x0f, 0xbc, 0xd2, //0x000027e2 bsfq         %r10, %rdx
	0xe9, 0x5c, 0x08, 0x00, 0x00, //0x000027e6 jmp          LBB0_559
	//0x000027eb LBB0_485
	0x49, 0x8d, 0x47, 0xff, //0x000027eb leaq         $-1(%r15), %rax
	0x48, 0x39, 0xc2, //0x000027ef cmpq         %rax, %rdx
	0x49, 0xf7, 0xd7, //0x000027f2 notq         %r15
	0x4d, 0x0f, 0x45, 0xf9, //0x000027f5 cmovneq      %r9, %r15
	0x84, 0xc9, //0x000027f9 testb        %cl, %cl
	0x4d, 0x0f, 0x45, 0xcf, //0x000027fb cmovneq      %r15, %r9
	0xe9, 0x65, 0x01, 0x00, 0x00, //0x000027ff jmp          LBB0_495
	//0x00002804 LBB0_486
	0xc5, 0x7d, 0x7f, 0xe8, //0x00002804 vmovdqa      %ymm13, %ymm0
	0x0f, 0xbc, 0xc3, //0x00002808 bsfl         %ebx, %eax
	0xe9, 0x3a, 0x00, 0x00, 0x00, //0x0000280b jmp          LBB0_490
	//0x00002810 LBB0_487
	0xc5, 0x7d, 0x7f, 0xe8, //0x00002810 vmovdqa      %ymm13, %ymm0
	0x48, 0x8b, 0x5c, 0x24, 0x20, //0x00002814 movq         $32(%rsp), %rbx
	0x49, 0x01, 0xde, //0x00002819 addq         %rbx, %r14
	0x4d, 0x29, 0xee, //0x0000281c subq         %r13, %r14
	0x49, 0x29, 0xce, //0x0000281f subq         %rcx, %r14
	0x49, 0x83, 0xc6, 0xfe, //0x00002822 addq         $-2, %r14
	0x4d, 0x89, 0xf5, //0x00002826 movq         %r14, %r13
	0x4c, 0x8b, 0x7c, 0x24, 0x08, //0x00002829 movq         $8(%rsp), %r15
	0xe9, 0xf3, 0xee, 0xff, 0xff, //0x0000282e jmp          LBB0_291
	//0x00002833 LBB0_488
	0x4d, 0x01, 0xf4, //0x00002833 addq         %r14, %r12
	0x48, 0x85, 0xc0, //0x00002836 testq        %rax, %rax
	0x0f, 0x85, 0xe9, 0xdc, 0xff, 0xff, //0x00002839 jne          LBB0_28
	0xe9, 0x1e, 0xdd, 0xff, 0xff, //0x0000283f jmp          LBB0_33
	//0x00002844 LBB0_489
	0xc5, 0x7d, 0x7f, 0xe8, //0x00002844 vmovdqa      %ymm13, %ymm0
	0x89, 0xd0, //0x00002848 movl         %edx, %eax
	//0x0000284a LBB0_490
	0x48, 0x8b, 0x5c, 0x24, 0x20, //0x0000284a movq         $32(%rsp), %rbx
	0x49, 0x01, 0xde, //0x0000284f addq         %rbx, %r14
	0x4d, 0x29, 0xee, //0x00002852 subq         %r13, %r14
	0x49, 0x29, 0xc6, //0x00002855 subq         %rax, %r14
	0x4d, 0x29, 0xfe, //0x00002858 subq         %r15, %r14
	0x49, 0x83, 0xc6, 0xfe, //0x0000285b addq         $-2, %r14
	0x4d, 0x89, 0xf5, //0x0000285f movq         %r14, %r13
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x00002862 movq         $24(%rsp), %r11
	0x48, 0x8b, 0x7c, 0x24, 0x10, //0x00002867 movq         $16(%rsp), %rdi
	0x4c, 0x8b, 0x7c, 0x24, 0x08, //0x0000286c movq         $8(%rsp), %r15
	0xe9, 0xb0, 0xee, 0xff, 0xff, //0x00002871 jmp          LBB0_291
	//0x00002876 LBB0_139
	0x4c, 0x01, 0xf3, //0x00002876 addq         %r14, %rbx
	0x48, 0x83, 0xf9, 0x20, //0x00002879 cmpq         $32, %rcx
	0x0f, 0x82, 0xda, 0x02, 0x00, 0x00, //0x0000287d jb           LBB0_512
	//0x00002883 LBB0_140
	0xc5, 0xfe, 0x6f, 0x03, //0x00002883 vmovdqu      (%rbx), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x00002887 vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xf1, //0x0000288b vpmovmskb    %ymm1, %esi
	0xc5, 0xfd, 0x74, 0xc7, //0x0000288f vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xd0, //0x00002893 vpmovmskb    %ymm0, %edx
	0x85, 0xd2, //0x00002897 testl        %edx, %edx
	0x4d, 0x89, 0xdf, //0x00002899 movq         %r11, %r15
	0x0f, 0x85, 0xc0, 0x08, 0x00, 0x00, //0x0000289c jne          LBB0_575
	0x4d, 0x85, 0xd2, //0x000028a2 testq        %r10, %r10
	0x0f, 0x85, 0xe5, 0x08, 0x00, 0x00, //0x000028a5 jne          LBB0_577
	0x45, 0x31, 0xd2, //0x000028ab xorl         %r10d, %r10d
	0x48, 0x85, 0xf6, //0x000028ae testq        %rsi, %rsi
	0x0f, 0x84, 0x4e, 0x09, 0x00, 0x00, //0x000028b1 je           LBB0_579
	//0x000028b7 LBB0_143
	0x48, 0x0f, 0xbc, 0xc6, //0x000028b7 bsfq         %rsi, %rax
	0x49, 0x89, 0xda, //0x000028bb movq         %rbx, %r10
	0x4d, 0x29, 0xf2, //0x000028be subq         %r14, %r10
	0x49, 0x01, 0xc2, //0x000028c1 addq         %rax, %r10
	0x49, 0x83, 0xc2, 0x01, //0x000028c4 addq         $1, %r10
	0xe9, 0xbf, 0xee, 0xff, 0xff, //0x000028c8 jmp          LBB0_298
	//0x000028cd LBB0_220
	0x4d, 0x01, 0xf2, //0x000028cd addq         %r14, %r10
	0x48, 0x83, 0xf9, 0x20, //0x000028d0 cmpq         $32, %rcx
	0x0f, 0x82, 0x3c, 0x0b, 0x00, 0x00, //0x000028d4 jb           LBB0_603
	//0x000028da LBB0_221
	0x4c, 0x89, 0xd3, //0x000028da movq         %r10, %rbx
	0xc4, 0xc1, 0x7e, 0x6f, 0x02, //0x000028dd vmovdqu      (%r10), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x000028e2 vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0x7d, 0xd7, 0xd1, //0x000028e6 vpmovmskb    %ymm1, %r10d
	0xc5, 0xfd, 0x74, 0xcf, //0x000028ea vpcmpeqb     %ymm7, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xd1, //0x000028ee vpmovmskb    %ymm1, %edx
	0x85, 0xd2, //0x000028f2 testl        %edx, %edx
	0x0f, 0x85, 0x12, 0x0a, 0x00, 0x00, //0x000028f4 jne          LBB0_593
	0x4d, 0x85, 0xdb, //0x000028fa testq        %r11, %r11
	0x0f, 0x85, 0x3f, 0x0a, 0x00, 0x00, //0x000028fd jne          LBB0_595
	0x45, 0x31, 0xdb, //0x00002903 xorl         %r11d, %r11d
	0x4d, 0x85, 0xd2, //0x00002906 testq        %r10, %r10
	0x0f, 0x84, 0xad, 0x0a, 0x00, 0x00, //0x00002909 je           LBB0_597
	//0x0000290f LBB0_224
	0x49, 0x0f, 0xbc, 0xd2, //0x0000290f bsfq         %r10, %rdx
	0xe9, 0xa9, 0x0a, 0x00, 0x00, //0x00002913 jmp          LBB0_598
	//0x00002918 LBB0_491
	0x49, 0x89, 0xf1, //0x00002918 movq         %rsi, %r9
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x0000291b movq         $-1, %rcx
	0x48, 0x8b, 0x54, 0x24, 0x28, //0x00002922 movq         $40(%rsp), %rdx
	0x48, 0x85, 0xd2, //0x00002927 testq        %rdx, %rdx
	0x0f, 0x85, 0x6f, 0xfc, 0xff, 0xff, //0x0000292a jne          LBB0_469
	0xe9, 0xdb, 0x13, 0x00, 0x00, //0x00002930 jmp          LBB0_709
	//0x00002935 LBB0_492
	0xc5, 0x7d, 0x7f, 0xe8, //0x00002935 vmovdqa      %ymm13, %ymm0
	0x0f, 0xbc, 0xc3, //0x00002939 bsfl         %ebx, %eax
	0xe9, 0x22, 0x00, 0x00, 0x00, //0x0000293c jmp          LBB0_494
	//0x00002941 LBB0_697
	0x66, 0x0f, 0xbc, 0xc0, //0x00002941 bsfw         %ax, %ax
	0x0f, 0xb7, 0xc8, //0x00002945 movzwl       %ax, %ecx
	0x48, 0x29, 0xd1, //0x00002948 subq         %rdx, %rcx
	0x49, 0x89, 0x4d, 0x00, //0x0000294b movq         %rcx, (%r13)
	0x48, 0x85, 0xdb, //0x0000294f testq        %rbx, %rbx
	0x0f, 0x8f, 0x48, 0xda, 0xff, 0xff, //0x00002952 jg           LBB0_3
	0xe9, 0x74, 0x13, 0x00, 0x00, //0x00002958 jmp          LBB0_698
	//0x0000295d LBB0_493
	0xc5, 0x7d, 0x7f, 0xe8, //0x0000295d vmovdqa      %ymm13, %ymm0
	0x89, 0xd0, //0x00002961 movl         %edx, %eax
	//0x00002963 LBB0_494
	0x49, 0xf7, 0xd1, //0x00002963 notq         %r9
	0x49, 0x29, 0xc1, //0x00002966 subq         %rax, %r9
	//0x00002969 LBB0_495
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x00002969 movq         $24(%rsp), %r11
	0x48, 0x8b, 0x7c, 0x24, 0x10, //0x0000296e movq         $16(%rsp), %rdi
	0x4c, 0x8b, 0x7c, 0x24, 0x08, //0x00002973 movq         $8(%rsp), %r15
	//0x00002978 LBB0_496
	0x4d, 0x85, 0xc9, //0x00002978 testq        %r9, %r9
	0x0f, 0x88, 0x8c, 0x13, 0x00, 0x00, //0x0000297b js           LBB0_708
	0x49, 0x8b, 0x4d, 0x00, //0x00002981 movq         (%r13), %rcx
	0xc5, 0x7d, 0x6f, 0xe8, //0x00002985 vmovdqa      %ymm0, %ymm13
	//0x00002989 LBB0_498
	0x4c, 0x01, 0xc9, //0x00002989 addq         %r9, %rcx
	0x49, 0x89, 0x4d, 0x00, //0x0000298c movq         %rcx, (%r13)
	0x48, 0x83, 0x3c, 0x24, 0x00, //0x00002990 cmpq         $0, (%rsp)
	0x0f, 0x8f, 0x05, 0xda, 0xff, 0xff, //0x00002995 jg           LBB0_3
	0xe9, 0x31, 0x13, 0x00, 0x00, //0x0000299b jmp          LBB0_698
	//0x000029a0 LBB0_243
	0x4c, 0x01, 0xf7, //0x000029a0 addq         %r14, %rdi
	0x49, 0x83, 0xf8, 0x20, //0x000029a3 cmpq         $32, %r8
	0x0f, 0x82, 0x2f, 0x02, 0x00, 0x00, //0x000029a7 jb           LBB0_517
	//0x000029ad LBB0_244
	0xc5, 0xfe, 0x6f, 0x07, //0x000029ad vmovdqu      (%rdi), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x000029b1 vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xf1, //0x000029b5 vpmovmskb    %ymm1, %esi
	0xc5, 0xfd, 0x74, 0xc7, //0x000029b9 vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xd0, //0x000029bd vpmovmskb    %ymm0, %edx
	0x85, 0xd2, //0x000029c1 testl        %edx, %edx
	0x0f, 0x85, 0x68, 0x0d, 0x00, 0x00, //0x000029c3 jne          LBB0_630
	0x4d, 0x85, 0xd2, //0x000029c9 testq        %r10, %r10
	0x0f, 0x85, 0x95, 0x0d, 0x00, 0x00, //0x000029cc jne          LBB0_632
	0x45, 0x31, 0xd2, //0x000029d2 xorl         %r10d, %r10d
	0xc5, 0x7d, 0x7f, 0xea, //0x000029d5 vmovdqa      %ymm13, %ymm2
	0x48, 0x85, 0xf6, //0x000029d9 testq        %rsi, %rsi
	0x0f, 0x84, 0x02, 0x0e, 0x00, 0x00, //0x000029dc je           LBB0_634
	//0x000029e2 LBB0_247
	0x48, 0x0f, 0xbc, 0xc6, //0x000029e2 bsfq         %rsi, %rax
	0x48, 0x89, 0xf9, //0x000029e6 movq         %rdi, %rcx
	0x4c, 0x29, 0xf1, //0x000029e9 subq         %r14, %rcx
	0x48, 0x01, 0xc1, //0x000029ec addq         %rax, %rcx
	0x48, 0x83, 0xc1, 0x01, //0x000029ef addq         $1, %rcx
	0xe9, 0xf6, 0xfa, 0xff, 0xff, //0x000029f3 jmp          LBB0_461
	//0x000029f8 LBB0_394
	0x4c, 0x01, 0xf1, //0x000029f8 addq         %r14, %rcx
	0x49, 0x83, 0xf8, 0x20, //0x000029fb cmpq         $32, %r8
	0x0f, 0x82, 0x01, 0x02, 0x00, 0x00, //0x000029ff jb           LBB0_519
	//0x00002a05 LBB0_395
	0x48, 0x89, 0xcb, //0x00002a05 movq         %rcx, %rbx
	0xc5, 0xfe, 0x6f, 0x01, //0x00002a08 vmovdqu      (%rcx), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x00002a0c vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xc9, //0x00002a10 vpmovmskb    %ymm1, %ecx
	0xc5, 0xfd, 0x74, 0xcf, //0x00002a14 vpcmpeqb     %ymm7, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xd1, //0x00002a18 vpmovmskb    %ymm1, %edx
	0x85, 0xd2, //0x00002a1c testl        %edx, %edx
	0x0f, 0x85, 0x86, 0x0e, 0x00, 0x00, //0x00002a1e jne          LBB0_647
	0x4d, 0x85, 0xdb, //0x00002a24 testq        %r11, %r11
	0x0f, 0x85, 0xab, 0x0e, 0x00, 0x00, //0x00002a27 jne          LBB0_649
	0x45, 0x31, 0xdb, //0x00002a2d xorl         %r11d, %r11d
	0x48, 0x85, 0xc9, //0x00002a30 testq        %rcx, %rcx
	0x0f, 0x84, 0x10, 0x0f, 0x00, 0x00, //0x00002a33 je           LBB0_651
	//0x00002a39 LBB0_398
	0x48, 0x0f, 0xbc, 0xd1, //0x00002a39 bsfq         %rcx, %rdx
	0xe9, 0x0c, 0x0f, 0x00, 0x00, //0x00002a3d jmp          LBB0_652
	//0x00002a42 LBB0_499
	0x0f, 0xbc, 0xc6, //0x00002a42 bsfl         %esi, %eax
	0xe9, 0xde, 0x00, 0x00, 0x00, //0x00002a45 jmp          LBB0_510
	//0x00002a4a LBB0_500
	0x4c, 0x03, 0x34, 0x24, //0x00002a4a addq         (%rsp), %r14
	0x4d, 0x29, 0xce, //0x00002a4e subq         %r9, %r14
	0x48, 0xf7, 0xd1, //0x00002a51 notq         %rcx
	0x4c, 0x01, 0xf1, //0x00002a54 addq         %r14, %rcx
	0x49, 0x89, 0xc9, //0x00002a57 movq         %rcx, %r9
	0xe9, 0xfc, 0xfb, 0xff, 0xff, //0x00002a5a jmp          LBB0_478
	//0x00002a5f LBB0_501
	0x49, 0x8d, 0x0c, 0x1e, //0x00002a5f leaq         (%r14,%rbx), %rcx
	0x48, 0x83, 0xfe, 0x10, //0x00002a63 cmpq         $16, %rsi
	0x0f, 0x83, 0x49, 0xe4, 0xff, 0xff, //0x00002a67 jae          LBB0_164
	0xe9, 0x8c, 0xe4, 0xff, 0xff, //0x00002a6d jmp          LBB0_167
	//0x00002a72 LBB0_502
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00002a72 movq         $-1, %r9
	0x48, 0xc7, 0x44, 0x24, 0x28, 0xff, 0xff, 0xff, 0xff, //0x00002a79 movq         $-1, $40(%rsp)
	0x4c, 0x8b, 0x6c, 0x24, 0x38, //0x00002a82 movq         $56(%rsp), %r13
	0x49, 0x83, 0xf8, 0x10, //0x00002a87 cmpq         $16, %r8
	0x0f, 0x83, 0x5a, 0xdf, 0xff, 0xff, //0x00002a8b jae          LBB0_95
	0xe9, 0xac, 0xe0, 0xff, 0xff, //0x00002a91 jmp          LBB0_113
	//0x00002a96 LBB0_503
	0x89, 0xd0, //0x00002a96 movl         %edx, %eax
	0xe9, 0x8b, 0x00, 0x00, 0x00, //0x00002a98 jmp          LBB0_510
	//0x00002a9d LBB0_504
	0x4b, 0x8d, 0x0c, 0x0e, //0x00002a9d leaq         (%r14,%r9), %rcx
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00002aa1 movq         $-1, %r11
	0x45, 0x31, 0xd2, //0x00002aa8 xorl         %r10d, %r10d
	0x48, 0x83, 0xfb, 0x20, //0x00002aab cmpq         $32, %rbx
	0x0f, 0x83, 0xa1, 0xfc, 0xff, 0xff, //0x00002aaf jae          LBB0_55
	//0x00002ab5 LBB0_505
	0x4d, 0x89, 0xdf, //0x00002ab5 movq         %r11, %r15
	0xe9, 0x09, 0x03, 0x00, 0x00, //0x00002ab8 jmp          LBB0_534
	//0x00002abd LBB0_506
	0x4f, 0x8d, 0x14, 0x0e, //0x00002abd leaq         (%r14,%r9), %r10
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00002ac1 movq         $-1, %r15
	0x45, 0x31, 0xdb, //0x00002ac8 xorl         %r11d, %r11d
	0x48, 0x83, 0xfb, 0x20, //0x00002acb cmpq         $32, %rbx
	0x0f, 0x83, 0xd8, 0xfc, 0xff, 0xff, //0x00002acf jae          LBB0_200
	0xe9, 0xc2, 0x05, 0x00, 0x00, //0x00002ad5 jmp          LBB0_564
	//0x00002ada LBB0_507
	0x4c, 0x29, 0xf1, //0x00002ada subq         %r14, %rcx
	0x48, 0x01, 0xd1, //0x00002add addq         %rdx, %rcx
	//0x00002ae0 LBB0_508
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x00002ae0 movq         $24(%rsp), %r11
	0x48, 0x8b, 0x7c, 0x24, 0x10, //0x00002ae5 movq         $16(%rsp), %rdi
	0x4c, 0x8b, 0x7c, 0x24, 0x08, //0x00002aea movq         $8(%rsp), %r15
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00002aef vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc4, 0x41, 0x7d, 0x6f, 0xeb, //0x00002af4 vmovdqa      %ymm11, %ymm13
	0xc5, 0xfe, 0x6f, 0x25, 0x9f, 0xd7, 0xff, 0xff, //0x00002af9 vmovdqu      $-10337(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x1d, 0xb7, 0xd6, 0xff, 0xff, //0x00002b01 vmovdqu      $-10569(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd8, //0x00002b09 vmovdqa      %ymm0, %ymm11
	0xc5, 0x7d, 0x6f, 0xe1, //0x00002b0d vmovdqa      %ymm1, %ymm12
	0x48, 0x89, 0xc3, //0x00002b11 movq         %rax, %rbx
	0x49, 0x89, 0x4d, 0x00, //0x00002b14 movq         %rcx, (%r13)
	0x48, 0x85, 0xdb, //0x00002b18 testq        %rbx, %rbx
	0x0f, 0x8f, 0x7f, 0xd8, 0xff, 0xff, //0x00002b1b jg           LBB0_3
	0xe9, 0x45, 0x10, 0x00, 0x00, //0x00002b21 jmp          LBB0_178
	//0x00002b26 LBB0_509
	0x89, 0xf0, //0x00002b26 movl         %esi, %eax
	//0x00002b28 LBB0_510
	0x4c, 0x03, 0x34, 0x24, //0x00002b28 addq         (%rsp), %r14
	0x4d, 0x29, 0xce, //0x00002b2c subq         %r9, %r14
	0x49, 0x29, 0xc6, //0x00002b2f subq         %rax, %r14
	0x49, 0xf7, 0xd5, //0x00002b32 notq         %r13
	0x4d, 0x01, 0xf5, //0x00002b35 addq         %r14, %r13
	0x4d, 0x89, 0xe9, //0x00002b38 movq         %r13, %r9
	0x4c, 0x8b, 0x6c, 0x24, 0x30, //0x00002b3b movq         $48(%rsp), %r13
	0xe9, 0x16, 0xfb, 0xff, 0xff, //0x00002b40 jmp          LBB0_478
	//0x00002b45 LBB0_511
	0x4b, 0x8d, 0x1c, 0x0e, //0x00002b45 leaq         (%r14,%r9), %rbx
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00002b49 movq         $-1, %r11
	0x45, 0x31, 0xd2, //0x00002b50 xorl         %r10d, %r10d
	0x48, 0x83, 0xf9, 0x20, //0x00002b53 cmpq         $32, %rcx
	0x0f, 0x83, 0x26, 0xfd, 0xff, 0xff, //0x00002b57 jae          LBB0_140
	//0x00002b5d LBB0_512
	0x4d, 0x89, 0xdf, //0x00002b5d movq         %r11, %r15
	0xe9, 0xa8, 0x06, 0x00, 0x00, //0x00002b60 jmp          LBB0_580
	//0x00002b65 LBB0_513
	0x4f, 0x8d, 0x14, 0x0e, //0x00002b65 leaq         (%r14,%r9), %r10
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00002b69 movq         $-1, %r15
	0x45, 0x31, 0xdb, //0x00002b70 xorl         %r11d, %r11d
	0x48, 0x83, 0xf9, 0x20, //0x00002b73 cmpq         $32, %rcx
	0x0f, 0x83, 0x5d, 0xfd, 0xff, 0xff, //0x00002b77 jae          LBB0_221
	0xe9, 0x94, 0x08, 0x00, 0x00, //0x00002b7d jmp          LBB0_603
	//0x00002b82 LBB0_514
	0x4c, 0x01, 0xf1, //0x00002b82 addq         %r14, %rcx
	0x48, 0x83, 0xfe, 0x10, //0x00002b85 cmpq         $16, %rsi
	0x48, 0x8b, 0x1c, 0x24, //0x00002b89 movq         (%rsp), %rbx
	0x0f, 0x83, 0x97, 0xed, 0xff, 0xff, //0x00002b8d jae          LBB0_318
	0xe9, 0xda, 0xed, 0xff, 0xff, //0x00002b93 jmp          LBB0_321
	//0x00002b98 LBB0_515
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00002b98 movq         $-1, %r15
	0x48, 0xc7, 0x44, 0x24, 0x28, 0xff, 0xff, 0xff, 0xff, //0x00002b9f movq         $-1, $40(%rsp)
	0x4c, 0x8b, 0x5c, 0x24, 0x38, //0x00002ba8 movq         $56(%rsp), %r11
	0x4d, 0x89, 0xd9, //0x00002bad movq         %r11, %r9
	0x4c, 0x89, 0x6c, 0x24, 0x30, //0x00002bb0 movq         %r13, $48(%rsp)
	0x49, 0x83, 0xfa, 0x10, //0x00002bb5 cmpq         $16, %r10
	0x0f, 0x83, 0xff, 0xf6, 0xff, 0xff, //0x00002bb9 jae          LBB0_425
	0xe9, 0x2f, 0xf8, 0xff, 0xff, //0x00002bbf jmp          LBB0_443
	//0x00002bc4 LBB0_516
	0x4b, 0x8d, 0x3c, 0x0e, //0x00002bc4 leaq         (%r14,%r9), %rdi
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00002bc8 movq         $-1, %r11
	0x45, 0x31, 0xd2, //0x00002bcf xorl         %r10d, %r10d
	0x49, 0x83, 0xf8, 0x20, //0x00002bd2 cmpq         $32, %r8
	0x0f, 0x83, 0xd1, 0xfd, 0xff, 0xff, //0x00002bd6 jae          LBB0_244
	//0x00002bdc LBB0_517
	0xc5, 0x7d, 0x7f, 0xe1, //0x00002bdc vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd8, //0x00002be0 vmovdqa      %ymm11, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xdd, //0x00002be4 vmovdqa      %ymm13, %ymm11
	0xe9, 0x0a, 0x0c, 0x00, 0x00, //0x00002be9 jmp          LBB0_635
	//0x00002bee LBB0_518
	0x4b, 0x8d, 0x0c, 0x0e, //0x00002bee leaq         (%r14,%r9), %rcx
	0x49, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00002bf2 movq         $-1, %r10
	0x45, 0x31, 0xdb, //0x00002bf9 xorl         %r11d, %r11d
	0x49, 0x83, 0xf8, 0x20, //0x00002bfc cmpq         $32, %r8
	0x0f, 0x83, 0xff, 0xfd, 0xff, 0xff, //0x00002c00 jae          LBB0_395
	//0x00002c06 LBB0_519
	0x48, 0x89, 0xcb, //0x00002c06 movq         %rcx, %rbx
	0xc5, 0x7d, 0x7f, 0xe1, //0x00002c09 vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd8, //0x00002c0d vmovdqa      %ymm11, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xdd, //0x00002c11 vmovdqa      %ymm13, %ymm11
	0xc4, 0x41, 0x7d, 0x6f, 0xe7, //0x00002c16 vmovdqa      %ymm15, %ymm12
	0xe9, 0x99, 0x0d, 0x00, 0x00, //0x00002c1b jmp          LBB0_657
	//0x00002c20 LBB0_520
	0x4c, 0x29, 0xf1, //0x00002c20 subq         %r14, %rcx
	0x48, 0x01, 0xd1, //0x00002c23 addq         %rdx, %rcx
	//0x00002c26 LBB0_521
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x00002c26 movq         $24(%rsp), %r11
	0x48, 0x8b, 0x7c, 0x24, 0x10, //0x00002c2b movq         $16(%rsp), %rdi
	0x4c, 0x8b, 0x7c, 0x24, 0x08, //0x00002c30 movq         $8(%rsp), %r15
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00002c35 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc4, 0x41, 0x7d, 0x6f, 0xeb, //0x00002c3a vmovdqa      %ymm11, %ymm13
	0xc5, 0xfe, 0x6f, 0x25, 0x59, 0xd6, 0xff, 0xff, //0x00002c3f vmovdqu      $-10663(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x1d, 0x71, 0xd5, 0xff, 0xff, //0x00002c47 vmovdqu      $-10895(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd8, //0x00002c4f vmovdqa      %ymm0, %ymm11
	0xc5, 0x7d, 0x6f, 0xe1, //0x00002c53 vmovdqa      %ymm1, %ymm12
	0xe9, 0x75, 0xf3, 0xff, 0xff, //0x00002c57 jmp          LBB0_383
	//0x00002c5c LBB0_522
	0x49, 0x39, 0xf2, //0x00002c5c cmpq         %rsi, %r10
	0x0f, 0x84, 0x30, 0x0f, 0x00, 0x00, //0x00002c5f je           LBB0_711
	0xc5, 0x7d, 0x7f, 0xe1, //0x00002c65 vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd8, //0x00002c69 vmovdqa      %ymm11, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xdd, //0x00002c6d vmovdqa      %ymm13, %ymm11
	0x49, 0x01, 0xf1, //0x00002c72 addq         %rsi, %r9
	0x49, 0x83, 0xc1, 0x01, //0x00002c75 addq         $1, %r9
	0x48, 0xf7, 0xd6, //0x00002c79 notq         %rsi
	0x49, 0x01, 0xf2, //0x00002c7c addq         %rsi, %r10
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x00002c7f movq         $24(%rsp), %r11
	0x48, 0x8b, 0x7c, 0x24, 0x10, //0x00002c84 movq         $16(%rsp), %rdi
	0x4c, 0x8b, 0x7c, 0x24, 0x08, //0x00002c89 movq         $8(%rsp), %r15
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00002c8e vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0xfe, 0x6f, 0x25, 0x05, 0xd6, 0xff, 0xff, //0x00002c93 vmovdqu      $-10747(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x1d, 0x1d, 0xd5, 0xff, 0xff, //0x00002c9b vmovdqu      $-10979(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd8, //0x00002ca3 vmovdqa      %ymm0, %ymm11
	0x4d, 0x85, 0xd2, //0x00002ca7 testq        %r10, %r10
	0x0f, 0x8f, 0x30, 0x00, 0x00, 0x00, //0x00002caa jg           LBB0_526
	0xe9, 0xe0, 0x0e, 0x00, 0x00, //0x00002cb0 jmp          LBB0_711
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002cb5 .p2align 4, 0x90
	//0x00002cc0 LBB0_524
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00002cc0 movq         $-2, %rcx
	0xba, 0x02, 0x00, 0x00, 0x00, //0x00002cc7 movl         $2, %edx
	0x49, 0x01, 0xd1, //0x00002ccc addq         %rdx, %r9
	0x48, 0xc7, 0x04, 0x24, 0xff, 0xff, 0xff, 0xff, //0x00002ccf movq         $-1, (%rsp)
	0x49, 0x01, 0xca, //0x00002cd7 addq         %rcx, %r10
	0x0f, 0x8e, 0xb5, 0x0e, 0x00, 0x00, //0x00002cda jle          LBB0_711
	//0x00002ce0 LBB0_526
	0x41, 0x0f, 0xb6, 0x09, //0x00002ce0 movzbl       (%r9), %ecx
	0x80, 0xf9, 0x5c, //0x00002ce4 cmpb         $92, %cl
	0x0f, 0x84, 0xd3, 0xff, 0xff, 0xff, //0x00002ce7 je           LBB0_524
	0x80, 0xf9, 0x22, //0x00002ced cmpb         $34, %cl
	0x0f, 0x84, 0x22, 0x0a, 0x00, 0x00, //0x00002cf0 je           LBB0_629
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00002cf6 movq         $-1, %rcx
	0xba, 0x01, 0x00, 0x00, 0x00, //0x00002cfd movl         $1, %edx
	0x49, 0x01, 0xd1, //0x00002d02 addq         %rdx, %r9
	0x48, 0xc7, 0x04, 0x24, 0xff, 0xff, 0xff, 0xff, //0x00002d05 movq         $-1, (%rsp)
	0x49, 0x01, 0xca, //0x00002d0d addq         %rcx, %r10
	0x0f, 0x8f, 0xca, 0xff, 0xff, 0xff, //0x00002d10 jg           LBB0_526
	0xe9, 0x7a, 0x0e, 0x00, 0x00, //0x00002d16 jmp          LBB0_711
	//0x00002d1b LBB0_529
	0xc5, 0x7d, 0x7f, 0xe1, //0x00002d1b vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd8, //0x00002d1f vmovdqa      %ymm11, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xdd, //0x00002d23 vmovdqa      %ymm13, %ymm11
	0xc4, 0x41, 0x7d, 0x6f, 0xe7, //0x00002d28 vmovdqa      %ymm15, %ymm12
	0x49, 0x83, 0xff, 0xff, //0x00002d2d cmpq         $-1, %r15
	0x0f, 0x85, 0x24, 0x00, 0x00, 0x00, //0x00002d31 jne          LBB0_532
	0x48, 0x89, 0xc8, //0x00002d37 movq         %rcx, %rax
	0x4c, 0x29, 0xf0, //0x00002d3a subq         %r14, %rax
	0x4c, 0x0f, 0xbc, 0xda, //0x00002d3d bsfq         %rdx, %r11
	0x49, 0x01, 0xc3, //0x00002d41 addq         %rax, %r11
	0xe9, 0x12, 0x00, 0x00, 0x00, //0x00002d44 jmp          LBB0_532
	//0x00002d49 LBB0_531
	0xc5, 0x7d, 0x7f, 0xe1, //0x00002d49 vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd8, //0x00002d4d vmovdqa      %ymm11, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xdd, //0x00002d51 vmovdqa      %ymm13, %ymm11
	0xc4, 0x41, 0x7d, 0x6f, 0xe7, //0x00002d56 vmovdqa      %ymm15, %ymm12
	//0x00002d5b LBB0_532
	0x44, 0x89, 0xd0, //0x00002d5b movl         %r10d, %eax
	0xf7, 0xd0, //0x00002d5e notl         %eax
	0x21, 0xd0, //0x00002d60 andl         %edx, %eax
	0x8d, 0x3c, 0x00, //0x00002d62 leal         (%rax,%rax), %edi
	0x45, 0x8d, 0x3c, 0x42, //0x00002d65 leal         (%r10,%rax,2), %r15d
	0xf7, 0xd7, //0x00002d69 notl         %edi
	0x21, 0xd7, //0x00002d6b andl         %edx, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x00002d6d andl         $-1431655766, %edi
	0x45, 0x31, 0xd2, //0x00002d73 xorl         %r10d, %r10d
	0x01, 0xc7, //0x00002d76 addl         %eax, %edi
	0x41, 0x0f, 0x92, 0xc2, //0x00002d78 setb         %r10b
	0x01, 0xff, //0x00002d7c addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x00002d7e xorl         $1431655765, %edi
	0x44, 0x21, 0xff, //0x00002d84 andl         %r15d, %edi
	0xf7, 0xd7, //0x00002d87 notl         %edi
	0x21, 0xfe, //0x00002d89 andl         %edi, %esi
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00002d8b vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc4, 0x41, 0x7d, 0x6f, 0xfc, //0x00002d90 vmovdqa      %ymm12, %ymm15
	0xc4, 0x41, 0x7d, 0x6f, 0xeb, //0x00002d95 vmovdqa      %ymm11, %ymm13
	0xc5, 0xfe, 0x6f, 0x25, 0xfe, 0xd4, 0xff, 0xff, //0x00002d9a vmovdqu      $-11010(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x1d, 0x16, 0xd4, 0xff, 0xff, //0x00002da2 vmovdqu      $-11242(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd8, //0x00002daa vmovdqa      %ymm0, %ymm11
	0xc5, 0x7d, 0x6f, 0xe1, //0x00002dae vmovdqa      %ymm1, %ymm12
	0x4d, 0x89, 0xdf, //0x00002db2 movq         %r11, %r15
	0x48, 0x85, 0xf6, //0x00002db5 testq        %rsi, %rsi
	0x0f, 0x85, 0xcc, 0xf9, 0xff, 0xff, //0x00002db8 jne          LBB0_58
	//0x00002dbe LBB0_533
	0x48, 0x83, 0xc1, 0x20, //0x00002dbe addq         $32, %rcx
	0x48, 0x83, 0xc3, 0xe0, //0x00002dc2 addq         $-32, %rbx
	//0x00002dc6 LBB0_534
	0x4d, 0x85, 0xd2, //0x00002dc6 testq        %r10, %r10
	0x0f, 0x85, 0x0d, 0x07, 0x00, 0x00, //0x00002dc9 jne          LBB0_614
	0x4c, 0x89, 0xf7, //0x00002dcf movq         %r14, %rdi
	0x48, 0xf7, 0xd7, //0x00002dd2 notq         %rdi
	0x4c, 0x89, 0xfe, //0x00002dd5 movq         %r15, %rsi
	0x49, 0x89, 0xca, //0x00002dd8 movq         %rcx, %r10
	0x48, 0x85, 0xdb, //0x00002ddb testq        %rbx, %rbx
	0x0f, 0x84, 0xa2, 0x00, 0x00, 0x00, //0x00002dde je           LBB0_546
	//0x00002de4 LBB0_536
	0x48, 0x83, 0xc7, 0x01, //0x00002de4 addq         $1, %rdi
	//0x00002de8 LBB0_537
	0x31, 0xd2, //0x00002de8 xorl         %edx, %edx
	//0x00002dea LBB0_538
	0x41, 0x0f, 0xb6, 0x0c, 0x12, //0x00002dea movzbl       (%r10,%rdx), %ecx
	0x80, 0xf9, 0x22, //0x00002def cmpb         $34, %cl
	0x0f, 0x84, 0x87, 0x00, 0x00, 0x00, //0x00002df2 je           LBB0_545
	0x80, 0xf9, 0x5c, //0x00002df8 cmpb         $92, %cl
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00002dfb je           LBB0_543
	0x48, 0x83, 0xc2, 0x01, //0x00002e01 addq         $1, %rdx
	0x48, 0x39, 0xd3, //0x00002e05 cmpq         %rdx, %rbx
	0x0f, 0x85, 0xdc, 0xff, 0xff, 0xff, //0x00002e08 jne          LBB0_538
	0xe9, 0x7b, 0x00, 0x00, 0x00, //0x00002e0e jmp          LBB0_541
	//0x00002e13 LBB0_543
	0x48, 0x8d, 0x43, 0xff, //0x00002e13 leaq         $-1(%rbx), %rax
	0x48, 0x39, 0xd0, //0x00002e17 cmpq         %rdx, %rax
	0x0f, 0x84, 0x30, 0x0d, 0x00, 0x00, //0x00002e1a je           LBB0_681
	0xc5, 0x7d, 0x7f, 0xe1, //0x00002e20 vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd8, //0x00002e24 vmovdqa      %ymm11, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xdd, //0x00002e28 vmovdqa      %ymm13, %ymm11
	0x4a, 0x8d, 0x04, 0x17, //0x00002e2d leaq         (%rdi,%r10), %rax
	0x48, 0x01, 0xd0, //0x00002e31 addq         %rdx, %rax
	0x48, 0x83, 0xfe, 0xff, //0x00002e34 cmpq         $-1, %rsi
	0x4c, 0x0f, 0x44, 0xf8, //0x00002e38 cmoveq       %rax, %r15
	0x48, 0x0f, 0x44, 0xf0, //0x00002e3c cmoveq       %rax, %rsi
	0x49, 0x01, 0xd2, //0x00002e40 addq         %rdx, %r10
	0x49, 0x83, 0xc2, 0x02, //0x00002e43 addq         $2, %r10
	0x48, 0x89, 0xd8, //0x00002e47 movq         %rbx, %rax
	0x48, 0x29, 0xd0, //0x00002e4a subq         %rdx, %rax
	0x48, 0x83, 0xc0, 0xfe, //0x00002e4d addq         $-2, %rax
	0x48, 0x83, 0xc3, 0xfe, //0x00002e51 addq         $-2, %rbx
	0x48, 0x39, 0xd3, //0x00002e55 cmpq         %rdx, %rbx
	0x48, 0x89, 0xc3, //0x00002e58 movq         %rax, %rbx
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00002e5b vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0xfe, 0x6f, 0x25, 0x38, 0xd4, 0xff, 0xff, //0x00002e60 vmovdqu      $-11208(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x1d, 0x50, 0xd3, 0xff, 0xff, //0x00002e68 vmovdqu      $-11440(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd8, //0x00002e70 vmovdqa      %ymm0, %ymm11
	0x0f, 0x85, 0x6e, 0xff, 0xff, 0xff, //0x00002e74 jne          LBB0_537
	0xe9, 0xd1, 0x0c, 0x00, 0x00, //0x00002e7a jmp          LBB0_681
	//0x00002e7f LBB0_545
	0x49, 0x01, 0xd2, //0x00002e7f addq         %rdx, %r10
	0x49, 0x83, 0xc2, 0x01, //0x00002e82 addq         $1, %r10
	//0x00002e86 LBB0_546
	0x4d, 0x29, 0xf2, //0x00002e86 subq         %r14, %r10
	0xe9, 0x92, 0xe4, 0xff, 0xff, //0x00002e89 jmp          LBB0_232
	//0x00002e8e LBB0_541
	0x80, 0xf9, 0x22, //0x00002e8e cmpb         $34, %cl
	0x0f, 0x85, 0xb9, 0x0c, 0x00, 0x00, //0x00002e91 jne          LBB0_681
	0xc5, 0x7d, 0x7f, 0xe1, //0x00002e97 vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd8, //0x00002e9b vmovdqa      %ymm11, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xdd, //0x00002e9f vmovdqa      %ymm13, %ymm11
	0x49, 0x01, 0xda, //0x00002ea4 addq         %rbx, %r10
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00002ea7 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0xfe, 0x6f, 0x25, 0xec, 0xd3, 0xff, 0xff, //0x00002eac vmovdqu      $-11284(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x1d, 0x04, 0xd3, 0xff, 0xff, //0x00002eb4 vmovdqu      $-11516(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd8, //0x00002ebc vmovdqa      %ymm0, %ymm11
	0xe9, 0xc1, 0xff, 0xff, 0xff, //0x00002ec0 jmp          LBB0_546
	//0x00002ec5 LBB0_547
	0x49, 0x39, 0xf2, //0x00002ec5 cmpq         %rsi, %r10
	0x0f, 0x84, 0xc7, 0x0c, 0x00, 0x00, //0x00002ec8 je           LBB0_711
	0xc5, 0x7d, 0x7f, 0xe1, //0x00002ece vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd8, //0x00002ed2 vmovdqa      %ymm11, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xdd, //0x00002ed6 vmovdqa      %ymm13, %ymm11
	0x49, 0x01, 0xf1, //0x00002edb addq         %rsi, %r9
	0x49, 0x83, 0xc1, 0x01, //0x00002ede addq         $1, %r9
	0x48, 0xf7, 0xd6, //0x00002ee2 notq         %rsi
	0x49, 0x01, 0xf2, //0x00002ee5 addq         %rsi, %r10
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x00002ee8 movq         $24(%rsp), %r11
	0x48, 0x8b, 0x7c, 0x24, 0x10, //0x00002eed movq         $16(%rsp), %rdi
	0x4c, 0x8b, 0x7c, 0x24, 0x08, //0x00002ef2 movq         $8(%rsp), %r15
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00002ef7 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0xfe, 0x6f, 0x25, 0x9c, 0xd3, 0xff, 0xff, //0x00002efc vmovdqu      $-11364(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x1d, 0xb4, 0xd2, 0xff, 0xff, //0x00002f04 vmovdqu      $-11596(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd8, //0x00002f0c vmovdqa      %ymm0, %ymm11
	0x4d, 0x85, 0xd2, //0x00002f10 testq        %r10, %r10
	0x0f, 0x8f, 0x25, 0x00, 0x00, 0x00, //0x00002f13 jg           LBB0_551
	0xe9, 0x77, 0x0c, 0x00, 0x00, //0x00002f19 jmp          LBB0_711
	//0x00002f1e LBB0_549
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00002f1e movq         $-2, %rcx
	0xba, 0x02, 0x00, 0x00, 0x00, //0x00002f25 movl         $2, %edx
	0x49, 0x01, 0xd1, //0x00002f2a addq         %rdx, %r9
	0x48, 0xc7, 0x04, 0x24, 0xff, 0xff, 0xff, 0xff, //0x00002f2d movq         $-1, (%rsp)
	0x49, 0x01, 0xca, //0x00002f35 addq         %rcx, %r10
	0x0f, 0x8e, 0x57, 0x0c, 0x00, 0x00, //0x00002f38 jle          LBB0_711
	//0x00002f3e LBB0_551
	0x41, 0x0f, 0xb6, 0x09, //0x00002f3e movzbl       (%r9), %ecx
	0x80, 0xf9, 0x5c, //0x00002f42 cmpb         $92, %cl
	0x0f, 0x84, 0xd3, 0xff, 0xff, 0xff, //0x00002f45 je           LBB0_549
	0x80, 0xf9, 0x22, //0x00002f4b cmpb         $34, %cl
	0x0f, 0x84, 0x25, 0x00, 0x00, 0x00, //0x00002f4e je           LBB0_678
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00002f54 movq         $-1, %rcx
	0xba, 0x01, 0x00, 0x00, 0x00, //0x00002f5b movl         $1, %edx
	0x49, 0x01, 0xd1, //0x00002f60 addq         %rdx, %r9
	0x48, 0xc7, 0x04, 0x24, 0xff, 0xff, 0xff, 0xff, //0x00002f63 movq         $-1, (%rsp)
	0x49, 0x01, 0xca, //0x00002f6b addq         %rcx, %r10
	0x0f, 0x8f, 0xca, 0xff, 0xff, 0xff, //0x00002f6e jg           LBB0_551
	0xe9, 0x1c, 0x0c, 0x00, 0x00, //0x00002f74 jmp          LBB0_711
	//0x00002f79 LBB0_678
	0x4d, 0x29, 0xf1, //0x00002f79 subq         %r14, %r9
	0x49, 0x83, 0xc1, 0x01, //0x00002f7c addq         $1, %r9
	0x4d, 0x89, 0x4d, 0x00, //0x00002f80 movq         %r9, (%r13)
	0x4d, 0x85, 0xc0, //0x00002f84 testq        %r8, %r8
	0x0f, 0x8f, 0x24, 0xe8, 0xff, 0xff, //0x00002f87 jg           LBB0_300
	0xe9, 0x9a, 0x0b, 0x00, 0x00, //0x00002f8d jmp          LBB0_679
	//0x00002f92 LBB0_554
	0xc5, 0x7d, 0x7f, 0xe2, //0x00002f92 vmovdqa      %ymm12, %ymm2
	0xc5, 0x7d, 0x7f, 0xd9, //0x00002f96 vmovdqa      %ymm11, %ymm1
	0xc4, 0x41, 0x7d, 0x6f, 0xdd, //0x00002f9a vmovdqa      %ymm13, %ymm11
	0xc4, 0x41, 0x7d, 0x6f, 0xe7, //0x00002f9f vmovdqa      %ymm15, %ymm12
	0x4c, 0x89, 0x3c, 0x24, //0x00002fa4 movq         %r15, (%rsp)
	0x49, 0x83, 0xff, 0xff, //0x00002fa8 cmpq         $-1, %r15
	0x0f, 0x85, 0x2c, 0x00, 0x00, 0x00, //0x00002fac jne          LBB0_557
	0x48, 0x89, 0xc8, //0x00002fb2 movq         %rcx, %rax
	0x4c, 0x29, 0xf0, //0x00002fb5 subq         %r14, %rax
	0x48, 0x0f, 0xbc, 0xf2, //0x00002fb8 bsfq         %rdx, %rsi
	0x48, 0x01, 0xc6, //0x00002fbc addq         %rax, %rsi
	0x48, 0x89, 0x34, 0x24, //0x00002fbf movq         %rsi, (%rsp)
	0xe9, 0x16, 0x00, 0x00, 0x00, //0x00002fc3 jmp          LBB0_557
	//0x00002fc8 LBB0_556
	0x4c, 0x89, 0x3c, 0x24, //0x00002fc8 movq         %r15, (%rsp)
	0xc5, 0x7d, 0x7f, 0xe2, //0x00002fcc vmovdqa      %ymm12, %ymm2
	0xc5, 0x7d, 0x7f, 0xd9, //0x00002fd0 vmovdqa      %ymm11, %ymm1
	0xc4, 0x41, 0x7d, 0x6f, 0xdd, //0x00002fd4 vmovdqa      %ymm13, %ymm11
	0xc4, 0x41, 0x7d, 0x6f, 0xe7, //0x00002fd9 vmovdqa      %ymm15, %ymm12
	//0x00002fde LBB0_557
	0x44, 0x89, 0xd8, //0x00002fde movl         %r11d, %eax
	0xf7, 0xd0, //0x00002fe1 notl         %eax
	0x21, 0xd0, //0x00002fe3 andl         %edx, %eax
	0x8d, 0x3c, 0x00, //0x00002fe5 leal         (%rax,%rax), %edi
	0x41, 0x8d, 0x34, 0x43, //0x00002fe8 leal         (%r11,%rax,2), %esi
	0xf7, 0xd7, //0x00002fec notl         %edi
	0x21, 0xd7, //0x00002fee andl         %edx, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x00002ff0 andl         $-1431655766, %edi
	0x45, 0x31, 0xdb, //0x00002ff6 xorl         %r11d, %r11d
	0x01, 0xc7, //0x00002ff9 addl         %eax, %edi
	0x41, 0x0f, 0x92, 0xc3, //0x00002ffb setb         %r11b
	0x01, 0xff, //0x00002fff addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x00003001 xorl         $1431655765, %edi
	0x21, 0xf7, //0x00003007 andl         %esi, %edi
	0xf7, 0xd7, //0x00003009 notl         %edi
	0x41, 0x21, 0xfa, //0x0000300b andl         %edi, %r10d
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x0000300e vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc4, 0x41, 0x7d, 0x6f, 0xfc, //0x00003013 vmovdqa      %ymm12, %ymm15
	0xc4, 0x41, 0x7d, 0x6f, 0xeb, //0x00003018 vmovdqa      %ymm11, %ymm13
	0xc5, 0xfe, 0x6f, 0x25, 0x7b, 0xd2, 0xff, 0xff, //0x0000301d vmovdqu      $-11653(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x1d, 0x93, 0xd1, 0xff, 0xff, //0x00003025 vmovdqu      $-11885(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd9, //0x0000302d vmovdqa      %ymm1, %ymm11
	0xc5, 0x7d, 0x6f, 0xe2, //0x00003031 vmovdqa      %ymm2, %ymm12
	0x4c, 0x8b, 0x3c, 0x24, //0x00003035 movq         (%rsp), %r15
	0x4d, 0x85, 0xd2, //0x00003039 testq        %r10, %r10
	0x0f, 0x85, 0xa0, 0xf7, 0xff, 0xff, //0x0000303c jne          LBB0_203
	//0x00003042 LBB0_558
	0xba, 0x40, 0x00, 0x00, 0x00, //0x00003042 movl         $64, %edx
	//0x00003047 LBB0_559
	0xc5, 0xbd, 0x64, 0xc8, //0x00003047 vpcmpgtb     %ymm0, %ymm8, %ymm1
	0xc4, 0xc1, 0x7d, 0x64, 0xc1, //0x0000304b vpcmpgtb     %ymm9, %ymm0, %ymm0
	0xc5, 0xf5, 0xdb, 0xc0, //0x00003050 vpand        %ymm0, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x00003054 vpmovmskb    %ymm0, %esi
	0x0f, 0xbc, 0xfe, //0x00003058 bsfl         %esi, %edi
	0x4d, 0x85, 0xd2, //0x0000305b testq        %r10, %r10
	0x0f, 0x84, 0x25, 0x00, 0x00, 0x00, //0x0000305e je           LBB0_562
	0x85, 0xf6, //0x00003064 testl        %esi, %esi
	0xb8, 0x40, 0x00, 0x00, 0x00, //0x00003066 movl         $64, %eax
	0x0f, 0x44, 0xf8, //0x0000306b cmovel       %eax, %edi
	0x48, 0x39, 0xfa, //0x0000306e cmpq         %rdi, %rdx
	0x49, 0x89, 0xca, //0x00003071 movq         %rcx, %r10
	0x0f, 0x87, 0x15, 0x0d, 0x00, 0x00, //0x00003074 ja           LBB0_721
	0x4d, 0x29, 0xf2, //0x0000307a subq         %r14, %r10
	0x49, 0x01, 0xd2, //0x0000307d addq         %rdx, %r10
	0x49, 0x83, 0xc2, 0x01, //0x00003080 addq         $1, %r10
	0xe9, 0x97, 0xe2, 0xff, 0xff, //0x00003084 jmp          LBB0_232
	//0x00003089 LBB0_562
	0x85, 0xf6, //0x00003089 testl        %esi, %esi
	0x49, 0x89, 0xca, //0x0000308b movq         %rcx, %r10
	0x0f, 0x85, 0x08, 0x0d, 0x00, 0x00, //0x0000308e jne          LBB0_722
	0x49, 0x83, 0xc2, 0x20, //0x00003094 addq         $32, %r10
	0x48, 0x83, 0xc3, 0xe0, //0x00003098 addq         $-32, %rbx
	//0x0000309c LBB0_564
	0x4d, 0x85, 0xdb, //0x0000309c testq        %r11, %r11
	0x0f, 0x85, 0x98, 0x04, 0x00, 0x00, //0x0000309f jne          LBB0_616
	0x4c, 0x89, 0xf9, //0x000030a5 movq         %r15, %rcx
	0x48, 0x85, 0xdb, //0x000030a8 testq        %rbx, %rbx
	0x0f, 0x84, 0x9f, 0x0a, 0x00, 0x00, //0x000030ab je           LBB0_681
	//0x000030b1 LBB0_566
	0x41, 0x0f, 0xb6, 0x12, //0x000030b1 movzbl       (%r10), %edx
	0x80, 0xfa, 0x22, //0x000030b5 cmpb         $34, %dl
	0x0f, 0x84, 0x98, 0x00, 0x00, 0x00, //0x000030b8 je           LBB0_574
	0x80, 0xfa, 0x5c, //0x000030be cmpb         $92, %dl
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x000030c1 je           LBB0_570
	0x80, 0xfa, 0x1f, //0x000030c7 cmpb         $31, %dl
	0x0f, 0x86, 0xd7, 0x0c, 0x00, 0x00, //0x000030ca jbe          LBB0_723
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x000030d0 movq         $-1, %rdx
	0xbe, 0x01, 0x00, 0x00, 0x00, //0x000030d7 movl         $1, %esi
	0x49, 0x01, 0xf2, //0x000030dc addq         %rsi, %r10
	0x48, 0x01, 0xd3, //0x000030df addq         %rdx, %rbx
	0x0f, 0x85, 0xc9, 0xff, 0xff, 0xff, //0x000030e2 jne          LBB0_566
	0xe9, 0x63, 0x0a, 0x00, 0x00, //0x000030e8 jmp          LBB0_681
	//0x000030ed LBB0_570
	0x48, 0x83, 0xfb, 0x01, //0x000030ed cmpq         $1, %rbx
	0x0f, 0x84, 0x59, 0x0a, 0x00, 0x00, //0x000030f1 je           LBB0_681
	0xc5, 0x7d, 0x7f, 0xe1, //0x000030f7 vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd8, //0x000030fb vmovdqa      %ymm11, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xdd, //0x000030ff vmovdqa      %ymm13, %ymm11
	0x48, 0xc7, 0xc2, 0xfe, 0xff, 0xff, 0xff, //0x00003104 movq         $-2, %rdx
	0xbe, 0x02, 0x00, 0x00, 0x00, //0x0000310b movl         $2, %esi
	0x48, 0x83, 0xf9, 0xff, //0x00003110 cmpq         $-1, %rcx
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00003114 jne          LBB0_573
	0x4d, 0x89, 0xd7, //0x0000311a movq         %r10, %r15
	0x4d, 0x29, 0xf7, //0x0000311d subq         %r14, %r15
	0x4c, 0x89, 0xf9, //0x00003120 movq         %r15, %rcx
	//0x00003123 LBB0_573
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00003123 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc4, 0x41, 0x7d, 0x6f, 0xeb, //0x00003128 vmovdqa      %ymm11, %ymm13
	0xc5, 0xfe, 0x6f, 0x25, 0x6b, 0xd1, 0xff, 0xff, //0x0000312d vmovdqu      $-11925(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x1d, 0x83, 0xd0, 0xff, 0xff, //0x00003135 vmovdqu      $-12157(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd8, //0x0000313d vmovdqa      %ymm0, %ymm11
	0xc5, 0x7d, 0x6f, 0xe1, //0x00003141 vmovdqa      %ymm1, %ymm12
	0x49, 0x01, 0xf2, //0x00003145 addq         %rsi, %r10
	0x48, 0x01, 0xd3, //0x00003148 addq         %rdx, %rbx
	0x0f, 0x85, 0x60, 0xff, 0xff, 0xff, //0x0000314b jne          LBB0_566
	0xe9, 0xfa, 0x09, 0x00, 0x00, //0x00003151 jmp          LBB0_681
	//0x00003156 LBB0_574
	0x4d, 0x29, 0xf2, //0x00003156 subq         %r14, %r10
	0x49, 0x83, 0xc2, 0x01, //0x00003159 addq         $1, %r10
	0xe9, 0xbe, 0xe1, 0xff, 0xff, //0x0000315d jmp          LBB0_232
	//0x00003162 LBB0_575
	0xc5, 0x7d, 0x7f, 0xe1, //0x00003162 vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd8, //0x00003166 vmovdqa      %ymm11, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xdd, //0x0000316a vmovdqa      %ymm13, %ymm11
	0xc4, 0x41, 0x7d, 0x6f, 0xe7, //0x0000316f vmovdqa      %ymm15, %ymm12
	0x49, 0x83, 0xff, 0xff, //0x00003174 cmpq         $-1, %r15
	0x0f, 0x85, 0x24, 0x00, 0x00, 0x00, //0x00003178 jne          LBB0_578
	0x48, 0x89, 0xd8, //0x0000317e movq         %rbx, %rax
	0x4c, 0x29, 0xf0, //0x00003181 subq         %r14, %rax
	0x4c, 0x0f, 0xbc, 0xda, //0x00003184 bsfq         %rdx, %r11
	0x49, 0x01, 0xc3, //0x00003188 addq         %rax, %r11
	0xe9, 0x12, 0x00, 0x00, 0x00, //0x0000318b jmp          LBB0_578
	//0x00003190 LBB0_577
	0xc5, 0x7d, 0x7f, 0xe1, //0x00003190 vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd8, //0x00003194 vmovdqa      %ymm11, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xdd, //0x00003198 vmovdqa      %ymm13, %ymm11
	0xc4, 0x41, 0x7d, 0x6f, 0xe7, //0x0000319d vmovdqa      %ymm15, %ymm12
	//0x000031a2 LBB0_578
	0x44, 0x89, 0xd0, //0x000031a2 movl         %r10d, %eax
	0xf7, 0xd0, //0x000031a5 notl         %eax
	0x21, 0xd0, //0x000031a7 andl         %edx, %eax
	0x8d, 0x3c, 0x00, //0x000031a9 leal         (%rax,%rax), %edi
	0x45, 0x8d, 0x3c, 0x42, //0x000031ac leal         (%r10,%rax,2), %r15d
	0xf7, 0xd7, //0x000031b0 notl         %edi
	0x21, 0xd7, //0x000031b2 andl         %edx, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x000031b4 andl         $-1431655766, %edi
	0x45, 0x31, 0xd2, //0x000031ba xorl         %r10d, %r10d
	0x01, 0xc7, //0x000031bd addl         %eax, %edi
	0x41, 0x0f, 0x92, 0xc2, //0x000031bf setb         %r10b
	0x01, 0xff, //0x000031c3 addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x000031c5 xorl         $1431655765, %edi
	0x44, 0x21, 0xff, //0x000031cb andl         %r15d, %edi
	0xf7, 0xd7, //0x000031ce notl         %edi
	0x21, 0xfe, //0x000031d0 andl         %edi, %esi
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x000031d2 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc4, 0x41, 0x7d, 0x6f, 0xfc, //0x000031d7 vmovdqa      %ymm12, %ymm15
	0xc4, 0x41, 0x7d, 0x6f, 0xeb, //0x000031dc vmovdqa      %ymm11, %ymm13
	0xc5, 0xfe, 0x6f, 0x25, 0xb7, 0xd0, 0xff, 0xff, //0x000031e1 vmovdqu      $-12105(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x1d, 0xcf, 0xcf, 0xff, 0xff, //0x000031e9 vmovdqu      $-12337(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd8, //0x000031f1 vmovdqa      %ymm0, %ymm11
	0xc5, 0x7d, 0x6f, 0xe1, //0x000031f5 vmovdqa      %ymm1, %ymm12
	0x4d, 0x89, 0xdf, //0x000031f9 movq         %r11, %r15
	0x48, 0x85, 0xf6, //0x000031fc testq        %rsi, %rsi
	0x0f, 0x85, 0xb2, 0xf6, 0xff, 0xff, //0x000031ff jne          LBB0_143
	//0x00003205 LBB0_579
	0x48, 0x83, 0xc3, 0x20, //0x00003205 addq         $32, %rbx
	0x48, 0x83, 0xc1, 0xe0, //0x00003209 addq         $-32, %rcx
	//0x0000320d LBB0_580
	0x4d, 0x85, 0xd2, //0x0000320d testq        %r10, %r10
	0x0f, 0x85, 0x8a, 0x03, 0x00, 0x00, //0x00003210 jne          LBB0_618
	0x4c, 0x89, 0xf6, //0x00003216 movq         %r14, %rsi
	0x48, 0xf7, 0xd6, //0x00003219 notq         %rsi
	0x4c, 0x89, 0xff, //0x0000321c movq         %r15, %rdi
	0x49, 0x89, 0xda, //0x0000321f movq         %rbx, %r10
	0x48, 0x85, 0xc9, //0x00003222 testq        %rcx, %rcx
	0x0f, 0x84, 0xa2, 0x00, 0x00, 0x00, //0x00003225 je           LBB0_592
	//0x0000322b LBB0_582
	0x48, 0x83, 0xc6, 0x01, //0x0000322b addq         $1, %rsi
	//0x0000322f LBB0_583
	0x31, 0xd2, //0x0000322f xorl         %edx, %edx
	//0x00003231 LBB0_584
	0x41, 0x0f, 0xb6, 0x1c, 0x12, //0x00003231 movzbl       (%r10,%rdx), %ebx
	0x80, 0xfb, 0x22, //0x00003236 cmpb         $34, %bl
	0x0f, 0x84, 0x87, 0x00, 0x00, 0x00, //0x00003239 je           LBB0_591
	0x80, 0xfb, 0x5c, //0x0000323f cmpb         $92, %bl
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00003242 je           LBB0_589
	0x48, 0x83, 0xc2, 0x01, //0x00003248 addq         $1, %rdx
	0x48, 0x39, 0xd1, //0x0000324c cmpq         %rdx, %rcx
	0x0f, 0x85, 0xdc, 0xff, 0xff, 0xff, //0x0000324f jne          LBB0_584
	0xe9, 0x7b, 0x00, 0x00, 0x00, //0x00003255 jmp          LBB0_587
	//0x0000325a LBB0_589
	0x48, 0x8d, 0x41, 0xff, //0x0000325a leaq         $-1(%rcx), %rax
	0x48, 0x39, 0xd0, //0x0000325e cmpq         %rdx, %rax
	0x0f, 0x84, 0xe9, 0x08, 0x00, 0x00, //0x00003261 je           LBB0_681
	0xc5, 0x7d, 0x7f, 0xe1, //0x00003267 vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd8, //0x0000326b vmovdqa      %ymm11, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xdd, //0x0000326f vmovdqa      %ymm13, %ymm11
	0x4a, 0x8d, 0x04, 0x16, //0x00003274 leaq         (%rsi,%r10), %rax
	0x48, 0x01, 0xd0, //0x00003278 addq         %rdx, %rax
	0x48, 0x83, 0xff, 0xff, //0x0000327b cmpq         $-1, %rdi
	0x4c, 0x0f, 0x44, 0xf8, //0x0000327f cmoveq       %rax, %r15
	0x48, 0x0f, 0x44, 0xf8, //0x00003283 cmoveq       %rax, %rdi
	0x49, 0x01, 0xd2, //0x00003287 addq         %rdx, %r10
	0x49, 0x83, 0xc2, 0x02, //0x0000328a addq         $2, %r10
	0x48, 0x89, 0xc8, //0x0000328e movq         %rcx, %rax
	0x48, 0x29, 0xd0, //0x00003291 subq         %rdx, %rax
	0x48, 0x83, 0xc0, 0xfe, //0x00003294 addq         $-2, %rax
	0x48, 0x83, 0xc1, 0xfe, //0x00003298 addq         $-2, %rcx
	0x48, 0x39, 0xd1, //0x0000329c cmpq         %rdx, %rcx
	0x48, 0x89, 0xc1, //0x0000329f movq         %rax, %rcx
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x000032a2 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0xfe, 0x6f, 0x25, 0xf1, 0xcf, 0xff, 0xff, //0x000032a7 vmovdqu      $-12303(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x1d, 0x09, 0xcf, 0xff, 0xff, //0x000032af vmovdqu      $-12535(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd8, //0x000032b7 vmovdqa      %ymm0, %ymm11
	0x0f, 0x85, 0x6e, 0xff, 0xff, 0xff, //0x000032bb jne          LBB0_583
	0xe9, 0x8a, 0x08, 0x00, 0x00, //0x000032c1 jmp          LBB0_681
	//0x000032c6 LBB0_591
	0x49, 0x01, 0xd2, //0x000032c6 addq         %rdx, %r10
	0x49, 0x83, 0xc2, 0x01, //0x000032c9 addq         $1, %r10
	//0x000032cd LBB0_592
	0x4d, 0x29, 0xf2, //0x000032cd subq         %r14, %r10
	0xe9, 0xb7, 0xe4, 0xff, 0xff, //0x000032d0 jmp          LBB0_298
	//0x000032d5 LBB0_587
	0x80, 0xfb, 0x22, //0x000032d5 cmpb         $34, %bl
	0x0f, 0x85, 0x72, 0x08, 0x00, 0x00, //0x000032d8 jne          LBB0_681
	0xc5, 0x7d, 0x7f, 0xe1, //0x000032de vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd8, //0x000032e2 vmovdqa      %ymm11, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xdd, //0x000032e6 vmovdqa      %ymm13, %ymm11
	0x49, 0x01, 0xca, //0x000032eb addq         %rcx, %r10
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x000032ee vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0xfe, 0x6f, 0x25, 0xa5, 0xcf, 0xff, 0xff, //0x000032f3 vmovdqu      $-12379(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x1d, 0xbd, 0xce, 0xff, 0xff, //0x000032fb vmovdqu      $-12611(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd8, //0x00003303 vmovdqa      %ymm0, %ymm11
	0xe9, 0xc1, 0xff, 0xff, 0xff, //0x00003307 jmp          LBB0_592
	//0x0000330c LBB0_593
	0xc5, 0x7d, 0x7f, 0xe2, //0x0000330c vmovdqa      %ymm12, %ymm2
	0xc5, 0x7d, 0x7f, 0xd9, //0x00003310 vmovdqa      %ymm11, %ymm1
	0xc4, 0x41, 0x7d, 0x6f, 0xdd, //0x00003314 vmovdqa      %ymm13, %ymm11
	0xc4, 0x41, 0x7d, 0x6f, 0xe7, //0x00003319 vmovdqa      %ymm15, %ymm12
	0x4c, 0x89, 0x3c, 0x24, //0x0000331e movq         %r15, (%rsp)
	0x49, 0x83, 0xff, 0xff, //0x00003322 cmpq         $-1, %r15
	0x0f, 0x85, 0x2c, 0x00, 0x00, 0x00, //0x00003326 jne          LBB0_596
	0x48, 0x89, 0xd8, //0x0000332c movq         %rbx, %rax
	0x4c, 0x29, 0xf0, //0x0000332f subq         %r14, %rax
	0x48, 0x0f, 0xbc, 0xf2, //0x00003332 bsfq         %rdx, %rsi
	0x48, 0x01, 0xc6, //0x00003336 addq         %rax, %rsi
	0x48, 0x89, 0x34, 0x24, //0x00003339 movq         %rsi, (%rsp)
	0xe9, 0x16, 0x00, 0x00, 0x00, //0x0000333d jmp          LBB0_596
	//0x00003342 LBB0_595
	0x4c, 0x89, 0x3c, 0x24, //0x00003342 movq         %r15, (%rsp)
	0xc5, 0x7d, 0x7f, 0xe2, //0x00003346 vmovdqa      %ymm12, %ymm2
	0xc5, 0x7d, 0x7f, 0xd9, //0x0000334a vmovdqa      %ymm11, %ymm1
	0xc4, 0x41, 0x7d, 0x6f, 0xdd, //0x0000334e vmovdqa      %ymm13, %ymm11
	0xc4, 0x41, 0x7d, 0x6f, 0xe7, //0x00003353 vmovdqa      %ymm15, %ymm12
	//0x00003358 LBB0_596
	0x44, 0x89, 0xd8, //0x00003358 movl         %r11d, %eax
	0xf7, 0xd0, //0x0000335b notl         %eax
	0x21, 0xd0, //0x0000335d andl         %edx, %eax
	0x8d, 0x34, 0x00, //0x0000335f leal         (%rax,%rax), %esi
	0x41, 0x8d, 0x3c, 0x43, //0x00003362 leal         (%r11,%rax,2), %edi
	0xf7, 0xd6, //0x00003366 notl         %esi
	0x21, 0xd6, //0x00003368 andl         %edx, %esi
	0x81, 0xe6, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000336a andl         $-1431655766, %esi
	0x45, 0x31, 0xdb, //0x00003370 xorl         %r11d, %r11d
	0x01, 0xc6, //0x00003373 addl         %eax, %esi
	0x41, 0x0f, 0x92, 0xc3, //0x00003375 setb         %r11b
	0x01, 0xf6, //0x00003379 addl         %esi, %esi
	0x81, 0xf6, 0x55, 0x55, 0x55, 0x55, //0x0000337b xorl         $1431655765, %esi
	0x21, 0xfe, //0x00003381 andl         %edi, %esi
	0xf7, 0xd6, //0x00003383 notl         %esi
	0x41, 0x21, 0xf2, //0x00003385 andl         %esi, %r10d
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00003388 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc4, 0x41, 0x7d, 0x6f, 0xfc, //0x0000338d vmovdqa      %ymm12, %ymm15
	0xc4, 0x41, 0x7d, 0x6f, 0xeb, //0x00003392 vmovdqa      %ymm11, %ymm13
	0xc5, 0xfe, 0x6f, 0x25, 0x01, 0xcf, 0xff, 0xff, //0x00003397 vmovdqu      $-12543(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x1d, 0x19, 0xce, 0xff, 0xff, //0x0000339f vmovdqu      $-12775(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd9, //0x000033a7 vmovdqa      %ymm1, %ymm11
	0xc5, 0x7d, 0x6f, 0xe2, //0x000033ab vmovdqa      %ymm2, %ymm12
	0x4c, 0x8b, 0x3c, 0x24, //0x000033af movq         (%rsp), %r15
	0x4d, 0x85, 0xd2, //0x000033b3 testq        %r10, %r10
	0x0f, 0x85, 0x53, 0xf5, 0xff, 0xff, //0x000033b6 jne          LBB0_224
	//0x000033bc LBB0_597
	0xba, 0x40, 0x00, 0x00, 0x00, //0x000033bc movl         $64, %edx
	//0x000033c1 LBB0_598
	0xc5, 0xbd, 0x64, 0xc8, //0x000033c1 vpcmpgtb     %ymm0, %ymm8, %ymm1
	0xc4, 0xc1, 0x7d, 0x64, 0xc1, //0x000033c5 vpcmpgtb     %ymm9, %ymm0, %ymm0
	0xc5, 0xf5, 0xdb, 0xc0, //0x000033ca vpand        %ymm0, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x000033ce vpmovmskb    %ymm0, %esi
	0x0f, 0xbc, 0xfe, //0x000033d2 bsfl         %esi, %edi
	0x4d, 0x85, 0xd2, //0x000033d5 testq        %r10, %r10
	0x0f, 0x84, 0x25, 0x00, 0x00, 0x00, //0x000033d8 je           LBB0_601
	0x85, 0xf6, //0x000033de testl        %esi, %esi
	0xb8, 0x40, 0x00, 0x00, 0x00, //0x000033e0 movl         $64, %eax
	0x0f, 0x44, 0xf8, //0x000033e5 cmovel       %eax, %edi
	0x48, 0x39, 0xfa, //0x000033e8 cmpq         %rdi, %rdx
	0x49, 0x89, 0xda, //0x000033eb movq         %rbx, %r10
	0x0f, 0x87, 0x9b, 0x09, 0x00, 0x00, //0x000033ee ja           LBB0_721
	0x4d, 0x29, 0xf2, //0x000033f4 subq         %r14, %r10
	0x49, 0x01, 0xd2, //0x000033f7 addq         %rdx, %r10
	0x49, 0x83, 0xc2, 0x01, //0x000033fa addq         $1, %r10
	0xe9, 0x89, 0xe3, 0xff, 0xff, //0x000033fe jmp          LBB0_298
	//0x00003403 LBB0_601
	0x85, 0xf6, //0x00003403 testl        %esi, %esi
	0x49, 0x89, 0xda, //0x00003405 movq         %rbx, %r10
	0x0f, 0x85, 0x8e, 0x09, 0x00, 0x00, //0x00003408 jne          LBB0_722
	0x49, 0x83, 0xc2, 0x20, //0x0000340e addq         $32, %r10
	0x48, 0x83, 0xc1, 0xe0, //0x00003412 addq         $-32, %rcx
	//0x00003416 LBB0_603
	0x4d, 0x85, 0xdb, //0x00003416 testq        %r11, %r11
	0x0f, 0x85, 0xe2, 0x01, 0x00, 0x00, //0x00003419 jne          LBB0_620
	0x4c, 0x89, 0xfe, //0x0000341f movq         %r15, %rsi
	0x48, 0x85, 0xc9, //0x00003422 testq        %rcx, %rcx
	0x0f, 0x84, 0x25, 0x07, 0x00, 0x00, //0x00003425 je           LBB0_681
	//0x0000342b LBB0_605
	0x41, 0x0f, 0xb6, 0x12, //0x0000342b movzbl       (%r10), %edx
	0x80, 0xfa, 0x22, //0x0000342f cmpb         $34, %dl
	0x0f, 0x84, 0x98, 0x00, 0x00, 0x00, //0x00003432 je           LBB0_613
	0x80, 0xfa, 0x5c, //0x00003438 cmpb         $92, %dl
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x0000343b je           LBB0_609
	0x80, 0xfa, 0x1f, //0x00003441 cmpb         $31, %dl
	0x0f, 0x86, 0x5d, 0x09, 0x00, 0x00, //0x00003444 jbe          LBB0_723
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x0000344a movq         $-1, %rdx
	0xbf, 0x01, 0x00, 0x00, 0x00, //0x00003451 movl         $1, %edi
	0x49, 0x01, 0xfa, //0x00003456 addq         %rdi, %r10
	0x48, 0x01, 0xd1, //0x00003459 addq         %rdx, %rcx
	0x0f, 0x85, 0xc9, 0xff, 0xff, 0xff, //0x0000345c jne          LBB0_605
	0xe9, 0xe9, 0x06, 0x00, 0x00, //0x00003462 jmp          LBB0_681
	//0x00003467 LBB0_609
	0x48, 0x83, 0xf9, 0x01, //0x00003467 cmpq         $1, %rcx
	0x0f, 0x84, 0xdf, 0x06, 0x00, 0x00, //0x0000346b je           LBB0_681
	0xc5, 0x7d, 0x7f, 0xe1, //0x00003471 vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd8, //0x00003475 vmovdqa      %ymm11, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xdd, //0x00003479 vmovdqa      %ymm13, %ymm11
	0x48, 0xc7, 0xc2, 0xfe, 0xff, 0xff, 0xff, //0x0000347e movq         $-2, %rdx
	0xbf, 0x02, 0x00, 0x00, 0x00, //0x00003485 movl         $2, %edi
	0x48, 0x83, 0xfe, 0xff, //0x0000348a cmpq         $-1, %rsi
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x0000348e jne          LBB0_612
	0x4d, 0x89, 0xd7, //0x00003494 movq         %r10, %r15
	0x4d, 0x29, 0xf7, //0x00003497 subq         %r14, %r15
	0x4c, 0x89, 0xfe, //0x0000349a movq         %r15, %rsi
	//0x0000349d LBB0_612
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x0000349d vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc4, 0x41, 0x7d, 0x6f, 0xeb, //0x000034a2 vmovdqa      %ymm11, %ymm13
	0xc5, 0xfe, 0x6f, 0x25, 0xf1, 0xcd, 0xff, 0xff, //0x000034a7 vmovdqu      $-12815(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x1d, 0x09, 0xcd, 0xff, 0xff, //0x000034af vmovdqu      $-13047(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd8, //0x000034b7 vmovdqa      %ymm0, %ymm11
	0xc5, 0x7d, 0x6f, 0xe1, //0x000034bb vmovdqa      %ymm1, %ymm12
	0x49, 0x01, 0xfa, //0x000034bf addq         %rdi, %r10
	0x48, 0x01, 0xd1, //0x000034c2 addq         %rdx, %rcx
	0x0f, 0x85, 0x60, 0xff, 0xff, 0xff, //0x000034c5 jne          LBB0_605
	0xe9, 0x80, 0x06, 0x00, 0x00, //0x000034cb jmp          LBB0_681
	//0x000034d0 LBB0_613
	0x4d, 0x29, 0xf2, //0x000034d0 subq         %r14, %r10
	0x49, 0x83, 0xc2, 0x01, //0x000034d3 addq         $1, %r10
	0xe9, 0xb0, 0xe2, 0xff, 0xff, //0x000034d7 jmp          LBB0_298
	//0x000034dc LBB0_614
	0x48, 0x85, 0xdb, //0x000034dc testq        %rbx, %rbx
	0x0f, 0x84, 0x6b, 0x06, 0x00, 0x00, //0x000034df je           LBB0_681
	0xc5, 0x7d, 0x7f, 0xe1, //0x000034e5 vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd8, //0x000034e9 vmovdqa      %ymm11, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xdd, //0x000034ed vmovdqa      %ymm13, %ymm11
	0x4c, 0x89, 0xf7, //0x000034f2 movq         %r14, %rdi
	0x48, 0xf7, 0xd7, //0x000034f5 notq         %rdi
	0x49, 0x89, 0xca, //0x000034f8 movq         %rcx, %r10
	0x48, 0x8d, 0x04, 0x39, //0x000034fb leaq         (%rcx,%rdi), %rax
	0x49, 0x83, 0xff, 0xff, //0x000034ff cmpq         $-1, %r15
	0x4c, 0x89, 0xfe, //0x00003503 movq         %r15, %rsi
	0x4c, 0x0f, 0x44, 0xf8, //0x00003506 cmoveq       %rax, %r15
	0x48, 0x0f, 0x44, 0xf0, //0x0000350a cmoveq       %rax, %rsi
	0x49, 0x83, 0xc2, 0x01, //0x0000350e addq         $1, %r10
	0x48, 0x83, 0xc3, 0xff, //0x00003512 addq         $-1, %rbx
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00003516 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0xfe, 0x6f, 0x25, 0x7d, 0xcd, 0xff, 0xff, //0x0000351b vmovdqu      $-12931(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x1d, 0x95, 0xcc, 0xff, 0xff, //0x00003523 vmovdqu      $-13163(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd8, //0x0000352b vmovdqa      %ymm0, %ymm11
	0x48, 0x85, 0xdb, //0x0000352f testq        %rbx, %rbx
	0x0f, 0x85, 0xac, 0xf8, 0xff, 0xff, //0x00003532 jne          LBB0_536
	0xe9, 0x49, 0xf9, 0xff, 0xff, //0x00003538 jmp          LBB0_546
	//0x0000353d LBB0_616
	0x48, 0x85, 0xdb, //0x0000353d testq        %rbx, %rbx
	0x0f, 0x84, 0x0a, 0x06, 0x00, 0x00, //0x00003540 je           LBB0_681
	0xc5, 0x7d, 0x7f, 0xe1, //0x00003546 vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd8, //0x0000354a vmovdqa      %ymm11, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xdd, //0x0000354e vmovdqa      %ymm13, %ymm11
	0x4c, 0x89, 0xf1, //0x00003553 movq         %r14, %rcx
	0x48, 0xf7, 0xd1, //0x00003556 notq         %rcx
	0x4c, 0x01, 0xd1, //0x00003559 addq         %r10, %rcx
	0x4c, 0x89, 0xfa, //0x0000355c movq         %r15, %rdx
	0x49, 0x83, 0xff, 0xff, //0x0000355f cmpq         $-1, %r15
	0x4c, 0x89, 0xf8, //0x00003563 movq         %r15, %rax
	0x48, 0x0f, 0x44, 0xc1, //0x00003566 cmoveq       %rcx, %rax
	0x49, 0x0f, 0x45, 0xcf, //0x0000356a cmovneq      %r15, %rcx
	0x49, 0x83, 0xc2, 0x01, //0x0000356e addq         $1, %r10
	0x48, 0x83, 0xc3, 0xff, //0x00003572 addq         $-1, %rbx
	0x49, 0x89, 0xc7, //0x00003576 movq         %rax, %r15
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00003579 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0xfe, 0x6f, 0x25, 0x1a, 0xcd, 0xff, 0xff, //0x0000357e vmovdqu      $-13030(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x1d, 0x32, 0xcc, 0xff, 0xff, //0x00003586 vmovdqu      $-13262(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd8, //0x0000358e vmovdqa      %ymm0, %ymm11
	0x48, 0x85, 0xdb, //0x00003592 testq        %rbx, %rbx
	0x0f, 0x85, 0x16, 0xfb, 0xff, 0xff, //0x00003595 jne          LBB0_566
	0xe9, 0xb0, 0x05, 0x00, 0x00, //0x0000359b jmp          LBB0_681
	//0x000035a0 LBB0_618
	0x48, 0x85, 0xc9, //0x000035a0 testq        %rcx, %rcx
	0x0f, 0x84, 0xa7, 0x05, 0x00, 0x00, //0x000035a3 je           LBB0_681
	0xc5, 0x7d, 0x7f, 0xe1, //0x000035a9 vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd8, //0x000035ad vmovdqa      %ymm11, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xdd, //0x000035b1 vmovdqa      %ymm13, %ymm11
	0x4c, 0x89, 0xf6, //0x000035b6 movq         %r14, %rsi
	0x48, 0xf7, 0xd6, //0x000035b9 notq         %rsi
	0x49, 0x89, 0xda, //0x000035bc movq         %rbx, %r10
	0x48, 0x8d, 0x04, 0x33, //0x000035bf leaq         (%rbx,%rsi), %rax
	0x49, 0x83, 0xff, 0xff, //0x000035c3 cmpq         $-1, %r15
	0x4c, 0x89, 0xff, //0x000035c7 movq         %r15, %rdi
	0x4c, 0x0f, 0x44, 0xf8, //0x000035ca cmoveq       %rax, %r15
	0x48, 0x0f, 0x44, 0xf8, //0x000035ce cmoveq       %rax, %rdi
	0x49, 0x83, 0xc2, 0x01, //0x000035d2 addq         $1, %r10
	0x48, 0x83, 0xc1, 0xff, //0x000035d6 addq         $-1, %rcx
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x000035da vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0xfe, 0x6f, 0x25, 0xb9, 0xcc, 0xff, 0xff, //0x000035df vmovdqu      $-13127(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x1d, 0xd1, 0xcb, 0xff, 0xff, //0x000035e7 vmovdqu      $-13359(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd8, //0x000035ef vmovdqa      %ymm0, %ymm11
	0x48, 0x85, 0xc9, //0x000035f3 testq        %rcx, %rcx
	0x0f, 0x85, 0x2f, 0xfc, 0xff, 0xff, //0x000035f6 jne          LBB0_582
	0xe9, 0xcc, 0xfc, 0xff, 0xff, //0x000035fc jmp          LBB0_592
	//0x00003601 LBB0_620
	0x48, 0x85, 0xc9, //0x00003601 testq        %rcx, %rcx
	0x0f, 0x84, 0x46, 0x05, 0x00, 0x00, //0x00003604 je           LBB0_681
	0xc5, 0x7d, 0x7f, 0xe1, //0x0000360a vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd8, //0x0000360e vmovdqa      %ymm11, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xdd, //0x00003612 vmovdqa      %ymm13, %ymm11
	0x4c, 0x89, 0xf6, //0x00003617 movq         %r14, %rsi
	0x48, 0xf7, 0xd6, //0x0000361a notq         %rsi
	0x4c, 0x01, 0xd6, //0x0000361d addq         %r10, %rsi
	0x4c, 0x89, 0xfa, //0x00003620 movq         %r15, %rdx
	0x49, 0x83, 0xff, 0xff, //0x00003623 cmpq         $-1, %r15
	0x4c, 0x89, 0xf8, //0x00003627 movq         %r15, %rax
	0x48, 0x0f, 0x44, 0xc6, //0x0000362a cmoveq       %rsi, %rax
	0x49, 0x0f, 0x45, 0xf7, //0x0000362e cmovneq      %r15, %rsi
	0x49, 0x83, 0xc2, 0x01, //0x00003632 addq         $1, %r10
	0x48, 0x83, 0xc1, 0xff, //0x00003636 addq         $-1, %rcx
	0x49, 0x89, 0xc7, //0x0000363a movq         %rax, %r15
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x0000363d vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0xfe, 0x6f, 0x25, 0x56, 0xcc, 0xff, 0xff, //0x00003642 vmovdqu      $-13226(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x1d, 0x6e, 0xcb, 0xff, 0xff, //0x0000364a vmovdqu      $-13458(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd8, //0x00003652 vmovdqa      %ymm0, %ymm11
	0x48, 0x85, 0xc9, //0x00003656 testq        %rcx, %rcx
	0x0f, 0x85, 0xcc, 0xfd, 0xff, 0xff, //0x00003659 jne          LBB0_605
	0xe9, 0xec, 0x04, 0x00, 0x00, //0x0000365f jmp          LBB0_681
	//0x00003664 LBB0_622
	0x49, 0x39, 0xf2, //0x00003664 cmpq         %rsi, %r10
	0x0f, 0x84, 0x28, 0x05, 0x00, 0x00, //0x00003667 je           LBB0_711
	0xc5, 0x7d, 0x7f, 0xe1, //0x0000366d vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd8, //0x00003671 vmovdqa      %ymm11, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xdd, //0x00003675 vmovdqa      %ymm13, %ymm11
	0x49, 0x01, 0xf1, //0x0000367a addq         %rsi, %r9
	0x49, 0x83, 0xc1, 0x01, //0x0000367d addq         $1, %r9
	0x48, 0xf7, 0xd6, //0x00003681 notq         %rsi
	0x49, 0x01, 0xf2, //0x00003684 addq         %rsi, %r10
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x00003687 movq         $24(%rsp), %r11
	0x48, 0x8b, 0x7c, 0x24, 0x10, //0x0000368c movq         $16(%rsp), %rdi
	0x4c, 0x8b, 0x7c, 0x24, 0x08, //0x00003691 movq         $8(%rsp), %r15
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00003696 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0xfe, 0x6f, 0x25, 0xfd, 0xcb, 0xff, 0xff, //0x0000369b vmovdqu      $-13315(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x1d, 0x15, 0xcb, 0xff, 0xff, //0x000036a3 vmovdqu      $-13547(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd8, //0x000036ab vmovdqa      %ymm0, %ymm11
	0x4d, 0x85, 0xd2, //0x000036af testq        %r10, %r10
	0x0f, 0x8f, 0x25, 0x00, 0x00, 0x00, //0x000036b2 jg           LBB0_626
	0xe9, 0xd8, 0x04, 0x00, 0x00, //0x000036b8 jmp          LBB0_711
	//0x000036bd LBB0_624
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x000036bd movq         $-2, %rcx
	0xba, 0x02, 0x00, 0x00, 0x00, //0x000036c4 movl         $2, %edx
	0x49, 0x01, 0xd1, //0x000036c9 addq         %rdx, %r9
	0x48, 0xc7, 0x04, 0x24, 0xff, 0xff, 0xff, 0xff, //0x000036cc movq         $-1, (%rsp)
	0x49, 0x01, 0xca, //0x000036d4 addq         %rcx, %r10
	0x0f, 0x8e, 0xb8, 0x04, 0x00, 0x00, //0x000036d7 jle          LBB0_711
	//0x000036dd LBB0_626
	0x41, 0x0f, 0xb6, 0x09, //0x000036dd movzbl       (%r9), %ecx
	0x80, 0xf9, 0x5c, //0x000036e1 cmpb         $92, %cl
	0x0f, 0x84, 0xd3, 0xff, 0xff, 0xff, //0x000036e4 je           LBB0_624
	0x80, 0xf9, 0x22, //0x000036ea cmpb         $34, %cl
	0x0f, 0x84, 0x25, 0x00, 0x00, 0x00, //0x000036ed je           LBB0_629
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x000036f3 movq         $-1, %rcx
	0xba, 0x01, 0x00, 0x00, 0x00, //0x000036fa movl         $1, %edx
	0x49, 0x01, 0xd1, //0x000036ff addq         %rdx, %r9
	0x48, 0xc7, 0x04, 0x24, 0xff, 0xff, 0xff, 0xff, //0x00003702 movq         $-1, (%rsp)
	0x49, 0x01, 0xca, //0x0000370a addq         %rcx, %r10
	0x0f, 0x8f, 0xca, 0xff, 0xff, 0xff, //0x0000370d jg           LBB0_626
	0xe9, 0x7d, 0x04, 0x00, 0x00, //0x00003713 jmp          LBB0_711
	//0x00003718 LBB0_629
	0x4d, 0x29, 0xf1, //0x00003718 subq         %r14, %r9
	0x49, 0x83, 0xc1, 0x01, //0x0000371b addq         $1, %r9
	0x4d, 0x89, 0x4d, 0x00, //0x0000371f movq         %r9, (%r13)
	0x4d, 0x85, 0xc0, //0x00003723 testq        %r8, %r8
	0x0f, 0x8f, 0x74, 0xcc, 0xff, 0xff, //0x00003726 jg           LBB0_3
	0xe9, 0xfb, 0x03, 0x00, 0x00, //0x0000372c jmp          LBB0_679
	//0x00003731 LBB0_630
	0xc5, 0x7d, 0x7f, 0xe1, //0x00003731 vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd8, //0x00003735 vmovdqa      %ymm11, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xdd, //0x00003739 vmovdqa      %ymm13, %ymm11
	0xc4, 0x41, 0x7d, 0x6f, 0xe7, //0x0000373e vmovdqa      %ymm15, %ymm12
	0x4c, 0x89, 0x1c, 0x24, //0x00003743 movq         %r11, (%rsp)
	0x49, 0x83, 0xfb, 0xff, //0x00003747 cmpq         $-1, %r11
	0x0f, 0x85, 0x2c, 0x00, 0x00, 0x00, //0x0000374b jne          LBB0_633
	0x48, 0x89, 0xf8, //0x00003751 movq         %rdi, %rax
	0x4c, 0x29, 0xf0, //0x00003754 subq         %r14, %rax
	0x48, 0x0f, 0xbc, 0xca, //0x00003757 bsfq         %rdx, %rcx
	0x48, 0x01, 0xc1, //0x0000375b addq         %rax, %rcx
	0x48, 0x89, 0x0c, 0x24, //0x0000375e movq         %rcx, (%rsp)
	0xe9, 0x16, 0x00, 0x00, 0x00, //0x00003762 jmp          LBB0_633
	//0x00003767 LBB0_632
	0x4c, 0x89, 0x1c, 0x24, //0x00003767 movq         %r11, (%rsp)
	0xc5, 0x7d, 0x7f, 0xe1, //0x0000376b vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd8, //0x0000376f vmovdqa      %ymm11, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xdd, //0x00003773 vmovdqa      %ymm13, %ymm11
	0xc4, 0x41, 0x7d, 0x6f, 0xe7, //0x00003778 vmovdqa      %ymm15, %ymm12
	//0x0000377d LBB0_633
	0x44, 0x89, 0xd0, //0x0000377d movl         %r10d, %eax
	0xf7, 0xd0, //0x00003780 notl         %eax
	0x21, 0xd0, //0x00003782 andl         %edx, %eax
	0x8d, 0x0c, 0x00, //0x00003784 leal         (%rax,%rax), %ecx
	0x41, 0x8d, 0x1c, 0x42, //0x00003787 leal         (%r10,%rax,2), %ebx
	0xf7, 0xd1, //0x0000378b notl         %ecx
	0x21, 0xd1, //0x0000378d andl         %edx, %ecx
	0x81, 0xe1, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000378f andl         $-1431655766, %ecx
	0x45, 0x31, 0xd2, //0x00003795 xorl         %r10d, %r10d
	0x01, 0xc1, //0x00003798 addl         %eax, %ecx
	0x41, 0x0f, 0x92, 0xc2, //0x0000379a setb         %r10b
	0x01, 0xc9, //0x0000379e addl         %ecx, %ecx
	0x81, 0xf1, 0x55, 0x55, 0x55, 0x55, //0x000037a0 xorl         $1431655765, %ecx
	0x21, 0xd9, //0x000037a6 andl         %ebx, %ecx
	0xf7, 0xd1, //0x000037a8 notl         %ecx
	0x21, 0xce, //0x000037aa andl         %ecx, %esi
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x000037ac vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc4, 0x41, 0x7d, 0x6f, 0xfc, //0x000037b1 vmovdqa      %ymm12, %ymm15
	0xc4, 0x41, 0x7d, 0x6f, 0xeb, //0x000037b6 vmovdqa      %ymm11, %ymm13
	0xc5, 0xfe, 0x6f, 0x25, 0xdd, 0xca, 0xff, 0xff, //0x000037bb vmovdqu      $-13603(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x1d, 0xf5, 0xc9, 0xff, 0xff, //0x000037c3 vmovdqu      $-13835(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd8, //0x000037cb vmovdqa      %ymm0, %ymm11
	0xc5, 0x7d, 0x6f, 0xe1, //0x000037cf vmovdqa      %ymm1, %ymm12
	0x4c, 0x8b, 0x1c, 0x24, //0x000037d3 movq         (%rsp), %r11
	0xc5, 0x7d, 0x7f, 0xea, //0x000037d7 vmovdqa      %ymm13, %ymm2
	0x48, 0x85, 0xf6, //0x000037db testq        %rsi, %rsi
	0x0f, 0x85, 0xfe, 0xf1, 0xff, 0xff, //0x000037de jne          LBB0_247
	//0x000037e4 LBB0_634
	0xc5, 0x7d, 0x7f, 0xe1, //0x000037e4 vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd8, //0x000037e8 vmovdqa      %ymm11, %ymm0
	0xc5, 0x7d, 0x6f, 0xda, //0x000037ec vmovdqa      %ymm2, %ymm11
	0x48, 0x83, 0xc7, 0x20, //0x000037f0 addq         $32, %rdi
	0x49, 0x83, 0xc0, 0xe0, //0x000037f4 addq         $-32, %r8
	//0x000037f8 LBB0_635
	0x4d, 0x85, 0xd2, //0x000037f8 testq        %r10, %r10
	0x0f, 0x85, 0x6e, 0x02, 0x00, 0x00, //0x000037fb jne          LBB0_669
	0x4c, 0x89, 0xf6, //0x00003801 movq         %r14, %rsi
	0x48, 0xf7, 0xd6, //0x00003804 notq         %rsi
	0x4d, 0x89, 0xda, //0x00003807 movq         %r11, %r10
	0x4d, 0x85, 0xc0, //0x0000380a testq        %r8, %r8
	0x0f, 0x84, 0x95, 0x02, 0x00, 0x00, //0x0000380d je           LBB0_671
	//0x00003813 LBB0_637
	0x48, 0x83, 0xc6, 0x01, //0x00003813 addq         $1, %rsi
	//0x00003817 LBB0_638
	0x31, 0xd2, //0x00003817 xorl         %edx, %edx
	0x48, 0x89, 0xf9, //0x00003819 movq         %rdi, %rcx
	//0x0000381c LBB0_639
	0x0f, 0xb6, 0x1c, 0x11, //0x0000381c movzbl       (%rcx,%rdx), %ebx
	0x80, 0xfb, 0x22, //0x00003820 cmpb         $34, %bl
	0x0f, 0x84, 0x61, 0x00, 0x00, 0x00, //0x00003823 je           LBB0_646
	0x80, 0xfb, 0x5c, //0x00003829 cmpb         $92, %bl
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x0000382c je           LBB0_644
	0x48, 0x83, 0xc2, 0x01, //0x00003832 addq         $1, %rdx
	0x49, 0x39, 0xd0, //0x00003836 cmpq         %rdx, %r8
	0x0f, 0x85, 0xdd, 0xff, 0xff, 0xff, //0x00003839 jne          LBB0_639
	0xe9, 0x52, 0x00, 0x00, 0x00, //0x0000383f jmp          LBB0_642
	//0x00003844 LBB0_644
	0x49, 0x8d, 0x40, 0xff, //0x00003844 leaq         $-1(%r8), %rax
	0x48, 0x39, 0xd0, //0x00003848 cmpq         %rdx, %rax
	0x0f, 0x84, 0xad, 0x04, 0x00, 0x00, //0x0000384b je           LBB0_707
	0x48, 0x8d, 0x04, 0x3e, //0x00003851 leaq         (%rsi,%rdi), %rax
	0x48, 0x01, 0xd0, //0x00003855 addq         %rdx, %rax
	0x49, 0x83, 0xfa, 0xff, //0x00003858 cmpq         $-1, %r10
	0x4c, 0x0f, 0x44, 0xd8, //0x0000385c cmoveq       %rax, %r11
	0x4c, 0x0f, 0x44, 0xd0, //0x00003860 cmoveq       %rax, %r10
	0x48, 0x01, 0xd7, //0x00003864 addq         %rdx, %rdi
	0x48, 0x83, 0xc7, 0x02, //0x00003867 addq         $2, %rdi
	0x4c, 0x89, 0xc0, //0x0000386b movq         %r8, %rax
	0x48, 0x29, 0xd0, //0x0000386e subq         %rdx, %rax
	0x48, 0x83, 0xc0, 0xfe, //0x00003871 addq         $-2, %rax
	0x49, 0x83, 0xc0, 0xfe, //0x00003875 addq         $-2, %r8
	0x49, 0x39, 0xd0, //0x00003879 cmpq         %rdx, %r8
	0x49, 0x89, 0xc0, //0x0000387c movq         %rax, %r8
	0x0f, 0x85, 0x92, 0xff, 0xff, 0xff, //0x0000387f jne          LBB0_638
	0xe9, 0x74, 0x04, 0x00, 0x00, //0x00003885 jmp          LBB0_707
	//0x0000388a LBB0_646
	0x48, 0x01, 0xd1, //0x0000388a addq         %rdx, %rcx
	0x48, 0x83, 0xc1, 0x01, //0x0000388d addq         $1, %rcx
	0xe9, 0x15, 0x02, 0x00, 0x00, //0x00003891 jmp          LBB0_672
	//0x00003896 LBB0_642
	0x80, 0xfb, 0x22, //0x00003896 cmpb         $34, %bl
	0x0f, 0x85, 0x5f, 0x04, 0x00, 0x00, //0x00003899 jne          LBB0_707
	0x48, 0x89, 0xf9, //0x0000389f movq         %rdi, %rcx
	0x4c, 0x01, 0xc1, //0x000038a2 addq         %r8, %rcx
	0xe9, 0x01, 0x02, 0x00, 0x00, //0x000038a5 jmp          LBB0_672
	//0x000038aa LBB0_647
	0xc5, 0x7d, 0x7f, 0xe2, //0x000038aa vmovdqa      %ymm12, %ymm2
	0xc5, 0x7d, 0x7f, 0xd9, //0x000038ae vmovdqa      %ymm11, %ymm1
	0xc4, 0x41, 0x7d, 0x6f, 0xdd, //0x000038b2 vmovdqa      %ymm13, %ymm11
	0xc4, 0x41, 0x7d, 0x6f, 0xe7, //0x000038b7 vmovdqa      %ymm15, %ymm12
	0x49, 0x83, 0xfa, 0xff, //0x000038bc cmpq         $-1, %r10
	0x0f, 0x85, 0x24, 0x00, 0x00, 0x00, //0x000038c0 jne          LBB0_650
	0x48, 0x89, 0xd8, //0x000038c6 movq         %rbx, %rax
	0x4c, 0x29, 0xf0, //0x000038c9 subq         %r14, %rax
	0x4c, 0x0f, 0xbc, 0xd2, //0x000038cc bsfq         %rdx, %r10
	0x49, 0x01, 0xc2, //0x000038d0 addq         %rax, %r10
	0xe9, 0x12, 0x00, 0x00, 0x00, //0x000038d3 jmp          LBB0_650
	//0x000038d8 LBB0_649
	0xc5, 0x7d, 0x7f, 0xe2, //0x000038d8 vmovdqa      %ymm12, %ymm2
	0xc5, 0x7d, 0x7f, 0xd9, //0x000038dc vmovdqa      %ymm11, %ymm1
	0xc4, 0x41, 0x7d, 0x6f, 0xdd, //0x000038e0 vmovdqa      %ymm13, %ymm11
	0xc4, 0x41, 0x7d, 0x6f, 0xe7, //0x000038e5 vmovdqa      %ymm15, %ymm12
	//0x000038ea LBB0_650
	0x44, 0x89, 0xd8, //0x000038ea movl         %r11d, %eax
	0xf7, 0xd0, //0x000038ed notl         %eax
	0x21, 0xd0, //0x000038ef andl         %edx, %eax
	0x8d, 0x34, 0x00, //0x000038f1 leal         (%rax,%rax), %esi
	0x41, 0x8d, 0x3c, 0x43, //0x000038f4 leal         (%r11,%rax,2), %edi
	0xf7, 0xd6, //0x000038f8 notl         %esi
	0x21, 0xd6, //0x000038fa andl         %edx, %esi
	0x81, 0xe6, 0xaa, 0xaa, 0xaa, 0xaa, //0x000038fc andl         $-1431655766, %esi
	0x45, 0x31, 0xdb, //0x00003902 xorl         %r11d, %r11d
	0x01, 0xc6, //0x00003905 addl         %eax, %esi
	0x41, 0x0f, 0x92, 0xc3, //0x00003907 setb         %r11b
	0x01, 0xf6, //0x0000390b addl         %esi, %esi
	0x81, 0xf6, 0x55, 0x55, 0x55, 0x55, //0x0000390d xorl         $1431655765, %esi
	0x21, 0xfe, //0x00003913 andl         %edi, %esi
	0xf7, 0xd6, //0x00003915 notl         %esi
	0x21, 0xf1, //0x00003917 andl         %esi, %ecx
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00003919 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc4, 0x41, 0x7d, 0x6f, 0xfc, //0x0000391e vmovdqa      %ymm12, %ymm15
	0xc4, 0x41, 0x7d, 0x6f, 0xeb, //0x00003923 vmovdqa      %ymm11, %ymm13
	0xc5, 0xfe, 0x6f, 0x25, 0x70, 0xc9, 0xff, 0xff, //0x00003928 vmovdqu      $-13968(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x1d, 0x88, 0xc8, 0xff, 0xff, //0x00003930 vmovdqu      $-14200(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd9, //0x00003938 vmovdqa      %ymm1, %ymm11
	0xc5, 0x7d, 0x6f, 0xe2, //0x0000393c vmovdqa      %ymm2, %ymm12
	0x48, 0x85, 0xc9, //0x00003940 testq        %rcx, %rcx
	0x0f, 0x85, 0xf0, 0xf0, 0xff, 0xff, //0x00003943 jne          LBB0_398
	//0x00003949 LBB0_651
	0xba, 0x40, 0x00, 0x00, 0x00, //0x00003949 movl         $64, %edx
	//0x0000394e LBB0_652
	0xc5, 0xbd, 0x64, 0xc8, //0x0000394e vpcmpgtb     %ymm0, %ymm8, %ymm1
	0xc4, 0xc1, 0x7d, 0x64, 0xc1, //0x00003952 vpcmpgtb     %ymm9, %ymm0, %ymm0
	0xc5, 0xf5, 0xdb, 0xc0, //0x00003957 vpand        %ymm0, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x0000395b vpmovmskb    %ymm0, %esi
	0x0f, 0xbc, 0xfe, //0x0000395f bsfl         %esi, %edi
	0x48, 0x85, 0xc9, //0x00003962 testq        %rcx, %rcx
	0x0f, 0x84, 0x2c, 0x00, 0x00, 0x00, //0x00003965 je           LBB0_655
	0x85, 0xf6, //0x0000396b testl        %esi, %esi
	0xb8, 0x40, 0x00, 0x00, 0x00, //0x0000396d movl         $64, %eax
	0x0f, 0x44, 0xf8, //0x00003972 cmovel       %eax, %edi
	0x48, 0x39, 0xfa, //0x00003975 cmpq         %rdi, %rdx
	0x48, 0x89, 0xd9, //0x00003978 movq         %rbx, %rcx
	0x0f, 0x87, 0x31, 0x04, 0x00, 0x00, //0x0000397b ja           LBB0_725
	0xc5, 0x7d, 0x7f, 0xea, //0x00003981 vmovdqa      %ymm13, %ymm2
	0x4c, 0x29, 0xf1, //0x00003985 subq         %r14, %rcx
	0x48, 0x01, 0xd1, //0x00003988 addq         %rdx, %rcx
	0x48, 0x83, 0xc1, 0x01, //0x0000398b addq         $1, %rcx
	0x4d, 0x89, 0xd3, //0x0000398f movq         %r10, %r11
	0xe9, 0x57, 0xeb, 0xff, 0xff, //0x00003992 jmp          LBB0_461
	//0x00003997 LBB0_655
	0x85, 0xf6, //0x00003997 testl        %esi, %esi
	0x0f, 0x85, 0x23, 0x04, 0x00, 0x00, //0x00003999 jne          LBB0_726
	0xc5, 0x7d, 0x7f, 0xe1, //0x0000399f vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd8, //0x000039a3 vmovdqa      %ymm11, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xdd, //0x000039a7 vmovdqa      %ymm13, %ymm11
	0xc4, 0x41, 0x7d, 0x6f, 0xe7, //0x000039ac vmovdqa      %ymm15, %ymm12
	0x48, 0x83, 0xc3, 0x20, //0x000039b1 addq         $32, %rbx
	0x49, 0x83, 0xc0, 0xe0, //0x000039b5 addq         $-32, %r8
	//0x000039b9 LBB0_657
	0x4d, 0x85, 0xdb, //0x000039b9 testq        %r11, %r11
	0x0f, 0x85, 0x12, 0x01, 0x00, 0x00, //0x000039bc jne          LBB0_673
	0x4c, 0x89, 0xd6, //0x000039c2 movq         %r10, %rsi
	0x4d, 0x85, 0xc0, //0x000039c5 testq        %r8, %r8
	0x0f, 0x84, 0x30, 0x03, 0x00, 0x00, //0x000039c8 je           LBB0_707
	//0x000039ce LBB0_659
	0x48, 0x89, 0xd9, //0x000039ce movq         %rbx, %rcx
	//0x000039d1 LBB0_660
	0x0f, 0xb6, 0x11, //0x000039d1 movzbl       (%rcx), %edx
	0x80, 0xfa, 0x22, //0x000039d4 cmpb         $34, %dl
	0x0f, 0x84, 0x5d, 0x00, 0x00, 0x00, //0x000039d7 je           LBB0_668
	0x80, 0xfa, 0x5c, //0x000039dd cmpb         $92, %dl
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x000039e0 je           LBB0_665
	0x80, 0xfa, 0x1f, //0x000039e6 cmpb         $31, %dl
	0x0f, 0x86, 0xe1, 0x03, 0x00, 0x00, //0x000039e9 jbe          LBB0_727
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x000039ef movq         $-1, %rdx
	0xbf, 0x01, 0x00, 0x00, 0x00, //0x000039f6 movl         $1, %edi
	//0x000039fb LBB0_664
	0x48, 0x01, 0xf9, //0x000039fb addq         %rdi, %rcx
	0x49, 0x01, 0xd0, //0x000039fe addq         %rdx, %r8
	0x0f, 0x85, 0xca, 0xff, 0xff, 0xff, //0x00003a01 jne          LBB0_660
	0xe9, 0xf2, 0x02, 0x00, 0x00, //0x00003a07 jmp          LBB0_707
	//0x00003a0c LBB0_665
	0x49, 0x83, 0xf8, 0x01, //0x00003a0c cmpq         $1, %r8
	0x0f, 0x84, 0xe8, 0x02, 0x00, 0x00, //0x00003a10 je           LBB0_707
	0x48, 0xc7, 0xc2, 0xfe, 0xff, 0xff, 0xff, //0x00003a16 movq         $-2, %rdx
	0xbf, 0x02, 0x00, 0x00, 0x00, //0x00003a1d movl         $2, %edi
	0x48, 0x83, 0xfe, 0xff, //0x00003a22 cmpq         $-1, %rsi
	0x0f, 0x85, 0xcf, 0xff, 0xff, 0xff, //0x00003a26 jne          LBB0_664
	0x48, 0x89, 0xce, //0x00003a2c movq         %rcx, %rsi
	0x4c, 0x29, 0xf6, //0x00003a2f subq         %r14, %rsi
	0x49, 0x89, 0xf2, //0x00003a32 movq         %rsi, %r10
	0xe9, 0xc1, 0xff, 0xff, 0xff, //0x00003a35 jmp          LBB0_664
	//0x00003a3a LBB0_668
	0x4c, 0x29, 0xf1, //0x00003a3a subq         %r14, %rcx
	0x48, 0x83, 0xc1, 0x01, //0x00003a3d addq         $1, %rcx
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00003a41 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0x7d, 0x7f, 0xda, //0x00003a46 vmovdqa      %ymm11, %ymm2
	0xc4, 0x41, 0x7d, 0x6f, 0xfc, //0x00003a4a vmovdqa      %ymm12, %ymm15
	0xc5, 0xfe, 0x6f, 0x25, 0x49, 0xc8, 0xff, 0xff, //0x00003a4f vmovdqu      $-14263(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x1d, 0x61, 0xc7, 0xff, 0xff, //0x00003a57 vmovdqu      $-14495(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd8, //0x00003a5f vmovdqa      %ymm0, %ymm11
	0xc5, 0x7d, 0x6f, 0xe1, //0x00003a63 vmovdqa      %ymm1, %ymm12
	0x4d, 0x89, 0xd3, //0x00003a67 movq         %r10, %r11
	0xe9, 0x7f, 0xea, 0xff, 0xff, //0x00003a6a jmp          LBB0_461
	//0x00003a6f LBB0_669
	0x4d, 0x85, 0xc0, //0x00003a6f testq        %r8, %r8
	0x0f, 0x84, 0x86, 0x02, 0x00, 0x00, //0x00003a72 je           LBB0_707
	0x4c, 0x89, 0xf6, //0x00003a78 movq         %r14, %rsi
	0x48, 0xf7, 0xd6, //0x00003a7b notq         %rsi
	0x48, 0x8d, 0x04, 0x37, //0x00003a7e leaq         (%rdi,%rsi), %rax
	0x4c, 0x89, 0xd9, //0x00003a82 movq         %r11, %rcx
	0x49, 0x83, 0xfb, 0xff, //0x00003a85 cmpq         $-1, %r11
	0x4d, 0x89, 0xda, //0x00003a89 movq         %r11, %r10
	0x48, 0x0f, 0x44, 0xc8, //0x00003a8c cmoveq       %rax, %rcx
	0x4c, 0x0f, 0x44, 0xd0, //0x00003a90 cmoveq       %rax, %r10
	0x48, 0x83, 0xc7, 0x01, //0x00003a94 addq         $1, %rdi
	0x49, 0x83, 0xc0, 0xff, //0x00003a98 addq         $-1, %r8
	0x49, 0x89, 0xcb, //0x00003a9c movq         %rcx, %r11
	0x4d, 0x85, 0xc0, //0x00003a9f testq        %r8, %r8
	0x0f, 0x85, 0x6b, 0xfd, 0xff, 0xff, //0x00003aa2 jne          LBB0_637
	//0x00003aa8 LBB0_671
	0x48, 0x89, 0xf9, //0x00003aa8 movq         %rdi, %rcx
	//0x00003aab LBB0_672
	0x4c, 0x29, 0xf1, //0x00003aab subq         %r14, %rcx
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00003aae vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0x7d, 0x7f, 0xda, //0x00003ab3 vmovdqa      %ymm11, %ymm2
	0xc5, 0xfe, 0x6f, 0x25, 0xe1, 0xc7, 0xff, 0xff, //0x00003ab7 vmovdqu      $-14367(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x1d, 0xf9, 0xc6, 0xff, 0xff, //0x00003abf vmovdqu      $-14599(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd8, //0x00003ac7 vmovdqa      %ymm0, %ymm11
	0xc5, 0x7d, 0x6f, 0xe1, //0x00003acb vmovdqa      %ymm1, %ymm12
	0xe9, 0x1a, 0xea, 0xff, 0xff, //0x00003acf jmp          LBB0_461
	//0x00003ad4 LBB0_673
	0x4d, 0x85, 0xc0, //0x00003ad4 testq        %r8, %r8
	0x0f, 0x84, 0x21, 0x02, 0x00, 0x00, //0x00003ad7 je           LBB0_707
	0x4c, 0x89, 0xf6, //0x00003add movq         %r14, %rsi
	0x48, 0xf7, 0xd6, //0x00003ae0 notq         %rsi
	0x48, 0x01, 0xde, //0x00003ae3 addq         %rbx, %rsi
	0x49, 0x83, 0xfa, 0xff, //0x00003ae6 cmpq         $-1, %r10
	0x4c, 0x89, 0xd0, //0x00003aea movq         %r10, %rax
	0x48, 0x0f, 0x44, 0xc6, //0x00003aed cmoveq       %rsi, %rax
	0x49, 0x0f, 0x45, 0xf2, //0x00003af1 cmovneq      %r10, %rsi
	0x48, 0x83, 0xc3, 0x01, //0x00003af5 addq         $1, %rbx
	0x49, 0x83, 0xc0, 0xff, //0x00003af9 addq         $-1, %r8
	0x49, 0x89, 0xc2, //0x00003afd movq         %rax, %r10
	0x4d, 0x85, 0xc0, //0x00003b00 testq        %r8, %r8
	0x0f, 0x85, 0xc5, 0xfe, 0xff, 0xff, //0x00003b03 jne          LBB0_659
	0xe9, 0xf0, 0x01, 0x00, 0x00, //0x00003b09 jmp          LBB0_707
	//0x00003b0e LBB0_675
	0x4d, 0x89, 0x65, 0x00, //0x00003b0e movq         %r12, (%r13)
	//0x00003b12 LBB0_677
	0x48, 0xc7, 0x04, 0x24, 0xff, 0xff, 0xff, 0xff, //0x00003b12 movq         $-1, (%rsp)
	0xe9, 0x76, 0x00, 0x00, 0x00, //0x00003b1a jmp          LBB0_711
	//0x00003b1f LBB0_705
	0x48, 0xc7, 0x04, 0x24, 0xf9, 0xff, 0xff, 0xff, //0x00003b1f movq         $-7, (%rsp)
	0xe9, 0x69, 0x00, 0x00, 0x00, //0x00003b27 jmp          LBB0_711
	//0x00003b2c LBB0_679
	0x49, 0x83, 0xc0, 0xff, //0x00003b2c addq         $-1, %r8
	0x4c, 0x89, 0x04, 0x24, //0x00003b30 movq         %r8, (%rsp)
	0xe9, 0x5c, 0x00, 0x00, 0x00, //0x00003b34 jmp          LBB0_711
	//0x00003b39 LBB0_463
	0x49, 0x83, 0xc1, 0xff, //0x00003b39 addq         $-1, %r9
	0x4c, 0x89, 0x0c, 0x24, //0x00003b3d movq         %r9, (%rsp)
	0xe9, 0x4f, 0x00, 0x00, 0x00, //0x00003b41 jmp          LBB0_711
	//0x00003b46 LBB0_680
	0x49, 0x83, 0xfa, 0xff, //0x00003b46 cmpq         $-1, %r10
	0x0f, 0x85, 0x7c, 0x00, 0x00, 0x00, //0x00003b4a jne          LBB0_703
	//0x00003b50 LBB0_681
	0x49, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00003b50 movq         $-1, %r10
	0x4d, 0x89, 0xc7, //0x00003b57 movq         %r8, %r15
	0xe9, 0x6d, 0x00, 0x00, 0x00, //0x00003b5a jmp          LBB0_703
	//0x00003b5f LBB0_682
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00003b5f movq         $-1, %rcx
	0xe9, 0x10, 0x00, 0x00, 0x00, //0x00003b66 jmp          LBB0_684
	//0x00003b6b LBB0_178
	0x48, 0x83, 0xc3, 0xff, //0x00003b6b addq         $-1, %rbx
	0x48, 0x89, 0x1c, 0x24, //0x00003b6f movq         %rbx, (%rsp)
	0xe9, 0x1d, 0x00, 0x00, 0x00, //0x00003b73 jmp          LBB0_711
	//0x00003b78 LBB0_683
	0x4c, 0x89, 0xe9, //0x00003b78 movq         %r13, %rcx
	//0x00003b7b LBB0_684
	0x48, 0x8b, 0x54, 0x24, 0x30, //0x00003b7b movq         $48(%rsp), %rdx
	0x48, 0x8b, 0x02, //0x00003b80 movq         (%rdx), %rax
	0x48, 0x29, 0xc8, //0x00003b83 subq         %rcx, %rax
	0x48, 0x83, 0xc0, 0xfe, //0x00003b86 addq         $-2, %rax
	0x48, 0x89, 0x02, //0x00003b8a movq         %rax, (%rdx)
	//0x00003b8d LBB0_710
	0x48, 0xc7, 0x04, 0x24, 0xfe, 0xff, 0xff, 0xff, //0x00003b8d movq         $-2, (%rsp)
	//0x00003b95 LBB0_711
	0x48, 0x8b, 0x04, 0x24, //0x00003b95 movq         (%rsp), %rax
	0x48, 0x8d, 0x65, 0xd8, //0x00003b99 leaq         $-40(%rbp), %rsp
	0x5b, //0x00003b9d popq         %rbx
	0x41, 0x5c, //0x00003b9e popq         %r12
	0x41, 0x5d, //0x00003ba0 popq         %r13
	0x41, 0x5e, //0x00003ba2 popq         %r14
	0x41, 0x5f, //0x00003ba4 popq         %r15
	0x5d, //0x00003ba6 popq         %rbp
	0xc5, 0xf8, 0x77, //0x00003ba7 vzeroupper   
	0xc3, //0x00003baa retq         
	//0x00003bab LBB0_686
	0x49, 0x89, 0x55, 0x00, //0x00003bab movq         %rdx, (%r13)
	0xe9, 0xe1, 0xff, 0xff, 0xff, //0x00003baf jmp          LBB0_711
	//0x00003bb4 LBB0_699
	0x49, 0x83, 0xff, 0xff, //0x00003bb4 cmpq         $-1, %r15
	0x0f, 0x85, 0x07, 0x00, 0x00, 0x00, //0x00003bb8 jne          LBB0_702
	0x4c, 0x0f, 0xbc, 0xfe, //0x00003bbe bsfq         %rsi, %r15
	//0x00003bc2 LBB0_701
	0x4d, 0x01, 0xd7, //0x00003bc2 addq         %r10, %r15
	//0x00003bc5 LBB0_702
	0x49, 0xc7, 0xc2, 0xfe, 0xff, 0xff, 0xff, //0x00003bc5 movq         $-2, %r10
	//0x00003bcc LBB0_703
	0x4c, 0x89, 0x14, 0x24, //0x00003bcc movq         %r10, (%rsp)
	0x4d, 0x89, 0x7d, 0x00, //0x00003bd0 movq         %r15, (%r13)
	0xe9, 0xbc, 0xff, 0xff, 0xff, //0x00003bd4 jmp          LBB0_711
	//0x00003bd9 LBB0_687
	0x80, 0xfa, 0x61, //0x00003bd9 cmpb         $97, %dl
	0x0f, 0x85, 0xab, 0xff, 0xff, 0xff, //0x00003bdc jne          LBB0_710
	0x48, 0x8d, 0x41, 0x01, //0x00003be2 leaq         $1(%rcx), %rax
	0x49, 0x89, 0x45, 0x00, //0x00003be6 movq         %rax, (%r13)
	0x41, 0x80, 0x7c, 0x0e, 0x01, 0x6c, //0x00003bea cmpb         $108, $1(%r14,%rcx)
	0x0f, 0x85, 0x97, 0xff, 0xff, 0xff, //0x00003bf0 jne          LBB0_710
	0x48, 0x8d, 0x41, 0x02, //0x00003bf6 leaq         $2(%rcx), %rax
	0x49, 0x89, 0x45, 0x00, //0x00003bfa movq         %rax, (%r13)
	0x41, 0x80, 0x7c, 0x0e, 0x02, 0x73, //0x00003bfe cmpb         $115, $2(%r14,%rcx)
	0x0f, 0x85, 0x83, 0xff, 0xff, 0xff, //0x00003c04 jne          LBB0_710
	0x48, 0xc7, 0x04, 0x24, 0xfe, 0xff, 0xff, 0xff, //0x00003c0a movq         $-2, (%rsp)
	0x48, 0x8d, 0x41, 0x03, //0x00003c12 leaq         $3(%rcx), %rax
	0x49, 0x89, 0x45, 0x00, //0x00003c16 movq         %rax, (%r13)
	0x41, 0x80, 0x7c, 0x0e, 0x03, 0x65, //0x00003c1a cmpb         $101, $3(%r14,%rcx)
	0x0f, 0x85, 0x6f, 0xff, 0xff, 0xff, //0x00003c20 jne          LBB0_711
	0x48, 0x83, 0xc1, 0x04, //0x00003c26 addq         $4, %rcx
	0x49, 0x89, 0x4d, 0x00, //0x00003c2a movq         %rcx, (%r13)
	0xe9, 0x62, 0xff, 0xff, 0xff, //0x00003c2e jmp          LBB0_711
	//0x00003c33 LBB0_267
	0x49, 0x89, 0x45, 0x00, //0x00003c33 movq         %rax, (%r13)
	0x41, 0x80, 0x3c, 0x06, 0x6e, //0x00003c37 cmpb         $110, (%r14,%rax)
	0x0f, 0x85, 0x4b, 0xff, 0xff, 0xff, //0x00003c3c jne          LBB0_710
	0x49, 0x89, 0x4d, 0x00, //0x00003c42 movq         %rcx, (%r13)
	0x41, 0x80, 0x3c, 0x0e, 0x75, //0x00003c46 cmpb         $117, (%r14,%rcx)
	0x0f, 0x85, 0x3c, 0xff, 0xff, 0xff, //0x00003c4b jne          LBB0_710
	0x48, 0x8d, 0x41, 0x01, //0x00003c51 leaq         $1(%rcx), %rax
	0x49, 0x89, 0x45, 0x00, //0x00003c55 movq         %rax, (%r13)
	0x41, 0x80, 0x7c, 0x0e, 0x01, 0x6c, //0x00003c59 cmpb         $108, $1(%r14,%rcx)
	0x0f, 0x85, 0x28, 0xff, 0xff, 0xff, //0x00003c5f jne          LBB0_710
	0x48, 0x8d, 0x41, 0x02, //0x00003c65 leaq         $2(%rcx), %rax
	0x49, 0x89, 0x45, 0x00, //0x00003c69 movq         %rax, (%r13)
	0x41, 0x80, 0x7c, 0x0e, 0x02, 0x6c, //0x00003c6d cmpb         $108, $2(%r14,%rcx)
	0x0f, 0x84, 0x4b, 0x00, 0x00, 0x00, //0x00003c73 je           LBB0_696
	0xe9, 0x0f, 0xff, 0xff, 0xff, //0x00003c79 jmp          LBB0_710
	//0x00003c7e LBB0_692
	0x49, 0x89, 0x45, 0x00, //0x00003c7e movq         %rax, (%r13)
	0x41, 0x80, 0x3c, 0x06, 0x74, //0x00003c82 cmpb         $116, (%r14,%rax)
	0x0f, 0x85, 0x00, 0xff, 0xff, 0xff, //0x00003c87 jne          LBB0_710
	0x49, 0x89, 0x4d, 0x00, //0x00003c8d movq         %rcx, (%r13)
	0x41, 0x80, 0x3c, 0x0e, 0x72, //0x00003c91 cmpb         $114, (%r14,%rcx)
	0x0f, 0x85, 0xf1, 0xfe, 0xff, 0xff, //0x00003c96 jne          LBB0_710
	0x48, 0x8d, 0x41, 0x01, //0x00003c9c leaq         $1(%rcx), %rax
	0x49, 0x89, 0x45, 0x00, //0x00003ca0 movq         %rax, (%r13)
	0x41, 0x80, 0x7c, 0x0e, 0x01, 0x75, //0x00003ca4 cmpb         $117, $1(%r14,%rcx)
	0x0f, 0x85, 0xdd, 0xfe, 0xff, 0xff, //0x00003caa jne          LBB0_710
	0x48, 0x8d, 0x41, 0x02, //0x00003cb0 leaq         $2(%rcx), %rax
	0x49, 0x89, 0x45, 0x00, //0x00003cb4 movq         %rax, (%r13)
	0x41, 0x80, 0x7c, 0x0e, 0x02, 0x65, //0x00003cb8 cmpb         $101, $2(%r14,%rcx)
	0x0f, 0x85, 0xc9, 0xfe, 0xff, 0xff, //0x00003cbe jne          LBB0_710
	//0x00003cc4 LBB0_696
	0x48, 0x83, 0xc1, 0x03, //0x00003cc4 addq         $3, %rcx
	0x49, 0x89, 0x4d, 0x00, //0x00003cc8 movq         %rcx, (%r13)
	0xe9, 0xbc, 0xfe, 0xff, 0xff, //0x00003ccc jmp          LBB0_710
	//0x00003cd1 LBB0_698
	0x48, 0x83, 0x04, 0x24, 0xff, //0x00003cd1 addq         $-1, (%rsp)
	0xe9, 0xba, 0xfe, 0xff, 0xff, //0x00003cd6 jmp          LBB0_711
	//0x00003cdb LBB0_264
	0x48, 0x83, 0xc1, 0xff, //0x00003cdb addq         $-1, %rcx
	0x48, 0x89, 0x0c, 0x24, //0x00003cdf movq         %rcx, (%rsp)
	0xe9, 0xad, 0xfe, 0xff, 0xff, //0x00003ce3 jmp          LBB0_711
	//0x00003ce8 LBB0_704
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00003ce8 movq         $-1, %rcx
	0xe9, 0x1c, 0x00, 0x00, 0x00, //0x00003cef jmp          LBB0_709
	//0x00003cf4 LBB0_706
	0x48, 0x83, 0xf9, 0xff, //0x00003cf4 cmpq         $-1, %rcx
	0x0f, 0x85, 0x58, 0x00, 0x00, 0x00, //0x00003cf8 jne          LBB0_716
	//0x00003cfe LBB0_707
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00003cfe movq         $-1, %rcx
	0x4d, 0x89, 0xfb, //0x00003d05 movq         %r15, %r11
	0xe9, 0x49, 0x00, 0x00, 0x00, //0x00003d08 jmp          LBB0_716
	//0x00003d0d LBB0_708
	0x4c, 0x89, 0xc9, //0x00003d0d movq         %r9, %rcx
	//0x00003d10 LBB0_709
	0x48, 0xf7, 0xd1, //0x00003d10 notq         %rcx
	0x49, 0x01, 0x4d, 0x00, //0x00003d13 addq         %rcx, (%r13)
	0xe9, 0x71, 0xfe, 0xff, 0xff, //0x00003d17 jmp          LBB0_710
	//0x00003d1c LBB0_717
	0x4d, 0x89, 0xc8, //0x00003d1c movq         %r9, %r8
	0xe9, 0x2c, 0xfe, 0xff, 0xff, //0x00003d1f jmp          LBB0_681
	//0x00003d24 LBB0_712
	0x49, 0x83, 0xfa, 0xff, //0x00003d24 cmpq         $-1, %r10
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x00003d28 je           LBB0_714
	0x4d, 0x89, 0xd3, //0x00003d2e movq         %r10, %r11
	0xe9, 0x19, 0x00, 0x00, 0x00, //0x00003d31 jmp          LBB0_715
	//0x00003d36 LBB0_209
	0x4c, 0x01, 0xd2, //0x00003d36 addq         %r10, %rdx
	0x49, 0xc7, 0xc2, 0xfe, 0xff, 0xff, 0xff, //0x00003d39 movq         $-2, %r10
	0x49, 0x89, 0xd7, //0x00003d40 movq         %rdx, %r15
	0xe9, 0x84, 0xfe, 0xff, 0xff, //0x00003d43 jmp          LBB0_703
	//0x00003d48 LBB0_714
	0x4c, 0x0f, 0xbc, 0xde, //0x00003d48 bsfq         %rsi, %r11
	0x49, 0x01, 0xcb, //0x00003d4c addq         %rcx, %r11
	//0x00003d4f LBB0_715
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00003d4f movq         $-2, %rcx
	//0x00003d56 LBB0_716
	0x48, 0x89, 0x0c, 0x24, //0x00003d56 movq         %rcx, (%rsp)
	0x4d, 0x89, 0x5d, 0x00, //0x00003d5a movq         %r11, (%r13)
	0xe9, 0x32, 0xfe, 0xff, 0xff, //0x00003d5e jmp          LBB0_711
	//0x00003d63 LBB0_718
	0x4d, 0x89, 0xcf, //0x00003d63 movq         %r9, %r15
	0xe9, 0x93, 0xff, 0xff, 0xff, //0x00003d66 jmp          LBB0_707
	//0x00003d6b LBB0_719
	0x48, 0x01, 0xca, //0x00003d6b addq         %rcx, %rdx
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00003d6e movq         $-2, %rcx
	0x49, 0x89, 0xd3, //0x00003d75 movq         %rdx, %r11
	0xe9, 0xd9, 0xff, 0xff, 0xff, //0x00003d78 jmp          LBB0_716
	//0x00003d7d LBB0_720
	0x48, 0x8b, 0x44, 0x24, 0x10, //0x00003d7d movq         $16(%rsp), %rax
	0x48, 0x8b, 0x40, 0x08, //0x00003d82 movq         $8(%rax), %rax
	0x49, 0x89, 0x45, 0x00, //0x00003d86 movq         %rax, (%r13)
	0xe9, 0x06, 0xfe, 0xff, 0xff, //0x00003d8a jmp          LBB0_711
	//0x00003d8f LBB0_721
	0x89, 0xf8, //0x00003d8f movl         %edi, %eax
	0x4d, 0x29, 0xf2, //0x00003d91 subq         %r14, %r10
	0x49, 0x01, 0xc2, //0x00003d94 addq         %rax, %r10
	0xe9, 0x0e, 0x00, 0x00, 0x00, //0x00003d97 jmp          LBB0_724
	//0x00003d9c LBB0_722
	0x4d, 0x29, 0xf2, //0x00003d9c subq         %r14, %r10
	0x41, 0x89, 0xff, //0x00003d9f movl         %edi, %r15d
	0xe9, 0x1b, 0xfe, 0xff, 0xff, //0x00003da2 jmp          LBB0_701
	//0x00003da7 LBB0_723
	0x4d, 0x29, 0xf2, //0x00003da7 subq         %r14, %r10
	//0x00003daa LBB0_724
	0x4d, 0x89, 0xd7, //0x00003daa movq         %r10, %r15
	0xe9, 0x13, 0xfe, 0xff, 0xff, //0x00003dad jmp          LBB0_702
	//0x00003db2 LBB0_725
	0x89, 0xf8, //0x00003db2 movl         %edi, %eax
	0x4c, 0x29, 0xf1, //0x00003db4 subq         %r14, %rcx
	0x48, 0x01, 0xc1, //0x00003db7 addq         %rax, %rcx
	0x49, 0x89, 0xcb, //0x00003dba movq         %rcx, %r11
	0xe9, 0x8d, 0xff, 0xff, 0xff, //0x00003dbd jmp          LBB0_715
	//0x00003dc2 LBB0_726
	0x4c, 0x29, 0xf3, //0x00003dc2 subq         %r14, %rbx
	0x41, 0x89, 0xfb, //0x00003dc5 movl         %edi, %r11d
	0x49, 0x01, 0xdb, //0x00003dc8 addq         %rbx, %r11
	0xe9, 0x7f, 0xff, 0xff, 0xff, //0x00003dcb jmp          LBB0_715
	//0x00003dd0 LBB0_727
	0x48, 0x89, 0xc8, //0x00003dd0 movq         %rcx, %rax
	0x4c, 0x29, 0xf0, //0x00003dd3 subq         %r14, %rax
	0x49, 0x89, 0xc3, //0x00003dd6 movq         %rax, %r11
	0xe9, 0x71, 0xff, 0xff, 0xff, //0x00003dd9 jmp          LBB0_715
	0x90, 0x90, //0x00003dde .p2align 2, 0x90
	// // .set L0_0_set_38, LBB0_38-LJTI0_0
	// // .set L0_0_set_42, LBB0_42-LJTI0_0
	// // .set L0_0_set_44, LBB0_44-LJTI0_0
	// // .set L0_0_set_63, LBB0_63-LJTI0_0
	// // .set L0_0_set_65, LBB0_65-LJTI0_0
	// // .set L0_0_set_68, LBB0_68-LJTI0_0
	//0x00003de0 LJTI0_0
	0xf7, 0xc7, 0xff, 0xff, //0x00003de0 .long L0_0_set_38
	0x27, 0xc8, 0xff, 0xff, //0x00003de4 .long L0_0_set_42
	0x52, 0xc8, 0xff, 0xff, //0x00003de8 .long L0_0_set_44
	0x9b, 0xc9, 0xff, 0xff, //0x00003dec .long L0_0_set_63
	0xb0, 0xc9, 0xff, 0xff, //0x00003df0 .long L0_0_set_65
	0x1a, 0xce, 0xff, 0xff, //0x00003df4 .long L0_0_set_68
	// // .set L0_1_set_711, LBB0_711-LJTI0_1
	// // .set L0_1_set_710, LBB0_710-LJTI0_1
	// // .set L0_1_set_234, LBB0_234-LJTI0_1
	// // .set L0_1_set_253, LBB0_253-LJTI0_1
	// // .set L0_1_set_70, LBB0_70-LJTI0_1
	// // .set L0_1_set_258, LBB0_258-LJTI0_1
	// // .set L0_1_set_261, LBB0_261-LJTI0_1
	// // .set L0_1_set_265, LBB0_265-LJTI0_1
	// // .set L0_1_set_271, LBB0_271-LJTI0_1
	// // .set L0_1_set_274, LBB0_274-LJTI0_1
	//0x00003df8 LJTI0_1
	0x9d, 0xfd, 0xff, 0xff, //0x00003df8 .long L0_1_set_711
	0x95, 0xfd, 0xff, 0xff, //0x00003dfc .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003e00 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003e04 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003e08 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003e0c .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003e10 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003e14 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003e18 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003e1c .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003e20 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003e24 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003e28 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003e2c .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003e30 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003e34 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003e38 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003e3c .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003e40 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003e44 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003e48 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003e4c .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003e50 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003e54 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003e58 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003e5c .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003e60 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003e64 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003e68 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003e6c .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003e70 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003e74 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003e78 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003e7c .long L0_1_set_710
	0x52, 0xd5, 0xff, 0xff, //0x00003e80 .long L0_1_set_234
	0x95, 0xfd, 0xff, 0xff, //0x00003e84 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003e88 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003e8c .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003e90 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003e94 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003e98 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003e9c .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003ea0 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003ea4 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003ea8 .long L0_1_set_710
	0x97, 0xd6, 0xff, 0xff, //0x00003eac .long L0_1_set_253
	0x95, 0xfd, 0xff, 0xff, //0x00003eb0 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003eb4 .long L0_1_set_710
	0xcb, 0xc9, 0xff, 0xff, //0x00003eb8 .long L0_1_set_70
	0xcb, 0xc9, 0xff, 0xff, //0x00003ebc .long L0_1_set_70
	0xcb, 0xc9, 0xff, 0xff, //0x00003ec0 .long L0_1_set_70
	0xcb, 0xc9, 0xff, 0xff, //0x00003ec4 .long L0_1_set_70
	0xcb, 0xc9, 0xff, 0xff, //0x00003ec8 .long L0_1_set_70
	0xcb, 0xc9, 0xff, 0xff, //0x00003ecc .long L0_1_set_70
	0xcb, 0xc9, 0xff, 0xff, //0x00003ed0 .long L0_1_set_70
	0xcb, 0xc9, 0xff, 0xff, //0x00003ed4 .long L0_1_set_70
	0xcb, 0xc9, 0xff, 0xff, //0x00003ed8 .long L0_1_set_70
	0xcb, 0xc9, 0xff, 0xff, //0x00003edc .long L0_1_set_70
	0x95, 0xfd, 0xff, 0xff, //0x00003ee0 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003ee4 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003ee8 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003eec .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003ef0 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003ef4 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003ef8 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003efc .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003f00 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003f04 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003f08 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003f0c .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003f10 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003f14 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003f18 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003f1c .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003f20 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003f24 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003f28 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003f2c .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003f30 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003f34 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003f38 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003f3c .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003f40 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003f44 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003f48 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003f4c .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003f50 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003f54 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003f58 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003f5c .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003f60 .long L0_1_set_710
	0xe1, 0xd6, 0xff, 0xff, //0x00003f64 .long L0_1_set_258
	0x95, 0xfd, 0xff, 0xff, //0x00003f68 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003f6c .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003f70 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003f74 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003f78 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003f7c .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003f80 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003f84 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003f88 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003f8c .long L0_1_set_710
	0x10, 0xd7, 0xff, 0xff, //0x00003f90 .long L0_1_set_261
	0x95, 0xfd, 0xff, 0xff, //0x00003f94 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003f98 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003f9c .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003fa0 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003fa4 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003fa8 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003fac .long L0_1_set_710
	0x4b, 0xd7, 0xff, 0xff, //0x00003fb0 .long L0_1_set_265
	0x95, 0xfd, 0xff, 0xff, //0x00003fb4 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003fb8 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003fbc .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003fc0 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003fc4 .long L0_1_set_710
	0x78, 0xd7, 0xff, 0xff, //0x00003fc8 .long L0_1_set_271
	0x95, 0xfd, 0xff, 0xff, //0x00003fcc .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003fd0 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003fd4 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003fd8 .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003fdc .long L0_1_set_710
	0x95, 0xfd, 0xff, 0xff, //0x00003fe0 .long L0_1_set_710
	0xba, 0xd7, 0xff, 0xff, //0x00003fe4 .long L0_1_set_274
	// // .set L0_2_set_445, LBB0_445-LJTI0_2
	// // .set L0_2_set_467, LBB0_467-LJTI0_2
	// // .set L0_2_set_451, LBB0_451-LJTI0_2
	// // .set L0_2_set_454, LBB0_454-LJTI0_2
	//0x00003fe8 LJTI0_2
	0x2a, 0xe4, 0xff, 0xff, //0x00003fe8 .long L0_2_set_445
	0x9f, 0xe5, 0xff, 0xff, //0x00003fec .long L0_2_set_467
	0x2a, 0xe4, 0xff, 0xff, //0x00003ff0 .long L0_2_set_445
	0x72, 0xe4, 0xff, 0xff, //0x00003ff4 .long L0_2_set_451
	0x9f, 0xe5, 0xff, 0xff, //0x00003ff8 .long L0_2_set_467
	0x9f, 0xe5, 0xff, 0xff, //0x00003ffc .long L0_2_set_467
	0x9f, 0xe5, 0xff, 0xff, //0x00004000 .long L0_2_set_467
	0x9f, 0xe5, 0xff, 0xff, //0x00004004 .long L0_2_set_467
	0x9f, 0xe5, 0xff, 0xff, //0x00004008 .long L0_2_set_467
	0x9f, 0xe5, 0xff, 0xff, //0x0000400c .long L0_2_set_467
	0x9f, 0xe5, 0xff, 0xff, //0x00004010 .long L0_2_set_467
	0x9f, 0xe5, 0xff, 0xff, //0x00004014 .long L0_2_set_467
	0x9f, 0xe5, 0xff, 0xff, //0x00004018 .long L0_2_set_467
	0x9f, 0xe5, 0xff, 0xff, //0x0000401c .long L0_2_set_467
	0x9f, 0xe5, 0xff, 0xff, //0x00004020 .long L0_2_set_467
	0x9f, 0xe5, 0xff, 0xff, //0x00004024 .long L0_2_set_467
	0x9f, 0xe5, 0xff, 0xff, //0x00004028 .long L0_2_set_467
	0x9f, 0xe5, 0xff, 0xff, //0x0000402c .long L0_2_set_467
	0x9f, 0xe5, 0xff, 0xff, //0x00004030 .long L0_2_set_467
	0x9f, 0xe5, 0xff, 0xff, //0x00004034 .long L0_2_set_467
	0x9f, 0xe5, 0xff, 0xff, //0x00004038 .long L0_2_set_467
	0x9f, 0xe5, 0xff, 0xff, //0x0000403c .long L0_2_set_467
	0x9f, 0xe5, 0xff, 0xff, //0x00004040 .long L0_2_set_467
	0x9f, 0xe5, 0xff, 0xff, //0x00004044 .long L0_2_set_467
	0x9f, 0xe5, 0xff, 0xff, //0x00004048 .long L0_2_set_467
	0x9f, 0xe5, 0xff, 0xff, //0x0000404c .long L0_2_set_467
	0x95, 0xe4, 0xff, 0xff, //0x00004050 .long L0_2_set_454
	// // .set L0_3_set_121, LBB0_121-LJTI0_3
	// // .set L0_3_set_278, LBB0_278-LJTI0_3
	// // .set L0_3_set_115, LBB0_115-LJTI0_3
	// // .set L0_3_set_124, LBB0_124-LJTI0_3
	//0x00004054 LJTI0_3
	0x77, 0xcb, 0xff, 0xff, //0x00004054 .long L0_3_set_121
	0xa4, 0xd5, 0xff, 0xff, //0x00004058 .long L0_3_set_278
	0x77, 0xcb, 0xff, 0xff, //0x0000405c .long L0_3_set_121
	0x21, 0xcb, 0xff, 0xff, //0x00004060 .long L0_3_set_115
	0xa4, 0xd5, 0xff, 0xff, //0x00004064 .long L0_3_set_278
	0xa4, 0xd5, 0xff, 0xff, //0x00004068 .long L0_3_set_278
	0xa4, 0xd5, 0xff, 0xff, //0x0000406c .long L0_3_set_278
	0xa4, 0xd5, 0xff, 0xff, //0x00004070 .long L0_3_set_278
	0xa4, 0xd5, 0xff, 0xff, //0x00004074 .long L0_3_set_278
	0xa4, 0xd5, 0xff, 0xff, //0x00004078 .long L0_3_set_278
	0xa4, 0xd5, 0xff, 0xff, //0x0000407c .long L0_3_set_278
	0xa4, 0xd5, 0xff, 0xff, //0x00004080 .long L0_3_set_278
	0xa4, 0xd5, 0xff, 0xff, //0x00004084 .long L0_3_set_278
	0xa4, 0xd5, 0xff, 0xff, //0x00004088 .long L0_3_set_278
	0xa4, 0xd5, 0xff, 0xff, //0x0000408c .long L0_3_set_278
	0xa4, 0xd5, 0xff, 0xff, //0x00004090 .long L0_3_set_278
	0xa4, 0xd5, 0xff, 0xff, //0x00004094 .long L0_3_set_278
	0xa4, 0xd5, 0xff, 0xff, //0x00004098 .long L0_3_set_278
	0xa4, 0xd5, 0xff, 0xff, //0x0000409c .long L0_3_set_278
	0xa4, 0xd5, 0xff, 0xff, //0x000040a0 .long L0_3_set_278
	0xa4, 0xd5, 0xff, 0xff, //0x000040a4 .long L0_3_set_278
	0xa4, 0xd5, 0xff, 0xff, //0x000040a8 .long L0_3_set_278
	0xa4, 0xd5, 0xff, 0xff, //0x000040ac .long L0_3_set_278
	0xa4, 0xd5, 0xff, 0xff, //0x000040b0 .long L0_3_set_278
	0xa4, 0xd5, 0xff, 0xff, //0x000040b4 .long L0_3_set_278
	0xa4, 0xd5, 0xff, 0xff, //0x000040b8 .long L0_3_set_278
	0x93, 0xcb, 0xff, 0xff, //0x000040bc .long L0_3_set_124
	//0x000040c0 .p2align 2, 0x00
	//0x000040c0 _MASK_USE_NUMBER
	0x02, 0x00, 0x00, 0x00, //0x000040c0 .long 2
}
 
