// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package avx2

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__parse_with_padding = 688
)

const (
    _stack__parse_with_padding = 200
)

const (
    _size__parse_with_padding = 48876
)

var (
    _pcsp__parse_with_padding = [][2]uint32{
        {0x1, 0},
        {0x6, 8},
        {0x8, 16},
        {0xa, 24},
        {0xc, 32},
        {0xd, 40},
        {0x14, 48},
        {0xbea, 200},
        {0xbeb, 48},
        {0xbed, 40},
        {0xbef, 32},
        {0xbf1, 24},
        {0xbf3, 16},
        {0xbf4, 8},
        {0xbf8, 0},
        {0xbeec, 200},
    }
)

var _cfunc_parse_with_padding = []loader.CFunc{
    {"_parse_with_padding_entry", 0,  _entry__parse_with_padding, 0, nil},
    {"_parse_with_padding", _entry__parse_with_padding, _size__parse_with_padding, _stack__parse_with_padding, _pcsp__parse_with_padding},
}
