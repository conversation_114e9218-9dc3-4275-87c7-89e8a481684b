// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package avx2

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__skip_object = 704
)

const (
    _stack__skip_object = 208
)

const (
    _size__skip_object = 15136
)

var (
    _pcsp__skip_object = [][2]uint32{
        {0x1, 0},
        {0x6, 8},
        {0x8, 16},
        {0xa, 24},
        {0xc, 32},
        {0xd, 40},
        {0x14, 48},
        {0x38dd, 208},
        {0x38de, 48},
        {0x38e0, 40},
        {0x38e2, 32},
        {0x38e4, 24},
        {0x38e6, 16},
        {0x38e7, 8},
        {0x38eb, 0},
        {0x3b20, 208},
    }
)

var _cfunc_skip_object = []loader.CFunc{
    {"_skip_object_entry", 0,  _entry__skip_object, 0, nil},
    {"_skip_object", _entry__skip_object, _size__skip_object, _stack__skip_object, _pcsp__skip_object},
}
