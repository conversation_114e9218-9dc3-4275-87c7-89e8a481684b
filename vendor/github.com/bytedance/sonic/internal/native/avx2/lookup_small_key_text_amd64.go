// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package avx2

var _text_lookup_small_key = []byte{
	// .p2align 5, 0x00
	// LCPI0_0
	0x40, 0x40, 0x40, 0x40, 0x40, 0x40, 0x40, 0x40, 0x40, 0x40, 0x40, 0x40, 0x40, 0x40, 0x40, 0x40, // QUAD $0x4040404040404040; QUAD $0x4040404040404040  // .space 16, '@@@@@@@@@@@@@@@@'
	0x40, 0x40, 0x40, 0x40, 0x40, 0x40, 0x40, 0x40, 0x40, 0x40, 0x40, 0x40, 0x40, 0x40, 0x40, 0x40, //0x00000010 QUAD $0x4040404040404040; QUAD $0x4040404040404040  // .space 16, '@@@@@@@@@@@@@@@@'
	//0x00000020 LCPI0_1
	0x5a, 0x5a, 0x5a, 0x5a, 0x5a, 0x5a, 0x5a, 0x5a, 0x5a, 0x5a, 0x5a, 0x5a, 0x5a, 0x5a, 0x5a, 0x5a, //0x00000020 QUAD $0x5a5a5a5a5a5a5a5a; QUAD $0x5a5a5a5a5a5a5a5a  // .space 16, 'ZZZZZZZZZZZZZZZZ'
	0x5a, 0x5a, 0x5a, 0x5a, 0x5a, 0x5a, 0x5a, 0x5a, 0x5a, 0x5a, 0x5a, 0x5a, 0x5a, 0x5a, 0x5a, 0x5a, //0x00000030 QUAD $0x5a5a5a5a5a5a5a5a; QUAD $0x5a5a5a5a5a5a5a5a  // .space 16, 'ZZZZZZZZZZZZZZZZ'
	//0x00000040 LCPI0_2
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, //0x00000040 QUAD $0x0101010101010101; QUAD $0x0101010101010101  // .space 16, '\x01\x01\x01\x01\x01\x01\x01\x01\x01\x01\x01\x01\x01\x01\x01\x01'
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, //0x00000050 QUAD $0x0101010101010101; QUAD $0x0101010101010101  // .space 16, '\x01\x01\x01\x01\x01\x01\x01\x01\x01\x01\x01\x01\x01\x01\x01\x01'
	//0x00000060 .p2align 4, 0x90
	//0x00000060 _lookup_small_key
	0x55, //0x00000060 pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000061 movq         %rsp, %rbp
	0x41, 0x57, //0x00000064 pushq        %r15
	0x41, 0x56, //0x00000066 pushq        %r14
	0x41, 0x55, //0x00000068 pushq        %r13
	0x41, 0x54, //0x0000006a pushq        %r12
	0x53, //0x0000006c pushq        %rbx
	0x50, //0x0000006d pushq        %rax
	0x4c, 0x8b, 0x57, 0x08, //0x0000006e movq         $8(%rdi), %r10
	0x4c, 0x8b, 0x2e, //0x00000072 movq         (%rsi), %r13
	0x45, 0x0f, 0xb6, 0xc2, //0x00000075 movzbl       %r10b, %r8d
	0x4b, 0x8d, 0x0c, 0x80, //0x00000079 leaq         (%r8,%r8,4), %rcx
	0x45, 0x0f, 0xb6, 0x4c, 0x0d, 0x00, //0x0000007d movzbl       (%r13,%rcx), %r9d
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000083 movq         $-1, %rax
	0x45, 0x85, 0xc9, //0x0000008a testl        %r9d, %r9d
	0x0f, 0x84, 0xc5, 0x02, 0x00, 0x00, //0x0000008d je           LBB0_40
	0x4c, 0x8b, 0x1f, //0x00000093 movq         (%rdi), %r11
	0x41, 0x8b, 0x44, 0x0d, 0x01, //0x00000096 movl         $1(%r13,%rcx), %eax
	0x8d, 0xb8, 0xa5, 0x00, 0x00, 0x00, //0x0000009b leal         $165(%rax), %edi
	0x4c, 0x01, 0xef, //0x000000a1 addq         %r13, %rdi
	0x41, 0x0f, 0xb6, 0xca, //0x000000a4 movzbl       %r10b, %ecx
	0x41, 0x83, 0xf8, 0x09, //0x000000a8 cmpl         $9, %r8d
	0x48, 0x89, 0x45, 0xd0, //0x000000ac movq         %rax, $-48(%rbp)
	0x0f, 0x83, 0xc6, 0x00, 0x00, 0x00, //0x000000b0 jae          LBB0_2
	0x45, 0x8a, 0x3b, //0x000000b6 movb         (%r11), %r15b
	0x45, 0x8d, 0x60, 0x01, //0x000000b9 leal         $1(%r8), %r12d
	0x44, 0x89, 0xcb, //0x000000bd movl         %r9d, %ebx
	//0x000000c0 .p2align 4, 0x90
	//0x000000c0 LBB0_7
	0x44, 0x38, 0x3f, //0x000000c0 cmpb         %r15b, (%rdi)
	0x0f, 0x85, 0x97, 0x00, 0x00, 0x00, //0x000000c3 jne          LBB0_8
	0x44, 0x0f, 0xb6, 0x77, 0x01, //0x000000c9 movzbl       $1(%rdi), %r14d
	0xbe, 0x01, 0x00, 0x00, 0x00, //0x000000ce movl         $1, %esi
	0x45, 0x3a, 0x73, 0x01, //0x000000d3 cmpb         $1(%r11), %r14b
	0x0f, 0x85, 0x85, 0x00, 0x00, 0x00, //0x000000d7 jne          LBB0_16
	0x0f, 0xb6, 0x47, 0x02, //0x000000dd movzbl       $2(%rdi), %eax
	0xbe, 0x02, 0x00, 0x00, 0x00, //0x000000e1 movl         $2, %esi
	0x41, 0x3a, 0x43, 0x02, //0x000000e6 cmpb         $2(%r11), %al
	0x0f, 0x85, 0x72, 0x00, 0x00, 0x00, //0x000000ea jne          LBB0_16
	0x0f, 0xb6, 0x47, 0x03, //0x000000f0 movzbl       $3(%rdi), %eax
	0xbe, 0x03, 0x00, 0x00, 0x00, //0x000000f4 movl         $3, %esi
	0x41, 0x3a, 0x43, 0x03, //0x000000f9 cmpb         $3(%r11), %al
	0x0f, 0x85, 0x5f, 0x00, 0x00, 0x00, //0x000000fd jne          LBB0_16
	0x0f, 0xb6, 0x47, 0x04, //0x00000103 movzbl       $4(%rdi), %eax
	0xbe, 0x04, 0x00, 0x00, 0x00, //0x00000107 movl         $4, %esi
	0x41, 0x3a, 0x43, 0x04, //0x0000010c cmpb         $4(%r11), %al
	0x0f, 0x85, 0x4c, 0x00, 0x00, 0x00, //0x00000110 jne          LBB0_16
	0x0f, 0xb6, 0x47, 0x05, //0x00000116 movzbl       $5(%rdi), %eax
	0xbe, 0x05, 0x00, 0x00, 0x00, //0x0000011a movl         $5, %esi
	0x41, 0x3a, 0x43, 0x05, //0x0000011f cmpb         $5(%r11), %al
	0x0f, 0x85, 0x39, 0x00, 0x00, 0x00, //0x00000123 jne          LBB0_16
	0x0f, 0xb6, 0x47, 0x06, //0x00000129 movzbl       $6(%rdi), %eax
	0xbe, 0x06, 0x00, 0x00, 0x00, //0x0000012d movl         $6, %esi
	0x41, 0x3a, 0x43, 0x06, //0x00000132 cmpb         $6(%r11), %al
	0x0f, 0x85, 0x26, 0x00, 0x00, 0x00, //0x00000136 jne          LBB0_16
	0x0f, 0xb6, 0x47, 0x07, //0x0000013c movzbl       $7(%rdi), %eax
	0x31, 0xf6, //0x00000140 xorl         %esi, %esi
	0x41, 0x3a, 0x43, 0x07, //0x00000142 cmpb         $7(%r11), %al
	0x40, 0x0f, 0x94, 0xc6, //0x00000146 sete         %sil
	0x48, 0x83, 0xc6, 0x07, //0x0000014a addq         $7, %rsi
	0xe9, 0x0f, 0x00, 0x00, 0x00, //0x0000014e jmp          LBB0_16
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000153 .p2align 4, 0x90
	//0x00000160 LBB0_8
	0x31, 0xf6, //0x00000160 xorl         %esi, %esi
	//0x00000162 LBB0_16
	0x48, 0x39, 0xce, //0x00000162 cmpq         %rcx, %rsi
	0x0f, 0x83, 0x81, 0x01, 0x00, 0x00, //0x00000165 jae          LBB0_17
	0x4c, 0x01, 0xe7, //0x0000016b addq         %r12, %rdi
	0x83, 0xc3, 0xff, //0x0000016e addl         $-1, %ebx
	0x0f, 0x85, 0x49, 0xff, 0xff, 0xff, //0x00000171 jne          LBB0_7
	0xe9, 0x43, 0x00, 0x00, 0x00, //0x00000177 jmp          LBB0_20
	//0x0000017c LBB0_2
	0xc4, 0xc1, 0x7e, 0x6f, 0x03, //0x0000017c vmovdqu      (%r11), %ymm0
	0x48, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00000181 movq         $-1, %rsi
	0x48, 0xd3, 0xe6, //0x00000188 shlq         %cl, %rsi
	0x45, 0x8d, 0x78, 0x01, //0x0000018b leal         $1(%r8), %r15d
	0x44, 0x89, 0xcb, //0x0000018f movl         %r9d, %ebx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000192 .p2align 4, 0x90
	//0x000001a0 LBB0_3
	0xc5, 0xfd, 0x74, 0x0f, //0x000001a0 vpcmpeqb     (%rdi), %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xc1, //0x000001a4 vpmovmskb    %ymm1, %eax
	0x09, 0xf0, //0x000001a8 orl          %esi, %eax
	0x83, 0xf8, 0xff, //0x000001aa cmpl         $-1, %eax
	0x0f, 0x84, 0x48, 0x01, 0x00, 0x00, //0x000001ad je           LBB0_4
	0x4c, 0x01, 0xff, //0x000001b3 addq         %r15, %rdi
	0x83, 0xc3, 0xff, //0x000001b6 addl         $-1, %ebx
	0x0f, 0x85, 0xe1, 0xff, 0xff, 0xff, //0x000001b9 jne          LBB0_3
	//0x000001bf LBB0_20
	0x48, 0x83, 0xfa, 0xff, //0x000001bf cmpq         $-1, %rdx
	0x0f, 0x84, 0x88, 0x01, 0x00, 0x00, //0x000001c3 je           LBB0_39
	0x48, 0x8b, 0x45, 0xd0, //0x000001c9 movq         $-48(%rbp), %rax
	0x48, 0x01, 0xd0, //0x000001cd addq         %rdx, %rax
	0x49, 0x01, 0xc5, //0x000001d0 addq         %rax, %r13
	0xc4, 0xc1, 0x7e, 0x6f, 0x03, //0x000001d3 vmovdqu      (%r11), %ymm0
	0xc5, 0xfd, 0x64, 0x0d, 0x20, 0xfe, 0xff, 0xff, //0x000001d8 vpcmpgtb     $-480(%rip), %ymm0, %ymm1  /* LCPI0_0+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x15, 0x38, 0xfe, 0xff, 0xff, //0x000001e0 vmovdqu      $-456(%rip), %ymm2  /* LCPI0_1+0(%rip) */
	0xc5, 0xed, 0x64, 0xd0, //0x000001e8 vpcmpgtb     %ymm0, %ymm2, %ymm2
	0xc5, 0xed, 0xdb, 0xc9, //0x000001ec vpand        %ymm1, %ymm2, %ymm1
	0xc5, 0xf5, 0xdb, 0x0d, 0x48, 0xfe, 0xff, 0xff, //0x000001f0 vpand        $-440(%rip), %ymm1, %ymm1  /* LCPI0_2+0(%rip) */
	0xc5, 0xf5, 0x71, 0xf1, 0x05, //0x000001f8 vpsllw       $5, %ymm1, %ymm1
	0xc5, 0xf5, 0xfc, 0xc0, //0x000001fd vpaddb       %ymm0, %ymm1, %ymm0
	0x41, 0x0f, 0xb6, 0xca, //0x00000201 movzbl       %r10b, %ecx
	0x41, 0x83, 0xf8, 0x09, //0x00000205 cmpl         $9, %r8d
	0x0f, 0x83, 0xf7, 0x00, 0x00, 0x00, //0x00000209 jae          LBB0_22
	0xc4, 0xe3, 0x79, 0x14, 0xc2, 0x01, //0x0000020f vpextrb      $1, %xmm0, %edx
	0xc4, 0xc3, 0x79, 0x14, 0xc4, 0x02, //0x00000215 vpextrb      $2, %xmm0, %r12d
	0xc4, 0xc3, 0x79, 0x14, 0xc7, 0x03, //0x0000021b vpextrb      $3, %xmm0, %r15d
	0xc4, 0xc3, 0x79, 0x14, 0xc2, 0x04, //0x00000221 vpextrb      $4, %xmm0, %r10d
	0xc4, 0xc3, 0x79, 0x14, 0xc3, 0x05, //0x00000227 vpextrb      $5, %xmm0, %r11d
	0xc4, 0xc3, 0x79, 0x14, 0xc6, 0x06, //0x0000022d vpextrb      $6, %xmm0, %r14d
	0xc5, 0xf9, 0x7e, 0xc3, //0x00000233 vmovd        %xmm0, %ebx
	0xc4, 0xe3, 0x79, 0x14, 0xc0, 0x07, //0x00000237 vpextrb      $7, %xmm0, %eax
	0x41, 0x83, 0xc0, 0x01, //0x0000023d addl         $1, %r8d
	0x41, 0x83, 0xf9, 0x02, //0x00000241 cmpl         $2, %r9d
	0xbf, 0x01, 0x00, 0x00, 0x00, //0x00000245 movl         $1, %edi
	0x41, 0x0f, 0x43, 0xf9, //0x0000024a cmovael      %r9d, %edi
	0x90, 0x90, //0x0000024e .p2align 4, 0x90
	//0x00000250 LBB0_26
	0x41, 0x38, 0x5d, 0x00, //0x00000250 cmpb         %bl, (%r13)
	0x0f, 0x85, 0x76, 0x00, 0x00, 0x00, //0x00000254 jne          LBB0_27
	0xbe, 0x01, 0x00, 0x00, 0x00, //0x0000025a movl         $1, %esi
	0x41, 0x38, 0x55, 0x01, //0x0000025f cmpb         %dl, $1(%r13)
	0x0f, 0x85, 0x69, 0x00, 0x00, 0x00, //0x00000263 jne          LBB0_35
	0xbe, 0x02, 0x00, 0x00, 0x00, //0x00000269 movl         $2, %esi
	0x45, 0x38, 0x65, 0x02, //0x0000026e cmpb         %r12b, $2(%r13)
	0x0f, 0x85, 0x5a, 0x00, 0x00, 0x00, //0x00000272 jne          LBB0_35
	0xbe, 0x03, 0x00, 0x00, 0x00, //0x00000278 movl         $3, %esi
	0x45, 0x38, 0x7d, 0x03, //0x0000027d cmpb         %r15b, $3(%r13)
	0x0f, 0x85, 0x4b, 0x00, 0x00, 0x00, //0x00000281 jne          LBB0_35
	0xbe, 0x04, 0x00, 0x00, 0x00, //0x00000287 movl         $4, %esi
	0x45, 0x38, 0x55, 0x04, //0x0000028c cmpb         %r10b, $4(%r13)
	0x0f, 0x85, 0x3c, 0x00, 0x00, 0x00, //0x00000290 jne          LBB0_35
	0xbe, 0x05, 0x00, 0x00, 0x00, //0x00000296 movl         $5, %esi
	0x45, 0x38, 0x5d, 0x05, //0x0000029b cmpb         %r11b, $5(%r13)
	0x0f, 0x85, 0x2d, 0x00, 0x00, 0x00, //0x0000029f jne          LBB0_35
	0xbe, 0x06, 0x00, 0x00, 0x00, //0x000002a5 movl         $6, %esi
	0x45, 0x38, 0x75, 0x06, //0x000002aa cmpb         %r14b, $6(%r13)
	0x0f, 0x85, 0x1e, 0x00, 0x00, 0x00, //0x000002ae jne          LBB0_35
	0x31, 0xf6, //0x000002b4 xorl         %esi, %esi
	0x41, 0x38, 0x45, 0x07, //0x000002b6 cmpb         %al, $7(%r13)
	0x40, 0x0f, 0x94, 0xc6, //0x000002ba sete         %sil
	0x48, 0x83, 0xc6, 0x07, //0x000002be addq         $7, %rsi
	0xe9, 0x0b, 0x00, 0x00, 0x00, //0x000002c2 jmp          LBB0_35
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000002c7 .p2align 4, 0x90
	//0x000002d0 LBB0_27
	0x31, 0xf6, //0x000002d0 xorl         %esi, %esi
	//0x000002d2 LBB0_35
	0x48, 0x39, 0xce, //0x000002d2 cmpq         %rcx, %rsi
	0x0f, 0x83, 0x8f, 0x00, 0x00, 0x00, //0x000002d5 jae          LBB0_36
	0x4d, 0x01, 0xc5, //0x000002db addq         %r8, %r13
	0x83, 0xc7, 0xff, //0x000002de addl         $-1, %edi
	0x0f, 0x85, 0x69, 0xff, 0xff, 0xff, //0x000002e1 jne          LBB0_26
	0xe9, 0x65, 0x00, 0x00, 0x00, //0x000002e7 jmp          LBB0_39
	//0x000002ec LBB0_17
	0x4c, 0x01, 0xe7, //0x000002ec addq         %r12, %rdi
	0x48, 0x83, 0xc7, 0xff, //0x000002ef addq         $-1, %rdi
	0x0f, 0xb6, 0x07, //0x000002f3 movzbl       (%rdi), %eax
	0xe9, 0x5d, 0x00, 0x00, 0x00, //0x000002f6 jmp          LBB0_40
	//0x000002fb LBB0_4
	0x48, 0x01, 0xcf, //0x000002fb addq         %rcx, %rdi
	0x0f, 0xb6, 0x07, //0x000002fe movzbl       (%rdi), %eax
	0xe9, 0x52, 0x00, 0x00, 0x00, //0x00000301 jmp          LBB0_40
	//0x00000306 LBB0_22
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000306 movq         $-1, %rax
	0x48, 0xd3, 0xe0, //0x0000030d shlq         %cl, %rax
	0x41, 0x83, 0xc0, 0x01, //0x00000310 addl         $1, %r8d
	0x41, 0x83, 0xf9, 0x02, //0x00000314 cmpl         $2, %r9d
	0xba, 0x01, 0x00, 0x00, 0x00, //0x00000318 movl         $1, %edx
	0x41, 0x0f, 0x43, 0xd1, //0x0000031d cmovael      %r9d, %edx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000321 .p2align 4, 0x90
	//0x00000330 LBB0_23
	0xc4, 0xc1, 0x7d, 0x74, 0x4d, 0x00, //0x00000330 vpcmpeqb     (%r13), %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xf1, //0x00000336 vpmovmskb    %ymm1, %esi
	0x09, 0xc6, //0x0000033a orl          %eax, %esi
	0x83, 0xfe, 0xff, //0x0000033c cmpl         $-1, %esi
	0x0f, 0x84, 0x35, 0x00, 0x00, 0x00, //0x0000033f je           LBB0_24
	0x4d, 0x01, 0xc5, //0x00000345 addq         %r8, %r13
	0x83, 0xc2, 0xff, //0x00000348 addl         $-1, %edx
	0x0f, 0x85, 0xdf, 0xff, 0xff, 0xff, //0x0000034b jne          LBB0_23
	//0x00000351 LBB0_39
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000351 movq         $-1, %rax
	//0x00000358 LBB0_40
	0x48, 0x83, 0xc4, 0x08, //0x00000358 addq         $8, %rsp
	0x5b, //0x0000035c popq         %rbx
	0x41, 0x5c, //0x0000035d popq         %r12
	0x41, 0x5d, //0x0000035f popq         %r13
	0x41, 0x5e, //0x00000361 popq         %r14
	0x41, 0x5f, //0x00000363 popq         %r15
	0x5d, //0x00000365 popq         %rbp
	0xc5, 0xf8, 0x77, //0x00000366 vzeroupper   
	0xc3, //0x00000369 retq         
	//0x0000036a LBB0_36
	0x4b, 0x8d, 0x3c, 0x28, //0x0000036a leaq         (%r8,%r13), %rdi
	0x48, 0x83, 0xc7, 0xff, //0x0000036e addq         $-1, %rdi
	0x0f, 0xb6, 0x07, //0x00000372 movzbl       (%rdi), %eax
	0xe9, 0xde, 0xff, 0xff, 0xff, //0x00000375 jmp          LBB0_40
	//0x0000037a LBB0_24
	0x49, 0x01, 0xcd, //0x0000037a addq         %rcx, %r13
	0x4c, 0x89, 0xef, //0x0000037d movq         %r13, %rdi
	0x41, 0x0f, 0xb6, 0x45, 0x00, //0x00000380 movzbl       (%r13), %eax
	0xe9, 0xce, 0xff, 0xff, 0xff, //0x00000385 jmp          LBB0_40
}
 
