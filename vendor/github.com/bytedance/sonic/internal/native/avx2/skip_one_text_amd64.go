// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package avx2

var _text_skip_one = []byte{
	// .p2align 5, 0x00
	// LCPI0_0
	0x20, // .byte 32
	0x00, //0x00000001 .byte 0
	0x00, //0x00000002 .byte 0
	0x00, //0x00000003 .byte 0
	0x00, //0x00000004 .byte 0
	0x00, //0x00000005 .byte 0
	0x00, //0x00000006 .byte 0
	0x00, //0x00000007 .byte 0
	0x00, //0x00000008 .byte 0
	0x09, //0x00000009 .byte 9
	0x0a, //0x0000000a .byte 10
	0x00, //0x0000000b .byte 0
	0x00, //0x0000000c .byte 0
	0x0d, //0x0000000d .byte 13
	0x00, //0x0000000e .byte 0
	0x00, //0x0000000f .byte 0
	0x20, //0x00000010 .byte 32
	0x00, //0x00000011 .byte 0
	0x00, //0x00000012 .byte 0
	0x00, //0x00000013 .byte 0
	0x00, //0x00000014 .byte 0
	0x00, //0x00000015 .byte 0
	0x00, //0x00000016 .byte 0
	0x00, //0x00000017 .byte 0
	0x00, //0x00000018 .byte 0
	0x09, //0x00000019 .byte 9
	0x0a, //0x0000001a .byte 10
	0x00, //0x0000001b .byte 0
	0x00, //0x0000001c .byte 0
	0x0d, //0x0000001d .byte 13
	0x00, //0x0000001e .byte 0
	0x00, //0x0000001f .byte 0
	//0x00000020 LCPI0_1
	0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, //0x00000020 QUAD $0x2c2c2c2c2c2c2c2c; QUAD $0x2c2c2c2c2c2c2c2c  // .space 16, ',,,,,,,,,,,,,,,,'
	0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, //0x00000030 QUAD $0x2c2c2c2c2c2c2c2c; QUAD $0x2c2c2c2c2c2c2c2c  // .space 16, ',,,,,,,,,,,,,,,,'
	//0x00000040 LCPI0_2
	0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, //0x00000040 QUAD $0xdfdfdfdfdfdfdfdf; QUAD $0xdfdfdfdfdfdfdfdf  // .space 16, '\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf'
	0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, //0x00000050 QUAD $0xdfdfdfdfdfdfdfdf; QUAD $0xdfdfdfdfdfdfdfdf  // .space 16, '\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf'
	//0x00000060 LCPI0_3
	0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, //0x00000060 QUAD $0x5d5d5d5d5d5d5d5d; QUAD $0x5d5d5d5d5d5d5d5d  // .space 16, ']]]]]]]]]]]]]]]]'
	0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, //0x00000070 QUAD $0x5d5d5d5d5d5d5d5d; QUAD $0x5d5d5d5d5d5d5d5d  // .space 16, ']]]]]]]]]]]]]]]]'
	//0x00000080 LCPI0_7
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, //0x00000080 QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, //0x00000090 QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	//0x000000a0 LCPI0_8
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x000000a0 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x000000b0 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	//0x000000c0 LCPI0_9
	0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, //0x000000c0 QUAD $0x7b7b7b7b7b7b7b7b; QUAD $0x7b7b7b7b7b7b7b7b  // .space 16, '{{{{{{{{{{{{{{{{'
	0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, //0x000000d0 QUAD $0x7b7b7b7b7b7b7b7b; QUAD $0x7b7b7b7b7b7b7b7b  // .space 16, '{{{{{{{{{{{{{{{{'
	//0x000000e0 LCPI0_10
	0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, //0x000000e0 QUAD $0x7d7d7d7d7d7d7d7d; QUAD $0x7d7d7d7d7d7d7d7d  // .space 16, '}}}}}}}}}}}}}}}}'
	0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, //0x000000f0 QUAD $0x7d7d7d7d7d7d7d7d; QUAD $0x7d7d7d7d7d7d7d7d  // .space 16, '}}}}}}}}}}}}}}}}'
	//0x00000100 LCPI0_11
	0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, //0x00000100 QUAD $0x5b5b5b5b5b5b5b5b; QUAD $0x5b5b5b5b5b5b5b5b  // .space 16, '[[[[[[[[[[[[[[[['
	0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, //0x00000110 QUAD $0x5b5b5b5b5b5b5b5b; QUAD $0x5b5b5b5b5b5b5b5b  // .space 16, '[[[[[[[[[[[[[[[['
	//0x00000120 LCPI0_13
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, //0x00000120 QUAD $0x2020202020202020; QUAD $0x2020202020202020  // .space 16, '                '
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, //0x00000130 QUAD $0x2020202020202020; QUAD $0x2020202020202020  // .space 16, '                '
	//0x00000140 LCPI0_14
	0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, //0x00000140 QUAD $0x2f2f2f2f2f2f2f2f; QUAD $0x2f2f2f2f2f2f2f2f  // .space 16, '////////////////'
	0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, //0x00000150 QUAD $0x2f2f2f2f2f2f2f2f; QUAD $0x2f2f2f2f2f2f2f2f  // .space 16, '////////////////'
	//0x00000160 LCPI0_15
	0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, //0x00000160 QUAD $0x3a3a3a3a3a3a3a3a; QUAD $0x3a3a3a3a3a3a3a3a  // .space 16, '::::::::::::::::'
	0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, //0x00000170 QUAD $0x3a3a3a3a3a3a3a3a; QUAD $0x3a3a3a3a3a3a3a3a  // .space 16, '::::::::::::::::'
	//0x00000180 LCPI0_16
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x00000180 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x00000190 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	//0x000001a0 LCPI0_17
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x000001a0 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x000001b0 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	//0x000001c0 LCPI0_18
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x000001c0 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x000001d0 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	//0x000001e0 LCPI0_19
	0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, //0x000001e0 QUAD $0x4545454545454545; QUAD $0x4545454545454545  // .space 16, 'EEEEEEEEEEEEEEEE'
	0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, //0x000001f0 QUAD $0x4545454545454545; QUAD $0x4545454545454545  // .space 16, 'EEEEEEEEEEEEEEEE'
	//0x00000200 .p2align 4, 0x00
	//0x00000200 LCPI0_4
	0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, //0x00000200 QUAD $0x2c2c2c2c2c2c2c2c; QUAD $0x2c2c2c2c2c2c2c2c  // .space 16, ',,,,,,,,,,,,,,,,'
	//0x00000210 LCPI0_5
	0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, //0x00000210 QUAD $0xdfdfdfdfdfdfdfdf; QUAD $0xdfdfdfdfdfdfdfdf  // .space 16, '\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf'
	//0x00000220 LCPI0_6
	0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, //0x00000220 QUAD $0x5d5d5d5d5d5d5d5d; QUAD $0x5d5d5d5d5d5d5d5d  // .space 16, ']]]]]]]]]]]]]]]]'
	//0x00000230 LCPI0_12
	0x01, 0x00, 0x00, 0x00, //0x00000230 .long 1
	0x00, 0x00, 0x00, 0x00, //0x00000234 .long 0
	0x00, 0x00, 0x00, 0x00, //0x00000238 .long 0
	0x00, 0x00, 0x00, 0x00, //0x0000023c .long 0
	//0x00000240 LCPI0_20
	0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, //0x00000240 QUAD $0x2f2f2f2f2f2f2f2f; QUAD $0x2f2f2f2f2f2f2f2f  // .space 16, '////////////////'
	//0x00000250 LCPI0_21
	0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, //0x00000250 QUAD $0x3a3a3a3a3a3a3a3a; QUAD $0x3a3a3a3a3a3a3a3a  // .space 16, '::::::::::::::::'
	//0x00000260 LCPI0_22
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x00000260 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	//0x00000270 LCPI0_23
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x00000270 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	//0x00000280 LCPI0_24
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x00000280 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	//0x00000290 LCPI0_25
	0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, //0x00000290 QUAD $0x4545454545454545; QUAD $0x4545454545454545  // .space 16, 'EEEEEEEEEEEEEEEE'
	//0x000002a0 .p2align 4, 0x90
	//0x000002a0 _skip_one
	0x55, //0x000002a0 pushq        %rbp
	0x48, 0x89, 0xe5, //0x000002a1 movq         %rsp, %rbp
	0x41, 0x57, //0x000002a4 pushq        %r15
	0x41, 0x56, //0x000002a6 pushq        %r14
	0x41, 0x55, //0x000002a8 pushq        %r13
	0x41, 0x54, //0x000002aa pushq        %r12
	0x53, //0x000002ac pushq        %rbx
	0x48, 0x81, 0xec, 0xa0, 0x00, 0x00, 0x00, //0x000002ad subq         $160, %rsp
	0x49, 0x89, 0xca, //0x000002b4 movq         %rcx, %r10
	0x49, 0x89, 0xf3, //0x000002b7 movq         %rsi, %r11
	0x49, 0x89, 0xfd, //0x000002ba movq         %rdi, %r13
	0x41, 0xf6, 0xc2, 0x40, //0x000002bd testb        $64, %r10b
	0x0f, 0x85, 0x82, 0x00, 0x00, 0x00, //0x000002c1 jne          LBB0_2
	0x49, 0x89, 0xd1, //0x000002c7 movq         %rdx, %r9
	0xc5, 0xfa, 0x6f, 0x05, 0x5e, 0xff, 0xff, 0xff, //0x000002ca vmovdqu      $-162(%rip), %xmm0  /* LCPI0_12+0(%rip) */
	0xc5, 0xfa, 0x7f, 0x02, //0x000002d2 vmovdqu      %xmm0, (%rdx)
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x000002d6 movq         $-1, %r14
	0xc5, 0xfe, 0x6f, 0x2d, 0x1b, 0xfd, 0xff, 0xff, //0x000002dd vmovdqu      $-741(%rip), %ymm5  /* LCPI0_0+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0x93, 0xfd, 0xff, 0xff, //0x000002e5 vmovdqu      $-621(%rip), %ymm6  /* LCPI0_7+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0xab, 0xfd, 0xff, 0xff, //0x000002ed vmovdqu      $-597(%rip), %ymm7  /* LCPI0_8+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0x23, 0xfe, 0xff, 0xff, //0x000002f5 vmovdqu      $-477(%rip), %ymm8  /* LCPI0_13+0(%rip) */
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x000002fd vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0x7e, 0x6f, 0x15, 0x36, 0xfe, 0xff, 0xff, //0x00000302 vmovdqu      $-458(%rip), %ymm10  /* LCPI0_14+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0x4e, 0xfe, 0xff, 0xff, //0x0000030a vmovdqu      $-434(%rip), %ymm11  /* LCPI0_15+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0x66, 0xfe, 0xff, 0xff, //0x00000312 vmovdqu      $-410(%rip), %ymm12  /* LCPI0_16+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0x7e, 0xfe, 0xff, 0xff, //0x0000031a vmovdqu      $-386(%rip), %ymm13  /* LCPI0_17+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0x16, 0xfd, 0xff, 0xff, //0x00000322 vmovdqu      $-746(%rip), %ymm14  /* LCPI0_2+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x3d, 0x8e, 0xfe, 0xff, 0xff, //0x0000032a vmovdqu      $-370(%rip), %ymm15  /* LCPI0_18+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0xa6, 0xfe, 0xff, 0xff, //0x00000332 vmovdqu      $-346(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0x4c, 0x89, 0x54, 0x24, 0x18, //0x0000033a movq         %r10, $24(%rsp)
	0x4c, 0x89, 0x5c, 0x24, 0x08, //0x0000033f movq         %r11, $8(%rsp)
	0xe9, 0xc7, 0x03, 0x00, 0x00, //0x00000344 jmp          LBB0_57
	//0x00000349 LBB0_2
	0x4d, 0x8b, 0x55, 0x00, //0x00000349 movq         (%r13), %r10
	0x49, 0x8b, 0x55, 0x08, //0x0000034d movq         $8(%r13), %rdx
	0x49, 0x8b, 0x33, //0x00000351 movq         (%r11), %rsi
	0x48, 0x39, 0xd6, //0x00000354 cmpq         %rdx, %rsi
	0x0f, 0x83, 0x26, 0x00, 0x00, 0x00, //0x00000357 jae          LBB0_7
	0x41, 0x8a, 0x04, 0x32, //0x0000035d movb         (%r10,%rsi), %al
	0x3c, 0x0d, //0x00000361 cmpb         $13, %al
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x00000363 je           LBB0_7
	0x3c, 0x20, //0x00000369 cmpb         $32, %al
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x0000036b je           LBB0_7
	0x04, 0xf7, //0x00000371 addb         $-9, %al
	0x3c, 0x01, //0x00000373 cmpb         $1, %al
	0x0f, 0x86, 0x08, 0x00, 0x00, 0x00, //0x00000375 jbe          LBB0_7
	0x48, 0x89, 0xf0, //0x0000037b movq         %rsi, %rax
	0xe9, 0x7b, 0x01, 0x00, 0x00, //0x0000037e jmp          LBB0_33
	//0x00000383 LBB0_7
	0x48, 0x8d, 0x46, 0x01, //0x00000383 leaq         $1(%rsi), %rax
	0x48, 0x39, 0xd0, //0x00000387 cmpq         %rdx, %rax
	0x0f, 0x83, 0x22, 0x00, 0x00, 0x00, //0x0000038a jae          LBB0_11
	0x41, 0x8a, 0x0c, 0x02, //0x00000390 movb         (%r10,%rax), %cl
	0x80, 0xf9, 0x0d, //0x00000394 cmpb         $13, %cl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x00000397 je           LBB0_11
	0x80, 0xf9, 0x20, //0x0000039d cmpb         $32, %cl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x000003a0 je           LBB0_11
	0x80, 0xc1, 0xf7, //0x000003a6 addb         $-9, %cl
	0x80, 0xf9, 0x01, //0x000003a9 cmpb         $1, %cl
	0x0f, 0x87, 0x4c, 0x01, 0x00, 0x00, //0x000003ac ja           LBB0_33
	//0x000003b2 LBB0_11
	0x48, 0x8d, 0x46, 0x02, //0x000003b2 leaq         $2(%rsi), %rax
	0x48, 0x39, 0xd0, //0x000003b6 cmpq         %rdx, %rax
	0x0f, 0x83, 0x22, 0x00, 0x00, 0x00, //0x000003b9 jae          LBB0_15
	0x41, 0x8a, 0x0c, 0x02, //0x000003bf movb         (%r10,%rax), %cl
	0x80, 0xf9, 0x0d, //0x000003c3 cmpb         $13, %cl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x000003c6 je           LBB0_15
	0x80, 0xf9, 0x20, //0x000003cc cmpb         $32, %cl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x000003cf je           LBB0_15
	0x80, 0xc1, 0xf7, //0x000003d5 addb         $-9, %cl
	0x80, 0xf9, 0x01, //0x000003d8 cmpb         $1, %cl
	0x0f, 0x87, 0x1d, 0x01, 0x00, 0x00, //0x000003db ja           LBB0_33
	//0x000003e1 LBB0_15
	0x48, 0x8d, 0x46, 0x03, //0x000003e1 leaq         $3(%rsi), %rax
	0x48, 0x39, 0xd0, //0x000003e5 cmpq         %rdx, %rax
	0x0f, 0x83, 0x22, 0x00, 0x00, 0x00, //0x000003e8 jae          LBB0_19
	0x41, 0x8a, 0x0c, 0x02, //0x000003ee movb         (%r10,%rax), %cl
	0x80, 0xf9, 0x0d, //0x000003f2 cmpb         $13, %cl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x000003f5 je           LBB0_19
	0x80, 0xf9, 0x20, //0x000003fb cmpb         $32, %cl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x000003fe je           LBB0_19
	0x80, 0xc1, 0xf7, //0x00000404 addb         $-9, %cl
	0x80, 0xf9, 0x01, //0x00000407 cmpb         $1, %cl
	0x0f, 0x87, 0xee, 0x00, 0x00, 0x00, //0x0000040a ja           LBB0_33
	//0x00000410 LBB0_19
	0x48, 0x8d, 0x46, 0x04, //0x00000410 leaq         $4(%rsi), %rax
	0x48, 0x89, 0xd7, //0x00000414 movq         %rdx, %rdi
	0x48, 0x29, 0xc7, //0x00000417 subq         %rax, %rdi
	0x0f, 0x86, 0xb7, 0x00, 0x00, 0x00, //0x0000041a jbe          LBB0_31
	0x48, 0x83, 0xff, 0x20, //0x00000420 cmpq         $32, %rdi
	0x0f, 0x82, 0xab, 0x32, 0x00, 0x00, //0x00000424 jb           LBB0_666
	0x48, 0xc7, 0xc7, 0xfc, 0xff, 0xff, 0xff, //0x0000042a movq         $-4, %rdi
	0x48, 0x29, 0xf7, //0x00000431 subq         %rsi, %rdi
	0xc5, 0xfe, 0x6f, 0x05, 0xc4, 0xfb, 0xff, 0xff, //0x00000434 vmovdqu      $-1084(%rip), %ymm0  /* LCPI0_0+0(%rip) */
	0x90, 0x90, 0x90, 0x90, //0x0000043c .p2align 4, 0x90
	//0x00000440 LBB0_22
	0xc4, 0xc1, 0x7e, 0x6f, 0x0c, 0x02, //0x00000440 vmovdqu      (%r10,%rax), %ymm1
	0xc4, 0xe2, 0x7d, 0x00, 0xd1, //0x00000446 vpshufb      %ymm1, %ymm0, %ymm2
	0xc5, 0xf5, 0x74, 0xca, //0x0000044b vpcmpeqb     %ymm2, %ymm1, %ymm1
	0xc5, 0xfd, 0xd7, 0xc9, //0x0000044f vpmovmskb    %ymm1, %ecx
	0x83, 0xf9, 0xff, //0x00000453 cmpl         $-1, %ecx
	0x0f, 0x85, 0x8a, 0x00, 0x00, 0x00, //0x00000456 jne          LBB0_32
	0x48, 0x83, 0xc0, 0x20, //0x0000045c addq         $32, %rax
	0x48, 0x8d, 0x0c, 0x3a, //0x00000460 leaq         (%rdx,%rdi), %rcx
	0x48, 0x83, 0xc1, 0xe0, //0x00000464 addq         $-32, %rcx
	0x48, 0x83, 0xc7, 0xe0, //0x00000468 addq         $-32, %rdi
	0x48, 0x83, 0xf9, 0x1f, //0x0000046c cmpq         $31, %rcx
	0x0f, 0x87, 0xca, 0xff, 0xff, 0xff, //0x00000470 ja           LBB0_22
	0x4c, 0x89, 0xd0, //0x00000476 movq         %r10, %rax
	0x48, 0x29, 0xf8, //0x00000479 subq         %rdi, %rax
	0x48, 0x01, 0xd7, //0x0000047c addq         %rdx, %rdi
	0x48, 0x85, 0xff, //0x0000047f testq        %rdi, %rdi
	0x0f, 0x84, 0x37, 0x00, 0x00, 0x00, //0x00000482 je           LBB0_30
	//0x00000488 LBB0_25
	0x4c, 0x8d, 0x04, 0x38, //0x00000488 leaq         (%rax,%rdi), %r8
	0x31, 0xf6, //0x0000048c xorl         %esi, %esi
	0x48, 0xb9, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x0000048e movabsq      $4294977024, %rcx
	//0x00000498 LBB0_26
	0x0f, 0xbe, 0x1c, 0x30, //0x00000498 movsbl       (%rax,%rsi), %ebx
	0x83, 0xfb, 0x20, //0x0000049c cmpl         $32, %ebx
	0x0f, 0x87, 0xe8, 0x31, 0x00, 0x00, //0x0000049f ja           LBB0_663
	0x48, 0x0f, 0xa3, 0xd9, //0x000004a5 btq          %rbx, %rcx
	0x0f, 0x83, 0xde, 0x31, 0x00, 0x00, //0x000004a9 jae          LBB0_663
	0x48, 0x83, 0xc6, 0x01, //0x000004af addq         $1, %rsi
	0x48, 0x39, 0xf7, //0x000004b3 cmpq         %rsi, %rdi
	0x0f, 0x85, 0xdc, 0xff, 0xff, 0xff, //0x000004b6 jne          LBB0_26
	0x4c, 0x89, 0xc0, //0x000004bc movq         %r8, %rax
	//0x000004bf LBB0_30
	0x4c, 0x29, 0xd0, //0x000004bf subq         %r10, %rax
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x000004c2 movq         $-1, %r15
	0x48, 0x39, 0xd0, //0x000004c9 cmpq         %rdx, %rax
	0x0f, 0x82, 0x2c, 0x00, 0x00, 0x00, //0x000004cc jb           LBB0_33
	0xe9, 0x01, 0x30, 0x00, 0x00, //0x000004d2 jmp          LBB0_638
	//0x000004d7 LBB0_31
	0x49, 0x89, 0x03, //0x000004d7 movq         %rax, (%r11)
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x000004da movq         $-1, %r15
	0xe9, 0xf2, 0x2f, 0x00, 0x00, //0x000004e1 jmp          LBB0_638
	//0x000004e6 LBB0_32
	0xf7, 0xd1, //0x000004e6 notl         %ecx
	0x0f, 0xbc, 0xc1, //0x000004e8 bsfl         %ecx, %eax
	0x48, 0x29, 0xf8, //0x000004eb subq         %rdi, %rax
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x000004ee movq         $-1, %r15
	0x48, 0x39, 0xd0, //0x000004f5 cmpq         %rdx, %rax
	0x0f, 0x83, 0xda, 0x2f, 0x00, 0x00, //0x000004f8 jae          LBB0_638
	//0x000004fe LBB0_33
	0x48, 0x8d, 0x50, 0x01, //0x000004fe leaq         $1(%rax), %rdx
	0x49, 0x89, 0x13, //0x00000502 movq         %rdx, (%r11)
	0x41, 0x0f, 0xbe, 0x0c, 0x02, //0x00000505 movsbl       (%r10,%rax), %ecx
	0x83, 0xf9, 0x7b, //0x0000050a cmpl         $123, %ecx
	0x0f, 0x87, 0xcd, 0x28, 0x00, 0x00, //0x0000050d ja           LBB0_569
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00000513 movq         $-1, %r15
	0x48, 0x8d, 0x35, 0x7b, 0x32, 0x00, 0x00, //0x0000051a leaq         $12923(%rip), %rsi  /* LJTI0_0+0(%rip) */
	0x48, 0x63, 0x0c, 0x8e, //0x00000521 movslq       (%rsi,%rcx,4), %rcx
	0x48, 0x01, 0xf1, //0x00000525 addq         %rsi, %rcx
	0xff, 0xe1, //0x00000528 jmpq         *%rcx
	//0x0000052a LBB0_35
	0x49, 0x8b, 0x7d, 0x08, //0x0000052a movq         $8(%r13), %rdi
	0x48, 0x89, 0xfe, //0x0000052e movq         %rdi, %rsi
	0x48, 0x29, 0xd6, //0x00000531 subq         %rdx, %rsi
	0x48, 0x83, 0xfe, 0x20, //0x00000534 cmpq         $32, %rsi
	0x0f, 0x82, 0xa8, 0x31, 0x00, 0x00, //0x00000538 jb           LBB0_667
	0x48, 0x89, 0xc6, //0x0000053e movq         %rax, %rsi
	0x48, 0xf7, 0xd6, //0x00000541 notq         %rsi
	0xc5, 0xfe, 0x6f, 0x05, 0xd4, 0xfa, 0xff, 0xff, //0x00000544 vmovdqu      $-1324(%rip), %ymm0  /* LCPI0_1+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x0d, 0xec, 0xfa, 0xff, 0xff, //0x0000054c vmovdqu      $-1300(%rip), %ymm1  /* LCPI0_2+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x15, 0x04, 0xfb, 0xff, 0xff, //0x00000554 vmovdqu      $-1276(%rip), %ymm2  /* LCPI0_3+0(%rip) */
	0x90, 0x90, 0x90, 0x90, //0x0000055c .p2align 4, 0x90
	//0x00000560 LBB0_37
	0xc4, 0xc1, 0x7e, 0x6f, 0x1c, 0x12, //0x00000560 vmovdqu      (%r10,%rdx), %ymm3
	0xc5, 0xe5, 0x74, 0xe0, //0x00000566 vpcmpeqb     %ymm0, %ymm3, %ymm4
	0xc5, 0xe5, 0xdb, 0xd9, //0x0000056a vpand        %ymm1, %ymm3, %ymm3
	0xc5, 0xe5, 0x74, 0xda, //0x0000056e vpcmpeqb     %ymm2, %ymm3, %ymm3
	0xc5, 0xe5, 0xeb, 0xdc, //0x00000572 vpor         %ymm4, %ymm3, %ymm3
	0xc5, 0xfd, 0xd7, 0xcb, //0x00000576 vpmovmskb    %ymm3, %ecx
	0x85, 0xc9, //0x0000057a testl        %ecx, %ecx
	0x0f, 0x85, 0xca, 0x00, 0x00, 0x00, //0x0000057c jne          LBB0_51
	0x48, 0x83, 0xc2, 0x20, //0x00000582 addq         $32, %rdx
	0x48, 0x8d, 0x0c, 0x37, //0x00000586 leaq         (%rdi,%rsi), %rcx
	0x48, 0x83, 0xc1, 0xe0, //0x0000058a addq         $-32, %rcx
	0x48, 0x83, 0xc6, 0xe0, //0x0000058e addq         $-32, %rsi
	0x48, 0x83, 0xf9, 0x1f, //0x00000592 cmpq         $31, %rcx
	0x0f, 0x87, 0xc4, 0xff, 0xff, 0xff, //0x00000596 ja           LBB0_37
	0x4c, 0x89, 0xd2, //0x0000059c movq         %r10, %rdx
	0x48, 0x29, 0xf2, //0x0000059f subq         %rsi, %rdx
	0x48, 0x01, 0xf7, //0x000005a2 addq         %rsi, %rdi
	0x48, 0x89, 0xfe, //0x000005a5 movq         %rdi, %rsi
	0x48, 0x83, 0xfe, 0x10, //0x000005a8 cmpq         $16, %rsi
	0x0f, 0x82, 0x54, 0x00, 0x00, 0x00, //0x000005ac jb           LBB0_43
	//0x000005b2 LBB0_40
	0x4c, 0x89, 0xd7, //0x000005b2 movq         %r10, %rdi
	0x48, 0x29, 0xd7, //0x000005b5 subq         %rdx, %rdi
	0xc5, 0xfa, 0x6f, 0x05, 0x40, 0xfc, 0xff, 0xff, //0x000005b8 vmovdqu      $-960(%rip), %xmm0  /* LCPI0_4+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x0d, 0x48, 0xfc, 0xff, 0xff, //0x000005c0 vmovdqu      $-952(%rip), %xmm1  /* LCPI0_5+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x15, 0x50, 0xfc, 0xff, 0xff, //0x000005c8 vmovdqu      $-944(%rip), %xmm2  /* LCPI0_6+0(%rip) */
	//0x000005d0 LBB0_41
	0xc5, 0xfa, 0x6f, 0x1a, //0x000005d0 vmovdqu      (%rdx), %xmm3
	0xc5, 0xe1, 0x74, 0xe0, //0x000005d4 vpcmpeqb     %xmm0, %xmm3, %xmm4
	0xc5, 0xe1, 0xdb, 0xd9, //0x000005d8 vpand        %xmm1, %xmm3, %xmm3
	0xc5, 0xe1, 0x74, 0xda, //0x000005dc vpcmpeqb     %xmm2, %xmm3, %xmm3
	0xc5, 0xe1, 0xeb, 0xdc, //0x000005e0 vpor         %xmm4, %xmm3, %xmm3
	0xc5, 0xf9, 0xd7, 0xcb, //0x000005e4 vpmovmskb    %xmm3, %ecx
	0x85, 0xc9, //0x000005e8 testl        %ecx, %ecx
	0x0f, 0x85, 0x70, 0x2f, 0x00, 0x00, //0x000005ea jne          LBB0_647
	0x48, 0x83, 0xc2, 0x10, //0x000005f0 addq         $16, %rdx
	0x48, 0x83, 0xc6, 0xf0, //0x000005f4 addq         $-16, %rsi
	0x48, 0x83, 0xc7, 0xf0, //0x000005f8 addq         $-16, %rdi
	0x48, 0x83, 0xfe, 0x0f, //0x000005fc cmpq         $15, %rsi
	0x0f, 0x87, 0xca, 0xff, 0xff, 0xff, //0x00000600 ja           LBB0_41
	//0x00000606 LBB0_43
	0x48, 0x85, 0xf6, //0x00000606 testq        %rsi, %rsi
	0x0f, 0x84, 0x35, 0x00, 0x00, 0x00, //0x00000609 je           LBB0_50
	0x48, 0x8d, 0x1c, 0x32, //0x0000060f leaq         (%rdx,%rsi), %rbx
	0x31, 0xff, //0x00000613 xorl         %edi, %edi
	//0x00000615 LBB0_45
	0x0f, 0xb6, 0x0c, 0x3a, //0x00000615 movzbl       (%rdx,%rdi), %ecx
	0x80, 0xf9, 0x2c, //0x00000619 cmpb         $44, %cl
	0x0f, 0x84, 0xd6, 0x30, 0x00, 0x00, //0x0000061c je           LBB0_668
	0x80, 0xf9, 0x7d, //0x00000622 cmpb         $125, %cl
	0x0f, 0x84, 0xcd, 0x30, 0x00, 0x00, //0x00000625 je           LBB0_668
	0x80, 0xf9, 0x5d, //0x0000062b cmpb         $93, %cl
	0x0f, 0x84, 0xc4, 0x30, 0x00, 0x00, //0x0000062e je           LBB0_668
	0x48, 0x83, 0xc7, 0x01, //0x00000634 addq         $1, %rdi
	0x48, 0x39, 0xfe, //0x00000638 cmpq         %rdi, %rsi
	0x0f, 0x85, 0xd4, 0xff, 0xff, 0xff, //0x0000063b jne          LBB0_45
	0x48, 0x89, 0xda, //0x00000641 movq         %rbx, %rdx
	//0x00000644 LBB0_50
	0x4c, 0x29, 0xd2, //0x00000644 subq         %r10, %rdx
	0xe9, 0xb2, 0x30, 0x00, 0x00, //0x00000647 jmp          LBB0_669
	//0x0000064c LBB0_51
	0x0f, 0xbc, 0xc9, //0x0000064c bsfl         %ecx, %ecx
	0x48, 0x29, 0xf1, //0x0000064f subq         %rsi, %rcx
	//0x00000652 LBB0_52
	0x49, 0x89, 0x0b, //0x00000652 movq         %rcx, (%r11)
	0x49, 0x89, 0xc7, //0x00000655 movq         %rax, %r15
	0xe9, 0x7b, 0x2e, 0x00, 0x00, //0x00000658 jmp          LBB0_638
	//0x0000065d LBB0_634
	0x49, 0xf7, 0xd8, //0x0000065d negq         %r8
	0x4c, 0x8b, 0x5c, 0x24, 0x08, //0x00000660 movq         $8(%rsp), %r11
	0xc5, 0xfe, 0x6f, 0x2d, 0x93, 0xf9, 0xff, 0xff, //0x00000665 vmovdqu      $-1645(%rip), %ymm5  /* LCPI0_0+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0x0b, 0xfa, 0xff, 0xff, //0x0000066d vmovdqu      $-1525(%rip), %ymm6  /* LCPI0_7+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0x23, 0xfa, 0xff, 0xff, //0x00000675 vmovdqu      $-1501(%rip), %ymm7  /* LCPI0_8+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0x9b, 0xfa, 0xff, 0xff, //0x0000067d vmovdqu      $-1381(%rip), %ymm8  /* LCPI0_13+0(%rip) */
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00000685 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0x7e, 0x6f, 0x15, 0xae, 0xfa, 0xff, 0xff, //0x0000068a vmovdqu      $-1362(%rip), %ymm10  /* LCPI0_14+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0xc6, 0xfa, 0xff, 0xff, //0x00000692 vmovdqu      $-1338(%rip), %ymm11  /* LCPI0_15+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0xde, 0xfa, 0xff, 0xff, //0x0000069a vmovdqu      $-1314(%rip), %ymm12  /* LCPI0_16+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0xf6, 0xfa, 0xff, 0xff, //0x000006a2 vmovdqu      $-1290(%rip), %ymm13  /* LCPI0_17+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0x8e, 0xf9, 0xff, 0xff, //0x000006aa vmovdqu      $-1650(%rip), %ymm14  /* LCPI0_2+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x3d, 0x06, 0xfb, 0xff, 0xff, //0x000006b2 vmovdqu      $-1274(%rip), %ymm15  /* LCPI0_18+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0x1e, 0xfb, 0xff, 0xff, //0x000006ba vmovdqu      $-1250(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0x4d, 0x85, 0xc0, //0x000006c2 testq        %r8, %r8
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x000006c5 movq         $24(%rsp), %r10
	0x0f, 0x88, 0xec, 0x2d, 0x00, 0x00, //0x000006ca js           LBB0_635
	//0x000006d0 LBB0_408
	0x49, 0x8b, 0x0b, //0x000006d0 movq         (%r11), %rcx
	0x48, 0x83, 0xc1, 0xff, //0x000006d3 addq         $-1, %rcx
	//0x000006d7 LBB0_54
	0x4c, 0x01, 0xc1, //0x000006d7 addq         %r8, %rcx
	0x49, 0x89, 0x0b, //0x000006da movq         %rcx, (%r11)
	0x48, 0x83, 0x7c, 0x24, 0x28, 0x00, //0x000006dd cmpq         $0, $40(%rsp)
	0x4c, 0x8b, 0x6c, 0x24, 0x38, //0x000006e3 movq         $56(%rsp), %r13
	0x0f, 0x8e, 0xea, 0x2d, 0x00, 0x00, //0x000006e8 jle          LBB0_638
	0x90, 0x90, //0x000006ee .p2align 4, 0x90
	//0x000006f0 LBB0_55
	0x49, 0x8b, 0x11, //0x000006f0 movq         (%r9), %rdx
	0x4d, 0x89, 0xf7, //0x000006f3 movq         %r14, %r15
	0x48, 0x85, 0xd2, //0x000006f6 testq        %rdx, %rdx
	0x0f, 0x85, 0x11, 0x00, 0x00, 0x00, //0x000006f9 jne          LBB0_57
	0xe9, 0xd4, 0x2d, 0x00, 0x00, //0x000006ff jmp          LBB0_638
	//0x00000704 LBB0_53
	0x4c, 0x89, 0xf9, //0x00000704 movq         %r15, %rcx
	0xe9, 0xcb, 0xff, 0xff, 0xff, //0x00000707 jmp          LBB0_54
	0x90, 0x90, 0x90, 0x90, //0x0000070c .p2align 4, 0x90
	//0x00000710 LBB0_57
	0x4c, 0x89, 0xf1, //0x00000710 movq         %r14, %rcx
	0x4d, 0x8b, 0x45, 0x00, //0x00000713 movq         (%r13), %r8
	0x49, 0x8b, 0x75, 0x08, //0x00000717 movq         $8(%r13), %rsi
	0x49, 0x8b, 0x3b, //0x0000071b movq         (%r11), %rdi
	0x48, 0x39, 0xf7, //0x0000071e cmpq         %rsi, %rdi
	0x4c, 0x89, 0x44, 0x24, 0x10, //0x00000721 movq         %r8, $16(%rsp)
	0x0f, 0x83, 0x34, 0x00, 0x00, 0x00, //0x00000726 jae          LBB0_62
	0x41, 0x8a, 0x04, 0x38, //0x0000072c movb         (%r8,%rdi), %al
	0x3c, 0x0d, //0x00000730 cmpb         $13, %al
	0x0f, 0x84, 0x28, 0x00, 0x00, 0x00, //0x00000732 je           LBB0_62
	0x3c, 0x20, //0x00000738 cmpb         $32, %al
	0x0f, 0x84, 0x20, 0x00, 0x00, 0x00, //0x0000073a je           LBB0_62
	0x04, 0xf7, //0x00000740 addb         $-9, %al
	0x3c, 0x01, //0x00000742 cmpb         $1, %al
	0x0f, 0x86, 0x16, 0x00, 0x00, 0x00, //0x00000744 jbe          LBB0_62
	0x49, 0x89, 0xfe, //0x0000074a movq         %rdi, %r14
	0xe9, 0x80, 0x01, 0x00, 0x00, //0x0000074d jmp          LBB0_87
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000752 .p2align 4, 0x90
	//0x00000760 LBB0_62
	0x4c, 0x8d, 0x77, 0x01, //0x00000760 leaq         $1(%rdi), %r14
	0x49, 0x39, 0xf6, //0x00000764 cmpq         %rsi, %r14
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000767 jae          LBB0_66
	0x43, 0x8a, 0x14, 0x30, //0x0000076d movb         (%r8,%r14), %dl
	0x80, 0xfa, 0x0d, //0x00000771 cmpb         $13, %dl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00000774 je           LBB0_66
	0x80, 0xfa, 0x20, //0x0000077a cmpb         $32, %dl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000077d je           LBB0_66
	0x80, 0xc2, 0xf7, //0x00000783 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x00000786 cmpb         $1, %dl
	0x0f, 0x87, 0x43, 0x01, 0x00, 0x00, //0x00000789 ja           LBB0_87
	0x90, //0x0000078f .p2align 4, 0x90
	//0x00000790 LBB0_66
	0x4c, 0x8d, 0x77, 0x02, //0x00000790 leaq         $2(%rdi), %r14
	0x49, 0x39, 0xf6, //0x00000794 cmpq         %rsi, %r14
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000797 jae          LBB0_70
	0x43, 0x8a, 0x14, 0x30, //0x0000079d movb         (%r8,%r14), %dl
	0x80, 0xfa, 0x0d, //0x000007a1 cmpb         $13, %dl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x000007a4 je           LBB0_70
	0x80, 0xfa, 0x20, //0x000007aa cmpb         $32, %dl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x000007ad je           LBB0_70
	0x80, 0xc2, 0xf7, //0x000007b3 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x000007b6 cmpb         $1, %dl
	0x0f, 0x87, 0x13, 0x01, 0x00, 0x00, //0x000007b9 ja           LBB0_87
	0x90, //0x000007bf .p2align 4, 0x90
	//0x000007c0 LBB0_70
	0x4c, 0x8d, 0x77, 0x03, //0x000007c0 leaq         $3(%rdi), %r14
	0x49, 0x39, 0xf6, //0x000007c4 cmpq         %rsi, %r14
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x000007c7 jae          LBB0_74
	0x43, 0x8a, 0x14, 0x30, //0x000007cd movb         (%r8,%r14), %dl
	0x80, 0xfa, 0x0d, //0x000007d1 cmpb         $13, %dl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x000007d4 je           LBB0_74
	0x80, 0xfa, 0x20, //0x000007da cmpb         $32, %dl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x000007dd je           LBB0_74
	0x80, 0xc2, 0xf7, //0x000007e3 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x000007e6 cmpb         $1, %dl
	0x0f, 0x87, 0xe3, 0x00, 0x00, 0x00, //0x000007e9 ja           LBB0_87
	0x90, //0x000007ef .p2align 4, 0x90
	//0x000007f0 LBB0_74
	0x4c, 0x8d, 0x77, 0x04, //0x000007f0 leaq         $4(%rdi), %r14
	0x48, 0x89, 0xf2, //0x000007f4 movq         %rsi, %rdx
	0x4c, 0x29, 0xf2, //0x000007f7 subq         %r14, %rdx
	0x0f, 0x86, 0x86, 0x25, 0x00, 0x00, //0x000007fa jbe          LBB0_566
	0x48, 0x83, 0xfa, 0x20, //0x00000800 cmpq         $32, %rdx
	0x0f, 0x82, 0x58, 0x18, 0x00, 0x00, //0x00000804 jb           LBB0_410
	0x48, 0xc7, 0xc2, 0xfc, 0xff, 0xff, 0xff, //0x0000080a movq         $-4, %rdx
	0x48, 0x29, 0xfa, //0x00000811 subq         %rdi, %rdx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000814 .p2align 4, 0x90
	//0x00000820 LBB0_77
	0xc4, 0x81, 0x7e, 0x6f, 0x04, 0x30, //0x00000820 vmovdqu      (%r8,%r14), %ymm0
	0xc4, 0xe2, 0x55, 0x00, 0xc8, //0x00000826 vpshufb      %ymm0, %ymm5, %ymm1
	0xc5, 0xfd, 0x74, 0xc1, //0x0000082b vpcmpeqb     %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf8, //0x0000082f vpmovmskb    %ymm0, %edi
	0x83, 0xff, 0xff, //0x00000833 cmpl         $-1, %edi
	0x0f, 0x85, 0x84, 0x00, 0x00, 0x00, //0x00000836 jne          LBB0_86
	0x49, 0x83, 0xc6, 0x20, //0x0000083c addq         $32, %r14
	0x48, 0x8d, 0x3c, 0x16, //0x00000840 leaq         (%rsi,%rdx), %rdi
	0x48, 0x83, 0xc7, 0xe0, //0x00000844 addq         $-32, %rdi
	0x48, 0x83, 0xc2, 0xe0, //0x00000848 addq         $-32, %rdx
	0x48, 0x83, 0xff, 0x1f, //0x0000084c cmpq         $31, %rdi
	0x0f, 0x87, 0xca, 0xff, 0xff, 0xff, //0x00000850 ja           LBB0_77
	0x4d, 0x89, 0xc6, //0x00000856 movq         %r8, %r14
	0x49, 0x29, 0xd6, //0x00000859 subq         %rdx, %r14
	0x48, 0x01, 0xf2, //0x0000085c addq         %rsi, %rdx
	0x48, 0x85, 0xd2, //0x0000085f testq        %rdx, %rdx
	0x0f, 0x84, 0x3f, 0x00, 0x00, 0x00, //0x00000862 je           LBB0_85
	//0x00000868 LBB0_80
	0x4d, 0x8d, 0x04, 0x16, //0x00000868 leaq         (%r14,%rdx), %r8
	0x31, 0xff, //0x0000086c xorl         %edi, %edi
	0x90, 0x90, //0x0000086e .p2align 4, 0x90
	//0x00000870 LBB0_81
	0x41, 0x0f, 0xbe, 0x1c, 0x3e, //0x00000870 movsbl       (%r14,%rdi), %ebx
	0x83, 0xfb, 0x20, //0x00000875 cmpl         $32, %ebx
	0x0f, 0x87, 0x1d, 0x16, 0x00, 0x00, //0x00000878 ja           LBB0_401
	0x48, 0xb8, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x0000087e movabsq      $4294977024, %rax
	0x48, 0x0f, 0xa3, 0xd8, //0x00000888 btq          %rbx, %rax
	0x0f, 0x83, 0x09, 0x16, 0x00, 0x00, //0x0000088c jae          LBB0_401
	0x48, 0x83, 0xc7, 0x01, //0x00000892 addq         $1, %rdi
	0x48, 0x39, 0xfa, //0x00000896 cmpq         %rdi, %rdx
	0x0f, 0x85, 0xd1, 0xff, 0xff, 0xff, //0x00000899 jne          LBB0_81
	0x4d, 0x89, 0xc6, //0x0000089f movq         %r8, %r14
	0x4c, 0x8b, 0x44, 0x24, 0x10, //0x000008a2 movq         $16(%rsp), %r8
	//0x000008a7 LBB0_85
	0x4d, 0x29, 0xc6, //0x000008a7 subq         %r8, %r14
	0x49, 0x39, 0xf6, //0x000008aa cmpq         %rsi, %r14
	0x0f, 0x82, 0x1f, 0x00, 0x00, 0x00, //0x000008ad jb           LBB0_87
	0xe9, 0xd1, 0x24, 0x00, 0x00, //0x000008b3 jmp          LBB0_567
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000008b8 .p2align 4, 0x90
	//0x000008c0 LBB0_86
	0xf7, 0xd7, //0x000008c0 notl         %edi
	0x44, 0x0f, 0xbc, 0xf7, //0x000008c2 bsfl         %edi, %r14d
	0x49, 0x29, 0xd6, //0x000008c6 subq         %rdx, %r14
	0x49, 0x39, 0xf6, //0x000008c9 cmpq         %rsi, %r14
	0x0f, 0x83, 0xb7, 0x24, 0x00, 0x00, //0x000008cc jae          LBB0_567
	//0x000008d2 LBB0_87
	0x49, 0x8d, 0x56, 0x01, //0x000008d2 leaq         $1(%r14), %rdx
	0x49, 0x89, 0x13, //0x000008d6 movq         %rdx, (%r11)
	0x43, 0x0f, 0xbe, 0x3c, 0x30, //0x000008d9 movsbl       (%r8,%r14), %edi
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x000008de movq         $-1, %r15
	0x85, 0xff, //0x000008e5 testl        %edi, %edi
	0x0f, 0x84, 0xeb, 0x2b, 0x00, 0x00, //0x000008e7 je           LBB0_638
	0x49, 0x8b, 0x31, //0x000008ed movq         (%r9), %rsi
	0x48, 0x8d, 0x56, 0xff, //0x000008f0 leaq         $-1(%rsi), %rdx
	0x41, 0x8b, 0x1c, 0xf1, //0x000008f4 movl         (%r9,%rsi,8), %ebx
	0x48, 0x83, 0xf9, 0xff, //0x000008f8 cmpq         $-1, %rcx
	0x4c, 0x0f, 0x45, 0xf1, //0x000008fc cmovneq      %rcx, %r14
	0x83, 0xc3, 0xff, //0x00000900 addl         $-1, %ebx
	0x83, 0xfb, 0x05, //0x00000903 cmpl         $5, %ebx
	0x0f, 0x87, 0xeb, 0x01, 0x00, 0x00, //0x00000906 ja           LBB0_119
	0x48, 0x8d, 0x05, 0x79, 0x30, 0x00, 0x00, //0x0000090c leaq         $12409(%rip), %rax  /* LJTI0_1+0(%rip) */
	0x48, 0x63, 0x0c, 0x98, //0x00000913 movslq       (%rax,%rbx,4), %rcx
	0x48, 0x01, 0xc1, //0x00000917 addq         %rax, %rcx
	0xff, 0xe1, //0x0000091a jmpq         *%rcx
	//0x0000091c LBB0_90
	0x83, 0xff, 0x2c, //0x0000091c cmpl         $44, %edi
	0x0f, 0x84, 0x4f, 0x06, 0x00, 0x00, //0x0000091f je           LBB0_189
	0x83, 0xff, 0x5d, //0x00000925 cmpl         $93, %edi
	0x0f, 0x84, 0x32, 0x06, 0x00, 0x00, //0x00000928 je           LBB0_92
	0xe9, 0x9e, 0x2b, 0x00, 0x00, //0x0000092e jmp          LBB0_637
	//0x00000933 LBB0_93
	0x40, 0x80, 0xff, 0x5d, //0x00000933 cmpb         $93, %dil
	0x0f, 0x84, 0x23, 0x06, 0x00, 0x00, //0x00000937 je           LBB0_92
	0x49, 0xc7, 0x04, 0xf1, 0x01, 0x00, 0x00, 0x00, //0x0000093d movq         $1, (%r9,%rsi,8)
	0x83, 0xff, 0x7b, //0x00000945 cmpl         $123, %edi
	0x0f, 0x86, 0xb5, 0x01, 0x00, 0x00, //0x00000948 jbe          LBB0_95
	0xe9, 0x7e, 0x2b, 0x00, 0x00, //0x0000094e jmp          LBB0_637
	//0x00000953 LBB0_96
	0x40, 0x80, 0xff, 0x22, //0x00000953 cmpb         $34, %dil
	0x0f, 0x85, 0x74, 0x2b, 0x00, 0x00, //0x00000957 jne          LBB0_637
	0x49, 0xc7, 0x04, 0xf1, 0x04, 0x00, 0x00, 0x00, //0x0000095d movq         $4, (%r9,%rsi,8)
	0x4d, 0x8b, 0x03, //0x00000965 movq         (%r11), %r8
	0x4d, 0x8b, 0x65, 0x08, //0x00000968 movq         $8(%r13), %r12
	0x41, 0xf6, 0xc2, 0x20, //0x0000096c testb        $32, %r10b
	0x0f, 0x85, 0x7f, 0x07, 0x00, 0x00, //0x00000970 jne          LBB0_198
	0x4c, 0x89, 0xe3, //0x00000976 movq         %r12, %rbx
	0x4c, 0x29, 0xc3, //0x00000979 subq         %r8, %rbx
	0x0f, 0x84, 0x26, 0x2d, 0x00, 0x00, //0x0000097c je           LBB0_670
	0x48, 0x83, 0xfb, 0x40, //0x00000982 cmpq         $64, %rbx
	0x0f, 0x82, 0x94, 0x18, 0x00, 0x00, //0x00000986 jb           LBB0_427
	0x4c, 0x89, 0xc6, //0x0000098c movq         %r8, %rsi
	0x48, 0xf7, 0xd6, //0x0000098f notq         %rsi
	0x48, 0xc7, 0x04, 0x24, 0xff, 0xff, 0xff, 0xff, //0x00000992 movq         $-1, (%rsp)
	0x4d, 0x89, 0xc7, //0x0000099a movq         %r8, %r15
	0x45, 0x31, 0xd2, //0x0000099d xorl         %r10d, %r10d
	//0x000009a0 .p2align 4, 0x90
	//0x000009a0 LBB0_101
	0x48, 0x8b, 0x44, 0x24, 0x10, //0x000009a0 movq         $16(%rsp), %rax
	0xc4, 0xa1, 0x7e, 0x6f, 0x04, 0x38, //0x000009a5 vmovdqu      (%rax,%r15), %ymm0
	0xc4, 0xa1, 0x7e, 0x6f, 0x4c, 0x38, 0x20, //0x000009ab vmovdqu      $32(%rax,%r15), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x000009b2 vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xfa, //0x000009b6 vpmovmskb    %ymm2, %edi
	0xc5, 0xf5, 0x74, 0xd6, //0x000009ba vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xca, //0x000009be vpmovmskb    %ymm2, %ecx
	0xc5, 0xfd, 0x74, 0xc7, //0x000009c2 vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xd0, //0x000009c6 vpmovmskb    %ymm0, %edx
	0xc5, 0xf5, 0x74, 0xc7, //0x000009ca vpcmpeqb     %ymm7, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xc0, //0x000009ce vpmovmskb    %ymm0, %eax
	0x48, 0xc1, 0xe1, 0x20, //0x000009d2 shlq         $32, %rcx
	0x48, 0x09, 0xcf, //0x000009d6 orq          %rcx, %rdi
	0x48, 0xc1, 0xe0, 0x20, //0x000009d9 shlq         $32, %rax
	0x48, 0x09, 0xc2, //0x000009dd orq          %rax, %rdx
	0x0f, 0x85, 0x30, 0x00, 0x00, 0x00, //0x000009e0 jne          LBB0_110
	0x4d, 0x85, 0xd2, //0x000009e6 testq        %r10, %r10
	0x0f, 0x85, 0x4d, 0x00, 0x00, 0x00, //0x000009e9 jne          LBB0_112
	0x45, 0x31, 0xd2, //0x000009ef xorl         %r10d, %r10d
	0x48, 0x85, 0xff, //0x000009f2 testq        %rdi, %rdi
	0x0f, 0x85, 0xa9, 0x00, 0x00, 0x00, //0x000009f5 jne          LBB0_114
	//0x000009fb LBB0_104
	0x48, 0x83, 0xc3, 0xc0, //0x000009fb addq         $-64, %rbx
	0x48, 0x83, 0xc6, 0xc0, //0x000009ff addq         $-64, %rsi
	0x49, 0x83, 0xc7, 0x40, //0x00000a03 addq         $64, %r15
	0x48, 0x83, 0xfb, 0x3f, //0x00000a07 cmpq         $63, %rbx
	0x0f, 0x87, 0x8f, 0xff, 0xff, 0xff, //0x00000a0b ja           LBB0_101
	0xe9, 0xab, 0x14, 0x00, 0x00, //0x00000a11 jmp          LBB0_105
	//0x00000a16 LBB0_110
	0x4c, 0x89, 0x4c, 0x24, 0x20, //0x00000a16 movq         %r9, $32(%rsp)
	0x4d, 0x89, 0xe9, //0x00000a1b movq         %r13, %r9
	0x4d, 0x89, 0xdd, //0x00000a1e movq         %r11, %r13
	0x48, 0x83, 0x3c, 0x24, 0xff, //0x00000a21 cmpq         $-1, (%rsp)
	0x0f, 0x85, 0x1b, 0x00, 0x00, 0x00, //0x00000a26 jne          LBB0_113
	0x48, 0x0f, 0xbc, 0xc2, //0x00000a2c bsfq         %rdx, %rax
	0x4c, 0x01, 0xf8, //0x00000a30 addq         %r15, %rax
	0x48, 0x89, 0x04, 0x24, //0x00000a33 movq         %rax, (%rsp)
	0xe9, 0x0b, 0x00, 0x00, 0x00, //0x00000a37 jmp          LBB0_113
	//0x00000a3c LBB0_112
	0x4c, 0x89, 0x4c, 0x24, 0x20, //0x00000a3c movq         %r9, $32(%rsp)
	0x4d, 0x89, 0xe9, //0x00000a41 movq         %r13, %r9
	0x4d, 0x89, 0xdd, //0x00000a44 movq         %r11, %r13
	//0x00000a47 LBB0_113
	0x4c, 0x89, 0xd0, //0x00000a47 movq         %r10, %rax
	0x48, 0xf7, 0xd0, //0x00000a4a notq         %rax
	0x48, 0x21, 0xd0, //0x00000a4d andq         %rdx, %rax
	0x4c, 0x8d, 0x1c, 0x00, //0x00000a50 leaq         (%rax,%rax), %r11
	0x4d, 0x09, 0xd3, //0x00000a54 orq          %r10, %r11
	0x4c, 0x89, 0xd9, //0x00000a57 movq         %r11, %rcx
	0x48, 0xf7, 0xd1, //0x00000a5a notq         %rcx
	0x48, 0x21, 0xd1, //0x00000a5d andq         %rdx, %rcx
	0x48, 0xba, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000a60 movabsq      $-6148914691236517206, %rdx
	0x48, 0x21, 0xd1, //0x00000a6a andq         %rdx, %rcx
	0x45, 0x31, 0xd2, //0x00000a6d xorl         %r10d, %r10d
	0x48, 0x01, 0xc1, //0x00000a70 addq         %rax, %rcx
	0x41, 0x0f, 0x92, 0xc2, //0x00000a73 setb         %r10b
	0x48, 0x01, 0xc9, //0x00000a77 addq         %rcx, %rcx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000a7a movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc1, //0x00000a84 xorq         %rax, %rcx
	0x4c, 0x21, 0xd9, //0x00000a87 andq         %r11, %rcx
	0x48, 0xf7, 0xd1, //0x00000a8a notq         %rcx
	0x48, 0x21, 0xcf, //0x00000a8d andq         %rcx, %rdi
	0x4d, 0x89, 0xeb, //0x00000a90 movq         %r13, %r11
	0x4d, 0x89, 0xcd, //0x00000a93 movq         %r9, %r13
	0x4c, 0x8b, 0x4c, 0x24, 0x20, //0x00000a96 movq         $32(%rsp), %r9
	0x48, 0x85, 0xff, //0x00000a9b testq        %rdi, %rdi
	0x0f, 0x84, 0x57, 0xff, 0xff, 0xff, //0x00000a9e je           LBB0_104
	//0x00000aa4 LBB0_114
	0x4c, 0x0f, 0xbc, 0xff, //0x00000aa4 bsfq         %rdi, %r15
	0x49, 0x29, 0xf7, //0x00000aa8 subq         %rsi, %r15
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x00000aab movq         $24(%rsp), %r10
	0xe9, 0x3c, 0x09, 0x00, 0x00, //0x00000ab0 jmp          LBB0_240
	//0x00000ab5 LBB0_115
	0x40, 0x80, 0xff, 0x3a, //0x00000ab5 cmpb         $58, %dil
	0x0f, 0x85, 0x12, 0x2a, 0x00, 0x00, //0x00000ab9 jne          LBB0_637
	0x49, 0xc7, 0x04, 0xf1, 0x00, 0x00, 0x00, 0x00, //0x00000abf movq         $0, (%r9,%rsi,8)
	0xe9, 0x24, 0xfc, 0xff, 0xff, //0x00000ac7 jmp          LBB0_55
	//0x00000acc LBB0_117
	0x83, 0xff, 0x2c, //0x00000acc cmpl         $44, %edi
	0x0f, 0x85, 0x82, 0x04, 0x00, 0x00, //0x00000acf jne          LBB0_118
	0x48, 0x81, 0xfe, 0xff, 0x0f, 0x00, 0x00, //0x00000ad5 cmpq         $4095, %rsi
	0x0f, 0x8f, 0xc6, 0x22, 0x00, 0x00, //0x00000adc jg           LBB0_650
	0x48, 0x8d, 0x4e, 0x01, //0x00000ae2 leaq         $1(%rsi), %rcx
	0x49, 0x89, 0x09, //0x00000ae6 movq         %rcx, (%r9)
	0x49, 0xc7, 0x44, 0xf1, 0x08, 0x03, 0x00, 0x00, 0x00, //0x00000ae9 movq         $3, $8(%r9,%rsi,8)
	0xe9, 0xf9, 0xfb, 0xff, 0xff, //0x00000af2 jmp          LBB0_55
	//0x00000af7 LBB0_119
	0x49, 0x89, 0x11, //0x00000af7 movq         %rdx, (%r9)
	0x83, 0xff, 0x7b, //0x00000afa cmpl         $123, %edi
	0x0f, 0x87, 0xce, 0x29, 0x00, 0x00, //0x00000afd ja           LBB0_637
	//0x00000b03 LBB0_95
	0x89, 0xf8, //0x00000b03 movl         %edi, %eax
	0x48, 0x8d, 0x0d, 0x98, 0x2e, 0x00, 0x00, //0x00000b05 leaq         $11928(%rip), %rcx  /* LJTI0_2+0(%rip) */
	0x48, 0x63, 0x04, 0x81, //0x00000b0c movslq       (%rcx,%rax,4), %rax
	0x48, 0x01, 0xc8, //0x00000b10 addq         %rcx, %rax
	0xff, 0xe0, //0x00000b13 jmpq         *%rax
	//0x00000b15 LBB0_134
	0x4c, 0x89, 0x6c, 0x24, 0x38, //0x00000b15 movq         %r13, $56(%rsp)
	0x4d, 0x8b, 0x6d, 0x08, //0x00000b1a movq         $8(%r13), %r13
	0x49, 0x8b, 0x03, //0x00000b1e movq         (%r11), %rax
	0x4c, 0x8d, 0x78, 0xff, //0x00000b21 leaq         $-1(%rax), %r15
	0x4d, 0x29, 0xfd, //0x00000b25 subq         %r15, %r13
	0x0f, 0x84, 0x82, 0x29, 0x00, 0x00, //0x00000b28 je           LBB0_633
	0x48, 0x89, 0x44, 0x24, 0x28, //0x00000b2e movq         %rax, $40(%rsp)
	0x4d, 0x8d, 0x24, 0x00, //0x00000b33 leaq         (%r8,%rax), %r12
	0x49, 0x83, 0xc4, 0xff, //0x00000b37 addq         $-1, %r12
	0x41, 0x80, 0x3c, 0x24, 0x30, //0x00000b3b cmpb         $48, (%r12)
	0x0f, 0x85, 0x43, 0x00, 0x00, 0x00, //0x00000b40 jne          LBB0_139
	0x41, 0xb8, 0x01, 0x00, 0x00, 0x00, //0x00000b46 movl         $1, %r8d
	0x49, 0x83, 0xfd, 0x01, //0x00000b4c cmpq         $1, %r13
	0x0f, 0x84, 0xae, 0xfb, 0xff, 0xff, //0x00000b50 je           LBB0_53
	0x48, 0x8b, 0x44, 0x24, 0x10, //0x00000b56 movq         $16(%rsp), %rax
	0x48, 0x8b, 0x4c, 0x24, 0x28, //0x00000b5b movq         $40(%rsp), %rcx
	0x8a, 0x0c, 0x08, //0x00000b60 movb         (%rax,%rcx), %cl
	0x80, 0xc1, 0xd2, //0x00000b63 addb         $-46, %cl
	0x80, 0xf9, 0x37, //0x00000b66 cmpb         $55, %cl
	0x0f, 0x87, 0x95, 0xfb, 0xff, 0xff, //0x00000b69 ja           LBB0_53
	0x0f, 0xb6, 0xc1, //0x00000b6f movzbl       %cl, %eax
	0x48, 0xb9, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x00000b72 movabsq      $36028797027352577, %rcx
	0x48, 0x0f, 0xa3, 0xc1, //0x00000b7c btq          %rax, %rcx
	0x4c, 0x89, 0xf9, //0x00000b80 movq         %r15, %rcx
	0x0f, 0x83, 0x4e, 0xfb, 0xff, 0xff, //0x00000b83 jae          LBB0_54
	//0x00000b89 LBB0_139
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00000b89 movq         $-1, %r11
	0x49, 0x83, 0xfd, 0x20, //0x00000b90 cmpq         $32, %r13
	0x0f, 0x82, 0x29, 0x16, 0x00, 0x00, //0x00000b94 jb           LBB0_419
	0x45, 0x31, 0xc0, //0x00000b9a xorl         %r8d, %r8d
	0x49, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00000b9d movq         $-1, %r10
	0x48, 0xc7, 0x04, 0x24, 0xff, 0xff, 0xff, 0xff, //0x00000ba4 movq         $-1, (%rsp)
	0x90, 0x90, 0x90, 0x90, //0x00000bac .p2align 4, 0x90
	//0x00000bb0 LBB0_141
	0xc4, 0x81, 0x7e, 0x6f, 0x04, 0x04, //0x00000bb0 vmovdqu      (%r12,%r8), %ymm0
	0xc4, 0xc1, 0x7d, 0x64, 0xca, //0x00000bb6 vpcmpgtb     %ymm10, %ymm0, %ymm1
	0xc5, 0xa5, 0x64, 0xd0, //0x00000bbb vpcmpgtb     %ymm0, %ymm11, %ymm2
	0xc5, 0xed, 0xdb, 0xc9, //0x00000bbf vpand        %ymm1, %ymm2, %ymm1
	0xc5, 0x9d, 0x74, 0xd0, //0x00000bc3 vpcmpeqb     %ymm0, %ymm12, %ymm2
	0xc5, 0x95, 0x74, 0xd8, //0x00000bc7 vpcmpeqb     %ymm0, %ymm13, %ymm3
	0xc5, 0xe5, 0xeb, 0xd2, //0x00000bcb vpor         %ymm2, %ymm3, %ymm2
	0xc5, 0x8d, 0xdb, 0xd8, //0x00000bcf vpand        %ymm0, %ymm14, %ymm3
	0xc5, 0x85, 0x74, 0xc0, //0x00000bd3 vpcmpeqb     %ymm0, %ymm15, %ymm0
	0xc5, 0xfd, 0xd7, 0xd0, //0x00000bd7 vpmovmskb    %ymm0, %edx
	0xc5, 0xe5, 0x74, 0xdc, //0x00000bdb vpcmpeqb     %ymm4, %ymm3, %ymm3
	0xc5, 0xfd, 0xd7, 0xfb, //0x00000bdf vpmovmskb    %ymm3, %edi
	0xc5, 0xfd, 0xd7, 0xf2, //0x00000be3 vpmovmskb    %ymm2, %esi
	0xc5, 0xf5, 0xeb, 0xc0, //0x00000be7 vpor         %ymm0, %ymm1, %ymm0
	0xc5, 0xe5, 0xeb, 0xca, //0x00000beb vpor         %ymm2, %ymm3, %ymm1
	0xc5, 0xfd, 0xeb, 0xc1, //0x00000bef vpor         %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xc0, //0x00000bf3 vpmovmskb    %ymm0, %eax
	0x48, 0xf7, 0xd0, //0x00000bf7 notq         %rax
	0x48, 0x0f, 0xbc, 0xc8, //0x00000bfa bsfq         %rax, %rcx
	0x83, 0xf9, 0x20, //0x00000bfe cmpl         $32, %ecx
	0x0f, 0x84, 0x11, 0x00, 0x00, 0x00, //0x00000c01 je           LBB0_143
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x00000c07 movl         $-1, %eax
	0xd3, 0xe0, //0x00000c0c shll         %cl, %eax
	0xf7, 0xd0, //0x00000c0e notl         %eax
	0x21, 0xc2, //0x00000c10 andl         %eax, %edx
	0x21, 0xc7, //0x00000c12 andl         %eax, %edi
	0x21, 0xf0, //0x00000c14 andl         %esi, %eax
	0x89, 0xc6, //0x00000c16 movl         %eax, %esi
	//0x00000c18 LBB0_143
	0x8d, 0x5a, 0xff, //0x00000c18 leal         $-1(%rdx), %ebx
	0x21, 0xd3, //0x00000c1b andl         %edx, %ebx
	0x0f, 0x85, 0xf1, 0x10, 0x00, 0x00, //0x00000c1d jne          LBB0_383
	0x8d, 0x5f, 0xff, //0x00000c23 leal         $-1(%rdi), %ebx
	0x21, 0xfb, //0x00000c26 andl         %edi, %ebx
	0x0f, 0x85, 0xe6, 0x10, 0x00, 0x00, //0x00000c28 jne          LBB0_383
	0x8d, 0x5e, 0xff, //0x00000c2e leal         $-1(%rsi), %ebx
	0x21, 0xf3, //0x00000c31 andl         %esi, %ebx
	0x0f, 0x85, 0xdb, 0x10, 0x00, 0x00, //0x00000c33 jne          LBB0_383
	0x85, 0xd2, //0x00000c39 testl        %edx, %edx
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x00000c3b je           LBB0_149
	0x0f, 0xbc, 0xd2, //0x00000c41 bsfl         %edx, %edx
	0x48, 0x83, 0x3c, 0x24, 0xff, //0x00000c44 cmpq         $-1, (%rsp)
	0x0f, 0x85, 0x65, 0x12, 0x00, 0x00, //0x00000c49 jne          LBB0_402
	0x4c, 0x01, 0xc2, //0x00000c4f addq         %r8, %rdx
	0x48, 0x89, 0x14, 0x24, //0x00000c52 movq         %rdx, (%rsp)
	//0x00000c56 LBB0_149
	0x85, 0xff, //0x00000c56 testl        %edi, %edi
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x00000c58 je           LBB0_152
	0x0f, 0xbc, 0xd7, //0x00000c5e bsfl         %edi, %edx
	0x49, 0x83, 0xfa, 0xff, //0x00000c61 cmpq         $-1, %r10
	0x0f, 0x85, 0x49, 0x12, 0x00, 0x00, //0x00000c65 jne          LBB0_402
	0x4c, 0x01, 0xc2, //0x00000c6b addq         %r8, %rdx
	0x49, 0x89, 0xd2, //0x00000c6e movq         %rdx, %r10
	//0x00000c71 LBB0_152
	0x85, 0xf6, //0x00000c71 testl        %esi, %esi
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x00000c73 je           LBB0_155
	0x0f, 0xbc, 0xd6, //0x00000c79 bsfl         %esi, %edx
	0x49, 0x83, 0xfb, 0xff, //0x00000c7c cmpq         $-1, %r11
	0x0f, 0x85, 0x2e, 0x12, 0x00, 0x00, //0x00000c80 jne          LBB0_402
	0x4c, 0x01, 0xc2, //0x00000c86 addq         %r8, %rdx
	0x49, 0x89, 0xd3, //0x00000c89 movq         %rdx, %r11
	//0x00000c8c LBB0_155
	0x83, 0xf9, 0x20, //0x00000c8c cmpl         $32, %ecx
	0x0f, 0x85, 0xbf, 0x05, 0x00, 0x00, //0x00000c8f jne          LBB0_218
	0x49, 0x83, 0xc5, 0xe0, //0x00000c95 addq         $-32, %r13
	0x49, 0x83, 0xc0, 0x20, //0x00000c99 addq         $32, %r8
	0x49, 0x83, 0xfd, 0x1f, //0x00000c9d cmpq         $31, %r13
	0x0f, 0x87, 0x09, 0xff, 0xff, 0xff, //0x00000ca1 ja           LBB0_141
	0xc5, 0xf8, 0x77, //0x00000ca7 vzeroupper   
	0xc5, 0x7e, 0x6f, 0x3d, 0x0e, 0xf5, 0xff, 0xff, //0x00000caa vmovdqu      $-2802(%rip), %ymm15  /* LCPI0_18+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0x86, 0xf3, 0xff, 0xff, //0x00000cb2 vmovdqu      $-3194(%rip), %ymm14  /* LCPI0_2+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0xde, 0xf4, 0xff, 0xff, //0x00000cba vmovdqu      $-2850(%rip), %ymm13  /* LCPI0_17+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0xb6, 0xf4, 0xff, 0xff, //0x00000cc2 vmovdqu      $-2890(%rip), %ymm12  /* LCPI0_16+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0x8e, 0xf4, 0xff, 0xff, //0x00000cca vmovdqu      $-2930(%rip), %ymm11  /* LCPI0_15+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x15, 0x66, 0xf4, 0xff, 0xff, //0x00000cd2 vmovdqu      $-2970(%rip), %ymm10  /* LCPI0_14+0(%rip) */
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00000cda vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0x7e, 0x6f, 0x05, 0x39, 0xf4, 0xff, 0xff, //0x00000cdf vmovdqu      $-3015(%rip), %ymm8  /* LCPI0_13+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0xb1, 0xf3, 0xff, 0xff, //0x00000ce7 vmovdqu      $-3151(%rip), %ymm7  /* LCPI0_8+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0x89, 0xf3, 0xff, 0xff, //0x00000cef vmovdqu      $-3191(%rip), %ymm6  /* LCPI0_7+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x2d, 0x01, 0xf3, 0xff, 0xff, //0x00000cf7 vmovdqu      $-3327(%rip), %ymm5  /* LCPI0_0+0(%rip) */
	0x4d, 0x01, 0xe0, //0x00000cff addq         %r12, %r8
	0x4c, 0x89, 0x4c, 0x24, 0x20, //0x00000d02 movq         %r9, $32(%rsp)
	0x49, 0x83, 0xfd, 0x10, //0x00000d07 cmpq         $16, %r13
	0x48, 0x8b, 0x44, 0x24, 0x28, //0x00000d0b movq         $40(%rsp), %rax
	0x0f, 0x82, 0x81, 0x01, 0x00, 0x00, //0x00000d10 jb           LBB0_176
	//0x00000d16 LBB0_158
	0x4c, 0x89, 0xc1, //0x00000d16 movq         %r8, %rcx
	0x48, 0x29, 0xc1, //0x00000d19 subq         %rax, %rcx
	0x48, 0x2b, 0x4c, 0x24, 0x10, //0x00000d1c subq         $16(%rsp), %rcx
	0x48, 0x83, 0xc1, 0x01, //0x00000d21 addq         $1, %rcx
	0x48, 0x89, 0x4c, 0x24, 0x30, //0x00000d25 movq         %rcx, $48(%rsp)
	0x45, 0x31, 0xc9, //0x00000d2a xorl         %r9d, %r9d
	0x90, 0x90, 0x90, //0x00000d2d .p2align 4, 0x90
	//0x00000d30 LBB0_159
	0x4c, 0x89, 0xdb, //0x00000d30 movq         %r11, %rbx
	0x4d, 0x89, 0xd3, //0x00000d33 movq         %r10, %r11
	0x4d, 0x89, 0xe2, //0x00000d36 movq         %r12, %r10
	0xc4, 0x81, 0x7a, 0x6f, 0x04, 0x08, //0x00000d39 vmovdqu      (%r8,%r9), %xmm0
	0xc5, 0xf9, 0x64, 0x0d, 0xf9, 0xf4, 0xff, 0xff, //0x00000d3f vpcmpgtb     $-2823(%rip), %xmm0, %xmm1  /* LCPI0_20+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x15, 0x01, 0xf5, 0xff, 0xff, //0x00000d47 vmovdqu      $-2815(%rip), %xmm2  /* LCPI0_21+0(%rip) */
	0xc5, 0xe9, 0x64, 0xd0, //0x00000d4f vpcmpgtb     %xmm0, %xmm2, %xmm2
	0xc5, 0xf1, 0xdb, 0xca, //0x00000d53 vpand        %xmm2, %xmm1, %xmm1
	0xc5, 0xf9, 0x74, 0x15, 0x01, 0xf5, 0xff, 0xff, //0x00000d57 vpcmpeqb     $-2815(%rip), %xmm0, %xmm2  /* LCPI0_22+0(%rip) */
	0xc5, 0xf9, 0x74, 0x1d, 0x09, 0xf5, 0xff, 0xff, //0x00000d5f vpcmpeqb     $-2807(%rip), %xmm0, %xmm3  /* LCPI0_23+0(%rip) */
	0xc5, 0xe1, 0xeb, 0xd2, //0x00000d67 vpor         %xmm2, %xmm3, %xmm2
	0xc5, 0xf9, 0xdb, 0x1d, 0x9d, 0xf4, 0xff, 0xff, //0x00000d6b vpand        $-2915(%rip), %xmm0, %xmm3  /* LCPI0_5+0(%rip) */
	0xc5, 0xf9, 0x74, 0x05, 0x05, 0xf5, 0xff, 0xff, //0x00000d73 vpcmpeqb     $-2811(%rip), %xmm0, %xmm0  /* LCPI0_24+0(%rip) */
	0xc5, 0xe1, 0x74, 0x1d, 0x0d, 0xf5, 0xff, 0xff, //0x00000d7b vpcmpeqb     $-2803(%rip), %xmm3, %xmm3  /* LCPI0_25+0(%rip) */
	0xc5, 0xe1, 0xeb, 0xe0, //0x00000d83 vpor         %xmm0, %xmm3, %xmm4
	0xc5, 0xe9, 0xeb, 0xc9, //0x00000d87 vpor         %xmm1, %xmm2, %xmm1
	0xc5, 0xd9, 0xeb, 0xc9, //0x00000d8b vpor         %xmm1, %xmm4, %xmm1
	0xc5, 0x79, 0xd7, 0xe0, //0x00000d8f vpmovmskb    %xmm0, %r12d
	0xc5, 0xf9, 0xd7, 0xd3, //0x00000d93 vpmovmskb    %xmm3, %edx
	0xc5, 0xf9, 0xd7, 0xfa, //0x00000d97 vpmovmskb    %xmm2, %edi
	0xc5, 0xf9, 0xd7, 0xc1, //0x00000d9b vpmovmskb    %xmm1, %eax
	0xf7, 0xd0, //0x00000d9f notl         %eax
	0x0f, 0xbc, 0xc8, //0x00000da1 bsfl         %eax, %ecx
	0x83, 0xf9, 0x10, //0x00000da4 cmpl         $16, %ecx
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00000da7 je           LBB0_161
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x00000dad movl         $-1, %eax
	0xd3, 0xe0, //0x00000db2 shll         %cl, %eax
	0xf7, 0xd0, //0x00000db4 notl         %eax
	0x41, 0x21, 0xc4, //0x00000db6 andl         %eax, %r12d
	0x21, 0xc2, //0x00000db9 andl         %eax, %edx
	0x21, 0xf8, //0x00000dbb andl         %edi, %eax
	0x89, 0xc7, //0x00000dbd movl         %eax, %edi
	//0x00000dbf LBB0_161
	0xc5, 0xfe, 0x6f, 0x25, 0x19, 0xf4, 0xff, 0xff, //0x00000dbf vmovdqu      $-3047(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0x41, 0x8d, 0x74, 0x24, 0xff, //0x00000dc7 leal         $-1(%r12), %esi
	0x44, 0x21, 0xe6, //0x00000dcc andl         %r12d, %esi
	0x0f, 0x85, 0xaa, 0x11, 0x00, 0x00, //0x00000dcf jne          LBB0_405
	0x8d, 0x72, 0xff, //0x00000dd5 leal         $-1(%rdx), %esi
	0x21, 0xd6, //0x00000dd8 andl         %edx, %esi
	0x0f, 0x85, 0x9f, 0x11, 0x00, 0x00, //0x00000dda jne          LBB0_405
	0x8d, 0x77, 0xff, //0x00000de0 leal         $-1(%rdi), %esi
	0x21, 0xfe, //0x00000de3 andl         %edi, %esi
	0x0f, 0x85, 0x94, 0x11, 0x00, 0x00, //0x00000de5 jne          LBB0_405
	0x45, 0x85, 0xe4, //0x00000deb testl        %r12d, %r12d
	0x0f, 0x84, 0x1e, 0x00, 0x00, 0x00, //0x00000dee je           LBB0_167
	0x41, 0x0f, 0xbc, 0xf4, //0x00000df4 bsfl         %r12d, %esi
	0x48, 0x83, 0x3c, 0x24, 0xff, //0x00000df8 cmpq         $-1, (%rsp)
	0x0f, 0x85, 0x94, 0x12, 0x00, 0x00, //0x00000dfd jne          LBB0_413
	0x48, 0x8b, 0x44, 0x24, 0x30, //0x00000e03 movq         $48(%rsp), %rax
	0x4c, 0x01, 0xc8, //0x00000e08 addq         %r9, %rax
	0x48, 0x01, 0xc6, //0x00000e0b addq         %rax, %rsi
	0x48, 0x89, 0x34, 0x24, //0x00000e0e movq         %rsi, (%rsp)
	//0x00000e12 LBB0_167
	0x85, 0xd2, //0x00000e12 testl        %edx, %edx
	0x4d, 0x89, 0xd4, //0x00000e14 movq         %r10, %r12
	0x0f, 0x84, 0x33, 0x00, 0x00, 0x00, //0x00000e17 je           LBB0_170
	0x0f, 0xbc, 0xd2, //0x00000e1d bsfl         %edx, %edx
	0x49, 0x83, 0xfb, 0xff, //0x00000e20 cmpq         $-1, %r11
	0x0f, 0x85, 0x31, 0x12, 0x00, 0x00, //0x00000e24 jne          LBB0_409
	0x48, 0x8b, 0x44, 0x24, 0x30, //0x00000e2a movq         $48(%rsp), %rax
	0x4c, 0x01, 0xc8, //0x00000e2f addq         %r9, %rax
	0x48, 0x01, 0xc2, //0x00000e32 addq         %rax, %rdx
	0x49, 0x89, 0xd2, //0x00000e35 movq         %rdx, %r10
	0x85, 0xff, //0x00000e38 testl        %edi, %edi
	0x49, 0x89, 0xdb, //0x00000e3a movq         %rbx, %r11
	0x0f, 0x85, 0x1b, 0x00, 0x00, 0x00, //0x00000e3d jne          LBB0_171
	0xe9, 0x31, 0x00, 0x00, 0x00, //0x00000e43 jmp          LBB0_173
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000e48 .p2align 4, 0x90
	//0x00000e50 LBB0_170
	0x4d, 0x89, 0xda, //0x00000e50 movq         %r11, %r10
	0x85, 0xff, //0x00000e53 testl        %edi, %edi
	0x49, 0x89, 0xdb, //0x00000e55 movq         %rbx, %r11
	0x0f, 0x84, 0x1b, 0x00, 0x00, 0x00, //0x00000e58 je           LBB0_173
	//0x00000e5e LBB0_171
	0x0f, 0xbc, 0xd7, //0x00000e5e bsfl         %edi, %edx
	0x49, 0x83, 0xfb, 0xff, //0x00000e61 cmpq         $-1, %r11
	0x0f, 0x85, 0xf0, 0x11, 0x00, 0x00, //0x00000e65 jne          LBB0_409
	0x48, 0x8b, 0x44, 0x24, 0x30, //0x00000e6b movq         $48(%rsp), %rax
	0x4c, 0x01, 0xc8, //0x00000e70 addq         %r9, %rax
	0x48, 0x01, 0xc2, //0x00000e73 addq         %rax, %rdx
	0x49, 0x89, 0xd3, //0x00000e76 movq         %rdx, %r11
	//0x00000e79 LBB0_173
	0x83, 0xf9, 0x10, //0x00000e79 cmpl         $16, %ecx
	0x0f, 0x85, 0x89, 0x05, 0x00, 0x00, //0x00000e7c jne          LBB0_243
	0x49, 0x83, 0xc5, 0xf0, //0x00000e82 addq         $-16, %r13
	0x49, 0x83, 0xc1, 0x10, //0x00000e86 addq         $16, %r9
	0x49, 0x83, 0xfd, 0x0f, //0x00000e8a cmpq         $15, %r13
	0x0f, 0x87, 0x9c, 0xfe, 0xff, 0xff, //0x00000e8e ja           LBB0_159
	0x4d, 0x01, 0xc8, //0x00000e94 addq         %r9, %r8
	//0x00000e97 LBB0_176
	0x4d, 0x85, 0xed, //0x00000e97 testq        %r13, %r13
	0x4c, 0x8b, 0x4c, 0x24, 0x20, //0x00000e9a movq         $32(%rsp), %r9
	0xc5, 0xfe, 0x6f, 0x25, 0x39, 0xf3, 0xff, 0xff, //0x00000e9f vmovdqu      $-3271(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0x48, 0x8b, 0x44, 0x24, 0x28, //0x00000ea7 movq         $40(%rsp), %rax
	0x0f, 0x84, 0x82, 0x05, 0x00, 0x00, //0x00000eac je           LBB0_245
	0x4b, 0x8d, 0x34, 0x28, //0x00000eb2 leaq         (%r8,%r13), %rsi
	0x4c, 0x89, 0xc7, //0x00000eb6 movq         %r8, %rdi
	0x48, 0x29, 0xc7, //0x00000eb9 subq         %rax, %rdi
	0x48, 0x2b, 0x7c, 0x24, 0x10, //0x00000ebc subq         $16(%rsp), %rdi
	0x48, 0x83, 0xc7, 0x01, //0x00000ec1 addq         $1, %rdi
	0x31, 0xc9, //0x00000ec5 xorl         %ecx, %ecx
	0xe9, 0x21, 0x00, 0x00, 0x00, //0x00000ec7 jmp          LBB0_181
	//0x00000ecc LBB0_178
	0x49, 0x83, 0xfb, 0xff, //0x00000ecc cmpq         $-1, %r11
	0x0f, 0x85, 0xb1, 0x10, 0x00, 0x00, //0x00000ed0 jne          LBB0_406
	0x4c, 0x8d, 0x1c, 0x0f, //0x00000ed6 leaq         (%rdi,%rcx), %r11
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000eda .p2align 4, 0x90
	//0x00000ee0 LBB0_180
	0x48, 0x83, 0xc1, 0x01, //0x00000ee0 addq         $1, %rcx
	0x49, 0x39, 0xcd, //0x00000ee4 cmpq         %rcx, %r13
	0x0f, 0x84, 0xe3, 0x0d, 0x00, 0x00, //0x00000ee7 je           LBB0_375
	//0x00000eed LBB0_181
	0x41, 0x0f, 0xbe, 0x14, 0x08, //0x00000eed movsbl       (%r8,%rcx), %edx
	0x8d, 0x42, 0xd0, //0x00000ef2 leal         $-48(%rdx), %eax
	0x83, 0xf8, 0x0a, //0x00000ef5 cmpl         $10, %eax
	0x0f, 0x82, 0xe2, 0xff, 0xff, 0xff, //0x00000ef8 jb           LBB0_180
	0x8d, 0x5a, 0xd5, //0x00000efe leal         $-43(%rdx), %ebx
	0x83, 0xfb, 0x1a, //0x00000f01 cmpl         $26, %ebx
	0x0f, 0x87, 0x28, 0x00, 0x00, 0x00, //0x00000f04 ja           LBB0_186
	0x48, 0x8d, 0x15, 0xef, 0x2c, 0x00, 0x00, //0x00000f0a leaq         $11503(%rip), %rdx  /* LJTI0_4+0(%rip) */
	0x48, 0x63, 0x04, 0x9a, //0x00000f11 movslq       (%rdx,%rbx,4), %rax
	0x48, 0x01, 0xd0, //0x00000f15 addq         %rdx, %rax
	0xff, 0xe0, //0x00000f18 jmpq         *%rax
	//0x00000f1a LBB0_184
	0x48, 0x83, 0x3c, 0x24, 0xff, //0x00000f1a cmpq         $-1, (%rsp)
	0x0f, 0x85, 0x62, 0x10, 0x00, 0x00, //0x00000f1f jne          LBB0_406
	0x48, 0x8d, 0x04, 0x0f, //0x00000f25 leaq         (%rdi,%rcx), %rax
	0x48, 0x89, 0x04, 0x24, //0x00000f29 movq         %rax, (%rsp)
	0xe9, 0xae, 0xff, 0xff, 0xff, //0x00000f2d jmp          LBB0_180
	//0x00000f32 LBB0_186
	0x83, 0xfa, 0x65, //0x00000f32 cmpl         $101, %edx
	0x0f, 0x85, 0xf6, 0x04, 0x00, 0x00, //0x00000f35 jne          LBB0_244
	//0x00000f3b LBB0_187
	0x49, 0x83, 0xfa, 0xff, //0x00000f3b cmpq         $-1, %r10
	0x0f, 0x85, 0x42, 0x10, 0x00, 0x00, //0x00000f3f jne          LBB0_406
	0x4c, 0x8d, 0x14, 0x0f, //0x00000f45 leaq         (%rdi,%rcx), %r10
	0xe9, 0x92, 0xff, 0xff, 0xff, //0x00000f49 jmp          LBB0_180
	//0x00000f4e LBB0_120
	0x83, 0xff, 0x22, //0x00000f4e cmpl         $34, %edi
	0x0f, 0x84, 0x3f, 0x00, 0x00, 0x00, //0x00000f51 je           LBB0_121
	//0x00000f57 LBB0_118
	0x83, 0xff, 0x7d, //0x00000f57 cmpl         $125, %edi
	0x0f, 0x85, 0x71, 0x25, 0x00, 0x00, //0x00000f5a jne          LBB0_637
	//0x00000f60 LBB0_92
	0x49, 0x89, 0x11, //0x00000f60 movq         %rdx, (%r9)
	0x4d, 0x89, 0xf7, //0x00000f63 movq         %r14, %r15
	0x48, 0x85, 0xd2, //0x00000f66 testq        %rdx, %rdx
	0x0f, 0x85, 0xa1, 0xf7, 0xff, 0xff, //0x00000f69 jne          LBB0_57
	0xe9, 0x64, 0x25, 0x00, 0x00, //0x00000f6f jmp          LBB0_638
	//0x00000f74 LBB0_189
	0x48, 0x81, 0xfe, 0xff, 0x0f, 0x00, 0x00, //0x00000f74 cmpq         $4095, %rsi
	0x0f, 0x8f, 0x27, 0x1e, 0x00, 0x00, //0x00000f7b jg           LBB0_650
	0x48, 0x8d, 0x4e, 0x01, //0x00000f81 leaq         $1(%rsi), %rcx
	0x49, 0x89, 0x09, //0x00000f85 movq         %rcx, (%r9)
	0x49, 0xc7, 0x44, 0xf1, 0x08, 0x00, 0x00, 0x00, 0x00, //0x00000f88 movq         $0, $8(%r9,%rsi,8)
	0xe9, 0x5a, 0xf7, 0xff, 0xff, //0x00000f91 jmp          LBB0_55
	//0x00000f96 LBB0_121
	0x49, 0xc7, 0x04, 0xf1, 0x02, 0x00, 0x00, 0x00, //0x00000f96 movq         $2, (%r9,%rsi,8)
	0x4d, 0x8b, 0x03, //0x00000f9e movq         (%r11), %r8
	0x4d, 0x8b, 0x65, 0x08, //0x00000fa1 movq         $8(%r13), %r12
	0x4c, 0x89, 0xe3, //0x00000fa5 movq         %r12, %rbx
	0x41, 0xf6, 0xc2, 0x20, //0x00000fa8 testb        $32, %r10b
	0x0f, 0x85, 0xc7, 0x02, 0x00, 0x00, //0x00000fac jne          LBB0_219
	0x4c, 0x29, 0xc3, //0x00000fb2 subq         %r8, %rbx
	0x0f, 0x84, 0xed, 0x26, 0x00, 0x00, //0x00000fb5 je           LBB0_670
	0x48, 0x83, 0xfb, 0x40, //0x00000fbb cmpq         $64, %rbx
	0x0f, 0x82, 0xdc, 0x12, 0x00, 0x00, //0x00000fbf jb           LBB0_434
	0x4c, 0x89, 0xc6, //0x00000fc5 movq         %r8, %rsi
	0x48, 0xf7, 0xd6, //0x00000fc8 notq         %rsi
	0x48, 0xc7, 0x04, 0x24, 0xff, 0xff, 0xff, 0xff, //0x00000fcb movq         $-1, (%rsp)
	0x4d, 0x89, 0xc7, //0x00000fd3 movq         %r8, %r15
	0x45, 0x31, 0xd2, //0x00000fd6 xorl         %r10d, %r10d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000fd9 .p2align 4, 0x90
	//0x00000fe0 LBB0_125
	0x48, 0x8b, 0x44, 0x24, 0x10, //0x00000fe0 movq         $16(%rsp), %rax
	0xc4, 0xa1, 0x7e, 0x6f, 0x04, 0x38, //0x00000fe5 vmovdqu      (%rax,%r15), %ymm0
	0xc4, 0xa1, 0x7e, 0x6f, 0x4c, 0x38, 0x20, //0x00000feb vmovdqu      $32(%rax,%r15), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x00000ff2 vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xfa, //0x00000ff6 vpmovmskb    %ymm2, %edi
	0xc5, 0xf5, 0x74, 0xd6, //0x00000ffa vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xc2, //0x00000ffe vpmovmskb    %ymm2, %eax
	0xc5, 0xfd, 0x74, 0xc7, //0x00001002 vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xd0, //0x00001006 vpmovmskb    %ymm0, %edx
	0xc5, 0xf5, 0x74, 0xc7, //0x0000100a vpcmpeqb     %ymm7, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xc8, //0x0000100e vpmovmskb    %ymm0, %ecx
	0x48, 0xc1, 0xe0, 0x20, //0x00001012 shlq         $32, %rax
	0x48, 0x09, 0xc7, //0x00001016 orq          %rax, %rdi
	0x48, 0xc1, 0xe1, 0x20, //0x00001019 shlq         $32, %rcx
	0x48, 0x09, 0xca, //0x0000101d orq          %rcx, %rdx
	0x0f, 0x85, 0x30, 0x00, 0x00, 0x00, //0x00001020 jne          LBB0_193
	0x4d, 0x85, 0xd2, //0x00001026 testq        %r10, %r10
	0x0f, 0x85, 0x4d, 0x00, 0x00, 0x00, //0x00001029 jne          LBB0_195
	0x45, 0x31, 0xd2, //0x0000102f xorl         %r10d, %r10d
	0x48, 0x85, 0xff, //0x00001032 testq        %rdi, %rdi
	0x0f, 0x85, 0xa9, 0x00, 0x00, 0x00, //0x00001035 jne          LBB0_197
	//0x0000103b LBB0_128
	0x48, 0x83, 0xc3, 0xc0, //0x0000103b addq         $-64, %rbx
	0x48, 0x83, 0xc6, 0xc0, //0x0000103f addq         $-64, %rsi
	0x49, 0x83, 0xc7, 0x40, //0x00001043 addq         $64, %r15
	0x48, 0x83, 0xfb, 0x3f, //0x00001047 cmpq         $63, %rbx
	0x0f, 0x87, 0x8f, 0xff, 0xff, 0xff, //0x0000104b ja           LBB0_125
	0xe9, 0x60, 0x0f, 0x00, 0x00, //0x00001051 jmp          LBB0_129
	//0x00001056 LBB0_193
	0x4c, 0x89, 0x4c, 0x24, 0x20, //0x00001056 movq         %r9, $32(%rsp)
	0x4d, 0x89, 0xe9, //0x0000105b movq         %r13, %r9
	0x4d, 0x89, 0xdd, //0x0000105e movq         %r11, %r13
	0x48, 0x83, 0x3c, 0x24, 0xff, //0x00001061 cmpq         $-1, (%rsp)
	0x0f, 0x85, 0x1b, 0x00, 0x00, 0x00, //0x00001066 jne          LBB0_196
	0x48, 0x0f, 0xbc, 0xc2, //0x0000106c bsfq         %rdx, %rax
	0x4c, 0x01, 0xf8, //0x00001070 addq         %r15, %rax
	0x48, 0x89, 0x04, 0x24, //0x00001073 movq         %rax, (%rsp)
	0xe9, 0x0b, 0x00, 0x00, 0x00, //0x00001077 jmp          LBB0_196
	//0x0000107c LBB0_195
	0x4c, 0x89, 0x4c, 0x24, 0x20, //0x0000107c movq         %r9, $32(%rsp)
	0x4d, 0x89, 0xe9, //0x00001081 movq         %r13, %r9
	0x4d, 0x89, 0xdd, //0x00001084 movq         %r11, %r13
	//0x00001087 LBB0_196
	0x4c, 0x89, 0xd0, //0x00001087 movq         %r10, %rax
	0x48, 0xf7, 0xd0, //0x0000108a notq         %rax
	0x48, 0x21, 0xd0, //0x0000108d andq         %rdx, %rax
	0x4c, 0x8d, 0x1c, 0x00, //0x00001090 leaq         (%rax,%rax), %r11
	0x4d, 0x09, 0xd3, //0x00001094 orq          %r10, %r11
	0x4c, 0x89, 0xd9, //0x00001097 movq         %r11, %rcx
	0x48, 0xf7, 0xd1, //0x0000109a notq         %rcx
	0x48, 0x21, 0xd1, //0x0000109d andq         %rdx, %rcx
	0x48, 0xba, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x000010a0 movabsq      $-6148914691236517206, %rdx
	0x48, 0x21, 0xd1, //0x000010aa andq         %rdx, %rcx
	0x45, 0x31, 0xd2, //0x000010ad xorl         %r10d, %r10d
	0x48, 0x01, 0xc1, //0x000010b0 addq         %rax, %rcx
	0x41, 0x0f, 0x92, 0xc2, //0x000010b3 setb         %r10b
	0x48, 0x01, 0xc9, //0x000010b7 addq         %rcx, %rcx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000010ba movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc1, //0x000010c4 xorq         %rax, %rcx
	0x4c, 0x21, 0xd9, //0x000010c7 andq         %r11, %rcx
	0x48, 0xf7, 0xd1, //0x000010ca notq         %rcx
	0x48, 0x21, 0xcf, //0x000010cd andq         %rcx, %rdi
	0x4d, 0x89, 0xeb, //0x000010d0 movq         %r13, %r11
	0x4d, 0x89, 0xcd, //0x000010d3 movq         %r9, %r13
	0x4c, 0x8b, 0x4c, 0x24, 0x20, //0x000010d6 movq         $32(%rsp), %r9
	0x48, 0x85, 0xff, //0x000010db testq        %rdi, %rdi
	0x0f, 0x84, 0x57, 0xff, 0xff, 0xff, //0x000010de je           LBB0_128
	//0x000010e4 LBB0_197
	0x4c, 0x0f, 0xbc, 0xff, //0x000010e4 bsfq         %rdi, %r15
	0x49, 0x29, 0xf7, //0x000010e8 subq         %rsi, %r15
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x000010eb movq         $24(%rsp), %r10
	0xe9, 0x42, 0x04, 0x00, 0x00, //0x000010f0 jmp          LBB0_259
	//0x000010f5 LBB0_198
	0x4c, 0x89, 0xc8, //0x000010f5 movq         %r9, %rax
	0x4d, 0x89, 0xe9, //0x000010f8 movq         %r13, %r9
	0x4d, 0x89, 0xe5, //0x000010fb movq         %r12, %r13
	0x4d, 0x29, 0xc5, //0x000010fe subq         %r8, %r13
	0x0f, 0x84, 0xa1, 0x25, 0x00, 0x00, //0x00001101 je           LBB0_670
	0x49, 0x83, 0xfd, 0x40, //0x00001107 cmpq         $64, %r13
	0x0f, 0x82, 0x32, 0x11, 0x00, 0x00, //0x0000110b jb           LBB0_428
	0x48, 0xc7, 0x04, 0x24, 0xff, 0xff, 0xff, 0xff, //0x00001111 movq         $-1, (%rsp)
	0x4d, 0x89, 0xc7, //0x00001119 movq         %r8, %r15
	0x45, 0x31, 0xdb, //0x0000111c xorl         %r11d, %r11d
	0x90, //0x0000111f .p2align 4, 0x90
	//0x00001120 LBB0_201
	0x48, 0x8b, 0x4c, 0x24, 0x10, //0x00001120 movq         $16(%rsp), %rcx
	0xc4, 0xa1, 0x7e, 0x6f, 0x04, 0x39, //0x00001125 vmovdqu      (%rcx,%r15), %ymm0
	0xc4, 0xa1, 0x7e, 0x6f, 0x4c, 0x39, 0x20, //0x0000112b vmovdqu      $32(%rcx,%r15), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x00001132 vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xfa, //0x00001136 vpmovmskb    %ymm2, %edi
	0xc5, 0xf5, 0x74, 0xd6, //0x0000113a vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xca, //0x0000113e vpmovmskb    %ymm2, %ecx
	0xc5, 0xfd, 0x74, 0xd7, //0x00001142 vpcmpeqb     %ymm7, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xd2, //0x00001146 vpmovmskb    %ymm2, %edx
	0xc5, 0xf5, 0x74, 0xd7, //0x0000114a vpcmpeqb     %ymm7, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xda, //0x0000114e vpmovmskb    %ymm2, %ebx
	0xc5, 0xbd, 0x64, 0xd1, //0x00001152 vpcmpgtb     %ymm1, %ymm8, %ymm2
	0xc4, 0xc1, 0x75, 0x64, 0xc9, //0x00001156 vpcmpgtb     %ymm9, %ymm1, %ymm1
	0xc5, 0xed, 0xdb, 0xc9, //0x0000115b vpand        %ymm1, %ymm2, %ymm1
	0xc5, 0xfd, 0xd7, 0xf1, //0x0000115f vpmovmskb    %ymm1, %esi
	0x48, 0xc1, 0xe1, 0x20, //0x00001163 shlq         $32, %rcx
	0x48, 0x09, 0xcf, //0x00001167 orq          %rcx, %rdi
	0x48, 0xc1, 0xe3, 0x20, //0x0000116a shlq         $32, %rbx
	0x48, 0xc1, 0xe6, 0x20, //0x0000116e shlq         $32, %rsi
	0x48, 0x09, 0xda, //0x00001172 orq          %rbx, %rdx
	0x0f, 0x85, 0x49, 0x00, 0x00, 0x00, //0x00001175 jne          LBB0_212
	0x4d, 0x85, 0xdb, //0x0000117b testq        %r11, %r11
	0x0f, 0x85, 0x56, 0x00, 0x00, 0x00, //0x0000117e jne          LBB0_214
	0x45, 0x31, 0xdb, //0x00001184 xorl         %r11d, %r11d
	//0x00001187 LBB0_204
	0xc5, 0xbd, 0x64, 0xc8, //0x00001187 vpcmpgtb     %ymm0, %ymm8, %ymm1
	0xc4, 0xc1, 0x7d, 0x64, 0xc1, //0x0000118b vpcmpgtb     %ymm9, %ymm0, %ymm0
	0xc5, 0xf5, 0xdb, 0xc0, //0x00001190 vpand        %ymm0, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xc8, //0x00001194 vpmovmskb    %ymm0, %ecx
	0x48, 0x09, 0xce, //0x00001198 orq          %rcx, %rsi
	0x48, 0x85, 0xff, //0x0000119b testq        %rdi, %rdi
	0x0f, 0x85, 0x89, 0x00, 0x00, 0x00, //0x0000119e jne          LBB0_215
	0x48, 0x85, 0xf6, //0x000011a4 testq        %rsi, %rsi
	0x0f, 0x85, 0x8a, 0x23, 0x00, 0x00, //0x000011a7 jne          LBB0_643
	0x49, 0x83, 0xc5, 0xc0, //0x000011ad addq         $-64, %r13
	0x49, 0x83, 0xc7, 0x40, //0x000011b1 addq         $64, %r15
	0x49, 0x83, 0xfd, 0x3f, //0x000011b5 cmpq         $63, %r13
	0x0f, 0x87, 0x61, 0xff, 0xff, 0xff, //0x000011b9 ja           LBB0_201
	0xe9, 0x58, 0x0d, 0x00, 0x00, //0x000011bf jmp          LBB0_207
	//0x000011c4 LBB0_212
	0x48, 0x83, 0x3c, 0x24, 0xff, //0x000011c4 cmpq         $-1, (%rsp)
	0x0f, 0x85, 0x0b, 0x00, 0x00, 0x00, //0x000011c9 jne          LBB0_214
	0x48, 0x0f, 0xbc, 0xca, //0x000011cf bsfq         %rdx, %rcx
	0x4c, 0x01, 0xf9, //0x000011d3 addq         %r15, %rcx
	0x48, 0x89, 0x0c, 0x24, //0x000011d6 movq         %rcx, (%rsp)
	//0x000011da LBB0_214
	0x4c, 0x89, 0xd9, //0x000011da movq         %r11, %rcx
	0x48, 0xf7, 0xd1, //0x000011dd notq         %rcx
	0x48, 0x21, 0xd1, //0x000011e0 andq         %rdx, %rcx
	0x4c, 0x8d, 0x14, 0x09, //0x000011e3 leaq         (%rcx,%rcx), %r10
	0x4d, 0x09, 0xda, //0x000011e7 orq          %r11, %r10
	0x4c, 0x89, 0xd3, //0x000011ea movq         %r10, %rbx
	0x48, 0xf7, 0xd3, //0x000011ed notq         %rbx
	0x48, 0x21, 0xd3, //0x000011f0 andq         %rdx, %rbx
	0x48, 0xba, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x000011f3 movabsq      $-6148914691236517206, %rdx
	0x48, 0x21, 0xd3, //0x000011fd andq         %rdx, %rbx
	0x45, 0x31, 0xdb, //0x00001200 xorl         %r11d, %r11d
	0x48, 0x01, 0xcb, //0x00001203 addq         %rcx, %rbx
	0x41, 0x0f, 0x92, 0xc3, //0x00001206 setb         %r11b
	0x48, 0x01, 0xdb, //0x0000120a addq         %rbx, %rbx
	0x48, 0xb9, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x0000120d movabsq      $6148914691236517205, %rcx
	0x48, 0x31, 0xcb, //0x00001217 xorq         %rcx, %rbx
	0x4c, 0x21, 0xd3, //0x0000121a andq         %r10, %rbx
	0x48, 0xf7, 0xd3, //0x0000121d notq         %rbx
	0x48, 0x21, 0xdf, //0x00001220 andq         %rbx, %rdi
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x00001223 movq         $24(%rsp), %r10
	0xe9, 0x5a, 0xff, 0xff, 0xff, //0x00001228 jmp          LBB0_204
	//0x0000122d LBB0_215
	0x48, 0x0f, 0xbc, 0xcf, //0x0000122d bsfq         %rdi, %rcx
	0x48, 0x85, 0xf6, //0x00001231 testq        %rsi, %rsi
	0x0f, 0x84, 0x97, 0x01, 0x00, 0x00, //0x00001234 je           LBB0_238
	0x48, 0x0f, 0xbc, 0xd6, //0x0000123a bsfq         %rsi, %rdx
	0x4c, 0x8b, 0x5c, 0x24, 0x08, //0x0000123e movq         $8(%rsp), %r11
	0x4d, 0x89, 0xcd, //0x00001243 movq         %r9, %r13
	0x48, 0x39, 0xca, //0x00001246 cmpq         %rcx, %rdx
	0x0f, 0x83, 0x98, 0x01, 0x00, 0x00, //0x00001249 jae          LBB0_239
	0xe9, 0x6f, 0x24, 0x00, 0x00, //0x0000124f jmp          LBB0_217
	//0x00001254 LBB0_218
	0x4c, 0x01, 0xc1, //0x00001254 addq         %r8, %rcx
	0x4c, 0x01, 0xe1, //0x00001257 addq         %r12, %rcx
	0xc5, 0xf8, 0x77, //0x0000125a vzeroupper   
	0x49, 0x89, 0xc8, //0x0000125d movq         %rcx, %r8
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00001260 movq         $-1, %rcx
	0x48, 0x8b, 0x14, 0x24, //0x00001267 movq         (%rsp), %rdx
	0x48, 0x85, 0xd2, //0x0000126b testq        %rdx, %rdx
	0x0f, 0x85, 0xd4, 0x01, 0x00, 0x00, //0x0000126e jne          LBB0_246
	0xe9, 0x46, 0x22, 0x00, 0x00, //0x00001274 jmp          LBB0_636
	//0x00001279 LBB0_219
	0x4c, 0x29, 0xc3, //0x00001279 subq         %r8, %rbx
	0x0f, 0x84, 0x26, 0x24, 0x00, 0x00, //0x0000127c je           LBB0_670
	0x48, 0x83, 0xfb, 0x40, //0x00001282 cmpq         $64, %rbx
	0x0f, 0x82, 0x38, 0x10, 0x00, 0x00, //0x00001286 jb           LBB0_435
	0x48, 0xc7, 0x04, 0x24, 0xff, 0xff, 0xff, 0xff, //0x0000128c movq         $-1, (%rsp)
	0x4d, 0x89, 0xc7, //0x00001294 movq         %r8, %r15
	0x45, 0x31, 0xdb, //0x00001297 xorl         %r11d, %r11d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x0000129a .p2align 4, 0x90
	//0x000012a0 LBB0_222
	0x48, 0x8b, 0x44, 0x24, 0x10, //0x000012a0 movq         $16(%rsp), %rax
	0xc4, 0xa1, 0x7e, 0x6f, 0x04, 0x38, //0x000012a5 vmovdqu      (%rax,%r15), %ymm0
	0xc4, 0xa1, 0x7e, 0x6f, 0x4c, 0x38, 0x20, //0x000012ab vmovdqu      $32(%rax,%r15), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x000012b2 vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xfa, //0x000012b6 vpmovmskb    %ymm2, %edi
	0xc5, 0xf5, 0x74, 0xd6, //0x000012ba vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xc2, //0x000012be vpmovmskb    %ymm2, %eax
	0xc5, 0xfd, 0x74, 0xd7, //0x000012c2 vpcmpeqb     %ymm7, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xd2, //0x000012c6 vpmovmskb    %ymm2, %edx
	0xc5, 0xf5, 0x74, 0xd7, //0x000012ca vpcmpeqb     %ymm7, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xca, //0x000012ce vpmovmskb    %ymm2, %ecx
	0xc5, 0xbd, 0x64, 0xd1, //0x000012d2 vpcmpgtb     %ymm1, %ymm8, %ymm2
	0xc4, 0xc1, 0x75, 0x64, 0xc9, //0x000012d6 vpcmpgtb     %ymm9, %ymm1, %ymm1
	0xc5, 0xed, 0xdb, 0xc9, //0x000012db vpand        %ymm1, %ymm2, %ymm1
	0xc5, 0xfd, 0xd7, 0xf1, //0x000012df vpmovmskb    %ymm1, %esi
	0x48, 0xc1, 0xe0, 0x20, //0x000012e3 shlq         $32, %rax
	0x48, 0x09, 0xc7, //0x000012e7 orq          %rax, %rdi
	0x48, 0xc1, 0xe1, 0x20, //0x000012ea shlq         $32, %rcx
	0x48, 0xc1, 0xe6, 0x20, //0x000012ee shlq         $32, %rsi
	0x48, 0x09, 0xca, //0x000012f2 orq          %rcx, %rdx
	0x0f, 0x85, 0x49, 0x00, 0x00, 0x00, //0x000012f5 jne          LBB0_233
	0x4d, 0x85, 0xdb, //0x000012fb testq        %r11, %r11
	0x0f, 0x85, 0x56, 0x00, 0x00, 0x00, //0x000012fe jne          LBB0_235
	0x45, 0x31, 0xdb, //0x00001304 xorl         %r11d, %r11d
	//0x00001307 LBB0_225
	0xc5, 0xbd, 0x64, 0xc8, //0x00001307 vpcmpgtb     %ymm0, %ymm8, %ymm1
	0xc4, 0xc1, 0x7d, 0x64, 0xc1, //0x0000130b vpcmpgtb     %ymm9, %ymm0, %ymm0
	0xc5, 0xf5, 0xdb, 0xc0, //0x00001310 vpand        %ymm0, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xc0, //0x00001314 vpmovmskb    %ymm0, %eax
	0x48, 0x09, 0xc6, //0x00001318 orq          %rax, %rsi
	0x48, 0x85, 0xff, //0x0000131b testq        %rdi, %rdi
	0x0f, 0x85, 0x89, 0x00, 0x00, 0x00, //0x0000131e jne          LBB0_236
	0x48, 0x85, 0xf6, //0x00001324 testq        %rsi, %rsi
	0x0f, 0x85, 0x0a, 0x22, 0x00, 0x00, //0x00001327 jne          LBB0_643
	0x48, 0x83, 0xc3, 0xc0, //0x0000132d addq         $-64, %rbx
	0x49, 0x83, 0xc7, 0x40, //0x00001331 addq         $64, %r15
	0x48, 0x83, 0xfb, 0x3f, //0x00001335 cmpq         $63, %rbx
	0x0f, 0x87, 0x61, 0xff, 0xff, 0xff, //0x00001339 ja           LBB0_222
	0xe9, 0xcd, 0x0c, 0x00, 0x00, //0x0000133f jmp          LBB0_228
	//0x00001344 LBB0_233
	0x48, 0x83, 0x3c, 0x24, 0xff, //0x00001344 cmpq         $-1, (%rsp)
	0x0f, 0x85, 0x0b, 0x00, 0x00, 0x00, //0x00001349 jne          LBB0_235
	0x48, 0x0f, 0xbc, 0xc2, //0x0000134f bsfq         %rdx, %rax
	0x4c, 0x01, 0xf8, //0x00001353 addq         %r15, %rax
	0x48, 0x89, 0x04, 0x24, //0x00001356 movq         %rax, (%rsp)
	//0x0000135a LBB0_235
	0x4c, 0x89, 0xd8, //0x0000135a movq         %r11, %rax
	0x48, 0xf7, 0xd0, //0x0000135d notq         %rax
	0x48, 0x21, 0xd0, //0x00001360 andq         %rdx, %rax
	0x4c, 0x8d, 0x14, 0x00, //0x00001363 leaq         (%rax,%rax), %r10
	0x4d, 0x09, 0xda, //0x00001367 orq          %r11, %r10
	0x4c, 0x89, 0xd1, //0x0000136a movq         %r10, %rcx
	0x48, 0xf7, 0xd1, //0x0000136d notq         %rcx
	0x48, 0x21, 0xd1, //0x00001370 andq         %rdx, %rcx
	0x48, 0xba, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001373 movabsq      $-6148914691236517206, %rdx
	0x48, 0x21, 0xd1, //0x0000137d andq         %rdx, %rcx
	0x45, 0x31, 0xdb, //0x00001380 xorl         %r11d, %r11d
	0x48, 0x01, 0xc1, //0x00001383 addq         %rax, %rcx
	0x41, 0x0f, 0x92, 0xc3, //0x00001386 setb         %r11b
	0x48, 0x01, 0xc9, //0x0000138a addq         %rcx, %rcx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x0000138d movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc1, //0x00001397 xorq         %rax, %rcx
	0x4c, 0x21, 0xd1, //0x0000139a andq         %r10, %rcx
	0x48, 0xf7, 0xd1, //0x0000139d notq         %rcx
	0x48, 0x21, 0xcf, //0x000013a0 andq         %rcx, %rdi
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x000013a3 movq         $24(%rsp), %r10
	0xe9, 0x5a, 0xff, 0xff, 0xff, //0x000013a8 jmp          LBB0_225
	//0x000013ad LBB0_236
	0x48, 0x0f, 0xbc, 0xcf, //0x000013ad bsfq         %rdi, %rcx
	0x48, 0x85, 0xf6, //0x000013b1 testq        %rsi, %rsi
	0x0f, 0x84, 0x63, 0x01, 0x00, 0x00, //0x000013b4 je           LBB0_257
	0x48, 0x0f, 0xbc, 0xd6, //0x000013ba bsfq         %rsi, %rdx
	0x4c, 0x8b, 0x5c, 0x24, 0x08, //0x000013be movq         $8(%rsp), %r11
	0x48, 0x39, 0xca, //0x000013c3 cmpq         %rcx, %rdx
	0x0f, 0x83, 0x64, 0x01, 0x00, 0x00, //0x000013c6 jae          LBB0_258
	0xe9, 0xf2, 0x22, 0x00, 0x00, //0x000013cc jmp          LBB0_217
	//0x000013d1 LBB0_238
	0xba, 0x40, 0x00, 0x00, 0x00, //0x000013d1 movl         $64, %edx
	0x4c, 0x8b, 0x5c, 0x24, 0x08, //0x000013d6 movq         $8(%rsp), %r11
	0x4d, 0x89, 0xcd, //0x000013db movq         %r9, %r13
	0x48, 0x39, 0xca, //0x000013de cmpq         %rcx, %rdx
	0x0f, 0x82, 0xdc, 0x22, 0x00, 0x00, //0x000013e1 jb           LBB0_217
	//0x000013e7 LBB0_239
	0x49, 0x89, 0xc1, //0x000013e7 movq         %rax, %r9
	0x49, 0x01, 0xcf, //0x000013ea addq         %rcx, %r15
	0x49, 0x83, 0xc7, 0x01, //0x000013ed addq         $1, %r15
	//0x000013f1 LBB0_240
	0x4d, 0x85, 0xff, //0x000013f1 testq        %r15, %r15
	0x0f, 0x88, 0xba, 0x19, 0x00, 0x00, //0x000013f4 js           LBB0_380
	//0x000013fa LBB0_241
	0x4d, 0x89, 0x3b, //0x000013fa movq         %r15, (%r11)
	0x4d, 0x85, 0xc0, //0x000013fd testq        %r8, %r8
	0x0f, 0x8f, 0xea, 0xf2, 0xff, 0xff, //0x00001400 jg           LBB0_55
	0xe9, 0xc9, 0x19, 0x00, 0x00, //0x00001406 jmp          LBB0_242
	//0x0000140b LBB0_243
	0x89, 0xc8, //0x0000140b movl         %ecx, %eax
	0x49, 0x01, 0xc0, //0x0000140d addq         %rax, %r8
	0x4d, 0x01, 0xc8, //0x00001410 addq         %r9, %r8
	0x4c, 0x8b, 0x4c, 0x24, 0x20, //0x00001413 movq         $32(%rsp), %r9
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00001418 movq         $-1, %rcx
	0x48, 0x8b, 0x14, 0x24, //0x0000141f movq         (%rsp), %rdx
	0x48, 0x85, 0xd2, //0x00001423 testq        %rdx, %rdx
	0x0f, 0x85, 0x1c, 0x00, 0x00, 0x00, //0x00001426 jne          LBB0_246
	0xe9, 0x8e, 0x20, 0x00, 0x00, //0x0000142c jmp          LBB0_636
	//0x00001431 LBB0_244
	0x49, 0x01, 0xc8, //0x00001431 addq         %rcx, %r8
	//0x00001434 LBB0_245
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00001434 movq         $-1, %rcx
	0x48, 0x8b, 0x14, 0x24, //0x0000143b movq         (%rsp), %rdx
	0x48, 0x85, 0xd2, //0x0000143f testq        %rdx, %rdx
	0x0f, 0x84, 0x77, 0x20, 0x00, 0x00, //0x00001442 je           LBB0_636
	//0x00001448 LBB0_246
	0x4d, 0x85, 0xdb, //0x00001448 testq        %r11, %r11
	0x0f, 0x84, 0x6e, 0x20, 0x00, 0x00, //0x0000144b je           LBB0_636
	0x4d, 0x85, 0xd2, //0x00001451 testq        %r10, %r10
	0x0f, 0x84, 0x65, 0x20, 0x00, 0x00, //0x00001454 je           LBB0_636
	0x4d, 0x29, 0xe0, //0x0000145a subq         %r12, %r8
	0x49, 0x8d, 0x48, 0xff, //0x0000145d leaq         $-1(%r8), %rcx
	0x48, 0x39, 0xca, //0x00001461 cmpq         %rcx, %rdx
	0x0f, 0x84, 0xf3, 0xf1, 0xff, 0xff, //0x00001464 je           LBB0_634
	0x49, 0x39, 0xcb, //0x0000146a cmpq         %rcx, %r11
	0x0f, 0x84, 0xea, 0xf1, 0xff, 0xff, //0x0000146d je           LBB0_634
	0x49, 0x39, 0xca, //0x00001473 cmpq         %rcx, %r10
	0x0f, 0x84, 0xe1, 0xf1, 0xff, 0xff, //0x00001476 je           LBB0_634
	0x4d, 0x85, 0xdb, //0x0000147c testq        %r11, %r11
	0xc5, 0xfe, 0x6f, 0x2d, 0x79, 0xeb, 0xff, 0xff, //0x0000147f vmovdqu      $-5255(%rip), %ymm5  /* LCPI0_0+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0xf1, 0xeb, 0xff, 0xff, //0x00001487 vmovdqu      $-5135(%rip), %ymm6  /* LCPI0_7+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0x09, 0xec, 0xff, 0xff, //0x0000148f vmovdqu      $-5111(%rip), %ymm7  /* LCPI0_8+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0x81, 0xec, 0xff, 0xff, //0x00001497 vmovdqu      $-4991(%rip), %ymm8  /* LCPI0_13+0(%rip) */
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x0000149f vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0x7e, 0x6f, 0x15, 0x94, 0xec, 0xff, 0xff, //0x000014a4 vmovdqu      $-4972(%rip), %ymm10  /* LCPI0_14+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0xac, 0xec, 0xff, 0xff, //0x000014ac vmovdqu      $-4948(%rip), %ymm11  /* LCPI0_15+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0xc4, 0xec, 0xff, 0xff, //0x000014b4 vmovdqu      $-4924(%rip), %ymm12  /* LCPI0_16+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0xdc, 0xec, 0xff, 0xff, //0x000014bc vmovdqu      $-4900(%rip), %ymm13  /* LCPI0_17+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0x74, 0xeb, 0xff, 0xff, //0x000014c4 vmovdqu      $-5260(%rip), %ymm14  /* LCPI0_2+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x3d, 0xec, 0xec, 0xff, 0xff, //0x000014cc vmovdqu      $-4884(%rip), %ymm15  /* LCPI0_18+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0x04, 0xed, 0xff, 0xff, //0x000014d4 vmovdqu      $-4860(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0x0f, 0x8e, 0x18, 0x00, 0x00, 0x00, //0x000014dc jle          LBB0_254
	0x49, 0x8d, 0x43, 0xff, //0x000014e2 leaq         $-1(%r11), %rax
	0x49, 0x39, 0xc2, //0x000014e6 cmpq         %rax, %r10
	0x0f, 0x84, 0x0b, 0x00, 0x00, 0x00, //0x000014e9 je           LBB0_254
	0x49, 0xf7, 0xd3, //0x000014ef notq         %r11
	0x4d, 0x89, 0xd8, //0x000014f2 movq         %r11, %r8
	0xe9, 0xa4, 0x0a, 0x00, 0x00, //0x000014f5 jmp          LBB0_407
	//0x000014fa LBB0_254
	0x48, 0x89, 0xd0, //0x000014fa movq         %rdx, %rax
	0x4c, 0x09, 0xd0, //0x000014fd orq          %r10, %rax
	0x0f, 0x99, 0xc1, //0x00001500 setns        %cl
	0x0f, 0x88, 0xe1, 0x02, 0x00, 0x00, //0x00001503 js           LBB0_302
	0x4c, 0x39, 0xd2, //0x00001509 cmpq         %r10, %rdx
	0x0f, 0x8c, 0xd8, 0x02, 0x00, 0x00, //0x0000150c jl           LBB0_302
	0x48, 0xf7, 0xd2, //0x00001512 notq         %rdx
	0x49, 0x89, 0xd0, //0x00001515 movq         %rdx, %r8
	0xe9, 0x81, 0x0a, 0x00, 0x00, //0x00001518 jmp          LBB0_407
	//0x0000151d LBB0_257
	0xba, 0x40, 0x00, 0x00, 0x00, //0x0000151d movl         $64, %edx
	0x4c, 0x8b, 0x5c, 0x24, 0x08, //0x00001522 movq         $8(%rsp), %r11
	0x48, 0x39, 0xca, //0x00001527 cmpq         %rcx, %rdx
	0x0f, 0x82, 0x93, 0x21, 0x00, 0x00, //0x0000152a jb           LBB0_217
	//0x00001530 LBB0_258
	0x49, 0x01, 0xcf, //0x00001530 addq         %rcx, %r15
	0x49, 0x83, 0xc7, 0x01, //0x00001533 addq         $1, %r15
	//0x00001537 LBB0_259
	0x4d, 0x85, 0xff, //0x00001537 testq        %r15, %r15
	0x0f, 0x88, 0x74, 0x18, 0x00, 0x00, //0x0000153a js           LBB0_380
	0x4d, 0x89, 0x3b, //0x00001540 movq         %r15, (%r11)
	0x4d, 0x85, 0xc0, //0x00001543 testq        %r8, %r8
	0x0f, 0x8e, 0x88, 0x18, 0x00, 0x00, //0x00001546 jle          LBB0_242
	0x49, 0x8b, 0x09, //0x0000154c movq         (%r9), %rcx
	0x48, 0x81, 0xf9, 0xff, 0x0f, 0x00, 0x00, //0x0000154f cmpq         $4095, %rcx
	0x0f, 0x8f, 0x4c, 0x18, 0x00, 0x00, //0x00001556 jg           LBB0_650
	0x48, 0x8d, 0x41, 0x01, //0x0000155c leaq         $1(%rcx), %rax
	0x49, 0x89, 0x01, //0x00001560 movq         %rax, (%r9)
	0x49, 0xc7, 0x44, 0xc9, 0x08, 0x04, 0x00, 0x00, 0x00, //0x00001563 movq         $4, $8(%r9,%rcx,8)
	0xe9, 0x7f, 0xf1, 0xff, 0xff, //0x0000156c jmp          LBB0_55
	//0x00001571 LBB0_263
	0x4d, 0x8b, 0x03, //0x00001571 movq         (%r11), %r8
	0x4d, 0x8b, 0x65, 0x08, //0x00001574 movq         $8(%r13), %r12
	0x4c, 0x89, 0xe3, //0x00001578 movq         %r12, %rbx
	0x41, 0xf6, 0xc2, 0x20, //0x0000157b testb        $32, %r10b
	0x0f, 0x85, 0x7e, 0x02, 0x00, 0x00, //0x0000157f jne          LBB0_303
	0x4c, 0x29, 0xc3, //0x00001585 subq         %r8, %rbx
	0x0f, 0x84, 0x1a, 0x21, 0x00, 0x00, //0x00001588 je           LBB0_670
	0x48, 0x83, 0xfb, 0x40, //0x0000158e cmpq         $64, %rbx
	0x0f, 0x82, 0x7c, 0x0d, 0x00, 0x00, //0x00001592 jb           LBB0_438
	0x4c, 0x89, 0xc6, //0x00001598 movq         %r8, %rsi
	0x48, 0xf7, 0xd6, //0x0000159b notq         %rsi
	0x48, 0xc7, 0x04, 0x24, 0xff, 0xff, 0xff, 0xff, //0x0000159e movq         $-1, (%rsp)
	0x4d, 0x89, 0xc7, //0x000015a6 movq         %r8, %r15
	0x45, 0x31, 0xd2, //0x000015a9 xorl         %r10d, %r10d
	0x90, 0x90, 0x90, 0x90, //0x000015ac .p2align 4, 0x90
	//0x000015b0 LBB0_267
	0x48, 0x8b, 0x44, 0x24, 0x10, //0x000015b0 movq         $16(%rsp), %rax
	0xc4, 0xa1, 0x7e, 0x6f, 0x04, 0x38, //0x000015b5 vmovdqu      (%rax,%r15), %ymm0
	0xc4, 0xa1, 0x7e, 0x6f, 0x4c, 0x38, 0x20, //0x000015bb vmovdqu      $32(%rax,%r15), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x000015c2 vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xfa, //0x000015c6 vpmovmskb    %ymm2, %edi
	0xc5, 0xf5, 0x74, 0xd6, //0x000015ca vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xc2, //0x000015ce vpmovmskb    %ymm2, %eax
	0xc5, 0xfd, 0x74, 0xc7, //0x000015d2 vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xd0, //0x000015d6 vpmovmskb    %ymm0, %edx
	0xc5, 0xf5, 0x74, 0xc7, //0x000015da vpcmpeqb     %ymm7, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xc8, //0x000015de vpmovmskb    %ymm0, %ecx
	0x48, 0xc1, 0xe0, 0x20, //0x000015e2 shlq         $32, %rax
	0x48, 0x09, 0xc7, //0x000015e6 orq          %rax, %rdi
	0x48, 0xc1, 0xe1, 0x20, //0x000015e9 shlq         $32, %rcx
	0x48, 0x09, 0xca, //0x000015ed orq          %rcx, %rdx
	0x0f, 0x85, 0x30, 0x00, 0x00, 0x00, //0x000015f0 jne          LBB0_276
	0x4d, 0x85, 0xd2, //0x000015f6 testq        %r10, %r10
	0x0f, 0x85, 0x4d, 0x00, 0x00, 0x00, //0x000015f9 jne          LBB0_278
	0x45, 0x31, 0xd2, //0x000015ff xorl         %r10d, %r10d
	0x48, 0x85, 0xff, //0x00001602 testq        %rdi, %rdi
	0x0f, 0x85, 0xa9, 0x00, 0x00, 0x00, //0x00001605 jne          LBB0_280
	//0x0000160b LBB0_270
	0x48, 0x83, 0xc3, 0xc0, //0x0000160b addq         $-64, %rbx
	0x48, 0x83, 0xc6, 0xc0, //0x0000160f addq         $-64, %rsi
	0x49, 0x83, 0xc7, 0x40, //0x00001613 addq         $64, %r15
	0x48, 0x83, 0xfb, 0x3f, //0x00001617 cmpq         $63, %rbx
	0x0f, 0x87, 0x8f, 0xff, 0xff, 0xff, //0x0000161b ja           LBB0_267
	0xe9, 0xaa, 0x0a, 0x00, 0x00, //0x00001621 jmp          LBB0_271
	//0x00001626 LBB0_276
	0x4c, 0x89, 0x4c, 0x24, 0x20, //0x00001626 movq         %r9, $32(%rsp)
	0x4d, 0x89, 0xe9, //0x0000162b movq         %r13, %r9
	0x4d, 0x89, 0xdd, //0x0000162e movq         %r11, %r13
	0x48, 0x83, 0x3c, 0x24, 0xff, //0x00001631 cmpq         $-1, (%rsp)
	0x0f, 0x85, 0x1b, 0x00, 0x00, 0x00, //0x00001636 jne          LBB0_279
	0x48, 0x0f, 0xbc, 0xc2, //0x0000163c bsfq         %rdx, %rax
	0x4c, 0x01, 0xf8, //0x00001640 addq         %r15, %rax
	0x48, 0x89, 0x04, 0x24, //0x00001643 movq         %rax, (%rsp)
	0xe9, 0x0b, 0x00, 0x00, 0x00, //0x00001647 jmp          LBB0_279
	//0x0000164c LBB0_278
	0x4c, 0x89, 0x4c, 0x24, 0x20, //0x0000164c movq         %r9, $32(%rsp)
	0x4d, 0x89, 0xe9, //0x00001651 movq         %r13, %r9
	0x4d, 0x89, 0xdd, //0x00001654 movq         %r11, %r13
	//0x00001657 LBB0_279
	0x4c, 0x89, 0xd0, //0x00001657 movq         %r10, %rax
	0x48, 0xf7, 0xd0, //0x0000165a notq         %rax
	0x48, 0x21, 0xd0, //0x0000165d andq         %rdx, %rax
	0x4c, 0x8d, 0x1c, 0x00, //0x00001660 leaq         (%rax,%rax), %r11
	0x4d, 0x09, 0xd3, //0x00001664 orq          %r10, %r11
	0x4c, 0x89, 0xd9, //0x00001667 movq         %r11, %rcx
	0x48, 0xf7, 0xd1, //0x0000166a notq         %rcx
	0x48, 0x21, 0xd1, //0x0000166d andq         %rdx, %rcx
	0x48, 0xba, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001670 movabsq      $-6148914691236517206, %rdx
	0x48, 0x21, 0xd1, //0x0000167a andq         %rdx, %rcx
	0x45, 0x31, 0xd2, //0x0000167d xorl         %r10d, %r10d
	0x48, 0x01, 0xc1, //0x00001680 addq         %rax, %rcx
	0x41, 0x0f, 0x92, 0xc2, //0x00001683 setb         %r10b
	0x48, 0x01, 0xc9, //0x00001687 addq         %rcx, %rcx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x0000168a movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc1, //0x00001694 xorq         %rax, %rcx
	0x4c, 0x21, 0xd9, //0x00001697 andq         %r11, %rcx
	0x48, 0xf7, 0xd1, //0x0000169a notq         %rcx
	0x48, 0x21, 0xcf, //0x0000169d andq         %rcx, %rdi
	0x4d, 0x89, 0xeb, //0x000016a0 movq         %r13, %r11
	0x4d, 0x89, 0xcd, //0x000016a3 movq         %r9, %r13
	0x4c, 0x8b, 0x4c, 0x24, 0x20, //0x000016a6 movq         $32(%rsp), %r9
	0x48, 0x85, 0xff, //0x000016ab testq        %rdi, %rdi
	0x0f, 0x84, 0x57, 0xff, 0xff, 0xff, //0x000016ae je           LBB0_270
	//0x000016b4 LBB0_280
	0x4c, 0x0f, 0xbc, 0xff, //0x000016b4 bsfq         %rdi, %r15
	0x49, 0x29, 0xf7, //0x000016b8 subq         %rsi, %r15
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x000016bb movq         $24(%rsp), %r10
	0xe9, 0x41, 0x06, 0x00, 0x00, //0x000016c0 jmp          LBB0_379
	//0x000016c5 LBB0_281
	0x4d, 0x8b, 0x55, 0x08, //0x000016c5 movq         $8(%r13), %r10
	0x4d, 0x8b, 0x3b, //0x000016c9 movq         (%r11), %r15
	0x4d, 0x29, 0xfa, //0x000016cc subq         %r15, %r10
	0x0f, 0x84, 0x9a, 0x1e, 0x00, 0x00, //0x000016cf je           LBB0_649
	0x4b, 0x8d, 0x04, 0x38, //0x000016d5 leaq         (%r8,%r15), %rax
	0x48, 0x89, 0x44, 0x24, 0x30, //0x000016d9 movq         %rax, $48(%rsp)
	0x80, 0x38, 0x30, //0x000016de cmpb         $48, (%rax)
	0x0f, 0x85, 0x9a, 0x02, 0x00, 0x00, //0x000016e1 jne          LBB0_324
	0x41, 0xbc, 0x01, 0x00, 0x00, 0x00, //0x000016e7 movl         $1, %r12d
	0x49, 0x83, 0xfa, 0x01, //0x000016ed cmpq         $1, %r10
	0x0f, 0x85, 0x5c, 0x02, 0x00, 0x00, //0x000016f1 jne          LBB0_322
	//0x000016f7 LBB0_284
	0x4c, 0x89, 0xf9, //0x000016f7 movq         %r15, %rcx
	0xe9, 0x08, 0x0b, 0x00, 0x00, //0x000016fa jmp          LBB0_425
	//0x000016ff LBB0_285
	0x49, 0x8b, 0x09, //0x000016ff movq         (%r9), %rcx
	0x48, 0x81, 0xf9, 0xff, 0x0f, 0x00, 0x00, //0x00001702 cmpq         $4095, %rcx
	0x0f, 0x8f, 0x99, 0x16, 0x00, 0x00, //0x00001709 jg           LBB0_650
	0x48, 0x8d, 0x41, 0x01, //0x0000170f leaq         $1(%rcx), %rax
	0x49, 0x89, 0x01, //0x00001713 movq         %rax, (%r9)
	0x49, 0xc7, 0x44, 0xc9, 0x08, 0x05, 0x00, 0x00, 0x00, //0x00001716 movq         $5, $8(%r9,%rcx,8)
	0xe9, 0xcc, 0xef, 0xff, 0xff, //0x0000171f jmp          LBB0_55
	//0x00001724 LBB0_287
	0x49, 0x8b, 0x0b, //0x00001724 movq         (%r11), %rcx
	0x49, 0x8b, 0x55, 0x08, //0x00001727 movq         $8(%r13), %rdx
	0x48, 0x8d, 0x42, 0xfc, //0x0000172b leaq         $-4(%rdx), %rax
	0x48, 0x39, 0xc1, //0x0000172f cmpq         %rax, %rcx
	0x0f, 0x87, 0x94, 0x16, 0x00, 0x00, //0x00001732 ja           LBB0_382
	0x41, 0x8b, 0x14, 0x08, //0x00001738 movl         (%r8,%rcx), %edx
	0x81, 0xfa, 0x61, 0x6c, 0x73, 0x65, //0x0000173c cmpl         $1702063201, %edx
	0x0f, 0x85, 0x33, 0x1e, 0x00, 0x00, //0x00001742 jne          LBB0_651
	0x48, 0x8d, 0x41, 0x04, //0x00001748 leaq         $4(%rcx), %rax
	0x49, 0x89, 0x03, //0x0000174c movq         %rax, (%r11)
	0x48, 0x85, 0xc9, //0x0000174f testq        %rcx, %rcx
	0x0f, 0x8f, 0x98, 0xef, 0xff, 0xff, //0x00001752 jg           LBB0_55
	0xe9, 0x08, 0x1f, 0x00, 0x00, //0x00001758 jmp          LBB0_290
	//0x0000175d LBB0_291
	0x49, 0x8b, 0x0b, //0x0000175d movq         (%r11), %rcx
	0x49, 0x8b, 0x55, 0x08, //0x00001760 movq         $8(%r13), %rdx
	0x48, 0x8d, 0x42, 0xfd, //0x00001764 leaq         $-3(%rdx), %rax
	0x48, 0x39, 0xc1, //0x00001768 cmpq         %rax, %rcx
	0x0f, 0x87, 0x5b, 0x16, 0x00, 0x00, //0x0000176b ja           LBB0_382
	0x4c, 0x8d, 0x79, 0xff, //0x00001771 leaq         $-1(%rcx), %r15
	0x41, 0x81, 0x7c, 0x08, 0xff, 0x6e, 0x75, 0x6c, 0x6c, //0x00001775 cmpl         $1819047278, $-1(%r8,%rcx)
	0x0f, 0x84, 0x51, 0x00, 0x00, 0x00, //0x0000177e je           LBB0_301
	0xe9, 0x47, 0x1e, 0x00, 0x00, //0x00001784 jmp          LBB0_293
	//0x00001789 LBB0_297
	0x49, 0x8b, 0x09, //0x00001789 movq         (%r9), %rcx
	0x48, 0x81, 0xf9, 0xff, 0x0f, 0x00, 0x00, //0x0000178c cmpq         $4095, %rcx
	0x0f, 0x8f, 0x0f, 0x16, 0x00, 0x00, //0x00001793 jg           LBB0_650
	0x48, 0x8d, 0x41, 0x01, //0x00001799 leaq         $1(%rcx), %rax
	0x49, 0x89, 0x01, //0x0000179d movq         %rax, (%r9)
	0x49, 0xc7, 0x44, 0xc9, 0x08, 0x06, 0x00, 0x00, 0x00, //0x000017a0 movq         $6, $8(%r9,%rcx,8)
	0xe9, 0x42, 0xef, 0xff, 0xff, //0x000017a9 jmp          LBB0_55
	//0x000017ae LBB0_299
	0x49, 0x8b, 0x0b, //0x000017ae movq         (%r11), %rcx
	0x49, 0x8b, 0x55, 0x08, //0x000017b1 movq         $8(%r13), %rdx
	0x48, 0x8d, 0x42, 0xfd, //0x000017b5 leaq         $-3(%rdx), %rax
	0x48, 0x39, 0xc1, //0x000017b9 cmpq         %rax, %rcx
	0x0f, 0x87, 0x0a, 0x16, 0x00, 0x00, //0x000017bc ja           LBB0_382
	0x4c, 0x8d, 0x79, 0xff, //0x000017c2 leaq         $-1(%rcx), %r15
	0x41, 0x81, 0x7c, 0x08, 0xff, 0x74, 0x72, 0x75, 0x65, //0x000017c6 cmpl         $1702195828, $-1(%r8,%rcx)
	0x0f, 0x85, 0x42, 0x1e, 0x00, 0x00, //0x000017cf jne          LBB0_656
	//0x000017d5 LBB0_301
	0x48, 0x8d, 0x41, 0x03, //0x000017d5 leaq         $3(%rcx), %rax
	0x49, 0x89, 0x03, //0x000017d9 movq         %rax, (%r11)
	0x48, 0x85, 0xc9, //0x000017dc testq        %rcx, %rcx
	0x0f, 0x8f, 0x0b, 0xef, 0xff, 0xff, //0x000017df jg           LBB0_55
	0xe9, 0xee, 0x1c, 0x00, 0x00, //0x000017e5 jmp          LBB0_638
	//0x000017ea LBB0_302
	0x49, 0x8d, 0x42, 0xff, //0x000017ea leaq         $-1(%r10), %rax
	0x48, 0x39, 0xc2, //0x000017ee cmpq         %rax, %rdx
	0x49, 0xf7, 0xd2, //0x000017f1 notq         %r10
	0x4d, 0x0f, 0x45, 0xd0, //0x000017f4 cmovneq      %r8, %r10
	0x84, 0xc9, //0x000017f8 testb        %cl, %cl
	0x4d, 0x0f, 0x45, 0xc2, //0x000017fa cmovneq      %r10, %r8
	0xe9, 0x9b, 0x07, 0x00, 0x00, //0x000017fe jmp          LBB0_407
	//0x00001803 LBB0_303
	0x4c, 0x29, 0xc3, //0x00001803 subq         %r8, %rbx
	0x0f, 0x84, 0x9c, 0x1e, 0x00, 0x00, //0x00001806 je           LBB0_670
	0x48, 0x83, 0xfb, 0x40, //0x0000180c cmpq         $64, %rbx
	0x0f, 0x82, 0x21, 0x0b, 0x00, 0x00, //0x00001810 jb           LBB0_439
	0x48, 0xc7, 0x04, 0x24, 0xff, 0xff, 0xff, 0xff, //0x00001816 movq         $-1, (%rsp)
	0x4d, 0x89, 0xc7, //0x0000181e movq         %r8, %r15
	0x45, 0x31, 0xdb, //0x00001821 xorl         %r11d, %r11d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001824 .p2align 4, 0x90
	//0x00001830 LBB0_306
	0x48, 0x8b, 0x44, 0x24, 0x10, //0x00001830 movq         $16(%rsp), %rax
	0xc4, 0xa1, 0x7e, 0x6f, 0x04, 0x38, //0x00001835 vmovdqu      (%rax,%r15), %ymm0
	0xc4, 0xa1, 0x7e, 0x6f, 0x4c, 0x38, 0x20, //0x0000183b vmovdqu      $32(%rax,%r15), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x00001842 vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xfa, //0x00001846 vpmovmskb    %ymm2, %edi
	0xc5, 0xf5, 0x74, 0xd6, //0x0000184a vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xc2, //0x0000184e vpmovmskb    %ymm2, %eax
	0xc5, 0xfd, 0x74, 0xd7, //0x00001852 vpcmpeqb     %ymm7, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xd2, //0x00001856 vpmovmskb    %ymm2, %edx
	0xc5, 0xf5, 0x74, 0xd7, //0x0000185a vpcmpeqb     %ymm7, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xca, //0x0000185e vpmovmskb    %ymm2, %ecx
	0xc5, 0xbd, 0x64, 0xd1, //0x00001862 vpcmpgtb     %ymm1, %ymm8, %ymm2
	0xc4, 0xc1, 0x75, 0x64, 0xc9, //0x00001866 vpcmpgtb     %ymm9, %ymm1, %ymm1
	0xc5, 0xed, 0xdb, 0xc9, //0x0000186b vpand        %ymm1, %ymm2, %ymm1
	0xc5, 0xfd, 0xd7, 0xf1, //0x0000186f vpmovmskb    %ymm1, %esi
	0x48, 0xc1, 0xe0, 0x20, //0x00001873 shlq         $32, %rax
	0x48, 0x09, 0xc7, //0x00001877 orq          %rax, %rdi
	0x48, 0xc1, 0xe1, 0x20, //0x0000187a shlq         $32, %rcx
	0x48, 0xc1, 0xe6, 0x20, //0x0000187e shlq         $32, %rsi
	0x48, 0x09, 0xca, //0x00001882 orq          %rcx, %rdx
	0x0f, 0x85, 0x49, 0x00, 0x00, 0x00, //0x00001885 jne          LBB0_317
	0x4d, 0x85, 0xdb, //0x0000188b testq        %r11, %r11
	0x0f, 0x85, 0x56, 0x00, 0x00, 0x00, //0x0000188e jne          LBB0_319
	0x45, 0x31, 0xdb, //0x00001894 xorl         %r11d, %r11d
	//0x00001897 LBB0_309
	0xc5, 0xbd, 0x64, 0xc8, //0x00001897 vpcmpgtb     %ymm0, %ymm8, %ymm1
	0xc4, 0xc1, 0x7d, 0x64, 0xc1, //0x0000189b vpcmpgtb     %ymm9, %ymm0, %ymm0
	0xc5, 0xf5, 0xdb, 0xc0, //0x000018a0 vpand        %ymm0, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xc0, //0x000018a4 vpmovmskb    %ymm0, %eax
	0x48, 0x09, 0xc6, //0x000018a8 orq          %rax, %rsi
	0x48, 0x85, 0xff, //0x000018ab testq        %rdi, %rdi
	0x0f, 0x85, 0x89, 0x00, 0x00, 0x00, //0x000018ae jne          LBB0_320
	0x48, 0x85, 0xf6, //0x000018b4 testq        %rsi, %rsi
	0x0f, 0x85, 0x7a, 0x1c, 0x00, 0x00, //0x000018b7 jne          LBB0_643
	0x48, 0x83, 0xc3, 0xc0, //0x000018bd addq         $-64, %rbx
	0x49, 0x83, 0xc7, 0x40, //0x000018c1 addq         $64, %r15
	0x48, 0x83, 0xfb, 0x3f, //0x000018c5 cmpq         $63, %rbx
	0x0f, 0x87, 0x61, 0xff, 0xff, 0xff, //0x000018c9 ja           LBB0_306
	0xe9, 0x57, 0x08, 0x00, 0x00, //0x000018cf jmp          LBB0_312
	//0x000018d4 LBB0_317
	0x48, 0x83, 0x3c, 0x24, 0xff, //0x000018d4 cmpq         $-1, (%rsp)
	0x0f, 0x85, 0x0b, 0x00, 0x00, 0x00, //0x000018d9 jne          LBB0_319
	0x48, 0x0f, 0xbc, 0xc2, //0x000018df bsfq         %rdx, %rax
	0x4c, 0x01, 0xf8, //0x000018e3 addq         %r15, %rax
	0x48, 0x89, 0x04, 0x24, //0x000018e6 movq         %rax, (%rsp)
	//0x000018ea LBB0_319
	0x4c, 0x89, 0xd8, //0x000018ea movq         %r11, %rax
	0x48, 0xf7, 0xd0, //0x000018ed notq         %rax
	0x48, 0x21, 0xd0, //0x000018f0 andq         %rdx, %rax
	0x4c, 0x8d, 0x14, 0x00, //0x000018f3 leaq         (%rax,%rax), %r10
	0x4d, 0x09, 0xda, //0x000018f7 orq          %r11, %r10
	0x4c, 0x89, 0xd1, //0x000018fa movq         %r10, %rcx
	0x48, 0xf7, 0xd1, //0x000018fd notq         %rcx
	0x48, 0x21, 0xd1, //0x00001900 andq         %rdx, %rcx
	0x48, 0xba, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001903 movabsq      $-6148914691236517206, %rdx
	0x48, 0x21, 0xd1, //0x0000190d andq         %rdx, %rcx
	0x45, 0x31, 0xdb, //0x00001910 xorl         %r11d, %r11d
	0x48, 0x01, 0xc1, //0x00001913 addq         %rax, %rcx
	0x41, 0x0f, 0x92, 0xc3, //0x00001916 setb         %r11b
	0x48, 0x01, 0xc9, //0x0000191a addq         %rcx, %rcx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x0000191d movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc1, //0x00001927 xorq         %rax, %rcx
	0x4c, 0x21, 0xd1, //0x0000192a andq         %r10, %rcx
	0x48, 0xf7, 0xd1, //0x0000192d notq         %rcx
	0x48, 0x21, 0xcf, //0x00001930 andq         %rcx, %rdi
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x00001933 movq         $24(%rsp), %r10
	0xe9, 0x5a, 0xff, 0xff, 0xff, //0x00001938 jmp          LBB0_309
	//0x0000193d LBB0_320
	0x48, 0x0f, 0xbc, 0xcf, //0x0000193d bsfq         %rdi, %rcx
	0x48, 0x85, 0xf6, //0x00001941 testq        %rsi, %rsi
	0x0f, 0x84, 0xa2, 0x03, 0x00, 0x00, //0x00001944 je           LBB0_376
	0x48, 0x0f, 0xbc, 0xd6, //0x0000194a bsfq         %rsi, %rdx
	0xe9, 0x9e, 0x03, 0x00, 0x00, //0x0000194e jmp          LBB0_377
	//0x00001953 LBB0_322
	0x48, 0x8b, 0x44, 0x24, 0x30, //0x00001953 movq         $48(%rsp), %rax
	0x8a, 0x48, 0x01, //0x00001958 movb         $1(%rax), %cl
	0x80, 0xc1, 0xd2, //0x0000195b addb         $-46, %cl
	0x80, 0xf9, 0x37, //0x0000195e cmpb         $55, %cl
	0x0f, 0x87, 0x90, 0xfd, 0xff, 0xff, //0x00001961 ja           LBB0_284
	0x0f, 0xb6, 0xc1, //0x00001967 movzbl       %cl, %eax
	0x48, 0xb9, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x0000196a movabsq      $36028797027352577, %rcx
	0x48, 0x0f, 0xa3, 0xc1, //0x00001974 btq          %rax, %rcx
	0x4c, 0x89, 0xf9, //0x00001978 movq         %r15, %rcx
	0x0f, 0x83, 0x86, 0x08, 0x00, 0x00, //0x0000197b jae          LBB0_425
	//0x00001981 LBB0_324
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00001981 movq         $-1, %r8
	0x49, 0x83, 0xfa, 0x20, //0x00001988 cmpq         $32, %r10
	0x0f, 0x82, 0x5a, 0x09, 0x00, 0x00, //0x0000198c jb           LBB0_437
	0x45, 0x31, 0xe4, //0x00001992 xorl         %r12d, %r12d
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00001995 movq         $-1, %r11
	0x48, 0xc7, 0x04, 0x24, 0xff, 0xff, 0xff, 0xff, //0x0000199c movq         $-1, (%rsp)
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000019a4 .p2align 4, 0x90
	//0x000019b0 LBB0_326
	0x48, 0x8b, 0x44, 0x24, 0x30, //0x000019b0 movq         $48(%rsp), %rax
	0xc4, 0xa1, 0x7e, 0x6f, 0x04, 0x20, //0x000019b5 vmovdqu      (%rax,%r12), %ymm0
	0xc4, 0xc1, 0x7d, 0x64, 0xca, //0x000019bb vpcmpgtb     %ymm10, %ymm0, %ymm1
	0xc5, 0xa5, 0x64, 0xd0, //0x000019c0 vpcmpgtb     %ymm0, %ymm11, %ymm2
	0xc5, 0xed, 0xdb, 0xc9, //0x000019c4 vpand        %ymm1, %ymm2, %ymm1
	0xc5, 0x9d, 0x74, 0xd0, //0x000019c8 vpcmpeqb     %ymm0, %ymm12, %ymm2
	0xc5, 0x95, 0x74, 0xd8, //0x000019cc vpcmpeqb     %ymm0, %ymm13, %ymm3
	0xc5, 0xe5, 0xeb, 0xd2, //0x000019d0 vpor         %ymm2, %ymm3, %ymm2
	0xc5, 0x8d, 0xdb, 0xd8, //0x000019d4 vpand        %ymm0, %ymm14, %ymm3
	0xc5, 0x85, 0x74, 0xc0, //0x000019d8 vpcmpeqb     %ymm0, %ymm15, %ymm0
	0xc5, 0xfd, 0xd7, 0xf8, //0x000019dc vpmovmskb    %ymm0, %edi
	0xc5, 0xe5, 0x74, 0xdc, //0x000019e0 vpcmpeqb     %ymm4, %ymm3, %ymm3
	0xc5, 0xfd, 0xd7, 0xd3, //0x000019e4 vpmovmskb    %ymm3, %edx
	0xc5, 0xfd, 0xd7, 0xf2, //0x000019e8 vpmovmskb    %ymm2, %esi
	0xc5, 0xf5, 0xeb, 0xc0, //0x000019ec vpor         %ymm0, %ymm1, %ymm0
	0xc5, 0xe5, 0xeb, 0xca, //0x000019f0 vpor         %ymm2, %ymm3, %ymm1
	0xc5, 0xfd, 0xeb, 0xc1, //0x000019f4 vpor         %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xc0, //0x000019f8 vpmovmskb    %ymm0, %eax
	0x48, 0xf7, 0xd0, //0x000019fc notq         %rax
	0x48, 0x0f, 0xbc, 0xc8, //0x000019ff bsfq         %rax, %rcx
	0x83, 0xf9, 0x20, //0x00001a03 cmpl         $32, %ecx
	0x0f, 0x84, 0x11, 0x00, 0x00, 0x00, //0x00001a06 je           LBB0_328
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x00001a0c movl         $-1, %eax
	0xd3, 0xe0, //0x00001a11 shll         %cl, %eax
	0xf7, 0xd0, //0x00001a13 notl         %eax
	0x21, 0xc7, //0x00001a15 andl         %eax, %edi
	0x21, 0xc2, //0x00001a17 andl         %eax, %edx
	0x21, 0xf0, //0x00001a19 andl         %esi, %eax
	0x89, 0xc6, //0x00001a1b movl         %eax, %esi
	//0x00001a1d LBB0_328
	0x8d, 0x5f, 0xff, //0x00001a1d leal         $-1(%rdi), %ebx
	0x21, 0xfb, //0x00001a20 andl         %edi, %ebx
	0x0f, 0x85, 0x67, 0x06, 0x00, 0x00, //0x00001a22 jne          LBB0_412
	0x8d, 0x5a, 0xff, //0x00001a28 leal         $-1(%rdx), %ebx
	0x21, 0xd3, //0x00001a2b andl         %edx, %ebx
	0x0f, 0x85, 0x5c, 0x06, 0x00, 0x00, //0x00001a2d jne          LBB0_412
	0x8d, 0x5e, 0xff, //0x00001a33 leal         $-1(%rsi), %ebx
	0x21, 0xf3, //0x00001a36 andl         %esi, %ebx
	0x0f, 0x85, 0x51, 0x06, 0x00, 0x00, //0x00001a38 jne          LBB0_412
	0x85, 0xff, //0x00001a3e testl        %edi, %edi
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x00001a40 je           LBB0_334
	0x0f, 0xbc, 0xff, //0x00001a46 bsfl         %edi, %edi
	0x48, 0x83, 0x3c, 0x24, 0xff, //0x00001a49 cmpq         $-1, (%rsp)
	0x0f, 0x85, 0x9a, 0x07, 0x00, 0x00, //0x00001a4e jne          LBB0_420
	0x4c, 0x01, 0xe7, //0x00001a54 addq         %r12, %rdi
	0x48, 0x89, 0x3c, 0x24, //0x00001a57 movq         %rdi, (%rsp)
	//0x00001a5b LBB0_334
	0x85, 0xd2, //0x00001a5b testl        %edx, %edx
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x00001a5d je           LBB0_337
	0x0f, 0xbc, 0xd2, //0x00001a63 bsfl         %edx, %edx
	0x49, 0x83, 0xfb, 0xff, //0x00001a66 cmpq         $-1, %r11
	0x0f, 0x85, 0x4c, 0x07, 0x00, 0x00, //0x00001a6a jne          LBB0_418
	0x4c, 0x01, 0xe2, //0x00001a70 addq         %r12, %rdx
	0x49, 0x89, 0xd3, //0x00001a73 movq         %rdx, %r11
	//0x00001a76 LBB0_337
	0x85, 0xf6, //0x00001a76 testl        %esi, %esi
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x00001a78 je           LBB0_340
	0x0f, 0xbc, 0xd6, //0x00001a7e bsfl         %esi, %edx
	0x49, 0x83, 0xf8, 0xff, //0x00001a81 cmpq         $-1, %r8
	0x0f, 0x85, 0x31, 0x07, 0x00, 0x00, //0x00001a85 jne          LBB0_418
	0x4c, 0x01, 0xe2, //0x00001a8b addq         %r12, %rdx
	0x49, 0x89, 0xd0, //0x00001a8e movq         %rdx, %r8
	//0x00001a91 LBB0_340
	0x83, 0xf9, 0x20, //0x00001a91 cmpl         $32, %ecx
	0x0f, 0x85, 0x0f, 0x02, 0x00, 0x00, //0x00001a94 jne          LBB0_374
	0x49, 0x83, 0xc2, 0xe0, //0x00001a9a addq         $-32, %r10
	0x49, 0x83, 0xc4, 0x20, //0x00001a9e addq         $32, %r12
	0x49, 0x83, 0xfa, 0x1f, //0x00001aa2 cmpq         $31, %r10
	0x0f, 0x87, 0x04, 0xff, 0xff, 0xff, //0x00001aa6 ja           LBB0_326
	0xc5, 0xf8, 0x77, //0x00001aac vzeroupper   
	0x4c, 0x03, 0x64, 0x24, 0x30, //0x00001aaf addq         $48(%rsp), %r12
	0x4c, 0x89, 0x6c, 0x24, 0x38, //0x00001ab4 movq         %r13, $56(%rsp)
	0x49, 0x83, 0xfa, 0x10, //0x00001ab9 cmpq         $16, %r10
	0x0f, 0x82, 0x3a, 0x01, 0x00, 0x00, //0x00001abd jb           LBB0_361
	//0x00001ac3 LBB0_343
	0x4c, 0x89, 0xe3, //0x00001ac3 movq         %r12, %rbx
	0x48, 0x2b, 0x5c, 0x24, 0x30, //0x00001ac6 subq         $48(%rsp), %rbx
	0x45, 0x31, 0xed, //0x00001acb xorl         %r13d, %r13d
	0x4c, 0x89, 0x4c, 0x24, 0x20, //0x00001ace movq         %r9, $32(%rsp)
	//0x00001ad3 LBB0_344
	0xc4, 0x81, 0x7a, 0x6f, 0x04, 0x2c, //0x00001ad3 vmovdqu      (%r12,%r13), %xmm0
	0xc5, 0xf9, 0x64, 0x0d, 0x5f, 0xe7, 0xff, 0xff, //0x00001ad9 vpcmpgtb     $-6305(%rip), %xmm0, %xmm1  /* LCPI0_20+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x15, 0x67, 0xe7, 0xff, 0xff, //0x00001ae1 vmovdqu      $-6297(%rip), %xmm2  /* LCPI0_21+0(%rip) */
	0xc5, 0xe9, 0x64, 0xd0, //0x00001ae9 vpcmpgtb     %xmm0, %xmm2, %xmm2
	0xc5, 0xf1, 0xdb, 0xca, //0x00001aed vpand        %xmm2, %xmm1, %xmm1
	0xc5, 0xf9, 0x74, 0x15, 0x67, 0xe7, 0xff, 0xff, //0x00001af1 vpcmpeqb     $-6297(%rip), %xmm0, %xmm2  /* LCPI0_22+0(%rip) */
	0xc5, 0xf9, 0x74, 0x1d, 0x6f, 0xe7, 0xff, 0xff, //0x00001af9 vpcmpeqb     $-6289(%rip), %xmm0, %xmm3  /* LCPI0_23+0(%rip) */
	0xc5, 0xe1, 0xeb, 0xd2, //0x00001b01 vpor         %xmm2, %xmm3, %xmm2
	0xc5, 0xf9, 0xdb, 0x1d, 0x03, 0xe7, 0xff, 0xff, //0x00001b05 vpand        $-6397(%rip), %xmm0, %xmm3  /* LCPI0_5+0(%rip) */
	0xc5, 0xf9, 0x74, 0x05, 0x6b, 0xe7, 0xff, 0xff, //0x00001b0d vpcmpeqb     $-6293(%rip), %xmm0, %xmm0  /* LCPI0_24+0(%rip) */
	0xc5, 0xe1, 0x74, 0x1d, 0x73, 0xe7, 0xff, 0xff, //0x00001b15 vpcmpeqb     $-6285(%rip), %xmm3, %xmm3  /* LCPI0_25+0(%rip) */
	0xc5, 0xe1, 0xeb, 0xe0, //0x00001b1d vpor         %xmm0, %xmm3, %xmm4
	0xc5, 0xe9, 0xeb, 0xc9, //0x00001b21 vpor         %xmm1, %xmm2, %xmm1
	0xc5, 0xd9, 0xeb, 0xc9, //0x00001b25 vpor         %xmm1, %xmm4, %xmm1
	0xc5, 0x79, 0xd7, 0xc8, //0x00001b29 vpmovmskb    %xmm0, %r9d
	0xc5, 0xf9, 0xd7, 0xfb, //0x00001b2d vpmovmskb    %xmm3, %edi
	0xc5, 0xf9, 0xd7, 0xd2, //0x00001b31 vpmovmskb    %xmm2, %edx
	0xc5, 0xf9, 0xd7, 0xc1, //0x00001b35 vpmovmskb    %xmm1, %eax
	0xf7, 0xd0, //0x00001b39 notl         %eax
	0x0f, 0xbc, 0xc8, //0x00001b3b bsfl         %eax, %ecx
	0x83, 0xf9, 0x10, //0x00001b3e cmpl         $16, %ecx
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00001b41 je           LBB0_346
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x00001b47 movl         $-1, %eax
	0xd3, 0xe0, //0x00001b4c shll         %cl, %eax
	0xf7, 0xd0, //0x00001b4e notl         %eax
	0x41, 0x21, 0xc1, //0x00001b50 andl         %eax, %r9d
	0x21, 0xc7, //0x00001b53 andl         %eax, %edi
	0x21, 0xd0, //0x00001b55 andl         %edx, %eax
	0x89, 0xc2, //0x00001b57 movl         %eax, %edx
	//0x00001b59 LBB0_346
	0x41, 0x8d, 0x71, 0xff, //0x00001b59 leal         $-1(%r9), %esi
	0x44, 0x21, 0xce, //0x00001b5d andl         %r9d, %esi
	0x0f, 0x85, 0x0f, 0x06, 0x00, 0x00, //0x00001b60 jne          LBB0_415
	0x8d, 0x77, 0xff, //0x00001b66 leal         $-1(%rdi), %esi
	0x21, 0xfe, //0x00001b69 andl         %edi, %esi
	0x0f, 0x85, 0x04, 0x06, 0x00, 0x00, //0x00001b6b jne          LBB0_415
	0x8d, 0x72, 0xff, //0x00001b71 leal         $-1(%rdx), %esi
	0x21, 0xd6, //0x00001b74 andl         %edx, %esi
	0x0f, 0x85, 0xf9, 0x05, 0x00, 0x00, //0x00001b76 jne          LBB0_415
	0x45, 0x85, 0xc9, //0x00001b7c testl        %r9d, %r9d
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x00001b7f je           LBB0_352
	0x41, 0x0f, 0xbc, 0xf1, //0x00001b85 bsfl         %r9d, %esi
	0x48, 0x83, 0x3c, 0x24, 0xff, //0x00001b89 cmpq         $-1, (%rsp)
	0x0f, 0x85, 0xd7, 0x06, 0x00, 0x00, //0x00001b8e jne          LBB0_430
	0x48, 0x01, 0xde, //0x00001b94 addq         %rbx, %rsi
	0x4c, 0x01, 0xee, //0x00001b97 addq         %r13, %rsi
	0x48, 0x89, 0x34, 0x24, //0x00001b9a movq         %rsi, (%rsp)
	//0x00001b9e LBB0_352
	0x4c, 0x8b, 0x4c, 0x24, 0x20, //0x00001b9e movq         $32(%rsp), %r9
	0x85, 0xff, //0x00001ba3 testl        %edi, %edi
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00001ba5 je           LBB0_355
	0x0f, 0xbc, 0xf7, //0x00001bab bsfl         %edi, %esi
	0x49, 0x83, 0xfb, 0xff, //0x00001bae cmpq         $-1, %r11
	0x0f, 0x85, 0xba, 0x06, 0x00, 0x00, //0x00001bb2 jne          LBB0_431
	0x48, 0x01, 0xde, //0x00001bb8 addq         %rbx, %rsi
	0x4c, 0x01, 0xee, //0x00001bbb addq         %r13, %rsi
	0x49, 0x89, 0xf3, //0x00001bbe movq         %rsi, %r11
	//0x00001bc1 LBB0_355
	0x85, 0xd2, //0x00001bc1 testl        %edx, %edx
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00001bc3 je           LBB0_358
	0x0f, 0xbc, 0xd2, //0x00001bc9 bsfl         %edx, %edx
	0x49, 0x83, 0xf8, 0xff, //0x00001bcc cmpq         $-1, %r8
	0x0f, 0x85, 0xa3, 0x06, 0x00, 0x00, //0x00001bd0 jne          LBB0_432
	0x48, 0x01, 0xda, //0x00001bd6 addq         %rbx, %rdx
	0x4c, 0x01, 0xea, //0x00001bd9 addq         %r13, %rdx
	0x49, 0x89, 0xd0, //0x00001bdc movq         %rdx, %r8
	//0x00001bdf LBB0_358
	0x83, 0xf9, 0x10, //0x00001bdf cmpl         $16, %ecx
	0x0f, 0x85, 0x34, 0x01, 0x00, 0x00, //0x00001be2 jne          LBB0_384
	0x49, 0x83, 0xc2, 0xf0, //0x00001be8 addq         $-16, %r10
	0x49, 0x83, 0xc5, 0x10, //0x00001bec addq         $16, %r13
	0x49, 0x83, 0xfa, 0x0f, //0x00001bf0 cmpq         $15, %r10
	0x0f, 0x87, 0xd9, 0xfe, 0xff, 0xff, //0x00001bf4 ja           LBB0_344
	0x4d, 0x01, 0xec, //0x00001bfa addq         %r13, %r12
	//0x00001bfd LBB0_361
	0x4d, 0x85, 0xd2, //0x00001bfd testq        %r10, %r10
	0x4c, 0x8b, 0x6c, 0x24, 0x38, //0x00001c00 movq         $56(%rsp), %r13
	0x0f, 0x84, 0x3a, 0x01, 0x00, 0x00, //0x00001c05 je           LBB0_386
	0x4b, 0x8d, 0x14, 0x14, //0x00001c0b leaq         (%r12,%r10), %rdx
	0x4c, 0x89, 0xe6, //0x00001c0f movq         %r12, %rsi
	0x48, 0x2b, 0x74, 0x24, 0x30, //0x00001c12 subq         $48(%rsp), %rsi
	0x31, 0xc9, //0x00001c17 xorl         %ecx, %ecx
	0xe9, 0x2f, 0x00, 0x00, 0x00, //0x00001c19 jmp          LBB0_366
	//0x00001c1e LBB0_363
	0x48, 0x83, 0x3c, 0x24, 0xff, //0x00001c1e cmpq         $-1, (%rsp)
	0x0f, 0x85, 0x7a, 0x05, 0x00, 0x00, //0x00001c23 jne          LBB0_417
	0x48, 0x8d, 0x04, 0x0e, //0x00001c29 leaq         (%rsi,%rcx), %rax
	0x48, 0x89, 0x04, 0x24, //0x00001c2d movq         %rax, (%rsp)
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001c31 .p2align 4, 0x90
	//0x00001c40 LBB0_365
	0x48, 0x83, 0xc1, 0x01, //0x00001c40 addq         $1, %rcx
	0x49, 0x39, 0xca, //0x00001c44 cmpq         %rcx, %r10
	0x0f, 0x84, 0x26, 0x04, 0x00, 0x00, //0x00001c47 je           LBB0_411
	//0x00001c4d LBB0_366
	0x41, 0x0f, 0xbe, 0x3c, 0x0c, //0x00001c4d movsbl       (%r12,%rcx), %edi
	0x8d, 0x47, 0xd0, //0x00001c52 leal         $-48(%rdi), %eax
	0x83, 0xf8, 0x0a, //0x00001c55 cmpl         $10, %eax
	0x0f, 0x82, 0xe2, 0xff, 0xff, 0xff, //0x00001c58 jb           LBB0_365
	0x8d, 0x5f, 0xd5, //0x00001c5e leal         $-43(%rdi), %ebx
	0x83, 0xfb, 0x1a, //0x00001c61 cmpl         $26, %ebx
	0x0f, 0x87, 0x23, 0x00, 0x00, 0x00, //0x00001c64 ja           LBB0_371
	0x48, 0x8d, 0x3d, 0x23, 0x1f, 0x00, 0x00, //0x00001c6a leaq         $7971(%rip), %rdi  /* LJTI0_3+0(%rip) */
	0x48, 0x63, 0x04, 0x9f, //0x00001c71 movslq       (%rdi,%rbx,4), %rax
	0x48, 0x01, 0xf8, //0x00001c75 addq         %rdi, %rax
	0xff, 0xe0, //0x00001c78 jmpq         *%rax
	//0x00001c7a LBB0_369
	0x49, 0x83, 0xf8, 0xff, //0x00001c7a cmpq         $-1, %r8
	0x0f, 0x85, 0x1f, 0x05, 0x00, 0x00, //0x00001c7e jne          LBB0_417
	0x4c, 0x8d, 0x04, 0x0e, //0x00001c84 leaq         (%rsi,%rcx), %r8
	0xe9, 0xb3, 0xff, 0xff, 0xff, //0x00001c88 jmp          LBB0_365
	//0x00001c8d LBB0_371
	0x83, 0xff, 0x65, //0x00001c8d cmpl         $101, %edi
	0x0f, 0x85, 0xac, 0x00, 0x00, 0x00, //0x00001c90 jne          LBB0_385
	//0x00001c96 LBB0_372
	0x49, 0x83, 0xfb, 0xff, //0x00001c96 cmpq         $-1, %r11
	0x0f, 0x85, 0x03, 0x05, 0x00, 0x00, //0x00001c9a jne          LBB0_417
	0x4c, 0x8d, 0x1c, 0x0e, //0x00001ca0 leaq         (%rsi,%rcx), %r11
	0xe9, 0x97, 0xff, 0xff, 0xff, //0x00001ca4 jmp          LBB0_365
	//0x00001ca9 LBB0_374
	0x4c, 0x01, 0xe1, //0x00001ca9 addq         %r12, %rcx
	0x48, 0x03, 0x4c, 0x24, 0x30, //0x00001cac addq         $48(%rsp), %rcx
	0xc5, 0xf8, 0x77, //0x00001cb1 vzeroupper   
	0x49, 0x89, 0xcc, //0x00001cb4 movq         %rcx, %r12
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00001cb7 movq         $-1, %rcx
	0x48, 0x8b, 0x14, 0x24, //0x00001cbe movq         (%rsp), %rdx
	0x48, 0x85, 0xd2, //0x00001cc2 testq        %rdx, %rdx
	0x0f, 0x85, 0x8e, 0x00, 0x00, 0x00, //0x00001cc5 jne          LBB0_387
	0xe9, 0xad, 0x19, 0x00, 0x00, //0x00001ccb jmp          LBB0_662
	//0x00001cd0 LBB0_375
	0x49, 0x89, 0xf0, //0x00001cd0 movq         %rsi, %r8
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00001cd3 movq         $-1, %rcx
	0x48, 0x8b, 0x14, 0x24, //0x00001cda movq         (%rsp), %rdx
	0x48, 0x85, 0xd2, //0x00001cde testq        %rdx, %rdx
	0x0f, 0x85, 0x61, 0xf7, 0xff, 0xff, //0x00001ce1 jne          LBB0_246
	0xe9, 0xd3, 0x17, 0x00, 0x00, //0x00001ce7 jmp          LBB0_636
	//0x00001cec LBB0_376
	0xba, 0x40, 0x00, 0x00, 0x00, //0x00001cec movl         $64, %edx
	//0x00001cf1 LBB0_377
	0x4c, 0x8b, 0x5c, 0x24, 0x08, //0x00001cf1 movq         $8(%rsp), %r11
	0x48, 0x39, 0xca, //0x00001cf6 cmpq         %rcx, %rdx
	0x0f, 0x82, 0xc4, 0x19, 0x00, 0x00, //0x00001cf9 jb           LBB0_217
	0x49, 0x01, 0xcf, //0x00001cff addq         %rcx, %r15
	0x49, 0x83, 0xc7, 0x01, //0x00001d02 addq         $1, %r15
	//0x00001d06 LBB0_379
	0x4d, 0x85, 0xff, //0x00001d06 testq        %r15, %r15
	0x0f, 0x89, 0xeb, 0xf6, 0xff, 0xff, //0x00001d09 jns          LBB0_241
	0xe9, 0xa0, 0x10, 0x00, 0x00, //0x00001d0f jmp          LBB0_380
	//0x00001d14 LBB0_383
	0x0f, 0xbc, 0xc3, //0x00001d14 bsfl         %ebx, %eax
	0xe9, 0x9a, 0x01, 0x00, 0x00, //0x00001d17 jmp          LBB0_403
	//0x00001d1c LBB0_384
	0x89, 0xc8, //0x00001d1c movl         %ecx, %eax
	0x49, 0x01, 0xc4, //0x00001d1e addq         %rax, %r12
	0x4d, 0x01, 0xec, //0x00001d21 addq         %r13, %r12
	0x4c, 0x8b, 0x6c, 0x24, 0x38, //0x00001d24 movq         $56(%rsp), %r13
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00001d29 movq         $-1, %rcx
	0x48, 0x8b, 0x14, 0x24, //0x00001d30 movq         (%rsp), %rdx
	0x48, 0x85, 0xd2, //0x00001d34 testq        %rdx, %rdx
	0x0f, 0x85, 0x1c, 0x00, 0x00, 0x00, //0x00001d37 jne          LBB0_387
	0xe9, 0x3b, 0x19, 0x00, 0x00, //0x00001d3d jmp          LBB0_662
	//0x00001d42 LBB0_385
	0x49, 0x01, 0xcc, //0x00001d42 addq         %rcx, %r12
	//0x00001d45 LBB0_386
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00001d45 movq         $-1, %rcx
	0x48, 0x8b, 0x14, 0x24, //0x00001d4c movq         (%rsp), %rdx
	0x48, 0x85, 0xd2, //0x00001d50 testq        %rdx, %rdx
	0x0f, 0x84, 0x24, 0x19, 0x00, 0x00, //0x00001d53 je           LBB0_662
	//0x00001d59 LBB0_387
	0x4d, 0x85, 0xc0, //0x00001d59 testq        %r8, %r8
	0x0f, 0x84, 0x1b, 0x19, 0x00, 0x00, //0x00001d5c je           LBB0_662
	0x4d, 0x85, 0xdb, //0x00001d62 testq        %r11, %r11
	0x0f, 0x84, 0x12, 0x19, 0x00, 0x00, //0x00001d65 je           LBB0_662
	0x4c, 0x2b, 0x64, 0x24, 0x30, //0x00001d6b subq         $48(%rsp), %r12
	0x49, 0x8d, 0x4c, 0x24, 0xff, //0x00001d70 leaq         $-1(%r12), %rcx
	0x48, 0x39, 0xca, //0x00001d75 cmpq         %rcx, %rdx
	0x0f, 0x84, 0x90, 0x00, 0x00, 0x00, //0x00001d78 je           LBB0_395
	0x49, 0x39, 0xc8, //0x00001d7e cmpq         %rcx, %r8
	0x0f, 0x84, 0x87, 0x00, 0x00, 0x00, //0x00001d81 je           LBB0_395
	0x49, 0x39, 0xcb, //0x00001d87 cmpq         %rcx, %r11
	0x0f, 0x84, 0x7e, 0x00, 0x00, 0x00, //0x00001d8a je           LBB0_395
	0x4d, 0x85, 0xc0, //0x00001d90 testq        %r8, %r8
	0xc5, 0xfe, 0x6f, 0x2d, 0x65, 0xe2, 0xff, 0xff, //0x00001d93 vmovdqu      $-7579(%rip), %ymm5  /* LCPI0_0+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0xdd, 0xe2, 0xff, 0xff, //0x00001d9b vmovdqu      $-7459(%rip), %ymm6  /* LCPI0_7+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0xf5, 0xe2, 0xff, 0xff, //0x00001da3 vmovdqu      $-7435(%rip), %ymm7  /* LCPI0_8+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0x6d, 0xe3, 0xff, 0xff, //0x00001dab vmovdqu      $-7315(%rip), %ymm8  /* LCPI0_13+0(%rip) */
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00001db3 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0x7e, 0x6f, 0x15, 0x80, 0xe3, 0xff, 0xff, //0x00001db8 vmovdqu      $-7296(%rip), %ymm10  /* LCPI0_14+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0x98, 0xe3, 0xff, 0xff, //0x00001dc0 vmovdqu      $-7272(%rip), %ymm11  /* LCPI0_15+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0xb0, 0xe3, 0xff, 0xff, //0x00001dc8 vmovdqu      $-7248(%rip), %ymm12  /* LCPI0_16+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0xc8, 0xe3, 0xff, 0xff, //0x00001dd0 vmovdqu      $-7224(%rip), %ymm13  /* LCPI0_17+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0x60, 0xe2, 0xff, 0xff, //0x00001dd8 vmovdqu      $-7584(%rip), %ymm14  /* LCPI0_2+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x3d, 0xd8, 0xe3, 0xff, 0xff, //0x00001de0 vmovdqu      $-7208(%rip), %ymm15  /* LCPI0_18+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0xf0, 0xe3, 0xff, 0xff, //0x00001de8 vmovdqu      $-7184(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0x0f, 0x8e, 0x82, 0x00, 0x00, 0x00, //0x00001df0 jle          LBB0_398
	0x49, 0x8d, 0x40, 0xff, //0x00001df6 leaq         $-1(%r8), %rax
	0x49, 0x39, 0xc3, //0x00001dfa cmpq         %rax, %r11
	0x0f, 0x84, 0x75, 0x00, 0x00, 0x00, //0x00001dfd je           LBB0_398
	0x49, 0xf7, 0xd0, //0x00001e03 notq         %r8
	0x4d, 0x89, 0xc4, //0x00001e06 movq         %r8, %r12
	0xe9, 0xe8, 0x03, 0x00, 0x00, //0x00001e09 jmp          LBB0_422
	//0x00001e0e LBB0_395
	0x49, 0xf7, 0xdc, //0x00001e0e negq         %r12
	//0x00001e11 LBB0_396
	0x4c, 0x8b, 0x5c, 0x24, 0x08, //0x00001e11 movq         $8(%rsp), %r11
	//0x00001e16 LBB0_397
	0xc5, 0xfe, 0x6f, 0x2d, 0xe2, 0xe1, 0xff, 0xff, //0x00001e16 vmovdqu      $-7710(%rip), %ymm5  /* LCPI0_0+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0x5a, 0xe2, 0xff, 0xff, //0x00001e1e vmovdqu      $-7590(%rip), %ymm6  /* LCPI0_7+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0x72, 0xe2, 0xff, 0xff, //0x00001e26 vmovdqu      $-7566(%rip), %ymm7  /* LCPI0_8+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0xea, 0xe2, 0xff, 0xff, //0x00001e2e vmovdqu      $-7446(%rip), %ymm8  /* LCPI0_13+0(%rip) */
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00001e36 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0x7e, 0x6f, 0x15, 0xfd, 0xe2, 0xff, 0xff, //0x00001e3b vmovdqu      $-7427(%rip), %ymm10  /* LCPI0_14+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0x15, 0xe3, 0xff, 0xff, //0x00001e43 vmovdqu      $-7403(%rip), %ymm11  /* LCPI0_15+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0x2d, 0xe3, 0xff, 0xff, //0x00001e4b vmovdqu      $-7379(%rip), %ymm12  /* LCPI0_16+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0x45, 0xe3, 0xff, 0xff, //0x00001e53 vmovdqu      $-7355(%rip), %ymm13  /* LCPI0_17+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0xdd, 0xe1, 0xff, 0xff, //0x00001e5b vmovdqu      $-7715(%rip), %ymm14  /* LCPI0_2+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x3d, 0x55, 0xe3, 0xff, 0xff, //0x00001e63 vmovdqu      $-7339(%rip), %ymm15  /* LCPI0_18+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0x6d, 0xe3, 0xff, 0xff, //0x00001e6b vmovdqu      $-7315(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0xe9, 0x83, 0x03, 0x00, 0x00, //0x00001e73 jmp          LBB0_423
	//0x00001e78 LBB0_398
	0x48, 0x89, 0xd0, //0x00001e78 movq         %rdx, %rax
	0x4c, 0x09, 0xd8, //0x00001e7b orq          %r11, %rax
	0x0f, 0x99, 0xc1, //0x00001e7e setns        %cl
	0x0f, 0x88, 0xdf, 0x00, 0x00, 0x00, //0x00001e81 js           LBB0_404
	0x4c, 0x39, 0xda, //0x00001e87 cmpq         %r11, %rdx
	0x0f, 0x8c, 0xd6, 0x00, 0x00, 0x00, //0x00001e8a jl           LBB0_404
	0x48, 0xf7, 0xd2, //0x00001e90 notq         %rdx
	0x49, 0x89, 0xd4, //0x00001e93 movq         %rdx, %r12
	0xe9, 0x5b, 0x03, 0x00, 0x00, //0x00001e96 jmp          LBB0_422
	//0x00001e9b LBB0_401
	0x4c, 0x8b, 0x44, 0x24, 0x10, //0x00001e9b movq         $16(%rsp), %r8
	0x4d, 0x29, 0xc6, //0x00001ea0 subq         %r8, %r14
	0x49, 0x01, 0xfe, //0x00001ea3 addq         %rdi, %r14
	0x49, 0x39, 0xf6, //0x00001ea6 cmpq         %rsi, %r14
	0x0f, 0x82, 0x23, 0xea, 0xff, 0xff, //0x00001ea9 jb           LBB0_87
	0xe9, 0xd5, 0x0e, 0x00, 0x00, //0x00001eaf jmp          LBB0_567
	//0x00001eb4 LBB0_402
	0x89, 0xd0, //0x00001eb4 movl         %edx, %eax
	//0x00001eb6 LBB0_403
	0x49, 0xf7, 0xd0, //0x00001eb6 notq         %r8
	0x49, 0x29, 0xc0, //0x00001eb9 subq         %rax, %r8
	0xe9, 0xdd, 0x00, 0x00, 0x00, //0x00001ebc jmp          LBB0_407
	//0x00001ec1 LBB0_105
	0x4c, 0x03, 0x7c, 0x24, 0x10, //0x00001ec1 addq         $16(%rsp), %r15
	0x48, 0x83, 0xfb, 0x20, //0x00001ec6 cmpq         $32, %rbx
	0x0f, 0x82, 0xf5, 0x04, 0x00, 0x00, //0x00001eca jb           LBB0_444
	//0x00001ed0 LBB0_106
	0xc4, 0xc1, 0x7e, 0x6f, 0x07, //0x00001ed0 vmovdqu      (%r15), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x00001ed5 vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xf1, //0x00001ed9 vpmovmskb    %ymm1, %esi
	0xc5, 0xfd, 0x74, 0xc7, //0x00001edd vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xd0, //0x00001ee1 vpmovmskb    %ymm0, %edx
	0x85, 0xd2, //0x00001ee5 testl        %edx, %edx
	0x0f, 0x85, 0x6d, 0x04, 0x00, 0x00, //0x00001ee7 jne          LBB0_440
	0x4d, 0x85, 0xd2, //0x00001eed testq        %r10, %r10
	0x0f, 0x85, 0x82, 0x04, 0x00, 0x00, //0x00001ef0 jne          LBB0_442
	0x45, 0x31, 0xd2, //0x00001ef6 xorl         %r10d, %r10d
	0x48, 0x85, 0xf6, //0x00001ef9 testq        %rsi, %rsi
	0x0f, 0x84, 0xbb, 0x04, 0x00, 0x00, //0x00001efc je           LBB0_443
	//0x00001f02 LBB0_109
	0x48, 0x0f, 0xbc, 0xc6, //0x00001f02 bsfq         %rsi, %rax
	0x4c, 0x2b, 0x7c, 0x24, 0x10, //0x00001f06 subq         $16(%rsp), %r15
	0x49, 0x01, 0xc7, //0x00001f0b addq         %rax, %r15
	0x49, 0x83, 0xc7, 0x01, //0x00001f0e addq         $1, %r15
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x00001f12 movq         $24(%rsp), %r10
	0xe9, 0xd5, 0xf4, 0xff, 0xff, //0x00001f17 jmp          LBB0_240
	//0x00001f1c LBB0_207
	0x4c, 0x03, 0x7c, 0x24, 0x10, //0x00001f1c addq         $16(%rsp), %r15
	0x49, 0x83, 0xfd, 0x20, //0x00001f21 cmpq         $32, %r13
	0x0f, 0x82, 0x36, 0x03, 0x00, 0x00, //0x00001f25 jb           LBB0_429
	//0x00001f2b LBB0_208
	0xc4, 0xc1, 0x7e, 0x6f, 0x07, //0x00001f2b vmovdqu      (%r15), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x00001f30 vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0x7d, 0xd7, 0xd1, //0x00001f34 vpmovmskb    %ymm1, %r10d
	0xc5, 0xfd, 0x74, 0xcf, //0x00001f38 vpcmpeqb     %ymm7, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xd1, //0x00001f3c vpmovmskb    %ymm1, %edx
	0x85, 0xd2, //0x00001f40 testl        %edx, %edx
	0x0f, 0x85, 0x63, 0x05, 0x00, 0x00, //0x00001f42 jne          LBB0_457
	0x4d, 0x85, 0xdb, //0x00001f48 testq        %r11, %r11
	0x0f, 0x85, 0x78, 0x05, 0x00, 0x00, //0x00001f4b jne          LBB0_459
	0x45, 0x31, 0xdb, //0x00001f51 xorl         %r11d, %r11d
	0x4d, 0x85, 0xd2, //0x00001f54 testq        %r10, %r10
	0x0f, 0x84, 0xb2, 0x05, 0x00, 0x00, //0x00001f57 je           LBB0_460
	//0x00001f5d LBB0_211
	0x49, 0x0f, 0xbc, 0xd2, //0x00001f5d bsfq         %r10, %rdx
	0xe9, 0xae, 0x05, 0x00, 0x00, //0x00001f61 jmp          LBB0_461
	//0x00001f66 LBB0_404
	0x49, 0x8d, 0x43, 0xff, //0x00001f66 leaq         $-1(%r11), %rax
	0x48, 0x39, 0xc2, //0x00001f6a cmpq         %rax, %rdx
	0x49, 0xf7, 0xd3, //0x00001f6d notq         %r11
	0x4d, 0x0f, 0x45, 0xdc, //0x00001f70 cmovneq      %r12, %r11
	0x84, 0xc9, //0x00001f74 testb        %cl, %cl
	0x4d, 0x0f, 0x45, 0xe3, //0x00001f76 cmovneq      %r11, %r12
	0xe9, 0x77, 0x02, 0x00, 0x00, //0x00001f7a jmp          LBB0_422
	//0x00001f7f LBB0_405
	0x0f, 0xbc, 0xc6, //0x00001f7f bsfl         %esi, %eax
	0xe9, 0x12, 0x01, 0x00, 0x00, //0x00001f82 jmp          LBB0_414
	//0x00001f87 LBB0_406
	0x48, 0x8b, 0x44, 0x24, 0x10, //0x00001f87 movq         $16(%rsp), %rax
	0x48, 0x03, 0x44, 0x24, 0x28, //0x00001f8c addq         $40(%rsp), %rax
	0x4c, 0x29, 0xc0, //0x00001f91 subq         %r8, %rax
	0x48, 0x29, 0xc8, //0x00001f94 subq         %rcx, %rax
	0x48, 0x83, 0xc0, 0xfe, //0x00001f97 addq         $-2, %rax
	0x49, 0x89, 0xc0, //0x00001f9b movq         %rax, %r8
	//0x00001f9e LBB0_407
	0x4c, 0x8b, 0x5c, 0x24, 0x08, //0x00001f9e movq         $8(%rsp), %r11
	0x4d, 0x85, 0xc0, //0x00001fa3 testq        %r8, %r8
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x00001fa6 movq         $24(%rsp), %r10
	0x0f, 0x89, 0x1f, 0xe7, 0xff, 0xff, //0x00001fab jns          LBB0_408
	0xe9, 0x06, 0x15, 0x00, 0x00, //0x00001fb1 jmp          LBB0_635
	//0x00001fb6 LBB0_129
	0x4c, 0x03, 0x7c, 0x24, 0x10, //0x00001fb6 addq         $16(%rsp), %r15
	0x48, 0x83, 0xfb, 0x20, //0x00001fbb cmpq         $32, %rbx
	0x0f, 0x82, 0xd9, 0x06, 0x00, 0x00, //0x00001fbf jb           LBB0_482
	//0x00001fc5 LBB0_130
	0xc4, 0xc1, 0x7e, 0x6f, 0x07, //0x00001fc5 vmovdqu      (%r15), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x00001fca vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xf1, //0x00001fce vpmovmskb    %ymm1, %esi
	0xc5, 0xfd, 0x74, 0xc7, //0x00001fd2 vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xd0, //0x00001fd6 vpmovmskb    %ymm0, %edx
	0x85, 0xd2, //0x00001fda testl        %edx, %edx
	0x0f, 0x85, 0x51, 0x06, 0x00, 0x00, //0x00001fdc jne          LBB0_478
	0x4d, 0x85, 0xd2, //0x00001fe2 testq        %r10, %r10
	0x0f, 0x85, 0x66, 0x06, 0x00, 0x00, //0x00001fe5 jne          LBB0_480
	0x45, 0x31, 0xd2, //0x00001feb xorl         %r10d, %r10d
	0x48, 0x85, 0xf6, //0x00001fee testq        %rsi, %rsi
	0x0f, 0x84, 0x9f, 0x06, 0x00, 0x00, //0x00001ff1 je           LBB0_481
	//0x00001ff7 LBB0_133
	0x48, 0x0f, 0xbc, 0xc6, //0x00001ff7 bsfq         %rsi, %rax
	0x4c, 0x2b, 0x7c, 0x24, 0x10, //0x00001ffb subq         $16(%rsp), %r15
	0x49, 0x01, 0xc7, //0x00002000 addq         %rax, %r15
	0x49, 0x83, 0xc7, 0x01, //0x00002003 addq         $1, %r15
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x00002007 movq         $24(%rsp), %r10
	0xe9, 0x26, 0xf5, 0xff, 0xff, //0x0000200c jmp          LBB0_259
	//0x00002011 LBB0_228
	0x4c, 0x03, 0x7c, 0x24, 0x10, //0x00002011 addq         $16(%rsp), %r15
	0x48, 0x83, 0xfb, 0x20, //0x00002016 cmpq         $32, %rbx
	0x0f, 0x82, 0xc2, 0x02, 0x00, 0x00, //0x0000201a jb           LBB0_436
	//0x00002020 LBB0_229
	0xc4, 0xc1, 0x7e, 0x6f, 0x07, //0x00002020 vmovdqu      (%r15), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x00002025 vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0x7d, 0xd7, 0xd1, //0x00002029 vpmovmskb    %ymm1, %r10d
	0xc5, 0xfd, 0x74, 0xcf, //0x0000202d vpcmpeqb     %ymm7, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xd1, //0x00002031 vpmovmskb    %ymm1, %edx
	0x85, 0xd2, //0x00002035 testl        %edx, %edx
	0x0f, 0x85, 0x47, 0x07, 0x00, 0x00, //0x00002037 jne          LBB0_495
	0x4d, 0x85, 0xdb, //0x0000203d testq        %r11, %r11
	0x0f, 0x85, 0x5c, 0x07, 0x00, 0x00, //0x00002040 jne          LBB0_497
	0x45, 0x31, 0xdb, //0x00002046 xorl         %r11d, %r11d
	0x4d, 0x85, 0xd2, //0x00002049 testq        %r10, %r10
	0x0f, 0x84, 0x96, 0x07, 0x00, 0x00, //0x0000204c je           LBB0_498
	//0x00002052 LBB0_232
	0x49, 0x0f, 0xbc, 0xd2, //0x00002052 bsfq         %r10, %rdx
	0xe9, 0x92, 0x07, 0x00, 0x00, //0x00002056 jmp          LBB0_499
	//0x0000205b LBB0_409
	0x89, 0xd0, //0x0000205b movl         %edx, %eax
	0xe9, 0x37, 0x00, 0x00, 0x00, //0x0000205d jmp          LBB0_414
	//0x00002062 LBB0_410
	0x4d, 0x01, 0xc6, //0x00002062 addq         %r8, %r14
	0x48, 0x85, 0xd2, //0x00002065 testq        %rdx, %rdx
	0x0f, 0x85, 0xfa, 0xe7, 0xff, 0xff, //0x00002068 jne          LBB0_80
	0xe9, 0x34, 0xe8, 0xff, 0xff, //0x0000206e jmp          LBB0_85
	//0x00002073 LBB0_411
	0x49, 0x89, 0xd4, //0x00002073 movq         %rdx, %r12
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00002076 movq         $-1, %rcx
	0x48, 0x8b, 0x14, 0x24, //0x0000207d movq         (%rsp), %rdx
	0x48, 0x85, 0xd2, //0x00002081 testq        %rdx, %rdx
	0x0f, 0x85, 0xcf, 0xfc, 0xff, 0xff, //0x00002084 jne          LBB0_387
	0xe9, 0xee, 0x15, 0x00, 0x00, //0x0000208a jmp          LBB0_662
	//0x0000208f LBB0_412
	0x0f, 0xbc, 0xc3, //0x0000208f bsfl         %ebx, %eax
	0xe9, 0x59, 0x01, 0x00, 0x00, //0x00002092 jmp          LBB0_421
	//0x00002097 LBB0_413
	0x89, 0xf0, //0x00002097 movl         %esi, %eax
	//0x00002099 LBB0_414
	0x48, 0x8b, 0x4c, 0x24, 0x10, //0x00002099 movq         $16(%rsp), %rcx
	0x48, 0x03, 0x4c, 0x24, 0x28, //0x0000209e addq         $40(%rsp), %rcx
	0x4c, 0x29, 0xc1, //0x000020a3 subq         %r8, %rcx
	0x48, 0x29, 0xc1, //0x000020a6 subq         %rax, %rcx
	0x4c, 0x29, 0xc9, //0x000020a9 subq         %r9, %rcx
	0x48, 0x83, 0xc1, 0xfe, //0x000020ac addq         $-2, %rcx
	0x49, 0x89, 0xc8, //0x000020b0 movq         %rcx, %r8
	0x4c, 0x8b, 0x5c, 0x24, 0x08, //0x000020b3 movq         $8(%rsp), %r11
	0x4c, 0x8b, 0x4c, 0x24, 0x20, //0x000020b8 movq         $32(%rsp), %r9
	0x4d, 0x85, 0xc0, //0x000020bd testq        %r8, %r8
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x000020c0 movq         $24(%rsp), %r10
	0x0f, 0x89, 0x05, 0xe6, 0xff, 0xff, //0x000020c5 jns          LBB0_408
	0xe9, 0xec, 0x13, 0x00, 0x00, //0x000020cb jmp          LBB0_635
	//0x000020d0 LBB0_271
	0x4c, 0x03, 0x7c, 0x24, 0x10, //0x000020d0 addq         $16(%rsp), %r15
	0x48, 0x83, 0xfb, 0x20, //0x000020d5 cmpq         $32, %rbx
	0x0f, 0x82, 0xf6, 0x09, 0x00, 0x00, //0x000020d9 jb           LBB0_527
	//0x000020df LBB0_272
	0xc4, 0xc1, 0x7e, 0x6f, 0x07, //0x000020df vmovdqu      (%r15), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x000020e4 vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xf1, //0x000020e8 vpmovmskb    %ymm1, %esi
	0xc5, 0xfd, 0x74, 0xc7, //0x000020ec vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xd0, //0x000020f0 vpmovmskb    %ymm0, %edx
	0x85, 0xd2, //0x000020f4 testl        %edx, %edx
	0x0f, 0x85, 0x6e, 0x09, 0x00, 0x00, //0x000020f6 jne          LBB0_523
	0x4d, 0x85, 0xd2, //0x000020fc testq        %r10, %r10
	0x0f, 0x85, 0x83, 0x09, 0x00, 0x00, //0x000020ff jne          LBB0_525
	0x45, 0x31, 0xd2, //0x00002105 xorl         %r10d, %r10d
	0x48, 0x85, 0xf6, //0x00002108 testq        %rsi, %rsi
	0x0f, 0x84, 0xbc, 0x09, 0x00, 0x00, //0x0000210b je           LBB0_526
	//0x00002111 LBB0_275
	0x48, 0x0f, 0xbc, 0xc6, //0x00002111 bsfq         %rsi, %rax
	0x4c, 0x2b, 0x7c, 0x24, 0x10, //0x00002115 subq         $16(%rsp), %r15
	0x49, 0x01, 0xc7, //0x0000211a addq         %rax, %r15
	0x49, 0x83, 0xc7, 0x01, //0x0000211d addq         $1, %r15
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x00002121 movq         $24(%rsp), %r10
	0xe9, 0xdb, 0xfb, 0xff, 0xff, //0x00002126 jmp          LBB0_379
	//0x0000212b LBB0_312
	0x4c, 0x03, 0x7c, 0x24, 0x10, //0x0000212b addq         $16(%rsp), %r15
	0x48, 0x83, 0xfb, 0x20, //0x00002130 cmpq         $32, %rbx
	0x0f, 0x82, 0x1c, 0x0b, 0x00, 0x00, //0x00002134 jb           LBB0_549
	//0x0000213a LBB0_313
	0xc4, 0xc1, 0x7e, 0x6f, 0x07, //0x0000213a vmovdqu      (%r15), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x0000213f vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0x7d, 0xd7, 0xd1, //0x00002143 vpmovmskb    %ymm1, %r10d
	0xc5, 0xfd, 0x74, 0xcf, //0x00002147 vpcmpeqb     %ymm7, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xd1, //0x0000214b vpmovmskb    %ymm1, %edx
	0x85, 0xd2, //0x0000214f testl        %edx, %edx
	0x0f, 0x85, 0x3b, 0x0a, 0x00, 0x00, //0x00002151 jne          LBB0_540
	0x4d, 0x85, 0xdb, //0x00002157 testq        %r11, %r11
	0x0f, 0x85, 0x50, 0x0a, 0x00, 0x00, //0x0000215a jne          LBB0_542
	0x45, 0x31, 0xdb, //0x00002160 xorl         %r11d, %r11d
	0x4d, 0x85, 0xd2, //0x00002163 testq        %r10, %r10
	0x0f, 0x84, 0x8a, 0x0a, 0x00, 0x00, //0x00002166 je           LBB0_543
	//0x0000216c LBB0_316
	0x49, 0x0f, 0xbc, 0xd2, //0x0000216c bsfq         %r10, %rdx
	0xe9, 0x86, 0x0a, 0x00, 0x00, //0x00002170 jmp          LBB0_544
	//0x00002175 LBB0_415
	0x0f, 0xbc, 0xc6, //0x00002175 bsfl         %esi, %eax
	//0x00002178 LBB0_416
	0x48, 0x8b, 0x4c, 0x24, 0x10, //0x00002178 movq         $16(%rsp), %rcx
	0x4c, 0x01, 0xf9, //0x0000217d addq         %r15, %rcx
	0x4c, 0x29, 0xe1, //0x00002180 subq         %r12, %rcx
	0x48, 0x29, 0xc1, //0x00002183 subq         %rax, %rcx
	0x49, 0xf7, 0xd5, //0x00002186 notq         %r13
	0x49, 0x01, 0xcd, //0x00002189 addq         %rcx, %r13
	0x4d, 0x89, 0xec, //0x0000218c movq         %r13, %r12
	0x4c, 0x8b, 0x5c, 0x24, 0x08, //0x0000218f movq         $8(%rsp), %r11
	0x4c, 0x8b, 0x6c, 0x24, 0x38, //0x00002194 movq         $56(%rsp), %r13
	0x4c, 0x8b, 0x4c, 0x24, 0x20, //0x00002199 movq         $32(%rsp), %r9
	0xe9, 0x73, 0xfc, 0xff, 0xff, //0x0000219e jmp          LBB0_397
	//0x000021a3 LBB0_417
	0x48, 0x8b, 0x44, 0x24, 0x10, //0x000021a3 movq         $16(%rsp), %rax
	0x4c, 0x01, 0xf8, //0x000021a8 addq         %r15, %rax
	0x4c, 0x29, 0xe0, //0x000021ab subq         %r12, %rax
	0x48, 0xf7, 0xd1, //0x000021ae notq         %rcx
	0x48, 0x01, 0xc1, //0x000021b1 addq         %rax, %rcx
	0x49, 0x89, 0xcc, //0x000021b4 movq         %rcx, %r12
	0xe9, 0x55, 0xfc, 0xff, 0xff, //0x000021b7 jmp          LBB0_396
	//0x000021bc LBB0_418
	0x89, 0xd0, //0x000021bc movl         %edx, %eax
	0xe9, 0x2d, 0x00, 0x00, 0x00, //0x000021be jmp          LBB0_421
	//0x000021c3 LBB0_419
	0x49, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x000021c3 movq         $-1, %r10
	0x48, 0xc7, 0x04, 0x24, 0xff, 0xff, 0xff, 0xff, //0x000021ca movq         $-1, (%rsp)
	0x4d, 0x89, 0xe0, //0x000021d2 movq         %r12, %r8
	0x4c, 0x89, 0x4c, 0x24, 0x20, //0x000021d5 movq         %r9, $32(%rsp)
	0x49, 0x83, 0xfd, 0x10, //0x000021da cmpq         $16, %r13
	0x48, 0x8b, 0x44, 0x24, 0x28, //0x000021de movq         $40(%rsp), %rax
	0x0f, 0x83, 0x2d, 0xeb, 0xff, 0xff, //0x000021e3 jae          LBB0_158
	0xe9, 0xa9, 0xec, 0xff, 0xff, //0x000021e9 jmp          LBB0_176
	//0x000021ee LBB0_420
	0x89, 0xf8, //0x000021ee movl         %edi, %eax
	//0x000021f0 LBB0_421
	0x49, 0xf7, 0xd4, //0x000021f0 notq         %r12
	0x49, 0x29, 0xc4, //0x000021f3 subq         %rax, %r12
	//0x000021f6 LBB0_422
	0x4c, 0x8b, 0x5c, 0x24, 0x08, //0x000021f6 movq         $8(%rsp), %r11
	//0x000021fb LBB0_423
	0x4d, 0x85, 0xe4, //0x000021fb testq        %r12, %r12
	0x0f, 0x88, 0x76, 0x14, 0x00, 0x00, //0x000021fe js           LBB0_661
	0x49, 0x8b, 0x0b, //0x00002204 movq         (%r11), %rcx
	//0x00002207 LBB0_425
	0x4c, 0x01, 0xe1, //0x00002207 addq         %r12, %rcx
	0x49, 0x89, 0x0b, //0x0000220a movq         %rcx, (%r11)
	0x4d, 0x85, 0xff, //0x0000220d testq        %r15, %r15
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x00002210 movq         $24(%rsp), %r10
	0x0f, 0x8f, 0xd5, 0xe4, 0xff, 0xff, //0x00002215 jg           LBB0_55
	0xe9, 0x51, 0x14, 0x00, 0x00, //0x0000221b jmp          LBB0_426
	//0x00002220 LBB0_427
	0x48, 0x8b, 0x44, 0x24, 0x10, //0x00002220 movq         $16(%rsp), %rax
	0x4e, 0x8d, 0x3c, 0x00, //0x00002225 leaq         (%rax,%r8), %r15
	0x48, 0xc7, 0x04, 0x24, 0xff, 0xff, 0xff, 0xff, //0x00002229 movq         $-1, (%rsp)
	0x45, 0x31, 0xd2, //0x00002231 xorl         %r10d, %r10d
	0x48, 0x83, 0xfb, 0x20, //0x00002234 cmpq         $32, %rbx
	0x0f, 0x83, 0x92, 0xfc, 0xff, 0xff, //0x00002238 jae          LBB0_106
	0xe9, 0x82, 0x01, 0x00, 0x00, //0x0000223e jmp          LBB0_444
	//0x00002243 LBB0_428
	0x48, 0x8b, 0x4c, 0x24, 0x10, //0x00002243 movq         $16(%rsp), %rcx
	0x4e, 0x8d, 0x3c, 0x01, //0x00002248 leaq         (%rcx,%r8), %r15
	0x48, 0xc7, 0x04, 0x24, 0xff, 0xff, 0xff, 0xff, //0x0000224c movq         $-1, (%rsp)
	0x45, 0x31, 0xdb, //0x00002254 xorl         %r11d, %r11d
	0x49, 0x83, 0xfd, 0x20, //0x00002257 cmpq         $32, %r13
	0x0f, 0x83, 0xca, 0xfc, 0xff, 0xff, //0x0000225b jae          LBB0_208
	//0x00002261 LBB0_429
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x00002261 movq         $24(%rsp), %r10
	0xe9, 0x09, 0x03, 0x00, 0x00, //0x00002266 jmp          LBB0_466
	//0x0000226b LBB0_430
	0x89, 0xf0, //0x0000226b movl         %esi, %eax
	0xe9, 0x06, 0xff, 0xff, 0xff, //0x0000226d jmp          LBB0_416
	//0x00002272 LBB0_431
	0x89, 0xf0, //0x00002272 movl         %esi, %eax
	0xe9, 0x02, 0x00, 0x00, 0x00, //0x00002274 jmp          LBB0_433
	//0x00002279 LBB0_432
	0x89, 0xd0, //0x00002279 movl         %edx, %eax
	//0x0000227b LBB0_433
	0x48, 0x8b, 0x4c, 0x24, 0x10, //0x0000227b movq         $16(%rsp), %rcx
	0x4c, 0x01, 0xf9, //0x00002280 addq         %r15, %rcx
	0x4c, 0x29, 0xe1, //0x00002283 subq         %r12, %rcx
	0x48, 0x29, 0xc1, //0x00002286 subq         %rax, %rcx
	0x49, 0xf7, 0xd5, //0x00002289 notq         %r13
	0x49, 0x01, 0xcd, //0x0000228c addq         %rcx, %r13
	0x4d, 0x89, 0xec, //0x0000228f movq         %r13, %r12
	0x4c, 0x8b, 0x5c, 0x24, 0x08, //0x00002292 movq         $8(%rsp), %r11
	0x4c, 0x8b, 0x6c, 0x24, 0x38, //0x00002297 movq         $56(%rsp), %r13
	0xe9, 0x75, 0xfb, 0xff, 0xff, //0x0000229c jmp          LBB0_397
	//0x000022a1 LBB0_434
	0x48, 0x8b, 0x44, 0x24, 0x10, //0x000022a1 movq         $16(%rsp), %rax
	0x4e, 0x8d, 0x3c, 0x00, //0x000022a6 leaq         (%rax,%r8), %r15
	0x48, 0xc7, 0x04, 0x24, 0xff, 0xff, 0xff, 0xff, //0x000022aa movq         $-1, (%rsp)
	0x45, 0x31, 0xd2, //0x000022b2 xorl         %r10d, %r10d
	0x48, 0x83, 0xfb, 0x20, //0x000022b5 cmpq         $32, %rbx
	0x0f, 0x83, 0x06, 0xfd, 0xff, 0xff, //0x000022b9 jae          LBB0_130
	0xe9, 0xda, 0x03, 0x00, 0x00, //0x000022bf jmp          LBB0_482
	//0x000022c4 LBB0_435
	0x48, 0x8b, 0x44, 0x24, 0x10, //0x000022c4 movq         $16(%rsp), %rax
	0x4e, 0x8d, 0x3c, 0x00, //0x000022c9 leaq         (%rax,%r8), %r15
	0x48, 0xc7, 0x04, 0x24, 0xff, 0xff, 0xff, 0xff, //0x000022cd movq         $-1, (%rsp)
	0x45, 0x31, 0xdb, //0x000022d5 xorl         %r11d, %r11d
	0x48, 0x83, 0xfb, 0x20, //0x000022d8 cmpq         $32, %rbx
	0x0f, 0x83, 0x3e, 0xfd, 0xff, 0xff, //0x000022dc jae          LBB0_229
	//0x000022e2 LBB0_436
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x000022e2 movq         $24(%rsp), %r10
	0xe9, 0x61, 0x05, 0x00, 0x00, //0x000022e7 jmp          LBB0_504
	//0x000022ec LBB0_437
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x000022ec movq         $-1, %r11
	0x48, 0xc7, 0x04, 0x24, 0xff, 0xff, 0xff, 0xff, //0x000022f3 movq         $-1, (%rsp)
	0x4c, 0x8b, 0x64, 0x24, 0x30, //0x000022fb movq         $48(%rsp), %r12
	0x4c, 0x89, 0x6c, 0x24, 0x38, //0x00002300 movq         %r13, $56(%rsp)
	0x49, 0x83, 0xfa, 0x10, //0x00002305 cmpq         $16, %r10
	0x0f, 0x83, 0xb4, 0xf7, 0xff, 0xff, //0x00002309 jae          LBB0_343
	0xe9, 0xe9, 0xf8, 0xff, 0xff, //0x0000230f jmp          LBB0_361
	//0x00002314 LBB0_438
	0x48, 0x8b, 0x44, 0x24, 0x10, //0x00002314 movq         $16(%rsp), %rax
	0x4e, 0x8d, 0x3c, 0x00, //0x00002319 leaq         (%rax,%r8), %r15
	0x48, 0xc7, 0x04, 0x24, 0xff, 0xff, 0xff, 0xff, //0x0000231d movq         $-1, (%rsp)
	0x45, 0x31, 0xd2, //0x00002325 xorl         %r10d, %r10d
	0x48, 0x83, 0xfb, 0x20, //0x00002328 cmpq         $32, %rbx
	0x0f, 0x83, 0xad, 0xfd, 0xff, 0xff, //0x0000232c jae          LBB0_272
	0xe9, 0x9e, 0x07, 0x00, 0x00, //0x00002332 jmp          LBB0_527
	//0x00002337 LBB0_439
	0x48, 0x8b, 0x44, 0x24, 0x10, //0x00002337 movq         $16(%rsp), %rax
	0x4e, 0x8d, 0x3c, 0x00, //0x0000233c leaq         (%rax,%r8), %r15
	0x48, 0xc7, 0x04, 0x24, 0xff, 0xff, 0xff, 0xff, //0x00002340 movq         $-1, (%rsp)
	0x45, 0x31, 0xdb, //0x00002348 xorl         %r11d, %r11d
	0x48, 0x83, 0xfb, 0x20, //0x0000234b cmpq         $32, %rbx
	0x0f, 0x83, 0xe5, 0xfd, 0xff, 0xff, //0x0000234f jae          LBB0_313
	0xe9, 0xfc, 0x08, 0x00, 0x00, //0x00002355 jmp          LBB0_549
	//0x0000235a LBB0_440
	0x48, 0x83, 0x3c, 0x24, 0xff, //0x0000235a cmpq         $-1, (%rsp)
	0x0f, 0x85, 0x13, 0x00, 0x00, 0x00, //0x0000235f jne          LBB0_442
	0x4c, 0x89, 0xf8, //0x00002365 movq         %r15, %rax
	0x48, 0x2b, 0x44, 0x24, 0x10, //0x00002368 subq         $16(%rsp), %rax
	0x48, 0x0f, 0xbc, 0xca, //0x0000236d bsfq         %rdx, %rcx
	0x48, 0x01, 0xc1, //0x00002371 addq         %rax, %rcx
	0x48, 0x89, 0x0c, 0x24, //0x00002374 movq         %rcx, (%rsp)
	//0x00002378 LBB0_442
	0x44, 0x89, 0xd0, //0x00002378 movl         %r10d, %eax
	0xf7, 0xd0, //0x0000237b notl         %eax
	0x21, 0xd0, //0x0000237d andl         %edx, %eax
	0x8d, 0x0c, 0x00, //0x0000237f leal         (%rax,%rax), %ecx
	0x41, 0x8d, 0x3c, 0x42, //0x00002382 leal         (%r10,%rax,2), %edi
	0xf7, 0xd1, //0x00002386 notl         %ecx
	0x21, 0xd1, //0x00002388 andl         %edx, %ecx
	0x81, 0xe1, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000238a andl         $-1431655766, %ecx
	0x45, 0x31, 0xd2, //0x00002390 xorl         %r10d, %r10d
	0x01, 0xc1, //0x00002393 addl         %eax, %ecx
	0x41, 0x0f, 0x92, 0xc2, //0x00002395 setb         %r10b
	0x01, 0xc9, //0x00002399 addl         %ecx, %ecx
	0x81, 0xf1, 0x55, 0x55, 0x55, 0x55, //0x0000239b xorl         $1431655765, %ecx
	0x21, 0xf9, //0x000023a1 andl         %edi, %ecx
	0xf7, 0xd1, //0x000023a3 notl         %ecx
	0x21, 0xce, //0x000023a5 andl         %ecx, %esi
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x000023a7 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0xfe, 0x6f, 0x25, 0x2c, 0xde, 0xff, 0xff, //0x000023ac vmovdqu      $-8660(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0x48, 0x85, 0xf6, //0x000023b4 testq        %rsi, %rsi
	0x0f, 0x85, 0x45, 0xfb, 0xff, 0xff, //0x000023b7 jne          LBB0_109
	//0x000023bd LBB0_443
	0x49, 0x83, 0xc7, 0x20, //0x000023bd addq         $32, %r15
	0x48, 0x83, 0xc3, 0xe0, //0x000023c1 addq         $-32, %rbx
	//0x000023c5 LBB0_444
	0x4d, 0x85, 0xd2, //0x000023c5 testq        %r10, %r10
	0x0f, 0x85, 0x38, 0x05, 0x00, 0x00, //0x000023c8 jne          LBB0_515
	0x48, 0x8b, 0x4c, 0x24, 0x10, //0x000023ce movq         $16(%rsp), %rcx
	0x48, 0xf7, 0xd1, //0x000023d3 notq         %rcx
	0x48, 0x8b, 0x3c, 0x24, //0x000023d6 movq         (%rsp), %rdi
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x000023da movq         $24(%rsp), %r10
	0x48, 0x85, 0xdb, //0x000023df testq        %rbx, %rbx
	0x0f, 0x84, 0x96, 0x00, 0x00, 0x00, //0x000023e2 je           LBB0_456
	//0x000023e8 LBB0_446
	0x48, 0x83, 0xc1, 0x01, //0x000023e8 addq         $1, %rcx
	//0x000023ec LBB0_447
	0x31, 0xf6, //0x000023ec xorl         %esi, %esi
	//0x000023ee LBB0_448
	0x41, 0x0f, 0xb6, 0x14, 0x37, //0x000023ee movzbl       (%r15,%rsi), %edx
	0x80, 0xfa, 0x22, //0x000023f3 cmpb         $34, %dl
	0x0f, 0x84, 0x7b, 0x00, 0x00, 0x00, //0x000023f6 je           LBB0_455
	0x80, 0xfa, 0x5c, //0x000023fc cmpb         $92, %dl
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x000023ff je           LBB0_453
	0x48, 0x83, 0xc6, 0x01, //0x00002405 addq         $1, %rsi
	0x48, 0x39, 0xf3, //0x00002409 cmpq         %rsi, %rbx
	0x0f, 0x85, 0xdc, 0xff, 0xff, 0xff, //0x0000240c jne          LBB0_448
	0xe9, 0x71, 0x00, 0x00, 0x00, //0x00002412 jmp          LBB0_451
	//0x00002417 LBB0_453
	0x48, 0x8d, 0x43, 0xff, //0x00002417 leaq         $-1(%rbx), %rax
	0x48, 0x39, 0xf0, //0x0000241b cmpq         %rsi, %rax
	0x0f, 0x84, 0x9e, 0x09, 0x00, 0x00, //0x0000241e je           LBB0_381
	0x4a, 0x8d, 0x04, 0x39, //0x00002424 leaq         (%rcx,%r15), %rax
	0x48, 0x01, 0xf0, //0x00002428 addq         %rsi, %rax
	0x48, 0x83, 0xff, 0xff, //0x0000242b cmpq         $-1, %rdi
	0x48, 0x8b, 0x14, 0x24, //0x0000242f movq         (%rsp), %rdx
	0x48, 0x0f, 0x44, 0xd0, //0x00002433 cmoveq       %rax, %rdx
	0x48, 0x89, 0x14, 0x24, //0x00002437 movq         %rdx, (%rsp)
	0x48, 0x0f, 0x44, 0xf8, //0x0000243b cmoveq       %rax, %rdi
	0x49, 0x01, 0xf7, //0x0000243f addq         %rsi, %r15
	0x49, 0x83, 0xc7, 0x02, //0x00002442 addq         $2, %r15
	0x48, 0x89, 0xd8, //0x00002446 movq         %rbx, %rax
	0x48, 0x29, 0xf0, //0x00002449 subq         %rsi, %rax
	0x48, 0x83, 0xc0, 0xfe, //0x0000244c addq         $-2, %rax
	0x48, 0x83, 0xc3, 0xfe, //0x00002450 addq         $-2, %rbx
	0x48, 0x39, 0xf3, //0x00002454 cmpq         %rsi, %rbx
	0x48, 0x89, 0xc3, //0x00002457 movq         %rax, %rbx
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x0000245a movq         $24(%rsp), %r10
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x0000245f vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0xfe, 0x6f, 0x25, 0x74, 0xdd, 0xff, 0xff, //0x00002464 vmovdqu      $-8844(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0x0f, 0x85, 0x7a, 0xff, 0xff, 0xff, //0x0000246c jne          LBB0_447
	0xe9, 0x4b, 0x09, 0x00, 0x00, //0x00002472 jmp          LBB0_381
	//0x00002477 LBB0_455
	0x49, 0x01, 0xf7, //0x00002477 addq         %rsi, %r15
	0x49, 0x83, 0xc7, 0x01, //0x0000247a addq         $1, %r15
	//0x0000247e LBB0_456
	0x4c, 0x2b, 0x7c, 0x24, 0x10, //0x0000247e subq         $16(%rsp), %r15
	0xe9, 0x69, 0xef, 0xff, 0xff, //0x00002483 jmp          LBB0_240
	//0x00002488 LBB0_451
	0x80, 0xfa, 0x22, //0x00002488 cmpb         $34, %dl
	0x0f, 0x85, 0x31, 0x09, 0x00, 0x00, //0x0000248b jne          LBB0_381
	0x49, 0x01, 0xdf, //0x00002491 addq         %rbx, %r15
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x00002494 movq         $24(%rsp), %r10
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00002499 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0xfe, 0x6f, 0x25, 0x3a, 0xdd, 0xff, 0xff, //0x0000249e vmovdqu      $-8902(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0xe9, 0xd3, 0xff, 0xff, 0xff, //0x000024a6 jmp          LBB0_456
	//0x000024ab LBB0_457
	0x48, 0x83, 0x3c, 0x24, 0xff, //0x000024ab cmpq         $-1, (%rsp)
	0x0f, 0x85, 0x13, 0x00, 0x00, 0x00, //0x000024b0 jne          LBB0_459
	0x4c, 0x89, 0xf9, //0x000024b6 movq         %r15, %rcx
	0x48, 0x2b, 0x4c, 0x24, 0x10, //0x000024b9 subq         $16(%rsp), %rcx
	0x48, 0x0f, 0xbc, 0xf2, //0x000024be bsfq         %rdx, %rsi
	0x48, 0x01, 0xce, //0x000024c2 addq         %rcx, %rsi
	0x48, 0x89, 0x34, 0x24, //0x000024c5 movq         %rsi, (%rsp)
	//0x000024c9 LBB0_459
	0x44, 0x89, 0xd9, //0x000024c9 movl         %r11d, %ecx
	0xf7, 0xd1, //0x000024cc notl         %ecx
	0x21, 0xd1, //0x000024ce andl         %edx, %ecx
	0x8d, 0x34, 0x09, //0x000024d0 leal         (%rcx,%rcx), %esi
	0x41, 0x8d, 0x3c, 0x4b, //0x000024d3 leal         (%r11,%rcx,2), %edi
	0xf7, 0xd6, //0x000024d7 notl         %esi
	0x21, 0xd6, //0x000024d9 andl         %edx, %esi
	0x81, 0xe6, 0xaa, 0xaa, 0xaa, 0xaa, //0x000024db andl         $-1431655766, %esi
	0x45, 0x31, 0xdb, //0x000024e1 xorl         %r11d, %r11d
	0x01, 0xce, //0x000024e4 addl         %ecx, %esi
	0x41, 0x0f, 0x92, 0xc3, //0x000024e6 setb         %r11b
	0x01, 0xf6, //0x000024ea addl         %esi, %esi
	0x81, 0xf6, 0x55, 0x55, 0x55, 0x55, //0x000024ec xorl         $1431655765, %esi
	0x21, 0xfe, //0x000024f2 andl         %edi, %esi
	0xf7, 0xd6, //0x000024f4 notl         %esi
	0x41, 0x21, 0xf2, //0x000024f6 andl         %esi, %r10d
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x000024f9 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0xfe, 0x6f, 0x25, 0xda, 0xdc, 0xff, 0xff, //0x000024fe vmovdqu      $-8998(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0x4d, 0x85, 0xd2, //0x00002506 testq        %r10, %r10
	0x0f, 0x85, 0x4e, 0xfa, 0xff, 0xff, //0x00002509 jne          LBB0_211
	//0x0000250f LBB0_460
	0xba, 0x40, 0x00, 0x00, 0x00, //0x0000250f movl         $64, %edx
	//0x00002514 LBB0_461
	0xc5, 0xbd, 0x64, 0xc8, //0x00002514 vpcmpgtb     %ymm0, %ymm8, %ymm1
	0xc4, 0xc1, 0x7d, 0x64, 0xc1, //0x00002518 vpcmpgtb     %ymm9, %ymm0, %ymm0
	0xc5, 0xf5, 0xdb, 0xc0, //0x0000251d vpand        %ymm0, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x00002521 vpmovmskb    %ymm0, %esi
	0x0f, 0xbc, 0xfe, //0x00002525 bsfl         %esi, %edi
	0x4d, 0x85, 0xd2, //0x00002528 testq        %r10, %r10
	0x0f, 0x84, 0x2e, 0x00, 0x00, 0x00, //0x0000252b je           LBB0_464
	0x85, 0xf6, //0x00002531 testl        %esi, %esi
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x00002533 movl         $64, %ecx
	0x0f, 0x44, 0xf9, //0x00002538 cmovel       %ecx, %edi
	0x48, 0x39, 0xfa, //0x0000253b cmpq         %rdi, %rdx
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x0000253e movq         $24(%rsp), %r10
	0x0f, 0x87, 0xd9, 0x11, 0x00, 0x00, //0x00002543 ja           LBB0_673
	0x4c, 0x2b, 0x7c, 0x24, 0x10, //0x00002549 subq         $16(%rsp), %r15
	0x49, 0x01, 0xd7, //0x0000254e addq         %rdx, %r15
	0x49, 0x83, 0xc7, 0x01, //0x00002551 addq         $1, %r15
	0x4c, 0x8b, 0x5c, 0x24, 0x08, //0x00002555 movq         $8(%rsp), %r11
	0xe9, 0xc9, 0x00, 0x00, 0x00, //0x0000255a jmp          LBB0_477
	//0x0000255f LBB0_464
	0x85, 0xf6, //0x0000255f testl        %esi, %esi
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x00002561 movq         $24(%rsp), %r10
	0x0f, 0x85, 0xc8, 0x11, 0x00, 0x00, //0x00002566 jne          LBB0_678
	0x49, 0x83, 0xc7, 0x20, //0x0000256c addq         $32, %r15
	0x49, 0x83, 0xc5, 0xe0, //0x00002570 addq         $-32, %r13
	//0x00002574 LBB0_466
	0x4d, 0x85, 0xdb, //0x00002574 testq        %r11, %r11
	0x0f, 0x85, 0xe3, 0x03, 0x00, 0x00, //0x00002577 jne          LBB0_517
	0x48, 0x8b, 0x0c, 0x24, //0x0000257d movq         (%rsp), %rcx
	0x4c, 0x8b, 0x5c, 0x24, 0x08, //0x00002581 movq         $8(%rsp), %r11
	0x4d, 0x85, 0xed, //0x00002586 testq        %r13, %r13
	0x0f, 0x84, 0x33, 0x08, 0x00, 0x00, //0x00002589 je           LBB0_381
	//0x0000258f LBB0_468
	0x41, 0x0f, 0xb6, 0x17, //0x0000258f movzbl       (%r15), %edx
	0x80, 0xfa, 0x22, //0x00002593 cmpb         $34, %dl
	0x0f, 0x84, 0x83, 0x00, 0x00, 0x00, //0x00002596 je           LBB0_476
	0x80, 0xfa, 0x5c, //0x0000259c cmpb         $92, %dl
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x0000259f je           LBB0_472
	0x80, 0xfa, 0x1f, //0x000025a5 cmpb         $31, %dl
	0x0f, 0x86, 0x92, 0x11, 0x00, 0x00, //0x000025a8 jbe          LBB0_674
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x000025ae movq         $-1, %rdx
	0xbe, 0x01, 0x00, 0x00, 0x00, //0x000025b5 movl         $1, %esi
	0x49, 0x01, 0xf7, //0x000025ba addq         %rsi, %r15
	0x49, 0x01, 0xd5, //0x000025bd addq         %rdx, %r13
	0x0f, 0x85, 0xc9, 0xff, 0xff, 0xff, //0x000025c0 jne          LBB0_468
	0xe9, 0xf7, 0x07, 0x00, 0x00, //0x000025c6 jmp          LBB0_381
	//0x000025cb LBB0_472
	0x49, 0x83, 0xfd, 0x01, //0x000025cb cmpq         $1, %r13
	0x0f, 0x84, 0xa7, 0x07, 0x00, 0x00, //0x000025cf je           LBB0_565
	0x48, 0xc7, 0xc2, 0xfe, 0xff, 0xff, 0xff, //0x000025d5 movq         $-2, %rdx
	0xbe, 0x02, 0x00, 0x00, 0x00, //0x000025dc movl         $2, %esi
	0x48, 0x83, 0xf9, 0xff, //0x000025e1 cmpq         $-1, %rcx
	0x0f, 0x85, 0x0c, 0x00, 0x00, 0x00, //0x000025e5 jne          LBB0_475
	0x4c, 0x89, 0xf9, //0x000025eb movq         %r15, %rcx
	0x48, 0x2b, 0x4c, 0x24, 0x10, //0x000025ee subq         $16(%rsp), %rcx
	0x48, 0x89, 0x0c, 0x24, //0x000025f3 movq         %rcx, (%rsp)
	//0x000025f7 LBB0_475
	0x4c, 0x8b, 0x5c, 0x24, 0x08, //0x000025f7 movq         $8(%rsp), %r11
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x000025fc movq         $24(%rsp), %r10
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00002601 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0xfe, 0x6f, 0x25, 0xd2, 0xdb, 0xff, 0xff, //0x00002606 vmovdqu      $-9262(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0x49, 0x01, 0xf7, //0x0000260e addq         %rsi, %r15
	0x49, 0x01, 0xd5, //0x00002611 addq         %rdx, %r13
	0x0f, 0x85, 0x75, 0xff, 0xff, 0xff, //0x00002614 jne          LBB0_468
	0xe9, 0xa3, 0x07, 0x00, 0x00, //0x0000261a jmp          LBB0_381
	//0x0000261f LBB0_476
	0x4c, 0x2b, 0x7c, 0x24, 0x10, //0x0000261f subq         $16(%rsp), %r15
	0x49, 0x83, 0xc7, 0x01, //0x00002624 addq         $1, %r15
	//0x00002628 LBB0_477
	0x4d, 0x89, 0xcd, //0x00002628 movq         %r9, %r13
	0x49, 0x89, 0xc1, //0x0000262b movq         %rax, %r9
	0xe9, 0xbe, 0xed, 0xff, 0xff, //0x0000262e jmp          LBB0_240
	//0x00002633 LBB0_478
	0x48, 0x83, 0x3c, 0x24, 0xff, //0x00002633 cmpq         $-1, (%rsp)
	0x0f, 0x85, 0x13, 0x00, 0x00, 0x00, //0x00002638 jne          LBB0_480
	0x4c, 0x89, 0xf8, //0x0000263e movq         %r15, %rax
	0x48, 0x2b, 0x44, 0x24, 0x10, //0x00002641 subq         $16(%rsp), %rax
	0x48, 0x0f, 0xbc, 0xca, //0x00002646 bsfq         %rdx, %rcx
	0x48, 0x01, 0xc1, //0x0000264a addq         %rax, %rcx
	0x48, 0x89, 0x0c, 0x24, //0x0000264d movq         %rcx, (%rsp)
	//0x00002651 LBB0_480
	0x44, 0x89, 0xd0, //0x00002651 movl         %r10d, %eax
	0xf7, 0xd0, //0x00002654 notl         %eax
	0x21, 0xd0, //0x00002656 andl         %edx, %eax
	0x8d, 0x0c, 0x00, //0x00002658 leal         (%rax,%rax), %ecx
	0x41, 0x8d, 0x3c, 0x42, //0x0000265b leal         (%r10,%rax,2), %edi
	0xf7, 0xd1, //0x0000265f notl         %ecx
	0x21, 0xd1, //0x00002661 andl         %edx, %ecx
	0x81, 0xe1, 0xaa, 0xaa, 0xaa, 0xaa, //0x00002663 andl         $-1431655766, %ecx
	0x45, 0x31, 0xd2, //0x00002669 xorl         %r10d, %r10d
	0x01, 0xc1, //0x0000266c addl         %eax, %ecx
	0x41, 0x0f, 0x92, 0xc2, //0x0000266e setb         %r10b
	0x01, 0xc9, //0x00002672 addl         %ecx, %ecx
	0x81, 0xf1, 0x55, 0x55, 0x55, 0x55, //0x00002674 xorl         $1431655765, %ecx
	0x21, 0xf9, //0x0000267a andl         %edi, %ecx
	0xf7, 0xd1, //0x0000267c notl         %ecx
	0x21, 0xce, //0x0000267e andl         %ecx, %esi
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00002680 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0xfe, 0x6f, 0x25, 0x53, 0xdb, 0xff, 0xff, //0x00002685 vmovdqu      $-9389(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0x48, 0x85, 0xf6, //0x0000268d testq        %rsi, %rsi
	0x0f, 0x85, 0x61, 0xf9, 0xff, 0xff, //0x00002690 jne          LBB0_133
	//0x00002696 LBB0_481
	0x49, 0x83, 0xc7, 0x20, //0x00002696 addq         $32, %r15
	0x48, 0x83, 0xc3, 0xe0, //0x0000269a addq         $-32, %rbx
	//0x0000269e LBB0_482
	0x4d, 0x85, 0xd2, //0x0000269e testq        %r10, %r10
	0x0f, 0x85, 0x11, 0x03, 0x00, 0x00, //0x000026a1 jne          LBB0_519
	0x48, 0x8b, 0x4c, 0x24, 0x10, //0x000026a7 movq         $16(%rsp), %rcx
	0x48, 0xf7, 0xd1, //0x000026ac notq         %rcx
	0x48, 0x8b, 0x3c, 0x24, //0x000026af movq         (%rsp), %rdi
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x000026b3 movq         $24(%rsp), %r10
	0x48, 0x85, 0xdb, //0x000026b8 testq        %rbx, %rbx
	0x0f, 0x84, 0x96, 0x00, 0x00, 0x00, //0x000026bb je           LBB0_494
	//0x000026c1 LBB0_484
	0x48, 0x83, 0xc1, 0x01, //0x000026c1 addq         $1, %rcx
	//0x000026c5 LBB0_485
	0x31, 0xf6, //0x000026c5 xorl         %esi, %esi
	//0x000026c7 LBB0_486
	0x41, 0x0f, 0xb6, 0x14, 0x37, //0x000026c7 movzbl       (%r15,%rsi), %edx
	0x80, 0xfa, 0x22, //0x000026cc cmpb         $34, %dl
	0x0f, 0x84, 0x7b, 0x00, 0x00, 0x00, //0x000026cf je           LBB0_493
	0x80, 0xfa, 0x5c, //0x000026d5 cmpb         $92, %dl
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x000026d8 je           LBB0_491
	0x48, 0x83, 0xc6, 0x01, //0x000026de addq         $1, %rsi
	0x48, 0x39, 0xf3, //0x000026e2 cmpq         %rsi, %rbx
	0x0f, 0x85, 0xdc, 0xff, 0xff, 0xff, //0x000026e5 jne          LBB0_486
	0xe9, 0x71, 0x00, 0x00, 0x00, //0x000026eb jmp          LBB0_489
	//0x000026f0 LBB0_491
	0x48, 0x8d, 0x43, 0xff, //0x000026f0 leaq         $-1(%rbx), %rax
	0x48, 0x39, 0xf0, //0x000026f4 cmpq         %rsi, %rax
	0x0f, 0x84, 0xc5, 0x06, 0x00, 0x00, //0x000026f7 je           LBB0_381
	0x4a, 0x8d, 0x04, 0x39, //0x000026fd leaq         (%rcx,%r15), %rax
	0x48, 0x01, 0xf0, //0x00002701 addq         %rsi, %rax
	0x48, 0x83, 0xff, 0xff, //0x00002704 cmpq         $-1, %rdi
	0x48, 0x8b, 0x14, 0x24, //0x00002708 movq         (%rsp), %rdx
	0x48, 0x0f, 0x44, 0xd0, //0x0000270c cmoveq       %rax, %rdx
	0x48, 0x89, 0x14, 0x24, //0x00002710 movq         %rdx, (%rsp)
	0x48, 0x0f, 0x44, 0xf8, //0x00002714 cmoveq       %rax, %rdi
	0x49, 0x01, 0xf7, //0x00002718 addq         %rsi, %r15
	0x49, 0x83, 0xc7, 0x02, //0x0000271b addq         $2, %r15
	0x48, 0x89, 0xd8, //0x0000271f movq         %rbx, %rax
	0x48, 0x29, 0xf0, //0x00002722 subq         %rsi, %rax
	0x48, 0x83, 0xc0, 0xfe, //0x00002725 addq         $-2, %rax
	0x48, 0x83, 0xc3, 0xfe, //0x00002729 addq         $-2, %rbx
	0x48, 0x39, 0xf3, //0x0000272d cmpq         %rsi, %rbx
	0x48, 0x89, 0xc3, //0x00002730 movq         %rax, %rbx
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x00002733 movq         $24(%rsp), %r10
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00002738 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0xfe, 0x6f, 0x25, 0x9b, 0xda, 0xff, 0xff, //0x0000273d vmovdqu      $-9573(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0x0f, 0x85, 0x7a, 0xff, 0xff, 0xff, //0x00002745 jne          LBB0_485
	0xe9, 0x72, 0x06, 0x00, 0x00, //0x0000274b jmp          LBB0_381
	//0x00002750 LBB0_493
	0x49, 0x01, 0xf7, //0x00002750 addq         %rsi, %r15
	0x49, 0x83, 0xc7, 0x01, //0x00002753 addq         $1, %r15
	//0x00002757 LBB0_494
	0x4c, 0x2b, 0x7c, 0x24, 0x10, //0x00002757 subq         $16(%rsp), %r15
	0xe9, 0xd6, 0xed, 0xff, 0xff, //0x0000275c jmp          LBB0_259
	//0x00002761 LBB0_489
	0x80, 0xfa, 0x22, //0x00002761 cmpb         $34, %dl
	0x0f, 0x85, 0x58, 0x06, 0x00, 0x00, //0x00002764 jne          LBB0_381
	0x49, 0x01, 0xdf, //0x0000276a addq         %rbx, %r15
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x0000276d movq         $24(%rsp), %r10
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00002772 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0xfe, 0x6f, 0x25, 0x61, 0xda, 0xff, 0xff, //0x00002777 vmovdqu      $-9631(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0xe9, 0xd3, 0xff, 0xff, 0xff, //0x0000277f jmp          LBB0_494
	//0x00002784 LBB0_495
	0x48, 0x83, 0x3c, 0x24, 0xff, //0x00002784 cmpq         $-1, (%rsp)
	0x0f, 0x85, 0x13, 0x00, 0x00, 0x00, //0x00002789 jne          LBB0_497
	0x4c, 0x89, 0xf8, //0x0000278f movq         %r15, %rax
	0x48, 0x2b, 0x44, 0x24, 0x10, //0x00002792 subq         $16(%rsp), %rax
	0x48, 0x0f, 0xbc, 0xca, //0x00002797 bsfq         %rdx, %rcx
	0x48, 0x01, 0xc1, //0x0000279b addq         %rax, %rcx
	0x48, 0x89, 0x0c, 0x24, //0x0000279e movq         %rcx, (%rsp)
	//0x000027a2 LBB0_497
	0x44, 0x89, 0xd8, //0x000027a2 movl         %r11d, %eax
	0xf7, 0xd0, //0x000027a5 notl         %eax
	0x21, 0xd0, //0x000027a7 andl         %edx, %eax
	0x8d, 0x0c, 0x00, //0x000027a9 leal         (%rax,%rax), %ecx
	0x41, 0x8d, 0x34, 0x43, //0x000027ac leal         (%r11,%rax,2), %esi
	0xf7, 0xd1, //0x000027b0 notl         %ecx
	0x21, 0xd1, //0x000027b2 andl         %edx, %ecx
	0x81, 0xe1, 0xaa, 0xaa, 0xaa, 0xaa, //0x000027b4 andl         $-1431655766, %ecx
	0x45, 0x31, 0xdb, //0x000027ba xorl         %r11d, %r11d
	0x01, 0xc1, //0x000027bd addl         %eax, %ecx
	0x41, 0x0f, 0x92, 0xc3, //0x000027bf setb         %r11b
	0x01, 0xc9, //0x000027c3 addl         %ecx, %ecx
	0x81, 0xf1, 0x55, 0x55, 0x55, 0x55, //0x000027c5 xorl         $1431655765, %ecx
	0x21, 0xf1, //0x000027cb andl         %esi, %ecx
	0xf7, 0xd1, //0x000027cd notl         %ecx
	0x41, 0x21, 0xca, //0x000027cf andl         %ecx, %r10d
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x000027d2 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0xfe, 0x6f, 0x25, 0x01, 0xda, 0xff, 0xff, //0x000027d7 vmovdqu      $-9727(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0x4d, 0x85, 0xd2, //0x000027df testq        %r10, %r10
	0x0f, 0x85, 0x6a, 0xf8, 0xff, 0xff, //0x000027e2 jne          LBB0_232
	//0x000027e8 LBB0_498
	0xba, 0x40, 0x00, 0x00, 0x00, //0x000027e8 movl         $64, %edx
	//0x000027ed LBB0_499
	0xc5, 0xbd, 0x64, 0xc8, //0x000027ed vpcmpgtb     %ymm0, %ymm8, %ymm1
	0xc4, 0xc1, 0x7d, 0x64, 0xc1, //0x000027f1 vpcmpgtb     %ymm9, %ymm0, %ymm0
	0xc5, 0xf5, 0xdb, 0xc0, //0x000027f6 vpand        %ymm0, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x000027fa vpmovmskb    %ymm0, %esi
	0x0f, 0xbc, 0xfe, //0x000027fe bsfl         %esi, %edi
	0x4d, 0x85, 0xd2, //0x00002801 testq        %r10, %r10
	0x0f, 0x84, 0x2e, 0x00, 0x00, 0x00, //0x00002804 je           LBB0_502
	0x85, 0xf6, //0x0000280a testl        %esi, %esi
	0xb8, 0x40, 0x00, 0x00, 0x00, //0x0000280c movl         $64, %eax
	0x0f, 0x44, 0xf8, //0x00002811 cmovel       %eax, %edi
	0x48, 0x39, 0xfa, //0x00002814 cmpq         %rdi, %rdx
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x00002817 movq         $24(%rsp), %r10
	0x0f, 0x87, 0x35, 0x0f, 0x00, 0x00, //0x0000281c ja           LBB0_677
	0x4c, 0x2b, 0x7c, 0x24, 0x10, //0x00002822 subq         $16(%rsp), %r15
	0x49, 0x01, 0xd7, //0x00002827 addq         %rdx, %r15
	0x49, 0x83, 0xc7, 0x01, //0x0000282a addq         $1, %r15
	0x4c, 0x8b, 0x5c, 0x24, 0x08, //0x0000282e movq         $8(%rsp), %r11
	0xe9, 0xff, 0xec, 0xff, 0xff, //0x00002833 jmp          LBB0_259
	//0x00002838 LBB0_502
	0x85, 0xf6, //0x00002838 testl        %esi, %esi
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x0000283a movq         $24(%rsp), %r10
	0x0f, 0x85, 0xef, 0x0e, 0x00, 0x00, //0x0000283f jne          LBB0_678
	0x49, 0x83, 0xc7, 0x20, //0x00002845 addq         $32, %r15
	0x48, 0x83, 0xc3, 0xe0, //0x00002849 addq         $-32, %rbx
	//0x0000284d LBB0_504
	0x4d, 0x85, 0xdb, //0x0000284d testq        %r11, %r11
	0x0f, 0x85, 0xbc, 0x01, 0x00, 0x00, //0x00002850 jne          LBB0_521
	0x48, 0x8b, 0x0c, 0x24, //0x00002856 movq         (%rsp), %rcx
	0x4c, 0x8b, 0x5c, 0x24, 0x08, //0x0000285a movq         $8(%rsp), %r11
	0x48, 0x85, 0xdb, //0x0000285f testq        %rbx, %rbx
	0x0f, 0x84, 0x5a, 0x05, 0x00, 0x00, //0x00002862 je           LBB0_381
	//0x00002868 LBB0_506
	0x41, 0x0f, 0xb6, 0x17, //0x00002868 movzbl       (%r15), %edx
	0x80, 0xfa, 0x22, //0x0000286c cmpb         $34, %dl
	0x0f, 0x84, 0x83, 0x00, 0x00, 0x00, //0x0000286f je           LBB0_514
	0x80, 0xfa, 0x5c, //0x00002875 cmpb         $92, %dl
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x00002878 je           LBB0_510
	0x80, 0xfa, 0x1f, //0x0000287e cmpb         $31, %dl
	0x0f, 0x86, 0xb9, 0x0e, 0x00, 0x00, //0x00002881 jbe          LBB0_674
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00002887 movq         $-1, %rdx
	0xbe, 0x01, 0x00, 0x00, 0x00, //0x0000288e movl         $1, %esi
	0x49, 0x01, 0xf7, //0x00002893 addq         %rsi, %r15
	0x48, 0x01, 0xd3, //0x00002896 addq         %rdx, %rbx
	0x0f, 0x85, 0xc9, 0xff, 0xff, 0xff, //0x00002899 jne          LBB0_506
	0xe9, 0x1e, 0x05, 0x00, 0x00, //0x0000289f jmp          LBB0_381
	//0x000028a4 LBB0_510
	0x48, 0x83, 0xfb, 0x01, //0x000028a4 cmpq         $1, %rbx
	0x0f, 0x84, 0xce, 0x04, 0x00, 0x00, //0x000028a8 je           LBB0_565
	0x48, 0xc7, 0xc2, 0xfe, 0xff, 0xff, 0xff, //0x000028ae movq         $-2, %rdx
	0xbe, 0x02, 0x00, 0x00, 0x00, //0x000028b5 movl         $2, %esi
	0x48, 0x83, 0xf9, 0xff, //0x000028ba cmpq         $-1, %rcx
	0x0f, 0x85, 0x0c, 0x00, 0x00, 0x00, //0x000028be jne          LBB0_513
	0x4c, 0x89, 0xf9, //0x000028c4 movq         %r15, %rcx
	0x48, 0x2b, 0x4c, 0x24, 0x10, //0x000028c7 subq         $16(%rsp), %rcx
	0x48, 0x89, 0x0c, 0x24, //0x000028cc movq         %rcx, (%rsp)
	//0x000028d0 LBB0_513
	0x4c, 0x8b, 0x5c, 0x24, 0x08, //0x000028d0 movq         $8(%rsp), %r11
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x000028d5 movq         $24(%rsp), %r10
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x000028da vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0xfe, 0x6f, 0x25, 0xf9, 0xd8, 0xff, 0xff, //0x000028df vmovdqu      $-9991(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0x49, 0x01, 0xf7, //0x000028e7 addq         %rsi, %r15
	0x48, 0x01, 0xd3, //0x000028ea addq         %rdx, %rbx
	0x0f, 0x85, 0x75, 0xff, 0xff, 0xff, //0x000028ed jne          LBB0_506
	0xe9, 0xca, 0x04, 0x00, 0x00, //0x000028f3 jmp          LBB0_381
	//0x000028f8 LBB0_514
	0x4c, 0x2b, 0x7c, 0x24, 0x10, //0x000028f8 subq         $16(%rsp), %r15
	0x49, 0x83, 0xc7, 0x01, //0x000028fd addq         $1, %r15
	0xe9, 0x31, 0xec, 0xff, 0xff, //0x00002901 jmp          LBB0_259
	//0x00002906 LBB0_515
	0x48, 0x85, 0xdb, //0x00002906 testq        %rbx, %rbx
	0x0f, 0x84, 0xb3, 0x04, 0x00, 0x00, //0x00002909 je           LBB0_381
	0x4c, 0x89, 0xca, //0x0000290f movq         %r9, %rdx
	0x48, 0x8b, 0x4c, 0x24, 0x10, //0x00002912 movq         $16(%rsp), %rcx
	0x48, 0xf7, 0xd1, //0x00002917 notq         %rcx
	0x49, 0x8d, 0x04, 0x0f, //0x0000291a leaq         (%r15,%rcx), %rax
	0x4c, 0x8b, 0x0c, 0x24, //0x0000291e movq         (%rsp), %r9
	0x49, 0x83, 0xf9, 0xff, //0x00002922 cmpq         $-1, %r9
	0x4c, 0x89, 0xcf, //0x00002926 movq         %r9, %rdi
	0x4c, 0x0f, 0x44, 0xc8, //0x00002929 cmoveq       %rax, %r9
	0x48, 0x0f, 0x44, 0xf8, //0x0000292d cmoveq       %rax, %rdi
	0x49, 0x83, 0xc7, 0x01, //0x00002931 addq         $1, %r15
	0x48, 0x83, 0xc3, 0xff, //0x00002935 addq         $-1, %rbx
	0x4c, 0x89, 0x0c, 0x24, //0x00002939 movq         %r9, (%rsp)
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x0000293d movq         $24(%rsp), %r10
	0x49, 0x89, 0xd1, //0x00002942 movq         %rdx, %r9
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00002945 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0xfe, 0x6f, 0x25, 0x8e, 0xd8, 0xff, 0xff, //0x0000294a vmovdqu      $-10098(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0x48, 0x85, 0xdb, //0x00002952 testq        %rbx, %rbx
	0x0f, 0x85, 0x8d, 0xfa, 0xff, 0xff, //0x00002955 jne          LBB0_446
	0xe9, 0x1e, 0xfb, 0xff, 0xff, //0x0000295b jmp          LBB0_456
	//0x00002960 LBB0_517
	0x4d, 0x85, 0xed, //0x00002960 testq        %r13, %r13
	0x0f, 0x84, 0x13, 0x04, 0x00, 0x00, //0x00002963 je           LBB0_565
	0x48, 0x8b, 0x4c, 0x24, 0x10, //0x00002969 movq         $16(%rsp), %rcx
	0x48, 0xf7, 0xd1, //0x0000296e notq         %rcx
	0x4c, 0x01, 0xf9, //0x00002971 addq         %r15, %rcx
	0x48, 0x8b, 0x34, 0x24, //0x00002974 movq         (%rsp), %rsi
	0x48, 0x83, 0xfe, 0xff, //0x00002978 cmpq         $-1, %rsi
	0x48, 0x89, 0xf2, //0x0000297c movq         %rsi, %rdx
	0x48, 0x0f, 0x44, 0xd1, //0x0000297f cmoveq       %rcx, %rdx
	0x48, 0x0f, 0x45, 0xce, //0x00002983 cmovneq      %rsi, %rcx
	0x49, 0x83, 0xc7, 0x01, //0x00002987 addq         $1, %r15
	0x49, 0x83, 0xc5, 0xff, //0x0000298b addq         $-1, %r13
	0x48, 0x89, 0x14, 0x24, //0x0000298f movq         %rdx, (%rsp)
	0x4c, 0x8b, 0x5c, 0x24, 0x08, //0x00002993 movq         $8(%rsp), %r11
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x00002998 movq         $24(%rsp), %r10
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x0000299d vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0xfe, 0x6f, 0x25, 0x36, 0xd8, 0xff, 0xff, //0x000029a2 vmovdqu      $-10186(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0x4d, 0x85, 0xed, //0x000029aa testq        %r13, %r13
	0x0f, 0x85, 0xdc, 0xfb, 0xff, 0xff, //0x000029ad jne          LBB0_468
	0xe9, 0x0a, 0x04, 0x00, 0x00, //0x000029b3 jmp          LBB0_381
	//0x000029b8 LBB0_519
	0x48, 0x85, 0xdb, //0x000029b8 testq        %rbx, %rbx
	0x0f, 0x84, 0x01, 0x04, 0x00, 0x00, //0x000029bb je           LBB0_381
	0x4c, 0x89, 0xca, //0x000029c1 movq         %r9, %rdx
	0x48, 0x8b, 0x4c, 0x24, 0x10, //0x000029c4 movq         $16(%rsp), %rcx
	0x48, 0xf7, 0xd1, //0x000029c9 notq         %rcx
	0x49, 0x8d, 0x04, 0x0f, //0x000029cc leaq         (%r15,%rcx), %rax
	0x4c, 0x8b, 0x0c, 0x24, //0x000029d0 movq         (%rsp), %r9
	0x49, 0x83, 0xf9, 0xff, //0x000029d4 cmpq         $-1, %r9
	0x4c, 0x89, 0xcf, //0x000029d8 movq         %r9, %rdi
	0x4c, 0x0f, 0x44, 0xc8, //0x000029db cmoveq       %rax, %r9
	0x48, 0x0f, 0x44, 0xf8, //0x000029df cmoveq       %rax, %rdi
	0x49, 0x83, 0xc7, 0x01, //0x000029e3 addq         $1, %r15
	0x48, 0x83, 0xc3, 0xff, //0x000029e7 addq         $-1, %rbx
	0x4c, 0x89, 0x0c, 0x24, //0x000029eb movq         %r9, (%rsp)
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x000029ef movq         $24(%rsp), %r10
	0x49, 0x89, 0xd1, //0x000029f4 movq         %rdx, %r9
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x000029f7 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0xfe, 0x6f, 0x25, 0xdc, 0xd7, 0xff, 0xff, //0x000029fc vmovdqu      $-10276(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0x48, 0x85, 0xdb, //0x00002a04 testq        %rbx, %rbx
	0x0f, 0x85, 0xb4, 0xfc, 0xff, 0xff, //0x00002a07 jne          LBB0_484
	0xe9, 0x45, 0xfd, 0xff, 0xff, //0x00002a0d jmp          LBB0_494
	//0x00002a12 LBB0_521
	0x48, 0x85, 0xdb, //0x00002a12 testq        %rbx, %rbx
	0x0f, 0x84, 0x61, 0x03, 0x00, 0x00, //0x00002a15 je           LBB0_565
	0x48, 0x8b, 0x4c, 0x24, 0x10, //0x00002a1b movq         $16(%rsp), %rcx
	0x48, 0xf7, 0xd1, //0x00002a20 notq         %rcx
	0x4c, 0x01, 0xf9, //0x00002a23 addq         %r15, %rcx
	0x48, 0x8b, 0x14, 0x24, //0x00002a26 movq         (%rsp), %rdx
	0x48, 0x83, 0xfa, 0xff, //0x00002a2a cmpq         $-1, %rdx
	0x48, 0x89, 0xd0, //0x00002a2e movq         %rdx, %rax
	0x48, 0x0f, 0x44, 0xc1, //0x00002a31 cmoveq       %rcx, %rax
	0x48, 0x0f, 0x45, 0xca, //0x00002a35 cmovneq      %rdx, %rcx
	0x49, 0x83, 0xc7, 0x01, //0x00002a39 addq         $1, %r15
	0x48, 0x83, 0xc3, 0xff, //0x00002a3d addq         $-1, %rbx
	0x48, 0x89, 0x04, 0x24, //0x00002a41 movq         %rax, (%rsp)
	0x4c, 0x8b, 0x5c, 0x24, 0x08, //0x00002a45 movq         $8(%rsp), %r11
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x00002a4a movq         $24(%rsp), %r10
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00002a4f vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0xfe, 0x6f, 0x25, 0x84, 0xd7, 0xff, 0xff, //0x00002a54 vmovdqu      $-10364(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0x48, 0x85, 0xdb, //0x00002a5c testq        %rbx, %rbx
	0x0f, 0x85, 0x03, 0xfe, 0xff, 0xff, //0x00002a5f jne          LBB0_506
	0xe9, 0x58, 0x03, 0x00, 0x00, //0x00002a65 jmp          LBB0_381
	//0x00002a6a LBB0_523
	0x48, 0x83, 0x3c, 0x24, 0xff, //0x00002a6a cmpq         $-1, (%rsp)
	0x0f, 0x85, 0x13, 0x00, 0x00, 0x00, //0x00002a6f jne          LBB0_525
	0x4c, 0x89, 0xf8, //0x00002a75 movq         %r15, %rax
	0x48, 0x2b, 0x44, 0x24, 0x10, //0x00002a78 subq         $16(%rsp), %rax
	0x48, 0x0f, 0xbc, 0xca, //0x00002a7d bsfq         %rdx, %rcx
	0x48, 0x01, 0xc1, //0x00002a81 addq         %rax, %rcx
	0x48, 0x89, 0x0c, 0x24, //0x00002a84 movq         %rcx, (%rsp)
	//0x00002a88 LBB0_525
	0x44, 0x89, 0xd0, //0x00002a88 movl         %r10d, %eax
	0xf7, 0xd0, //0x00002a8b notl         %eax
	0x21, 0xd0, //0x00002a8d andl         %edx, %eax
	0x8d, 0x0c, 0x00, //0x00002a8f leal         (%rax,%rax), %ecx
	0x41, 0x8d, 0x3c, 0x42, //0x00002a92 leal         (%r10,%rax,2), %edi
	0xf7, 0xd1, //0x00002a96 notl         %ecx
	0x21, 0xd1, //0x00002a98 andl         %edx, %ecx
	0x81, 0xe1, 0xaa, 0xaa, 0xaa, 0xaa, //0x00002a9a andl         $-1431655766, %ecx
	0x45, 0x31, 0xd2, //0x00002aa0 xorl         %r10d, %r10d
	0x01, 0xc1, //0x00002aa3 addl         %eax, %ecx
	0x41, 0x0f, 0x92, 0xc2, //0x00002aa5 setb         %r10b
	0x01, 0xc9, //0x00002aa9 addl         %ecx, %ecx
	0x81, 0xf1, 0x55, 0x55, 0x55, 0x55, //0x00002aab xorl         $1431655765, %ecx
	0x21, 0xf9, //0x00002ab1 andl         %edi, %ecx
	0xf7, 0xd1, //0x00002ab3 notl         %ecx
	0x21, 0xce, //0x00002ab5 andl         %ecx, %esi
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00002ab7 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0xfe, 0x6f, 0x25, 0x1c, 0xd7, 0xff, 0xff, //0x00002abc vmovdqu      $-10468(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0x48, 0x85, 0xf6, //0x00002ac4 testq        %rsi, %rsi
	0x0f, 0x85, 0x44, 0xf6, 0xff, 0xff, //0x00002ac7 jne          LBB0_275
	//0x00002acd LBB0_526
	0x49, 0x83, 0xc7, 0x20, //0x00002acd addq         $32, %r15
	0x48, 0x83, 0xc3, 0xe0, //0x00002ad1 addq         $-32, %rbx
	//0x00002ad5 LBB0_527
	0x4d, 0x85, 0xd2, //0x00002ad5 testq        %r10, %r10
	0x0f, 0x85, 0x20, 0x02, 0x00, 0x00, //0x00002ad8 jne          LBB0_561
	0x48, 0x8b, 0x4c, 0x24, 0x10, //0x00002ade movq         $16(%rsp), %rcx
	0x48, 0xf7, 0xd1, //0x00002ae3 notq         %rcx
	0x48, 0x8b, 0x3c, 0x24, //0x00002ae6 movq         (%rsp), %rdi
	0x48, 0x85, 0xdb, //0x00002aea testq        %rbx, %rbx
	0x0f, 0x84, 0x84, 0x00, 0x00, 0x00, //0x00002aed je           LBB0_539
	//0x00002af3 LBB0_529
	0x48, 0x83, 0xc1, 0x01, //0x00002af3 addq         $1, %rcx
	//0x00002af7 LBB0_530
	0x31, 0xf6, //0x00002af7 xorl         %esi, %esi
	//0x00002af9 LBB0_531
	0x41, 0x0f, 0xb6, 0x14, 0x37, //0x00002af9 movzbl       (%r15,%rsi), %edx
	0x80, 0xfa, 0x22, //0x00002afe cmpb         $34, %dl
	0x0f, 0x84, 0x69, 0x00, 0x00, 0x00, //0x00002b01 je           LBB0_538
	0x80, 0xfa, 0x5c, //0x00002b07 cmpb         $92, %dl
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00002b0a je           LBB0_536
	0x48, 0x83, 0xc6, 0x01, //0x00002b10 addq         $1, %rsi
	0x48, 0x39, 0xf3, //0x00002b14 cmpq         %rsi, %rbx
	0x0f, 0x85, 0xdc, 0xff, 0xff, 0xff, //0x00002b17 jne          LBB0_531
	0xe9, 0x5f, 0x00, 0x00, 0x00, //0x00002b1d jmp          LBB0_534
	//0x00002b22 LBB0_536
	0x48, 0x8d, 0x43, 0xff, //0x00002b22 leaq         $-1(%rbx), %rax
	0x48, 0x39, 0xf0, //0x00002b26 cmpq         %rsi, %rax
	0x0f, 0x84, 0x93, 0x02, 0x00, 0x00, //0x00002b29 je           LBB0_381
	0x4a, 0x8d, 0x04, 0x39, //0x00002b2f leaq         (%rcx,%r15), %rax
	0x48, 0x01, 0xf0, //0x00002b33 addq         %rsi, %rax
	0x48, 0x83, 0xff, 0xff, //0x00002b36 cmpq         $-1, %rdi
	0x48, 0x8b, 0x14, 0x24, //0x00002b3a movq         (%rsp), %rdx
	0x48, 0x0f, 0x44, 0xd0, //0x00002b3e cmoveq       %rax, %rdx
	0x48, 0x89, 0x14, 0x24, //0x00002b42 movq         %rdx, (%rsp)
	0x48, 0x0f, 0x44, 0xf8, //0x00002b46 cmoveq       %rax, %rdi
	0x49, 0x01, 0xf7, //0x00002b4a addq         %rsi, %r15
	0x49, 0x83, 0xc7, 0x02, //0x00002b4d addq         $2, %r15
	0x48, 0x89, 0xd8, //0x00002b51 movq         %rbx, %rax
	0x48, 0x29, 0xf0, //0x00002b54 subq         %rsi, %rax
	0x48, 0x83, 0xc0, 0xfe, //0x00002b57 addq         $-2, %rax
	0x48, 0x83, 0xc3, 0xfe, //0x00002b5b addq         $-2, %rbx
	0x48, 0x39, 0xf3, //0x00002b5f cmpq         %rsi, %rbx
	0x48, 0x89, 0xc3, //0x00002b62 movq         %rax, %rbx
	0x0f, 0x85, 0x8c, 0xff, 0xff, 0xff, //0x00002b65 jne          LBB0_530
	0xe9, 0x52, 0x02, 0x00, 0x00, //0x00002b6b jmp          LBB0_381
	//0x00002b70 LBB0_538
	0x49, 0x01, 0xf7, //0x00002b70 addq         %rsi, %r15
	0x49, 0x83, 0xc7, 0x01, //0x00002b73 addq         $1, %r15
	//0x00002b77 LBB0_539
	0x4c, 0x2b, 0x7c, 0x24, 0x10, //0x00002b77 subq         $16(%rsp), %r15
	0xe9, 0x66, 0x01, 0x00, 0x00, //0x00002b7c jmp          LBB0_560
	//0x00002b81 LBB0_534
	0x80, 0xfa, 0x22, //0x00002b81 cmpb         $34, %dl
	0x0f, 0x85, 0x38, 0x02, 0x00, 0x00, //0x00002b84 jne          LBB0_381
	0x49, 0x01, 0xdf, //0x00002b8a addq         %rbx, %r15
	0xe9, 0xe5, 0xff, 0xff, 0xff, //0x00002b8d jmp          LBB0_539
	//0x00002b92 LBB0_540
	0x48, 0x83, 0x3c, 0x24, 0xff, //0x00002b92 cmpq         $-1, (%rsp)
	0x0f, 0x85, 0x13, 0x00, 0x00, 0x00, //0x00002b97 jne          LBB0_542
	0x4c, 0x89, 0xf8, //0x00002b9d movq         %r15, %rax
	0x48, 0x2b, 0x44, 0x24, 0x10, //0x00002ba0 subq         $16(%rsp), %rax
	0x48, 0x0f, 0xbc, 0xca, //0x00002ba5 bsfq         %rdx, %rcx
	0x48, 0x01, 0xc1, //0x00002ba9 addq         %rax, %rcx
	0x48, 0x89, 0x0c, 0x24, //0x00002bac movq         %rcx, (%rsp)
	//0x00002bb0 LBB0_542
	0x44, 0x89, 0xd8, //0x00002bb0 movl         %r11d, %eax
	0xf7, 0xd0, //0x00002bb3 notl         %eax
	0x21, 0xd0, //0x00002bb5 andl         %edx, %eax
	0x8d, 0x0c, 0x00, //0x00002bb7 leal         (%rax,%rax), %ecx
	0x41, 0x8d, 0x34, 0x43, //0x00002bba leal         (%r11,%rax,2), %esi
	0xf7, 0xd1, //0x00002bbe notl         %ecx
	0x21, 0xd1, //0x00002bc0 andl         %edx, %ecx
	0x81, 0xe1, 0xaa, 0xaa, 0xaa, 0xaa, //0x00002bc2 andl         $-1431655766, %ecx
	0x45, 0x31, 0xdb, //0x00002bc8 xorl         %r11d, %r11d
	0x01, 0xc1, //0x00002bcb addl         %eax, %ecx
	0x41, 0x0f, 0x92, 0xc3, //0x00002bcd setb         %r11b
	0x01, 0xc9, //0x00002bd1 addl         %ecx, %ecx
	0x81, 0xf1, 0x55, 0x55, 0x55, 0x55, //0x00002bd3 xorl         $1431655765, %ecx
	0x21, 0xf1, //0x00002bd9 andl         %esi, %ecx
	0xf7, 0xd1, //0x00002bdb notl         %ecx
	0x41, 0x21, 0xca, //0x00002bdd andl         %ecx, %r10d
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00002be0 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0xfe, 0x6f, 0x25, 0xf3, 0xd5, 0xff, 0xff, //0x00002be5 vmovdqu      $-10765(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0x4d, 0x85, 0xd2, //0x00002bed testq        %r10, %r10
	0x0f, 0x85, 0x76, 0xf5, 0xff, 0xff, //0x00002bf0 jne          LBB0_316
	//0x00002bf6 LBB0_543
	0xba, 0x40, 0x00, 0x00, 0x00, //0x00002bf6 movl         $64, %edx
	//0x00002bfb LBB0_544
	0xc5, 0xbd, 0x64, 0xc8, //0x00002bfb vpcmpgtb     %ymm0, %ymm8, %ymm1
	0xc4, 0xc1, 0x7d, 0x64, 0xc1, //0x00002bff vpcmpgtb     %ymm9, %ymm0, %ymm0
	0xc5, 0xf5, 0xdb, 0xc0, //0x00002c04 vpand        %ymm0, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x00002c08 vpmovmskb    %ymm0, %esi
	0x0f, 0xbc, 0xfe, //0x00002c0c bsfl         %esi, %edi
	0x4d, 0x85, 0xd2, //0x00002c0f testq        %r10, %r10
	0x0f, 0x84, 0x2e, 0x00, 0x00, 0x00, //0x00002c12 je           LBB0_547
	0x85, 0xf6, //0x00002c18 testl        %esi, %esi
	0xb8, 0x40, 0x00, 0x00, 0x00, //0x00002c1a movl         $64, %eax
	0x0f, 0x44, 0xf8, //0x00002c1f cmovel       %eax, %edi
	0x48, 0x39, 0xfa, //0x00002c22 cmpq         %rdi, %rdx
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x00002c25 movq         $24(%rsp), %r10
	0x0f, 0x87, 0x27, 0x0b, 0x00, 0x00, //0x00002c2a ja           LBB0_677
	0x4c, 0x2b, 0x7c, 0x24, 0x10, //0x00002c30 subq         $16(%rsp), %r15
	0x49, 0x01, 0xd7, //0x00002c35 addq         %rdx, %r15
	0x49, 0x83, 0xc7, 0x01, //0x00002c38 addq         $1, %r15
	0x4c, 0x8b, 0x5c, 0x24, 0x08, //0x00002c3c movq         $8(%rsp), %r11
	0xe9, 0xc0, 0xf0, 0xff, 0xff, //0x00002c41 jmp          LBB0_379
	//0x00002c46 LBB0_547
	0x85, 0xf6, //0x00002c46 testl        %esi, %esi
	0x0f, 0x85, 0xe6, 0x0a, 0x00, 0x00, //0x00002c48 jne          LBB0_678
	0x49, 0x83, 0xc7, 0x20, //0x00002c4e addq         $32, %r15
	0x48, 0x83, 0xc3, 0xe0, //0x00002c52 addq         $-32, %rbx
	//0x00002c56 LBB0_549
	0x4d, 0x85, 0xdb, //0x00002c56 testq        %r11, %r11
	0x0f, 0x85, 0xe1, 0x00, 0x00, 0x00, //0x00002c59 jne          LBB0_563
	0x48, 0x8b, 0x0c, 0x24, //0x00002c5f movq         (%rsp), %rcx
	0x48, 0x85, 0xdb, //0x00002c63 testq        %rbx, %rbx
	0x0f, 0x84, 0x10, 0x01, 0x00, 0x00, //0x00002c66 je           LBB0_565
	//0x00002c6c LBB0_551
	0x41, 0x0f, 0xb6, 0x17, //0x00002c6c movzbl       (%r15), %edx
	0x80, 0xfa, 0x22, //0x00002c70 cmpb         $34, %dl
	0x0f, 0x84, 0x60, 0x00, 0x00, 0x00, //0x00002c73 je           LBB0_559
	0x80, 0xfa, 0x5c, //0x00002c79 cmpb         $92, %dl
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x00002c7c je           LBB0_556
	0x80, 0xfa, 0x1f, //0x00002c82 cmpb         $31, %dl
	0x0f, 0x86, 0x02, 0x0b, 0x00, 0x00, //0x00002c85 jbe          LBB0_679
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00002c8b movq         $-1, %rdx
	0xbe, 0x01, 0x00, 0x00, 0x00, //0x00002c92 movl         $1, %esi
	//0x00002c97 LBB0_555
	0x49, 0x01, 0xf7, //0x00002c97 addq         %rsi, %r15
	0x48, 0x01, 0xd3, //0x00002c9a addq         %rdx, %rbx
	0x0f, 0x85, 0xc9, 0xff, 0xff, 0xff, //0x00002c9d jne          LBB0_551
	0xe9, 0xd4, 0x00, 0x00, 0x00, //0x00002ca3 jmp          LBB0_565
	//0x00002ca8 LBB0_556
	0x48, 0x83, 0xfb, 0x01, //0x00002ca8 cmpq         $1, %rbx
	0x0f, 0x84, 0xca, 0x00, 0x00, 0x00, //0x00002cac je           LBB0_565
	0x48, 0xc7, 0xc2, 0xfe, 0xff, 0xff, 0xff, //0x00002cb2 movq         $-2, %rdx
	0xbe, 0x02, 0x00, 0x00, 0x00, //0x00002cb9 movl         $2, %esi
	0x48, 0x83, 0xf9, 0xff, //0x00002cbe cmpq         $-1, %rcx
	0x0f, 0x85, 0xcf, 0xff, 0xff, 0xff, //0x00002cc2 jne          LBB0_555
	0x4c, 0x89, 0xf9, //0x00002cc8 movq         %r15, %rcx
	0x48, 0x2b, 0x4c, 0x24, 0x10, //0x00002ccb subq         $16(%rsp), %rcx
	0x48, 0x89, 0x0c, 0x24, //0x00002cd0 movq         %rcx, (%rsp)
	0xe9, 0xbe, 0xff, 0xff, 0xff, //0x00002cd4 jmp          LBB0_555
	//0x00002cd9 LBB0_559
	0x4c, 0x2b, 0x7c, 0x24, 0x10, //0x00002cd9 subq         $16(%rsp), %r15
	0x49, 0x83, 0xc7, 0x01, //0x00002cde addq         $1, %r15
	0x4c, 0x8b, 0x5c, 0x24, 0x08, //0x00002ce2 movq         $8(%rsp), %r11
	//0x00002ce7 LBB0_560
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x00002ce7 movq         $24(%rsp), %r10
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00002cec vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0xfe, 0x6f, 0x25, 0xe7, 0xd4, 0xff, 0xff, //0x00002cf1 vmovdqu      $-11033(%rip), %ymm4  /* LCPI0_19+0(%rip) */
	0xe9, 0x08, 0xf0, 0xff, 0xff, //0x00002cf9 jmp          LBB0_379
	//0x00002cfe LBB0_561
	0x48, 0x85, 0xdb, //0x00002cfe testq        %rbx, %rbx
	0x0f, 0x84, 0xbb, 0x00, 0x00, 0x00, //0x00002d01 je           LBB0_381
	0x48, 0x8b, 0x4c, 0x24, 0x10, //0x00002d07 movq         $16(%rsp), %rcx
	0x48, 0xf7, 0xd1, //0x00002d0c notq         %rcx
	0x49, 0x8d, 0x04, 0x0f, //0x00002d0f leaq         (%r15,%rcx), %rax
	0x48, 0x8b, 0x14, 0x24, //0x00002d13 movq         (%rsp), %rdx
	0x48, 0x83, 0xfa, 0xff, //0x00002d17 cmpq         $-1, %rdx
	0x48, 0x89, 0xd7, //0x00002d1b movq         %rdx, %rdi
	0x48, 0x0f, 0x44, 0xd0, //0x00002d1e cmoveq       %rax, %rdx
	0x48, 0x0f, 0x44, 0xf8, //0x00002d22 cmoveq       %rax, %rdi
	0x49, 0x83, 0xc7, 0x01, //0x00002d26 addq         $1, %r15
	0x48, 0x83, 0xc3, 0xff, //0x00002d2a addq         $-1, %rbx
	0x48, 0x89, 0x14, 0x24, //0x00002d2e movq         %rdx, (%rsp)
	0x48, 0x85, 0xdb, //0x00002d32 testq        %rbx, %rbx
	0x0f, 0x85, 0xb8, 0xfd, 0xff, 0xff, //0x00002d35 jne          LBB0_529
	0xe9, 0x37, 0xfe, 0xff, 0xff, //0x00002d3b jmp          LBB0_539
	//0x00002d40 LBB0_563
	0x48, 0x85, 0xdb, //0x00002d40 testq        %rbx, %rbx
	0x0f, 0x84, 0x33, 0x00, 0x00, 0x00, //0x00002d43 je           LBB0_565
	0x48, 0x8b, 0x4c, 0x24, 0x10, //0x00002d49 movq         $16(%rsp), %rcx
	0x48, 0xf7, 0xd1, //0x00002d4e notq         %rcx
	0x4c, 0x01, 0xf9, //0x00002d51 addq         %r15, %rcx
	0x48, 0x8b, 0x14, 0x24, //0x00002d54 movq         (%rsp), %rdx
	0x48, 0x83, 0xfa, 0xff, //0x00002d58 cmpq         $-1, %rdx
	0x48, 0x89, 0xd0, //0x00002d5c movq         %rdx, %rax
	0x48, 0x0f, 0x44, 0xc1, //0x00002d5f cmoveq       %rcx, %rax
	0x48, 0x0f, 0x45, 0xca, //0x00002d63 cmovneq      %rdx, %rcx
	0x49, 0x83, 0xc7, 0x01, //0x00002d67 addq         $1, %r15
	0x48, 0x83, 0xc3, 0xff, //0x00002d6b addq         $-1, %rbx
	0x48, 0x89, 0x04, 0x24, //0x00002d6f movq         %rax, (%rsp)
	0x48, 0x85, 0xdb, //0x00002d73 testq        %rbx, %rbx
	0x0f, 0x85, 0xf0, 0xfe, 0xff, 0xff, //0x00002d76 jne          LBB0_551
	//0x00002d7c LBB0_565
	0x4c, 0x8b, 0x5c, 0x24, 0x08, //0x00002d7c movq         $8(%rsp), %r11
	0xe9, 0x3c, 0x00, 0x00, 0x00, //0x00002d81 jmp          LBB0_381
	//0x00002d86 LBB0_566
	0x4d, 0x89, 0x33, //0x00002d86 movq         %r14, (%r11)
	//0x00002d89 LBB0_567
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00002d89 movq         $-1, %r15
	0xe9, 0x43, 0x07, 0x00, 0x00, //0x00002d90 jmp          LBB0_638
	//0x00002d95 LBB0_568
	0x48, 0x8d, 0x48, 0x04, //0x00002d95 leaq         $4(%rax), %rcx
	0x49, 0x3b, 0x4d, 0x08, //0x00002d99 cmpq         $8(%r13), %rcx
	0x0f, 0x86, 0xaf, 0xd8, 0xff, 0xff, //0x00002d9d jbe          LBB0_52
	0xe9, 0x30, 0x07, 0x00, 0x00, //0x00002da3 jmp          LBB0_638
	//0x00002da8 LBB0_650
	0x49, 0xc7, 0xc7, 0xf9, 0xff, 0xff, 0xff, //0x00002da8 movq         $-7, %r15
	0xe9, 0x24, 0x07, 0x00, 0x00, //0x00002daf jmp          LBB0_638
	//0x00002db4 LBB0_380
	0x49, 0x83, 0xff, 0xff, //0x00002db4 cmpq         $-1, %r15
	0x48, 0x8b, 0x14, 0x24, //0x00002db8 movq         (%rsp), %rdx
	0x0f, 0x85, 0x0a, 0x00, 0x00, 0x00, //0x00002dbc jne          LBB0_382
	//0x00002dc2 LBB0_381
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00002dc2 movq         $-1, %r15
	0x4c, 0x89, 0xe2, //0x00002dc9 movq         %r12, %rdx
	//0x00002dcc LBB0_382
	0x49, 0x89, 0x13, //0x00002dcc movq         %rdx, (%r11)
	0xe9, 0x04, 0x07, 0x00, 0x00, //0x00002dcf jmp          LBB0_638
	//0x00002dd4 LBB0_242
	0x49, 0x83, 0xc0, 0xff, //0x00002dd4 addq         $-1, %r8
	0x4d, 0x89, 0xc7, //0x00002dd8 movq         %r8, %r15
	0xe9, 0xf8, 0x06, 0x00, 0x00, //0x00002ddb jmp          LBB0_638
	//0x00002de0 LBB0_569
	0x49, 0x89, 0x03, //0x00002de0 movq         %rax, (%r11)
	0xe9, 0xe9, 0x06, 0x00, 0x00, //0x00002de3 jmp          LBB0_637
	//0x00002de8 LBB0_570
	0x4c, 0x89, 0xe9, //0x00002de8 movq         %r13, %rcx
	0x4d, 0x89, 0xdd, //0x00002deb movq         %r11, %r13
	0x4c, 0x8b, 0x41, 0x08, //0x00002dee movq         $8(%rcx), %r8
	0x4d, 0x89, 0xc6, //0x00002df2 movq         %r8, %r14
	0x49, 0x29, 0xd6, //0x00002df5 subq         %rdx, %r14
	0x49, 0x83, 0xfe, 0x20, //0x00002df8 cmpq         $32, %r14
	0x0f, 0x8c, 0x07, 0x09, 0x00, 0x00, //0x00002dfc jl           LBB0_671
	0x4d, 0x8d, 0x0c, 0x02, //0x00002e02 leaq         (%r10,%rax), %r9
	0x49, 0x29, 0xc0, //0x00002e06 subq         %rax, %r8
	0xbb, 0x1f, 0x00, 0x00, 0x00, //0x00002e09 movl         $31, %ebx
	0x45, 0x31, 0xf6, //0x00002e0e xorl         %r14d, %r14d
	0xc5, 0xfe, 0x6f, 0x05, 0x67, 0xd2, 0xff, 0xff, //0x00002e11 vmovdqu      $-11673(%rip), %ymm0  /* LCPI0_7+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x0d, 0x7f, 0xd2, 0xff, 0xff, //0x00002e19 vmovdqu      $-11649(%rip), %ymm1  /* LCPI0_8+0(%rip) */
	0x45, 0x31, 0xe4, //0x00002e21 xorl         %r12d, %r12d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002e24 .p2align 4, 0x90
	//0x00002e30 LBB0_572
	0xc4, 0x81, 0x7e, 0x6f, 0x54, 0x31, 0x01, //0x00002e30 vmovdqu      $1(%r9,%r14), %ymm2
	0xc5, 0xed, 0x74, 0xd8, //0x00002e37 vpcmpeqb     %ymm0, %ymm2, %ymm3
	0xc5, 0x7d, 0xd7, 0xdb, //0x00002e3b vpmovmskb    %ymm3, %r11d
	0xc5, 0xed, 0x74, 0xd1, //0x00002e3f vpcmpeqb     %ymm1, %ymm2, %ymm2
	0xc5, 0xfd, 0xd7, 0xca, //0x00002e43 vpmovmskb    %ymm2, %ecx
	0x85, 0xc9, //0x00002e47 testl        %ecx, %ecx
	0x0f, 0x85, 0x11, 0x00, 0x00, 0x00, //0x00002e49 jne          LBB0_575
	0x4d, 0x85, 0xe4, //0x00002e4f testq        %r12, %r12
	0x0f, 0x85, 0x08, 0x00, 0x00, 0x00, //0x00002e52 jne          LBB0_575
	0x45, 0x31, 0xe4, //0x00002e58 xorl         %r12d, %r12d
	0xe9, 0x31, 0x00, 0x00, 0x00, //0x00002e5b jmp          LBB0_576
	//0x00002e60 LBB0_575
	0x44, 0x89, 0xe7, //0x00002e60 movl         %r12d, %edi
	0xf7, 0xd7, //0x00002e63 notl         %edi
	0x21, 0xcf, //0x00002e65 andl         %ecx, %edi
	0x8d, 0x14, 0x3f, //0x00002e67 leal         (%rdi,%rdi), %edx
	0x44, 0x09, 0xe2, //0x00002e6a orl          %r12d, %edx
	0x89, 0xd6, //0x00002e6d movl         %edx, %esi
	0xf7, 0xd6, //0x00002e6f notl         %esi
	0x21, 0xce, //0x00002e71 andl         %ecx, %esi
	0x81, 0xe6, 0xaa, 0xaa, 0xaa, 0xaa, //0x00002e73 andl         $-1431655766, %esi
	0x45, 0x31, 0xe4, //0x00002e79 xorl         %r12d, %r12d
	0x01, 0xfe, //0x00002e7c addl         %edi, %esi
	0x41, 0x0f, 0x92, 0xc4, //0x00002e7e setb         %r12b
	0x01, 0xf6, //0x00002e82 addl         %esi, %esi
	0x81, 0xf6, 0x55, 0x55, 0x55, 0x55, //0x00002e84 xorl         $1431655765, %esi
	0x21, 0xd6, //0x00002e8a andl         %edx, %esi
	0xf7, 0xd6, //0x00002e8c notl         %esi
	0x41, 0x21, 0xf3, //0x00002e8e andl         %esi, %r11d
	//0x00002e91 LBB0_576
	0x4d, 0x85, 0xdb, //0x00002e91 testq        %r11, %r11
	0x0f, 0x85, 0xfc, 0x05, 0x00, 0x00, //0x00002e94 jne          LBB0_632
	0x49, 0x83, 0xc6, 0x20, //0x00002e9a addq         $32, %r14
	0x49, 0x8d, 0x0c, 0x18, //0x00002e9e leaq         (%r8,%rbx), %rcx
	0x48, 0x83, 0xc1, 0xe0, //0x00002ea2 addq         $-32, %rcx
	0x48, 0x83, 0xc3, 0xe0, //0x00002ea6 addq         $-32, %rbx
	0x48, 0x83, 0xf9, 0x3f, //0x00002eaa cmpq         $63, %rcx
	0x0f, 0x8f, 0x7c, 0xff, 0xff, 0xff, //0x00002eae jg           LBB0_572
	0x4d, 0x85, 0xe4, //0x00002eb4 testq        %r12, %r12
	0x0f, 0x85, 0xac, 0x08, 0x00, 0x00, //0x00002eb7 jne          LBB0_675
	0x4b, 0x8d, 0x14, 0x0e, //0x00002ebd leaq         (%r14,%r9), %rdx
	0x48, 0x83, 0xc2, 0x01, //0x00002ec1 addq         $1, %rdx
	0x49, 0xf7, 0xd6, //0x00002ec5 notq         %r14
	0x4d, 0x01, 0xc6, //0x00002ec8 addq         %r8, %r14
	//0x00002ecb LBB0_580
	0x4d, 0x85, 0xf6, //0x00002ecb testq        %r14, %r14
	0x0f, 0x8e, 0x04, 0x06, 0x00, 0x00, //0x00002ece jle          LBB0_638
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00002ed4 movq         $-1, %r15
	0xe9, 0x25, 0x06, 0x00, 0x00, //0x00002edb jmp          LBB0_640
	//0x00002ee0 LBB0_582
	0x48, 0x8d, 0x48, 0x05, //0x00002ee0 leaq         $5(%rax), %rcx
	0x49, 0x3b, 0x4d, 0x08, //0x00002ee4 cmpq         $8(%r13), %rcx
	0x0f, 0x86, 0x64, 0xd7, 0xff, 0xff, //0x00002ee8 jbe          LBB0_52
	0xe9, 0xe5, 0x05, 0x00, 0x00, //0x00002eee jmp          LBB0_638
	//0x00002ef3 LBB0_583
	0x4c, 0x89, 0x5c, 0x24, 0x08, //0x00002ef3 movq         %r11, $8(%rsp)
	0x4d, 0x8b, 0x75, 0x08, //0x00002ef8 movq         $8(%r13), %r14
	0x49, 0x29, 0xd6, //0x00002efc subq         %rdx, %r14
	0x49, 0x01, 0xd2, //0x00002eff addq         %rdx, %r10
	0x45, 0x31, 0xdb, //0x00002f02 xorl         %r11d, %r11d
	0xc5, 0xfe, 0x6f, 0x05, 0x93, 0xd1, 0xff, 0xff, //0x00002f05 vmovdqu      $-11885(%rip), %ymm0  /* LCPI0_8+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x0d, 0x6b, 0xd1, 0xff, 0xff, //0x00002f0d vmovdqu      $-11925(%rip), %ymm1  /* LCPI0_7+0(%rip) */
	0xc5, 0xe9, 0x76, 0xd2, //0x00002f15 vpcmpeqd     %xmm2, %xmm2, %xmm2
	0xc5, 0xfe, 0x6f, 0x1d, 0x9f, 0xd1, 0xff, 0xff, //0x00002f19 vmovdqu      $-11873(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0xb7, 0xd1, 0xff, 0xff, //0x00002f21 vmovdqu      $-11849(%rip), %ymm4  /* LCPI0_10+0(%rip) */
	0xc4, 0x41, 0x31, 0xef, 0xc9, //0x00002f29 vpxor        %xmm9, %xmm9, %xmm9
	0x45, 0x31, 0xe4, //0x00002f2e xorl         %r12d, %r12d
	0x45, 0x31, 0xc9, //0x00002f31 xorl         %r9d, %r9d
	0x31, 0xd2, //0x00002f34 xorl         %edx, %edx
	0x49, 0x83, 0xfe, 0x40, //0x00002f36 cmpq         $64, %r14
	0x0f, 0x8c, 0x4c, 0x01, 0x00, 0x00, //0x00002f3a jl           LBB0_592
	//0x00002f40 LBB0_586
	0xc4, 0xc1, 0x7e, 0x6f, 0x3a, //0x00002f40 vmovdqu      (%r10), %ymm7
	0xc4, 0xc1, 0x7e, 0x6f, 0x72, 0x20, //0x00002f45 vmovdqu      $32(%r10), %ymm6
	0xc5, 0x45, 0x74, 0xc0, //0x00002f4b vpcmpeqb     %ymm0, %ymm7, %ymm8
	0xc4, 0xc1, 0x7d, 0xd7, 0xf0, //0x00002f4f vpmovmskb    %ymm8, %esi
	0xc5, 0x4d, 0x74, 0xc0, //0x00002f54 vpcmpeqb     %ymm0, %ymm6, %ymm8
	0xc4, 0xc1, 0x7d, 0xd7, 0xc8, //0x00002f58 vpmovmskb    %ymm8, %ecx
	0x48, 0xc1, 0xe1, 0x20, //0x00002f5d shlq         $32, %rcx
	0x48, 0x09, 0xce, //0x00002f61 orq          %rcx, %rsi
	0x48, 0x89, 0xf1, //0x00002f64 movq         %rsi, %rcx
	0x4d, 0x89, 0xe8, //0x00002f67 movq         %r13, %r8
	0x4c, 0x09, 0xe1, //0x00002f6a orq          %r12, %rcx
	0x0f, 0x85, 0x0f, 0x00, 0x00, 0x00, //0x00002f6d jne          LBB0_588
	0x48, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00002f73 movq         $-1, %rsi
	0x45, 0x31, 0xe4, //0x00002f7a xorl         %r12d, %r12d
	0xe9, 0x46, 0x00, 0x00, 0x00, //0x00002f7d jmp          LBB0_589
	//0x00002f82 LBB0_588
	0x4c, 0x89, 0xe1, //0x00002f82 movq         %r12, %rcx
	0x48, 0xf7, 0xd1, //0x00002f85 notq         %rcx
	0x48, 0x21, 0xf1, //0x00002f88 andq         %rsi, %rcx
	0x4c, 0x8d, 0x2c, 0x09, //0x00002f8b leaq         (%rcx,%rcx), %r13
	0x4d, 0x09, 0xe5, //0x00002f8f orq          %r12, %r13
	0x4c, 0x89, 0xeb, //0x00002f92 movq         %r13, %rbx
	0x48, 0xf7, 0xd3, //0x00002f95 notq         %rbx
	0x48, 0xbf, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00002f98 movabsq      $-6148914691236517206, %rdi
	0x48, 0x21, 0xfe, //0x00002fa2 andq         %rdi, %rsi
	0x48, 0x21, 0xde, //0x00002fa5 andq         %rbx, %rsi
	0x45, 0x31, 0xe4, //0x00002fa8 xorl         %r12d, %r12d
	0x48, 0x01, 0xce, //0x00002fab addq         %rcx, %rsi
	0x41, 0x0f, 0x92, 0xc4, //0x00002fae setb         %r12b
	0x48, 0x01, 0xf6, //0x00002fb2 addq         %rsi, %rsi
	0x48, 0xb9, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00002fb5 movabsq      $6148914691236517205, %rcx
	0x48, 0x31, 0xce, //0x00002fbf xorq         %rcx, %rsi
	0x4c, 0x21, 0xee, //0x00002fc2 andq         %r13, %rsi
	0x48, 0xf7, 0xd6, //0x00002fc5 notq         %rsi
	//0x00002fc8 LBB0_589
	0xc5, 0x4d, 0x74, 0xc1, //0x00002fc8 vpcmpeqb     %ymm1, %ymm6, %ymm8
	0xc4, 0xc1, 0x7d, 0xd7, 0xc8, //0x00002fcc vpmovmskb    %ymm8, %ecx
	0x48, 0xc1, 0xe1, 0x20, //0x00002fd1 shlq         $32, %rcx
	0xc5, 0x45, 0x74, 0xc1, //0x00002fd5 vpcmpeqb     %ymm1, %ymm7, %ymm8
	0xc4, 0xc1, 0x7d, 0xd7, 0xf8, //0x00002fd9 vpmovmskb    %ymm8, %edi
	0x48, 0x09, 0xcf, //0x00002fde orq          %rcx, %rdi
	0x48, 0x21, 0xf7, //0x00002fe1 andq         %rsi, %rdi
	0xc4, 0xe1, 0xf9, 0x6e, 0xef, //0x00002fe4 vmovq        %rdi, %xmm5
	0xc4, 0xe3, 0x51, 0x44, 0xea, 0x00, //0x00002fe9 vpclmulqdq   $0, %xmm2, %xmm5, %xmm5
	0xc4, 0xc1, 0xf9, 0x7e, 0xed, //0x00002fef vmovq        %xmm5, %r13
	0x4d, 0x31, 0xdd, //0x00002ff4 xorq         %r11, %r13
	0xc5, 0xc5, 0x74, 0xeb, //0x00002ff7 vpcmpeqb     %ymm3, %ymm7, %ymm5
	0xc5, 0xfd, 0xd7, 0xfd, //0x00002ffb vpmovmskb    %ymm5, %edi
	0xc5, 0xcd, 0x74, 0xeb, //0x00002fff vpcmpeqb     %ymm3, %ymm6, %ymm5
	0xc5, 0xfd, 0xd7, 0xcd, //0x00003003 vpmovmskb    %ymm5, %ecx
	0x48, 0xc1, 0xe1, 0x20, //0x00003007 shlq         $32, %rcx
	0x48, 0x09, 0xcf, //0x0000300b orq          %rcx, %rdi
	0x4c, 0x89, 0xe9, //0x0000300e movq         %r13, %rcx
	0x48, 0xf7, 0xd1, //0x00003011 notq         %rcx
	0x48, 0x21, 0xcf, //0x00003014 andq         %rcx, %rdi
	0xc5, 0xc5, 0x74, 0xec, //0x00003017 vpcmpeqb     %ymm4, %ymm7, %ymm5
	0xc5, 0xfd, 0xd7, 0xdd, //0x0000301b vpmovmskb    %ymm5, %ebx
	0xc5, 0xcd, 0x74, 0xec, //0x0000301f vpcmpeqb     %ymm4, %ymm6, %ymm5
	0xc5, 0xfd, 0xd7, 0xf5, //0x00003023 vpmovmskb    %ymm5, %esi
	0x48, 0xc1, 0xe6, 0x20, //0x00003027 shlq         $32, %rsi
	0x48, 0x09, 0xf3, //0x0000302b orq          %rsi, %rbx
	0x48, 0x21, 0xcb, //0x0000302e andq         %rcx, %rbx
	0x0f, 0x84, 0x31, 0x00, 0x00, 0x00, //0x00003031 je           LBB0_584
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00003037 .p2align 4, 0x90
	//0x00003040 LBB0_590
	0x48, 0x8d, 0x4b, 0xff, //0x00003040 leaq         $-1(%rbx), %rcx
	0x48, 0x89, 0xce, //0x00003044 movq         %rcx, %rsi
	0x48, 0x21, 0xfe, //0x00003047 andq         %rdi, %rsi
	0xf3, 0x48, 0x0f, 0xb8, 0xf6, //0x0000304a popcntq      %rsi, %rsi
	0x4c, 0x01, 0xce, //0x0000304f addq         %r9, %rsi
	0x48, 0x39, 0xd6, //0x00003052 cmpq         %rdx, %rsi
	0x0f, 0x86, 0x00, 0x04, 0x00, 0x00, //0x00003055 jbe          LBB0_631
	0x48, 0x83, 0xc2, 0x01, //0x0000305b addq         $1, %rdx
	0x48, 0x21, 0xcb, //0x0000305f andq         %rcx, %rbx
	0x0f, 0x85, 0xd8, 0xff, 0xff, 0xff, //0x00003062 jne          LBB0_590
	//0x00003068 LBB0_584
	0x49, 0xc1, 0xfd, 0x3f, //0x00003068 sarq         $63, %r13
	0xf3, 0x48, 0x0f, 0xb8, 0xcf, //0x0000306c popcntq      %rdi, %rcx
	0x49, 0x01, 0xc9, //0x00003071 addq         %rcx, %r9
	0x49, 0x83, 0xc2, 0x40, //0x00003074 addq         $64, %r10
	0x49, 0x83, 0xc6, 0xc0, //0x00003078 addq         $-64, %r14
	0x4d, 0x89, 0xeb, //0x0000307c movq         %r13, %r11
	0x4d, 0x89, 0xc5, //0x0000307f movq         %r8, %r13
	0x49, 0x83, 0xfe, 0x40, //0x00003082 cmpq         $64, %r14
	0x0f, 0x8d, 0xb4, 0xfe, 0xff, 0xff, //0x00003086 jge          LBB0_586
	//0x0000308c LBB0_592
	0x4d, 0x85, 0xf6, //0x0000308c testq        %r14, %r14
	0x0f, 0x8e, 0x7c, 0x06, 0x00, 0x00, //0x0000308f jle          LBB0_672
	0xc5, 0x7e, 0x7f, 0x4c, 0x24, 0x60, //0x00003095 vmovdqu      %ymm9, $96(%rsp)
	0xc5, 0x7e, 0x7f, 0x4c, 0x24, 0x40, //0x0000309b vmovdqu      %ymm9, $64(%rsp)
	0x44, 0x89, 0xd1, //0x000030a1 movl         %r10d, %ecx
	0x81, 0xe1, 0xff, 0x0f, 0x00, 0x00, //0x000030a4 andl         $4095, %ecx
	0x81, 0xf9, 0xc1, 0x0f, 0x00, 0x00, //0x000030aa cmpl         $4033, %ecx
	0x0f, 0x82, 0x8a, 0xfe, 0xff, 0xff, //0x000030b0 jb           LBB0_586
	0x49, 0x83, 0xfe, 0x20, //0x000030b6 cmpq         $32, %r14
	0x0f, 0x82, 0x1d, 0x00, 0x00, 0x00, //0x000030ba jb           LBB0_596
	0xc4, 0xc1, 0x7e, 0x6f, 0x2a, //0x000030c0 vmovdqu      (%r10), %ymm5
	0xc5, 0xfe, 0x7f, 0x6c, 0x24, 0x40, //0x000030c5 vmovdqu      %ymm5, $64(%rsp)
	0x49, 0x83, 0xc2, 0x20, //0x000030cb addq         $32, %r10
	0x49, 0x8d, 0x7e, 0xe0, //0x000030cf leaq         $-32(%r14), %rdi
	0x48, 0x8d, 0x74, 0x24, 0x60, //0x000030d3 leaq         $96(%rsp), %rsi
	0xe9, 0x08, 0x00, 0x00, 0x00, //0x000030d8 jmp          LBB0_597
	//0x000030dd LBB0_596
	0x48, 0x8d, 0x74, 0x24, 0x40, //0x000030dd leaq         $64(%rsp), %rsi
	0x4c, 0x89, 0xf7, //0x000030e2 movq         %r14, %rdi
	//0x000030e5 LBB0_597
	0x48, 0x83, 0xff, 0x10, //0x000030e5 cmpq         $16, %rdi
	0x0f, 0x82, 0x5a, 0x00, 0x00, 0x00, //0x000030e9 jb           LBB0_598
	0xc4, 0xc1, 0x7a, 0x6f, 0x2a, //0x000030ef vmovdqu      (%r10), %xmm5
	0xc5, 0xfa, 0x7f, 0x2e, //0x000030f4 vmovdqu      %xmm5, (%rsi)
	0x49, 0x83, 0xc2, 0x10, //0x000030f8 addq         $16, %r10
	0x48, 0x83, 0xc6, 0x10, //0x000030fc addq         $16, %rsi
	0x48, 0x83, 0xc7, 0xf0, //0x00003100 addq         $-16, %rdi
	0x48, 0x83, 0xff, 0x08, //0x00003104 cmpq         $8, %rdi
	0x0f, 0x83, 0x45, 0x00, 0x00, 0x00, //0x00003108 jae          LBB0_603
	//0x0000310e LBB0_599
	0x48, 0x83, 0xff, 0x04, //0x0000310e cmpq         $4, %rdi
	0x0f, 0x8c, 0x57, 0x00, 0x00, 0x00, //0x00003112 jl           LBB0_600
	//0x00003118 LBB0_604
	0x41, 0x8b, 0x0a, //0x00003118 movl         (%r10), %ecx
	0x89, 0x0e, //0x0000311b movl         %ecx, (%rsi)
	0x49, 0x83, 0xc2, 0x04, //0x0000311d addq         $4, %r10
	0x48, 0x83, 0xc6, 0x04, //0x00003121 addq         $4, %rsi
	0x48, 0x83, 0xc7, 0xfc, //0x00003125 addq         $-4, %rdi
	0x48, 0x83, 0xff, 0x02, //0x00003129 cmpq         $2, %rdi
	0x0f, 0x83, 0x46, 0x00, 0x00, 0x00, //0x0000312d jae          LBB0_605
	//0x00003133 LBB0_601
	0x4c, 0x89, 0xd3, //0x00003133 movq         %r10, %rbx
	0x4c, 0x8d, 0x54, 0x24, 0x40, //0x00003136 leaq         $64(%rsp), %r10
	0x48, 0x85, 0xff, //0x0000313b testq        %rdi, %rdi
	0x0f, 0x85, 0x59, 0x00, 0x00, 0x00, //0x0000313e jne          LBB0_606
	0xe9, 0xf7, 0xfd, 0xff, 0xff, //0x00003144 jmp          LBB0_586
	//0x00003149 LBB0_598
	0x48, 0x83, 0xff, 0x08, //0x00003149 cmpq         $8, %rdi
	0x0f, 0x82, 0xbb, 0xff, 0xff, 0xff, //0x0000314d jb           LBB0_599
	//0x00003153 LBB0_603
	0x49, 0x8b, 0x0a, //0x00003153 movq         (%r10), %rcx
	0x48, 0x89, 0x0e, //0x00003156 movq         %rcx, (%rsi)
	0x49, 0x83, 0xc2, 0x08, //0x00003159 addq         $8, %r10
	0x48, 0x83, 0xc6, 0x08, //0x0000315d addq         $8, %rsi
	0x48, 0x83, 0xc7, 0xf8, //0x00003161 addq         $-8, %rdi
	0x48, 0x83, 0xff, 0x04, //0x00003165 cmpq         $4, %rdi
	0x0f, 0x8d, 0xa9, 0xff, 0xff, 0xff, //0x00003169 jge          LBB0_604
	//0x0000316f LBB0_600
	0x48, 0x83, 0xff, 0x02, //0x0000316f cmpq         $2, %rdi
	0x0f, 0x82, 0xba, 0xff, 0xff, 0xff, //0x00003173 jb           LBB0_601
	//0x00003179 LBB0_605
	0x41, 0x0f, 0xb7, 0x0a, //0x00003179 movzwl       (%r10), %ecx
	0x66, 0x89, 0x0e, //0x0000317d movw         %cx, (%rsi)
	0x49, 0x83, 0xc2, 0x02, //0x00003180 addq         $2, %r10
	0x48, 0x83, 0xc6, 0x02, //0x00003184 addq         $2, %rsi
	0x48, 0x83, 0xc7, 0xfe, //0x00003188 addq         $-2, %rdi
	0x4c, 0x89, 0xd3, //0x0000318c movq         %r10, %rbx
	0x4c, 0x8d, 0x54, 0x24, 0x40, //0x0000318f leaq         $64(%rsp), %r10
	0x48, 0x85, 0xff, //0x00003194 testq        %rdi, %rdi
	0x0f, 0x84, 0xa3, 0xfd, 0xff, 0xff, //0x00003197 je           LBB0_586
	//0x0000319d LBB0_606
	0x8a, 0x0b, //0x0000319d movb         (%rbx), %cl
	0x88, 0x0e, //0x0000319f movb         %cl, (%rsi)
	0x4c, 0x8d, 0x54, 0x24, 0x40, //0x000031a1 leaq         $64(%rsp), %r10
	0xe9, 0x95, 0xfd, 0xff, 0xff, //0x000031a6 jmp          LBB0_586
	//0x000031ab LBB0_607
	0x4c, 0x89, 0x5c, 0x24, 0x08, //0x000031ab movq         %r11, $8(%rsp)
	0x4d, 0x8b, 0x75, 0x08, //0x000031b0 movq         $8(%r13), %r14
	0x49, 0x29, 0xd6, //0x000031b4 subq         %rdx, %r14
	0x49, 0x01, 0xd2, //0x000031b7 addq         %rdx, %r10
	0x45, 0x31, 0xdb, //0x000031ba xorl         %r11d, %r11d
	0xc5, 0xfe, 0x6f, 0x05, 0xdb, 0xce, 0xff, 0xff, //0x000031bd vmovdqu      $-12581(%rip), %ymm0  /* LCPI0_8+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x0d, 0xb3, 0xce, 0xff, 0xff, //0x000031c5 vmovdqu      $-12621(%rip), %ymm1  /* LCPI0_7+0(%rip) */
	0xc5, 0xe9, 0x76, 0xd2, //0x000031cd vpcmpeqd     %xmm2, %xmm2, %xmm2
	0xc5, 0xfe, 0x6f, 0x1d, 0x27, 0xcf, 0xff, 0xff, //0x000031d1 vmovdqu      $-12505(%rip), %ymm3  /* LCPI0_11+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0x7f, 0xce, 0xff, 0xff, //0x000031d9 vmovdqu      $-12673(%rip), %ymm4  /* LCPI0_3+0(%rip) */
	0xc4, 0x41, 0x31, 0xef, 0xc9, //0x000031e1 vpxor        %xmm9, %xmm9, %xmm9
	0x45, 0x31, 0xe4, //0x000031e6 xorl         %r12d, %r12d
	0x45, 0x31, 0xc9, //0x000031e9 xorl         %r9d, %r9d
	0x31, 0xd2, //0x000031ec xorl         %edx, %edx
	0x49, 0x83, 0xfe, 0x40, //0x000031ee cmpq         $64, %r14
	0x0f, 0x8c, 0x44, 0x01, 0x00, 0x00, //0x000031f2 jl           LBB0_616
	//0x000031f8 LBB0_610
	0xc4, 0xc1, 0x7e, 0x6f, 0x3a, //0x000031f8 vmovdqu      (%r10), %ymm7
	0xc4, 0xc1, 0x7e, 0x6f, 0x72, 0x20, //0x000031fd vmovdqu      $32(%r10), %ymm6
	0xc5, 0x45, 0x74, 0xc0, //0x00003203 vpcmpeqb     %ymm0, %ymm7, %ymm8
	0xc4, 0xc1, 0x7d, 0xd7, 0xf0, //0x00003207 vpmovmskb    %ymm8, %esi
	0xc5, 0x4d, 0x74, 0xc0, //0x0000320c vpcmpeqb     %ymm0, %ymm6, %ymm8
	0xc4, 0xc1, 0x7d, 0xd7, 0xc8, //0x00003210 vpmovmskb    %ymm8, %ecx
	0x48, 0xc1, 0xe1, 0x20, //0x00003215 shlq         $32, %rcx
	0x48, 0x09, 0xce, //0x00003219 orq          %rcx, %rsi
	0x48, 0x89, 0xf1, //0x0000321c movq         %rsi, %rcx
	0x4d, 0x89, 0xe8, //0x0000321f movq         %r13, %r8
	0x4c, 0x09, 0xe1, //0x00003222 orq          %r12, %rcx
	0x0f, 0x85, 0x0f, 0x00, 0x00, 0x00, //0x00003225 jne          LBB0_612
	0x48, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x0000322b movq         $-1, %rsi
	0x45, 0x31, 0xe4, //0x00003232 xorl         %r12d, %r12d
	0xe9, 0x46, 0x00, 0x00, 0x00, //0x00003235 jmp          LBB0_613
	//0x0000323a LBB0_612
	0x4c, 0x89, 0xe1, //0x0000323a movq         %r12, %rcx
	0x48, 0xf7, 0xd1, //0x0000323d notq         %rcx
	0x48, 0x21, 0xf1, //0x00003240 andq         %rsi, %rcx
	0x4c, 0x8d, 0x2c, 0x09, //0x00003243 leaq         (%rcx,%rcx), %r13
	0x4d, 0x09, 0xe5, //0x00003247 orq          %r12, %r13
	0x4c, 0x89, 0xeb, //0x0000324a movq         %r13, %rbx
	0x48, 0xf7, 0xd3, //0x0000324d notq         %rbx
	0x48, 0xbf, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00003250 movabsq      $-6148914691236517206, %rdi
	0x48, 0x21, 0xfe, //0x0000325a andq         %rdi, %rsi
	0x48, 0x21, 0xde, //0x0000325d andq         %rbx, %rsi
	0x45, 0x31, 0xe4, //0x00003260 xorl         %r12d, %r12d
	0x48, 0x01, 0xce, //0x00003263 addq         %rcx, %rsi
	0x41, 0x0f, 0x92, 0xc4, //0x00003266 setb         %r12b
	0x48, 0x01, 0xf6, //0x0000326a addq         %rsi, %rsi
	0x48, 0xb9, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x0000326d movabsq      $6148914691236517205, %rcx
	0x48, 0x31, 0xce, //0x00003277 xorq         %rcx, %rsi
	0x4c, 0x21, 0xee, //0x0000327a andq         %r13, %rsi
	0x48, 0xf7, 0xd6, //0x0000327d notq         %rsi
	//0x00003280 LBB0_613
	0xc5, 0x4d, 0x74, 0xc1, //0x00003280 vpcmpeqb     %ymm1, %ymm6, %ymm8
	0xc4, 0xc1, 0x7d, 0xd7, 0xc8, //0x00003284 vpmovmskb    %ymm8, %ecx
	0x48, 0xc1, 0xe1, 0x20, //0x00003289 shlq         $32, %rcx
	0xc5, 0x45, 0x74, 0xc1, //0x0000328d vpcmpeqb     %ymm1, %ymm7, %ymm8
	0xc4, 0xc1, 0x7d, 0xd7, 0xf8, //0x00003291 vpmovmskb    %ymm8, %edi
	0x48, 0x09, 0xcf, //0x00003296 orq          %rcx, %rdi
	0x48, 0x21, 0xf7, //0x00003299 andq         %rsi, %rdi
	0xc4, 0xe1, 0xf9, 0x6e, 0xef, //0x0000329c vmovq        %rdi, %xmm5
	0xc4, 0xe3, 0x51, 0x44, 0xea, 0x00, //0x000032a1 vpclmulqdq   $0, %xmm2, %xmm5, %xmm5
	0xc4, 0xc1, 0xf9, 0x7e, 0xed, //0x000032a7 vmovq        %xmm5, %r13
	0x4d, 0x31, 0xdd, //0x000032ac xorq         %r11, %r13
	0xc5, 0xc5, 0x74, 0xeb, //0x000032af vpcmpeqb     %ymm3, %ymm7, %ymm5
	0xc5, 0xfd, 0xd7, 0xfd, //0x000032b3 vpmovmskb    %ymm5, %edi
	0xc5, 0xcd, 0x74, 0xeb, //0x000032b7 vpcmpeqb     %ymm3, %ymm6, %ymm5
	0xc5, 0xfd, 0xd7, 0xcd, //0x000032bb vpmovmskb    %ymm5, %ecx
	0x48, 0xc1, 0xe1, 0x20, //0x000032bf shlq         $32, %rcx
	0x48, 0x09, 0xcf, //0x000032c3 orq          %rcx, %rdi
	0x4c, 0x89, 0xe9, //0x000032c6 movq         %r13, %rcx
	0x48, 0xf7, 0xd1, //0x000032c9 notq         %rcx
	0x48, 0x21, 0xcf, //0x000032cc andq         %rcx, %rdi
	0xc5, 0xc5, 0x74, 0xec, //0x000032cf vpcmpeqb     %ymm4, %ymm7, %ymm5
	0xc5, 0xfd, 0xd7, 0xdd, //0x000032d3 vpmovmskb    %ymm5, %ebx
	0xc5, 0xcd, 0x74, 0xec, //0x000032d7 vpcmpeqb     %ymm4, %ymm6, %ymm5
	0xc5, 0xfd, 0xd7, 0xf5, //0x000032db vpmovmskb    %ymm5, %esi
	0x48, 0xc1, 0xe6, 0x20, //0x000032df shlq         $32, %rsi
	0x48, 0x09, 0xf3, //0x000032e3 orq          %rsi, %rbx
	0x48, 0x21, 0xcb, //0x000032e6 andq         %rcx, %rbx
	0x0f, 0x84, 0x29, 0x00, 0x00, 0x00, //0x000032e9 je           LBB0_608
	0x90, //0x000032ef .p2align 4, 0x90
	//0x000032f0 LBB0_614
	0x48, 0x8d, 0x4b, 0xff, //0x000032f0 leaq         $-1(%rbx), %rcx
	0x48, 0x89, 0xce, //0x000032f4 movq         %rcx, %rsi
	0x48, 0x21, 0xfe, //0x000032f7 andq         %rdi, %rsi
	0xf3, 0x48, 0x0f, 0xb8, 0xf6, //0x000032fa popcntq      %rsi, %rsi
	0x4c, 0x01, 0xce, //0x000032ff addq         %r9, %rsi
	0x48, 0x39, 0xd6, //0x00003302 cmpq         %rdx, %rsi
	0x0f, 0x86, 0x50, 0x01, 0x00, 0x00, //0x00003305 jbe          LBB0_631
	0x48, 0x83, 0xc2, 0x01, //0x0000330b addq         $1, %rdx
	0x48, 0x21, 0xcb, //0x0000330f andq         %rcx, %rbx
	0x0f, 0x85, 0xd8, 0xff, 0xff, 0xff, //0x00003312 jne          LBB0_614
	//0x00003318 LBB0_608
	0x49, 0xc1, 0xfd, 0x3f, //0x00003318 sarq         $63, %r13
	0xf3, 0x48, 0x0f, 0xb8, 0xcf, //0x0000331c popcntq      %rdi, %rcx
	0x49, 0x01, 0xc9, //0x00003321 addq         %rcx, %r9
	0x49, 0x83, 0xc2, 0x40, //0x00003324 addq         $64, %r10
	0x49, 0x83, 0xc6, 0xc0, //0x00003328 addq         $-64, %r14
	0x4d, 0x89, 0xeb, //0x0000332c movq         %r13, %r11
	0x4d, 0x89, 0xc5, //0x0000332f movq         %r8, %r13
	0x49, 0x83, 0xfe, 0x40, //0x00003332 cmpq         $64, %r14
	0x0f, 0x8d, 0xbc, 0xfe, 0xff, 0xff, //0x00003336 jge          LBB0_610
	//0x0000333c LBB0_616
	0x4d, 0x85, 0xf6, //0x0000333c testq        %r14, %r14
	0x0f, 0x8e, 0xcc, 0x03, 0x00, 0x00, //0x0000333f jle          LBB0_672
	0xc5, 0x7e, 0x7f, 0x4c, 0x24, 0x60, //0x00003345 vmovdqu      %ymm9, $96(%rsp)
	0xc5, 0x7e, 0x7f, 0x4c, 0x24, 0x40, //0x0000334b vmovdqu      %ymm9, $64(%rsp)
	0x44, 0x89, 0xd1, //0x00003351 movl         %r10d, %ecx
	0x81, 0xe1, 0xff, 0x0f, 0x00, 0x00, //0x00003354 andl         $4095, %ecx
	0x81, 0xf9, 0xc1, 0x0f, 0x00, 0x00, //0x0000335a cmpl         $4033, %ecx
	0x0f, 0x82, 0x92, 0xfe, 0xff, 0xff, //0x00003360 jb           LBB0_610
	0x49, 0x83, 0xfe, 0x20, //0x00003366 cmpq         $32, %r14
	0x0f, 0x82, 0x1d, 0x00, 0x00, 0x00, //0x0000336a jb           LBB0_620
	0xc4, 0xc1, 0x7e, 0x6f, 0x2a, //0x00003370 vmovdqu      (%r10), %ymm5
	0xc5, 0xfe, 0x7f, 0x6c, 0x24, 0x40, //0x00003375 vmovdqu      %ymm5, $64(%rsp)
	0x49, 0x83, 0xc2, 0x20, //0x0000337b addq         $32, %r10
	0x49, 0x8d, 0x7e, 0xe0, //0x0000337f leaq         $-32(%r14), %rdi
	0x48, 0x8d, 0x74, 0x24, 0x60, //0x00003383 leaq         $96(%rsp), %rsi
	0xe9, 0x08, 0x00, 0x00, 0x00, //0x00003388 jmp          LBB0_621
	//0x0000338d LBB0_620
	0x48, 0x8d, 0x74, 0x24, 0x40, //0x0000338d leaq         $64(%rsp), %rsi
	0x4c, 0x89, 0xf7, //0x00003392 movq         %r14, %rdi
	//0x00003395 LBB0_621
	0x48, 0x83, 0xff, 0x10, //0x00003395 cmpq         $16, %rdi
	0x0f, 0x82, 0x5a, 0x00, 0x00, 0x00, //0x00003399 jb           LBB0_622
	0xc4, 0xc1, 0x7a, 0x6f, 0x2a, //0x0000339f vmovdqu      (%r10), %xmm5
	0xc5, 0xfa, 0x7f, 0x2e, //0x000033a4 vmovdqu      %xmm5, (%rsi)
	0x49, 0x83, 0xc2, 0x10, //0x000033a8 addq         $16, %r10
	0x48, 0x83, 0xc6, 0x10, //0x000033ac addq         $16, %rsi
	0x48, 0x83, 0xc7, 0xf0, //0x000033b0 addq         $-16, %rdi
	0x48, 0x83, 0xff, 0x08, //0x000033b4 cmpq         $8, %rdi
	0x0f, 0x83, 0x45, 0x00, 0x00, 0x00, //0x000033b8 jae          LBB0_627
	//0x000033be LBB0_623
	0x48, 0x83, 0xff, 0x04, //0x000033be cmpq         $4, %rdi
	0x0f, 0x8c, 0x57, 0x00, 0x00, 0x00, //0x000033c2 jl           LBB0_624
	//0x000033c8 LBB0_628
	0x41, 0x8b, 0x0a, //0x000033c8 movl         (%r10), %ecx
	0x89, 0x0e, //0x000033cb movl         %ecx, (%rsi)
	0x49, 0x83, 0xc2, 0x04, //0x000033cd addq         $4, %r10
	0x48, 0x83, 0xc6, 0x04, //0x000033d1 addq         $4, %rsi
	0x48, 0x83, 0xc7, 0xfc, //0x000033d5 addq         $-4, %rdi
	0x48, 0x83, 0xff, 0x02, //0x000033d9 cmpq         $2, %rdi
	0x0f, 0x83, 0x46, 0x00, 0x00, 0x00, //0x000033dd jae          LBB0_629
	//0x000033e3 LBB0_625
	0x4c, 0x89, 0xd3, //0x000033e3 movq         %r10, %rbx
	0x4c, 0x8d, 0x54, 0x24, 0x40, //0x000033e6 leaq         $64(%rsp), %r10
	0x48, 0x85, 0xff, //0x000033eb testq        %rdi, %rdi
	0x0f, 0x85, 0x59, 0x00, 0x00, 0x00, //0x000033ee jne          LBB0_630
	0xe9, 0xff, 0xfd, 0xff, 0xff, //0x000033f4 jmp          LBB0_610
	//0x000033f9 LBB0_622
	0x48, 0x83, 0xff, 0x08, //0x000033f9 cmpq         $8, %rdi
	0x0f, 0x82, 0xbb, 0xff, 0xff, 0xff, //0x000033fd jb           LBB0_623
	//0x00003403 LBB0_627
	0x49, 0x8b, 0x0a, //0x00003403 movq         (%r10), %rcx
	0x48, 0x89, 0x0e, //0x00003406 movq         %rcx, (%rsi)
	0x49, 0x83, 0xc2, 0x08, //0x00003409 addq         $8, %r10
	0x48, 0x83, 0xc6, 0x08, //0x0000340d addq         $8, %rsi
	0x48, 0x83, 0xc7, 0xf8, //0x00003411 addq         $-8, %rdi
	0x48, 0x83, 0xff, 0x04, //0x00003415 cmpq         $4, %rdi
	0x0f, 0x8d, 0xa9, 0xff, 0xff, 0xff, //0x00003419 jge          LBB0_628
	//0x0000341f LBB0_624
	0x48, 0x83, 0xff, 0x02, //0x0000341f cmpq         $2, %rdi
	0x0f, 0x82, 0xba, 0xff, 0xff, 0xff, //0x00003423 jb           LBB0_625
	//0x00003429 LBB0_629
	0x41, 0x0f, 0xb7, 0x0a, //0x00003429 movzwl       (%r10), %ecx
	0x66, 0x89, 0x0e, //0x0000342d movw         %cx, (%rsi)
	0x49, 0x83, 0xc2, 0x02, //0x00003430 addq         $2, %r10
	0x48, 0x83, 0xc6, 0x02, //0x00003434 addq         $2, %rsi
	0x48, 0x83, 0xc7, 0xfe, //0x00003438 addq         $-2, %rdi
	0x4c, 0x89, 0xd3, //0x0000343c movq         %r10, %rbx
	0x4c, 0x8d, 0x54, 0x24, 0x40, //0x0000343f leaq         $64(%rsp), %r10
	0x48, 0x85, 0xff, //0x00003444 testq        %rdi, %rdi
	0x0f, 0x84, 0xab, 0xfd, 0xff, 0xff, //0x00003447 je           LBB0_610
	//0x0000344d LBB0_630
	0x8a, 0x0b, //0x0000344d movb         (%rbx), %cl
	0x88, 0x0e, //0x0000344f movb         %cl, (%rsi)
	0x4c, 0x8d, 0x54, 0x24, 0x40, //0x00003451 leaq         $64(%rsp), %r10
	0xe9, 0x9d, 0xfd, 0xff, 0xff, //0x00003456 jmp          LBB0_610
	//0x0000345b LBB0_631
	0x49, 0x8b, 0x48, 0x08, //0x0000345b movq         $8(%r8), %rcx
	0x48, 0x0f, 0xbc, 0xd3, //0x0000345f bsfq         %rbx, %rdx
	0x4c, 0x29, 0xf2, //0x00003463 subq         %r14, %rdx
	0x48, 0x01, 0xd1, //0x00003466 addq         %rdx, %rcx
	0x48, 0x83, 0xc1, 0x01, //0x00003469 addq         $1, %rcx
	0x4c, 0x8b, 0x5c, 0x24, 0x08, //0x0000346d movq         $8(%rsp), %r11
	0x49, 0x89, 0x0b, //0x00003472 movq         %rcx, (%r11)
	0x49, 0x8b, 0x50, 0x08, //0x00003475 movq         $8(%r8), %rdx
	0x48, 0x39, 0xd1, //0x00003479 cmpq         %rdx, %rcx
	0x48, 0x0f, 0x47, 0xca, //0x0000347c cmovaq       %rdx, %rcx
	0x49, 0x89, 0x0b, //0x00003480 movq         %rcx, (%r11)
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00003483 movq         $-1, %rcx
	0x48, 0x0f, 0x47, 0xc1, //0x0000348a cmovaq       %rcx, %rax
	0x49, 0x89, 0xc7, //0x0000348e movq         %rax, %r15
	0xe9, 0x42, 0x00, 0x00, 0x00, //0x00003491 jmp          LBB0_638
	//0x00003496 LBB0_632
	0x41, 0x0f, 0xbc, 0xcb, //0x00003496 bsfl         %r11d, %ecx
	0x48, 0x01, 0xc1, //0x0000349a addq         %rax, %rcx
	0x4c, 0x01, 0xf1, //0x0000349d addq         %r14, %rcx
	0x48, 0x83, 0xc1, 0x02, //0x000034a0 addq         $2, %rcx
	0x49, 0x89, 0x4d, 0x00, //0x000034a4 movq         %rcx, (%r13)
	0x49, 0x89, 0xc7, //0x000034a8 movq         %rax, %r15
	0xe9, 0x28, 0x00, 0x00, 0x00, //0x000034ab jmp          LBB0_638
	//0x000034b0 LBB0_633
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x000034b0 movq         $-1, %rcx
	0xe9, 0x03, 0x00, 0x00, 0x00, //0x000034b7 jmp          LBB0_636
	//0x000034bc LBB0_635
	0x4c, 0x89, 0xc1, //0x000034bc movq         %r8, %rcx
	//0x000034bf LBB0_636
	0x48, 0x8b, 0x54, 0x24, 0x08, //0x000034bf movq         $8(%rsp), %rdx
	0x48, 0x8b, 0x02, //0x000034c4 movq         (%rdx), %rax
	0x48, 0x29, 0xc8, //0x000034c7 subq         %rcx, %rax
	0x48, 0x83, 0xc0, 0xfe, //0x000034ca addq         $-2, %rax
	0x48, 0x89, 0x02, //0x000034ce movq         %rax, (%rdx)
	//0x000034d1 LBB0_637
	0x49, 0xc7, 0xc7, 0xfe, 0xff, 0xff, 0xff, //0x000034d1 movq         $-2, %r15
	//0x000034d8 LBB0_638
	0x4c, 0x89, 0xf8, //0x000034d8 movq         %r15, %rax
	0x48, 0x8d, 0x65, 0xd8, //0x000034db leaq         $-40(%rbp), %rsp
	0x5b, //0x000034df popq         %rbx
	0x41, 0x5c, //0x000034e0 popq         %r12
	0x41, 0x5d, //0x000034e2 popq         %r13
	0x41, 0x5e, //0x000034e4 popq         %r14
	0x41, 0x5f, //0x000034e6 popq         %r15
	0x5d, //0x000034e8 popq         %rbp
	0xc5, 0xf8, 0x77, //0x000034e9 vzeroupper   
	0xc3, //0x000034ec retq         
	//0x000034ed LBB0_639
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x000034ed movq         $-2, %rcx
	0xbf, 0x02, 0x00, 0x00, 0x00, //0x000034f4 movl         $2, %edi
	0x48, 0x01, 0xfa, //0x000034f9 addq         %rdi, %rdx
	0x49, 0x01, 0xce, //0x000034fc addq         %rcx, %r14
	0x0f, 0x8e, 0xd3, 0xff, 0xff, 0xff, //0x000034ff jle          LBB0_638
	//0x00003505 LBB0_640
	0x0f, 0xb6, 0x0a, //0x00003505 movzbl       (%rdx), %ecx
	0x80, 0xf9, 0x5c, //0x00003508 cmpb         $92, %cl
	0x0f, 0x84, 0xdc, 0xff, 0xff, 0xff, //0x0000350b je           LBB0_639
	0x80, 0xf9, 0x22, //0x00003511 cmpb         $34, %cl
	0x0f, 0x84, 0x96, 0x01, 0x00, 0x00, //0x00003514 je           LBB0_665
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x0000351a movq         $-1, %rcx
	0xbf, 0x01, 0x00, 0x00, 0x00, //0x00003521 movl         $1, %edi
	0x48, 0x01, 0xfa, //0x00003526 addq         %rdi, %rdx
	0x49, 0x01, 0xce, //0x00003529 addq         %rcx, %r14
	0x0f, 0x8f, 0xd3, 0xff, 0xff, 0xff, //0x0000352c jg           LBB0_640
	0xe9, 0xa1, 0xff, 0xff, 0xff, //0x00003532 jmp          LBB0_638
	//0x00003537 LBB0_643
	0x48, 0x8b, 0x14, 0x24, //0x00003537 movq         (%rsp), %rdx
	0x48, 0x83, 0xfa, 0xff, //0x0000353b cmpq         $-1, %rdx
	0x0f, 0x85, 0x07, 0x00, 0x00, 0x00, //0x0000353f jne          LBB0_646
	0x48, 0x0f, 0xbc, 0xd6, //0x00003545 bsfq         %rsi, %rdx
	//0x00003549 LBB0_645
	0x4c, 0x01, 0xfa, //0x00003549 addq         %r15, %rdx
	//0x0000354c LBB0_646
	0x49, 0xc7, 0xc7, 0xfe, 0xff, 0xff, 0xff, //0x0000354c movq         $-2, %r15
	0x4c, 0x8b, 0x5c, 0x24, 0x08, //0x00003553 movq         $8(%rsp), %r11
	0x49, 0x89, 0x13, //0x00003558 movq         %rdx, (%r11)
	0xe9, 0x78, 0xff, 0xff, 0xff, //0x0000355b jmp          LBB0_638
	//0x00003560 LBB0_647
	0x66, 0x0f, 0xbc, 0xc9, //0x00003560 bsfw         %cx, %cx
	0x0f, 0xb7, 0xc9, //0x00003564 movzwl       %cx, %ecx
	0x48, 0x29, 0xf9, //0x00003567 subq         %rdi, %rcx
	0xe9, 0xe3, 0xd0, 0xff, 0xff, //0x0000356a jmp          LBB0_52
	//0x0000356f LBB0_649
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x0000356f movq         $-1, %rcx
	0xe9, 0x02, 0x01, 0x00, 0x00, //0x00003576 jmp          LBB0_662
	//0x0000357b LBB0_651
	0x49, 0xc7, 0xc7, 0xfe, 0xff, 0xff, 0xff, //0x0000357b movq         $-2, %r15
	0x80, 0xfa, 0x61, //0x00003582 cmpb         $97, %dl
	0x0f, 0x85, 0x4d, 0xff, 0xff, 0xff, //0x00003585 jne          LBB0_638
	0x48, 0x8d, 0x41, 0x01, //0x0000358b leaq         $1(%rcx), %rax
	0x49, 0x89, 0x03, //0x0000358f movq         %rax, (%r11)
	0x41, 0x80, 0x7c, 0x08, 0x01, 0x6c, //0x00003592 cmpb         $108, $1(%r8,%rcx)
	0x0f, 0x85, 0x3a, 0xff, 0xff, 0xff, //0x00003598 jne          LBB0_638
	0x48, 0x8d, 0x41, 0x02, //0x0000359e leaq         $2(%rcx), %rax
	0x49, 0x89, 0x03, //0x000035a2 movq         %rax, (%r11)
	0x41, 0x80, 0x7c, 0x08, 0x02, 0x73, //0x000035a5 cmpb         $115, $2(%r8,%rcx)
	0x0f, 0x85, 0x27, 0xff, 0xff, 0xff, //0x000035ab jne          LBB0_638
	0x48, 0x8d, 0x41, 0x03, //0x000035b1 leaq         $3(%rcx), %rax
	0x49, 0x89, 0x03, //0x000035b5 movq         %rax, (%r11)
	0x41, 0x80, 0x7c, 0x08, 0x03, 0x65, //0x000035b8 cmpb         $101, $3(%r8,%rcx)
	0x0f, 0x85, 0x14, 0xff, 0xff, 0xff, //0x000035be jne          LBB0_638
	0x48, 0x83, 0xc1, 0x04, //0x000035c4 addq         $4, %rcx
	0x49, 0x89, 0x0b, //0x000035c8 movq         %rcx, (%r11)
	0xe9, 0x08, 0xff, 0xff, 0xff, //0x000035cb jmp          LBB0_638
	//0x000035d0 LBB0_293
	0x4d, 0x89, 0x3b, //0x000035d0 movq         %r15, (%r11)
	0x43, 0x80, 0x3c, 0x38, 0x6e, //0x000035d3 cmpb         $110, (%r8,%r15)
	0x0f, 0x85, 0xf3, 0xfe, 0xff, 0xff, //0x000035d8 jne          LBB0_637
	0x49, 0x89, 0x0b, //0x000035de movq         %rcx, (%r11)
	0x41, 0x80, 0x3c, 0x08, 0x75, //0x000035e1 cmpb         $117, (%r8,%rcx)
	0x0f, 0x85, 0xe5, 0xfe, 0xff, 0xff, //0x000035e6 jne          LBB0_637
	0x48, 0x8d, 0x41, 0x01, //0x000035ec leaq         $1(%rcx), %rax
	0x49, 0x89, 0x03, //0x000035f0 movq         %rax, (%r11)
	0x41, 0x80, 0x7c, 0x08, 0x01, 0x6c, //0x000035f3 cmpb         $108, $1(%r8,%rcx)
	0x0f, 0x85, 0xd2, 0xfe, 0xff, 0xff, //0x000035f9 jne          LBB0_637
	0x48, 0x8d, 0x41, 0x02, //0x000035ff leaq         $2(%rcx), %rax
	0x49, 0x89, 0x03, //0x00003603 movq         %rax, (%r11)
	0x41, 0x80, 0x7c, 0x08, 0x02, 0x6c, //0x00003606 cmpb         $108, $2(%r8,%rcx)
	0x0f, 0x85, 0xbf, 0xfe, 0xff, 0xff, //0x0000360c jne          LBB0_637
	0xe9, 0x42, 0x00, 0x00, 0x00, //0x00003612 jmp          LBB0_660
	//0x00003617 LBB0_656
	0x4d, 0x89, 0x3b, //0x00003617 movq         %r15, (%r11)
	0x43, 0x80, 0x3c, 0x38, 0x74, //0x0000361a cmpb         $116, (%r8,%r15)
	0x0f, 0x85, 0xac, 0xfe, 0xff, 0xff, //0x0000361f jne          LBB0_637
	0x49, 0x89, 0x0b, //0x00003625 movq         %rcx, (%r11)
	0x41, 0x80, 0x3c, 0x08, 0x72, //0x00003628 cmpb         $114, (%r8,%rcx)
	0x0f, 0x85, 0x9e, 0xfe, 0xff, 0xff, //0x0000362d jne          LBB0_637
	0x48, 0x8d, 0x41, 0x01, //0x00003633 leaq         $1(%rcx), %rax
	0x49, 0x89, 0x03, //0x00003637 movq         %rax, (%r11)
	0x41, 0x80, 0x7c, 0x08, 0x01, 0x75, //0x0000363a cmpb         $117, $1(%r8,%rcx)
	0x0f, 0x85, 0x8b, 0xfe, 0xff, 0xff, //0x00003640 jne          LBB0_637
	0x48, 0x8d, 0x41, 0x02, //0x00003646 leaq         $2(%rcx), %rax
	0x49, 0x89, 0x03, //0x0000364a movq         %rax, (%r11)
	0x41, 0x80, 0x7c, 0x08, 0x02, 0x65, //0x0000364d cmpb         $101, $2(%r8,%rcx)
	0x0f, 0x85, 0x78, 0xfe, 0xff, 0xff, //0x00003653 jne          LBB0_637
	//0x00003659 LBB0_660
	0x48, 0x83, 0xc1, 0x03, //0x00003659 addq         $3, %rcx
	0x49, 0x89, 0x0b, //0x0000365d movq         %rcx, (%r11)
	0xe9, 0x6c, 0xfe, 0xff, 0xff, //0x00003660 jmp          LBB0_637
	//0x00003665 LBB0_290
	0x48, 0x83, 0xc1, 0xff, //0x00003665 addq         $-1, %rcx
	0x49, 0x89, 0xcf, //0x00003669 movq         %rcx, %r15
	0xe9, 0x67, 0xfe, 0xff, 0xff, //0x0000366c jmp          LBB0_638
	//0x00003671 LBB0_426
	0x49, 0x83, 0xc7, 0xff, //0x00003671 addq         $-1, %r15
	0xe9, 0x5e, 0xfe, 0xff, 0xff, //0x00003675 jmp          LBB0_638
	//0x0000367a LBB0_661
	0x4c, 0x89, 0xe1, //0x0000367a movq         %r12, %rcx
	//0x0000367d LBB0_662
	0x48, 0xf7, 0xd1, //0x0000367d notq         %rcx
	0x48, 0x8b, 0x44, 0x24, 0x08, //0x00003680 movq         $8(%rsp), %rax
	0x48, 0x01, 0x08, //0x00003685 addq         %rcx, (%rax)
	0xe9, 0x44, 0xfe, 0xff, 0xff, //0x00003688 jmp          LBB0_637
	//0x0000368d LBB0_663
	0x4c, 0x29, 0xd0, //0x0000368d subq         %r10, %rax
	0x48, 0x01, 0xf0, //0x00003690 addq         %rsi, %rax
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00003693 movq         $-1, %r15
	0x48, 0x39, 0xd0, //0x0000369a cmpq         %rdx, %rax
	0x0f, 0x82, 0x5b, 0xce, 0xff, 0xff, //0x0000369d jb           LBB0_33
	0xe9, 0x30, 0xfe, 0xff, 0xff, //0x000036a3 jmp          LBB0_638
	//0x000036a8 LBB0_670
	0x4d, 0x89, 0xc4, //0x000036a8 movq         %r8, %r12
	0xe9, 0x12, 0xf7, 0xff, 0xff, //0x000036ab jmp          LBB0_381
	//0x000036b0 LBB0_665
	0x4c, 0x29, 0xd2, //0x000036b0 subq         %r10, %rdx
	0x48, 0x83, 0xc2, 0x01, //0x000036b3 addq         $1, %rdx
	0x49, 0x89, 0x55, 0x00, //0x000036b7 movq         %rdx, (%r13)
	0x49, 0x89, 0xc7, //0x000036bb movq         %rax, %r15
	0xe9, 0x15, 0xfe, 0xff, 0xff, //0x000036be jmp          LBB0_638
	//0x000036c3 LBB0_217
	0x4c, 0x01, 0xfa, //0x000036c3 addq         %r15, %rdx
	0x49, 0xc7, 0xc7, 0xfe, 0xff, 0xff, 0xff, //0x000036c6 movq         $-2, %r15
	0x49, 0x89, 0x13, //0x000036cd movq         %rdx, (%r11)
	0xe9, 0x03, 0xfe, 0xff, 0xff, //0x000036d0 jmp          LBB0_638
	//0x000036d5 LBB0_666
	0x4c, 0x01, 0xd0, //0x000036d5 addq         %r10, %rax
	0x48, 0x85, 0xff, //0x000036d8 testq        %rdi, %rdi
	0x0f, 0x85, 0xa7, 0xcd, 0xff, 0xff, //0x000036db jne          LBB0_25
	0xe9, 0xd9, 0xcd, 0xff, 0xff, //0x000036e1 jmp          LBB0_30
	//0x000036e6 LBB0_667
	0x4c, 0x01, 0xd2, //0x000036e6 addq         %r10, %rdx
	0x48, 0x83, 0xfe, 0x10, //0x000036e9 cmpq         $16, %rsi
	0x0f, 0x83, 0xbf, 0xce, 0xff, 0xff, //0x000036ed jae          LBB0_40
	0xe9, 0x0e, 0xcf, 0xff, 0xff, //0x000036f3 jmp          LBB0_43
	//0x000036f8 LBB0_668
	0x4c, 0x29, 0xd2, //0x000036f8 subq         %r10, %rdx
	0x48, 0x01, 0xfa, //0x000036fb addq         %rdi, %rdx
	//0x000036fe LBB0_669
	0x49, 0x89, 0x13, //0x000036fe movq         %rdx, (%r11)
	0x49, 0x89, 0xc7, //0x00003701 movq         %rax, %r15
	0xe9, 0xcf, 0xfd, 0xff, 0xff, //0x00003704 jmp          LBB0_638
	//0x00003709 LBB0_671
	0x4c, 0x01, 0xd2, //0x00003709 addq         %r10, %rdx
	0xe9, 0xba, 0xf7, 0xff, 0xff, //0x0000370c jmp          LBB0_580
	//0x00003711 LBB0_672
	0x49, 0x8b, 0x45, 0x08, //0x00003711 movq         $8(%r13), %rax
	0x48, 0x8b, 0x4c, 0x24, 0x08, //0x00003715 movq         $8(%rsp), %rcx
	0x48, 0x89, 0x01, //0x0000371a movq         %rax, (%rcx)
	0xe9, 0xb6, 0xfd, 0xff, 0xff, //0x0000371d jmp          LBB0_638
	//0x00003722 LBB0_673
	0x89, 0xf9, //0x00003722 movl         %edi, %ecx
	0x4c, 0x2b, 0x7c, 0x24, 0x10, //0x00003724 subq         $16(%rsp), %r15
	0x49, 0x01, 0xcf, //0x00003729 addq         %rcx, %r15
	0x4c, 0x89, 0xfa, //0x0000372c movq         %r15, %rdx
	0xe9, 0x18, 0xfe, 0xff, 0xff, //0x0000372f jmp          LBB0_646
	//0x00003734 LBB0_678
	0x4c, 0x2b, 0x7c, 0x24, 0x10, //0x00003734 subq         $16(%rsp), %r15
	0x89, 0xfa, //0x00003739 movl         %edi, %edx
	0xe9, 0x09, 0xfe, 0xff, 0xff, //0x0000373b jmp          LBB0_645
	//0x00003740 LBB0_674
	0x4c, 0x2b, 0x7c, 0x24, 0x10, //0x00003740 subq         $16(%rsp), %r15
	0x4c, 0x89, 0xfa, //0x00003745 movq         %r15, %rdx
	0x49, 0xc7, 0xc7, 0xfe, 0xff, 0xff, 0xff, //0x00003748 movq         $-2, %r15
	0x49, 0x89, 0x13, //0x0000374f movq         %rdx, (%r11)
	0xe9, 0x81, 0xfd, 0xff, 0xff, //0x00003752 jmp          LBB0_638
	//0x00003757 LBB0_677
	0x89, 0xf8, //0x00003757 movl         %edi, %eax
	0x4c, 0x2b, 0x7c, 0x24, 0x10, //0x00003759 subq         $16(%rsp), %r15
	0x49, 0x01, 0xc7, //0x0000375e addq         %rax, %r15
	0x4c, 0x89, 0xfa, //0x00003761 movq         %r15, %rdx
	0xe9, 0xe3, 0xfd, 0xff, 0xff, //0x00003764 jmp          LBB0_646
	//0x00003769 LBB0_675
	0x49, 0x8d, 0x48, 0xff, //0x00003769 leaq         $-1(%r8), %rcx
	0x4c, 0x39, 0xf1, //0x0000376d cmpq         %r14, %rcx
	0x0f, 0x84, 0x62, 0xfd, 0xff, 0xff, //0x00003770 je           LBB0_638
	0x4b, 0x8d, 0x14, 0x0e, //0x00003776 leaq         (%r14,%r9), %rdx
	0x48, 0x83, 0xc2, 0x02, //0x0000377a addq         $2, %rdx
	0x4d, 0x29, 0xf0, //0x0000377e subq         %r14, %r8
	0x49, 0x83, 0xc0, 0xfe, //0x00003781 addq         $-2, %r8
	0x4d, 0x89, 0xc6, //0x00003785 movq         %r8, %r14
	0xe9, 0x3e, 0xf7, 0xff, 0xff, //0x00003788 jmp          LBB0_580
	//0x0000378d LBB0_679
	0x4c, 0x2b, 0x7c, 0x24, 0x10, //0x0000378d subq         $16(%rsp), %r15
	0x4c, 0x89, 0xfa, //0x00003792 movq         %r15, %rdx
	0xe9, 0xb2, 0xfd, 0xff, 0xff, //0x00003795 jmp          LBB0_646
	0x90, 0x90, //0x0000379a .p2align 2, 0x90
	// // .set L0_0_set_638, LBB0_638-LJTI0_0
	// // .set L0_0_set_569, LBB0_569-LJTI0_0
	// // .set L0_0_set_570, LBB0_570-LJTI0_0
	// // .set L0_0_set_35, LBB0_35-LJTI0_0
	// // .set L0_0_set_607, LBB0_607-LJTI0_0
	// // .set L0_0_set_582, LBB0_582-LJTI0_0
	// // .set L0_0_set_568, LBB0_568-LJTI0_0
	// // .set L0_0_set_583, LBB0_583-LJTI0_0
	//0x0000379c LJTI0_0
	0x3c, 0xfd, 0xff, 0xff, //0x0000379c .long L0_0_set_638
	0x44, 0xf6, 0xff, 0xff, //0x000037a0 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000037a4 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000037a8 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000037ac .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000037b0 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000037b4 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000037b8 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000037bc .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000037c0 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000037c4 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000037c8 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000037cc .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000037d0 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000037d4 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000037d8 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000037dc .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000037e0 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000037e4 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000037e8 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000037ec .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000037f0 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000037f4 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000037f8 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000037fc .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x00003800 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x00003804 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x00003808 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x0000380c .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x00003810 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x00003814 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x00003818 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x0000381c .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x00003820 .long L0_0_set_569
	0x4c, 0xf6, 0xff, 0xff, //0x00003824 .long L0_0_set_570
	0x44, 0xf6, 0xff, 0xff, //0x00003828 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x0000382c .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x00003830 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x00003834 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x00003838 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x0000383c .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x00003840 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x00003844 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x00003848 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x0000384c .long L0_0_set_569
	0x8e, 0xcd, 0xff, 0xff, //0x00003850 .long L0_0_set_35
	0x44, 0xf6, 0xff, 0xff, //0x00003854 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x00003858 .long L0_0_set_569
	0x8e, 0xcd, 0xff, 0xff, //0x0000385c .long L0_0_set_35
	0x8e, 0xcd, 0xff, 0xff, //0x00003860 .long L0_0_set_35
	0x8e, 0xcd, 0xff, 0xff, //0x00003864 .long L0_0_set_35
	0x8e, 0xcd, 0xff, 0xff, //0x00003868 .long L0_0_set_35
	0x8e, 0xcd, 0xff, 0xff, //0x0000386c .long L0_0_set_35
	0x8e, 0xcd, 0xff, 0xff, //0x00003870 .long L0_0_set_35
	0x8e, 0xcd, 0xff, 0xff, //0x00003874 .long L0_0_set_35
	0x8e, 0xcd, 0xff, 0xff, //0x00003878 .long L0_0_set_35
	0x8e, 0xcd, 0xff, 0xff, //0x0000387c .long L0_0_set_35
	0x8e, 0xcd, 0xff, 0xff, //0x00003880 .long L0_0_set_35
	0x44, 0xf6, 0xff, 0xff, //0x00003884 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x00003888 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x0000388c .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x00003890 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x00003894 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x00003898 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x0000389c .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000038a0 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000038a4 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000038a8 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000038ac .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000038b0 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000038b4 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000038b8 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000038bc .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000038c0 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000038c4 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000038c8 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000038cc .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000038d0 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000038d4 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000038d8 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000038dc .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000038e0 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000038e4 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000038e8 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000038ec .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000038f0 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000038f4 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000038f8 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x000038fc .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x00003900 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x00003904 .long L0_0_set_569
	0x0f, 0xfa, 0xff, 0xff, //0x00003908 .long L0_0_set_607
	0x44, 0xf6, 0xff, 0xff, //0x0000390c .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x00003910 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x00003914 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x00003918 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x0000391c .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x00003920 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x00003924 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x00003928 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x0000392c .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x00003930 .long L0_0_set_569
	0x44, 0xf7, 0xff, 0xff, //0x00003934 .long L0_0_set_582
	0x44, 0xf6, 0xff, 0xff, //0x00003938 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x0000393c .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x00003940 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x00003944 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x00003948 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x0000394c .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x00003950 .long L0_0_set_569
	0xf9, 0xf5, 0xff, 0xff, //0x00003954 .long L0_0_set_568
	0x44, 0xf6, 0xff, 0xff, //0x00003958 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x0000395c .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x00003960 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x00003964 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x00003968 .long L0_0_set_569
	0xf9, 0xf5, 0xff, 0xff, //0x0000396c .long L0_0_set_568
	0x44, 0xf6, 0xff, 0xff, //0x00003970 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x00003974 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x00003978 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x0000397c .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x00003980 .long L0_0_set_569
	0x44, 0xf6, 0xff, 0xff, //0x00003984 .long L0_0_set_569
	0x57, 0xf7, 0xff, 0xff, //0x00003988 .long L0_0_set_583
	// // .set L0_1_set_90, LBB0_90-LJTI0_1
	// // .set L0_1_set_117, LBB0_117-LJTI0_1
	// // .set L0_1_set_96, LBB0_96-LJTI0_1
	// // .set L0_1_set_115, LBB0_115-LJTI0_1
	// // .set L0_1_set_93, LBB0_93-LJTI0_1
	// // .set L0_1_set_120, LBB0_120-LJTI0_1
	//0x0000398c LJTI0_1
	0x90, 0xcf, 0xff, 0xff, //0x0000398c .long L0_1_set_90
	0x40, 0xd1, 0xff, 0xff, //0x00003990 .long L0_1_set_117
	0xc7, 0xcf, 0xff, 0xff, //0x00003994 .long L0_1_set_96
	0x29, 0xd1, 0xff, 0xff, //0x00003998 .long L0_1_set_115
	0xa7, 0xcf, 0xff, 0xff, //0x0000399c .long L0_1_set_93
	0xc2, 0xd5, 0xff, 0xff, //0x000039a0 .long L0_1_set_120
	// // .set L0_2_set_638, LBB0_638-LJTI0_2
	// // .set L0_2_set_637, LBB0_637-LJTI0_2
	// // .set L0_2_set_263, LBB0_263-LJTI0_2
	// // .set L0_2_set_281, LBB0_281-LJTI0_2
	// // .set L0_2_set_134, LBB0_134-LJTI0_2
	// // .set L0_2_set_285, LBB0_285-LJTI0_2
	// // .set L0_2_set_287, LBB0_287-LJTI0_2
	// // .set L0_2_set_291, LBB0_291-LJTI0_2
	// // .set L0_2_set_299, LBB0_299-LJTI0_2
	// // .set L0_2_set_297, LBB0_297-LJTI0_2
	//0x000039a4 LJTI0_2
	0x34, 0xfb, 0xff, 0xff, //0x000039a4 .long L0_2_set_638
	0x2d, 0xfb, 0xff, 0xff, //0x000039a8 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x000039ac .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x000039b0 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x000039b4 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x000039b8 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x000039bc .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x000039c0 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x000039c4 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x000039c8 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x000039cc .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x000039d0 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x000039d4 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x000039d8 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x000039dc .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x000039e0 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x000039e4 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x000039e8 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x000039ec .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x000039f0 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x000039f4 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x000039f8 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x000039fc .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003a00 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003a04 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003a08 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003a0c .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003a10 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003a14 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003a18 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003a1c .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003a20 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003a24 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003a28 .long L0_2_set_637
	0xcd, 0xdb, 0xff, 0xff, //0x00003a2c .long L0_2_set_263
	0x2d, 0xfb, 0xff, 0xff, //0x00003a30 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003a34 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003a38 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003a3c .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003a40 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003a44 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003a48 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003a4c .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003a50 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003a54 .long L0_2_set_637
	0x21, 0xdd, 0xff, 0xff, //0x00003a58 .long L0_2_set_281
	0x2d, 0xfb, 0xff, 0xff, //0x00003a5c .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003a60 .long L0_2_set_637
	0x71, 0xd1, 0xff, 0xff, //0x00003a64 .long L0_2_set_134
	0x71, 0xd1, 0xff, 0xff, //0x00003a68 .long L0_2_set_134
	0x71, 0xd1, 0xff, 0xff, //0x00003a6c .long L0_2_set_134
	0x71, 0xd1, 0xff, 0xff, //0x00003a70 .long L0_2_set_134
	0x71, 0xd1, 0xff, 0xff, //0x00003a74 .long L0_2_set_134
	0x71, 0xd1, 0xff, 0xff, //0x00003a78 .long L0_2_set_134
	0x71, 0xd1, 0xff, 0xff, //0x00003a7c .long L0_2_set_134
	0x71, 0xd1, 0xff, 0xff, //0x00003a80 .long L0_2_set_134
	0x71, 0xd1, 0xff, 0xff, //0x00003a84 .long L0_2_set_134
	0x71, 0xd1, 0xff, 0xff, //0x00003a88 .long L0_2_set_134
	0x2d, 0xfb, 0xff, 0xff, //0x00003a8c .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003a90 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003a94 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003a98 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003a9c .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003aa0 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003aa4 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003aa8 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003aac .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003ab0 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003ab4 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003ab8 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003abc .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003ac0 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003ac4 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003ac8 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003acc .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003ad0 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003ad4 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003ad8 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003adc .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003ae0 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003ae4 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003ae8 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003aec .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003af0 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003af4 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003af8 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003afc .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003b00 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003b04 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003b08 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003b0c .long L0_2_set_637
	0x5b, 0xdd, 0xff, 0xff, //0x00003b10 .long L0_2_set_285
	0x2d, 0xfb, 0xff, 0xff, //0x00003b14 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003b18 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003b1c .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003b20 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003b24 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003b28 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003b2c .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003b30 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003b34 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003b38 .long L0_2_set_637
	0x80, 0xdd, 0xff, 0xff, //0x00003b3c .long L0_2_set_287
	0x2d, 0xfb, 0xff, 0xff, //0x00003b40 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003b44 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003b48 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003b4c .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003b50 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003b54 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003b58 .long L0_2_set_637
	0xb9, 0xdd, 0xff, 0xff, //0x00003b5c .long L0_2_set_291
	0x2d, 0xfb, 0xff, 0xff, //0x00003b60 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003b64 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003b68 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003b6c .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003b70 .long L0_2_set_637
	0x0a, 0xde, 0xff, 0xff, //0x00003b74 .long L0_2_set_299
	0x2d, 0xfb, 0xff, 0xff, //0x00003b78 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003b7c .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003b80 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003b84 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003b88 .long L0_2_set_637
	0x2d, 0xfb, 0xff, 0xff, //0x00003b8c .long L0_2_set_637
	0xe5, 0xdd, 0xff, 0xff, //0x00003b90 .long L0_2_set_297
	// // .set L0_3_set_369, LBB0_369-LJTI0_3
	// // .set L0_3_set_385, LBB0_385-LJTI0_3
	// // .set L0_3_set_363, LBB0_363-LJTI0_3
	// // .set L0_3_set_372, LBB0_372-LJTI0_3
	//0x00003b94 LJTI0_3
	0xe6, 0xe0, 0xff, 0xff, //0x00003b94 .long L0_3_set_369
	0xae, 0xe1, 0xff, 0xff, //0x00003b98 .long L0_3_set_385
	0xe6, 0xe0, 0xff, 0xff, //0x00003b9c .long L0_3_set_369
	0x8a, 0xe0, 0xff, 0xff, //0x00003ba0 .long L0_3_set_363
	0xae, 0xe1, 0xff, 0xff, //0x00003ba4 .long L0_3_set_385
	0xae, 0xe1, 0xff, 0xff, //0x00003ba8 .long L0_3_set_385
	0xae, 0xe1, 0xff, 0xff, //0x00003bac .long L0_3_set_385
	0xae, 0xe1, 0xff, 0xff, //0x00003bb0 .long L0_3_set_385
	0xae, 0xe1, 0xff, 0xff, //0x00003bb4 .long L0_3_set_385
	0xae, 0xe1, 0xff, 0xff, //0x00003bb8 .long L0_3_set_385
	0xae, 0xe1, 0xff, 0xff, //0x00003bbc .long L0_3_set_385
	0xae, 0xe1, 0xff, 0xff, //0x00003bc0 .long L0_3_set_385
	0xae, 0xe1, 0xff, 0xff, //0x00003bc4 .long L0_3_set_385
	0xae, 0xe1, 0xff, 0xff, //0x00003bc8 .long L0_3_set_385
	0xae, 0xe1, 0xff, 0xff, //0x00003bcc .long L0_3_set_385
	0xae, 0xe1, 0xff, 0xff, //0x00003bd0 .long L0_3_set_385
	0xae, 0xe1, 0xff, 0xff, //0x00003bd4 .long L0_3_set_385
	0xae, 0xe1, 0xff, 0xff, //0x00003bd8 .long L0_3_set_385
	0xae, 0xe1, 0xff, 0xff, //0x00003bdc .long L0_3_set_385
	0xae, 0xe1, 0xff, 0xff, //0x00003be0 .long L0_3_set_385
	0xae, 0xe1, 0xff, 0xff, //0x00003be4 .long L0_3_set_385
	0xae, 0xe1, 0xff, 0xff, //0x00003be8 .long L0_3_set_385
	0xae, 0xe1, 0xff, 0xff, //0x00003bec .long L0_3_set_385
	0xae, 0xe1, 0xff, 0xff, //0x00003bf0 .long L0_3_set_385
	0xae, 0xe1, 0xff, 0xff, //0x00003bf4 .long L0_3_set_385
	0xae, 0xe1, 0xff, 0xff, //0x00003bf8 .long L0_3_set_385
	0x02, 0xe1, 0xff, 0xff, //0x00003bfc .long L0_3_set_372
	// // .set L0_4_set_178, LBB0_178-LJTI0_4
	// // .set L0_4_set_244, LBB0_244-LJTI0_4
	// // .set L0_4_set_184, LBB0_184-LJTI0_4
	// // .set L0_4_set_187, LBB0_187-LJTI0_4
	//0x00003c00 LJTI0_4
	0xcc, 0xd2, 0xff, 0xff, //0x00003c00 .long L0_4_set_178
	0x31, 0xd8, 0xff, 0xff, //0x00003c04 .long L0_4_set_244
	0xcc, 0xd2, 0xff, 0xff, //0x00003c08 .long L0_4_set_178
	0x1a, 0xd3, 0xff, 0xff, //0x00003c0c .long L0_4_set_184
	0x31, 0xd8, 0xff, 0xff, //0x00003c10 .long L0_4_set_244
	0x31, 0xd8, 0xff, 0xff, //0x00003c14 .long L0_4_set_244
	0x31, 0xd8, 0xff, 0xff, //0x00003c18 .long L0_4_set_244
	0x31, 0xd8, 0xff, 0xff, //0x00003c1c .long L0_4_set_244
	0x31, 0xd8, 0xff, 0xff, //0x00003c20 .long L0_4_set_244
	0x31, 0xd8, 0xff, 0xff, //0x00003c24 .long L0_4_set_244
	0x31, 0xd8, 0xff, 0xff, //0x00003c28 .long L0_4_set_244
	0x31, 0xd8, 0xff, 0xff, //0x00003c2c .long L0_4_set_244
	0x31, 0xd8, 0xff, 0xff, //0x00003c30 .long L0_4_set_244
	0x31, 0xd8, 0xff, 0xff, //0x00003c34 .long L0_4_set_244
	0x31, 0xd8, 0xff, 0xff, //0x00003c38 .long L0_4_set_244
	0x31, 0xd8, 0xff, 0xff, //0x00003c3c .long L0_4_set_244
	0x31, 0xd8, 0xff, 0xff, //0x00003c40 .long L0_4_set_244
	0x31, 0xd8, 0xff, 0xff, //0x00003c44 .long L0_4_set_244
	0x31, 0xd8, 0xff, 0xff, //0x00003c48 .long L0_4_set_244
	0x31, 0xd8, 0xff, 0xff, //0x00003c4c .long L0_4_set_244
	0x31, 0xd8, 0xff, 0xff, //0x00003c50 .long L0_4_set_244
	0x31, 0xd8, 0xff, 0xff, //0x00003c54 .long L0_4_set_244
	0x31, 0xd8, 0xff, 0xff, //0x00003c58 .long L0_4_set_244
	0x31, 0xd8, 0xff, 0xff, //0x00003c5c .long L0_4_set_244
	0x31, 0xd8, 0xff, 0xff, //0x00003c60 .long L0_4_set_244
	0x31, 0xd8, 0xff, 0xff, //0x00003c64 .long L0_4_set_244
	0x3b, 0xd3, 0xff, 0xff, //0x00003c68 .long L0_4_set_187
	//0x00003c6c .p2align 2, 0x00
	//0x00003c6c _MASK_USE_NUMBER
	0x02, 0x00, 0x00, 0x00, //0x00003c6c .long 2
}
 
