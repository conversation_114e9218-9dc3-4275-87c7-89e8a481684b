// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package sse

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__skip_object = 256
)

const (
    _stack__skip_object = 184
)

const (
    _size__skip_object = 15328
)

var (
    _pcsp__skip_object = [][2]uint32{
        {0x1, 0},
        {0x6, 8},
        {0x8, 16},
        {0xa, 24},
        {0xc, 32},
        {0xd, 40},
        {0x14, 48},
        {0x394e, 184},
        {0x394f, 48},
        {0x3951, 40},
        {0x3953, 32},
        {0x3955, 24},
        {0x3957, 16},
        {0x3958, 8},
        {0x3959, 0},
        {0x3be0, 184},
    }
)

var _cfunc_skip_object = []loader.CFunc{
    {"_skip_object_entry", 0,  _entry__skip_object, 0, nil},
    {"_skip_object", _entry__skip_object, _size__skip_object, _stack__skip_object, _pcsp__skip_object},
}
