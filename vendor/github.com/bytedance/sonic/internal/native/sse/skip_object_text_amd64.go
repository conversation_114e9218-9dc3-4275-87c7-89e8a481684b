// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package sse

var _text_skip_object = []byte{
	// .p2align 4, 0x00
	// LCPI0_0
	0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // .quad 1
	0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000008 .quad 6
	//0x00000010 LCPI0_1
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, //0x00000010 QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	//0x00000020 LCPI0_2
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x00000020 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	//0x00000030 LCPI0_3
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, //0x00000030 QUAD $0x2020202020202020; QUAD $0x2020202020202020  // .space 16, '                '
	//0x00000040 LCPI0_4
	0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, //0x00000040 QUAD $0x7b7b7b7b7b7b7b7b; QUAD $0x7b7b7b7b7b7b7b7b  // .space 16, '{{{{{{{{{{{{{{{{'
	//0x00000050 LCPI0_5
	0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, //0x00000050 QUAD $0x7d7d7d7d7d7d7d7d; QUAD $0x7d7d7d7d7d7d7d7d  // .space 16, '}}}}}}}}}}}}}}}}'
	//0x00000060 LCPI0_6
	0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, //0x00000060 QUAD $0x5b5b5b5b5b5b5b5b; QUAD $0x5b5b5b5b5b5b5b5b  // .space 16, '[[[[[[[[[[[[[[[['
	//0x00000070 LCPI0_7
	0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, //0x00000070 QUAD $0x5d5d5d5d5d5d5d5d; QUAD $0x5d5d5d5d5d5d5d5d  // .space 16, ']]]]]]]]]]]]]]]]'
	//0x00000080 LCPI0_8
	0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, //0x00000080 QUAD $0x2c2c2c2c2c2c2c2c; QUAD $0x2c2c2c2c2c2c2c2c  // .space 16, ',,,,,,,,,,,,,,,,'
	//0x00000090 LCPI0_9
	0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, //0x00000090 QUAD $0xdfdfdfdfdfdfdfdf; QUAD $0xdfdfdfdfdfdfdfdf  // .space 16, '\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf'
	//0x000000a0 LCPI0_10
	0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, //0x000000a0 QUAD $0x2f2f2f2f2f2f2f2f; QUAD $0x2f2f2f2f2f2f2f2f  // .space 16, '////////////////'
	//0x000000b0 LCPI0_11
	0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, //0x000000b0 QUAD $0x3a3a3a3a3a3a3a3a; QUAD $0x3a3a3a3a3a3a3a3a  // .space 16, '::::::::::::::::'
	//0x000000c0 LCPI0_12
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x000000c0 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	//0x000000d0 LCPI0_13
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x000000d0 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	//0x000000e0 LCPI0_14
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x000000e0 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	//0x000000f0 LCPI0_15
	0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, //0x000000f0 QUAD $0x4545454545454545; QUAD $0x4545454545454545  // .space 16, 'EEEEEEEEEEEEEEEE'
	//0x00000100 .p2align 4, 0x90
	//0x00000100 _skip_object
	0x55, //0x00000100 pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000101 movq         %rsp, %rbp
	0x41, 0x57, //0x00000104 pushq        %r15
	0x41, 0x56, //0x00000106 pushq        %r14
	0x41, 0x55, //0x00000108 pushq        %r13
	0x41, 0x54, //0x0000010a pushq        %r12
	0x53, //0x0000010c pushq        %rbx
	0x48, 0x81, 0xec, 0x88, 0x00, 0x00, 0x00, //0x0000010d subq         $136, %rsp
	0x48, 0x89, 0x4d, 0x98, //0x00000114 movq         %rcx, $-104(%rbp)
	0x49, 0x89, 0xd5, //0x00000118 movq         %rdx, %r13
	0x49, 0x89, 0xf6, //0x0000011b movq         %rsi, %r14
	0x48, 0x89, 0x7d, 0xa8, //0x0000011e movq         %rdi, $-88(%rbp)
	0x0f, 0x10, 0x05, 0xd7, 0xfe, 0xff, 0xff, //0x00000122 movups       $-297(%rip), %xmm0  /* LCPI0_0+0(%rip) */
	0x0f, 0x11, 0x02, //0x00000129 movups       %xmm0, (%rdx)
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x0000012c movq         $-1, %rcx
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00000133 movabsq      $4294977024, %r11
	0xf3, 0x0f, 0x6f, 0x05, 0xcb, 0xfe, 0xff, 0xff, //0x0000013d movdqu       $-309(%rip), %xmm0  /* LCPI0_1+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x0d, 0xd3, 0xfe, 0xff, 0xff, //0x00000145 movdqu       $-301(%rip), %xmm1  /* LCPI0_2+0(%rip) */
	0xf3, 0x44, 0x0f, 0x6f, 0x25, 0xda, 0xfe, 0xff, 0xff, //0x0000014d movdqu       $-294(%rip), %xmm12  /* LCPI0_3+0(%rip) */
	0x66, 0x45, 0x0f, 0x76, 0xd2, //0x00000156 pcmpeqd      %xmm10, %xmm10
	0xf3, 0x44, 0x0f, 0x6f, 0x3d, 0x4c, 0xff, 0xff, 0xff, //0x0000015b movdqu       $-180(%rip), %xmm15  /* LCPI0_11+0(%rip) */
	0xf3, 0x44, 0x0f, 0x6f, 0x0d, 0x23, 0xff, 0xff, 0xff, //0x00000164 movdqu       $-221(%rip), %xmm9  /* LCPI0_9+0(%rip) */
	0xf3, 0x44, 0x0f, 0x6f, 0x1d, 0x7a, 0xff, 0xff, 0xff, //0x0000016d movdqu       $-134(%rip), %xmm11  /* LCPI0_15+0(%rip) */
	0xf3, 0x44, 0x0f, 0x6f, 0x35, 0x01, 0xff, 0xff, 0xff, //0x00000176 movdqu       $-255(%rip), %xmm14  /* LCPI0_8+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x15, 0xe9, 0xfe, 0xff, 0xff, //0x0000017f movdqu       $-279(%rip), %xmm2  /* LCPI0_7+0(%rip) */
	0xf3, 0x44, 0x0f, 0x6f, 0x2d, 0xb0, 0xfe, 0xff, 0xff, //0x00000187 movdqu       $-336(%rip), %xmm13  /* LCPI0_4+0(%rip) */
	0xf3, 0x44, 0x0f, 0x6f, 0x05, 0xb7, 0xfe, 0xff, 0xff, //0x00000190 movdqu       $-329(%rip), %xmm8  /* LCPI0_5+0(%rip) */
	0x48, 0x89, 0x75, 0xd0, //0x00000199 movq         %rsi, $-48(%rbp)
	0x48, 0x89, 0x55, 0xb8, //0x0000019d movq         %rdx, $-72(%rbp)
	0xe9, 0x7a, 0x00, 0x00, 0x00, //0x000001a1 jmp          LBB0_6
	//0x000001a6 LBB0_613
	0x66, 0x0f, 0xbc, 0xc2, //0x000001a6 bsfw         %dx, %ax
	0x0f, 0xb7, 0xc0, //0x000001aa movzwl       %ax, %eax
	0x48, 0x29, 0xc8, //0x000001ad subq         %rcx, %rax
	0x49, 0x89, 0x06, //0x000001b0 movq         %rax, (%r14)
	0x48, 0x85, 0xf6, //0x000001b3 testq        %rsi, %rsi
	0x0f, 0x8e, 0x9d, 0x38, 0x00, 0x00, //0x000001b6 jle          LBB0_614
	0x90, 0x90, 0x90, 0x90, //0x000001bc .p2align 4, 0x90
	//0x000001c0 LBB0_4
	0x4d, 0x8b, 0x45, 0x00, //0x000001c0 movq         (%r13), %r8
	0x48, 0x8b, 0x75, 0x90, //0x000001c4 movq         $-112(%rbp), %rsi
	0x48, 0x89, 0xf1, //0x000001c8 movq         %rsi, %rcx
	0x48, 0x89, 0xf0, //0x000001cb movq         %rsi, %rax
	0x4d, 0x85, 0xc0, //0x000001ce testq        %r8, %r8
	0x0f, 0x85, 0x49, 0x00, 0x00, 0x00, //0x000001d1 jne          LBB0_6
	0xe9, 0x6b, 0x38, 0x00, 0x00, //0x000001d7 jmp          LBB0_638
	//0x000001dc LBB0_1
	0x49, 0xf7, 0xdb, //0x000001dc negq         %r11
	0x4d, 0x89, 0xdd, //0x000001df movq         %r11, %r13
	//0x000001e2 LBB0_2
	0x4d, 0x85, 0xed, //0x000001e2 testq        %r13, %r13
	0x0f, 0x88, 0x47, 0x38, 0x00, 0x00, //0x000001e5 js           LBB0_612
	//0x000001eb LBB0_3
	0x49, 0x01, 0xc5, //0x000001eb addq         %rax, %r13
	0x4c, 0x8b, 0x75, 0xd0, //0x000001ee movq         $-48(%rbp), %r14
	0x4d, 0x89, 0x2e, //0x000001f2 movq         %r13, (%r14)
	0x48, 0x85, 0xc0, //0x000001f5 testq        %rax, %rax
	0x4c, 0x8b, 0x6d, 0xb8, //0x000001f8 movq         $-72(%rbp), %r13
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000001fc movabsq      $4294977024, %r11
	0x0f, 0x89, 0xb4, 0xff, 0xff, 0xff, //0x00000206 jns          LBB0_4
	0xe9, 0x36, 0x38, 0x00, 0x00, //0x0000020c jmp          LBB0_638
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000211 .p2align 4, 0x90
	//0x00000220 LBB0_6
	0x48, 0x8b, 0x45, 0xa8, //0x00000220 movq         $-88(%rbp), %rax
	0x4c, 0x8b, 0x20, //0x00000224 movq         (%rax), %r12
	0x48, 0x8b, 0x40, 0x08, //0x00000227 movq         $8(%rax), %rax
	0x49, 0x8b, 0x16, //0x0000022b movq         (%r14), %rdx
	0x48, 0x39, 0xc2, //0x0000022e cmpq         %rax, %rdx
	0x0f, 0x83, 0x39, 0x00, 0x00, 0x00, //0x00000231 jae          LBB0_11
	0x41, 0x8a, 0x1c, 0x14, //0x00000237 movb         (%r12,%rdx), %bl
	0x80, 0xfb, 0x0d, //0x0000023b cmpb         $13, %bl
	0x0f, 0x84, 0x2c, 0x00, 0x00, 0x00, //0x0000023e je           LBB0_11
	0x80, 0xfb, 0x20, //0x00000244 cmpb         $32, %bl
	0x0f, 0x84, 0x23, 0x00, 0x00, 0x00, //0x00000247 je           LBB0_11
	0x80, 0xc3, 0xf7, //0x0000024d addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00000250 cmpb         $1, %bl
	0x0f, 0x86, 0x17, 0x00, 0x00, 0x00, //0x00000253 jbe          LBB0_11
	0x48, 0x89, 0xd6, //0x00000259 movq         %rdx, %rsi
	0xe9, 0x07, 0x01, 0x00, 0x00, //0x0000025c jmp          LBB0_32
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000261 .p2align 4, 0x90
	//0x00000270 LBB0_11
	0x48, 0x8d, 0x72, 0x01, //0x00000270 leaq         $1(%rdx), %rsi
	0x48, 0x39, 0xc6, //0x00000274 cmpq         %rax, %rsi
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000277 jae          LBB0_15
	0x41, 0x8a, 0x1c, 0x34, //0x0000027d movb         (%r12,%rsi), %bl
	0x80, 0xfb, 0x0d, //0x00000281 cmpb         $13, %bl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00000284 je           LBB0_15
	0x80, 0xfb, 0x20, //0x0000028a cmpb         $32, %bl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000028d je           LBB0_15
	0x80, 0xc3, 0xf7, //0x00000293 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00000296 cmpb         $1, %bl
	0x0f, 0x87, 0xc9, 0x00, 0x00, 0x00, //0x00000299 ja           LBB0_32
	0x90, //0x0000029f .p2align 4, 0x90
	//0x000002a0 LBB0_15
	0x48, 0x8d, 0x72, 0x02, //0x000002a0 leaq         $2(%rdx), %rsi
	0x48, 0x39, 0xc6, //0x000002a4 cmpq         %rax, %rsi
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x000002a7 jae          LBB0_19
	0x41, 0x8a, 0x1c, 0x34, //0x000002ad movb         (%r12,%rsi), %bl
	0x80, 0xfb, 0x0d, //0x000002b1 cmpb         $13, %bl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x000002b4 je           LBB0_19
	0x80, 0xfb, 0x20, //0x000002ba cmpb         $32, %bl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x000002bd je           LBB0_19
	0x80, 0xc3, 0xf7, //0x000002c3 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x000002c6 cmpb         $1, %bl
	0x0f, 0x87, 0x99, 0x00, 0x00, 0x00, //0x000002c9 ja           LBB0_32
	0x90, //0x000002cf .p2align 4, 0x90
	//0x000002d0 LBB0_19
	0x48, 0x8d, 0x72, 0x03, //0x000002d0 leaq         $3(%rdx), %rsi
	0x48, 0x39, 0xc6, //0x000002d4 cmpq         %rax, %rsi
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x000002d7 jae          LBB0_23
	0x41, 0x8a, 0x1c, 0x34, //0x000002dd movb         (%r12,%rsi), %bl
	0x80, 0xfb, 0x0d, //0x000002e1 cmpb         $13, %bl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x000002e4 je           LBB0_23
	0x80, 0xfb, 0x20, //0x000002ea cmpb         $32, %bl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x000002ed je           LBB0_23
	0x80, 0xc3, 0xf7, //0x000002f3 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x000002f6 cmpb         $1, %bl
	0x0f, 0x87, 0x69, 0x00, 0x00, 0x00, //0x000002f9 ja           LBB0_32
	0x90, //0x000002ff .p2align 4, 0x90
	//0x00000300 LBB0_23
	0x48, 0x83, 0xc2, 0x04, //0x00000300 addq         $4, %rdx
	0x48, 0x39, 0xd0, //0x00000304 cmpq         %rdx, %rax
	0x0f, 0x86, 0xd0, 0x36, 0x00, 0x00, //0x00000307 jbe          LBB0_603
	0x48, 0x39, 0xd0, //0x0000030d cmpq         %rdx, %rax
	0x0f, 0x84, 0x3a, 0x00, 0x00, 0x00, //0x00000310 je           LBB0_29
	0x49, 0x8d, 0x34, 0x04, //0x00000316 leaq         (%r12,%rax), %rsi
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x0000031a .p2align 4, 0x90
	//0x00000320 LBB0_26
	0x41, 0x0f, 0xbe, 0x3c, 0x14, //0x00000320 movsbl       (%r12,%rdx), %edi
	0x83, 0xff, 0x20, //0x00000325 cmpl         $32, %edi
	0x0f, 0x87, 0x2e, 0x00, 0x00, 0x00, //0x00000328 ja           LBB0_31
	0x49, 0x0f, 0xa3, 0xfb, //0x0000032e btq          %rdi, %r11
	0x0f, 0x83, 0x24, 0x00, 0x00, 0x00, //0x00000332 jae          LBB0_31
	0x48, 0x83, 0xc2, 0x01, //0x00000338 addq         $1, %rdx
	0x48, 0x39, 0xd0, //0x0000033c cmpq         %rdx, %rax
	0x0f, 0x85, 0xdb, 0xff, 0xff, 0xff, //0x0000033f jne          LBB0_26
	0xe9, 0x0c, 0x00, 0x00, 0x00, //0x00000345 jmp          LBB0_30
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x0000034a .p2align 4, 0x90
	//0x00000350 LBB0_29
	0x4c, 0x01, 0xe2, //0x00000350 addq         %r12, %rdx
	0x48, 0x89, 0xd6, //0x00000353 movq         %rdx, %rsi
	//0x00000356 LBB0_30
	0x4c, 0x29, 0xe6, //0x00000356 subq         %r12, %rsi
	0x48, 0x89, 0xf2, //0x00000359 movq         %rsi, %rdx
	//0x0000035c LBB0_31
	0x48, 0x89, 0xd6, //0x0000035c movq         %rdx, %rsi
	0x48, 0x39, 0xc2, //0x0000035f cmpq         %rax, %rdx
	0x0f, 0x83, 0x78, 0x36, 0x00, 0x00, //0x00000362 jae          LBB0_604
	//0x00000368 LBB0_32
	0x48, 0x8d, 0x46, 0x01, //0x00000368 leaq         $1(%rsi), %rax
	0x49, 0x89, 0x06, //0x0000036c movq         %rax, (%r14)
	0x41, 0x0f, 0xbe, 0x3c, 0x34, //0x0000036f movsbl       (%r12,%rsi), %edi
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000374 movq         $-1, %rax
	0x85, 0xff, //0x0000037b testl        %edi, %edi
	0x0f, 0x84, 0xc4, 0x36, 0x00, 0x00, //0x0000037d je           LBB0_638
	0x4d, 0x8b, 0x4d, 0x00, //0x00000383 movq         (%r13), %r9
	0x4d, 0x8d, 0x41, 0xff, //0x00000387 leaq         $-1(%r9), %r8
	0x43, 0x8b, 0x5c, 0xcd, 0x00, //0x0000038b movl         (%r13,%r9,8), %ebx
	0x48, 0x83, 0xf9, 0xff, //0x00000390 cmpq         $-1, %rcx
	0x48, 0x0f, 0x45, 0xf1, //0x00000394 cmovneq      %rcx, %rsi
	0x83, 0xc3, 0xff, //0x00000398 addl         $-1, %ebx
	0x83, 0xfb, 0x05, //0x0000039b cmpl         $5, %ebx
	0x0f, 0x87, 0x82, 0x02, 0x00, 0x00, //0x0000039e ja           LBB0_78
	0x48, 0x8d, 0x15, 0x35, 0x39, 0x00, 0x00, //0x000003a4 leaq         $14645(%rip), %rdx  /* LJTI0_0+0(%rip) */
	0x48, 0x63, 0x0c, 0x9a, //0x000003ab movslq       (%rdx,%rbx,4), %rcx
	0x48, 0x01, 0xd1, //0x000003af addq         %rdx, %rcx
	0xff, 0xe1, //0x000003b2 jmpq         *%rcx
	//0x000003b4 LBB0_35
	0x83, 0xff, 0x2c, //0x000003b4 cmpl         $44, %edi
	0x0f, 0x84, 0xec, 0x04, 0x00, 0x00, //0x000003b7 je           LBB0_117
	0x83, 0xff, 0x5d, //0x000003bd cmpl         $93, %edi
	0x0f, 0x84, 0x48, 0x02, 0x00, 0x00, //0x000003c0 je           LBB0_37
	0xe9, 0x75, 0x36, 0x00, 0x00, //0x000003c6 jmp          LBB0_637
	//0x000003cb LBB0_38
	0x40, 0x80, 0xff, 0x5d, //0x000003cb cmpb         $93, %dil
	0x0f, 0x84, 0x39, 0x02, 0x00, 0x00, //0x000003cf je           LBB0_37
	0x48, 0x89, 0x75, 0x90, //0x000003d5 movq         %rsi, $-112(%rbp)
	0x4b, 0xc7, 0x44, 0xcd, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000003d9 movq         $1, (%r13,%r9,8)
	0x83, 0xff, 0x7b, //0x000003e2 cmpl         $123, %edi
	0x0f, 0x86, 0x4c, 0x02, 0x00, 0x00, //0x000003e5 jbe          LBB0_79
	0xe9, 0x50, 0x36, 0x00, 0x00, //0x000003eb jmp          LBB0_637
	//0x000003f0 LBB0_40
	0x40, 0x80, 0xff, 0x22, //0x000003f0 cmpb         $34, %dil
	0x0f, 0x85, 0x46, 0x36, 0x00, 0x00, //0x000003f4 jne          LBB0_637
	0x4b, 0xc7, 0x44, 0xcd, 0x00, 0x04, 0x00, 0x00, 0x00, //0x000003fa movq         $4, (%r13,%r9,8)
	0x48, 0x8b, 0x4d, 0x98, //0x00000403 movq         $-104(%rbp), %rcx
	0xf6, 0xc1, 0x40, //0x00000407 testb        $64, %cl
	0x48, 0x89, 0x75, 0x90, //0x0000040a movq         %rsi, $-112(%rbp)
	0x0f, 0x85, 0x7c, 0x06, 0x00, 0x00, //0x0000040e jne          LBB0_125
	0x49, 0x8b, 0x16, //0x00000414 movq         (%r14), %rdx
	0x48, 0x8b, 0x45, 0xa8, //0x00000417 movq         $-88(%rbp), %rax
	0x48, 0x8b, 0x40, 0x08, //0x0000041b movq         $8(%rax), %rax
	0xf6, 0xc1, 0x20, //0x0000041f testb        $32, %cl
	0x48, 0x89, 0x45, 0xa0, //0x00000422 movq         %rax, $-96(%rbp)
	0x48, 0x89, 0x55, 0xb0, //0x00000426 movq         %rdx, $-80(%rbp)
	0x0f, 0x85, 0x5e, 0x09, 0x00, 0x00, //0x0000042a jne          LBB0_157
	0x49, 0x89, 0xc1, //0x00000430 movq         %rax, %r9
	0x49, 0x29, 0xd1, //0x00000433 subq         %rdx, %r9
	0x0f, 0x84, 0xc2, 0x37, 0x00, 0x00, //0x00000436 je           LBB0_642
	0x49, 0x83, 0xf9, 0x40, //0x0000043c cmpq         $64, %r9
	0x0f, 0x82, 0x4a, 0x2a, 0x00, 0x00, //0x00000440 jb           LBB0_484
	0x48, 0x8b, 0x45, 0xb0, //0x00000446 movq         $-80(%rbp), %rax
	0x49, 0x89, 0xc6, //0x0000044a movq         %rax, %r14
	0x49, 0xf7, 0xd6, //0x0000044d notq         %r14
	0x48, 0xc7, 0x45, 0xc8, 0xff, 0xff, 0xff, 0xff, //0x00000450 movq         $-1, $-56(%rbp)
	0x45, 0x31, 0xc0, //0x00000458 xorl         %r8d, %r8d
	0x90, 0x90, 0x90, 0x90, 0x90, //0x0000045b .p2align 4, 0x90
	//0x00000460 LBB0_46
	0xf3, 0x41, 0x0f, 0x6f, 0x1c, 0x04, //0x00000460 movdqu       (%r12,%rax), %xmm3
	0xf3, 0x41, 0x0f, 0x6f, 0x64, 0x04, 0x10, //0x00000466 movdqu       $16(%r12,%rax), %xmm4
	0xf3, 0x41, 0x0f, 0x6f, 0x6c, 0x04, 0x20, //0x0000046d movdqu       $32(%r12,%rax), %xmm5
	0xf3, 0x41, 0x0f, 0x6f, 0x74, 0x04, 0x30, //0x00000474 movdqu       $48(%r12,%rax), %xmm6
	0x66, 0x0f, 0x6f, 0xfb, //0x0000047b movdqa       %xmm3, %xmm7
	0x66, 0x0f, 0x74, 0xf8, //0x0000047f pcmpeqb      %xmm0, %xmm7
	0x66, 0x44, 0x0f, 0xd7, 0xd7, //0x00000483 pmovmskb     %xmm7, %r10d
	0x66, 0x0f, 0x6f, 0xfc, //0x00000488 movdqa       %xmm4, %xmm7
	0x66, 0x0f, 0x74, 0xf8, //0x0000048c pcmpeqb      %xmm0, %xmm7
	0x66, 0x0f, 0xd7, 0xdf, //0x00000490 pmovmskb     %xmm7, %ebx
	0x66, 0x0f, 0x6f, 0xfd, //0x00000494 movdqa       %xmm5, %xmm7
	0x66, 0x0f, 0x74, 0xf8, //0x00000498 pcmpeqb      %xmm0, %xmm7
	0x66, 0x0f, 0xd7, 0xff, //0x0000049c pmovmskb     %xmm7, %edi
	0x66, 0x0f, 0x6f, 0xfe, //0x000004a0 movdqa       %xmm6, %xmm7
	0x66, 0x0f, 0x74, 0xf8, //0x000004a4 pcmpeqb      %xmm0, %xmm7
	0x66, 0x0f, 0xd7, 0xd7, //0x000004a8 pmovmskb     %xmm7, %edx
	0x66, 0x0f, 0x74, 0xd9, //0x000004ac pcmpeqb      %xmm1, %xmm3
	0x66, 0x44, 0x0f, 0xd7, 0xdb, //0x000004b0 pmovmskb     %xmm3, %r11d
	0x66, 0x0f, 0x74, 0xe1, //0x000004b5 pcmpeqb      %xmm1, %xmm4
	0x66, 0x0f, 0xd7, 0xcc, //0x000004b9 pmovmskb     %xmm4, %ecx
	0x66, 0x0f, 0x74, 0xe9, //0x000004bd pcmpeqb      %xmm1, %xmm5
	0x66, 0x0f, 0xd7, 0xf5, //0x000004c1 pmovmskb     %xmm5, %esi
	0x66, 0x0f, 0x74, 0xf1, //0x000004c5 pcmpeqb      %xmm1, %xmm6
	0x66, 0x44, 0x0f, 0xd7, 0xfe, //0x000004c9 pmovmskb     %xmm6, %r15d
	0x48, 0xc1, 0xe2, 0x30, //0x000004ce shlq         $48, %rdx
	0x48, 0xc1, 0xe7, 0x20, //0x000004d2 shlq         $32, %rdi
	0x48, 0x09, 0xd7, //0x000004d6 orq          %rdx, %rdi
	0x48, 0xc1, 0xe3, 0x10, //0x000004d9 shlq         $16, %rbx
	0x48, 0x09, 0xfb, //0x000004dd orq          %rdi, %rbx
	0x49, 0x09, 0xda, //0x000004e0 orq          %rbx, %r10
	0x49, 0xc1, 0xe7, 0x30, //0x000004e3 shlq         $48, %r15
	0x48, 0xc1, 0xe6, 0x20, //0x000004e7 shlq         $32, %rsi
	0x4c, 0x09, 0xfe, //0x000004eb orq          %r15, %rsi
	0x48, 0xc1, 0xe1, 0x10, //0x000004ee shlq         $16, %rcx
	0x48, 0x09, 0xf1, //0x000004f2 orq          %rsi, %rcx
	0x49, 0x09, 0xcb, //0x000004f5 orq          %rcx, %r11
	0x0f, 0x85, 0x30, 0x00, 0x00, 0x00, //0x000004f8 jne          LBB0_55
	0x4d, 0x85, 0xc0, //0x000004fe testq        %r8, %r8
	0x0f, 0x85, 0x3d, 0x00, 0x00, 0x00, //0x00000501 jne          LBB0_57
	0x45, 0x31, 0xc0, //0x00000507 xorl         %r8d, %r8d
	0x4d, 0x85, 0xd2, //0x0000050a testq        %r10, %r10
	0x0f, 0x85, 0x83, 0x00, 0x00, 0x00, //0x0000050d jne          LBB0_58
	//0x00000513 LBB0_49
	0x49, 0x83, 0xc1, 0xc0, //0x00000513 addq         $-64, %r9
	0x49, 0x83, 0xc6, 0xc0, //0x00000517 addq         $-64, %r14
	0x48, 0x83, 0xc0, 0x40, //0x0000051b addq         $64, %rax
	0x49, 0x83, 0xf9, 0x3f, //0x0000051f cmpq         $63, %r9
	0x0f, 0x87, 0x37, 0xff, 0xff, 0xff, //0x00000523 ja           LBB0_46
	0xe9, 0xce, 0x21, 0x00, 0x00, //0x00000529 jmp          LBB0_50
	//0x0000052e LBB0_55
	0x48, 0x83, 0x7d, 0xc8, 0xff, //0x0000052e cmpq         $-1, $-56(%rbp)
	0x0f, 0x85, 0x0b, 0x00, 0x00, 0x00, //0x00000533 jne          LBB0_57
	0x49, 0x0f, 0xbc, 0xcb, //0x00000539 bsfq         %r11, %rcx
	0x48, 0x01, 0xc1, //0x0000053d addq         %rax, %rcx
	0x48, 0x89, 0x4d, 0xc8, //0x00000540 movq         %rcx, $-56(%rbp)
	//0x00000544 LBB0_57
	0x4c, 0x89, 0xc1, //0x00000544 movq         %r8, %rcx
	0x48, 0xf7, 0xd1, //0x00000547 notq         %rcx
	0x4c, 0x21, 0xd9, //0x0000054a andq         %r11, %rcx
	0x48, 0x8d, 0x14, 0x09, //0x0000054d leaq         (%rcx,%rcx), %rdx
	0x4c, 0x09, 0xc2, //0x00000551 orq          %r8, %rdx
	0x48, 0x89, 0xd6, //0x00000554 movq         %rdx, %rsi
	0x48, 0xf7, 0xd6, //0x00000557 notq         %rsi
	0x4c, 0x21, 0xde, //0x0000055a andq         %r11, %rsi
	0x48, 0xbf, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000055d movabsq      $-6148914691236517206, %rdi
	0x48, 0x21, 0xfe, //0x00000567 andq         %rdi, %rsi
	0x45, 0x31, 0xc0, //0x0000056a xorl         %r8d, %r8d
	0x48, 0x01, 0xce, //0x0000056d addq         %rcx, %rsi
	0x41, 0x0f, 0x92, 0xc0, //0x00000570 setb         %r8b
	0x48, 0x01, 0xf6, //0x00000574 addq         %rsi, %rsi
	0x48, 0xb9, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000577 movabsq      $6148914691236517205, %rcx
	0x48, 0x31, 0xce, //0x00000581 xorq         %rcx, %rsi
	0x48, 0x21, 0xd6, //0x00000584 andq         %rdx, %rsi
	0x48, 0xf7, 0xd6, //0x00000587 notq         %rsi
	0x49, 0x21, 0xf2, //0x0000058a andq         %rsi, %r10
	0x4d, 0x85, 0xd2, //0x0000058d testq        %r10, %r10
	0x0f, 0x84, 0x7d, 0xff, 0xff, 0xff, //0x00000590 je           LBB0_49
	//0x00000596 LBB0_58
	0x49, 0x0f, 0xbc, 0xc2, //0x00000596 bsfq         %r10, %rax
	0x4c, 0x29, 0xf0, //0x0000059a subq         %r14, %rax
	0x4c, 0x8b, 0x75, 0xd0, //0x0000059d movq         $-48(%rbp), %r14
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000005a1 movabsq      $4294977024, %r11
	0xe9, 0xd6, 0x0c, 0x00, 0x00, //0x000005ab jmp          LBB0_223
	//0x000005b0 LBB0_59
	0x40, 0x80, 0xff, 0x3a, //0x000005b0 cmpb         $58, %dil
	0x0f, 0x85, 0x86, 0x34, 0x00, 0x00, //0x000005b4 jne          LBB0_637
	0x48, 0x89, 0x75, 0x90, //0x000005ba movq         %rsi, $-112(%rbp)
	0x4b, 0xc7, 0x44, 0xcd, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000005be movq         $0, (%r13,%r9,8)
	0xe9, 0xf4, 0xfb, 0xff, 0xff, //0x000005c7 jmp          LBB0_4
	//0x000005cc LBB0_61
	0x83, 0xff, 0x2c, //0x000005cc cmpl         $44, %edi
	0x0f, 0x85, 0x30, 0x00, 0x00, 0x00, //0x000005cf jne          LBB0_62
	0x49, 0x81, 0xf9, 0xff, 0x0f, 0x00, 0x00, //0x000005d5 cmpq         $4095, %r9
	0x0f, 0x8f, 0x0a, 0x34, 0x00, 0x00, //0x000005dc jg           LBB0_634
	0x48, 0x89, 0x75, 0x90, //0x000005e2 movq         %rsi, $-112(%rbp)
	0x49, 0x8d, 0x41, 0x01, //0x000005e6 leaq         $1(%r9), %rax
	0x49, 0x89, 0x45, 0x00, //0x000005ea movq         %rax, (%r13)
	0x4b, 0xc7, 0x44, 0xcd, 0x08, 0x03, 0x00, 0x00, 0x00, //0x000005ee movq         $3, $8(%r13,%r9,8)
	0xe9, 0xc4, 0xfb, 0xff, 0xff, //0x000005f7 jmp          LBB0_4
	//0x000005fc LBB0_63
	0x83, 0xff, 0x22, //0x000005fc cmpl         $34, %edi
	0x0f, 0x84, 0xcb, 0x02, 0x00, 0x00, //0x000005ff je           LBB0_64
	//0x00000605 LBB0_62
	0x83, 0xff, 0x7d, //0x00000605 cmpl         $125, %edi
	0x0f, 0x85, 0x32, 0x34, 0x00, 0x00, //0x00000608 jne          LBB0_637
	//0x0000060e LBB0_37
	0x4d, 0x89, 0x45, 0x00, //0x0000060e movq         %r8, (%r13)
	0x48, 0x89, 0xf1, //0x00000612 movq         %rsi, %rcx
	0x48, 0x89, 0xf0, //0x00000615 movq         %rsi, %rax
	0x4d, 0x85, 0xc0, //0x00000618 testq        %r8, %r8
	0x0f, 0x85, 0xff, 0xfb, 0xff, 0xff, //0x0000061b jne          LBB0_6
	0xe9, 0x21, 0x34, 0x00, 0x00, //0x00000621 jmp          LBB0_638
	//0x00000626 LBB0_78
	0x48, 0x89, 0x75, 0x90, //0x00000626 movq         %rsi, $-112(%rbp)
	0x4d, 0x89, 0x45, 0x00, //0x0000062a movq         %r8, (%r13)
	0x83, 0xff, 0x7b, //0x0000062e cmpl         $123, %edi
	0x0f, 0x87, 0x09, 0x34, 0x00, 0x00, //0x00000631 ja           LBB0_637
	//0x00000637 LBB0_79
	0x89, 0xf9, //0x00000637 movl         %edi, %ecx
	0x48, 0x8d, 0x15, 0xb8, 0x36, 0x00, 0x00, //0x00000639 leaq         $14008(%rip), %rdx  /* LJTI0_1+0(%rip) */
	0x48, 0x63, 0x0c, 0x8a, //0x00000640 movslq       (%rdx,%rcx,4), %rcx
	0x48, 0x01, 0xd1, //0x00000644 addq         %rdx, %rcx
	0xff, 0xe1, //0x00000647 jmpq         *%rcx
	//0x00000649 LBB0_80
	0x48, 0x8b, 0x45, 0xa8, //0x00000649 movq         $-88(%rbp), %rax
	0x48, 0x8b, 0x78, 0x08, //0x0000064d movq         $8(%rax), %rdi
	0x49, 0x8b, 0x36, //0x00000651 movq         (%r14), %rsi
	0xf6, 0x45, 0x98, 0x40, //0x00000654 testb        $64, $-104(%rbp)
	0x0f, 0x85, 0x40, 0x05, 0x00, 0x00, //0x00000658 jne          LBB0_135
	0x48, 0x8d, 0x46, 0xff, //0x0000065e leaq         $-1(%rsi), %rax
	0x48, 0x29, 0xc7, //0x00000662 subq         %rax, %rdi
	0x0f, 0x84, 0xc0, 0x33, 0x00, 0x00, //0x00000665 je           LBB0_611
	0x4d, 0x8d, 0x34, 0x34, //0x0000066b leaq         (%r12,%rsi), %r14
	0x49, 0x83, 0xc6, 0xff, //0x0000066f addq         $-1, %r14
	0x41, 0x80, 0x3e, 0x30, //0x00000673 cmpb         $48, (%r14)
	0x0f, 0x85, 0x37, 0x00, 0x00, 0x00, //0x00000677 jne          LBB0_86
	0x41, 0xbd, 0x01, 0x00, 0x00, 0x00, //0x0000067d movl         $1, %r13d
	0x48, 0x83, 0xff, 0x01, //0x00000683 cmpq         $1, %rdi
	0x0f, 0x84, 0x5e, 0xfb, 0xff, 0xff, //0x00000687 je           LBB0_3
	0x41, 0x8a, 0x0c, 0x34, //0x0000068d movb         (%r12,%rsi), %cl
	0x80, 0xc1, 0xd2, //0x00000691 addb         $-46, %cl
	0x80, 0xf9, 0x37, //0x00000694 cmpb         $55, %cl
	0x0f, 0x87, 0x4e, 0xfb, 0xff, 0xff, //0x00000697 ja           LBB0_3
	0x0f, 0xb6, 0xc9, //0x0000069d movzbl       %cl, %ecx
	0x48, 0xba, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x000006a0 movabsq      $36028797027352577, %rdx
	0x48, 0x0f, 0xa3, 0xca, //0x000006aa btq          %rcx, %rdx
	0x0f, 0x83, 0x37, 0xfb, 0xff, 0xff, //0x000006ae jae          LBB0_3
	//0x000006b4 LBB0_86
	0x48, 0x89, 0x75, 0xc0, //0x000006b4 movq         %rsi, $-64(%rbp)
	0x48, 0x83, 0xff, 0x10, //0x000006b8 cmpq         $16, %rdi
	0x0f, 0x82, 0x14, 0x27, 0x00, 0x00, //0x000006bc jb           LBB0_472
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x000006c2 movq         $-1, %r9
	0x45, 0x31, 0xed, //0x000006c9 xorl         %r13d, %r13d
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x000006cc movq         $-1, %r15
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000006d3 movq         $-1, %r8
	0x49, 0x89, 0xfa, //0x000006da movq         %rdi, %r10
	0x90, 0x90, 0x90, //0x000006dd .p2align 4, 0x90
	//0x000006e0 LBB0_88
	0xf3, 0x43, 0x0f, 0x6f, 0x1c, 0x2e, //0x000006e0 movdqu       (%r14,%r13), %xmm3
	0x66, 0x0f, 0x6f, 0xe3, //0x000006e6 movdqa       %xmm3, %xmm4
	0x66, 0x0f, 0x64, 0x25, 0xae, 0xf9, 0xff, 0xff, //0x000006ea pcmpgtb      $-1618(%rip), %xmm4  /* LCPI0_10+0(%rip) */
	0x66, 0x41, 0x0f, 0x6f, 0xef, //0x000006f2 movdqa       %xmm15, %xmm5
	0x66, 0x0f, 0x64, 0xeb, //0x000006f7 pcmpgtb      %xmm3, %xmm5
	0x66, 0x0f, 0xdb, 0xec, //0x000006fb pand         %xmm4, %xmm5
	0x66, 0x0f, 0x6f, 0xe3, //0x000006ff movdqa       %xmm3, %xmm4
	0x66, 0x0f, 0x74, 0x25, 0xb5, 0xf9, 0xff, 0xff, //0x00000703 pcmpeqb      $-1611(%rip), %xmm4  /* LCPI0_12+0(%rip) */
	0x66, 0x0f, 0x6f, 0xf3, //0x0000070b movdqa       %xmm3, %xmm6
	0x66, 0x0f, 0x74, 0x35, 0xb9, 0xf9, 0xff, 0xff, //0x0000070f pcmpeqb      $-1607(%rip), %xmm6  /* LCPI0_13+0(%rip) */
	0x66, 0x0f, 0xeb, 0xf4, //0x00000717 por          %xmm4, %xmm6
	0x66, 0x0f, 0x6f, 0xe3, //0x0000071b movdqa       %xmm3, %xmm4
	0x66, 0x41, 0x0f, 0xdb, 0xe1, //0x0000071f pand         %xmm9, %xmm4
	0x66, 0x0f, 0x74, 0x1d, 0xb4, 0xf9, 0xff, 0xff, //0x00000724 pcmpeqb      $-1612(%rip), %xmm3  /* LCPI0_14+0(%rip) */
	0x66, 0x41, 0x0f, 0x74, 0xe3, //0x0000072c pcmpeqb      %xmm11, %xmm4
	0x66, 0x0f, 0xd7, 0xd4, //0x00000731 pmovmskb     %xmm4, %edx
	0x66, 0x0f, 0xeb, 0xe3, //0x00000735 por          %xmm3, %xmm4
	0x66, 0x0f, 0xeb, 0xee, //0x00000739 por          %xmm6, %xmm5
	0x66, 0x0f, 0xeb, 0xec, //0x0000073d por          %xmm4, %xmm5
	0x66, 0x44, 0x0f, 0xd7, 0xdb, //0x00000741 pmovmskb     %xmm3, %r11d
	0x66, 0x0f, 0xd7, 0xf6, //0x00000746 pmovmskb     %xmm6, %esi
	0x66, 0x0f, 0xd7, 0xcd, //0x0000074a pmovmskb     %xmm5, %ecx
	0xf7, 0xd1, //0x0000074e notl         %ecx
	0x0f, 0xbc, 0xc9, //0x00000750 bsfl         %ecx, %ecx
	0x83, 0xf9, 0x10, //0x00000753 cmpl         $16, %ecx
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00000756 je           LBB0_90
	0xbb, 0xff, 0xff, 0xff, 0xff, //0x0000075c movl         $-1, %ebx
	0xd3, 0xe3, //0x00000761 shll         %cl, %ebx
	0xf7, 0xd3, //0x00000763 notl         %ebx
	0x41, 0x21, 0xdb, //0x00000765 andl         %ebx, %r11d
	0x21, 0xda, //0x00000768 andl         %ebx, %edx
	0x21, 0xf3, //0x0000076a andl         %esi, %ebx
	0x89, 0xde, //0x0000076c movl         %ebx, %esi
	//0x0000076e LBB0_90
	0x41, 0x8d, 0x5b, 0xff, //0x0000076e leal         $-1(%r11), %ebx
	0x44, 0x21, 0xdb, //0x00000772 andl         %r11d, %ebx
	0x0f, 0x85, 0x12, 0x1f, 0x00, 0x00, //0x00000775 jne          LBB0_429
	0x8d, 0x5a, 0xff, //0x0000077b leal         $-1(%rdx), %ebx
	0x21, 0xd3, //0x0000077e andl         %edx, %ebx
	0x0f, 0x85, 0x07, 0x1f, 0x00, 0x00, //0x00000780 jne          LBB0_429
	0x8d, 0x5e, 0xff, //0x00000786 leal         $-1(%rsi), %ebx
	0x21, 0xf3, //0x00000789 andl         %esi, %ebx
	0x0f, 0x85, 0xfc, 0x1e, 0x00, 0x00, //0x0000078b jne          LBB0_429
	0x45, 0x85, 0xdb, //0x00000791 testl        %r11d, %r11d
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x00000794 je           LBB0_96
	0x41, 0x0f, 0xbc, 0xdb, //0x0000079a bsfl         %r11d, %ebx
	0x49, 0x83, 0xf8, 0xff, //0x0000079e cmpq         $-1, %r8
	0x0f, 0x85, 0x75, 0x22, 0x00, 0x00, //0x000007a2 jne          LBB0_436
	0x4c, 0x01, 0xeb, //0x000007a8 addq         %r13, %rbx
	0x49, 0x89, 0xd8, //0x000007ab movq         %rbx, %r8
	//0x000007ae LBB0_96
	0x85, 0xd2, //0x000007ae testl        %edx, %edx
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x000007b0 je           LBB0_99
	0x0f, 0xbc, 0xd2, //0x000007b6 bsfl         %edx, %edx
	0x49, 0x83, 0xff, 0xff, //0x000007b9 cmpq         $-1, %r15
	0x0f, 0x85, 0xd1, 0x20, 0x00, 0x00, //0x000007bd jne          LBB0_435
	0x4c, 0x01, 0xea, //0x000007c3 addq         %r13, %rdx
	0x49, 0x89, 0xd7, //0x000007c6 movq         %rdx, %r15
	//0x000007c9 LBB0_99
	0x85, 0xf6, //0x000007c9 testl        %esi, %esi
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x000007cb je           LBB0_102
	0x0f, 0xbc, 0xd6, //0x000007d1 bsfl         %esi, %edx
	0x49, 0x83, 0xf9, 0xff, //0x000007d4 cmpq         $-1, %r9
	0x0f, 0x85, 0xb6, 0x20, 0x00, 0x00, //0x000007d8 jne          LBB0_435
	0x4c, 0x01, 0xea, //0x000007de addq         %r13, %rdx
	0x49, 0x89, 0xd1, //0x000007e1 movq         %rdx, %r9
	//0x000007e4 LBB0_102
	0x83, 0xf9, 0x10, //0x000007e4 cmpl         $16, %ecx
	0x0f, 0x85, 0xbb, 0x07, 0x00, 0x00, //0x000007e7 jne          LBB0_183
	0x49, 0x83, 0xc2, 0xf0, //0x000007ed addq         $-16, %r10
	0x49, 0x83, 0xc5, 0x10, //0x000007f1 addq         $16, %r13
	0x49, 0x83, 0xfa, 0x0f, //0x000007f5 cmpq         $15, %r10
	0x0f, 0x87, 0xe1, 0xfe, 0xff, 0xff, //0x000007f9 ja           LBB0_88
	0x4b, 0x8d, 0x0c, 0x2e, //0x000007ff leaq         (%r14,%r13), %rcx
	0x49, 0x89, 0xcb, //0x00000803 movq         %rcx, %r11
	0x4c, 0x39, 0xef, //0x00000806 cmpq         %r13, %rdi
	0x0f, 0x84, 0xa2, 0x07, 0x00, 0x00, //0x00000809 je           LBB0_184
	//0x0000080f LBB0_105
	0x4e, 0x8d, 0x1c, 0x11, //0x0000080f leaq         (%rcx,%r10), %r11
	0x48, 0x89, 0xca, //0x00000813 movq         %rcx, %rdx
	0x48, 0x2b, 0x55, 0xc0, //0x00000816 subq         $-64(%rbp), %rdx
	0x4c, 0x29, 0xe2, //0x0000081a subq         %r12, %rdx
	0x48, 0x83, 0xc2, 0x01, //0x0000081d addq         $1, %rdx
	0x31, 0xff, //0x00000821 xorl         %edi, %edi
	0x4c, 0x8d, 0x2d, 0x2a, 0x37, 0x00, 0x00, //0x00000823 leaq         $14122(%rip), %r13  /* LJTI0_3+0(%rip) */
	0xe9, 0x2e, 0x00, 0x00, 0x00, //0x0000082a jmp          LBB0_110
	//0x0000082f LBB0_106
	0x83, 0xfe, 0x65, //0x0000082f cmpl         $101, %esi
	0x0f, 0x85, 0xbc, 0x09, 0x00, 0x00, //0x00000832 jne          LBB0_212
	//0x00000838 LBB0_107
	0x49, 0x83, 0xff, 0xff, //0x00000838 cmpq         $-1, %r15
	0x0f, 0x85, 0x76, 0x1e, 0x00, 0x00, //0x0000083c jne          LBB0_433
	0x4c, 0x8d, 0x3c, 0x3a, //0x00000842 leaq         (%rdx,%rdi), %r15
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000846 .p2align 4, 0x90
	//0x00000850 LBB0_109
	0x48, 0x83, 0xc7, 0x01, //0x00000850 addq         $1, %rdi
	0x49, 0x39, 0xfa, //0x00000854 cmpq         %rdi, %r10
	0x0f, 0x84, 0x54, 0x07, 0x00, 0x00, //0x00000857 je           LBB0_184
	//0x0000085d LBB0_110
	0x0f, 0xbe, 0x34, 0x39, //0x0000085d movsbl       (%rcx,%rdi), %esi
	0x8d, 0x5e, 0xd0, //0x00000861 leal         $-48(%rsi), %ebx
	0x83, 0xfb, 0x0a, //0x00000864 cmpl         $10, %ebx
	0x0f, 0x82, 0xe3, 0xff, 0xff, 0xff, //0x00000867 jb           LBB0_109
	0x8d, 0x5e, 0xd5, //0x0000086d leal         $-43(%rsi), %ebx
	0x83, 0xfb, 0x1a, //0x00000870 cmpl         $26, %ebx
	0x0f, 0x87, 0xb6, 0xff, 0xff, 0xff, //0x00000873 ja           LBB0_106
	0x49, 0x63, 0x74, 0x9d, 0x00, //0x00000879 movslq       (%r13,%rbx,4), %rsi
	0x4c, 0x01, 0xee, //0x0000087e addq         %r13, %rsi
	0xff, 0xe6, //0x00000881 jmpq         *%rsi
	//0x00000883 LBB0_113
	0x49, 0x83, 0xf9, 0xff, //0x00000883 cmpq         $-1, %r9
	0x0f, 0x85, 0x2b, 0x1e, 0x00, 0x00, //0x00000887 jne          LBB0_433
	0x4c, 0x8d, 0x0c, 0x3a, //0x0000088d leaq         (%rdx,%rdi), %r9
	0xe9, 0xba, 0xff, 0xff, 0xff, //0x00000891 jmp          LBB0_109
	//0x00000896 LBB0_115
	0x49, 0x83, 0xf8, 0xff, //0x00000896 cmpq         $-1, %r8
	0x0f, 0x85, 0x18, 0x1e, 0x00, 0x00, //0x0000089a jne          LBB0_433
	0x4c, 0x8d, 0x04, 0x3a, //0x000008a0 leaq         (%rdx,%rdi), %r8
	0xe9, 0xa7, 0xff, 0xff, 0xff, //0x000008a4 jmp          LBB0_109
	//0x000008a9 LBB0_117
	0x49, 0x81, 0xf9, 0xff, 0x0f, 0x00, 0x00, //0x000008a9 cmpq         $4095, %r9
	0x0f, 0x8f, 0x36, 0x31, 0x00, 0x00, //0x000008b0 jg           LBB0_634
	0x48, 0x89, 0x75, 0x90, //0x000008b6 movq         %rsi, $-112(%rbp)
	0x49, 0x8d, 0x41, 0x01, //0x000008ba leaq         $1(%r9), %rax
	0x49, 0x89, 0x45, 0x00, //0x000008be movq         %rax, (%r13)
	0x4b, 0xc7, 0x44, 0xcd, 0x08, 0x00, 0x00, 0x00, 0x00, //0x000008c2 movq         $0, $8(%r13,%r9,8)
	0xe9, 0xf0, 0xf8, 0xff, 0xff, //0x000008cb jmp          LBB0_4
	//0x000008d0 LBB0_64
	0x4b, 0xc7, 0x44, 0xcd, 0x00, 0x02, 0x00, 0x00, 0x00, //0x000008d0 movq         $2, (%r13,%r9,8)
	0x48, 0x8b, 0x4d, 0x98, //0x000008d9 movq         $-104(%rbp), %rcx
	0xf6, 0xc1, 0x40, //0x000008dd testb        $64, %cl
	0x48, 0x89, 0x75, 0x90, //0x000008e0 movq         %rsi, $-112(%rbp)
	0x0f, 0x85, 0x9a, 0x03, 0x00, 0x00, //0x000008e4 jne          LBB0_147
	0x49, 0x8b, 0x16, //0x000008ea movq         (%r14), %rdx
	0x48, 0x8b, 0x45, 0xa8, //0x000008ed movq         $-88(%rbp), %rax
	0x48, 0x8b, 0x40, 0x08, //0x000008f1 movq         $8(%rax), %rax
	0xf6, 0xc1, 0x20, //0x000008f5 testb        $32, %cl
	0x48, 0x89, 0xd1, //0x000008f8 movq         %rdx, %rcx
	0x48, 0x89, 0x55, 0xb0, //0x000008fb movq         %rdx, $-80(%rbp)
	0x48, 0x89, 0x45, 0xa0, //0x000008ff movq         %rax, $-96(%rbp)
	0x49, 0x89, 0xc1, //0x00000903 movq         %rax, %r9
	0x0f, 0x85, 0xba, 0x06, 0x00, 0x00, //0x00000906 jne          LBB0_186
	0x49, 0x29, 0xd1, //0x0000090c subq         %rdx, %r9
	0x0f, 0x84, 0x23, 0x33, 0x00, 0x00, //0x0000090f je           LBB0_646
	0x49, 0x83, 0xf9, 0x40, //0x00000915 cmpq         $64, %r9
	0x0f, 0x82, 0x30, 0x26, 0x00, 0x00, //0x00000919 jb           LBB0_492
	0x48, 0x8b, 0x45, 0xb0, //0x0000091f movq         $-80(%rbp), %rax
	0x49, 0x89, 0xc6, //0x00000923 movq         %rax, %r14
	0x49, 0xf7, 0xd6, //0x00000926 notq         %r14
	0x48, 0xc7, 0x45, 0xc8, 0xff, 0xff, 0xff, 0xff, //0x00000929 movq         $-1, $-56(%rbp)
	0x45, 0x31, 0xc0, //0x00000931 xorl         %r8d, %r8d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000934 .p2align 4, 0x90
	//0x00000940 LBB0_69
	0xf3, 0x41, 0x0f, 0x6f, 0x1c, 0x04, //0x00000940 movdqu       (%r12,%rax), %xmm3
	0xf3, 0x41, 0x0f, 0x6f, 0x64, 0x04, 0x10, //0x00000946 movdqu       $16(%r12,%rax), %xmm4
	0xf3, 0x41, 0x0f, 0x6f, 0x6c, 0x04, 0x20, //0x0000094d movdqu       $32(%r12,%rax), %xmm5
	0xf3, 0x41, 0x0f, 0x6f, 0x74, 0x04, 0x30, //0x00000954 movdqu       $48(%r12,%rax), %xmm6
	0x66, 0x0f, 0x6f, 0xfb, //0x0000095b movdqa       %xmm3, %xmm7
	0x66, 0x0f, 0x74, 0xf8, //0x0000095f pcmpeqb      %xmm0, %xmm7
	0x66, 0x44, 0x0f, 0xd7, 0xd7, //0x00000963 pmovmskb     %xmm7, %r10d
	0x66, 0x0f, 0x6f, 0xfc, //0x00000968 movdqa       %xmm4, %xmm7
	0x66, 0x0f, 0x74, 0xf8, //0x0000096c pcmpeqb      %xmm0, %xmm7
	0x66, 0x0f, 0xd7, 0xdf, //0x00000970 pmovmskb     %xmm7, %ebx
	0x66, 0x0f, 0x6f, 0xfd, //0x00000974 movdqa       %xmm5, %xmm7
	0x66, 0x0f, 0x74, 0xf8, //0x00000978 pcmpeqb      %xmm0, %xmm7
	0x66, 0x0f, 0xd7, 0xff, //0x0000097c pmovmskb     %xmm7, %edi
	0x66, 0x0f, 0x6f, 0xfe, //0x00000980 movdqa       %xmm6, %xmm7
	0x66, 0x0f, 0x74, 0xf8, //0x00000984 pcmpeqb      %xmm0, %xmm7
	0x66, 0x0f, 0xd7, 0xd7, //0x00000988 pmovmskb     %xmm7, %edx
	0x66, 0x0f, 0x74, 0xd9, //0x0000098c pcmpeqb      %xmm1, %xmm3
	0x66, 0x44, 0x0f, 0xd7, 0xdb, //0x00000990 pmovmskb     %xmm3, %r11d
	0x66, 0x0f, 0x74, 0xe1, //0x00000995 pcmpeqb      %xmm1, %xmm4
	0x66, 0x0f, 0xd7, 0xcc, //0x00000999 pmovmskb     %xmm4, %ecx
	0x66, 0x0f, 0x74, 0xe9, //0x0000099d pcmpeqb      %xmm1, %xmm5
	0x66, 0x0f, 0xd7, 0xf5, //0x000009a1 pmovmskb     %xmm5, %esi
	0x66, 0x0f, 0x74, 0xf1, //0x000009a5 pcmpeqb      %xmm1, %xmm6
	0x66, 0x44, 0x0f, 0xd7, 0xfe, //0x000009a9 pmovmskb     %xmm6, %r15d
	0x48, 0xc1, 0xe2, 0x30, //0x000009ae shlq         $48, %rdx
	0x48, 0xc1, 0xe7, 0x20, //0x000009b2 shlq         $32, %rdi
	0x48, 0x09, 0xd7, //0x000009b6 orq          %rdx, %rdi
	0x48, 0xc1, 0xe3, 0x10, //0x000009b9 shlq         $16, %rbx
	0x48, 0x09, 0xfb, //0x000009bd orq          %rdi, %rbx
	0x49, 0x09, 0xda, //0x000009c0 orq          %rbx, %r10
	0x49, 0xc1, 0xe7, 0x30, //0x000009c3 shlq         $48, %r15
	0x48, 0xc1, 0xe6, 0x20, //0x000009c7 shlq         $32, %rsi
	0x4c, 0x09, 0xfe, //0x000009cb orq          %r15, %rsi
	0x48, 0xc1, 0xe1, 0x10, //0x000009ce shlq         $16, %rcx
	0x48, 0x09, 0xf1, //0x000009d2 orq          %rsi, %rcx
	0x49, 0x09, 0xcb, //0x000009d5 orq          %rcx, %r11
	0x0f, 0x85, 0x30, 0x00, 0x00, 0x00, //0x000009d8 jne          LBB0_121
	0x4d, 0x85, 0xc0, //0x000009de testq        %r8, %r8
	0x0f, 0x85, 0x3d, 0x00, 0x00, 0x00, //0x000009e1 jne          LBB0_123
	0x45, 0x31, 0xc0, //0x000009e7 xorl         %r8d, %r8d
	0x4d, 0x85, 0xd2, //0x000009ea testq        %r10, %r10
	0x0f, 0x85, 0x83, 0x00, 0x00, 0x00, //0x000009ed jne          LBB0_124
	//0x000009f3 LBB0_72
	0x49, 0x83, 0xc1, 0xc0, //0x000009f3 addq         $-64, %r9
	0x49, 0x83, 0xc6, 0xc0, //0x000009f7 addq         $-64, %r14
	0x48, 0x83, 0xc0, 0x40, //0x000009fb addq         $64, %rax
	0x49, 0x83, 0xf9, 0x3f, //0x000009ff cmpq         $63, %r9
	0x0f, 0x87, 0x37, 0xff, 0xff, 0xff, //0x00000a03 ja           LBB0_69
	0xe9, 0x8d, 0x1e, 0x00, 0x00, //0x00000a09 jmp          LBB0_73
	//0x00000a0e LBB0_121
	0x48, 0x83, 0x7d, 0xc8, 0xff, //0x00000a0e cmpq         $-1, $-56(%rbp)
	0x0f, 0x85, 0x0b, 0x00, 0x00, 0x00, //0x00000a13 jne          LBB0_123
	0x49, 0x0f, 0xbc, 0xcb, //0x00000a19 bsfq         %r11, %rcx
	0x48, 0x01, 0xc1, //0x00000a1d addq         %rax, %rcx
	0x48, 0x89, 0x4d, 0xc8, //0x00000a20 movq         %rcx, $-56(%rbp)
	//0x00000a24 LBB0_123
	0x4c, 0x89, 0xc1, //0x00000a24 movq         %r8, %rcx
	0x48, 0xf7, 0xd1, //0x00000a27 notq         %rcx
	0x4c, 0x21, 0xd9, //0x00000a2a andq         %r11, %rcx
	0x48, 0x8d, 0x14, 0x09, //0x00000a2d leaq         (%rcx,%rcx), %rdx
	0x4c, 0x09, 0xc2, //0x00000a31 orq          %r8, %rdx
	0x48, 0x89, 0xd6, //0x00000a34 movq         %rdx, %rsi
	0x48, 0xf7, 0xd6, //0x00000a37 notq         %rsi
	0x4c, 0x21, 0xde, //0x00000a3a andq         %r11, %rsi
	0x48, 0xbf, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000a3d movabsq      $-6148914691236517206, %rdi
	0x48, 0x21, 0xfe, //0x00000a47 andq         %rdi, %rsi
	0x45, 0x31, 0xc0, //0x00000a4a xorl         %r8d, %r8d
	0x48, 0x01, 0xce, //0x00000a4d addq         %rcx, %rsi
	0x41, 0x0f, 0x92, 0xc0, //0x00000a50 setb         %r8b
	0x48, 0x01, 0xf6, //0x00000a54 addq         %rsi, %rsi
	0x48, 0xb9, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000a57 movabsq      $6148914691236517205, %rcx
	0x48, 0x31, 0xce, //0x00000a61 xorq         %rcx, %rsi
	0x48, 0x21, 0xd6, //0x00000a64 andq         %rdx, %rsi
	0x48, 0xf7, 0xd6, //0x00000a67 notq         %rsi
	0x49, 0x21, 0xf2, //0x00000a6a andq         %rsi, %r10
	0x4d, 0x85, 0xd2, //0x00000a6d testq        %r10, %r10
	0x0f, 0x84, 0x7d, 0xff, 0xff, 0xff, //0x00000a70 je           LBB0_72
	//0x00000a76 LBB0_124
	0x49, 0x0f, 0xbc, 0xc2, //0x00000a76 bsfq         %r10, %rax
	0x4c, 0x29, 0xf0, //0x00000a7a subq         %r14, %rax
	0x4c, 0x8b, 0x75, 0xd0, //0x00000a7d movq         $-48(%rbp), %r14
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00000a81 movabsq      $4294977024, %r11
	0xe9, 0x73, 0x0d, 0x00, 0x00, //0x00000a8b jmp          LBB0_304
	//0x00000a90 LBB0_125
	0x48, 0x8b, 0x4d, 0xa8, //0x00000a90 movq         $-88(%rbp), %rcx
	0x4c, 0x8b, 0x49, 0x08, //0x00000a94 movq         $8(%rcx), %r9
	0x4d, 0x8b, 0x06, //0x00000a98 movq         (%r14), %r8
	0x4f, 0x8d, 0x14, 0x04, //0x00000a9b leaq         (%r12,%r8), %r10
	0x4d, 0x29, 0xc1, //0x00000a9f subq         %r8, %r9
	0x49, 0x83, 0xf9, 0x20, //0x00000aa2 cmpq         $32, %r9
	0x0f, 0x8c, 0xbc, 0x1b, 0x00, 0x00, //0x00000aa6 jl           LBB0_134
	0x41, 0xbe, 0x20, 0x00, 0x00, 0x00, //0x00000aac movl         $32, %r14d
	0x31, 0xd2, //0x00000ab2 xorl         %edx, %edx
	0x45, 0x31, 0xdb, //0x00000ab4 xorl         %r11d, %r11d
	0xe9, 0x2a, 0x00, 0x00, 0x00, //0x00000ab7 jmp          LBB0_127
	0x90, 0x90, 0x90, 0x90, //0x00000abc .p2align 4, 0x90
	//0x00000ac0 LBB0_130
	0x45, 0x31, 0xdb, //0x00000ac0 xorl         %r11d, %r11d
	0x48, 0x85, 0xff, //0x00000ac3 testq        %rdi, %rdi
	0x0f, 0x85, 0xa5, 0x00, 0x00, 0x00, //0x00000ac6 jne          LBB0_129
	//0x00000acc LBB0_131
	0x48, 0x83, 0xc2, 0x20, //0x00000acc addq         $32, %rdx
	0x4b, 0x8d, 0x0c, 0x31, //0x00000ad0 leaq         (%r9,%r14), %rcx
	0x48, 0x83, 0xc1, 0xe0, //0x00000ad4 addq         $-32, %rcx
	0x49, 0x83, 0xc6, 0xe0, //0x00000ad8 addq         $-32, %r14
	0x48, 0x83, 0xf9, 0x3f, //0x00000adc cmpq         $63, %rcx
	0x0f, 0x8e, 0x65, 0x1b, 0x00, 0x00, //0x00000ae0 jle          LBB0_132
	//0x00000ae6 LBB0_127
	0xf3, 0x41, 0x0f, 0x6f, 0x24, 0x12, //0x00000ae6 movdqu       (%r10,%rdx), %xmm4
	0xf3, 0x41, 0x0f, 0x6f, 0x6c, 0x12, 0x10, //0x00000aec movdqu       $16(%r10,%rdx), %xmm5
	0x66, 0x0f, 0x6f, 0xf4, //0x00000af3 movdqa       %xmm4, %xmm6
	0x66, 0x0f, 0x74, 0xf0, //0x00000af7 pcmpeqb      %xmm0, %xmm6
	0x66, 0x0f, 0xd7, 0xf6, //0x00000afb pmovmskb     %xmm6, %esi
	0x66, 0x0f, 0x6f, 0xf5, //0x00000aff movdqa       %xmm5, %xmm6
	0x66, 0x0f, 0x74, 0xf0, //0x00000b03 pcmpeqb      %xmm0, %xmm6
	0x66, 0x0f, 0xd7, 0xfe, //0x00000b07 pmovmskb     %xmm6, %edi
	0x48, 0xc1, 0xe7, 0x10, //0x00000b0b shlq         $16, %rdi
	0x48, 0x09, 0xf7, //0x00000b0f orq          %rsi, %rdi
	0x66, 0x0f, 0x74, 0xe1, //0x00000b12 pcmpeqb      %xmm1, %xmm4
	0x66, 0x0f, 0xd7, 0xcc, //0x00000b16 pmovmskb     %xmm4, %ecx
	0x66, 0x0f, 0x74, 0xe9, //0x00000b1a pcmpeqb      %xmm1, %xmm5
	0x66, 0x0f, 0xd7, 0xf5, //0x00000b1e pmovmskb     %xmm5, %esi
	0x48, 0xc1, 0xe6, 0x10, //0x00000b22 shlq         $16, %rsi
	0x48, 0x09, 0xce, //0x00000b26 orq          %rcx, %rsi
	0x48, 0x89, 0xf1, //0x00000b29 movq         %rsi, %rcx
	0x4c, 0x09, 0xd9, //0x00000b2c orq          %r11, %rcx
	0x0f, 0x84, 0x8b, 0xff, 0xff, 0xff, //0x00000b2f je           LBB0_130
	0x44, 0x89, 0xd9, //0x00000b35 movl         %r11d, %ecx
	0xf7, 0xd1, //0x00000b38 notl         %ecx
	0x21, 0xf1, //0x00000b3a andl         %esi, %ecx
	0x44, 0x8d, 0x3c, 0x09, //0x00000b3c leal         (%rcx,%rcx), %r15d
	0x45, 0x09, 0xdf, //0x00000b40 orl          %r11d, %r15d
	0x44, 0x89, 0xfb, //0x00000b43 movl         %r15d, %ebx
	0xf7, 0xd3, //0x00000b46 notl         %ebx
	0x21, 0xf3, //0x00000b48 andl         %esi, %ebx
	0x81, 0xe3, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000b4a andl         $-1431655766, %ebx
	0x45, 0x31, 0xdb, //0x00000b50 xorl         %r11d, %r11d
	0x01, 0xcb, //0x00000b53 addl         %ecx, %ebx
	0x41, 0x0f, 0x92, 0xc3, //0x00000b55 setb         %r11b
	0x01, 0xdb, //0x00000b59 addl         %ebx, %ebx
	0x81, 0xf3, 0x55, 0x55, 0x55, 0x55, //0x00000b5b xorl         $1431655765, %ebx
	0x44, 0x21, 0xfb, //0x00000b61 andl         %r15d, %ebx
	0xf7, 0xd3, //0x00000b64 notl         %ebx
	0x21, 0xdf, //0x00000b66 andl         %ebx, %edi
	0x48, 0x85, 0xff, //0x00000b68 testq        %rdi, %rdi
	0x0f, 0x84, 0x5b, 0xff, 0xff, 0xff, //0x00000b6b je           LBB0_131
	//0x00000b71 LBB0_129
	0x0f, 0xbc, 0xc7, //0x00000b71 bsfl         %edi, %eax
	0x4c, 0x01, 0xc0, //0x00000b74 addq         %r8, %rax
	0x4c, 0x8d, 0x14, 0x02, //0x00000b77 leaq         (%rdx,%rax), %r10
	0x49, 0x83, 0xc2, 0x01, //0x00000b7b addq         $1, %r10
	0x4c, 0x8b, 0x75, 0xd0, //0x00000b7f movq         $-48(%rbp), %r14
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00000b83 movabsq      $4294977024, %r11
	0x4d, 0x89, 0x16, //0x00000b8d movq         %r10, (%r14)
	0x4d, 0x85, 0xc0, //0x00000b90 testq        %r8, %r8
	0x0f, 0x8f, 0x27, 0xf6, 0xff, 0xff, //0x00000b93 jg           LBB0_4
	0xe9, 0x63, 0x2e, 0x00, 0x00, //0x00000b99 jmp          LBB0_608
	//0x00000b9e LBB0_135
	0x48, 0x89, 0xf9, //0x00000b9e movq         %rdi, %rcx
	0x48, 0x29, 0xf1, //0x00000ba1 subq         %rsi, %rcx
	0x48, 0x83, 0xf9, 0x10, //0x00000ba4 cmpq         $16, %rcx
	0x0f, 0x82, 0x0d, 0x22, 0x00, 0x00, //0x00000ba8 jb           LBB0_470
	0x48, 0x89, 0xf1, //0x00000bae movq         %rsi, %rcx
	0x48, 0xf7, 0xd9, //0x00000bb1 negq         %rcx
	0x48, 0x89, 0xf0, //0x00000bb4 movq         %rsi, %rax
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000bb7 .p2align 4, 0x90
	//0x00000bc0 LBB0_137
	0xf3, 0x41, 0x0f, 0x6f, 0x1c, 0x04, //0x00000bc0 movdqu       (%r12,%rax), %xmm3
	0x66, 0x0f, 0x6f, 0xe3, //0x00000bc6 movdqa       %xmm3, %xmm4
	0x66, 0x41, 0x0f, 0x74, 0xe6, //0x00000bca pcmpeqb      %xmm14, %xmm4
	0x66, 0x41, 0x0f, 0xdb, 0xd9, //0x00000bcf pand         %xmm9, %xmm3
	0x66, 0x0f, 0x74, 0xda, //0x00000bd4 pcmpeqb      %xmm2, %xmm3
	0x66, 0x0f, 0xeb, 0xdc, //0x00000bd8 por          %xmm4, %xmm3
	0x66, 0x0f, 0xd7, 0xd3, //0x00000bdc pmovmskb     %xmm3, %edx
	0x85, 0xd2, //0x00000be0 testl        %edx, %edx
	0x0f, 0x85, 0xbe, 0xf5, 0xff, 0xff, //0x00000be2 jne          LBB0_613
	0x48, 0x83, 0xc0, 0x10, //0x00000be8 addq         $16, %rax
	0x48, 0x8d, 0x14, 0x0f, //0x00000bec leaq         (%rdi,%rcx), %rdx
	0x48, 0x83, 0xc2, 0xf0, //0x00000bf0 addq         $-16, %rdx
	0x48, 0x83, 0xc1, 0xf0, //0x00000bf4 addq         $-16, %rcx
	0x48, 0x83, 0xfa, 0x0f, //0x00000bf8 cmpq         $15, %rdx
	0x0f, 0x87, 0xbe, 0xff, 0xff, 0xff, //0x00000bfc ja           LBB0_137
	0x4c, 0x89, 0xe0, //0x00000c02 movq         %r12, %rax
	0x48, 0x29, 0xc8, //0x00000c05 subq         %rcx, %rax
	0x48, 0x01, 0xcf, //0x00000c08 addq         %rcx, %rdi
	0x48, 0x89, 0xf9, //0x00000c0b movq         %rdi, %rcx
	0x48, 0x85, 0xc9, //0x00000c0e testq        %rcx, %rcx
	0x0f, 0x84, 0xb1, 0x21, 0x00, 0x00, //0x00000c11 je           LBB0_471
	//0x00000c17 LBB0_140
	0x48, 0x8d, 0x3c, 0x08, //0x00000c17 leaq         (%rax,%rcx), %rdi
	0x31, 0xd2, //0x00000c1b xorl         %edx, %edx
	//0x00000c1d LBB0_141
	0x0f, 0xb6, 0x1c, 0x10, //0x00000c1d movzbl       (%rax,%rdx), %ebx
	0x80, 0xfb, 0x2c, //0x00000c21 cmpb         $44, %bl
	0x0f, 0x84, 0x4c, 0x1a, 0x00, 0x00, //0x00000c24 je           LBB0_428
	0x80, 0xfb, 0x7d, //0x00000c2a cmpb         $125, %bl
	0x0f, 0x84, 0x43, 0x1a, 0x00, 0x00, //0x00000c2d je           LBB0_428
	0x80, 0xfb, 0x5d, //0x00000c33 cmpb         $93, %bl
	0x0f, 0x84, 0x3a, 0x1a, 0x00, 0x00, //0x00000c36 je           LBB0_428
	0x48, 0x83, 0xc2, 0x01, //0x00000c3c addq         $1, %rdx
	0x48, 0x39, 0xd1, //0x00000c40 cmpq         %rdx, %rcx
	0x0f, 0x85, 0xd4, 0xff, 0xff, 0xff, //0x00000c43 jne          LBB0_141
	0x66, 0x41, 0x0f, 0x6f, 0xf5, //0x00000c49 movdqa       %xmm13, %xmm6
	0x66, 0x0f, 0x6f, 0xea, //0x00000c4e movdqa       %xmm2, %xmm5
	0x48, 0x89, 0xf8, //0x00000c52 movq         %rdi, %rax
	//0x00000c55 LBB0_146
	0x4c, 0x29, 0xe0, //0x00000c55 subq         %r12, %rax
	0x4c, 0x8b, 0x75, 0xd0, //0x00000c58 movq         $-48(%rbp), %r14
	0x4c, 0x8b, 0x6d, 0xb8, //0x00000c5c movq         $-72(%rbp), %r13
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00000c60 movabsq      $4294977024, %r11
	0x66, 0x0f, 0x6f, 0xd5, //0x00000c6a movdqa       %xmm5, %xmm2
	0x66, 0x44, 0x0f, 0x6f, 0xee, //0x00000c6e movdqa       %xmm6, %xmm13
	0x49, 0x89, 0x06, //0x00000c73 movq         %rax, (%r14)
	0x48, 0x85, 0xf6, //0x00000c76 testq        %rsi, %rsi
	0x0f, 0x8f, 0x41, 0xf5, 0xff, 0xff, //0x00000c79 jg           LBB0_4
	0xe9, 0xd5, 0x2d, 0x00, 0x00, //0x00000c7f jmp          LBB0_614
	//0x00000c84 LBB0_147
	0x48, 0x8b, 0x4d, 0xa8, //0x00000c84 movq         $-88(%rbp), %rcx
	0x4c, 0x8b, 0x49, 0x08, //0x00000c88 movq         $8(%rcx), %r9
	0x4d, 0x8b, 0x06, //0x00000c8c movq         (%r14), %r8
	0x4f, 0x8d, 0x14, 0x04, //0x00000c8f leaq         (%r12,%r8), %r10
	0x4d, 0x29, 0xc1, //0x00000c93 subq         %r8, %r9
	0x49, 0x83, 0xf9, 0x20, //0x00000c96 cmpq         $32, %r9
	0x0f, 0x8c, 0x4e, 0x1a, 0x00, 0x00, //0x00000c9a jl           LBB0_156
	0x41, 0xbe, 0x20, 0x00, 0x00, 0x00, //0x00000ca0 movl         $32, %r14d
	0x31, 0xd2, //0x00000ca6 xorl         %edx, %edx
	0x45, 0x31, 0xdb, //0x00000ca8 xorl         %r11d, %r11d
	0xe9, 0x26, 0x00, 0x00, 0x00, //0x00000cab jmp          LBB0_149
	//0x00000cb0 .p2align 4, 0x90
	//0x00000cb0 LBB0_152
	0x45, 0x31, 0xdb, //0x00000cb0 xorl         %r11d, %r11d
	0x48, 0x85, 0xff, //0x00000cb3 testq        %rdi, %rdi
	0x0f, 0x85, 0xa5, 0x00, 0x00, 0x00, //0x00000cb6 jne          LBB0_151
	//0x00000cbc LBB0_153
	0x48, 0x83, 0xc2, 0x20, //0x00000cbc addq         $32, %rdx
	0x4b, 0x8d, 0x0c, 0x31, //0x00000cc0 leaq         (%r9,%r14), %rcx
	0x48, 0x83, 0xc1, 0xe0, //0x00000cc4 addq         $-32, %rcx
	0x49, 0x83, 0xc6, 0xe0, //0x00000cc8 addq         $-32, %r14
	0x48, 0x83, 0xf9, 0x3f, //0x00000ccc cmpq         $63, %rcx
	0x0f, 0x8e, 0xfb, 0x19, 0x00, 0x00, //0x00000cd0 jle          LBB0_154
	//0x00000cd6 LBB0_149
	0xf3, 0x41, 0x0f, 0x6f, 0x1c, 0x12, //0x00000cd6 movdqu       (%r10,%rdx), %xmm3
	0xf3, 0x41, 0x0f, 0x6f, 0x64, 0x12, 0x10, //0x00000cdc movdqu       $16(%r10,%rdx), %xmm4
	0x66, 0x0f, 0x6f, 0xeb, //0x00000ce3 movdqa       %xmm3, %xmm5
	0x66, 0x0f, 0x74, 0xe8, //0x00000ce7 pcmpeqb      %xmm0, %xmm5
	0x66, 0x0f, 0xd7, 0xf5, //0x00000ceb pmovmskb     %xmm5, %esi
	0x66, 0x0f, 0x6f, 0xec, //0x00000cef movdqa       %xmm4, %xmm5
	0x66, 0x0f, 0x74, 0xe8, //0x00000cf3 pcmpeqb      %xmm0, %xmm5
	0x66, 0x0f, 0xd7, 0xfd, //0x00000cf7 pmovmskb     %xmm5, %edi
	0x48, 0xc1, 0xe7, 0x10, //0x00000cfb shlq         $16, %rdi
	0x48, 0x09, 0xf7, //0x00000cff orq          %rsi, %rdi
	0x66, 0x0f, 0x74, 0xd9, //0x00000d02 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xcb, //0x00000d06 pmovmskb     %xmm3, %ecx
	0x66, 0x0f, 0x74, 0xe1, //0x00000d0a pcmpeqb      %xmm1, %xmm4
	0x66, 0x0f, 0xd7, 0xf4, //0x00000d0e pmovmskb     %xmm4, %esi
	0x48, 0xc1, 0xe6, 0x10, //0x00000d12 shlq         $16, %rsi
	0x48, 0x09, 0xce, //0x00000d16 orq          %rcx, %rsi
	0x48, 0x89, 0xf1, //0x00000d19 movq         %rsi, %rcx
	0x4c, 0x09, 0xd9, //0x00000d1c orq          %r11, %rcx
	0x0f, 0x84, 0x8b, 0xff, 0xff, 0xff, //0x00000d1f je           LBB0_152
	0x44, 0x89, 0xd9, //0x00000d25 movl         %r11d, %ecx
	0xf7, 0xd1, //0x00000d28 notl         %ecx
	0x21, 0xf1, //0x00000d2a andl         %esi, %ecx
	0x44, 0x8d, 0x3c, 0x09, //0x00000d2c leal         (%rcx,%rcx), %r15d
	0x45, 0x09, 0xdf, //0x00000d30 orl          %r11d, %r15d
	0x44, 0x89, 0xfb, //0x00000d33 movl         %r15d, %ebx
	0xf7, 0xd3, //0x00000d36 notl         %ebx
	0x21, 0xf3, //0x00000d38 andl         %esi, %ebx
	0x81, 0xe3, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000d3a andl         $-1431655766, %ebx
	0x45, 0x31, 0xdb, //0x00000d40 xorl         %r11d, %r11d
	0x01, 0xcb, //0x00000d43 addl         %ecx, %ebx
	0x41, 0x0f, 0x92, 0xc3, //0x00000d45 setb         %r11b
	0x01, 0xdb, //0x00000d49 addl         %ebx, %ebx
	0x81, 0xf3, 0x55, 0x55, 0x55, 0x55, //0x00000d4b xorl         $1431655765, %ebx
	0x44, 0x21, 0xfb, //0x00000d51 andl         %r15d, %ebx
	0xf7, 0xd3, //0x00000d54 notl         %ebx
	0x21, 0xdf, //0x00000d56 andl         %ebx, %edi
	0x48, 0x85, 0xff, //0x00000d58 testq        %rdi, %rdi
	0x0f, 0x84, 0x5b, 0xff, 0xff, 0xff, //0x00000d5b je           LBB0_153
	//0x00000d61 LBB0_151
	0x0f, 0xbc, 0xc7, //0x00000d61 bsfl         %edi, %eax
	0x4c, 0x01, 0xc0, //0x00000d64 addq         %r8, %rax
	0x4c, 0x8d, 0x14, 0x02, //0x00000d67 leaq         (%rdx,%rax), %r10
	0x49, 0x83, 0xc2, 0x01, //0x00000d6b addq         $1, %r10
	0x4c, 0x8b, 0x75, 0xd0, //0x00000d6f movq         $-48(%rbp), %r14
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00000d73 movabsq      $4294977024, %r11
	0x4d, 0x89, 0x16, //0x00000d7d movq         %r10, (%r14)
	0x4d, 0x85, 0xc0, //0x00000d80 testq        %r8, %r8
	0x0f, 0x8f, 0x93, 0x0a, 0x00, 0x00, //0x00000d83 jg           LBB0_306
	0xe9, 0x73, 0x2c, 0x00, 0x00, //0x00000d89 jmp          LBB0_608
	//0x00000d8e LBB0_157
	0x49, 0x89, 0xc6, //0x00000d8e movq         %rax, %r14
	0x49, 0x29, 0xd6, //0x00000d91 subq         %rdx, %r14
	0x0f, 0x84, 0x71, 0x2e, 0x00, 0x00, //0x00000d94 je           LBB0_643
	0x49, 0x83, 0xfe, 0x40, //0x00000d9a cmpq         $64, %r14
	0x0f, 0x82, 0x17, 0x21, 0x00, 0x00, //0x00000d9e jb           LBB0_486
	0x4c, 0x89, 0x65, 0xc0, //0x00000da4 movq         %r12, $-64(%rbp)
	0x48, 0xc7, 0x45, 0xc8, 0xff, 0xff, 0xff, 0xff, //0x00000da8 movq         $-1, $-56(%rbp)
	0x48, 0x8b, 0x45, 0xb0, //0x00000db0 movq         $-80(%rbp), %rax
	0x31, 0xdb, //0x00000db4 xorl         %ebx, %ebx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000db6 .p2align 4, 0x90
	//0x00000dc0 LBB0_160
	0x49, 0x89, 0xdc, //0x00000dc0 movq         %rbx, %r12
	0x48, 0x8b, 0x4d, 0xc0, //0x00000dc3 movq         $-64(%rbp), %rcx
	0xf3, 0x0f, 0x6f, 0x1c, 0x01, //0x00000dc7 movdqu       (%rcx,%rax), %xmm3
	0xf3, 0x0f, 0x6f, 0x7c, 0x01, 0x10, //0x00000dcc movdqu       $16(%rcx,%rax), %xmm7
	0xf3, 0x0f, 0x6f, 0x74, 0x01, 0x20, //0x00000dd2 movdqu       $32(%rcx,%rax), %xmm6
	0xf3, 0x0f, 0x6f, 0x64, 0x01, 0x30, //0x00000dd8 movdqu       $48(%rcx,%rax), %xmm4
	0x66, 0x0f, 0x6f, 0xeb, //0x00000dde movdqa       %xmm3, %xmm5
	0x66, 0x0f, 0x74, 0xe8, //0x00000de2 pcmpeqb      %xmm0, %xmm5
	0x66, 0x44, 0x0f, 0xd7, 0xdd, //0x00000de6 pmovmskb     %xmm5, %r11d
	0x66, 0x0f, 0x6f, 0xef, //0x00000deb movdqa       %xmm7, %xmm5
	0x66, 0x0f, 0x74, 0xe8, //0x00000def pcmpeqb      %xmm0, %xmm5
	0x66, 0x0f, 0xd7, 0xdd, //0x00000df3 pmovmskb     %xmm5, %ebx
	0x66, 0x0f, 0x6f, 0xee, //0x00000df7 movdqa       %xmm6, %xmm5
	0x66, 0x0f, 0x74, 0xe8, //0x00000dfb pcmpeqb      %xmm0, %xmm5
	0x66, 0x44, 0x0f, 0xd7, 0xd5, //0x00000dff pmovmskb     %xmm5, %r10d
	0x66, 0x0f, 0x6f, 0xec, //0x00000e04 movdqa       %xmm4, %xmm5
	0x66, 0x0f, 0x74, 0xe8, //0x00000e08 pcmpeqb      %xmm0, %xmm5
	0x66, 0x44, 0x0f, 0xd7, 0xfd, //0x00000e0c pmovmskb     %xmm5, %r15d
	0x66, 0x0f, 0x6f, 0xeb, //0x00000e11 movdqa       %xmm3, %xmm5
	0x66, 0x0f, 0x74, 0xe9, //0x00000e15 pcmpeqb      %xmm1, %xmm5
	0x66, 0x44, 0x0f, 0xd7, 0xed, //0x00000e19 pmovmskb     %xmm5, %r13d
	0x66, 0x0f, 0x6f, 0xef, //0x00000e1e movdqa       %xmm7, %xmm5
	0x66, 0x0f, 0x74, 0xe9, //0x00000e22 pcmpeqb      %xmm1, %xmm5
	0x66, 0x0f, 0xd7, 0xfd, //0x00000e26 pmovmskb     %xmm5, %edi
	0x66, 0x0f, 0x6f, 0xee, //0x00000e2a movdqa       %xmm6, %xmm5
	0x66, 0x0f, 0x74, 0xe9, //0x00000e2e pcmpeqb      %xmm1, %xmm5
	0x66, 0x0f, 0xd7, 0xcd, //0x00000e32 pmovmskb     %xmm5, %ecx
	0x66, 0x0f, 0x6f, 0xec, //0x00000e36 movdqa       %xmm4, %xmm5
	0x66, 0x0f, 0x74, 0xe9, //0x00000e3a pcmpeqb      %xmm1, %xmm5
	0x66, 0x44, 0x0f, 0xd7, 0xc5, //0x00000e3e pmovmskb     %xmm5, %r8d
	0x66, 0x41, 0x0f, 0x6f, 0xec, //0x00000e43 movdqa       %xmm12, %xmm5
	0x66, 0x0f, 0x64, 0xef, //0x00000e48 pcmpgtb      %xmm7, %xmm5
	0x66, 0x41, 0x0f, 0x64, 0xfa, //0x00000e4c pcmpgtb      %xmm10, %xmm7
	0x66, 0x0f, 0xdb, 0xfd, //0x00000e51 pand         %xmm5, %xmm7
	0x66, 0x0f, 0xd7, 0xf7, //0x00000e55 pmovmskb     %xmm7, %esi
	0x66, 0x41, 0x0f, 0x6f, 0xec, //0x00000e59 movdqa       %xmm12, %xmm5
	0x66, 0x0f, 0x64, 0xee, //0x00000e5e pcmpgtb      %xmm6, %xmm5
	0x66, 0x41, 0x0f, 0x64, 0xf2, //0x00000e62 pcmpgtb      %xmm10, %xmm6
	0x66, 0x0f, 0xdb, 0xf5, //0x00000e67 pand         %xmm5, %xmm6
	0x66, 0x0f, 0xd7, 0xd6, //0x00000e6b pmovmskb     %xmm6, %edx
	0x66, 0x41, 0x0f, 0x6f, 0xec, //0x00000e6f movdqa       %xmm12, %xmm5
	0x66, 0x0f, 0x64, 0xec, //0x00000e74 pcmpgtb      %xmm4, %xmm5
	0x66, 0x41, 0x0f, 0x64, 0xe2, //0x00000e78 pcmpgtb      %xmm10, %xmm4
	0x66, 0x0f, 0xdb, 0xe5, //0x00000e7d pand         %xmm5, %xmm4
	0x66, 0x44, 0x0f, 0xd7, 0xcc, //0x00000e81 pmovmskb     %xmm4, %r9d
	0x49, 0xc1, 0xe7, 0x30, //0x00000e86 shlq         $48, %r15
	0x49, 0xc1, 0xe2, 0x20, //0x00000e8a shlq         $32, %r10
	0x4d, 0x09, 0xfa, //0x00000e8e orq          %r15, %r10
	0x48, 0xc1, 0xe3, 0x10, //0x00000e91 shlq         $16, %rbx
	0x4c, 0x09, 0xd3, //0x00000e95 orq          %r10, %rbx
	0x49, 0x09, 0xdb, //0x00000e98 orq          %rbx, %r11
	0x49, 0xc1, 0xe0, 0x30, //0x00000e9b shlq         $48, %r8
	0x48, 0xc1, 0xe1, 0x20, //0x00000e9f shlq         $32, %rcx
	0x4c, 0x09, 0xc1, //0x00000ea3 orq          %r8, %rcx
	0x48, 0xc1, 0xe7, 0x10, //0x00000ea6 shlq         $16, %rdi
	0x48, 0x09, 0xcf, //0x00000eaa orq          %rcx, %rdi
	0x49, 0xc1, 0xe1, 0x30, //0x00000ead shlq         $48, %r9
	0x48, 0xc1, 0xe2, 0x20, //0x00000eb1 shlq         $32, %rdx
	0x4c, 0x09, 0xca, //0x00000eb5 orq          %r9, %rdx
	0x48, 0xc1, 0xe6, 0x10, //0x00000eb8 shlq         $16, %rsi
	0x48, 0x09, 0xd6, //0x00000ebc orq          %rdx, %rsi
	0x49, 0x09, 0xfd, //0x00000ebf orq          %rdi, %r13
	0x0f, 0x85, 0x51, 0x00, 0x00, 0x00, //0x00000ec2 jne          LBB0_177
	0x4d, 0x85, 0xe4, //0x00000ec8 testq        %r12, %r12
	0x0f, 0x85, 0x5e, 0x00, 0x00, 0x00, //0x00000ecb jne          LBB0_179
	0x31, 0xdb, //0x00000ed1 xorl         %ebx, %ebx
	//0x00000ed3 LBB0_163
	0x66, 0x41, 0x0f, 0x6f, 0xe4, //0x00000ed3 movdqa       %xmm12, %xmm4
	0x66, 0x0f, 0x64, 0xe3, //0x00000ed8 pcmpgtb      %xmm3, %xmm4
	0x66, 0x41, 0x0f, 0x64, 0xda, //0x00000edc pcmpgtb      %xmm10, %xmm3
	0x66, 0x0f, 0xdb, 0xdc, //0x00000ee1 pand         %xmm4, %xmm3
	0x66, 0x0f, 0xd7, 0xcb, //0x00000ee5 pmovmskb     %xmm3, %ecx
	0x48, 0x09, 0xce, //0x00000ee9 orq          %rcx, %rsi
	0x4d, 0x85, 0xdb, //0x00000eec testq        %r11, %r11
	0x4c, 0x8b, 0x6d, 0xb8, //0x00000eef movq         $-72(%rbp), %r13
	0x0f, 0x85, 0x82, 0x00, 0x00, 0x00, //0x00000ef3 jne          LBB0_180
	0x48, 0x85, 0xf6, //0x00000ef9 testq        %rsi, %rsi
	0x0f, 0x85, 0x84, 0x2b, 0x00, 0x00, //0x00000efc jne          LBB0_618
	0x49, 0x83, 0xc6, 0xc0, //0x00000f02 addq         $-64, %r14
	0x48, 0x83, 0xc0, 0x40, //0x00000f06 addq         $64, %rax
	0x49, 0x83, 0xfe, 0x3f, //0x00000f0a cmpq         $63, %r14
	0x0f, 0x87, 0xac, 0xfe, 0xff, 0xff, //0x00000f0e ja           LBB0_160
	0xe9, 0x6b, 0x18, 0x00, 0x00, //0x00000f14 jmp          LBB0_166
	//0x00000f19 LBB0_177
	0x48, 0x83, 0x7d, 0xc8, 0xff, //0x00000f19 cmpq         $-1, $-56(%rbp)
	0x0f, 0x85, 0x0b, 0x00, 0x00, 0x00, //0x00000f1e jne          LBB0_179
	0x49, 0x0f, 0xbc, 0xcd, //0x00000f24 bsfq         %r13, %rcx
	0x48, 0x01, 0xc1, //0x00000f28 addq         %rax, %rcx
	0x48, 0x89, 0x4d, 0xc8, //0x00000f2b movq         %rcx, $-56(%rbp)
	//0x00000f2f LBB0_179
	0x4c, 0x89, 0xe1, //0x00000f2f movq         %r12, %rcx
	0x48, 0xf7, 0xd1, //0x00000f32 notq         %rcx
	0x4c, 0x21, 0xe9, //0x00000f35 andq         %r13, %rcx
	0x48, 0x8d, 0x14, 0x09, //0x00000f38 leaq         (%rcx,%rcx), %rdx
	0x4c, 0x09, 0xe2, //0x00000f3c orq          %r12, %rdx
	0x48, 0x89, 0xd7, //0x00000f3f movq         %rdx, %rdi
	0x48, 0xf7, 0xd7, //0x00000f42 notq         %rdi
	0x4c, 0x21, 0xef, //0x00000f45 andq         %r13, %rdi
	0x48, 0xbb, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000f48 movabsq      $-6148914691236517206, %rbx
	0x48, 0x21, 0xdf, //0x00000f52 andq         %rbx, %rdi
	0x31, 0xdb, //0x00000f55 xorl         %ebx, %ebx
	0x48, 0x01, 0xcf, //0x00000f57 addq         %rcx, %rdi
	0x0f, 0x92, 0xc3, //0x00000f5a setb         %bl
	0x48, 0x01, 0xff, //0x00000f5d addq         %rdi, %rdi
	0x48, 0xb9, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000f60 movabsq      $6148914691236517205, %rcx
	0x48, 0x31, 0xcf, //0x00000f6a xorq         %rcx, %rdi
	0x48, 0x21, 0xd7, //0x00000f6d andq         %rdx, %rdi
	0x48, 0xf7, 0xd7, //0x00000f70 notq         %rdi
	0x49, 0x21, 0xfb, //0x00000f73 andq         %rdi, %r11
	0xe9, 0x58, 0xff, 0xff, 0xff, //0x00000f76 jmp          LBB0_163
	//0x00000f7b LBB0_180
	0x49, 0x0f, 0xbc, 0xcb, //0x00000f7b bsfq         %r11, %rcx
	0x48, 0x85, 0xf6, //0x00000f7f testq        %rsi, %rsi
	0x0f, 0x84, 0xdb, 0x02, 0x00, 0x00, //0x00000f82 je           LBB0_221
	0x48, 0x0f, 0xbc, 0xd6, //0x00000f88 bsfq         %rsi, %rdx
	0x4c, 0x8b, 0x75, 0xd0, //0x00000f8c movq         $-48(%rbp), %r14
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00000f90 movabsq      $4294977024, %r11
	0x48, 0x39, 0xca, //0x00000f9a cmpq         %rcx, %rdx
	0x0f, 0x83, 0xdc, 0x02, 0x00, 0x00, //0x00000f9d jae          LBB0_222
	0xe9, 0x74, 0x2c, 0x00, 0x00, //0x00000fa3 jmp          LBB0_182
	//0x00000fa8 LBB0_183
	0x41, 0x89, 0xcb, //0x00000fa8 movl         %ecx, %r11d
	0x4d, 0x01, 0xf3, //0x00000fab addq         %r14, %r11
	0x4d, 0x01, 0xeb, //0x00000fae addq         %r13, %r11
	//0x00000fb1 LBB0_184
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x00000fb1 movq         $-1, %r13
	0x4d, 0x85, 0xc0, //0x00000fb8 testq        %r8, %r8
	0x0f, 0x85, 0x49, 0x02, 0x00, 0x00, //0x00000fbb jne          LBB0_213
	0xe9, 0x00, 0x2c, 0x00, 0x00, //0x00000fc1 jmp          LBB0_185
	//0x00000fc6 LBB0_186
	0x49, 0x29, 0xd1, //0x00000fc6 subq         %rdx, %r9
	0x48, 0x89, 0xd1, //0x00000fc9 movq         %rdx, %rcx
	0x0f, 0x84, 0x73, 0x2c, 0x00, 0x00, //0x00000fcc je           LBB0_644
	0x4c, 0x89, 0x65, 0xc0, //0x00000fd2 movq         %r12, $-64(%rbp)
	0x49, 0x83, 0xf9, 0x40, //0x00000fd6 cmpq         $64, %r9
	0x0f, 0x82, 0x9e, 0x1f, 0x00, 0x00, //0x00000fda jb           LBB0_494
	0x48, 0xc7, 0x45, 0xc8, 0xff, 0xff, 0xff, 0xff, //0x00000fe0 movq         $-1, $-56(%rbp)
	0x48, 0x89, 0xc8, //0x00000fe8 movq         %rcx, %rax
	0x45, 0x31, 0xe4, //0x00000feb xorl         %r12d, %r12d
	0x90, 0x90, //0x00000fee .p2align 4, 0x90
	//0x00000ff0 LBB0_189
	0x48, 0x8b, 0x4d, 0xc0, //0x00000ff0 movq         $-64(%rbp), %rcx
	0xf3, 0x44, 0x0f, 0x6f, 0x2c, 0x01, //0x00000ff4 movdqu       (%rcx,%rax), %xmm13
	0xf3, 0x0f, 0x6f, 0x74, 0x01, 0x10, //0x00000ffa movdqu       $16(%rcx,%rax), %xmm6
	0xf3, 0x0f, 0x6f, 0x6c, 0x01, 0x20, //0x00001000 movdqu       $32(%rcx,%rax), %xmm5
	0xf3, 0x0f, 0x6f, 0x64, 0x01, 0x30, //0x00001006 movdqu       $48(%rcx,%rax), %xmm4
	0x66, 0x41, 0x0f, 0x6f, 0xdd, //0x0000100c movdqa       %xmm13, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x00001011 pcmpeqb      %xmm0, %xmm3
	0x66, 0x44, 0x0f, 0xd7, 0xdb, //0x00001015 pmovmskb     %xmm3, %r11d
	0x66, 0x0f, 0x6f, 0xde, //0x0000101a movdqa       %xmm6, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x0000101e pcmpeqb      %xmm0, %xmm3
	0x66, 0x0f, 0xd7, 0xdb, //0x00001022 pmovmskb     %xmm3, %ebx
	0x66, 0x0f, 0x6f, 0xdd, //0x00001026 movdqa       %xmm5, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x0000102a pcmpeqb      %xmm0, %xmm3
	0x66, 0x44, 0x0f, 0xd7, 0xf3, //0x0000102e pmovmskb     %xmm3, %r14d
	0x66, 0x0f, 0x6f, 0xdc, //0x00001033 movdqa       %xmm4, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x00001037 pcmpeqb      %xmm0, %xmm3
	0x66, 0x44, 0x0f, 0xd7, 0xfb, //0x0000103b pmovmskb     %xmm3, %r15d
	0x66, 0x41, 0x0f, 0x6f, 0xdd, //0x00001040 movdqa       %xmm13, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x00001045 pcmpeqb      %xmm1, %xmm3
	0x66, 0x44, 0x0f, 0xd7, 0xeb, //0x00001049 pmovmskb     %xmm3, %r13d
	0x66, 0x0f, 0x6f, 0xde, //0x0000104e movdqa       %xmm6, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x00001052 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xcb, //0x00001056 pmovmskb     %xmm3, %ecx
	0x66, 0x0f, 0x6f, 0xdd, //0x0000105a movdqa       %xmm5, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x0000105e pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xf3, //0x00001062 pmovmskb     %xmm3, %esi
	0x66, 0x0f, 0x6f, 0xdc, //0x00001066 movdqa       %xmm4, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x0000106a pcmpeqb      %xmm1, %xmm3
	0x66, 0x44, 0x0f, 0xd7, 0xd3, //0x0000106e pmovmskb     %xmm3, %r10d
	0x66, 0x41, 0x0f, 0x6f, 0xdc, //0x00001073 movdqa       %xmm12, %xmm3
	0x66, 0x0f, 0x64, 0xde, //0x00001078 pcmpgtb      %xmm6, %xmm3
	0x66, 0x41, 0x0f, 0x64, 0xf2, //0x0000107c pcmpgtb      %xmm10, %xmm6
	0x66, 0x0f, 0xdb, 0xf3, //0x00001081 pand         %xmm3, %xmm6
	0x66, 0x0f, 0xd7, 0xfe, //0x00001085 pmovmskb     %xmm6, %edi
	0x66, 0x41, 0x0f, 0x6f, 0xdc, //0x00001089 movdqa       %xmm12, %xmm3
	0x66, 0x0f, 0x64, 0xdd, //0x0000108e pcmpgtb      %xmm5, %xmm3
	0x66, 0x41, 0x0f, 0x64, 0xea, //0x00001092 pcmpgtb      %xmm10, %xmm5
	0x66, 0x0f, 0xdb, 0xeb, //0x00001097 pand         %xmm3, %xmm5
	0x66, 0x0f, 0xd7, 0xd5, //0x0000109b pmovmskb     %xmm5, %edx
	0x66, 0x41, 0x0f, 0x6f, 0xdc, //0x0000109f movdqa       %xmm12, %xmm3
	0x66, 0x0f, 0x64, 0xdc, //0x000010a4 pcmpgtb      %xmm4, %xmm3
	0x66, 0x41, 0x0f, 0x64, 0xe2, //0x000010a8 pcmpgtb      %xmm10, %xmm4
	0x66, 0x0f, 0xdb, 0xe3, //0x000010ad pand         %xmm3, %xmm4
	0x66, 0x44, 0x0f, 0xd7, 0xc4, //0x000010b1 pmovmskb     %xmm4, %r8d
	0x49, 0xc1, 0xe7, 0x30, //0x000010b6 shlq         $48, %r15
	0x49, 0xc1, 0xe6, 0x20, //0x000010ba shlq         $32, %r14
	0x4d, 0x09, 0xfe, //0x000010be orq          %r15, %r14
	0x48, 0xc1, 0xe3, 0x10, //0x000010c1 shlq         $16, %rbx
	0x4c, 0x09, 0xf3, //0x000010c5 orq          %r14, %rbx
	0x49, 0x09, 0xdb, //0x000010c8 orq          %rbx, %r11
	0x49, 0xc1, 0xe2, 0x30, //0x000010cb shlq         $48, %r10
	0x48, 0xc1, 0xe6, 0x20, //0x000010cf shlq         $32, %rsi
	0x4c, 0x09, 0xd6, //0x000010d3 orq          %r10, %rsi
	0x48, 0xc1, 0xe1, 0x10, //0x000010d6 shlq         $16, %rcx
	0x48, 0x09, 0xf1, //0x000010da orq          %rsi, %rcx
	0x49, 0xc1, 0xe0, 0x30, //0x000010dd shlq         $48, %r8
	0x48, 0xc1, 0xe2, 0x20, //0x000010e1 shlq         $32, %rdx
	0x4c, 0x09, 0xc2, //0x000010e5 orq          %r8, %rdx
	0x48, 0xc1, 0xe7, 0x10, //0x000010e8 shlq         $16, %rdi
	0x48, 0x09, 0xd7, //0x000010ec orq          %rdx, %rdi
	0x49, 0x09, 0xcd, //0x000010ef orq          %rcx, %r13
	0x0f, 0x85, 0x59, 0x00, 0x00, 0x00, //0x000010f2 jne          LBB0_206
	0x4d, 0x85, 0xe4, //0x000010f8 testq        %r12, %r12
	0x0f, 0x85, 0x6f, 0x00, 0x00, 0x00, //0x000010fb jne          LBB0_208
	0x45, 0x31, 0xe4, //0x00001101 xorl         %r12d, %r12d
	0x4c, 0x8b, 0x75, 0xd0, //0x00001104 movq         $-48(%rbp), %r14
	//0x00001108 LBB0_192
	0x66, 0x41, 0x0f, 0x6f, 0xdc, //0x00001108 movdqa       %xmm12, %xmm3
	0x66, 0x41, 0x0f, 0x64, 0xdd, //0x0000110d pcmpgtb      %xmm13, %xmm3
	0x66, 0x45, 0x0f, 0x64, 0xea, //0x00001112 pcmpgtb      %xmm10, %xmm13
	0x66, 0x44, 0x0f, 0xdb, 0xeb, //0x00001117 pand         %xmm3, %xmm13
	0x66, 0x41, 0x0f, 0xd7, 0xcd, //0x0000111c pmovmskb     %xmm13, %ecx
	0x48, 0x09, 0xcf, //0x00001121 orq          %rcx, %rdi
	0x4d, 0x85, 0xdb, //0x00001124 testq        %r11, %r11
	0x4c, 0x8b, 0x6d, 0xb8, //0x00001127 movq         $-72(%rbp), %r13
	0x0f, 0x85, 0x91, 0x00, 0x00, 0x00, //0x0000112b jne          LBB0_210
	0x48, 0x85, 0xff, //0x00001131 testq        %rdi, %rdi
	0x0f, 0x85, 0x6f, 0x2a, 0x00, 0x00, //0x00001134 jne          LBB0_632
	0x49, 0x83, 0xc1, 0xc0, //0x0000113a addq         $-64, %r9
	0x48, 0x83, 0xc0, 0x40, //0x0000113e addq         $64, %rax
	0x49, 0x83, 0xf9, 0x3f, //0x00001142 cmpq         $63, %r9
	0x0f, 0x87, 0xa4, 0xfe, 0xff, 0xff, //0x00001146 ja           LBB0_189
	0xe9, 0xd2, 0x17, 0x00, 0x00, //0x0000114c jmp          LBB0_195
	//0x00001151 LBB0_206
	0x48, 0x83, 0x7d, 0xc8, 0xff, //0x00001151 cmpq         $-1, $-56(%rbp)
	0x4c, 0x8b, 0x75, 0xd0, //0x00001156 movq         $-48(%rbp), %r14
	0x0f, 0x85, 0x14, 0x00, 0x00, 0x00, //0x0000115a jne          LBB0_209
	0x49, 0x0f, 0xbc, 0xcd, //0x00001160 bsfq         %r13, %rcx
	0x48, 0x01, 0xc1, //0x00001164 addq         %rax, %rcx
	0x48, 0x89, 0x4d, 0xc8, //0x00001167 movq         %rcx, $-56(%rbp)
	0xe9, 0x04, 0x00, 0x00, 0x00, //0x0000116b jmp          LBB0_209
	//0x00001170 LBB0_208
	0x4c, 0x8b, 0x75, 0xd0, //0x00001170 movq         $-48(%rbp), %r14
	//0x00001174 LBB0_209
	0x4c, 0x89, 0xe1, //0x00001174 movq         %r12, %rcx
	0x48, 0xf7, 0xd1, //0x00001177 notq         %rcx
	0x4c, 0x21, 0xe9, //0x0000117a andq         %r13, %rcx
	0x48, 0x8d, 0x14, 0x09, //0x0000117d leaq         (%rcx,%rcx), %rdx
	0x4c, 0x09, 0xe2, //0x00001181 orq          %r12, %rdx
	0x48, 0x89, 0xd6, //0x00001184 movq         %rdx, %rsi
	0x48, 0xf7, 0xd6, //0x00001187 notq         %rsi
	0x4c, 0x21, 0xee, //0x0000118a andq         %r13, %rsi
	0x48, 0xbb, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000118d movabsq      $-6148914691236517206, %rbx
	0x48, 0x21, 0xde, //0x00001197 andq         %rbx, %rsi
	0x45, 0x31, 0xe4, //0x0000119a xorl         %r12d, %r12d
	0x48, 0x01, 0xce, //0x0000119d addq         %rcx, %rsi
	0x41, 0x0f, 0x92, 0xc4, //0x000011a0 setb         %r12b
	0x48, 0x01, 0xf6, //0x000011a4 addq         %rsi, %rsi
	0x48, 0xb9, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000011a7 movabsq      $6148914691236517205, %rcx
	0x48, 0x31, 0xce, //0x000011b1 xorq         %rcx, %rsi
	0x48, 0x21, 0xd6, //0x000011b4 andq         %rdx, %rsi
	0x48, 0xf7, 0xd6, //0x000011b7 notq         %rsi
	0x49, 0x21, 0xf3, //0x000011ba andq         %rsi, %r11
	0xe9, 0x46, 0xff, 0xff, 0xff, //0x000011bd jmp          LBB0_192
	//0x000011c2 LBB0_210
	0x49, 0x0f, 0xbc, 0xcb, //0x000011c2 bsfq         %r11, %rcx
	0x48, 0x85, 0xff, //0x000011c6 testq        %rdi, %rdi
	0x0f, 0x84, 0x0c, 0x06, 0x00, 0x00, //0x000011c9 je           LBB0_302
	0x48, 0x0f, 0xbc, 0xd7, //0x000011cf bsfq         %rdi, %rdx
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000011d3 movabsq      $4294977024, %r11
	0xf3, 0x44, 0x0f, 0x6f, 0x2d, 0x5a, 0xee, 0xff, 0xff, //0x000011dd movdqu       $-4518(%rip), %xmm13  /* LCPI0_4+0(%rip) */
	0x48, 0x39, 0xca, //0x000011e6 cmpq         %rcx, %rdx
	0x0f, 0x83, 0x0d, 0x06, 0x00, 0x00, //0x000011e9 jae          LBB0_303
	0xe9, 0xf8, 0x29, 0x00, 0x00, //0x000011ef jmp          LBB0_640
	//0x000011f4 LBB0_212
	0x48, 0x01, 0xf9, //0x000011f4 addq         %rdi, %rcx
	0x49, 0x89, 0xcb, //0x000011f7 movq         %rcx, %r11
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x000011fa movq         $-1, %r13
	0x4d, 0x85, 0xc0, //0x00001201 testq        %r8, %r8
	0x0f, 0x84, 0xbc, 0x29, 0x00, 0x00, //0x00001204 je           LBB0_185
	//0x0000120a LBB0_213
	0x4d, 0x85, 0xc9, //0x0000120a testq        %r9, %r9
	0x48, 0x8b, 0x75, 0xc0, //0x0000120d movq         $-64(%rbp), %rsi
	0x0f, 0x84, 0x1b, 0x28, 0x00, 0x00, //0x00001211 je           LBB0_612
	0x4d, 0x85, 0xff, //0x00001217 testq        %r15, %r15
	0x0f, 0x84, 0x12, 0x28, 0x00, 0x00, //0x0000121a je           LBB0_612
	0x4d, 0x29, 0xf3, //0x00001220 subq         %r14, %r11
	0x49, 0x8d, 0x4b, 0xff, //0x00001223 leaq         $-1(%r11), %rcx
	0x49, 0x39, 0xc8, //0x00001227 cmpq         %rcx, %r8
	0x0f, 0x84, 0xac, 0xef, 0xff, 0xff, //0x0000122a je           LBB0_1
	0x49, 0x39, 0xc9, //0x00001230 cmpq         %rcx, %r9
	0x0f, 0x84, 0xa3, 0xef, 0xff, 0xff, //0x00001233 je           LBB0_1
	0x49, 0x39, 0xcf, //0x00001239 cmpq         %rcx, %r15
	0x0f, 0x84, 0x9a, 0xef, 0xff, 0xff, //0x0000123c je           LBB0_1
	0x4d, 0x85, 0xc9, //0x00001242 testq        %r9, %r9
	0x0f, 0x8e, 0x6d, 0x05, 0x00, 0x00, //0x00001245 jle          LBB0_299
	0x49, 0x8d, 0x49, 0xff, //0x0000124b leaq         $-1(%r9), %rcx
	0x49, 0x39, 0xcf, //0x0000124f cmpq         %rcx, %r15
	0x0f, 0x84, 0x60, 0x05, 0x00, 0x00, //0x00001252 je           LBB0_299
	0x49, 0xf7, 0xd1, //0x00001258 notq         %r9
	0x4d, 0x89, 0xcd, //0x0000125b movq         %r9, %r13
	0xe9, 0x7f, 0xef, 0xff, 0xff, //0x0000125e jmp          LBB0_2
	//0x00001263 LBB0_221
	0xba, 0x40, 0x00, 0x00, 0x00, //0x00001263 movl         $64, %edx
	0x4c, 0x8b, 0x75, 0xd0, //0x00001268 movq         $-48(%rbp), %r14
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x0000126c movabsq      $4294977024, %r11
	0x48, 0x39, 0xca, //0x00001276 cmpq         %rcx, %rdx
	0x0f, 0x82, 0x9d, 0x29, 0x00, 0x00, //0x00001279 jb           LBB0_182
	//0x0000127f LBB0_222
	0x48, 0x01, 0xc8, //0x0000127f addq         %rcx, %rax
	0x48, 0x83, 0xc0, 0x01, //0x00001282 addq         $1, %rax
	//0x00001286 LBB0_223
	0x48, 0x85, 0xc0, //0x00001286 testq        %rax, %rax
	0x0f, 0x88, 0x7e, 0x27, 0x00, 0x00, //0x00001289 js           LBB0_609
	//0x0000128f LBB0_224
	0x49, 0x89, 0x06, //0x0000128f movq         %rax, (%r14)
	0x48, 0x8b, 0x45, 0xb0, //0x00001292 movq         $-80(%rbp), %rax
	0x48, 0x85, 0xc0, //0x00001296 testq        %rax, %rax
	0x0f, 0x8f, 0x21, 0xef, 0xff, 0xff, //0x00001299 jg           LBB0_4
	0xe9, 0x54, 0x27, 0x00, 0x00, //0x0000129f jmp          LBB0_606
	//0x000012a4 LBB0_225
	0x49, 0x8b, 0x0e, //0x000012a4 movq         (%r14), %rcx
	0x48, 0x8b, 0x55, 0xa8, //0x000012a7 movq         $-88(%rbp), %rdx
	0x48, 0x8b, 0x52, 0x08, //0x000012ab movq         $8(%rdx), %rdx
	0x48, 0x8d, 0x72, 0xfc, //0x000012af leaq         $-4(%rdx), %rsi
	0x48, 0x39, 0xf1, //0x000012b3 cmpq         %rsi, %rcx
	0x0f, 0x87, 0xc2, 0x27, 0x00, 0x00, //0x000012b6 ja           LBB0_617
	0x41, 0x8b, 0x14, 0x0c, //0x000012bc movl         (%r12,%rcx), %edx
	0x81, 0xfa, 0x61, 0x6c, 0x73, 0x65, //0x000012c0 cmpl         $1702063201, %edx
	0x0f, 0x85, 0xe7, 0x27, 0x00, 0x00, //0x000012c6 jne          LBB0_622
	0x48, 0x8d, 0x41, 0x04, //0x000012cc leaq         $4(%rcx), %rax
	0x49, 0x89, 0x06, //0x000012d0 movq         %rax, (%r14)
	0x48, 0x85, 0xc9, //0x000012d3 testq        %rcx, %rcx
	0x0f, 0x8f, 0xe4, 0xee, 0xff, 0xff, //0x000012d6 jg           LBB0_4
	0xe9, 0xbc, 0x28, 0x00, 0x00, //0x000012dc jmp          LBB0_228
	//0x000012e1 LBB0_229
	0xf6, 0x45, 0x98, 0x40, //0x000012e1 testb        $64, $-104(%rbp)
	0x0f, 0x85, 0x57, 0x05, 0x00, 0x00, //0x000012e5 jne          LBB0_308
	0x49, 0x8b, 0x45, 0x00, //0x000012eb movq         (%r13), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x000012ef cmpq         $4095, %rax
	0x0f, 0x8f, 0xf1, 0x26, 0x00, 0x00, //0x000012f5 jg           LBB0_634
	0x48, 0x8d, 0x48, 0x01, //0x000012fb leaq         $1(%rax), %rcx
	0x49, 0x89, 0x4d, 0x00, //0x000012ff movq         %rcx, (%r13)
	0x49, 0xc7, 0x44, 0xc5, 0x08, 0x05, 0x00, 0x00, 0x00, //0x00001303 movq         $5, $8(%r13,%rax,8)
	0xe9, 0xaf, 0xee, 0xff, 0xff, //0x0000130c jmp          LBB0_4
	//0x00001311 LBB0_232
	0x48, 0x8b, 0x4d, 0x98, //0x00001311 movq         $-104(%rbp), %rcx
	0xf6, 0xc1, 0x40, //0x00001315 testb        $64, %cl
	0x0f, 0x85, 0xa0, 0x09, 0x00, 0x00, //0x00001318 jne          LBB0_334
	0x49, 0x8b, 0x16, //0x0000131e movq         (%r14), %rdx
	0x48, 0x8b, 0x45, 0xa8, //0x00001321 movq         $-88(%rbp), %rax
	0x48, 0x8b, 0x40, 0x08, //0x00001325 movq         $8(%rax), %rax
	0xf6, 0xc1, 0x20, //0x00001329 testb        $32, %cl
	0x48, 0x89, 0xd1, //0x0000132c movq         %rdx, %rcx
	0x48, 0x89, 0x55, 0xb0, //0x0000132f movq         %rdx, $-80(%rbp)
	0x48, 0x89, 0x45, 0xa0, //0x00001333 movq         %rax, $-96(%rbp)
	0x0f, 0x85, 0x45, 0x10, 0x00, 0x00, //0x00001337 jne          LBB0_385
	0x49, 0x89, 0xc1, //0x0000133d movq         %rax, %r9
	0x49, 0x29, 0xd1, //0x00001340 subq         %rdx, %r9
	0x0f, 0x84, 0xef, 0x28, 0x00, 0x00, //0x00001343 je           LBB0_646
	0x49, 0x83, 0xf9, 0x40, //0x00001349 cmpq         $64, %r9
	0x0f, 0x82, 0x05, 0x1d, 0x00, 0x00, //0x0000134d jb           LBB0_501
	0x48, 0x8b, 0x45, 0xb0, //0x00001353 movq         $-80(%rbp), %rax
	0x49, 0x89, 0xc6, //0x00001357 movq         %rax, %r14
	0x49, 0xf7, 0xd6, //0x0000135a notq         %r14
	0x48, 0xc7, 0x45, 0xc8, 0xff, 0xff, 0xff, 0xff, //0x0000135d movq         $-1, $-56(%rbp)
	0x45, 0x31, 0xc0, //0x00001365 xorl         %r8d, %r8d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001368 .p2align 4, 0x90
	//0x00001370 LBB0_237
	0xf3, 0x41, 0x0f, 0x6f, 0x1c, 0x04, //0x00001370 movdqu       (%r12,%rax), %xmm3
	0xf3, 0x41, 0x0f, 0x6f, 0x64, 0x04, 0x10, //0x00001376 movdqu       $16(%r12,%rax), %xmm4
	0xf3, 0x41, 0x0f, 0x6f, 0x6c, 0x04, 0x20, //0x0000137d movdqu       $32(%r12,%rax), %xmm5
	0xf3, 0x41, 0x0f, 0x6f, 0x74, 0x04, 0x30, //0x00001384 movdqu       $48(%r12,%rax), %xmm6
	0x66, 0x0f, 0x6f, 0xfb, //0x0000138b movdqa       %xmm3, %xmm7
	0x66, 0x0f, 0x74, 0xf8, //0x0000138f pcmpeqb      %xmm0, %xmm7
	0x66, 0x44, 0x0f, 0xd7, 0xd7, //0x00001393 pmovmskb     %xmm7, %r10d
	0x66, 0x0f, 0x6f, 0xfc, //0x00001398 movdqa       %xmm4, %xmm7
	0x66, 0x0f, 0x74, 0xf8, //0x0000139c pcmpeqb      %xmm0, %xmm7
	0x66, 0x0f, 0xd7, 0xcf, //0x000013a0 pmovmskb     %xmm7, %ecx
	0x66, 0x0f, 0x6f, 0xfd, //0x000013a4 movdqa       %xmm5, %xmm7
	0x66, 0x0f, 0x74, 0xf8, //0x000013a8 pcmpeqb      %xmm0, %xmm7
	0x66, 0x0f, 0xd7, 0xff, //0x000013ac pmovmskb     %xmm7, %edi
	0x66, 0x0f, 0x6f, 0xfe, //0x000013b0 movdqa       %xmm6, %xmm7
	0x66, 0x0f, 0x74, 0xf8, //0x000013b4 pcmpeqb      %xmm0, %xmm7
	0x66, 0x0f, 0xd7, 0xd7, //0x000013b8 pmovmskb     %xmm7, %edx
	0x66, 0x0f, 0x74, 0xd9, //0x000013bc pcmpeqb      %xmm1, %xmm3
	0x66, 0x44, 0x0f, 0xd7, 0xdb, //0x000013c0 pmovmskb     %xmm3, %r11d
	0x66, 0x0f, 0x74, 0xe1, //0x000013c5 pcmpeqb      %xmm1, %xmm4
	0x66, 0x0f, 0xd7, 0xdc, //0x000013c9 pmovmskb     %xmm4, %ebx
	0x66, 0x0f, 0x74, 0xe9, //0x000013cd pcmpeqb      %xmm1, %xmm5
	0x66, 0x0f, 0xd7, 0xf5, //0x000013d1 pmovmskb     %xmm5, %esi
	0x66, 0x0f, 0x74, 0xf1, //0x000013d5 pcmpeqb      %xmm1, %xmm6
	0x66, 0x44, 0x0f, 0xd7, 0xfe, //0x000013d9 pmovmskb     %xmm6, %r15d
	0x48, 0xc1, 0xe2, 0x30, //0x000013de shlq         $48, %rdx
	0x48, 0xc1, 0xe7, 0x20, //0x000013e2 shlq         $32, %rdi
	0x48, 0x09, 0xd7, //0x000013e6 orq          %rdx, %rdi
	0x48, 0xc1, 0xe1, 0x10, //0x000013e9 shlq         $16, %rcx
	0x48, 0x09, 0xf9, //0x000013ed orq          %rdi, %rcx
	0x49, 0x09, 0xca, //0x000013f0 orq          %rcx, %r10
	0x49, 0xc1, 0xe7, 0x30, //0x000013f3 shlq         $48, %r15
	0x48, 0xc1, 0xe6, 0x20, //0x000013f7 shlq         $32, %rsi
	0x4c, 0x09, 0xfe, //0x000013fb orq          %r15, %rsi
	0x48, 0xc1, 0xe3, 0x10, //0x000013fe shlq         $16, %rbx
	0x48, 0x09, 0xf3, //0x00001402 orq          %rsi, %rbx
	0x49, 0x09, 0xdb, //0x00001405 orq          %rbx, %r11
	0x0f, 0x85, 0x3a, 0x00, 0x00, 0x00, //0x00001408 jne          LBB0_246
	0x4d, 0x85, 0xc0, //0x0000140e testq        %r8, %r8
	0x0f, 0x85, 0x47, 0x00, 0x00, 0x00, //0x00001411 jne          LBB0_248
	0x45, 0x31, 0xc0, //0x00001417 xorl         %r8d, %r8d
	0x4d, 0x85, 0xd2, //0x0000141a testq        %r10, %r10
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x0000141d movabsq      $4294977024, %r11
	0x0f, 0x85, 0x8d, 0x00, 0x00, 0x00, //0x00001427 jne          LBB0_249
	//0x0000142d LBB0_240
	0x49, 0x83, 0xc1, 0xc0, //0x0000142d addq         $-64, %r9
	0x49, 0x83, 0xc6, 0xc0, //0x00001431 addq         $-64, %r14
	0x48, 0x83, 0xc0, 0x40, //0x00001435 addq         $64, %rax
	0x49, 0x83, 0xf9, 0x3f, //0x00001439 cmpq         $63, %r9
	0x0f, 0x87, 0x2d, 0xff, 0xff, 0xff, //0x0000143d ja           LBB0_237
	0xe9, 0x06, 0x18, 0x00, 0x00, //0x00001443 jmp          LBB0_241
	//0x00001448 LBB0_246
	0x48, 0x83, 0x7d, 0xc8, 0xff, //0x00001448 cmpq         $-1, $-56(%rbp)
	0x0f, 0x85, 0x0b, 0x00, 0x00, 0x00, //0x0000144d jne          LBB0_248
	0x49, 0x0f, 0xbc, 0xcb, //0x00001453 bsfq         %r11, %rcx
	0x48, 0x01, 0xc1, //0x00001457 addq         %rax, %rcx
	0x48, 0x89, 0x4d, 0xc8, //0x0000145a movq         %rcx, $-56(%rbp)
	//0x0000145e LBB0_248
	0x4c, 0x89, 0xc1, //0x0000145e movq         %r8, %rcx
	0x48, 0xf7, 0xd1, //0x00001461 notq         %rcx
	0x4c, 0x21, 0xd9, //0x00001464 andq         %r11, %rcx
	0x48, 0x8d, 0x14, 0x09, //0x00001467 leaq         (%rcx,%rcx), %rdx
	0x4c, 0x09, 0xc2, //0x0000146b orq          %r8, %rdx
	0x48, 0x89, 0xd6, //0x0000146e movq         %rdx, %rsi
	0x48, 0xf7, 0xd6, //0x00001471 notq         %rsi
	0x4c, 0x21, 0xde, //0x00001474 andq         %r11, %rsi
	0x48, 0xbf, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001477 movabsq      $-6148914691236517206, %rdi
	0x48, 0x21, 0xfe, //0x00001481 andq         %rdi, %rsi
	0x45, 0x31, 0xc0, //0x00001484 xorl         %r8d, %r8d
	0x48, 0x01, 0xce, //0x00001487 addq         %rcx, %rsi
	0x41, 0x0f, 0x92, 0xc0, //0x0000148a setb         %r8b
	0x48, 0x01, 0xf6, //0x0000148e addq         %rsi, %rsi
	0x48, 0xb9, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00001491 movabsq      $6148914691236517205, %rcx
	0x48, 0x31, 0xce, //0x0000149b xorq         %rcx, %rsi
	0x48, 0x21, 0xd6, //0x0000149e andq         %rdx, %rsi
	0x48, 0xf7, 0xd6, //0x000014a1 notq         %rsi
	0x49, 0x21, 0xf2, //0x000014a4 andq         %rsi, %r10
	0x4d, 0x85, 0xd2, //0x000014a7 testq        %r10, %r10
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000014aa movabsq      $4294977024, %r11
	0x0f, 0x84, 0x73, 0xff, 0xff, 0xff, //0x000014b4 je           LBB0_240
	//0x000014ba LBB0_249
	0x49, 0x0f, 0xbc, 0xc2, //0x000014ba bsfq         %r10, %rax
	0x4c, 0x29, 0xf0, //0x000014be subq         %r14, %rax
	0x4c, 0x8b, 0x75, 0xd0, //0x000014c1 movq         $-48(%rbp), %r14
	0xe9, 0x73, 0x11, 0x00, 0x00, //0x000014c5 jmp          LBB0_426
	//0x000014ca LBB0_250
	0x48, 0x8b, 0x45, 0xa8, //0x000014ca movq         $-88(%rbp), %rax
	0x48, 0x8b, 0x78, 0x08, //0x000014ce movq         $8(%rax), %rdi
	0x49, 0x8b, 0x06, //0x000014d2 movq         (%r14), %rax
	0xf6, 0x45, 0x98, 0x40, //0x000014d5 testb        $64, $-104(%rbp)
	0x0f, 0x85, 0xf2, 0x08, 0x00, 0x00, //0x000014d9 jne          LBB0_344
	0x48, 0x29, 0xc7, //0x000014df subq         %rax, %rdi
	0x0f, 0x84, 0xe7, 0x26, 0x00, 0x00, //0x000014e2 je           LBB0_635
	0x4d, 0x8d, 0x04, 0x04, //0x000014e8 leaq         (%r12,%rax), %r8
	0x41, 0x80, 0x38, 0x30, //0x000014ec cmpb         $48, (%r8)
	0x0f, 0x85, 0x36, 0x00, 0x00, 0x00, //0x000014f0 jne          LBB0_256
	0xbb, 0x01, 0x00, 0x00, 0x00, //0x000014f6 movl         $1, %ebx
	0x48, 0x83, 0xff, 0x01, //0x000014fb cmpq         $1, %rdi
	0x0f, 0x84, 0x23, 0x17, 0x00, 0x00, //0x000014ff je           LBB0_469
	0x41, 0x8a, 0x48, 0x01, //0x00001505 movb         $1(%r8), %cl
	0x80, 0xc1, 0xd2, //0x00001509 addb         $-46, %cl
	0x80, 0xf9, 0x37, //0x0000150c cmpb         $55, %cl
	0x0f, 0x87, 0x13, 0x17, 0x00, 0x00, //0x0000150f ja           LBB0_469
	0x0f, 0xb6, 0xc9, //0x00001515 movzbl       %cl, %ecx
	0x48, 0xba, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x00001518 movabsq      $36028797027352577, %rdx
	0x48, 0x0f, 0xa3, 0xca, //0x00001522 btq          %rcx, %rdx
	0x0f, 0x83, 0xfc, 0x16, 0x00, 0x00, //0x00001526 jae          LBB0_469
	//0x0000152c LBB0_256
	0x48, 0x83, 0xff, 0x10, //0x0000152c cmpq         $16, %rdi
	0x0f, 0x82, 0x02, 0x1b, 0x00, 0x00, //0x00001530 jb           LBB0_500
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x00001536 movq         $-1, %r13
	0x31, 0xdb, //0x0000153d xorl         %ebx, %ebx
	0x49, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x0000153f movq         $-1, %r10
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00001546 movq         $-1, %r14
	0x49, 0x89, 0xf9, //0x0000154d movq         %rdi, %r9
	//0x00001550 .p2align 4, 0x90
	//0x00001550 LBB0_258
	0xf3, 0x41, 0x0f, 0x6f, 0x1c, 0x18, //0x00001550 movdqu       (%r8,%rbx), %xmm3
	0x66, 0x0f, 0x6f, 0xe3, //0x00001556 movdqa       %xmm3, %xmm4
	0x66, 0x0f, 0x64, 0x25, 0x3e, 0xeb, 0xff, 0xff, //0x0000155a pcmpgtb      $-5314(%rip), %xmm4  /* LCPI0_10+0(%rip) */
	0x66, 0x41, 0x0f, 0x6f, 0xef, //0x00001562 movdqa       %xmm15, %xmm5
	0x66, 0x0f, 0x64, 0xeb, //0x00001567 pcmpgtb      %xmm3, %xmm5
	0x66, 0x0f, 0xdb, 0xec, //0x0000156b pand         %xmm4, %xmm5
	0x66, 0x0f, 0x6f, 0xe3, //0x0000156f movdqa       %xmm3, %xmm4
	0x66, 0x0f, 0x74, 0x25, 0x45, 0xeb, 0xff, 0xff, //0x00001573 pcmpeqb      $-5307(%rip), %xmm4  /* LCPI0_12+0(%rip) */
	0x66, 0x0f, 0x6f, 0xf3, //0x0000157b movdqa       %xmm3, %xmm6
	0x66, 0x0f, 0x74, 0x35, 0x49, 0xeb, 0xff, 0xff, //0x0000157f pcmpeqb      $-5303(%rip), %xmm6  /* LCPI0_13+0(%rip) */
	0x66, 0x0f, 0xeb, 0xf4, //0x00001587 por          %xmm4, %xmm6
	0x66, 0x0f, 0x6f, 0xe3, //0x0000158b movdqa       %xmm3, %xmm4
	0x66, 0x41, 0x0f, 0xdb, 0xe1, //0x0000158f pand         %xmm9, %xmm4
	0x66, 0x0f, 0x74, 0x1d, 0x44, 0xeb, 0xff, 0xff, //0x00001594 pcmpeqb      $-5308(%rip), %xmm3  /* LCPI0_14+0(%rip) */
	0x66, 0x41, 0x0f, 0x74, 0xe3, //0x0000159c pcmpeqb      %xmm11, %xmm4
	0x66, 0x44, 0x0f, 0xd7, 0xfc, //0x000015a1 pmovmskb     %xmm4, %r15d
	0x66, 0x0f, 0xeb, 0xe3, //0x000015a6 por          %xmm3, %xmm4
	0x66, 0x0f, 0xeb, 0xee, //0x000015aa por          %xmm6, %xmm5
	0x66, 0x0f, 0xeb, 0xec, //0x000015ae por          %xmm4, %xmm5
	0x66, 0x0f, 0xd7, 0xd3, //0x000015b2 pmovmskb     %xmm3, %edx
	0x66, 0x44, 0x0f, 0xd7, 0xde, //0x000015b6 pmovmskb     %xmm6, %r11d
	0x66, 0x0f, 0xd7, 0xcd, //0x000015bb pmovmskb     %xmm5, %ecx
	0xf7, 0xd1, //0x000015bf notl         %ecx
	0x0f, 0xbc, 0xc9, //0x000015c1 bsfl         %ecx, %ecx
	0x83, 0xf9, 0x10, //0x000015c4 cmpl         $16, %ecx
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x000015c7 je           LBB0_260
	0xbe, 0xff, 0xff, 0xff, 0xff, //0x000015cd movl         $-1, %esi
	0xd3, 0xe6, //0x000015d2 shll         %cl, %esi
	0xf7, 0xd6, //0x000015d4 notl         %esi
	0x21, 0xf2, //0x000015d6 andl         %esi, %edx
	0x41, 0x21, 0xf7, //0x000015d8 andl         %esi, %r15d
	0x44, 0x21, 0xde, //0x000015db andl         %r11d, %esi
	0x41, 0x89, 0xf3, //0x000015de movl         %esi, %r11d
	//0x000015e1 LBB0_260
	0x8d, 0x72, 0xff, //0x000015e1 leal         $-1(%rdx), %esi
	0x21, 0xd6, //0x000015e4 andl         %edx, %esi
	0x0f, 0x85, 0x06, 0x16, 0x00, 0x00, //0x000015e6 jne          LBB0_463
	0x41, 0x8d, 0x77, 0xff, //0x000015ec leal         $-1(%r15), %esi
	0x44, 0x21, 0xfe, //0x000015f0 andl         %r15d, %esi
	0x0f, 0x85, 0xf9, 0x15, 0x00, 0x00, //0x000015f3 jne          LBB0_463
	0x41, 0x8d, 0x73, 0xff, //0x000015f9 leal         $-1(%r11), %esi
	0x44, 0x21, 0xde, //0x000015fd andl         %r11d, %esi
	0x0f, 0x85, 0xec, 0x15, 0x00, 0x00, //0x00001600 jne          LBB0_463
	0x85, 0xd2, //0x00001606 testl        %edx, %edx
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x00001608 je           LBB0_266
	0x0f, 0xbc, 0xd2, //0x0000160e bsfl         %edx, %edx
	0x49, 0x83, 0xfe, 0xff, //0x00001611 cmpq         $-1, %r14
	0x0f, 0x85, 0xfc, 0x15, 0x00, 0x00, //0x00001615 jne          LBB0_466
	0x48, 0x01, 0xda, //0x0000161b addq         %rbx, %rdx
	0x49, 0x89, 0xd6, //0x0000161e movq         %rdx, %r14
	//0x00001621 LBB0_266
	0x45, 0x85, 0xff, //0x00001621 testl        %r15d, %r15d
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x00001624 je           LBB0_269
	0x41, 0x0f, 0xbc, 0xd7, //0x0000162a bsfl         %r15d, %edx
	0x49, 0x83, 0xfa, 0xff, //0x0000162e cmpq         $-1, %r10
	0x0f, 0x85, 0xdf, 0x15, 0x00, 0x00, //0x00001632 jne          LBB0_466
	0x48, 0x01, 0xda, //0x00001638 addq         %rbx, %rdx
	0x49, 0x89, 0xd2, //0x0000163b movq         %rdx, %r10
	//0x0000163e LBB0_269
	0x45, 0x85, 0xdb, //0x0000163e testl        %r11d, %r11d
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x00001641 je           LBB0_272
	0x41, 0x0f, 0xbc, 0xd3, //0x00001647 bsfl         %r11d, %edx
	0x49, 0x83, 0xfd, 0xff, //0x0000164b cmpq         $-1, %r13
	0x0f, 0x85, 0xc2, 0x15, 0x00, 0x00, //0x0000164f jne          LBB0_466
	0x48, 0x01, 0xda, //0x00001655 addq         %rbx, %rdx
	0x49, 0x89, 0xd5, //0x00001658 movq         %rdx, %r13
	//0x0000165b LBB0_272
	0x83, 0xf9, 0x10, //0x0000165b cmpl         $16, %ecx
	0x0f, 0x85, 0x36, 0x0f, 0x00, 0x00, //0x0000165e jne          LBB0_411
	0x49, 0x83, 0xc1, 0xf0, //0x00001664 addq         $-16, %r9
	0x48, 0x83, 0xc3, 0x10, //0x00001668 addq         $16, %rbx
	0x49, 0x83, 0xf9, 0x0f, //0x0000166c cmpq         $15, %r9
	0x0f, 0x87, 0xda, 0xfe, 0xff, 0xff, //0x00001670 ja           LBB0_258
	0x49, 0x8d, 0x14, 0x18, //0x00001676 leaq         (%r8,%rbx), %rdx
	0x49, 0x89, 0xd3, //0x0000167a movq         %rdx, %r11
	0x48, 0x39, 0xdf, //0x0000167d cmpq         %rbx, %rdi
	0x0f, 0x84, 0x28, 0x0f, 0x00, 0x00, //0x00001680 je           LBB0_413
	//0x00001686 LBB0_275
	0x4e, 0x8d, 0x1c, 0x0a, //0x00001686 leaq         (%rdx,%r9), %r11
	0x49, 0x89, 0xd7, //0x0000168a movq         %rdx, %r15
	0x4d, 0x29, 0xc7, //0x0000168d subq         %r8, %r15
	0x31, 0xdb, //0x00001690 xorl         %ebx, %ebx
	0xe9, 0x26, 0x00, 0x00, 0x00, //0x00001692 jmp          LBB0_279
	//0x00001697 LBB0_276
	0x49, 0x83, 0xfd, 0xff, //0x00001697 cmpq         $-1, %r13
	0x0f, 0x85, 0x65, 0x15, 0x00, 0x00, //0x0000169b jne          LBB0_465
	0x4d, 0x8d, 0x2c, 0x1f, //0x000016a1 leaq         (%r15,%rbx), %r13
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000016a5 .p2align 4, 0x90
	//0x000016b0 LBB0_278
	0x48, 0x83, 0xc3, 0x01, //0x000016b0 addq         $1, %rbx
	0x49, 0x39, 0xd9, //0x000016b4 cmpq         %rbx, %r9
	0x0f, 0x84, 0xf1, 0x0e, 0x00, 0x00, //0x000016b7 je           LBB0_413
	//0x000016bd LBB0_279
	0x0f, 0xbe, 0x0c, 0x1a, //0x000016bd movsbl       (%rdx,%rbx), %ecx
	0x8d, 0x71, 0xd0, //0x000016c1 leal         $-48(%rcx), %esi
	0x83, 0xfe, 0x0a, //0x000016c4 cmpl         $10, %esi
	0x0f, 0x82, 0xe3, 0xff, 0xff, 0xff, //0x000016c7 jb           LBB0_278
	0x8d, 0x71, 0xd5, //0x000016cd leal         $-43(%rcx), %esi
	0x83, 0xfe, 0x1a, //0x000016d0 cmpl         $26, %esi
	0x0f, 0x87, 0x23, 0x00, 0x00, 0x00, //0x000016d3 ja           LBB0_284
	0x48, 0x8d, 0x3d, 0x08, 0x28, 0x00, 0x00, //0x000016d9 leaq         $10248(%rip), %rdi  /* LJTI0_2+0(%rip) */
	0x48, 0x63, 0x0c, 0xb7, //0x000016e0 movslq       (%rdi,%rsi,4), %rcx
	0x48, 0x01, 0xf9, //0x000016e4 addq         %rdi, %rcx
	0xff, 0xe1, //0x000016e7 jmpq         *%rcx
	//0x000016e9 LBB0_282
	0x49, 0x83, 0xfe, 0xff, //0x000016e9 cmpq         $-1, %r14
	0x0f, 0x85, 0x13, 0x15, 0x00, 0x00, //0x000016ed jne          LBB0_465
	0x4d, 0x8d, 0x34, 0x1f, //0x000016f3 leaq         (%r15,%rbx), %r14
	0xe9, 0xb4, 0xff, 0xff, 0xff, //0x000016f7 jmp          LBB0_278
	//0x000016fc LBB0_284
	0x83, 0xf9, 0x65, //0x000016fc cmpl         $101, %ecx
	0x0f, 0x85, 0xa3, 0x0e, 0x00, 0x00, //0x000016ff jne          LBB0_412
	//0x00001705 LBB0_285
	0x49, 0x83, 0xfa, 0xff, //0x00001705 cmpq         $-1, %r10
	0x0f, 0x85, 0xf7, 0x14, 0x00, 0x00, //0x00001709 jne          LBB0_465
	0x4d, 0x8d, 0x14, 0x1f, //0x0000170f leaq         (%r15,%rbx), %r10
	0xe9, 0x98, 0xff, 0xff, 0xff, //0x00001713 jmp          LBB0_278
	//0x00001718 LBB0_287
	0x49, 0x8b, 0x0e, //0x00001718 movq         (%r14), %rcx
	0x48, 0x8b, 0x55, 0xa8, //0x0000171b movq         $-88(%rbp), %rdx
	0x48, 0x8b, 0x52, 0x08, //0x0000171f movq         $8(%rdx), %rdx
	0x48, 0x8d, 0x72, 0xfd, //0x00001723 leaq         $-3(%rdx), %rsi
	0x48, 0x39, 0xf1, //0x00001727 cmpq         %rsi, %rcx
	0x0f, 0x87, 0x4e, 0x23, 0x00, 0x00, //0x0000172a ja           LBB0_617
	0x48, 0x8d, 0x41, 0xff, //0x00001730 leaq         $-1(%rcx), %rax
	0x41, 0x81, 0x7c, 0x0c, 0xff, 0x6e, 0x75, 0x6c, 0x6c, //0x00001734 cmpl         $1819047278, $-1(%r12,%rcx)
	0x0f, 0x84, 0x60, 0x00, 0x00, 0x00, //0x0000173d je           LBB0_298
	0xe9, 0x07, 0x24, 0x00, 0x00, //0x00001743 jmp          LBB0_289
	//0x00001748 LBB0_293
	0xf6, 0x45, 0x98, 0x40, //0x00001748 testb        $64, $-104(%rbp)
	0x0f, 0x85, 0x62, 0x07, 0x00, 0x00, //0x0000174c jne          LBB0_356
	0x49, 0x8b, 0x45, 0x00, //0x00001752 movq         (%r13), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x00001756 cmpq         $4095, %rax
	0x0f, 0x8f, 0x8a, 0x22, 0x00, 0x00, //0x0000175c jg           LBB0_634
	0x48, 0x8d, 0x48, 0x01, //0x00001762 leaq         $1(%rax), %rcx
	0x49, 0x89, 0x4d, 0x00, //0x00001766 movq         %rcx, (%r13)
	0x49, 0xc7, 0x44, 0xc5, 0x08, 0x06, 0x00, 0x00, 0x00, //0x0000176a movq         $6, $8(%r13,%rax,8)
	0xe9, 0x48, 0xea, 0xff, 0xff, //0x00001773 jmp          LBB0_4
	//0x00001778 LBB0_296
	0x49, 0x8b, 0x0e, //0x00001778 movq         (%r14), %rcx
	0x48, 0x8b, 0x55, 0xa8, //0x0000177b movq         $-88(%rbp), %rdx
	0x48, 0x8b, 0x52, 0x08, //0x0000177f movq         $8(%rdx), %rdx
	0x48, 0x8d, 0x72, 0xfd, //0x00001783 leaq         $-3(%rdx), %rsi
	0x48, 0x39, 0xf1, //0x00001787 cmpq         %rsi, %rcx
	0x0f, 0x87, 0xee, 0x22, 0x00, 0x00, //0x0000178a ja           LBB0_617
	0x48, 0x8d, 0x41, 0xff, //0x00001790 leaq         $-1(%rcx), %rax
	0x41, 0x81, 0x7c, 0x0c, 0xff, 0x74, 0x72, 0x75, 0x65, //0x00001794 cmpl         $1702195828, $-1(%r12,%rcx)
	0x0f, 0x85, 0x65, 0x23, 0x00, 0x00, //0x0000179d jne          LBB0_627
	//0x000017a3 LBB0_298
	0x48, 0x8d, 0x51, 0x03, //0x000017a3 leaq         $3(%rcx), %rdx
	0x49, 0x89, 0x16, //0x000017a7 movq         %rdx, (%r14)
	0x48, 0x85, 0xc9, //0x000017aa testq        %rcx, %rcx
	0x0f, 0x8f, 0x0d, 0xea, 0xff, 0xff, //0x000017ad jg           LBB0_4
	0xe9, 0x8f, 0x22, 0x00, 0x00, //0x000017b3 jmp          LBB0_638
	//0x000017b8 LBB0_299
	0x4c, 0x89, 0xc1, //0x000017b8 movq         %r8, %rcx
	0x4c, 0x09, 0xf9, //0x000017bb orq          %r15, %rcx
	0x0f, 0x99, 0xc1, //0x000017be setns        %cl
	0x0f, 0x88, 0x9f, 0x0b, 0x00, 0x00, //0x000017c1 js           LBB0_384
	0x4d, 0x39, 0xf8, //0x000017c7 cmpq         %r15, %r8
	0x0f, 0x8c, 0x96, 0x0b, 0x00, 0x00, //0x000017ca jl           LBB0_384
	0x49, 0xf7, 0xd0, //0x000017d0 notq         %r8
	0x4d, 0x89, 0xc5, //0x000017d3 movq         %r8, %r13
	0xe9, 0x07, 0xea, 0xff, 0xff, //0x000017d6 jmp          LBB0_2
	//0x000017db LBB0_302
	0xba, 0x40, 0x00, 0x00, 0x00, //0x000017db movl         $64, %edx
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000017e0 movabsq      $4294977024, %r11
	0xf3, 0x44, 0x0f, 0x6f, 0x2d, 0x4d, 0xe8, 0xff, 0xff, //0x000017ea movdqu       $-6067(%rip), %xmm13  /* LCPI0_4+0(%rip) */
	0x48, 0x39, 0xca, //0x000017f3 cmpq         %rcx, %rdx
	0x0f, 0x82, 0xf0, 0x23, 0x00, 0x00, //0x000017f6 jb           LBB0_640
	//0x000017fc LBB0_303
	0x48, 0x01, 0xc8, //0x000017fc addq         %rcx, %rax
	0x48, 0x83, 0xc0, 0x01, //0x000017ff addq         $1, %rax
	//0x00001803 LBB0_304
	0x48, 0x85, 0xc0, //0x00001803 testq        %rax, %rax
	0x0f, 0x88, 0x59, 0x22, 0x00, 0x00, //0x00001806 js           LBB0_615
	0x49, 0x89, 0x06, //0x0000180c movq         %rax, (%r14)
	0x48, 0x8b, 0x45, 0xb0, //0x0000180f movq         $-80(%rbp), %rax
	0x48, 0x85, 0xc0, //0x00001813 testq        %rax, %rax
	0x0f, 0x8e, 0xdc, 0x21, 0x00, 0x00, //0x00001816 jle          LBB0_606
	//0x0000181c LBB0_306
	0x49, 0x8b, 0x45, 0x00, //0x0000181c movq         (%r13), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x00001820 cmpq         $4095, %rax
	0x0f, 0x8f, 0xc0, 0x21, 0x00, 0x00, //0x00001826 jg           LBB0_634
	0x48, 0x8d, 0x48, 0x01, //0x0000182c leaq         $1(%rax), %rcx
	0x49, 0x89, 0x4d, 0x00, //0x00001830 movq         %rcx, (%r13)
	0x49, 0xc7, 0x44, 0xc5, 0x08, 0x04, 0x00, 0x00, 0x00, //0x00001834 movq         $4, $8(%r13,%rax,8)
	0xe9, 0x7e, 0xe9, 0xff, 0xff, //0x0000183d jmp          LBB0_4
	//0x00001842 LBB0_308
	0x48, 0x8b, 0x55, 0xa8, //0x00001842 movq         $-88(%rbp), %rdx
	0x48, 0x8b, 0x52, 0x08, //0x00001846 movq         $8(%rdx), %rdx
	0x49, 0x8b, 0x0e, //0x0000184a movq         (%r14), %rcx
	0x48, 0x29, 0xca, //0x0000184d subq         %rcx, %rdx
	0x48, 0x89, 0x4d, 0xb0, //0x00001850 movq         %rcx, $-80(%rbp)
	0x49, 0x01, 0xcc, //0x00001854 addq         %rcx, %r12
	0x45, 0x31, 0xc0, //0x00001857 xorl         %r8d, %r8d
	0x45, 0x31, 0xd2, //0x0000185a xorl         %r10d, %r10d
	0x45, 0x31, 0xc9, //0x0000185d xorl         %r9d, %r9d
	0x45, 0x31, 0xf6, //0x00001860 xorl         %r14d, %r14d
	0x48, 0x83, 0xfa, 0x40, //0x00001863 cmpq         $64, %rdx
	0x48, 0x89, 0x55, 0xc0, //0x00001867 movq         %rdx, $-64(%rbp)
	0x0f, 0x8d, 0x4c, 0x01, 0x00, 0x00, //0x0000186b jge          LBB0_309
	//0x00001871 LBB0_318
	0x48, 0x85, 0xd2, //0x00001871 testq        %rdx, %rdx
	0x0f, 0x8e, 0xdd, 0x23, 0x00, 0x00, //0x00001874 jle          LBB0_648
	0x66, 0x0f, 0x6f, 0xf2, //0x0000187a movdqa       %xmm2, %xmm6
	0x66, 0x0f, 0xef, 0xd2, //0x0000187e pxor         %xmm2, %xmm2
	0xf3, 0x0f, 0x7f, 0x55, 0x80, //0x00001882 movdqu       %xmm2, $-128(%rbp)
	0xf3, 0x0f, 0x7f, 0x95, 0x70, 0xff, 0xff, 0xff, //0x00001887 movdqu       %xmm2, $-144(%rbp)
	0xf3, 0x0f, 0x7f, 0x95, 0x60, 0xff, 0xff, 0xff, //0x0000188f movdqu       %xmm2, $-160(%rbp)
	0xf3, 0x0f, 0x7f, 0x95, 0x50, 0xff, 0xff, 0xff, //0x00001897 movdqu       %xmm2, $-176(%rbp)
	0x4c, 0x89, 0xe7, //0x0000189f movq         %r12, %rdi
	0x44, 0x89, 0xe1, //0x000018a2 movl         %r12d, %ecx
	0x81, 0xe1, 0xff, 0x0f, 0x00, 0x00, //0x000018a5 andl         $4095, %ecx
	0x81, 0xf9, 0xc1, 0x0f, 0x00, 0x00, //0x000018ab cmpl         $4033, %ecx
	0x0f, 0x82, 0x3a, 0x00, 0x00, 0x00, //0x000018b1 jb           LBB0_322
	0x48, 0x83, 0x7d, 0xc0, 0x20, //0x000018b7 cmpq         $32, $-64(%rbp)
	0x0f, 0x82, 0x3b, 0x00, 0x00, 0x00, //0x000018bc jb           LBB0_323
	0x0f, 0x10, 0x1f, //0x000018c2 movups       (%rdi), %xmm3
	0x0f, 0x11, 0x9d, 0x50, 0xff, 0xff, 0xff, //0x000018c5 movups       %xmm3, $-176(%rbp)
	0xf3, 0x0f, 0x6f, 0x5f, 0x10, //0x000018cc movdqu       $16(%rdi), %xmm3
	0xf3, 0x0f, 0x7f, 0x9d, 0x60, 0xff, 0xff, 0xff, //0x000018d1 movdqu       %xmm3, $-160(%rbp)
	0x48, 0x83, 0xc7, 0x20, //0x000018d9 addq         $32, %rdi
	0x48, 0x8b, 0x4d, 0xc0, //0x000018dd movq         $-64(%rbp), %rcx
	0x48, 0x8d, 0x71, 0xe0, //0x000018e1 leaq         $-32(%rcx), %rsi
	0x48, 0x8d, 0x95, 0x70, 0xff, 0xff, 0xff, //0x000018e5 leaq         $-144(%rbp), %rdx
	0xe9, 0x17, 0x00, 0x00, 0x00, //0x000018ec jmp          LBB0_324
	//0x000018f1 LBB0_322
	0x66, 0x0f, 0x6f, 0xd6, //0x000018f1 movdqa       %xmm6, %xmm2
	0x49, 0x89, 0xfc, //0x000018f5 movq         %rdi, %r12
	0xe9, 0xc0, 0x00, 0x00, 0x00, //0x000018f8 jmp          LBB0_309
	//0x000018fd LBB0_323
	0x48, 0x8d, 0x95, 0x50, 0xff, 0xff, 0xff, //0x000018fd leaq         $-176(%rbp), %rdx
	0x48, 0x8b, 0x75, 0xc0, //0x00001904 movq         $-64(%rbp), %rsi
	//0x00001908 LBB0_324
	0x48, 0x83, 0xfe, 0x10, //0x00001908 cmpq         $16, %rsi
	0x0f, 0x82, 0x47, 0x00, 0x00, 0x00, //0x0000190c jb           LBB0_325
	0xf3, 0x0f, 0x6f, 0x1f, //0x00001912 movdqu       (%rdi), %xmm3
	0xf3, 0x0f, 0x7f, 0x1a, //0x00001916 movdqu       %xmm3, (%rdx)
	0x48, 0x83, 0xc7, 0x10, //0x0000191a addq         $16, %rdi
	0x48, 0x83, 0xc2, 0x10, //0x0000191e addq         $16, %rdx
	0x48, 0x83, 0xc6, 0xf0, //0x00001922 addq         $-16, %rsi
	0x48, 0x83, 0xfe, 0x08, //0x00001926 cmpq         $8, %rsi
	0x0f, 0x83, 0x33, 0x00, 0x00, 0x00, //0x0000192a jae          LBB0_332
	//0x00001930 LBB0_326
	0x48, 0x83, 0xfe, 0x04, //0x00001930 cmpq         $4, %rsi
	0x0f, 0x8c, 0x45, 0x00, 0x00, 0x00, //0x00001934 jl           LBB0_327
	//0x0000193a LBB0_333
	0x8b, 0x0f, //0x0000193a movl         (%rdi), %ecx
	0x89, 0x0a, //0x0000193c movl         %ecx, (%rdx)
	0x48, 0x83, 0xc7, 0x04, //0x0000193e addq         $4, %rdi
	0x48, 0x83, 0xc2, 0x04, //0x00001942 addq         $4, %rdx
	0x48, 0x83, 0xc6, 0xfc, //0x00001946 addq         $-4, %rsi
	0x48, 0x83, 0xfe, 0x02, //0x0000194a cmpq         $2, %rsi
	0x0f, 0x83, 0x35, 0x00, 0x00, 0x00, //0x0000194e jae          LBB0_328
	0xe9, 0x42, 0x00, 0x00, 0x00, //0x00001954 jmp          LBB0_329
	//0x00001959 LBB0_325
	0x48, 0x83, 0xfe, 0x08, //0x00001959 cmpq         $8, %rsi
	0x0f, 0x82, 0xcd, 0xff, 0xff, 0xff, //0x0000195d jb           LBB0_326
	//0x00001963 LBB0_332
	0x48, 0x8b, 0x0f, //0x00001963 movq         (%rdi), %rcx
	0x48, 0x89, 0x0a, //0x00001966 movq         %rcx, (%rdx)
	0x48, 0x83, 0xc7, 0x08, //0x00001969 addq         $8, %rdi
	0x48, 0x83, 0xc2, 0x08, //0x0000196d addq         $8, %rdx
	0x48, 0x83, 0xc6, 0xf8, //0x00001971 addq         $-8, %rsi
	0x48, 0x83, 0xfe, 0x04, //0x00001975 cmpq         $4, %rsi
	0x0f, 0x8d, 0xbb, 0xff, 0xff, 0xff, //0x00001979 jge          LBB0_333
	//0x0000197f LBB0_327
	0x48, 0x83, 0xfe, 0x02, //0x0000197f cmpq         $2, %rsi
	0x0f, 0x82, 0x12, 0x00, 0x00, 0x00, //0x00001983 jb           LBB0_329
	//0x00001989 LBB0_328
	0x0f, 0xb7, 0x0f, //0x00001989 movzwl       (%rdi), %ecx
	0x66, 0x89, 0x0a, //0x0000198c movw         %cx, (%rdx)
	0x48, 0x83, 0xc7, 0x02, //0x0000198f addq         $2, %rdi
	0x48, 0x83, 0xc2, 0x02, //0x00001993 addq         $2, %rdx
	0x48, 0x83, 0xc6, 0xfe, //0x00001997 addq         $-2, %rsi
	//0x0000199b LBB0_329
	0x48, 0x89, 0xf9, //0x0000199b movq         %rdi, %rcx
	0x4c, 0x8d, 0xa5, 0x50, 0xff, 0xff, 0xff, //0x0000199e leaq         $-176(%rbp), %r12
	0x48, 0x85, 0xf6, //0x000019a5 testq        %rsi, %rsi
	0x66, 0x0f, 0x6f, 0xd6, //0x000019a8 movdqa       %xmm6, %xmm2
	0x0f, 0x84, 0x0b, 0x00, 0x00, 0x00, //0x000019ac je           LBB0_309
	0x8a, 0x09, //0x000019b2 movb         (%rcx), %cl
	0x88, 0x0a, //0x000019b4 movb         %cl, (%rdx)
	0x4c, 0x8d, 0xa5, 0x50, 0xff, 0xff, 0xff, //0x000019b6 leaq         $-176(%rbp), %r12
	//0x000019bd LBB0_309
	0x66, 0x44, 0x0f, 0x6f, 0xc2, //0x000019bd movdqa       %xmm2, %xmm8
	0xf3, 0x41, 0x0f, 0x6f, 0x24, 0x24, //0x000019c2 movdqu       (%r12), %xmm4
	0xf3, 0x41, 0x0f, 0x6f, 0x7c, 0x24, 0x10, //0x000019c8 movdqu       $16(%r12), %xmm7
	0xf3, 0x41, 0x0f, 0x6f, 0x74, 0x24, 0x20, //0x000019cf movdqu       $32(%r12), %xmm6
	0xf3, 0x41, 0x0f, 0x6f, 0x6c, 0x24, 0x30, //0x000019d6 movdqu       $48(%r12), %xmm5
	0x66, 0x0f, 0x6f, 0xdc, //0x000019dd movdqa       %xmm4, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x000019e1 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xd3, //0x000019e5 pmovmskb     %xmm3, %edx
	0x66, 0x0f, 0x6f, 0xdf, //0x000019e9 movdqa       %xmm7, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x000019ed pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xcb, //0x000019f1 pmovmskb     %xmm3, %ecx
	0x66, 0x0f, 0x6f, 0xde, //0x000019f5 movdqa       %xmm6, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x000019f9 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xf3, //0x000019fd pmovmskb     %xmm3, %esi
	0x66, 0x0f, 0x6f, 0xdd, //0x00001a01 movdqa       %xmm5, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x00001a05 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xfb, //0x00001a09 pmovmskb     %xmm3, %edi
	0x48, 0xc1, 0xe7, 0x30, //0x00001a0d shlq         $48, %rdi
	0x48, 0xc1, 0xe6, 0x20, //0x00001a11 shlq         $32, %rsi
	0x48, 0x09, 0xfe, //0x00001a15 orq          %rdi, %rsi
	0x48, 0xc1, 0xe1, 0x10, //0x00001a18 shlq         $16, %rcx
	0x48, 0x09, 0xf1, //0x00001a1c orq          %rsi, %rcx
	0x48, 0x09, 0xca, //0x00001a1f orq          %rcx, %rdx
	0x48, 0x89, 0xd1, //0x00001a22 movq         %rdx, %rcx
	0x66, 0x45, 0x0f, 0x6f, 0xee, //0x00001a25 movdqa       %xmm14, %xmm13
	0x4c, 0x09, 0xd1, //0x00001a2a orq          %r10, %rcx
	0x0f, 0x85, 0x0f, 0x00, 0x00, 0x00, //0x00001a2d jne          LBB0_311
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00001a33 movq         $-1, %rdx
	0x45, 0x31, 0xd2, //0x00001a3a xorl         %r10d, %r10d
	0xe9, 0x46, 0x00, 0x00, 0x00, //0x00001a3d jmp          LBB0_312
	//0x00001a42 LBB0_311
	0x4c, 0x89, 0xd1, //0x00001a42 movq         %r10, %rcx
	0x48, 0xf7, 0xd1, //0x00001a45 notq         %rcx
	0x48, 0x21, 0xd1, //0x00001a48 andq         %rdx, %rcx
	0x4c, 0x8d, 0x1c, 0x09, //0x00001a4b leaq         (%rcx,%rcx), %r11
	0x4d, 0x09, 0xd3, //0x00001a4f orq          %r10, %r11
	0x4c, 0x89, 0xdf, //0x00001a52 movq         %r11, %rdi
	0x48, 0xf7, 0xd7, //0x00001a55 notq         %rdi
	0x48, 0xbe, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001a58 movabsq      $-6148914691236517206, %rsi
	0x48, 0x21, 0xf2, //0x00001a62 andq         %rsi, %rdx
	0x48, 0x21, 0xfa, //0x00001a65 andq         %rdi, %rdx
	0x45, 0x31, 0xd2, //0x00001a68 xorl         %r10d, %r10d
	0x48, 0x01, 0xca, //0x00001a6b addq         %rcx, %rdx
	0x41, 0x0f, 0x92, 0xc2, //0x00001a6e setb         %r10b
	0x48, 0x01, 0xd2, //0x00001a72 addq         %rdx, %rdx
	0x48, 0xb9, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00001a75 movabsq      $6148914691236517205, %rcx
	0x48, 0x31, 0xca, //0x00001a7f xorq         %rcx, %rdx
	0x4c, 0x21, 0xda, //0x00001a82 andq         %r11, %rdx
	0x48, 0xf7, 0xd2, //0x00001a85 notq         %rdx
	//0x00001a88 LBB0_312
	0x66, 0x0f, 0x6f, 0xdd, //0x00001a88 movdqa       %xmm5, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x00001a8c pcmpeqb      %xmm0, %xmm3
	0x66, 0x0f, 0xd7, 0xcb, //0x00001a90 pmovmskb     %xmm3, %ecx
	0x48, 0xc1, 0xe1, 0x30, //0x00001a94 shlq         $48, %rcx
	0x66, 0x0f, 0x6f, 0xde, //0x00001a98 movdqa       %xmm6, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x00001a9c pcmpeqb      %xmm0, %xmm3
	0x66, 0x0f, 0xd7, 0xf3, //0x00001aa0 pmovmskb     %xmm3, %esi
	0x48, 0xc1, 0xe6, 0x20, //0x00001aa4 shlq         $32, %rsi
	0x48, 0x09, 0xce, //0x00001aa8 orq          %rcx, %rsi
	0x66, 0x0f, 0x6f, 0xdf, //0x00001aab movdqa       %xmm7, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x00001aaf pcmpeqb      %xmm0, %xmm3
	0x66, 0x0f, 0xd7, 0xcb, //0x00001ab3 pmovmskb     %xmm3, %ecx
	0x48, 0xc1, 0xe1, 0x10, //0x00001ab7 shlq         $16, %rcx
	0x48, 0x09, 0xf1, //0x00001abb orq          %rsi, %rcx
	0x66, 0x0f, 0x6f, 0xdc, //0x00001abe movdqa       %xmm4, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x00001ac2 pcmpeqb      %xmm0, %xmm3
	0x66, 0x0f, 0xd7, 0xf3, //0x00001ac6 pmovmskb     %xmm3, %esi
	0x48, 0x09, 0xce, //0x00001aca orq          %rcx, %rsi
	0x48, 0x21, 0xd6, //0x00001acd andq         %rdx, %rsi
	0x66, 0x48, 0x0f, 0x6e, 0xde, //0x00001ad0 movq         %rsi, %xmm3
	0x66, 0x41, 0x0f, 0x3a, 0x44, 0xda, 0x00, //0x00001ad5 pclmulqdq    $0, %xmm10, %xmm3
	0x66, 0x49, 0x0f, 0x7e, 0xdb, //0x00001adc movq         %xmm3, %r11
	0x4d, 0x31, 0xc3, //0x00001ae1 xorq         %r8, %r11
	0x66, 0x0f, 0x6f, 0xdc, //0x00001ae4 movdqa       %xmm4, %xmm3
	0xf3, 0x44, 0x0f, 0x6f, 0x1d, 0x6f, 0xe5, 0xff, 0xff, //0x00001ae8 movdqu       $-6801(%rip), %xmm11  /* LCPI0_6+0(%rip) */
	0x66, 0x41, 0x0f, 0x74, 0xdb, //0x00001af1 pcmpeqb      %xmm11, %xmm3
	0x66, 0x44, 0x0f, 0xd7, 0xeb, //0x00001af6 pmovmskb     %xmm3, %r13d
	0x66, 0x0f, 0x6f, 0xdf, //0x00001afb movdqa       %xmm7, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xdb, //0x00001aff pcmpeqb      %xmm11, %xmm3
	0x66, 0x0f, 0xd7, 0xcb, //0x00001b04 pmovmskb     %xmm3, %ecx
	0x66, 0x0f, 0x6f, 0xde, //0x00001b08 movdqa       %xmm6, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xdb, //0x00001b0c pcmpeqb      %xmm11, %xmm3
	0x66, 0x0f, 0xd7, 0xd3, //0x00001b11 pmovmskb     %xmm3, %edx
	0x66, 0x0f, 0x6f, 0xdd, //0x00001b15 movdqa       %xmm5, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xdb, //0x00001b19 pcmpeqb      %xmm11, %xmm3
	0x66, 0x0f, 0xd7, 0xf3, //0x00001b1e pmovmskb     %xmm3, %esi
	0x48, 0xc1, 0xe6, 0x30, //0x00001b22 shlq         $48, %rsi
	0x48, 0xc1, 0xe2, 0x20, //0x00001b26 shlq         $32, %rdx
	0x48, 0x09, 0xf2, //0x00001b2a orq          %rsi, %rdx
	0x48, 0xc1, 0xe1, 0x10, //0x00001b2d shlq         $16, %rcx
	0x48, 0x09, 0xd1, //0x00001b31 orq          %rdx, %rcx
	0x49, 0x09, 0xcd, //0x00001b34 orq          %rcx, %r13
	0x4d, 0x89, 0xd8, //0x00001b37 movq         %r11, %r8
	0x49, 0xf7, 0xd0, //0x00001b3a notq         %r8
	0x4d, 0x21, 0xc5, //0x00001b3d andq         %r8, %r13
	0x66, 0x41, 0x0f, 0x6f, 0xd0, //0x00001b40 movdqa       %xmm8, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xe0, //0x00001b45 pcmpeqb      %xmm8, %xmm4
	0x66, 0x0f, 0xd7, 0xd4, //0x00001b4a pmovmskb     %xmm4, %edx
	0x66, 0x41, 0x0f, 0x74, 0xf8, //0x00001b4e pcmpeqb      %xmm8, %xmm7
	0x66, 0x0f, 0xd7, 0xf7, //0x00001b53 pmovmskb     %xmm7, %esi
	0x66, 0x41, 0x0f, 0x74, 0xf0, //0x00001b57 pcmpeqb      %xmm8, %xmm6
	0x66, 0x0f, 0xd7, 0xce, //0x00001b5c pmovmskb     %xmm6, %ecx
	0x66, 0x41, 0x0f, 0x74, 0xe8, //0x00001b60 pcmpeqb      %xmm8, %xmm5
	0x66, 0x44, 0x0f, 0xd7, 0xfd, //0x00001b65 pmovmskb     %xmm5, %r15d
	0x49, 0xc1, 0xe7, 0x30, //0x00001b6a shlq         $48, %r15
	0x48, 0xc1, 0xe1, 0x20, //0x00001b6e shlq         $32, %rcx
	0x4c, 0x09, 0xf9, //0x00001b72 orq          %r15, %rcx
	0x48, 0xc1, 0xe6, 0x10, //0x00001b75 shlq         $16, %rsi
	0x48, 0x09, 0xce, //0x00001b79 orq          %rcx, %rsi
	0x48, 0x09, 0xf2, //0x00001b7c orq          %rsi, %rdx
	0x66, 0x45, 0x0f, 0x6f, 0xf5, //0x00001b7f movdqa       %xmm13, %xmm14
	0x4c, 0x21, 0xc2, //0x00001b84 andq         %r8, %rdx
	0x0f, 0x84, 0xaa, 0x00, 0x00, 0x00, //0x00001b87 je           LBB0_316
	0xf3, 0x44, 0x0f, 0x6f, 0x1d, 0x5a, 0xe5, 0xff, 0xff, //0x00001b8d movdqu       $-6822(%rip), %xmm11  /* LCPI0_15+0(%rip) */
	0x66, 0x41, 0x0f, 0x6f, 0xd0, //0x00001b96 movdqa       %xmm8, %xmm2
	0xf3, 0x44, 0x0f, 0x6f, 0x2d, 0x9c, 0xe4, 0xff, 0xff, //0x00001b9b movdqu       $-7012(%rip), %xmm13  /* LCPI0_4+0(%rip) */
	0x49, 0xb8, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, //0x00001ba4 movabsq      $3689348814741910323, %r8
	0xf3, 0x44, 0x0f, 0x6f, 0x05, 0x99, 0xe4, 0xff, 0xff, //0x00001bae movdqu       $-7015(%rip), %xmm8  /* LCPI0_5+0(%rip) */
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001bb7 .p2align 4, 0x90
	//0x00001bc0 LBB0_314
	0x48, 0x8d, 0x7a, 0xff, //0x00001bc0 leaq         $-1(%rdx), %rdi
	0x48, 0x89, 0xf9, //0x00001bc4 movq         %rdi, %rcx
	0x4c, 0x21, 0xe9, //0x00001bc7 andq         %r13, %rcx
	0x48, 0x89, 0xce, //0x00001bca movq         %rcx, %rsi
	0x48, 0xd1, 0xee, //0x00001bcd shrq         %rsi
	0x48, 0xbb, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00001bd0 movabsq      $6148914691236517205, %rbx
	0x48, 0x21, 0xde, //0x00001bda andq         %rbx, %rsi
	0x48, 0x29, 0xf1, //0x00001bdd subq         %rsi, %rcx
	0x48, 0x89, 0xce, //0x00001be0 movq         %rcx, %rsi
	0x4c, 0x21, 0xc6, //0x00001be3 andq         %r8, %rsi
	0x48, 0xc1, 0xe9, 0x02, //0x00001be6 shrq         $2, %rcx
	0x4c, 0x21, 0xc1, //0x00001bea andq         %r8, %rcx
	0x48, 0x01, 0xf1, //0x00001bed addq         %rsi, %rcx
	0x48, 0x89, 0xce, //0x00001bf0 movq         %rcx, %rsi
	0x48, 0xc1, 0xee, 0x04, //0x00001bf3 shrq         $4, %rsi
	0x48, 0x01, 0xce, //0x00001bf7 addq         %rcx, %rsi
	0x48, 0xb9, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, //0x00001bfa movabsq      $1085102592571150095, %rcx
	0x48, 0x21, 0xce, //0x00001c04 andq         %rcx, %rsi
	0x48, 0xb9, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, //0x00001c07 movabsq      $72340172838076673, %rcx
	0x48, 0x0f, 0xaf, 0xf1, //0x00001c11 imulq        %rcx, %rsi
	0x48, 0xc1, 0xee, 0x38, //0x00001c15 shrq         $56, %rsi
	0x4c, 0x01, 0xce, //0x00001c19 addq         %r9, %rsi
	0x4c, 0x39, 0xf6, //0x00001c1c cmpq         %r14, %rsi
	0x0f, 0x86, 0xd4, 0x06, 0x00, 0x00, //0x00001c1f jbe          LBB0_382
	0x49, 0x83, 0xc6, 0x01, //0x00001c25 addq         $1, %r14
	0x48, 0x21, 0xfa, //0x00001c29 andq         %rdi, %rdx
	0x0f, 0x85, 0x8e, 0xff, 0xff, 0xff, //0x00001c2c jne          LBB0_314
	0xe9, 0x0f, 0x00, 0x00, 0x00, //0x00001c32 jmp          LBB0_317
	//0x00001c37 LBB0_316
	0x66, 0x41, 0x0f, 0x6f, 0xd0, //0x00001c37 movdqa       %xmm8, %xmm2
	0x49, 0xb8, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, //0x00001c3c movabsq      $3689348814741910323, %r8
	//0x00001c46 LBB0_317
	0x49, 0xc1, 0xfb, 0x3f, //0x00001c46 sarq         $63, %r11
	0x4c, 0x89, 0xe9, //0x00001c4a movq         %r13, %rcx
	0x48, 0xd1, 0xe9, //0x00001c4d shrq         %rcx
	0x48, 0xba, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00001c50 movabsq      $6148914691236517205, %rdx
	0x48, 0x21, 0xd1, //0x00001c5a andq         %rdx, %rcx
	0x49, 0x29, 0xcd, //0x00001c5d subq         %rcx, %r13
	0x4c, 0x89, 0xe9, //0x00001c60 movq         %r13, %rcx
	0x4c, 0x21, 0xc1, //0x00001c63 andq         %r8, %rcx
	0x49, 0xc1, 0xed, 0x02, //0x00001c66 shrq         $2, %r13
	0x4d, 0x21, 0xc5, //0x00001c6a andq         %r8, %r13
	0x49, 0x01, 0xcd, //0x00001c6d addq         %rcx, %r13
	0x4c, 0x89, 0xe9, //0x00001c70 movq         %r13, %rcx
	0x48, 0xc1, 0xe9, 0x04, //0x00001c73 shrq         $4, %rcx
	0x4c, 0x01, 0xe9, //0x00001c77 addq         %r13, %rcx
	0x48, 0xba, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, //0x00001c7a movabsq      $1085102592571150095, %rdx
	0x48, 0x21, 0xd1, //0x00001c84 andq         %rdx, %rcx
	0x48, 0xba, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, //0x00001c87 movabsq      $72340172838076673, %rdx
	0x48, 0x0f, 0xaf, 0xca, //0x00001c91 imulq        %rdx, %rcx
	0x48, 0xc1, 0xe9, 0x38, //0x00001c95 shrq         $56, %rcx
	0x49, 0x01, 0xc9, //0x00001c99 addq         %rcx, %r9
	0x49, 0x83, 0xc4, 0x40, //0x00001c9c addq         $64, %r12
	0x48, 0x8b, 0x55, 0xc0, //0x00001ca0 movq         $-64(%rbp), %rdx
	0x48, 0x83, 0xc2, 0xc0, //0x00001ca4 addq         $-64, %rdx
	0x4d, 0x89, 0xd8, //0x00001ca8 movq         %r11, %r8
	0x48, 0x83, 0xfa, 0x40, //0x00001cab cmpq         $64, %rdx
	0x48, 0x89, 0x55, 0xc0, //0x00001caf movq         %rdx, $-64(%rbp)
	0x0f, 0x8d, 0x04, 0xfd, 0xff, 0xff, //0x00001cb3 jge          LBB0_309
	0xe9, 0xb3, 0xfb, 0xff, 0xff, //0x00001cb9 jmp          LBB0_318
	//0x00001cbe LBB0_334
	0x48, 0x8b, 0x4d, 0xa8, //0x00001cbe movq         $-88(%rbp), %rcx
	0x4c, 0x8b, 0x49, 0x08, //0x00001cc2 movq         $8(%rcx), %r9
	0x4d, 0x8b, 0x06, //0x00001cc6 movq         (%r14), %r8
	0x4f, 0x8d, 0x14, 0x04, //0x00001cc9 leaq         (%r12,%r8), %r10
	0x4d, 0x29, 0xc1, //0x00001ccd subq         %r8, %r9
	0x49, 0x83, 0xf9, 0x20, //0x00001cd0 cmpq         $32, %r9
	0x0f, 0x8c, 0xdb, 0x00, 0x00, 0x00, //0x00001cd4 jl           LBB0_343
	0x41, 0xbe, 0x20, 0x00, 0x00, 0x00, //0x00001cda movl         $32, %r14d
	0x31, 0xd2, //0x00001ce0 xorl         %edx, %edx
	0x45, 0x31, 0xdb, //0x00001ce2 xorl         %r11d, %r11d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001ce5 .p2align 4, 0x90
	//0x00001cf0 LBB0_336
	0xf3, 0x41, 0x0f, 0x6f, 0x1c, 0x12, //0x00001cf0 movdqu       (%r10,%rdx), %xmm3
	0xf3, 0x41, 0x0f, 0x6f, 0x64, 0x12, 0x10, //0x00001cf6 movdqu       $16(%r10,%rdx), %xmm4
	0x66, 0x0f, 0x6f, 0xeb, //0x00001cfd movdqa       %xmm3, %xmm5
	0x66, 0x0f, 0x74, 0xe8, //0x00001d01 pcmpeqb      %xmm0, %xmm5
	0x66, 0x0f, 0xd7, 0xf5, //0x00001d05 pmovmskb     %xmm5, %esi
	0x66, 0x0f, 0x6f, 0xec, //0x00001d09 movdqa       %xmm4, %xmm5
	0x66, 0x0f, 0x74, 0xe8, //0x00001d0d pcmpeqb      %xmm0, %xmm5
	0x66, 0x0f, 0xd7, 0xfd, //0x00001d11 pmovmskb     %xmm5, %edi
	0x48, 0xc1, 0xe7, 0x10, //0x00001d15 shlq         $16, %rdi
	0x48, 0x09, 0xf7, //0x00001d19 orq          %rsi, %rdi
	0x66, 0x0f, 0x74, 0xd9, //0x00001d1c pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xcb, //0x00001d20 pmovmskb     %xmm3, %ecx
	0x66, 0x0f, 0x74, 0xe1, //0x00001d24 pcmpeqb      %xmm1, %xmm4
	0x66, 0x0f, 0xd7, 0xf4, //0x00001d28 pmovmskb     %xmm4, %esi
	0x48, 0xc1, 0xe6, 0x10, //0x00001d2c shlq         $16, %rsi
	0x48, 0x09, 0xce, //0x00001d30 orq          %rcx, %rsi
	0x48, 0x89, 0xf1, //0x00001d33 movq         %rsi, %rcx
	0x4c, 0x09, 0xd9, //0x00001d36 orq          %r11, %rcx
	0x0f, 0x84, 0x41, 0x00, 0x00, 0x00, //0x00001d39 je           LBB0_338
	0x44, 0x89, 0xd9, //0x00001d3f movl         %r11d, %ecx
	0xf7, 0xd1, //0x00001d42 notl         %ecx
	0x21, 0xf1, //0x00001d44 andl         %esi, %ecx
	0x44, 0x8d, 0x3c, 0x09, //0x00001d46 leal         (%rcx,%rcx), %r15d
	0x45, 0x09, 0xdf, //0x00001d4a orl          %r11d, %r15d
	0x44, 0x89, 0xfb, //0x00001d4d movl         %r15d, %ebx
	0xf7, 0xd3, //0x00001d50 notl         %ebx
	0x21, 0xf3, //0x00001d52 andl         %esi, %ebx
	0x81, 0xe3, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001d54 andl         $-1431655766, %ebx
	0x45, 0x31, 0xdb, //0x00001d5a xorl         %r11d, %r11d
	0x01, 0xcb, //0x00001d5d addl         %ecx, %ebx
	0x41, 0x0f, 0x92, 0xc3, //0x00001d5f setb         %r11b
	0x01, 0xdb, //0x00001d63 addl         %ebx, %ebx
	0x81, 0xf3, 0x55, 0x55, 0x55, 0x55, //0x00001d65 xorl         $1431655765, %ebx
	0x44, 0x21, 0xfb, //0x00001d6b andl         %r15d, %ebx
	0xf7, 0xd3, //0x00001d6e notl         %ebx
	0x21, 0xdf, //0x00001d70 andl         %ebx, %edi
	0xe9, 0x0c, 0x00, 0x00, 0x00, //0x00001d72 jmp          LBB0_339
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001d77 .p2align 4, 0x90
	//0x00001d80 LBB0_338
	0x45, 0x31, 0xdb, //0x00001d80 xorl         %r11d, %r11d
	//0x00001d83 LBB0_339
	0x48, 0x85, 0xff, //0x00001d83 testq        %rdi, %rdi
	0x0f, 0x85, 0xe5, 0xed, 0xff, 0xff, //0x00001d86 jne          LBB0_129
	0x48, 0x83, 0xc2, 0x20, //0x00001d8c addq         $32, %rdx
	0x4b, 0x8d, 0x0c, 0x31, //0x00001d90 leaq         (%r9,%r14), %rcx
	0x48, 0x83, 0xc1, 0xe0, //0x00001d94 addq         $-32, %rcx
	0x49, 0x83, 0xc6, 0xe0, //0x00001d98 addq         $-32, %r14
	0x48, 0x83, 0xf9, 0x3f, //0x00001d9c cmpq         $63, %rcx
	0x0f, 0x8f, 0x4a, 0xff, 0xff, 0xff, //0x00001da0 jg           LBB0_336
	0x4d, 0x85, 0xdb, //0x00001da6 testq        %r11, %r11
	0x0f, 0x85, 0x4d, 0x19, 0x00, 0x00, //0x00001da9 jne          LBB0_570
	0x49, 0x01, 0xd2, //0x00001daf addq         %rdx, %r10
	0x49, 0x29, 0xd1, //0x00001db2 subq         %rdx, %r9
	//0x00001db5 LBB0_343
	0x4c, 0x8b, 0x75, 0xd0, //0x00001db5 movq         $-48(%rbp), %r14
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00001db9 movabsq      $4294977024, %r11
	0x4d, 0x85, 0xc9, //0x00001dc3 testq        %r9, %r9
	0x0f, 0x8f, 0x8e, 0x19, 0x00, 0x00, //0x00001dc6 jg           LBB0_574
	0xe9, 0x76, 0x1c, 0x00, 0x00, //0x00001dcc jmp          LBB0_638
	//0x00001dd1 LBB0_344
	0x48, 0x89, 0xfa, //0x00001dd1 movq         %rdi, %rdx
	0x48, 0x29, 0xc2, //0x00001dd4 subq         %rax, %rdx
	0x48, 0x83, 0xfa, 0x10, //0x00001dd7 cmpq         $16, %rdx
	0x0f, 0x82, 0x3c, 0x12, 0x00, 0x00, //0x00001ddb jb           LBB0_498
	0x48, 0x89, 0xc2, //0x00001de1 movq         %rax, %rdx
	0x48, 0xf7, 0xda, //0x00001de4 negq         %rdx
	0x48, 0x89, 0xc1, //0x00001de7 movq         %rax, %rcx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001dea .p2align 4, 0x90
	//0x00001df0 LBB0_346
	0xf3, 0x41, 0x0f, 0x6f, 0x1c, 0x0c, //0x00001df0 movdqu       (%r12,%rcx), %xmm3
	0x66, 0x0f, 0x6f, 0xe3, //0x00001df6 movdqa       %xmm3, %xmm4
	0x66, 0x41, 0x0f, 0x74, 0xe6, //0x00001dfa pcmpeqb      %xmm14, %xmm4
	0x66, 0x41, 0x0f, 0xdb, 0xd9, //0x00001dff pand         %xmm9, %xmm3
	0x66, 0x0f, 0x74, 0xda, //0x00001e04 pcmpeqb      %xmm2, %xmm3
	0x66, 0x0f, 0xeb, 0xdc, //0x00001e08 por          %xmm4, %xmm3
	0x66, 0x0f, 0xd7, 0xf3, //0x00001e0c pmovmskb     %xmm3, %esi
	0x85, 0xf6, //0x00001e10 testl        %esi, %esi
	0x0f, 0x85, 0x33, 0x05, 0x00, 0x00, //0x00001e12 jne          LBB0_605
	0x48, 0x83, 0xc1, 0x10, //0x00001e18 addq         $16, %rcx
	0x48, 0x8d, 0x34, 0x17, //0x00001e1c leaq         (%rdi,%rdx), %rsi
	0x48, 0x83, 0xc6, 0xf0, //0x00001e20 addq         $-16, %rsi
	0x48, 0x83, 0xc2, 0xf0, //0x00001e24 addq         $-16, %rdx
	0x48, 0x83, 0xfe, 0x0f, //0x00001e28 cmpq         $15, %rsi
	0x0f, 0x87, 0xbe, 0xff, 0xff, 0xff, //0x00001e2c ja           LBB0_346
	0x4c, 0x89, 0xe1, //0x00001e32 movq         %r12, %rcx
	0x48, 0x29, 0xd1, //0x00001e35 subq         %rdx, %rcx
	0x48, 0x01, 0xd7, //0x00001e38 addq         %rdx, %rdi
	0x48, 0x89, 0xfa, //0x00001e3b movq         %rdi, %rdx
	0x48, 0x85, 0xd2, //0x00001e3e testq        %rdx, %rdx
	0x0f, 0x84, 0xe3, 0x11, 0x00, 0x00, //0x00001e41 je           LBB0_499
	//0x00001e47 LBB0_349
	0x48, 0x8d, 0x3c, 0x11, //0x00001e47 leaq         (%rcx,%rdx), %rdi
	0x31, 0xf6, //0x00001e4b xorl         %esi, %esi
	//0x00001e4d LBB0_350
	0x0f, 0xb6, 0x1c, 0x31, //0x00001e4d movzbl       (%rcx,%rsi), %ebx
	0x80, 0xfb, 0x2c, //0x00001e51 cmpb         $44, %bl
	0x0f, 0x84, 0x81, 0x0d, 0x00, 0x00, //0x00001e54 je           LBB0_462
	0x80, 0xfb, 0x7d, //0x00001e5a cmpb         $125, %bl
	0x0f, 0x84, 0x78, 0x0d, 0x00, 0x00, //0x00001e5d je           LBB0_462
	0x80, 0xfb, 0x5d, //0x00001e63 cmpb         $93, %bl
	0x0f, 0x84, 0x6f, 0x0d, 0x00, 0x00, //0x00001e66 je           LBB0_462
	0x48, 0x83, 0xc6, 0x01, //0x00001e6c addq         $1, %rsi
	0x48, 0x39, 0xf2, //0x00001e70 cmpq         %rsi, %rdx
	0x0f, 0x85, 0xd4, 0xff, 0xff, 0xff, //0x00001e73 jne          LBB0_350
	0x66, 0x41, 0x0f, 0x6f, 0xf5, //0x00001e79 movdqa       %xmm13, %xmm6
	0x66, 0x0f, 0x6f, 0xea, //0x00001e7e movdqa       %xmm2, %xmm5
	0x48, 0x89, 0xf9, //0x00001e82 movq         %rdi, %rcx
	//0x00001e85 LBB0_355
	0x4c, 0x29, 0xe1, //0x00001e85 subq         %r12, %rcx
	0x4c, 0x8b, 0x75, 0xd0, //0x00001e88 movq         $-48(%rbp), %r14
	0x4c, 0x8b, 0x6d, 0xb8, //0x00001e8c movq         $-72(%rbp), %r13
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00001e90 movabsq      $4294977024, %r11
	0x66, 0x0f, 0x6f, 0xd5, //0x00001e9a movdqa       %xmm5, %xmm2
	0x66, 0x44, 0x0f, 0x6f, 0xee, //0x00001e9e movdqa       %xmm6, %xmm13
	0x49, 0x89, 0x0e, //0x00001ea3 movq         %rcx, (%r14)
	0x48, 0x85, 0xc0, //0x00001ea6 testq        %rax, %rax
	0x0f, 0x8f, 0x11, 0xe3, 0xff, 0xff, //0x00001ea9 jg           LBB0_4
	0xe9, 0x44, 0x1b, 0x00, 0x00, //0x00001eaf jmp          LBB0_606
	//0x00001eb4 LBB0_356
	0x48, 0x8b, 0x55, 0xa8, //0x00001eb4 movq         $-88(%rbp), %rdx
	0x48, 0x8b, 0x52, 0x08, //0x00001eb8 movq         $8(%rdx), %rdx
	0x49, 0x8b, 0x0e, //0x00001ebc movq         (%r14), %rcx
	0x48, 0x29, 0xca, //0x00001ebf subq         %rcx, %rdx
	0x48, 0x89, 0x4d, 0xb0, //0x00001ec2 movq         %rcx, $-80(%rbp)
	0x49, 0x01, 0xcc, //0x00001ec6 addq         %rcx, %r12
	0x45, 0x31, 0xc0, //0x00001ec9 xorl         %r8d, %r8d
	0x45, 0x31, 0xd2, //0x00001ecc xorl         %r10d, %r10d
	0x45, 0x31, 0xc9, //0x00001ecf xorl         %r9d, %r9d
	0x45, 0x31, 0xf6, //0x00001ed2 xorl         %r14d, %r14d
	0x48, 0x83, 0xfa, 0x40, //0x00001ed5 cmpq         $64, %rdx
	0x48, 0x89, 0x55, 0xc0, //0x00001ed9 movq         %rdx, $-64(%rbp)
	0x0f, 0x8d, 0x5b, 0x01, 0x00, 0x00, //0x00001edd jge          LBB0_357
	//0x00001ee3 LBB0_366
	0x48, 0x85, 0xd2, //0x00001ee3 testq        %rdx, %rdx
	0x0f, 0x8e, 0x6b, 0x1d, 0x00, 0x00, //0x00001ee6 jle          LBB0_648
	0x66, 0x41, 0x0f, 0x6f, 0xfd, //0x00001eec movdqa       %xmm13, %xmm7
	0x66, 0x0f, 0x6f, 0xf2, //0x00001ef1 movdqa       %xmm2, %xmm6
	0x66, 0x0f, 0xef, 0xd2, //0x00001ef5 pxor         %xmm2, %xmm2
	0xf3, 0x0f, 0x7f, 0x55, 0x80, //0x00001ef9 movdqu       %xmm2, $-128(%rbp)
	0xf3, 0x0f, 0x7f, 0x95, 0x70, 0xff, 0xff, 0xff, //0x00001efe movdqu       %xmm2, $-144(%rbp)
	0xf3, 0x0f, 0x7f, 0x95, 0x60, 0xff, 0xff, 0xff, //0x00001f06 movdqu       %xmm2, $-160(%rbp)
	0xf3, 0x0f, 0x7f, 0x95, 0x50, 0xff, 0xff, 0xff, //0x00001f0e movdqu       %xmm2, $-176(%rbp)
	0x4c, 0x89, 0xe7, //0x00001f16 movq         %r12, %rdi
	0x44, 0x89, 0xe1, //0x00001f19 movl         %r12d, %ecx
	0x81, 0xe1, 0xff, 0x0f, 0x00, 0x00, //0x00001f1c andl         $4095, %ecx
	0x81, 0xf9, 0xc1, 0x0f, 0x00, 0x00, //0x00001f22 cmpl         $4033, %ecx
	0x0f, 0x82, 0x3a, 0x00, 0x00, 0x00, //0x00001f28 jb           LBB0_370
	0x48, 0x83, 0x7d, 0xc0, 0x20, //0x00001f2e cmpq         $32, $-64(%rbp)
	0x0f, 0x82, 0x40, 0x00, 0x00, 0x00, //0x00001f33 jb           LBB0_371
	0x0f, 0x10, 0x1f, //0x00001f39 movups       (%rdi), %xmm3
	0x0f, 0x11, 0x9d, 0x50, 0xff, 0xff, 0xff, //0x00001f3c movups       %xmm3, $-176(%rbp)
	0xf3, 0x0f, 0x6f, 0x5f, 0x10, //0x00001f43 movdqu       $16(%rdi), %xmm3
	0xf3, 0x0f, 0x7f, 0x9d, 0x60, 0xff, 0xff, 0xff, //0x00001f48 movdqu       %xmm3, $-160(%rbp)
	0x48, 0x83, 0xc7, 0x20, //0x00001f50 addq         $32, %rdi
	0x48, 0x8b, 0x4d, 0xc0, //0x00001f54 movq         $-64(%rbp), %rcx
	0x48, 0x8d, 0x71, 0xe0, //0x00001f58 leaq         $-32(%rcx), %rsi
	0x48, 0x8d, 0x95, 0x70, 0xff, 0xff, 0xff, //0x00001f5c leaq         $-144(%rbp), %rdx
	0xe9, 0x1c, 0x00, 0x00, 0x00, //0x00001f63 jmp          LBB0_372
	//0x00001f68 LBB0_370
	0x66, 0x0f, 0x6f, 0xd6, //0x00001f68 movdqa       %xmm6, %xmm2
	0x66, 0x44, 0x0f, 0x6f, 0xef, //0x00001f6c movdqa       %xmm7, %xmm13
	0x49, 0x89, 0xfc, //0x00001f71 movq         %rdi, %r12
	0xe9, 0xc5, 0x00, 0x00, 0x00, //0x00001f74 jmp          LBB0_357
	//0x00001f79 LBB0_371
	0x48, 0x8d, 0x95, 0x50, 0xff, 0xff, 0xff, //0x00001f79 leaq         $-176(%rbp), %rdx
	0x48, 0x8b, 0x75, 0xc0, //0x00001f80 movq         $-64(%rbp), %rsi
	//0x00001f84 LBB0_372
	0x48, 0x83, 0xfe, 0x10, //0x00001f84 cmpq         $16, %rsi
	0x0f, 0x82, 0x47, 0x00, 0x00, 0x00, //0x00001f88 jb           LBB0_373
	0xf3, 0x0f, 0x6f, 0x1f, //0x00001f8e movdqu       (%rdi), %xmm3
	0xf3, 0x0f, 0x7f, 0x1a, //0x00001f92 movdqu       %xmm3, (%rdx)
	0x48, 0x83, 0xc7, 0x10, //0x00001f96 addq         $16, %rdi
	0x48, 0x83, 0xc2, 0x10, //0x00001f9a addq         $16, %rdx
	0x48, 0x83, 0xc6, 0xf0, //0x00001f9e addq         $-16, %rsi
	0x48, 0x83, 0xfe, 0x08, //0x00001fa2 cmpq         $8, %rsi
	0x0f, 0x83, 0x33, 0x00, 0x00, 0x00, //0x00001fa6 jae          LBB0_380
	//0x00001fac LBB0_374
	0x48, 0x83, 0xfe, 0x04, //0x00001fac cmpq         $4, %rsi
	0x0f, 0x8c, 0x45, 0x00, 0x00, 0x00, //0x00001fb0 jl           LBB0_375
	//0x00001fb6 LBB0_381
	0x8b, 0x0f, //0x00001fb6 movl         (%rdi), %ecx
	0x89, 0x0a, //0x00001fb8 movl         %ecx, (%rdx)
	0x48, 0x83, 0xc7, 0x04, //0x00001fba addq         $4, %rdi
	0x48, 0x83, 0xc2, 0x04, //0x00001fbe addq         $4, %rdx
	0x48, 0x83, 0xc6, 0xfc, //0x00001fc2 addq         $-4, %rsi
	0x48, 0x83, 0xfe, 0x02, //0x00001fc6 cmpq         $2, %rsi
	0x0f, 0x83, 0x35, 0x00, 0x00, 0x00, //0x00001fca jae          LBB0_376
	0xe9, 0x42, 0x00, 0x00, 0x00, //0x00001fd0 jmp          LBB0_377
	//0x00001fd5 LBB0_373
	0x48, 0x83, 0xfe, 0x08, //0x00001fd5 cmpq         $8, %rsi
	0x0f, 0x82, 0xcd, 0xff, 0xff, 0xff, //0x00001fd9 jb           LBB0_374
	//0x00001fdf LBB0_380
	0x48, 0x8b, 0x0f, //0x00001fdf movq         (%rdi), %rcx
	0x48, 0x89, 0x0a, //0x00001fe2 movq         %rcx, (%rdx)
	0x48, 0x83, 0xc7, 0x08, //0x00001fe5 addq         $8, %rdi
	0x48, 0x83, 0xc2, 0x08, //0x00001fe9 addq         $8, %rdx
	0x48, 0x83, 0xc6, 0xf8, //0x00001fed addq         $-8, %rsi
	0x48, 0x83, 0xfe, 0x04, //0x00001ff1 cmpq         $4, %rsi
	0x0f, 0x8d, 0xbb, 0xff, 0xff, 0xff, //0x00001ff5 jge          LBB0_381
	//0x00001ffb LBB0_375
	0x48, 0x83, 0xfe, 0x02, //0x00001ffb cmpq         $2, %rsi
	0x0f, 0x82, 0x12, 0x00, 0x00, 0x00, //0x00001fff jb           LBB0_377
	//0x00002005 LBB0_376
	0x0f, 0xb7, 0x0f, //0x00002005 movzwl       (%rdi), %ecx
	0x66, 0x89, 0x0a, //0x00002008 movw         %cx, (%rdx)
	0x48, 0x83, 0xc7, 0x02, //0x0000200b addq         $2, %rdi
	0x48, 0x83, 0xc2, 0x02, //0x0000200f addq         $2, %rdx
	0x48, 0x83, 0xc6, 0xfe, //0x00002013 addq         $-2, %rsi
	//0x00002017 LBB0_377
	0x48, 0x89, 0xf9, //0x00002017 movq         %rdi, %rcx
	0x4c, 0x8d, 0xa5, 0x50, 0xff, 0xff, 0xff, //0x0000201a leaq         $-176(%rbp), %r12
	0x48, 0x85, 0xf6, //0x00002021 testq        %rsi, %rsi
	0x66, 0x0f, 0x6f, 0xd6, //0x00002024 movdqa       %xmm6, %xmm2
	0x66, 0x44, 0x0f, 0x6f, 0xef, //0x00002028 movdqa       %xmm7, %xmm13
	0x0f, 0x84, 0x0b, 0x00, 0x00, 0x00, //0x0000202d je           LBB0_357
	0x8a, 0x09, //0x00002033 movb         (%rcx), %cl
	0x88, 0x0a, //0x00002035 movb         %cl, (%rdx)
	0x4c, 0x8d, 0xa5, 0x50, 0xff, 0xff, 0xff, //0x00002037 leaq         $-176(%rbp), %r12
	//0x0000203e LBB0_357
	0xf3, 0x41, 0x0f, 0x6f, 0x24, 0x24, //0x0000203e movdqu       (%r12), %xmm4
	0xf3, 0x41, 0x0f, 0x6f, 0x7c, 0x24, 0x10, //0x00002044 movdqu       $16(%r12), %xmm7
	0xf3, 0x41, 0x0f, 0x6f, 0x74, 0x24, 0x20, //0x0000204b movdqu       $32(%r12), %xmm6
	0xf3, 0x41, 0x0f, 0x6f, 0x6c, 0x24, 0x30, //0x00002052 movdqu       $48(%r12), %xmm5
	0x66, 0x0f, 0x6f, 0xdc, //0x00002059 movdqa       %xmm4, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x0000205d pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xd3, //0x00002061 pmovmskb     %xmm3, %edx
	0x66, 0x0f, 0x6f, 0xdf, //0x00002065 movdqa       %xmm7, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x00002069 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xcb, //0x0000206d pmovmskb     %xmm3, %ecx
	0x66, 0x0f, 0x6f, 0xde, //0x00002071 movdqa       %xmm6, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x00002075 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xf3, //0x00002079 pmovmskb     %xmm3, %esi
	0x66, 0x0f, 0x6f, 0xdd, //0x0000207d movdqa       %xmm5, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x00002081 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xfb, //0x00002085 pmovmskb     %xmm3, %edi
	0x48, 0xc1, 0xe7, 0x30, //0x00002089 shlq         $48, %rdi
	0x48, 0xc1, 0xe6, 0x20, //0x0000208d shlq         $32, %rsi
	0x48, 0x09, 0xfe, //0x00002091 orq          %rdi, %rsi
	0x48, 0xc1, 0xe1, 0x10, //0x00002094 shlq         $16, %rcx
	0x48, 0x09, 0xf1, //0x00002098 orq          %rsi, %rcx
	0x48, 0x09, 0xca, //0x0000209b orq          %rcx, %rdx
	0x48, 0x89, 0xd1, //0x0000209e movq         %rdx, %rcx
	0x4c, 0x09, 0xd1, //0x000020a1 orq          %r10, %rcx
	0x0f, 0x85, 0x0f, 0x00, 0x00, 0x00, //0x000020a4 jne          LBB0_359
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x000020aa movq         $-1, %rdx
	0x45, 0x31, 0xd2, //0x000020b1 xorl         %r10d, %r10d
	0xe9, 0x46, 0x00, 0x00, 0x00, //0x000020b4 jmp          LBB0_360
	//0x000020b9 LBB0_359
	0x4c, 0x89, 0xd1, //0x000020b9 movq         %r10, %rcx
	0x48, 0xf7, 0xd1, //0x000020bc notq         %rcx
	0x48, 0x21, 0xd1, //0x000020bf andq         %rdx, %rcx
	0x4c, 0x8d, 0x1c, 0x09, //0x000020c2 leaq         (%rcx,%rcx), %r11
	0x4d, 0x09, 0xd3, //0x000020c6 orq          %r10, %r11
	0x4c, 0x89, 0xdf, //0x000020c9 movq         %r11, %rdi
	0x48, 0xf7, 0xd7, //0x000020cc notq         %rdi
	0x48, 0xbe, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x000020cf movabsq      $-6148914691236517206, %rsi
	0x48, 0x21, 0xf2, //0x000020d9 andq         %rsi, %rdx
	0x48, 0x21, 0xfa, //0x000020dc andq         %rdi, %rdx
	0x45, 0x31, 0xd2, //0x000020df xorl         %r10d, %r10d
	0x48, 0x01, 0xca, //0x000020e2 addq         %rcx, %rdx
	0x41, 0x0f, 0x92, 0xc2, //0x000020e5 setb         %r10b
	0x48, 0x01, 0xd2, //0x000020e9 addq         %rdx, %rdx
	0x48, 0xb9, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000020ec movabsq      $6148914691236517205, %rcx
	0x48, 0x31, 0xca, //0x000020f6 xorq         %rcx, %rdx
	0x4c, 0x21, 0xda, //0x000020f9 andq         %r11, %rdx
	0x48, 0xf7, 0xd2, //0x000020fc notq         %rdx
	//0x000020ff LBB0_360
	0x66, 0x0f, 0x6f, 0xdd, //0x000020ff movdqa       %xmm5, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x00002103 pcmpeqb      %xmm0, %xmm3
	0x66, 0x0f, 0xd7, 0xcb, //0x00002107 pmovmskb     %xmm3, %ecx
	0x48, 0xc1, 0xe1, 0x30, //0x0000210b shlq         $48, %rcx
	0x66, 0x0f, 0x6f, 0xde, //0x0000210f movdqa       %xmm6, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x00002113 pcmpeqb      %xmm0, %xmm3
	0x66, 0x0f, 0xd7, 0xf3, //0x00002117 pmovmskb     %xmm3, %esi
	0x48, 0xc1, 0xe6, 0x20, //0x0000211b shlq         $32, %rsi
	0x48, 0x09, 0xce, //0x0000211f orq          %rcx, %rsi
	0x66, 0x0f, 0x6f, 0xdf, //0x00002122 movdqa       %xmm7, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x00002126 pcmpeqb      %xmm0, %xmm3
	0x66, 0x0f, 0xd7, 0xcb, //0x0000212a pmovmskb     %xmm3, %ecx
	0x48, 0xc1, 0xe1, 0x10, //0x0000212e shlq         $16, %rcx
	0x48, 0x09, 0xf1, //0x00002132 orq          %rsi, %rcx
	0x66, 0x0f, 0x6f, 0xdc, //0x00002135 movdqa       %xmm4, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x00002139 pcmpeqb      %xmm0, %xmm3
	0x66, 0x0f, 0xd7, 0xf3, //0x0000213d pmovmskb     %xmm3, %esi
	0x48, 0x09, 0xce, //0x00002141 orq          %rcx, %rsi
	0x48, 0x21, 0xd6, //0x00002144 andq         %rdx, %rsi
	0x66, 0x48, 0x0f, 0x6e, 0xde, //0x00002147 movq         %rsi, %xmm3
	0x66, 0x41, 0x0f, 0x3a, 0x44, 0xda, 0x00, //0x0000214c pclmulqdq    $0, %xmm10, %xmm3
	0x66, 0x49, 0x0f, 0x7e, 0xdb, //0x00002153 movq         %xmm3, %r11
	0x4d, 0x31, 0xc3, //0x00002158 xorq         %r8, %r11
	0x66, 0x0f, 0x6f, 0xdc, //0x0000215b movdqa       %xmm4, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xdd, //0x0000215f pcmpeqb      %xmm13, %xmm3
	0x66, 0x44, 0x0f, 0xd7, 0xeb, //0x00002164 pmovmskb     %xmm3, %r13d
	0x66, 0x0f, 0x6f, 0xdf, //0x00002169 movdqa       %xmm7, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xdd, //0x0000216d pcmpeqb      %xmm13, %xmm3
	0x66, 0x0f, 0xd7, 0xcb, //0x00002172 pmovmskb     %xmm3, %ecx
	0x66, 0x0f, 0x6f, 0xde, //0x00002176 movdqa       %xmm6, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xdd, //0x0000217a pcmpeqb      %xmm13, %xmm3
	0x66, 0x0f, 0xd7, 0xd3, //0x0000217f pmovmskb     %xmm3, %edx
	0x66, 0x0f, 0x6f, 0xdd, //0x00002183 movdqa       %xmm5, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xdd, //0x00002187 pcmpeqb      %xmm13, %xmm3
	0x66, 0x0f, 0xd7, 0xf3, //0x0000218c pmovmskb     %xmm3, %esi
	0x48, 0xc1, 0xe6, 0x30, //0x00002190 shlq         $48, %rsi
	0x48, 0xc1, 0xe2, 0x20, //0x00002194 shlq         $32, %rdx
	0x48, 0x09, 0xf2, //0x00002198 orq          %rsi, %rdx
	0x48, 0xc1, 0xe1, 0x10, //0x0000219b shlq         $16, %rcx
	0x48, 0x09, 0xd1, //0x0000219f orq          %rdx, %rcx
	0x49, 0x09, 0xcd, //0x000021a2 orq          %rcx, %r13
	0x4d, 0x89, 0xd8, //0x000021a5 movq         %r11, %r8
	0x49, 0xf7, 0xd0, //0x000021a8 notq         %r8
	0x4d, 0x21, 0xc5, //0x000021ab andq         %r8, %r13
	0x66, 0x41, 0x0f, 0x74, 0xe0, //0x000021ae pcmpeqb      %xmm8, %xmm4
	0x66, 0x0f, 0xd7, 0xd4, //0x000021b3 pmovmskb     %xmm4, %edx
	0x66, 0x41, 0x0f, 0x74, 0xf8, //0x000021b7 pcmpeqb      %xmm8, %xmm7
	0x66, 0x0f, 0xd7, 0xf7, //0x000021bc pmovmskb     %xmm7, %esi
	0x66, 0x41, 0x0f, 0x74, 0xf0, //0x000021c0 pcmpeqb      %xmm8, %xmm6
	0x66, 0x0f, 0xd7, 0xce, //0x000021c5 pmovmskb     %xmm6, %ecx
	0x66, 0x41, 0x0f, 0x74, 0xe8, //0x000021c9 pcmpeqb      %xmm8, %xmm5
	0x66, 0x44, 0x0f, 0xd7, 0xfd, //0x000021ce pmovmskb     %xmm5, %r15d
	0x49, 0xc1, 0xe7, 0x30, //0x000021d3 shlq         $48, %r15
	0x48, 0xc1, 0xe1, 0x20, //0x000021d7 shlq         $32, %rcx
	0x4c, 0x09, 0xf9, //0x000021db orq          %r15, %rcx
	0x48, 0xc1, 0xe6, 0x10, //0x000021de shlq         $16, %rsi
	0x48, 0x09, 0xce, //0x000021e2 orq          %rcx, %rsi
	0x48, 0x09, 0xf2, //0x000021e5 orq          %rsi, %rdx
	0x4c, 0x21, 0xc2, //0x000021e8 andq         %r8, %rdx
	0x0f, 0x84, 0x86, 0x00, 0x00, 0x00, //0x000021eb je           LBB0_364
	0x49, 0xb8, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, //0x000021f1 movabsq      $3689348814741910323, %r8
	0x90, 0x90, 0x90, 0x90, 0x90, //0x000021fb .p2align 4, 0x90
	//0x00002200 LBB0_362
	0x48, 0x8d, 0x7a, 0xff, //0x00002200 leaq         $-1(%rdx), %rdi
	0x48, 0x89, 0xf9, //0x00002204 movq         %rdi, %rcx
	0x4c, 0x21, 0xe9, //0x00002207 andq         %r13, %rcx
	0x48, 0x89, 0xce, //0x0000220a movq         %rcx, %rsi
	0x48, 0xd1, 0xee, //0x0000220d shrq         %rsi
	0x48, 0xbb, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00002210 movabsq      $6148914691236517205, %rbx
	0x48, 0x21, 0xde, //0x0000221a andq         %rbx, %rsi
	0x48, 0x29, 0xf1, //0x0000221d subq         %rsi, %rcx
	0x48, 0x89, 0xce, //0x00002220 movq         %rcx, %rsi
	0x4c, 0x21, 0xc6, //0x00002223 andq         %r8, %rsi
	0x48, 0xc1, 0xe9, 0x02, //0x00002226 shrq         $2, %rcx
	0x4c, 0x21, 0xc1, //0x0000222a andq         %r8, %rcx
	0x48, 0x01, 0xf1, //0x0000222d addq         %rsi, %rcx
	0x48, 0x89, 0xce, //0x00002230 movq         %rcx, %rsi
	0x48, 0xc1, 0xee, 0x04, //0x00002233 shrq         $4, %rsi
	0x48, 0x01, 0xce, //0x00002237 addq         %rcx, %rsi
	0x48, 0xb9, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, //0x0000223a movabsq      $1085102592571150095, %rcx
	0x48, 0x21, 0xce, //0x00002244 andq         %rcx, %rsi
	0x48, 0xb9, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, //0x00002247 movabsq      $72340172838076673, %rcx
	0x48, 0x0f, 0xaf, 0xf1, //0x00002251 imulq        %rcx, %rsi
	0x48, 0xc1, 0xee, 0x38, //0x00002255 shrq         $56, %rsi
	0x4c, 0x01, 0xce, //0x00002259 addq         %r9, %rsi
	0x4c, 0x39, 0xf6, //0x0000225c cmpq         %r14, %rsi
	0x0f, 0x86, 0x94, 0x00, 0x00, 0x00, //0x0000225f jbe          LBB0_382
	0x49, 0x83, 0xc6, 0x01, //0x00002265 addq         $1, %r14
	0x48, 0x21, 0xfa, //0x00002269 andq         %rdi, %rdx
	0x0f, 0x85, 0x8e, 0xff, 0xff, 0xff, //0x0000226c jne          LBB0_362
	0xe9, 0x0a, 0x00, 0x00, 0x00, //0x00002272 jmp          LBB0_365
	//0x00002277 LBB0_364
	0x49, 0xb8, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, //0x00002277 movabsq      $3689348814741910323, %r8
	//0x00002281 LBB0_365
	0x49, 0xc1, 0xfb, 0x3f, //0x00002281 sarq         $63, %r11
	0x4c, 0x89, 0xe9, //0x00002285 movq         %r13, %rcx
	0x48, 0xd1, 0xe9, //0x00002288 shrq         %rcx
	0x48, 0xba, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x0000228b movabsq      $6148914691236517205, %rdx
	0x48, 0x21, 0xd1, //0x00002295 andq         %rdx, %rcx
	0x49, 0x29, 0xcd, //0x00002298 subq         %rcx, %r13
	0x4c, 0x89, 0xe9, //0x0000229b movq         %r13, %rcx
	0x4c, 0x21, 0xc1, //0x0000229e andq         %r8, %rcx
	0x49, 0xc1, 0xed, 0x02, //0x000022a1 shrq         $2, %r13
	0x4d, 0x21, 0xc5, //0x000022a5 andq         %r8, %r13
	0x49, 0x01, 0xcd, //0x000022a8 addq         %rcx, %r13
	0x4c, 0x89, 0xe9, //0x000022ab movq         %r13, %rcx
	0x48, 0xc1, 0xe9, 0x04, //0x000022ae shrq         $4, %rcx
	0x4c, 0x01, 0xe9, //0x000022b2 addq         %r13, %rcx
	0x48, 0xba, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, //0x000022b5 movabsq      $1085102592571150095, %rdx
	0x48, 0x21, 0xd1, //0x000022bf andq         %rdx, %rcx
	0x48, 0xba, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, //0x000022c2 movabsq      $72340172838076673, %rdx
	0x48, 0x0f, 0xaf, 0xca, //0x000022cc imulq        %rdx, %rcx
	0x48, 0xc1, 0xe9, 0x38, //0x000022d0 shrq         $56, %rcx
	0x49, 0x01, 0xc9, //0x000022d4 addq         %rcx, %r9
	0x49, 0x83, 0xc4, 0x40, //0x000022d7 addq         $64, %r12
	0x48, 0x8b, 0x55, 0xc0, //0x000022db movq         $-64(%rbp), %rdx
	0x48, 0x83, 0xc2, 0xc0, //0x000022df addq         $-64, %rdx
	0x4d, 0x89, 0xd8, //0x000022e3 movq         %r11, %r8
	0x48, 0x83, 0xfa, 0x40, //0x000022e6 cmpq         $64, %rdx
	0x48, 0x89, 0x55, 0xc0, //0x000022ea movq         %rdx, $-64(%rbp)
	0x0f, 0x8d, 0x4a, 0xfd, 0xff, 0xff, //0x000022ee jge          LBB0_357
	0xe9, 0xea, 0xfb, 0xff, 0xff, //0x000022f4 jmp          LBB0_366
	//0x000022f9 LBB0_382
	0x48, 0x8b, 0x75, 0xa8, //0x000022f9 movq         $-88(%rbp), %rsi
	0x48, 0x8b, 0x4e, 0x08, //0x000022fd movq         $8(%rsi), %rcx
	0x48, 0x0f, 0xbc, 0xd2, //0x00002301 bsfq         %rdx, %rdx
	0x48, 0x2b, 0x55, 0xc0, //0x00002305 subq         $-64(%rbp), %rdx
	0x48, 0x01, 0xd1, //0x00002309 addq         %rdx, %rcx
	0x48, 0x83, 0xc1, 0x01, //0x0000230c addq         $1, %rcx
	0x4c, 0x8b, 0x75, 0xd0, //0x00002310 movq         $-48(%rbp), %r14
	0x49, 0x89, 0x0e, //0x00002314 movq         %rcx, (%r14)
	0x48, 0x8b, 0x56, 0x08, //0x00002317 movq         $8(%rsi), %rdx
	0x48, 0x39, 0xd1, //0x0000231b cmpq         %rdx, %rcx
	0x48, 0x0f, 0x47, 0xca, //0x0000231e cmovaq       %rdx, %rcx
	0x49, 0x89, 0x0e, //0x00002322 movq         %rcx, (%r14)
	0x0f, 0x87, 0x1c, 0x17, 0x00, 0x00, //0x00002325 ja           LBB0_638
	0x48, 0x8b, 0x45, 0xb0, //0x0000232b movq         $-80(%rbp), %rax
	0x48, 0x85, 0xc0, //0x0000232f testq        %rax, %rax
	0x4c, 0x8b, 0x6d, 0xb8, //0x00002332 movq         $-72(%rbp), %r13
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00002336 movabsq      $4294977024, %r11
	0x0f, 0x8f, 0x7a, 0xde, 0xff, 0xff, //0x00002340 jg           LBB0_4
	0xe9, 0xad, 0x16, 0x00, 0x00, //0x00002346 jmp          LBB0_606
	//0x0000234b LBB0_605
	0x66, 0x0f, 0xbc, 0xce, //0x0000234b bsfw         %si, %cx
	0x0f, 0xb7, 0xc9, //0x0000234f movzwl       %cx, %ecx
	0x48, 0x29, 0xd1, //0x00002352 subq         %rdx, %rcx
	0x49, 0x89, 0x0e, //0x00002355 movq         %rcx, (%r14)
	0x48, 0x85, 0xc0, //0x00002358 testq        %rax, %rax
	0x0f, 0x8f, 0x5f, 0xde, 0xff, 0xff, //0x0000235b jg           LBB0_4
	0xe9, 0x92, 0x16, 0x00, 0x00, //0x00002361 jmp          LBB0_606
	//0x00002366 LBB0_384
	0x49, 0x8d, 0x57, 0xff, //0x00002366 leaq         $-1(%r15), %rdx
	0x49, 0x39, 0xd0, //0x0000236a cmpq         %rdx, %r8
	0x49, 0xf7, 0xd7, //0x0000236d notq         %r15
	0x4d, 0x0f, 0x45, 0xfb, //0x00002370 cmovneq      %r11, %r15
	0x84, 0xc9, //0x00002374 testb        %cl, %cl
	0x4d, 0x0f, 0x44, 0xfb, //0x00002376 cmoveq       %r11, %r15
	0x4d, 0x89, 0xfd, //0x0000237a movq         %r15, %r13
	0xe9, 0x60, 0xde, 0xff, 0xff, //0x0000237d jmp          LBB0_2
	//0x00002382 LBB0_385
	0x48, 0x89, 0xc6, //0x00002382 movq         %rax, %rsi
	0x48, 0x29, 0xd6, //0x00002385 subq         %rdx, %rsi
	0x48, 0x89, 0xd1, //0x00002388 movq         %rdx, %rcx
	0x0f, 0x84, 0xb4, 0x18, 0x00, 0x00, //0x0000238b je           LBB0_644
	0x48, 0x83, 0xfe, 0x40, //0x00002391 cmpq         $64, %rsi
	0x0f, 0x82, 0xeb, 0x0c, 0x00, 0x00, //0x00002395 jb           LBB0_503
	0x48, 0xc7, 0x45, 0xc8, 0xff, 0xff, 0xff, 0xff, //0x0000239b movq         $-1, $-56(%rbp)
	0x48, 0x89, 0xc8, //0x000023a3 movq         %rcx, %rax
	0x31, 0xdb, //0x000023a6 xorl         %ebx, %ebx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000023a8 .p2align 4, 0x90
	//0x000023b0 LBB0_388
	0x48, 0x89, 0x75, 0xc0, //0x000023b0 movq         %rsi, $-64(%rbp)
	0x49, 0x89, 0xd9, //0x000023b4 movq         %rbx, %r9
	0xf3, 0x41, 0x0f, 0x6f, 0x2c, 0x04, //0x000023b7 movdqu       (%r12,%rax), %xmm5
	0xf3, 0x41, 0x0f, 0x6f, 0x7c, 0x04, 0x10, //0x000023bd movdqu       $16(%r12,%rax), %xmm7
	0xf3, 0x41, 0x0f, 0x6f, 0x74, 0x04, 0x20, //0x000023c4 movdqu       $32(%r12,%rax), %xmm6
	0xf3, 0x41, 0x0f, 0x6f, 0x64, 0x04, 0x30, //0x000023cb movdqu       $48(%r12,%rax), %xmm4
	0x66, 0x0f, 0x6f, 0xdd, //0x000023d2 movdqa       %xmm5, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x000023d6 pcmpeqb      %xmm0, %xmm3
	0x66, 0x44, 0x0f, 0xd7, 0xdb, //0x000023da pmovmskb     %xmm3, %r11d
	0x66, 0x0f, 0x6f, 0xdf, //0x000023df movdqa       %xmm7, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x000023e3 pcmpeqb      %xmm0, %xmm3
	0x66, 0x0f, 0xd7, 0xcb, //0x000023e7 pmovmskb     %xmm3, %ecx
	0x66, 0x0f, 0x6f, 0xde, //0x000023eb movdqa       %xmm6, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x000023ef pcmpeqb      %xmm0, %xmm3
	0x66, 0x44, 0x0f, 0xd7, 0xf3, //0x000023f3 pmovmskb     %xmm3, %r14d
	0x66, 0x0f, 0x6f, 0xdc, //0x000023f8 movdqa       %xmm4, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x000023fc pcmpeqb      %xmm0, %xmm3
	0x66, 0x44, 0x0f, 0xd7, 0xfb, //0x00002400 pmovmskb     %xmm3, %r15d
	0x66, 0x0f, 0x6f, 0xdd, //0x00002405 movdqa       %xmm5, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x00002409 pcmpeqb      %xmm1, %xmm3
	0x66, 0x44, 0x0f, 0xd7, 0xeb, //0x0000240d pmovmskb     %xmm3, %r13d
	0x66, 0x0f, 0x6f, 0xdf, //0x00002412 movdqa       %xmm7, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x00002416 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xdb, //0x0000241a pmovmskb     %xmm3, %ebx
	0x66, 0x0f, 0x6f, 0xde, //0x0000241e movdqa       %xmm6, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x00002422 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xf3, //0x00002426 pmovmskb     %xmm3, %esi
	0x66, 0x0f, 0x6f, 0xdc, //0x0000242a movdqa       %xmm4, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x0000242e pcmpeqb      %xmm1, %xmm3
	0x66, 0x44, 0x0f, 0xd7, 0xd3, //0x00002432 pmovmskb     %xmm3, %r10d
	0x66, 0x41, 0x0f, 0x6f, 0xdc, //0x00002437 movdqa       %xmm12, %xmm3
	0x66, 0x0f, 0x64, 0xdf, //0x0000243c pcmpgtb      %xmm7, %xmm3
	0x66, 0x41, 0x0f, 0x64, 0xfa, //0x00002440 pcmpgtb      %xmm10, %xmm7
	0x66, 0x0f, 0xdb, 0xfb, //0x00002445 pand         %xmm3, %xmm7
	0x66, 0x0f, 0xd7, 0xff, //0x00002449 pmovmskb     %xmm7, %edi
	0x66, 0x41, 0x0f, 0x6f, 0xdc, //0x0000244d movdqa       %xmm12, %xmm3
	0x66, 0x0f, 0x64, 0xde, //0x00002452 pcmpgtb      %xmm6, %xmm3
	0x66, 0x41, 0x0f, 0x64, 0xf2, //0x00002456 pcmpgtb      %xmm10, %xmm6
	0x66, 0x0f, 0xdb, 0xf3, //0x0000245b pand         %xmm3, %xmm6
	0x66, 0x0f, 0xd7, 0xd6, //0x0000245f pmovmskb     %xmm6, %edx
	0x66, 0x41, 0x0f, 0x6f, 0xdc, //0x00002463 movdqa       %xmm12, %xmm3
	0x66, 0x0f, 0x64, 0xdc, //0x00002468 pcmpgtb      %xmm4, %xmm3
	0x66, 0x41, 0x0f, 0x64, 0xe2, //0x0000246c pcmpgtb      %xmm10, %xmm4
	0x66, 0x0f, 0xdb, 0xe3, //0x00002471 pand         %xmm3, %xmm4
	0x66, 0x44, 0x0f, 0xd7, 0xc4, //0x00002475 pmovmskb     %xmm4, %r8d
	0x49, 0xc1, 0xe7, 0x30, //0x0000247a shlq         $48, %r15
	0x49, 0xc1, 0xe6, 0x20, //0x0000247e shlq         $32, %r14
	0x4d, 0x09, 0xfe, //0x00002482 orq          %r15, %r14
	0x48, 0xc1, 0xe1, 0x10, //0x00002485 shlq         $16, %rcx
	0x4c, 0x09, 0xf1, //0x00002489 orq          %r14, %rcx
	0x49, 0x09, 0xcb, //0x0000248c orq          %rcx, %r11
	0x49, 0xc1, 0xe2, 0x30, //0x0000248f shlq         $48, %r10
	0x48, 0xc1, 0xe6, 0x20, //0x00002493 shlq         $32, %rsi
	0x4c, 0x09, 0xd6, //0x00002497 orq          %r10, %rsi
	0x48, 0xc1, 0xe3, 0x10, //0x0000249a shlq         $16, %rbx
	0x48, 0x09, 0xf3, //0x0000249e orq          %rsi, %rbx
	0x49, 0xc1, 0xe0, 0x30, //0x000024a1 shlq         $48, %r8
	0x48, 0xc1, 0xe2, 0x20, //0x000024a5 shlq         $32, %rdx
	0x4c, 0x09, 0xc2, //0x000024a9 orq          %r8, %rdx
	0x48, 0xc1, 0xe7, 0x10, //0x000024ac shlq         $16, %rdi
	0x48, 0x09, 0xd7, //0x000024b0 orq          %rdx, %rdi
	0x49, 0x09, 0xdd, //0x000024b3 orq          %rbx, %r13
	0x0f, 0x85, 0x59, 0x00, 0x00, 0x00, //0x000024b6 jne          LBB0_405
	0x4d, 0x85, 0xc9, //0x000024bc testq        %r9, %r9
	0x0f, 0x85, 0x6f, 0x00, 0x00, 0x00, //0x000024bf jne          LBB0_407
	0x31, 0xdb, //0x000024c5 xorl         %ebx, %ebx
	0x4c, 0x8b, 0x75, 0xd0, //0x000024c7 movq         $-48(%rbp), %r14
	//0x000024cb LBB0_391
	0x66, 0x41, 0x0f, 0x6f, 0xdc, //0x000024cb movdqa       %xmm12, %xmm3
	0x66, 0x0f, 0x64, 0xdd, //0x000024d0 pcmpgtb      %xmm5, %xmm3
	0x66, 0x41, 0x0f, 0x64, 0xea, //0x000024d4 pcmpgtb      %xmm10, %xmm5
	0x66, 0x0f, 0xdb, 0xeb, //0x000024d9 pand         %xmm3, %xmm5
	0x66, 0x0f, 0xd7, 0xcd, //0x000024dd pmovmskb     %xmm5, %ecx
	0x48, 0x09, 0xcf, //0x000024e1 orq          %rcx, %rdi
	0x4d, 0x85, 0xdb, //0x000024e4 testq        %r11, %r11
	0x4c, 0x8b, 0x6d, 0xb8, //0x000024e7 movq         $-72(%rbp), %r13
	0x0f, 0x85, 0x93, 0x00, 0x00, 0x00, //0x000024eb jne          LBB0_409
	0x48, 0x85, 0xff, //0x000024f1 testq        %rdi, %rdi
	0x0f, 0x85, 0xaf, 0x16, 0x00, 0x00, //0x000024f4 jne          LBB0_632
	0x48, 0x8b, 0x75, 0xc0, //0x000024fa movq         $-64(%rbp), %rsi
	0x48, 0x83, 0xc6, 0xc0, //0x000024fe addq         $-64, %rsi
	0x48, 0x83, 0xc0, 0x40, //0x00002502 addq         $64, %rax
	0x48, 0x83, 0xfe, 0x3f, //0x00002506 cmpq         $63, %rsi
	0x0f, 0x87, 0xa0, 0xfe, 0xff, 0xff, //0x0000250a ja           LBB0_388
	0xe9, 0xb7, 0x07, 0x00, 0x00, //0x00002510 jmp          LBB0_394
	//0x00002515 LBB0_405
	0x48, 0x83, 0x7d, 0xc8, 0xff, //0x00002515 cmpq         $-1, $-56(%rbp)
	0x4c, 0x8b, 0x75, 0xd0, //0x0000251a movq         $-48(%rbp), %r14
	0x0f, 0x85, 0x14, 0x00, 0x00, 0x00, //0x0000251e jne          LBB0_408
	0x49, 0x0f, 0xbc, 0xcd, //0x00002524 bsfq         %r13, %rcx
	0x48, 0x01, 0xc1, //0x00002528 addq         %rax, %rcx
	0x48, 0x89, 0x4d, 0xc8, //0x0000252b movq         %rcx, $-56(%rbp)
	0xe9, 0x04, 0x00, 0x00, 0x00, //0x0000252f jmp          LBB0_408
	//0x00002534 LBB0_407
	0x4c, 0x8b, 0x75, 0xd0, //0x00002534 movq         $-48(%rbp), %r14
	//0x00002538 LBB0_408
	0x4c, 0x89, 0xc9, //0x00002538 movq         %r9, %rcx
	0x48, 0xf7, 0xd1, //0x0000253b notq         %rcx
	0x4c, 0x21, 0xe9, //0x0000253e andq         %r13, %rcx
	0x48, 0x8d, 0x14, 0x09, //0x00002541 leaq         (%rcx,%rcx), %rdx
	0x4c, 0x09, 0xca, //0x00002545 orq          %r9, %rdx
	0x48, 0x89, 0xd6, //0x00002548 movq         %rdx, %rsi
	0x48, 0xf7, 0xd6, //0x0000254b notq         %rsi
	0x4c, 0x21, 0xee, //0x0000254e andq         %r13, %rsi
	0x48, 0xbb, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00002551 movabsq      $-6148914691236517206, %rbx
	0x48, 0x21, 0xde, //0x0000255b andq         %rbx, %rsi
	0x31, 0xdb, //0x0000255e xorl         %ebx, %ebx
	0x48, 0x01, 0xce, //0x00002560 addq         %rcx, %rsi
	0x0f, 0x92, 0xc3, //0x00002563 setb         %bl
	0x48, 0x01, 0xf6, //0x00002566 addq         %rsi, %rsi
	0x48, 0xb9, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00002569 movabsq      $6148914691236517205, %rcx
	0x48, 0x31, 0xce, //0x00002573 xorq         %rcx, %rsi
	0x48, 0x21, 0xd6, //0x00002576 andq         %rdx, %rsi
	0x48, 0xf7, 0xd6, //0x00002579 notq         %rsi
	0x49, 0x21, 0xf3, //0x0000257c andq         %rsi, %r11
	0xe9, 0x47, 0xff, 0xff, 0xff, //0x0000257f jmp          LBB0_391
	//0x00002584 LBB0_409
	0x49, 0x0f, 0xbc, 0xcb, //0x00002584 bsfq         %r11, %rcx
	0x48, 0x85, 0xff, //0x00002588 testq        %rdi, %rdi
	0x0f, 0x84, 0x8d, 0x00, 0x00, 0x00, //0x0000258b je           LBB0_423
	0x48, 0x0f, 0xbc, 0xd7, //0x00002591 bsfq         %rdi, %rdx
	0xe9, 0x89, 0x00, 0x00, 0x00, //0x00002595 jmp          LBB0_424
	//0x0000259a LBB0_411
	0x41, 0x89, 0xcb, //0x0000259a movl         %ecx, %r11d
	0x4d, 0x01, 0xc3, //0x0000259d addq         %r8, %r11
	0x49, 0x01, 0xdb, //0x000025a0 addq         %rbx, %r11
	0xe9, 0x06, 0x00, 0x00, 0x00, //0x000025a3 jmp          LBB0_413
	//0x000025a8 LBB0_412
	0x48, 0x01, 0xda, //0x000025a8 addq         %rbx, %rdx
	0x49, 0x89, 0xd3, //0x000025ab movq         %rdx, %r11
	//0x000025ae LBB0_413
	0x48, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x000025ae movq         $-1, %rbx
	0x4d, 0x85, 0xf6, //0x000025b5 testq        %r14, %r14
	0x0f, 0x84, 0x18, 0x16, 0x00, 0x00, //0x000025b8 je           LBB0_636
	0x4d, 0x85, 0xed, //0x000025be testq        %r13, %r13
	0x0f, 0x84, 0x0f, 0x16, 0x00, 0x00, //0x000025c1 je           LBB0_636
	0x4d, 0x85, 0xd2, //0x000025c7 testq        %r10, %r10
	0x0f, 0x84, 0x06, 0x16, 0x00, 0x00, //0x000025ca je           LBB0_636
	0x4d, 0x29, 0xc3, //0x000025d0 subq         %r8, %r11
	0x49, 0x8d, 0x4b, 0xff, //0x000025d3 leaq         $-1(%r11), %rcx
	0x49, 0x39, 0xce, //0x000025d7 cmpq         %rcx, %r14
	0x0f, 0x84, 0x33, 0x00, 0x00, 0x00, //0x000025da je           LBB0_422
	0x49, 0x39, 0xcd, //0x000025e0 cmpq         %rcx, %r13
	0x0f, 0x84, 0x2a, 0x00, 0x00, 0x00, //0x000025e3 je           LBB0_422
	0x49, 0x39, 0xca, //0x000025e9 cmpq         %rcx, %r10
	0x0f, 0x84, 0x21, 0x00, 0x00, 0x00, //0x000025ec je           LBB0_422
	0x4d, 0x85, 0xed, //0x000025f2 testq        %r13, %r13
	0x0f, 0x8e, 0x9a, 0x00, 0x00, 0x00, //0x000025f5 jle          LBB0_430
	0x49, 0x8d, 0x4d, 0xff, //0x000025fb leaq         $-1(%r13), %rcx
	0x49, 0x39, 0xca, //0x000025ff cmpq         %rcx, %r10
	0x0f, 0x84, 0x8d, 0x00, 0x00, 0x00, //0x00002602 je           LBB0_430
	0x49, 0xf7, 0xd5, //0x00002608 notq         %r13
	0x4c, 0x89, 0xeb, //0x0000260b movq         %r13, %rbx
	0xe9, 0x0c, 0x06, 0x00, 0x00, //0x0000260e jmp          LBB0_468
	//0x00002613 LBB0_422
	0x49, 0xf7, 0xdb, //0x00002613 negq         %r11
	0x4c, 0x89, 0xdb, //0x00002616 movq         %r11, %rbx
	0xe9, 0x01, 0x06, 0x00, 0x00, //0x00002619 jmp          LBB0_468
	//0x0000261e LBB0_423
	0xba, 0x40, 0x00, 0x00, 0x00, //0x0000261e movl         $64, %edx
	//0x00002623 LBB0_424
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00002623 movabsq      $4294977024, %r11
	0x48, 0x39, 0xca, //0x0000262d cmpq         %rcx, %rdx
	0x0f, 0x82, 0xb6, 0x15, 0x00, 0x00, //0x00002630 jb           LBB0_640
	0x48, 0x01, 0xc8, //0x00002636 addq         %rcx, %rax
	0x48, 0x83, 0xc0, 0x01, //0x00002639 addq         $1, %rax
	//0x0000263d LBB0_426
	0x48, 0x85, 0xc0, //0x0000263d testq        %rax, %rax
	0x0f, 0x89, 0x49, 0xec, 0xff, 0xff, //0x00002640 jns          LBB0_224
	0xe9, 0x1a, 0x14, 0x00, 0x00, //0x00002646 jmp          LBB0_615
	//0x0000264b LBB0_132
	0x4d, 0x85, 0xdb, //0x0000264b testq        %r11, %r11
	0x0f, 0x85, 0x9e, 0x0a, 0x00, 0x00, //0x0000264e jne          LBB0_508
	0x49, 0x01, 0xd2, //0x00002654 addq         %rdx, %r10
	0x49, 0x29, 0xd1, //0x00002657 subq         %rdx, %r9
	0x4c, 0x8b, 0x75, 0xd0, //0x0000265a movq         $-48(%rbp), %r14
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x0000265e movabsq      $4294977024, %r11
	//0x00002668 LBB0_134
	0x4d, 0x85, 0xc9, //0x00002668 testq        %r9, %r9
	0x0f, 0x8f, 0xee, 0x0a, 0x00, 0x00, //0x0000266b jg           LBB0_512
	0xe9, 0xd1, 0x13, 0x00, 0x00, //0x00002671 jmp          LBB0_638
	//0x00002676 LBB0_428
	0x4c, 0x29, 0xe0, //0x00002676 subq         %r12, %rax
	0x48, 0x01, 0xd0, //0x00002679 addq         %rdx, %rax
	0x49, 0x89, 0x06, //0x0000267c movq         %rax, (%r14)
	0x48, 0x85, 0xf6, //0x0000267f testq        %rsi, %rsi
	0x0f, 0x8f, 0x38, 0xdb, 0xff, 0xff, //0x00002682 jg           LBB0_4
	0xe9, 0xcc, 0x13, 0x00, 0x00, //0x00002688 jmp          LBB0_614
	//0x0000268d LBB0_429
	0x0f, 0xbc, 0xcb, //0x0000268d bsfl         %ebx, %ecx
	0xe9, 0x8a, 0x03, 0x00, 0x00, //0x00002690 jmp          LBB0_437
	//0x00002695 LBB0_430
	0x4c, 0x89, 0xf1, //0x00002695 movq         %r14, %rcx
	0x4c, 0x09, 0xd1, //0x00002698 orq          %r10, %rcx
	0x0f, 0x99, 0xc2, //0x0000269b setns        %dl
	0x0f, 0x88, 0xd4, 0x01, 0x00, 0x00, //0x0000269e js           LBB0_434
	0x4d, 0x39, 0xd6, //0x000026a4 cmpq         %r10, %r14
	0x0f, 0x8c, 0xcb, 0x01, 0x00, 0x00, //0x000026a7 jl           LBB0_434
	0x49, 0xf7, 0xd6, //0x000026ad notq         %r14
	0x4c, 0x89, 0xf3, //0x000026b0 movq         %r14, %rbx
	0xe9, 0x67, 0x05, 0x00, 0x00, //0x000026b3 jmp          LBB0_468
	//0x000026b8 LBB0_433
	0x48, 0x8b, 0x75, 0xc0, //0x000026b8 movq         $-64(%rbp), %rsi
	0x49, 0x01, 0xf4, //0x000026bc addq         %rsi, %r12
	0x49, 0x29, 0xcc, //0x000026bf subq         %rcx, %r12
	0x49, 0x29, 0xfc, //0x000026c2 subq         %rdi, %r12
	0x49, 0x83, 0xc4, 0xfe, //0x000026c5 addq         $-2, %r12
	0x4d, 0x89, 0xe5, //0x000026c9 movq         %r12, %r13
	0xe9, 0x11, 0xdb, 0xff, 0xff, //0x000026cc jmp          LBB0_2
	//0x000026d1 LBB0_154
	0x4d, 0x85, 0xdb, //0x000026d1 testq        %r11, %r11
	0x0f, 0x85, 0x66, 0x0c, 0x00, 0x00, //0x000026d4 jne          LBB0_535
	0x49, 0x01, 0xd2, //0x000026da addq         %rdx, %r10
	0x49, 0x29, 0xd1, //0x000026dd subq         %rdx, %r9
	0x4c, 0x8b, 0x75, 0xd0, //0x000026e0 movq         $-48(%rbp), %r14
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000026e4 movabsq      $4294977024, %r11
	//0x000026ee LBB0_156
	0x4d, 0x85, 0xc9, //0x000026ee testq        %r9, %r9
	0x0f, 0x8f, 0xa7, 0x0c, 0x00, 0x00, //0x000026f1 jg           LBB0_539
	0xe9, 0x4b, 0x13, 0x00, 0x00, //0x000026f7 jmp          LBB0_638
	//0x000026fc LBB0_50
	0x4c, 0x01, 0xe0, //0x000026fc addq         %r12, %rax
	0x4c, 0x8b, 0x75, 0xd0, //0x000026ff movq         $-48(%rbp), %r14
	0x49, 0x83, 0xf9, 0x20, //0x00002703 cmpq         $32, %r9
	0x0f, 0x82, 0x9f, 0x07, 0x00, 0x00, //0x00002707 jb           LBB0_485
	//0x0000270d LBB0_51
	0xf3, 0x0f, 0x6f, 0x18, //0x0000270d movdqu       (%rax), %xmm3
	0xf3, 0x0f, 0x6f, 0x60, 0x10, //0x00002711 movdqu       $16(%rax), %xmm4
	0x66, 0x0f, 0x6f, 0xeb, //0x00002716 movdqa       %xmm3, %xmm5
	0x66, 0x0f, 0x74, 0xe8, //0x0000271a pcmpeqb      %xmm0, %xmm5
	0x66, 0x0f, 0xd7, 0xd5, //0x0000271e pmovmskb     %xmm5, %edx
	0x66, 0x0f, 0x6f, 0xec, //0x00002722 movdqa       %xmm4, %xmm5
	0x66, 0x0f, 0x74, 0xe8, //0x00002726 pcmpeqb      %xmm0, %xmm5
	0x66, 0x0f, 0xd7, 0xcd, //0x0000272a pmovmskb     %xmm5, %ecx
	0x66, 0x0f, 0x74, 0xd9, //0x0000272e pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xf3, //0x00002732 pmovmskb     %xmm3, %esi
	0x66, 0x0f, 0x74, 0xe1, //0x00002736 pcmpeqb      %xmm1, %xmm4
	0x66, 0x0f, 0xd7, 0xfc, //0x0000273a pmovmskb     %xmm4, %edi
	0x48, 0xc1, 0xe1, 0x10, //0x0000273e shlq         $16, %rcx
	0x48, 0x09, 0xca, //0x00002742 orq          %rcx, %rdx
	0x48, 0xc1, 0xe7, 0x10, //0x00002745 shlq         $16, %rdi
	0x48, 0x09, 0xfe, //0x00002749 orq          %rdi, %rsi
	0x0f, 0x85, 0x45, 0x0a, 0x00, 0x00, //0x0000274c jne          LBB0_515
	0x4d, 0x85, 0xc0, //0x00002752 testq        %r8, %r8
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00002755 movabsq      $4294977024, %r11
	0x0f, 0x85, 0x5c, 0x0a, 0x00, 0x00, //0x0000275f jne          LBB0_517
	0x45, 0x31, 0xc0, //0x00002765 xorl         %r8d, %r8d
	0x48, 0x85, 0xd2, //0x00002768 testq        %rdx, %rdx
	0x0f, 0x84, 0xac, 0x0a, 0x00, 0x00, //0x0000276b je           LBB0_519
	//0x00002771 LBB0_54
	0x48, 0x0f, 0xbc, 0xca, //0x00002771 bsfq         %rdx, %rcx
	0x4c, 0x29, 0xe0, //0x00002775 subq         %r12, %rax
	0x48, 0x01, 0xc8, //0x00002778 addq         %rcx, %rax
	0x48, 0x83, 0xc0, 0x01, //0x0000277b addq         $1, %rax
	0xe9, 0x02, 0xeb, 0xff, 0xff, //0x0000277f jmp          LBB0_223
	//0x00002784 LBB0_166
	0x4c, 0x8b, 0x65, 0xc0, //0x00002784 movq         $-64(%rbp), %r12
	0x4c, 0x01, 0xe0, //0x00002788 addq         %r12, %rax
	0x49, 0x83, 0xfe, 0x20, //0x0000278b cmpq         $32, %r14
	0x0f, 0x82, 0x41, 0x07, 0x00, 0x00, //0x0000278f jb           LBB0_487
	//0x00002795 LBB0_167
	0xf3, 0x0f, 0x6f, 0x20, //0x00002795 movdqu       (%rax), %xmm4
	0xf3, 0x0f, 0x6f, 0x58, 0x10, //0x00002799 movdqu       $16(%rax), %xmm3
	0x66, 0x0f, 0x6f, 0xec, //0x0000279e movdqa       %xmm4, %xmm5
	0x66, 0x0f, 0x74, 0xe8, //0x000027a2 pcmpeqb      %xmm0, %xmm5
	0x66, 0x0f, 0xd7, 0xcd, //0x000027a6 pmovmskb     %xmm5, %ecx
	0x66, 0x0f, 0x6f, 0xeb, //0x000027aa movdqa       %xmm3, %xmm5
	0x66, 0x0f, 0x74, 0xe8, //0x000027ae pcmpeqb      %xmm0, %xmm5
	0x66, 0x0f, 0xd7, 0xfd, //0x000027b2 pmovmskb     %xmm5, %edi
	0x66, 0x0f, 0x6f, 0xec, //0x000027b6 movdqa       %xmm4, %xmm5
	0x66, 0x0f, 0x74, 0xe9, //0x000027ba pcmpeqb      %xmm1, %xmm5
	0x66, 0x44, 0x0f, 0xd7, 0xcd, //0x000027be pmovmskb     %xmm5, %r9d
	0x66, 0x0f, 0x6f, 0xeb, //0x000027c3 movdqa       %xmm3, %xmm5
	0x66, 0x0f, 0x74, 0xe9, //0x000027c7 pcmpeqb      %xmm1, %xmm5
	0x66, 0x0f, 0xd7, 0xd5, //0x000027cb pmovmskb     %xmm5, %edx
	0x66, 0x41, 0x0f, 0x6f, 0xec, //0x000027cf movdqa       %xmm12, %xmm5
	0x66, 0x0f, 0x64, 0xeb, //0x000027d4 pcmpgtb      %xmm3, %xmm5
	0x66, 0x41, 0x0f, 0x64, 0xda, //0x000027d8 pcmpgtb      %xmm10, %xmm3
	0x66, 0x0f, 0xdb, 0xdd, //0x000027dd pand         %xmm5, %xmm3
	0x66, 0x0f, 0xd7, 0xf3, //0x000027e1 pmovmskb     %xmm3, %esi
	0x48, 0xc1, 0xe7, 0x10, //0x000027e5 shlq         $16, %rdi
	0x48, 0x09, 0xf9, //0x000027e9 orq          %rdi, %rcx
	0x48, 0xc1, 0xe2, 0x10, //0x000027ec shlq         $16, %rdx
	0x48, 0xc1, 0xe6, 0x10, //0x000027f0 shlq         $16, %rsi
	0x49, 0x09, 0xd1, //0x000027f4 orq          %rdx, %r9
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000027f7 movabsq      $4294977024, %r11
	0x0f, 0x85, 0x0c, 0x0b, 0x00, 0x00, //0x00002801 jne          LBB0_533
	0x48, 0x85, 0xdb, //0x00002807 testq        %rbx, %rbx
	0x4c, 0x8b, 0x6d, 0xb8, //0x0000280a movq         $-72(%rbp), %r13
	0x0f, 0x85, 0xda, 0x0b, 0x00, 0x00, //0x0000280e jne          LBB0_542
	0x31, 0xdb, //0x00002814 xorl         %ebx, %ebx
	//0x00002816 LBB0_170
	0x66, 0x41, 0x0f, 0x6f, 0xdc, //0x00002816 movdqa       %xmm12, %xmm3
	0x66, 0x0f, 0x64, 0xdc, //0x0000281b pcmpgtb      %xmm4, %xmm3
	0x66, 0x41, 0x0f, 0x64, 0xe2, //0x0000281f pcmpgtb      %xmm10, %xmm4
	0x66, 0x0f, 0xdb, 0xe3, //0x00002824 pand         %xmm3, %xmm4
	0x66, 0x0f, 0xd7, 0xd4, //0x00002828 pmovmskb     %xmm4, %edx
	0x48, 0x09, 0xd6, //0x0000282c orq          %rdx, %rsi
	0xba, 0x40, 0x00, 0x00, 0x00, //0x0000282f movl         $64, %edx
	0xbf, 0x40, 0x00, 0x00, 0x00, //0x00002834 movl         $64, %edi
	0x48, 0x85, 0xc9, //0x00002839 testq        %rcx, %rcx
	0x0f, 0x84, 0x04, 0x00, 0x00, 0x00, //0x0000283c je           LBB0_172
	0x48, 0x0f, 0xbc, 0xf9, //0x00002842 bsfq         %rcx, %rdi
	//0x00002846 LBB0_172
	0x48, 0x85, 0xf6, //0x00002846 testq        %rsi, %rsi
	0x0f, 0x84, 0x04, 0x00, 0x00, 0x00, //0x00002849 je           LBB0_174
	0x48, 0x0f, 0xbc, 0xd6, //0x0000284f bsfq         %rsi, %rdx
	//0x00002853 LBB0_174
	0x48, 0x85, 0xc9, //0x00002853 testq        %rcx, %rcx
	0x0f, 0x84, 0xd2, 0x01, 0x00, 0x00, //0x00002856 je           LBB0_438
	0x48, 0x39, 0xfa, //0x0000285c cmpq         %rdi, %rdx
	0x0f, 0x82, 0x06, 0x14, 0x00, 0x00, //0x0000285f jb           LBB0_649
	0x4c, 0x29, 0xe0, //0x00002865 subq         %r12, %rax
	0x48, 0x01, 0xf8, //0x00002868 addq         %rdi, %rax
	0x48, 0x83, 0xc0, 0x01, //0x0000286b addq         $1, %rax
	0x4c, 0x8b, 0x75, 0xd0, //0x0000286f movq         $-48(%rbp), %r14
	0xe9, 0x0e, 0xea, 0xff, 0xff, //0x00002873 jmp          LBB0_223
	//0x00002878 LBB0_434
	0x49, 0x8d, 0x4a, 0xff, //0x00002878 leaq         $-1(%r10), %rcx
	0x49, 0x39, 0xce, //0x0000287c cmpq         %rcx, %r14
	0x49, 0xf7, 0xd2, //0x0000287f notq         %r10
	0x4d, 0x0f, 0x45, 0xd3, //0x00002882 cmovneq      %r11, %r10
	0x84, 0xd2, //0x00002886 testb        %dl, %dl
	0x4d, 0x0f, 0x44, 0xd3, //0x00002888 cmoveq       %r11, %r10
	0x4c, 0x89, 0xd3, //0x0000288c movq         %r10, %rbx
	0xe9, 0x8b, 0x03, 0x00, 0x00, //0x0000288f jmp          LBB0_468
	//0x00002894 LBB0_435
	0x89, 0xd1, //0x00002894 movl         %edx, %ecx
	0xe9, 0x84, 0x01, 0x00, 0x00, //0x00002896 jmp          LBB0_437
	//0x0000289b LBB0_73
	0x4c, 0x01, 0xe0, //0x0000289b addq         %r12, %rax
	0x49, 0x83, 0xf9, 0x20, //0x0000289e cmpq         $32, %r9
	0x4c, 0x8b, 0x75, 0xd0, //0x000028a2 movq         $-48(%rbp), %r14
	0x0f, 0x82, 0xc3, 0x06, 0x00, 0x00, //0x000028a6 jb           LBB0_493
	//0x000028ac LBB0_74
	0xf3, 0x0f, 0x6f, 0x18, //0x000028ac movdqu       (%rax), %xmm3
	0xf3, 0x0f, 0x6f, 0x60, 0x10, //0x000028b0 movdqu       $16(%rax), %xmm4
	0x66, 0x0f, 0x6f, 0xeb, //0x000028b5 movdqa       %xmm3, %xmm5
	0x66, 0x0f, 0x74, 0xe8, //0x000028b9 pcmpeqb      %xmm0, %xmm5
	0x66, 0x0f, 0xd7, 0xd5, //0x000028bd pmovmskb     %xmm5, %edx
	0x66, 0x0f, 0x6f, 0xec, //0x000028c1 movdqa       %xmm4, %xmm5
	0x66, 0x0f, 0x74, 0xe8, //0x000028c5 pcmpeqb      %xmm0, %xmm5
	0x66, 0x0f, 0xd7, 0xcd, //0x000028c9 pmovmskb     %xmm5, %ecx
	0x66, 0x0f, 0x74, 0xd9, //0x000028cd pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xf3, //0x000028d1 pmovmskb     %xmm3, %esi
	0x66, 0x0f, 0x74, 0xe1, //0x000028d5 pcmpeqb      %xmm1, %xmm4
	0x66, 0x0f, 0xd7, 0xfc, //0x000028d9 pmovmskb     %xmm4, %edi
	0x48, 0xc1, 0xe1, 0x10, //0x000028dd shlq         $16, %rcx
	0x48, 0x09, 0xca, //0x000028e1 orq          %rcx, %rdx
	0x48, 0xc1, 0xe7, 0x10, //0x000028e4 shlq         $16, %rdi
	0x48, 0x09, 0xfe, //0x000028e8 orq          %rdi, %rsi
	0x0f, 0x85, 0x55, 0x0b, 0x00, 0x00, //0x000028eb jne          LBB0_544
	0x4d, 0x85, 0xc0, //0x000028f1 testq        %r8, %r8
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000028f4 movabsq      $4294977024, %r11
	0x0f, 0x85, 0x6c, 0x0b, 0x00, 0x00, //0x000028fe jne          LBB0_546
	0x45, 0x31, 0xc0, //0x00002904 xorl         %r8d, %r8d
	0x48, 0x85, 0xd2, //0x00002907 testq        %rdx, %rdx
	0x0f, 0x84, 0xbc, 0x0b, 0x00, 0x00, //0x0000290a je           LBB0_548
	//0x00002910 LBB0_77
	0x48, 0x0f, 0xbc, 0xca, //0x00002910 bsfq         %rdx, %rcx
	0x4c, 0x29, 0xe0, //0x00002914 subq         %r12, %rax
	0x48, 0x01, 0xc8, //0x00002917 addq         %rcx, %rax
	0x48, 0x83, 0xc0, 0x01, //0x0000291a addq         $1, %rax
	0xe9, 0xe0, 0xee, 0xff, 0xff, //0x0000291e jmp          LBB0_304
	//0x00002923 LBB0_195
	0x4c, 0x8b, 0x45, 0xc0, //0x00002923 movq         $-64(%rbp), %r8
	0x4c, 0x01, 0xc0, //0x00002927 addq         %r8, %rax
	0x49, 0x83, 0xf9, 0x20, //0x0000292a cmpq         $32, %r9
	0x0f, 0x82, 0x67, 0x06, 0x00, 0x00, //0x0000292e jb           LBB0_495
	//0x00002934 LBB0_196
	0xf3, 0x0f, 0x6f, 0x20, //0x00002934 movdqu       (%rax), %xmm4
	0xf3, 0x0f, 0x6f, 0x58, 0x10, //0x00002938 movdqu       $16(%rax), %xmm3
	0x66, 0x0f, 0x6f, 0xec, //0x0000293d movdqa       %xmm4, %xmm5
	0x66, 0x0f, 0x74, 0xe8, //0x00002941 pcmpeqb      %xmm0, %xmm5
	0x66, 0x0f, 0xd7, 0xf5, //0x00002945 pmovmskb     %xmm5, %esi
	0x66, 0x0f, 0x6f, 0xeb, //0x00002949 movdqa       %xmm3, %xmm5
	0x66, 0x0f, 0x74, 0xe8, //0x0000294d pcmpeqb      %xmm0, %xmm5
	0x66, 0x0f, 0xd7, 0xd5, //0x00002951 pmovmskb     %xmm5, %edx
	0x66, 0x0f, 0x6f, 0xec, //0x00002955 movdqa       %xmm4, %xmm5
	0x66, 0x0f, 0x74, 0xe9, //0x00002959 pcmpeqb      %xmm1, %xmm5
	0x66, 0x44, 0x0f, 0xd7, 0xd5, //0x0000295d pmovmskb     %xmm5, %r10d
	0x66, 0x0f, 0x6f, 0xeb, //0x00002962 movdqa       %xmm3, %xmm5
	0x66, 0x0f, 0x74, 0xe9, //0x00002966 pcmpeqb      %xmm1, %xmm5
	0x66, 0x0f, 0xd7, 0xcd, //0x0000296a pmovmskb     %xmm5, %ecx
	0x66, 0x41, 0x0f, 0x6f, 0xec, //0x0000296e movdqa       %xmm12, %xmm5
	0x66, 0x0f, 0x64, 0xeb, //0x00002973 pcmpgtb      %xmm3, %xmm5
	0x66, 0x41, 0x0f, 0x64, 0xda, //0x00002977 pcmpgtb      %xmm10, %xmm3
	0x66, 0x0f, 0xdb, 0xdd, //0x0000297c pand         %xmm5, %xmm3
	0x66, 0x0f, 0xd7, 0xfb, //0x00002980 pmovmskb     %xmm3, %edi
	0x48, 0xc1, 0xe2, 0x10, //0x00002984 shlq         $16, %rdx
	0x48, 0x09, 0xd6, //0x00002988 orq          %rdx, %rsi
	0x48, 0xc1, 0xe1, 0x10, //0x0000298b shlq         $16, %rcx
	0x48, 0xc1, 0xe7, 0x10, //0x0000298f shlq         $16, %rdi
	0x49, 0x09, 0xca, //0x00002993 orq          %rcx, %r10
	0x0f, 0x85, 0x26, 0x0c, 0x00, 0x00, //0x00002996 jne          LBB0_562
	0x4d, 0x85, 0xe4, //0x0000299c testq        %r12, %r12
	0x4c, 0x8b, 0x6d, 0xb8, //0x0000299f movq         $-72(%rbp), %r13
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000029a3 movabsq      $4294977024, %r11
	0xf3, 0x44, 0x0f, 0x6f, 0x2d, 0x8a, 0xd6, 0xff, 0xff, //0x000029ad movdqu       $-10614(%rip), %xmm13  /* LCPI0_4+0(%rip) */
	0x0f, 0x85, 0x2c, 0x0c, 0x00, 0x00, //0x000029b6 jne          LBB0_564
	0x45, 0x31, 0xe4, //0x000029bc xorl         %r12d, %r12d
	//0x000029bf LBB0_199
	0x66, 0x41, 0x0f, 0x6f, 0xdc, //0x000029bf movdqa       %xmm12, %xmm3
	0x66, 0x0f, 0x64, 0xdc, //0x000029c4 pcmpgtb      %xmm4, %xmm3
	0x66, 0x41, 0x0f, 0x64, 0xe2, //0x000029c8 pcmpgtb      %xmm10, %xmm4
	0x66, 0x0f, 0xdb, 0xe3, //0x000029cd pand         %xmm3, %xmm4
	0x66, 0x0f, 0xd7, 0xcc, //0x000029d1 pmovmskb     %xmm4, %ecx
	0x48, 0x09, 0xcf, //0x000029d5 orq          %rcx, %rdi
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x000029d8 movl         $64, %ecx
	0xba, 0x40, 0x00, 0x00, 0x00, //0x000029dd movl         $64, %edx
	0x48, 0x85, 0xf6, //0x000029e2 testq        %rsi, %rsi
	0x0f, 0x84, 0x04, 0x00, 0x00, 0x00, //0x000029e5 je           LBB0_201
	0x48, 0x0f, 0xbc, 0xd6, //0x000029eb bsfq         %rsi, %rdx
	//0x000029ef LBB0_201
	0x48, 0x85, 0xff, //0x000029ef testq        %rdi, %rdi
	0x0f, 0x84, 0x04, 0x00, 0x00, 0x00, //0x000029f2 je           LBB0_203
	0x48, 0x0f, 0xbc, 0xcf, //0x000029f8 bsfq         %rdi, %rcx
	//0x000029fc LBB0_203
	0x48, 0x85, 0xf6, //0x000029fc testq        %rsi, %rsi
	0x0f, 0x84, 0xf6, 0x00, 0x00, 0x00, //0x000029ff je           LBB0_450
	0x48, 0x39, 0xd1, //0x00002a05 cmpq         %rdx, %rcx
	0x0f, 0x82, 0x6c, 0x12, 0x00, 0x00, //0x00002a08 jb           LBB0_650
	0x4c, 0x29, 0xc0, //0x00002a0e subq         %r8, %rax
	0x48, 0x01, 0xd0, //0x00002a11 addq         %rdx, %rax
	0x48, 0x83, 0xc0, 0x01, //0x00002a14 addq         $1, %rax
	0xe9, 0xe6, 0xed, 0xff, 0xff, //0x00002a18 jmp          LBB0_304
	//0x00002a1d LBB0_436
	0x89, 0xd9, //0x00002a1d movl         %ebx, %ecx
	//0x00002a1f LBB0_437
	0x49, 0xf7, 0xd5, //0x00002a1f notq         %r13
	0x49, 0x29, 0xcd, //0x00002a22 subq         %rcx, %r13
	0x48, 0x8b, 0x75, 0xc0, //0x00002a25 movq         $-64(%rbp), %rsi
	0xe9, 0xb4, 0xd7, 0xff, 0xff, //0x00002a29 jmp          LBB0_2
	//0x00002a2e LBB0_438
	0x48, 0x85, 0xf6, //0x00002a2e testq        %rsi, %rsi
	0x0f, 0x85, 0x34, 0x12, 0x00, 0x00, //0x00002a31 jne          LBB0_649
	0x48, 0x83, 0xc0, 0x20, //0x00002a37 addq         $32, %rax
	0x49, 0x83, 0xc6, 0xe0, //0x00002a3b addq         $-32, %r14
	0x48, 0x85, 0xdb, //0x00002a3f testq        %rbx, %rbx
	0x0f, 0x85, 0xa5, 0x04, 0x00, 0x00, //0x00002a42 jne          LBB0_488
	//0x00002a48 LBB0_440
	0x48, 0x8b, 0x4d, 0xc8, //0x00002a48 movq         $-56(%rbp), %rcx
	0x4d, 0x85, 0xf6, //0x00002a4c testq        %r14, %r14
	0x0f, 0x84, 0xda, 0x11, 0x00, 0x00, //0x00002a4f je           LBB0_490
	//0x00002a55 LBB0_441
	0x0f, 0xb6, 0x10, //0x00002a55 movzbl       (%rax), %edx
	0x80, 0xfa, 0x22, //0x00002a58 cmpb         $34, %dl
	0x0f, 0x84, 0x8a, 0x00, 0x00, 0x00, //0x00002a5b je           LBB0_449
	0x80, 0xfa, 0x5c, //0x00002a61 cmpb         $92, %dl
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x00002a64 je           LBB0_445
	0x80, 0xfa, 0x1f, //0x00002a6a cmpb         $31, %dl
	0x0f, 0x86, 0x12, 0x12, 0x00, 0x00, //0x00002a6d jbe          LBB0_651
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00002a73 movq         $-1, %rdx
	0xbe, 0x01, 0x00, 0x00, 0x00, //0x00002a7a movl         $1, %esi
	0x48, 0x01, 0xf0, //0x00002a7f addq         %rsi, %rax
	0x49, 0x01, 0xd6, //0x00002a82 addq         %rdx, %r14
	0x0f, 0x85, 0xca, 0xff, 0xff, 0xff, //0x00002a85 jne          LBB0_441
	0xe9, 0x9f, 0x11, 0x00, 0x00, //0x00002a8b jmp          LBB0_490
	//0x00002a90 LBB0_445
	0x49, 0x83, 0xfe, 0x01, //0x00002a90 cmpq         $1, %r14
	0x0f, 0x84, 0x95, 0x11, 0x00, 0x00, //0x00002a94 je           LBB0_490
	0x66, 0x41, 0x0f, 0x6f, 0xf5, //0x00002a9a movdqa       %xmm13, %xmm6
	0x66, 0x0f, 0x6f, 0xea, //0x00002a9f movdqa       %xmm2, %xmm5
	0x48, 0xc7, 0xc2, 0xfe, 0xff, 0xff, 0xff, //0x00002aa3 movq         $-2, %rdx
	0xbe, 0x02, 0x00, 0x00, 0x00, //0x00002aaa movl         $2, %esi
	0x48, 0x83, 0xf9, 0xff, //0x00002aaf cmpq         $-1, %rcx
	0x0f, 0x85, 0x0a, 0x00, 0x00, 0x00, //0x00002ab3 jne          LBB0_448
	0x48, 0x89, 0xc1, //0x00002ab9 movq         %rax, %rcx
	0x4c, 0x29, 0xe1, //0x00002abc subq         %r12, %rcx
	0x48, 0x89, 0x4d, 0xc8, //0x00002abf movq         %rcx, $-56(%rbp)
	//0x00002ac3 LBB0_448
	0x4c, 0x8b, 0x6d, 0xb8, //0x00002ac3 movq         $-72(%rbp), %r13
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00002ac7 movabsq      $4294977024, %r11
	0x66, 0x0f, 0x6f, 0xd5, //0x00002ad1 movdqa       %xmm5, %xmm2
	0x66, 0x44, 0x0f, 0x6f, 0xee, //0x00002ad5 movdqa       %xmm6, %xmm13
	0x48, 0x01, 0xf0, //0x00002ada addq         %rsi, %rax
	0x49, 0x01, 0xd6, //0x00002add addq         %rdx, %r14
	0x0f, 0x85, 0x6f, 0xff, 0xff, 0xff, //0x00002ae0 jne          LBB0_441
	0xe9, 0x44, 0x11, 0x00, 0x00, //0x00002ae6 jmp          LBB0_490
	//0x00002aeb LBB0_449
	0x4c, 0x29, 0xe0, //0x00002aeb subq         %r12, %rax
	0x48, 0x83, 0xc0, 0x01, //0x00002aee addq         $1, %rax
	0x4c, 0x8b, 0x75, 0xd0, //0x00002af2 movq         $-48(%rbp), %r14
	0xe9, 0x8b, 0xe7, 0xff, 0xff, //0x00002af6 jmp          LBB0_223
	//0x00002afb LBB0_450
	0x48, 0x85, 0xff, //0x00002afb testq        %rdi, %rdi
	0x0f, 0x85, 0x76, 0x11, 0x00, 0x00, //0x00002afe jne          LBB0_650
	0x48, 0x83, 0xc0, 0x20, //0x00002b04 addq         $32, %rax
	0x49, 0x83, 0xc1, 0xe0, //0x00002b08 addq         $-32, %r9
	0x4d, 0x85, 0xe4, //0x00002b0c testq        %r12, %r12
	0x0f, 0x85, 0xa6, 0x04, 0x00, 0x00, //0x00002b0f jne          LBB0_496
	//0x00002b15 LBB0_452
	0x48, 0x8b, 0x4d, 0xc8, //0x00002b15 movq         $-56(%rbp), %rcx
	0x4d, 0x85, 0xc9, //0x00002b19 testq        %r9, %r9
	0x0f, 0x84, 0x51, 0x0f, 0x00, 0x00, //0x00002b1c je           LBB0_616
	//0x00002b22 LBB0_453
	0x0f, 0xb6, 0x10, //0x00002b22 movzbl       (%rax), %edx
	0x80, 0xfa, 0x22, //0x00002b25 cmpb         $34, %dl
	0x0f, 0x84, 0xcc, 0x00, 0x00, 0x00, //0x00002b28 je           LBB0_464
	0x80, 0xfa, 0x5c, //0x00002b2e cmpb         $92, %dl
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x00002b31 je           LBB0_458
	0x80, 0xfa, 0x1f, //0x00002b37 cmpb         $31, %dl
	0x0f, 0x86, 0x51, 0x11, 0x00, 0x00, //0x00002b3a jbe          LBB0_652
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00002b40 movq         $-1, %rdx
	0xbe, 0x01, 0x00, 0x00, 0x00, //0x00002b47 movl         $1, %esi
	//0x00002b4c LBB0_457
	0x48, 0x01, 0xf0, //0x00002b4c addq         %rsi, %rax
	0x49, 0x01, 0xd1, //0x00002b4f addq         %rdx, %r9
	0x0f, 0x85, 0xca, 0xff, 0xff, 0xff, //0x00002b52 jne          LBB0_453
	0xe9, 0x16, 0x0f, 0x00, 0x00, //0x00002b58 jmp          LBB0_616
	//0x00002b5d LBB0_458
	0x49, 0x83, 0xf9, 0x01, //0x00002b5d cmpq         $1, %r9
	0x0f, 0x84, 0xe7, 0x10, 0x00, 0x00, //0x00002b61 je           LBB0_507
	0x66, 0x0f, 0x6f, 0xea, //0x00002b67 movdqa       %xmm2, %xmm5
	0x48, 0xc7, 0xc2, 0xfe, 0xff, 0xff, 0xff, //0x00002b6b movq         $-2, %rdx
	0xbe, 0x02, 0x00, 0x00, 0x00, //0x00002b72 movl         $2, %esi
	0x48, 0x83, 0xf9, 0xff, //0x00002b77 cmpq         $-1, %rcx
	0x0f, 0x84, 0x28, 0x00, 0x00, 0x00, //0x00002b7b je           LBB0_461
	0x4c, 0x8b, 0x75, 0xd0, //0x00002b81 movq         $-48(%rbp), %r14
	0x4c, 0x8b, 0x6d, 0xb8, //0x00002b85 movq         $-72(%rbp), %r13
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00002b89 movabsq      $4294977024, %r11
	0x66, 0x0f, 0x6f, 0xd5, //0x00002b93 movdqa       %xmm5, %xmm2
	0xf3, 0x44, 0x0f, 0x6f, 0x2d, 0xa0, 0xd4, 0xff, 0xff, //0x00002b97 movdqu       $-11104(%rip), %xmm13  /* LCPI0_4+0(%rip) */
	0x4c, 0x8b, 0x45, 0xc0, //0x00002ba0 movq         $-64(%rbp), %r8
	0xe9, 0xa3, 0xff, 0xff, 0xff, //0x00002ba4 jmp          LBB0_457
	//0x00002ba9 LBB0_461
	0x48, 0x89, 0xc1, //0x00002ba9 movq         %rax, %rcx
	0x4c, 0x8b, 0x45, 0xc0, //0x00002bac movq         $-64(%rbp), %r8
	0x4c, 0x29, 0xc1, //0x00002bb0 subq         %r8, %rcx
	0x48, 0x89, 0x4d, 0xc8, //0x00002bb3 movq         %rcx, $-56(%rbp)
	0x4c, 0x8b, 0x75, 0xd0, //0x00002bb7 movq         $-48(%rbp), %r14
	0x4c, 0x8b, 0x6d, 0xb8, //0x00002bbb movq         $-72(%rbp), %r13
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00002bbf movabsq      $4294977024, %r11
	0x66, 0x0f, 0x6f, 0xd5, //0x00002bc9 movdqa       %xmm5, %xmm2
	0xf3, 0x44, 0x0f, 0x6f, 0x2d, 0x6a, 0xd4, 0xff, 0xff, //0x00002bcd movdqu       $-11158(%rip), %xmm13  /* LCPI0_4+0(%rip) */
	0xe9, 0x71, 0xff, 0xff, 0xff, //0x00002bd6 jmp          LBB0_457
	//0x00002bdb LBB0_462
	0x4c, 0x29, 0xe1, //0x00002bdb subq         %r12, %rcx
	0x48, 0x01, 0xf1, //0x00002bde addq         %rsi, %rcx
	0x49, 0x89, 0x0e, //0x00002be1 movq         %rcx, (%r14)
	0x48, 0x85, 0xc0, //0x00002be4 testq        %rax, %rax
	0x0f, 0x8f, 0xd3, 0xd5, 0xff, 0xff, //0x00002be7 jg           LBB0_4
	0xe9, 0x06, 0x0e, 0x00, 0x00, //0x00002bed jmp          LBB0_606
	//0x00002bf2 LBB0_463
	0x0f, 0xbc, 0xce, //0x00002bf2 bsfl         %esi, %ecx
	0xe9, 0x1f, 0x00, 0x00, 0x00, //0x00002bf5 jmp          LBB0_467
	//0x00002bfa LBB0_464
	0x4c, 0x29, 0xc0, //0x00002bfa subq         %r8, %rax
	0x48, 0x83, 0xc0, 0x01, //0x00002bfd addq         $1, %rax
	0xe9, 0xfd, 0xeb, 0xff, 0xff, //0x00002c01 jmp          LBB0_304
	//0x00002c06 LBB0_465
	0x49, 0x01, 0xc4, //0x00002c06 addq         %rax, %r12
	0x49, 0x29, 0xd4, //0x00002c09 subq         %rdx, %r12
	0x48, 0xf7, 0xd3, //0x00002c0c notq         %rbx
	0x4c, 0x01, 0xe3, //0x00002c0f addq         %r12, %rbx
	0xe9, 0x08, 0x00, 0x00, 0x00, //0x00002c12 jmp          LBB0_468
	//0x00002c17 LBB0_466
	0x89, 0xd1, //0x00002c17 movl         %edx, %ecx
	//0x00002c19 LBB0_467
	0x48, 0xf7, 0xd3, //0x00002c19 notq         %rbx
	0x48, 0x29, 0xcb, //0x00002c1c subq         %rcx, %rbx
	//0x00002c1f LBB0_468
	0x48, 0x85, 0xdb, //0x00002c1f testq        %rbx, %rbx
	0x0f, 0x88, 0xae, 0x0f, 0x00, 0x00, //0x00002c22 js           LBB0_636
	//0x00002c28 LBB0_469
	0x48, 0x01, 0xc3, //0x00002c28 addq         %rax, %rbx
	0x4c, 0x8b, 0x75, 0xd0, //0x00002c2b movq         $-48(%rbp), %r14
	0x49, 0x89, 0x1e, //0x00002c2f movq         %rbx, (%r14)
	0x48, 0x85, 0xc0, //0x00002c32 testq        %rax, %rax
	0x4c, 0x8b, 0x6d, 0xb8, //0x00002c35 movq         $-72(%rbp), %r13
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00002c39 movabsq      $4294977024, %r11
	0x0f, 0x8f, 0x77, 0xd5, 0xff, 0xff, //0x00002c43 jg           LBB0_4
	0xe9, 0xaa, 0x0d, 0x00, 0x00, //0x00002c49 jmp          LBB0_606
	//0x00002c4e LBB0_241
	0x4c, 0x01, 0xe0, //0x00002c4e addq         %r12, %rax
	0x49, 0x83, 0xf9, 0x20, //0x00002c51 cmpq         $32, %r9
	0x4c, 0x8b, 0x75, 0xd0, //0x00002c55 movq         $-48(%rbp), %r14
	0x0f, 0x82, 0x19, 0x04, 0x00, 0x00, //0x00002c59 jb           LBB0_502
	//0x00002c5f LBB0_242
	0xf3, 0x0f, 0x6f, 0x18, //0x00002c5f movdqu       (%rax), %xmm3
	0xf3, 0x0f, 0x6f, 0x60, 0x10, //0x00002c63 movdqu       $16(%rax), %xmm4
	0x66, 0x0f, 0x6f, 0xeb, //0x00002c68 movdqa       %xmm3, %xmm5
	0x66, 0x0f, 0x74, 0xe8, //0x00002c6c pcmpeqb      %xmm0, %xmm5
	0x66, 0x0f, 0xd7, 0xd5, //0x00002c70 pmovmskb     %xmm5, %edx
	0x66, 0x0f, 0x6f, 0xec, //0x00002c74 movdqa       %xmm4, %xmm5
	0x66, 0x0f, 0x74, 0xe8, //0x00002c78 pcmpeqb      %xmm0, %xmm5
	0x66, 0x0f, 0xd7, 0xcd, //0x00002c7c pmovmskb     %xmm5, %ecx
	0x66, 0x0f, 0x74, 0xd9, //0x00002c80 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xf3, //0x00002c84 pmovmskb     %xmm3, %esi
	0x66, 0x0f, 0x74, 0xe1, //0x00002c88 pcmpeqb      %xmm1, %xmm4
	0x66, 0x0f, 0xd7, 0xfc, //0x00002c8c pmovmskb     %xmm4, %edi
	0x48, 0xc1, 0xe1, 0x10, //0x00002c90 shlq         $16, %rcx
	0x48, 0x09, 0xca, //0x00002c94 orq          %rcx, %rdx
	0x48, 0xc1, 0xe7, 0x10, //0x00002c97 shlq         $16, %rdi
	0x48, 0x09, 0xfe, //0x00002c9b orq          %rdi, %rsi
	0x0f, 0x85, 0x06, 0x0b, 0x00, 0x00, //0x00002c9e jne          LBB0_578
	0x4d, 0x85, 0xc0, //0x00002ca4 testq        %r8, %r8
	0x0f, 0x85, 0x27, 0x0b, 0x00, 0x00, //0x00002ca7 jne          LBB0_580
	0x45, 0x31, 0xc0, //0x00002cad xorl         %r8d, %r8d
	0x48, 0x85, 0xd2, //0x00002cb0 testq        %rdx, %rdx
	0x0f, 0x84, 0x77, 0x0b, 0x00, 0x00, //0x00002cb3 je           LBB0_582
	//0x00002cb9 LBB0_245
	0x48, 0x0f, 0xbc, 0xca, //0x00002cb9 bsfq         %rdx, %rcx
	0x4c, 0x29, 0xe0, //0x00002cbd subq         %r12, %rax
	0x48, 0x01, 0xc8, //0x00002cc0 addq         %rcx, %rax
	0x48, 0x83, 0xc0, 0x01, //0x00002cc3 addq         $1, %rax
	0xe9, 0x71, 0xf9, 0xff, 0xff, //0x00002cc7 jmp          LBB0_426
	//0x00002ccc LBB0_394
	0x4c, 0x01, 0xe0, //0x00002ccc addq         %r12, %rax
	0x49, 0x89, 0xf2, //0x00002ccf movq         %rsi, %r10
	0x48, 0x83, 0xfe, 0x20, //0x00002cd2 cmpq         $32, %rsi
	0x0f, 0x82, 0xc5, 0x03, 0x00, 0x00, //0x00002cd6 jb           LBB0_504
	//0x00002cdc LBB0_395
	0xf3, 0x0f, 0x6f, 0x20, //0x00002cdc movdqu       (%rax), %xmm4
	0xf3, 0x0f, 0x6f, 0x58, 0x10, //0x00002ce0 movdqu       $16(%rax), %xmm3
	0x66, 0x0f, 0x6f, 0xec, //0x00002ce5 movdqa       %xmm4, %xmm5
	0x66, 0x0f, 0x74, 0xe8, //0x00002ce9 pcmpeqb      %xmm0, %xmm5
	0x66, 0x0f, 0xd7, 0xf5, //0x00002ced pmovmskb     %xmm5, %esi
	0x66, 0x0f, 0x6f, 0xeb, //0x00002cf1 movdqa       %xmm3, %xmm5
	0x66, 0x0f, 0x74, 0xe8, //0x00002cf5 pcmpeqb      %xmm0, %xmm5
	0x66, 0x0f, 0xd7, 0xcd, //0x00002cf9 pmovmskb     %xmm5, %ecx
	0x66, 0x0f, 0x6f, 0xec, //0x00002cfd movdqa       %xmm4, %xmm5
	0x66, 0x0f, 0x74, 0xe9, //0x00002d01 pcmpeqb      %xmm1, %xmm5
	0x66, 0x44, 0x0f, 0xd7, 0xcd, //0x00002d05 pmovmskb     %xmm5, %r9d
	0x66, 0x0f, 0x6f, 0xeb, //0x00002d0a movdqa       %xmm3, %xmm5
	0x66, 0x0f, 0x74, 0xe9, //0x00002d0e pcmpeqb      %xmm1, %xmm5
	0x66, 0x0f, 0xd7, 0xd5, //0x00002d12 pmovmskb     %xmm5, %edx
	0x66, 0x41, 0x0f, 0x6f, 0xec, //0x00002d16 movdqa       %xmm12, %xmm5
	0x66, 0x0f, 0x64, 0xeb, //0x00002d1b pcmpgtb      %xmm3, %xmm5
	0x66, 0x41, 0x0f, 0x64, 0xda, //0x00002d1f pcmpgtb      %xmm10, %xmm3
	0x66, 0x0f, 0xdb, 0xdd, //0x00002d24 pand         %xmm5, %xmm3
	0x66, 0x0f, 0xd7, 0xfb, //0x00002d28 pmovmskb     %xmm3, %edi
	0x48, 0xc1, 0xe1, 0x10, //0x00002d2c shlq         $16, %rcx
	0x48, 0x09, 0xce, //0x00002d30 orq          %rcx, %rsi
	0x48, 0xc1, 0xe2, 0x10, //0x00002d33 shlq         $16, %rdx
	0x48, 0xc1, 0xe7, 0x10, //0x00002d37 shlq         $16, %rdi
	0x49, 0x09, 0xd1, //0x00002d3b orq          %rdx, %r9
	0x0f, 0x85, 0xd0, 0x0b, 0x00, 0x00, //0x00002d3e jne          LBB0_597
	0x48, 0x85, 0xdb, //0x00002d44 testq        %rbx, %rbx
	0x4c, 0x8b, 0x6d, 0xb8, //0x00002d47 movq         $-72(%rbp), %r13
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00002d4b movabsq      $4294977024, %r11
	0x0f, 0x85, 0xe6, 0x0b, 0x00, 0x00, //0x00002d55 jne          LBB0_599
	0x31, 0xdb, //0x00002d5b xorl         %ebx, %ebx
	//0x00002d5d LBB0_398
	0x66, 0x41, 0x0f, 0x6f, 0xdc, //0x00002d5d movdqa       %xmm12, %xmm3
	0x66, 0x0f, 0x64, 0xdc, //0x00002d62 pcmpgtb      %xmm4, %xmm3
	0x66, 0x41, 0x0f, 0x64, 0xe2, //0x00002d66 pcmpgtb      %xmm10, %xmm4
	0x66, 0x0f, 0xdb, 0xe3, //0x00002d6b pand         %xmm3, %xmm4
	0x66, 0x0f, 0xd7, 0xcc, //0x00002d6f pmovmskb     %xmm4, %ecx
	0x48, 0x09, 0xcf, //0x00002d73 orq          %rcx, %rdi
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x00002d76 movl         $64, %ecx
	0xba, 0x40, 0x00, 0x00, 0x00, //0x00002d7b movl         $64, %edx
	0x48, 0x85, 0xf6, //0x00002d80 testq        %rsi, %rsi
	0x0f, 0x84, 0x04, 0x00, 0x00, 0x00, //0x00002d83 je           LBB0_400
	0x48, 0x0f, 0xbc, 0xd6, //0x00002d89 bsfq         %rsi, %rdx
	//0x00002d8d LBB0_400
	0x48, 0x85, 0xff, //0x00002d8d testq        %rdi, %rdi
	0x0f, 0x84, 0x04, 0x00, 0x00, 0x00, //0x00002d90 je           LBB0_402
	0x48, 0x0f, 0xbc, 0xcf, //0x00002d96 bsfq         %rdi, %rcx
	//0x00002d9a LBB0_402
	0x48, 0x85, 0xf6, //0x00002d9a testq        %rsi, %rsi
	0x0f, 0x84, 0x53, 0x00, 0x00, 0x00, //0x00002d9d je           LBB0_473
	0x4c, 0x29, 0xe0, //0x00002da3 subq         %r12, %rax
	0x48, 0x39, 0xd1, //0x00002da6 cmpq         %rdx, %rcx
	0x0f, 0x82, 0xf7, 0x0e, 0x00, 0x00, //0x00002da9 jb           LBB0_654
	0x48, 0x01, 0xd0, //0x00002daf addq         %rdx, %rax
	0x48, 0x83, 0xc0, 0x01, //0x00002db2 addq         $1, %rax
	0xe9, 0x82, 0xf8, 0xff, 0xff, //0x00002db6 jmp          LBB0_426
	//0x00002dbb LBB0_470
	0x49, 0x8d, 0x04, 0x34, //0x00002dbb leaq         (%r12,%rsi), %rax
	0x48, 0x85, 0xc9, //0x00002dbf testq        %rcx, %rcx
	0x0f, 0x85, 0x4f, 0xde, 0xff, 0xff, //0x00002dc2 jne          LBB0_140
	//0x00002dc8 LBB0_471
	0x66, 0x41, 0x0f, 0x6f, 0xf5, //0x00002dc8 movdqa       %xmm13, %xmm6
	0x66, 0x0f, 0x6f, 0xea, //0x00002dcd movdqa       %xmm2, %xmm5
	0xe9, 0x7f, 0xde, 0xff, 0xff, //0x00002dd1 jmp          LBB0_146
	//0x00002dd6 LBB0_472
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00002dd6 movq         $-1, %r8
	0x4c, 0x89, 0xf1, //0x00002ddd movq         %r14, %rcx
	0x49, 0x89, 0xfa, //0x00002de0 movq         %rdi, %r10
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00002de3 movq         $-1, %r15
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00002dea movq         $-1, %r9
	0xe9, 0x19, 0xda, 0xff, 0xff, //0x00002df1 jmp          LBB0_105
	//0x00002df6 LBB0_473
	0x48, 0x85, 0xff, //0x00002df6 testq        %rdi, %rdi
	0x0f, 0x85, 0xbc, 0x0e, 0x00, 0x00, //0x00002df9 jne          LBB0_655
	0x66, 0x41, 0x0f, 0x6f, 0xf5, //0x00002dff movdqa       %xmm13, %xmm6
	0x66, 0x0f, 0x6f, 0xea, //0x00002e04 movdqa       %xmm2, %xmm5
	0x48, 0x83, 0xc0, 0x20, //0x00002e08 addq         $32, %rax
	0x49, 0x83, 0xc2, 0xe0, //0x00002e0c addq         $-32, %r10
	0x48, 0x85, 0xdb, //0x00002e10 testq        %rbx, %rbx
	0x0f, 0x85, 0x9a, 0x02, 0x00, 0x00, //0x00002e13 jne          LBB0_505
	//0x00002e19 LBB0_475
	0x48, 0x8b, 0x55, 0xc8, //0x00002e19 movq         $-56(%rbp), %rdx
	0x4d, 0x85, 0xd2, //0x00002e1d testq        %r10, %r10
	0x0f, 0x84, 0x28, 0x0e, 0x00, 0x00, //0x00002e20 je           LBB0_507
	//0x00002e26 LBB0_476
	0x0f, 0xb6, 0x08, //0x00002e26 movzbl       (%rax), %ecx
	0x80, 0xf9, 0x22, //0x00002e29 cmpb         $34, %cl
	0x0f, 0x84, 0x11, 0x01, 0x00, 0x00, //0x00002e2c je           LBB0_491
	0x80, 0xf9, 0x5c, //0x00002e32 cmpb         $92, %cl
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x00002e35 je           LBB0_481
	0x80, 0xf9, 0x1f, //0x00002e3b cmpb         $31, %cl
	0x0f, 0x86, 0x82, 0x0e, 0x00, 0x00, //0x00002e3e jbe          LBB0_656
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00002e44 movq         $-1, %rcx
	0xbe, 0x01, 0x00, 0x00, 0x00, //0x00002e4b movl         $1, %esi
	//0x00002e50 LBB0_480
	0x48, 0x01, 0xf0, //0x00002e50 addq         %rsi, %rax
	0x49, 0x01, 0xca, //0x00002e53 addq         %rcx, %r10
	0x0f, 0x85, 0xca, 0xff, 0xff, 0xff, //0x00002e56 jne          LBB0_476
	0xe9, 0xed, 0x0d, 0x00, 0x00, //0x00002e5c jmp          LBB0_507
	//0x00002e61 LBB0_481
	0x49, 0x83, 0xfa, 0x01, //0x00002e61 cmpq         $1, %r10
	0x0f, 0x84, 0xe3, 0x0d, 0x00, 0x00, //0x00002e65 je           LBB0_507
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00002e6b movq         $-2, %rcx
	0xbe, 0x02, 0x00, 0x00, 0x00, //0x00002e72 movl         $2, %esi
	0x48, 0x83, 0xfa, 0xff, //0x00002e77 cmpq         $-1, %rdx
	0x0f, 0x85, 0xcf, 0xff, 0xff, 0xff, //0x00002e7b jne          LBB0_480
	0x48, 0x89, 0xc2, //0x00002e81 movq         %rax, %rdx
	0x4c, 0x29, 0xe2, //0x00002e84 subq         %r12, %rdx
	0x48, 0x89, 0x55, 0xc8, //0x00002e87 movq         %rdx, $-56(%rbp)
	0xe9, 0xc0, 0xff, 0xff, 0xff, //0x00002e8b jmp          LBB0_480
	//0x00002e90 LBB0_484
	0x48, 0x8b, 0x45, 0xb0, //0x00002e90 movq         $-80(%rbp), %rax
	0x4c, 0x01, 0xe0, //0x00002e94 addq         %r12, %rax
	0x48, 0xc7, 0x45, 0xc8, 0xff, 0xff, 0xff, 0xff, //0x00002e97 movq         $-1, $-56(%rbp)
	0x45, 0x31, 0xc0, //0x00002e9f xorl         %r8d, %r8d
	0x49, 0x83, 0xf9, 0x20, //0x00002ea2 cmpq         $32, %r9
	0x0f, 0x83, 0x61, 0xf8, 0xff, 0xff, //0x00002ea6 jae          LBB0_51
	//0x00002eac LBB0_485
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00002eac movabsq      $4294977024, %r11
	0xe9, 0x6a, 0x03, 0x00, 0x00, //0x00002eb6 jmp          LBB0_520
	//0x00002ebb LBB0_486
	0x48, 0x8b, 0x45, 0xb0, //0x00002ebb movq         $-80(%rbp), %rax
	0x4c, 0x01, 0xe0, //0x00002ebf addq         %r12, %rax
	0x48, 0xc7, 0x45, 0xc8, 0xff, 0xff, 0xff, 0xff, //0x00002ec2 movq         $-1, $-56(%rbp)
	0x31, 0xdb, //0x00002eca xorl         %ebx, %ebx
	0x49, 0x83, 0xfe, 0x20, //0x00002ecc cmpq         $32, %r14
	0x0f, 0x83, 0xbf, 0xf8, 0xff, 0xff, //0x00002ed0 jae          LBB0_167
	//0x00002ed6 LBB0_487
	0x4c, 0x8b, 0x6d, 0xb8, //0x00002ed6 movq         $-72(%rbp), %r13
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00002eda movabsq      $4294977024, %r11
	0x48, 0x85, 0xdb, //0x00002ee4 testq        %rbx, %rbx
	0x0f, 0x84, 0x5b, 0xfb, 0xff, 0xff, //0x00002ee7 je           LBB0_440
	//0x00002eed LBB0_488
	0x4d, 0x85, 0xf6, //0x00002eed testq        %r14, %r14
	0x0f, 0x84, 0x39, 0x0d, 0x00, 0x00, //0x00002ef0 je           LBB0_490
	0x66, 0x41, 0x0f, 0x6f, 0xf5, //0x00002ef6 movdqa       %xmm13, %xmm6
	0x66, 0x0f, 0x6f, 0xea, //0x00002efb movdqa       %xmm2, %xmm5
	0x4c, 0x89, 0xe1, //0x00002eff movq         %r12, %rcx
	0x48, 0xf7, 0xd1, //0x00002f02 notq         %rcx
	0x48, 0x01, 0xc1, //0x00002f05 addq         %rax, %rcx
	0x48, 0x8b, 0x75, 0xc8, //0x00002f08 movq         $-56(%rbp), %rsi
	0x48, 0x83, 0xfe, 0xff, //0x00002f0c cmpq         $-1, %rsi
	0x48, 0x89, 0xf2, //0x00002f10 movq         %rsi, %rdx
	0x48, 0x0f, 0x44, 0xd1, //0x00002f13 cmoveq       %rcx, %rdx
	0x48, 0x0f, 0x45, 0xce, //0x00002f17 cmovneq      %rsi, %rcx
	0x48, 0x83, 0xc0, 0x01, //0x00002f1b addq         $1, %rax
	0x49, 0x83, 0xc6, 0xff, //0x00002f1f addq         $-1, %r14
	0x48, 0x89, 0x55, 0xc8, //0x00002f23 movq         %rdx, $-56(%rbp)
	0x4c, 0x8b, 0x6d, 0xb8, //0x00002f27 movq         $-72(%rbp), %r13
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00002f2b movabsq      $4294977024, %r11
	0x4d, 0x85, 0xf6, //0x00002f35 testq        %r14, %r14
	0x0f, 0x85, 0x17, 0xfb, 0xff, 0xff, //0x00002f38 jne          LBB0_441
	0xe9, 0xec, 0x0c, 0x00, 0x00, //0x00002f3e jmp          LBB0_490
	//0x00002f43 LBB0_491
	0x4c, 0x29, 0xe0, //0x00002f43 subq         %r12, %rax
	0x48, 0x83, 0xc0, 0x01, //0x00002f46 addq         $1, %rax
	0xe9, 0x94, 0x09, 0x00, 0x00, //0x00002f4a jmp          LBB0_596
	//0x00002f4f LBB0_492
	0x48, 0x8b, 0x45, 0xb0, //0x00002f4f movq         $-80(%rbp), %rax
	0x4c, 0x01, 0xe0, //0x00002f53 addq         %r12, %rax
	0x48, 0xc7, 0x45, 0xc8, 0xff, 0xff, 0xff, 0xff, //0x00002f56 movq         $-1, $-56(%rbp)
	0x45, 0x31, 0xc0, //0x00002f5e xorl         %r8d, %r8d
	0x49, 0x83, 0xf9, 0x20, //0x00002f61 cmpq         $32, %r9
	0x4c, 0x8b, 0x75, 0xd0, //0x00002f65 movq         $-48(%rbp), %r14
	0x0f, 0x83, 0x3d, 0xf9, 0xff, 0xff, //0x00002f69 jae          LBB0_74
	//0x00002f6f LBB0_493
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00002f6f movabsq      $4294977024, %r11
	0xe9, 0x56, 0x05, 0x00, 0x00, //0x00002f79 jmp          LBB0_549
	//0x00002f7e LBB0_494
	0x4c, 0x8b, 0x45, 0xc0, //0x00002f7e movq         $-64(%rbp), %r8
	0x49, 0x8d, 0x04, 0x08, //0x00002f82 leaq         (%r8,%rcx), %rax
	0x48, 0xc7, 0x45, 0xc8, 0xff, 0xff, 0xff, 0xff, //0x00002f86 movq         $-1, $-56(%rbp)
	0x45, 0x31, 0xe4, //0x00002f8e xorl         %r12d, %r12d
	0x49, 0x83, 0xf9, 0x20, //0x00002f91 cmpq         $32, %r9
	0x0f, 0x83, 0x99, 0xf9, 0xff, 0xff, //0x00002f95 jae          LBB0_196
	//0x00002f9b LBB0_495
	0x4c, 0x8b, 0x6d, 0xb8, //0x00002f9b movq         $-72(%rbp), %r13
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00002f9f movabsq      $4294977024, %r11
	0xf3, 0x44, 0x0f, 0x6f, 0x2d, 0x8e, 0xd0, 0xff, 0xff, //0x00002fa9 movdqu       $-12146(%rip), %xmm13  /* LCPI0_4+0(%rip) */
	0x4d, 0x85, 0xe4, //0x00002fb2 testq        %r12, %r12
	0x0f, 0x84, 0x5a, 0xfb, 0xff, 0xff, //0x00002fb5 je           LBB0_452
	//0x00002fbb LBB0_496
	0x4d, 0x85, 0xc9, //0x00002fbb testq        %r9, %r9
	0x0f, 0x84, 0x8a, 0x0c, 0x00, 0x00, //0x00002fbe je           LBB0_507
	0x66, 0x0f, 0x6f, 0xea, //0x00002fc4 movdqa       %xmm2, %xmm5
	0x4c, 0x8b, 0x45, 0xc0, //0x00002fc8 movq         $-64(%rbp), %r8
	0x4c, 0x89, 0xc1, //0x00002fcc movq         %r8, %rcx
	0x48, 0xf7, 0xd1, //0x00002fcf notq         %rcx
	0x48, 0x01, 0xc1, //0x00002fd2 addq         %rax, %rcx
	0x48, 0x8b, 0x75, 0xc8, //0x00002fd5 movq         $-56(%rbp), %rsi
	0x48, 0x83, 0xfe, 0xff, //0x00002fd9 cmpq         $-1, %rsi
	0x48, 0x89, 0xf2, //0x00002fdd movq         %rsi, %rdx
	0x48, 0x0f, 0x44, 0xd1, //0x00002fe0 cmoveq       %rcx, %rdx
	0x48, 0x0f, 0x45, 0xce, //0x00002fe4 cmovneq      %rsi, %rcx
	0x48, 0x83, 0xc0, 0x01, //0x00002fe8 addq         $1, %rax
	0x49, 0x83, 0xc1, 0xff, //0x00002fec addq         $-1, %r9
	0x48, 0x89, 0x55, 0xc8, //0x00002ff0 movq         %rdx, $-56(%rbp)
	0x4c, 0x8b, 0x75, 0xd0, //0x00002ff4 movq         $-48(%rbp), %r14
	0x4c, 0x8b, 0x6d, 0xb8, //0x00002ff8 movq         $-72(%rbp), %r13
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00002ffc movabsq      $4294977024, %r11
	0xf3, 0x44, 0x0f, 0x6f, 0x2d, 0x31, 0xd0, 0xff, 0xff, //0x00003006 movdqu       $-12239(%rip), %xmm13  /* LCPI0_4+0(%rip) */
	0x4d, 0x85, 0xc9, //0x0000300f testq        %r9, %r9
	0x0f, 0x85, 0x0a, 0xfb, 0xff, 0xff, //0x00003012 jne          LBB0_453
	0xe9, 0x56, 0x0a, 0x00, 0x00, //0x00003018 jmp          LBB0_616
	//0x0000301d LBB0_498
	0x49, 0x8d, 0x0c, 0x04, //0x0000301d leaq         (%r12,%rax), %rcx
	0x48, 0x85, 0xd2, //0x00003021 testq        %rdx, %rdx
	0x0f, 0x85, 0x1d, 0xee, 0xff, 0xff, //0x00003024 jne          LBB0_349
	//0x0000302a LBB0_499
	0x66, 0x41, 0x0f, 0x6f, 0xf5, //0x0000302a movdqa       %xmm13, %xmm6
	0x66, 0x0f, 0x6f, 0xea, //0x0000302f movdqa       %xmm2, %xmm5
	0xe9, 0x4d, 0xee, 0xff, 0xff, //0x00003033 jmp          LBB0_355
	//0x00003038 LBB0_500
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00003038 movq         $-1, %r14
	0x4c, 0x89, 0xc2, //0x0000303f movq         %r8, %rdx
	0x49, 0x89, 0xf9, //0x00003042 movq         %rdi, %r9
	0x49, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00003045 movq         $-1, %r10
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x0000304c movq         $-1, %r13
	0xe9, 0x2e, 0xe6, 0xff, 0xff, //0x00003053 jmp          LBB0_275
	//0x00003058 LBB0_501
	0x48, 0x8b, 0x45, 0xb0, //0x00003058 movq         $-80(%rbp), %rax
	0x4c, 0x01, 0xe0, //0x0000305c addq         %r12, %rax
	0x48, 0xc7, 0x45, 0xc8, 0xff, 0xff, 0xff, 0xff, //0x0000305f movq         $-1, $-56(%rbp)
	0x45, 0x31, 0xc0, //0x00003067 xorl         %r8d, %r8d
	0x49, 0x83, 0xf9, 0x20, //0x0000306a cmpq         $32, %r9
	0x4c, 0x8b, 0x75, 0xd0, //0x0000306e movq         $-48(%rbp), %r14
	0x0f, 0x83, 0xe7, 0xfb, 0xff, 0xff, //0x00003072 jae          LBB0_242
	//0x00003078 LBB0_502
	0x66, 0x41, 0x0f, 0x6f, 0xf5, //0x00003078 movdqa       %xmm13, %xmm6
	0x66, 0x0f, 0x6f, 0xea, //0x0000307d movdqa       %xmm2, %xmm5
	0xe9, 0xbb, 0x07, 0x00, 0x00, //0x00003081 jmp          LBB0_583
	//0x00003086 LBB0_503
	0x49, 0x8d, 0x04, 0x0c, //0x00003086 leaq         (%r12,%rcx), %rax
	0x48, 0xc7, 0x45, 0xc8, 0xff, 0xff, 0xff, 0xff, //0x0000308a movq         $-1, $-56(%rbp)
	0x31, 0xdb, //0x00003092 xorl         %ebx, %ebx
	0x49, 0x89, 0xf2, //0x00003094 movq         %rsi, %r10
	0x48, 0x83, 0xfe, 0x20, //0x00003097 cmpq         $32, %rsi
	0x0f, 0x83, 0x3b, 0xfc, 0xff, 0xff, //0x0000309b jae          LBB0_395
	//0x000030a1 LBB0_504
	0x66, 0x41, 0x0f, 0x6f, 0xf5, //0x000030a1 movdqa       %xmm13, %xmm6
	0x66, 0x0f, 0x6f, 0xea, //0x000030a6 movdqa       %xmm2, %xmm5
	0x48, 0x85, 0xdb, //0x000030aa testq        %rbx, %rbx
	0x0f, 0x84, 0x66, 0xfd, 0xff, 0xff, //0x000030ad je           LBB0_475
	//0x000030b3 LBB0_505
	0x4d, 0x85, 0xd2, //0x000030b3 testq        %r10, %r10
	0x0f, 0x84, 0x92, 0x0b, 0x00, 0x00, //0x000030b6 je           LBB0_507
	0x4c, 0x89, 0xe2, //0x000030bc movq         %r12, %rdx
	0x48, 0xf7, 0xd2, //0x000030bf notq         %rdx
	0x48, 0x01, 0xc2, //0x000030c2 addq         %rax, %rdx
	0x48, 0x8b, 0x75, 0xc8, //0x000030c5 movq         $-56(%rbp), %rsi
	0x48, 0x83, 0xfe, 0xff, //0x000030c9 cmpq         $-1, %rsi
	0x48, 0x89, 0xf1, //0x000030cd movq         %rsi, %rcx
	0x48, 0x0f, 0x44, 0xca, //0x000030d0 cmoveq       %rdx, %rcx
	0x48, 0x0f, 0x45, 0xd6, //0x000030d4 cmovneq      %rsi, %rdx
	0x48, 0x83, 0xc0, 0x01, //0x000030d8 addq         $1, %rax
	0x49, 0x83, 0xc2, 0xff, //0x000030dc addq         $-1, %r10
	0x48, 0x89, 0x4d, 0xc8, //0x000030e0 movq         %rcx, $-56(%rbp)
	0x4d, 0x85, 0xd2, //0x000030e4 testq        %r10, %r10
	0x0f, 0x85, 0x39, 0xfd, 0xff, 0xff, //0x000030e7 jne          LBB0_476
	0xe9, 0x5c, 0x0b, 0x00, 0x00, //0x000030ed jmp          LBB0_507
	//0x000030f2 LBB0_508
	0x49, 0x39, 0xd1, //0x000030f2 cmpq         %rdx, %r9
	0x0f, 0x84, 0x4c, 0x09, 0x00, 0x00, //0x000030f5 je           LBB0_638
	0x66, 0x41, 0x0f, 0x6f, 0xf5, //0x000030fb movdqa       %xmm13, %xmm6
	0x66, 0x0f, 0x6f, 0xea, //0x00003100 movdqa       %xmm2, %xmm5
	0x49, 0x01, 0xd2, //0x00003104 addq         %rdx, %r10
	0x49, 0x83, 0xc2, 0x01, //0x00003107 addq         $1, %r10
	0x48, 0xf7, 0xd2, //0x0000310b notq         %rdx
	0x49, 0x01, 0xd1, //0x0000310e addq         %rdx, %r9
	0x4c, 0x8b, 0x75, 0xd0, //0x00003111 movq         $-48(%rbp), %r14
	0x4c, 0x8b, 0x6d, 0xb8, //0x00003115 movq         $-72(%rbp), %r13
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00003119 movabsq      $4294977024, %r11
	0x4d, 0x85, 0xc9, //0x00003123 testq        %r9, %r9
	0x0f, 0x8f, 0x33, 0x00, 0x00, 0x00, //0x00003126 jg           LBB0_512
	0xe9, 0x16, 0x09, 0x00, 0x00, //0x0000312c jmp          LBB0_638
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00003131 .p2align 4, 0x90
	//0x00003140 LBB0_510
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00003140 movq         $-2, %rcx
	0xb8, 0x02, 0x00, 0x00, 0x00, //0x00003147 movl         $2, %eax
	0x49, 0x01, 0xc2, //0x0000314c addq         %rax, %r10
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x0000314f movq         $-1, %rax
	0x49, 0x01, 0xc9, //0x00003156 addq         %rcx, %r9
	0x0f, 0x8e, 0xe8, 0x08, 0x00, 0x00, //0x00003159 jle          LBB0_638
	//0x0000315f LBB0_512
	0x41, 0x0f, 0xb6, 0x02, //0x0000315f movzbl       (%r10), %eax
	0x3c, 0x5c, //0x00003163 cmpb         $92, %al
	0x0f, 0x84, 0xd5, 0xff, 0xff, 0xff, //0x00003165 je           LBB0_510
	0x3c, 0x22, //0x0000316b cmpb         $34, %al
	0x0f, 0x84, 0x1f, 0x06, 0x00, 0x00, //0x0000316d je           LBB0_577
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00003173 movq         $-1, %rcx
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x0000317a movl         $1, %eax
	0x49, 0x01, 0xc2, //0x0000317f addq         %rax, %r10
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00003182 movq         $-1, %rax
	0x49, 0x01, 0xc9, //0x00003189 addq         %rcx, %r9
	0x0f, 0x8f, 0xcd, 0xff, 0xff, 0xff, //0x0000318c jg           LBB0_512
	0xe9, 0xb0, 0x08, 0x00, 0x00, //0x00003192 jmp          LBB0_638
	//0x00003197 LBB0_515
	0x66, 0x41, 0x0f, 0x6f, 0xf5, //0x00003197 movdqa       %xmm13, %xmm6
	0x66, 0x0f, 0x6f, 0xea, //0x0000319c movdqa       %xmm2, %xmm5
	0x48, 0x83, 0x7d, 0xc8, 0xff, //0x000031a0 cmpq         $-1, $-56(%rbp)
	0x0f, 0x85, 0x1f, 0x00, 0x00, 0x00, //0x000031a5 jne          LBB0_518
	0x48, 0x89, 0xc1, //0x000031ab movq         %rax, %rcx
	0x4c, 0x29, 0xe1, //0x000031ae subq         %r12, %rcx
	0x48, 0x0f, 0xbc, 0xfe, //0x000031b1 bsfq         %rsi, %rdi
	0x48, 0x01, 0xcf, //0x000031b5 addq         %rcx, %rdi
	0x48, 0x89, 0x7d, 0xc8, //0x000031b8 movq         %rdi, $-56(%rbp)
	0xe9, 0x09, 0x00, 0x00, 0x00, //0x000031bc jmp          LBB0_518
	//0x000031c1 LBB0_517
	0x66, 0x41, 0x0f, 0x6f, 0xf5, //0x000031c1 movdqa       %xmm13, %xmm6
	0x66, 0x0f, 0x6f, 0xea, //0x000031c6 movdqa       %xmm2, %xmm5
	//0x000031ca LBB0_518
	0x44, 0x89, 0xc1, //0x000031ca movl         %r8d, %ecx
	0xf7, 0xd1, //0x000031cd notl         %ecx
	0x21, 0xf1, //0x000031cf andl         %esi, %ecx
	0x41, 0x8d, 0x3c, 0x48, //0x000031d1 leal         (%r8,%rcx,2), %edi
	0x8d, 0x1c, 0x09, //0x000031d5 leal         (%rcx,%rcx), %ebx
	0xf7, 0xd3, //0x000031d8 notl         %ebx
	0x21, 0xf3, //0x000031da andl         %esi, %ebx
	0x81, 0xe3, 0xaa, 0xaa, 0xaa, 0xaa, //0x000031dc andl         $-1431655766, %ebx
	0x45, 0x31, 0xc0, //0x000031e2 xorl         %r8d, %r8d
	0x01, 0xcb, //0x000031e5 addl         %ecx, %ebx
	0x41, 0x0f, 0x92, 0xc0, //0x000031e7 setb         %r8b
	0x01, 0xdb, //0x000031eb addl         %ebx, %ebx
	0x81, 0xf3, 0x55, 0x55, 0x55, 0x55, //0x000031ed xorl         $1431655765, %ebx
	0x21, 0xfb, //0x000031f3 andl         %edi, %ebx
	0xf7, 0xd3, //0x000031f5 notl         %ebx
	0x21, 0xda, //0x000031f7 andl         %ebx, %edx
	0x4c, 0x8b, 0x75, 0xd0, //0x000031f9 movq         $-48(%rbp), %r14
	0x4c, 0x8b, 0x6d, 0xb8, //0x000031fd movq         $-72(%rbp), %r13
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00003201 movabsq      $4294977024, %r11
	0x66, 0x0f, 0x6f, 0xd5, //0x0000320b movdqa       %xmm5, %xmm2
	0x66, 0x44, 0x0f, 0x6f, 0xee, //0x0000320f movdqa       %xmm6, %xmm13
	0x48, 0x85, 0xd2, //0x00003214 testq        %rdx, %rdx
	0x0f, 0x85, 0x54, 0xf5, 0xff, 0xff, //0x00003217 jne          LBB0_54
	//0x0000321d LBB0_519
	0x48, 0x83, 0xc0, 0x20, //0x0000321d addq         $32, %rax
	0x49, 0x83, 0xc1, 0xe0, //0x00003221 addq         $-32, %r9
	//0x00003225 LBB0_520
	0x4d, 0x85, 0xc0, //0x00003225 testq        %r8, %r8
	0x0f, 0x85, 0x18, 0x04, 0x00, 0x00, //0x00003228 jne          LBB0_566
	0x4c, 0x89, 0xe1, //0x0000322e movq         %r12, %rcx
	0x48, 0xf7, 0xd1, //0x00003231 notq         %rcx
	0x48, 0x8b, 0x55, 0xc8, //0x00003234 movq         $-56(%rbp), %rdx
	0x4d, 0x85, 0xc9, //0x00003238 testq        %r9, %r9
	0x0f, 0x84, 0x9e, 0x00, 0x00, 0x00, //0x0000323b je           LBB0_532
	//0x00003241 LBB0_522
	0x48, 0x83, 0xc1, 0x01, //0x00003241 addq         $1, %rcx
	//0x00003245 LBB0_523
	0x31, 0xf6, //0x00003245 xorl         %esi, %esi
	//0x00003247 LBB0_524
	0x0f, 0xb6, 0x1c, 0x30, //0x00003247 movzbl       (%rax,%rsi), %ebx
	0x80, 0xfb, 0x22, //0x0000324b cmpb         $34, %bl
	0x0f, 0x84, 0x84, 0x00, 0x00, 0x00, //0x0000324e je           LBB0_531
	0x80, 0xfb, 0x5c, //0x00003254 cmpb         $92, %bl
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00003257 je           LBB0_529
	0x48, 0x83, 0xc6, 0x01, //0x0000325d addq         $1, %rsi
	0x49, 0x39, 0xf1, //0x00003261 cmpq         %rsi, %r9
	0x0f, 0x85, 0xdd, 0xff, 0xff, 0xff, //0x00003264 jne          LBB0_524
	0xe9, 0x78, 0x00, 0x00, 0x00, //0x0000326a jmp          LBB0_527
	//0x0000326f LBB0_529
	0x49, 0x8d, 0x79, 0xff, //0x0000326f leaq         $-1(%r9), %rdi
	0x48, 0x39, 0xf7, //0x00003273 cmpq         %rsi, %rdi
	0x0f, 0x84, 0xb3, 0x09, 0x00, 0x00, //0x00003276 je           LBB0_490
	0x66, 0x41, 0x0f, 0x6f, 0xf5, //0x0000327c movdqa       %xmm13, %xmm6
	0x66, 0x0f, 0x6f, 0xea, //0x00003281 movdqa       %xmm2, %xmm5
	0x48, 0x8d, 0x3c, 0x01, //0x00003285 leaq         (%rcx,%rax), %rdi
	0x48, 0x01, 0xf7, //0x00003289 addq         %rsi, %rdi
	0x48, 0x83, 0xfa, 0xff, //0x0000328c cmpq         $-1, %rdx
	0x48, 0x8b, 0x5d, 0xc8, //0x00003290 movq         $-56(%rbp), %rbx
	0x48, 0x0f, 0x44, 0xdf, //0x00003294 cmoveq       %rdi, %rbx
	0x48, 0x89, 0x5d, 0xc8, //0x00003298 movq         %rbx, $-56(%rbp)
	0x48, 0x0f, 0x44, 0xd7, //0x0000329c cmoveq       %rdi, %rdx
	0x48, 0x01, 0xf0, //0x000032a0 addq         %rsi, %rax
	0x48, 0x83, 0xc0, 0x02, //0x000032a3 addq         $2, %rax
	0x4c, 0x89, 0xcf, //0x000032a7 movq         %r9, %rdi
	0x48, 0x29, 0xf7, //0x000032aa subq         %rsi, %rdi
	0x48, 0x83, 0xc7, 0xfe, //0x000032ad addq         $-2, %rdi
	0x49, 0x83, 0xc1, 0xfe, //0x000032b1 addq         $-2, %r9
	0x49, 0x39, 0xf1, //0x000032b5 cmpq         %rsi, %r9
	0x49, 0x89, 0xf9, //0x000032b8 movq         %rdi, %r9
	0x4c, 0x8b, 0x75, 0xd0, //0x000032bb movq         $-48(%rbp), %r14
	0x4c, 0x8b, 0x6d, 0xb8, //0x000032bf movq         $-72(%rbp), %r13
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000032c3 movabsq      $4294977024, %r11
	0x0f, 0x85, 0x72, 0xff, 0xff, 0xff, //0x000032cd jne          LBB0_523
	0xe9, 0x3f, 0x07, 0x00, 0x00, //0x000032d3 jmp          LBB0_610
	//0x000032d8 LBB0_531
	0x48, 0x01, 0xf0, //0x000032d8 addq         %rsi, %rax
	0x48, 0x83, 0xc0, 0x01, //0x000032db addq         $1, %rax
	//0x000032df LBB0_532
	0x4c, 0x29, 0xe0, //0x000032df subq         %r12, %rax
	0xe9, 0x9f, 0xdf, 0xff, 0xff, //0x000032e2 jmp          LBB0_223
	//0x000032e7 LBB0_527
	0x80, 0xfb, 0x22, //0x000032e7 cmpb         $34, %bl
	0x0f, 0x85, 0x3f, 0x09, 0x00, 0x00, //0x000032ea jne          LBB0_490
	0x66, 0x41, 0x0f, 0x6f, 0xf5, //0x000032f0 movdqa       %xmm13, %xmm6
	0x66, 0x0f, 0x6f, 0xea, //0x000032f5 movdqa       %xmm2, %xmm5
	0x4c, 0x01, 0xc8, //0x000032f9 addq         %r9, %rax
	0x4c, 0x8b, 0x75, 0xd0, //0x000032fc movq         $-48(%rbp), %r14
	0x4c, 0x8b, 0x6d, 0xb8, //0x00003300 movq         $-72(%rbp), %r13
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00003304 movabsq      $4294977024, %r11
	0xe9, 0xcc, 0xff, 0xff, 0xff, //0x0000330e jmp          LBB0_532
	//0x00003313 LBB0_533
	0x48, 0x89, 0xdf, //0x00003313 movq         %rbx, %rdi
	0x66, 0x41, 0x0f, 0x6f, 0xfd, //0x00003316 movdqa       %xmm13, %xmm7
	0x66, 0x0f, 0x6f, 0xf2, //0x0000331b movdqa       %xmm2, %xmm6
	0x48, 0x83, 0x7d, 0xc8, 0xff, //0x0000331f cmpq         $-1, $-56(%rbp)
	0x0f, 0x85, 0xd0, 0x00, 0x00, 0x00, //0x00003324 jne          LBB0_543
	0x49, 0x89, 0xc0, //0x0000332a movq         %rax, %r8
	0x4d, 0x29, 0xe0, //0x0000332d subq         %r12, %r8
	0x49, 0x0f, 0xbc, 0xd9, //0x00003330 bsfq         %r9, %rbx
	0x4c, 0x01, 0xc3, //0x00003334 addq         %r8, %rbx
	0x48, 0x89, 0x5d, 0xc8, //0x00003337 movq         %rbx, $-56(%rbp)
	0xe9, 0xba, 0x00, 0x00, 0x00, //0x0000333b jmp          LBB0_543
	//0x00003340 LBB0_535
	0x49, 0x39, 0xd1, //0x00003340 cmpq         %rdx, %r9
	0x0f, 0x84, 0xfe, 0x06, 0x00, 0x00, //0x00003343 je           LBB0_638
	0x66, 0x41, 0x0f, 0x6f, 0xf5, //0x00003349 movdqa       %xmm13, %xmm6
	0x66, 0x0f, 0x6f, 0xea, //0x0000334e movdqa       %xmm2, %xmm5
	0x49, 0x01, 0xd2, //0x00003352 addq         %rdx, %r10
	0x49, 0x83, 0xc2, 0x01, //0x00003355 addq         $1, %r10
	0x48, 0xf7, 0xd2, //0x00003359 notq         %rdx
	0x49, 0x01, 0xd1, //0x0000335c addq         %rdx, %r9
	0x4c, 0x8b, 0x75, 0xd0, //0x0000335f movq         $-48(%rbp), %r14
	0x4c, 0x8b, 0x6d, 0xb8, //0x00003363 movq         $-72(%rbp), %r13
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00003367 movabsq      $4294977024, %r11
	0x4d, 0x85, 0xc9, //0x00003371 testq        %r9, %r9
	0x0f, 0x8f, 0x24, 0x00, 0x00, 0x00, //0x00003374 jg           LBB0_539
	0xe9, 0xc8, 0x06, 0x00, 0x00, //0x0000337a jmp          LBB0_638
	//0x0000337f LBB0_537
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x0000337f movq         $-2, %rcx
	0xb8, 0x02, 0x00, 0x00, 0x00, //0x00003386 movl         $2, %eax
	0x49, 0x01, 0xc2, //0x0000338b addq         %rax, %r10
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x0000338e movq         $-1, %rax
	0x49, 0x01, 0xc9, //0x00003395 addq         %rcx, %r9
	0x0f, 0x8e, 0xa9, 0x06, 0x00, 0x00, //0x00003398 jle          LBB0_638
	//0x0000339e LBB0_539
	0x41, 0x0f, 0xb6, 0x02, //0x0000339e movzbl       (%r10), %eax
	0x3c, 0x5c, //0x000033a2 cmpb         $92, %al
	0x0f, 0x84, 0xd5, 0xff, 0xff, 0xff, //0x000033a4 je           LBB0_537
	0x3c, 0x22, //0x000033aa cmpb         $34, %al
	0x0f, 0x84, 0x24, 0x00, 0x00, 0x00, //0x000033ac je           LBB0_607
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x000033b2 movq         $-1, %rcx
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x000033b9 movl         $1, %eax
	0x49, 0x01, 0xc2, //0x000033be addq         %rax, %r10
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000033c1 movq         $-1, %rax
	0x49, 0x01, 0xc9, //0x000033c8 addq         %rcx, %r9
	0x0f, 0x8f, 0xcd, 0xff, 0xff, 0xff, //0x000033cb jg           LBB0_539
	0xe9, 0x71, 0x06, 0x00, 0x00, //0x000033d1 jmp          LBB0_638
	//0x000033d6 LBB0_607
	0x4d, 0x29, 0xe2, //0x000033d6 subq         %r12, %r10
	0x49, 0x83, 0xc2, 0x01, //0x000033d9 addq         $1, %r10
	0x4d, 0x89, 0x16, //0x000033dd movq         %r10, (%r14)
	0x4d, 0x85, 0xc0, //0x000033e0 testq        %r8, %r8
	0x0f, 0x8f, 0x33, 0xe4, 0xff, 0xff, //0x000033e3 jg           LBB0_306
	0xe9, 0x13, 0x06, 0x00, 0x00, //0x000033e9 jmp          LBB0_608
	//0x000033ee LBB0_542
	0x48, 0x89, 0xdf, //0x000033ee movq         %rbx, %rdi
	0x66, 0x41, 0x0f, 0x6f, 0xfd, //0x000033f1 movdqa       %xmm13, %xmm7
	0x66, 0x0f, 0x6f, 0xf2, //0x000033f6 movdqa       %xmm2, %xmm6
	//0x000033fa LBB0_543
	0x48, 0x89, 0xfb, //0x000033fa movq         %rdi, %rbx
	0xf7, 0xd7, //0x000033fd notl         %edi
	0x44, 0x21, 0xcf, //0x000033ff andl         %r9d, %edi
	0x44, 0x8d, 0x04, 0x7b, //0x00003402 leal         (%rbx,%rdi,2), %r8d
	0x8d, 0x14, 0x3f, //0x00003406 leal         (%rdi,%rdi), %edx
	0xf7, 0xd2, //0x00003409 notl         %edx
	0x44, 0x21, 0xca, //0x0000340b andl         %r9d, %edx
	0x81, 0xe2, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000340e andl         $-1431655766, %edx
	0x31, 0xdb, //0x00003414 xorl         %ebx, %ebx
	0x01, 0xfa, //0x00003416 addl         %edi, %edx
	0x0f, 0x92, 0xc3, //0x00003418 setb         %bl
	0x01, 0xd2, //0x0000341b addl         %edx, %edx
	0x81, 0xf2, 0x55, 0x55, 0x55, 0x55, //0x0000341d xorl         $1431655765, %edx
	0x44, 0x21, 0xc2, //0x00003423 andl         %r8d, %edx
	0xf7, 0xd2, //0x00003426 notl         %edx
	0x21, 0xd1, //0x00003428 andl         %edx, %ecx
	0x4c, 0x8b, 0x6d, 0xb8, //0x0000342a movq         $-72(%rbp), %r13
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x0000342e movabsq      $4294977024, %r11
	0x66, 0x0f, 0x6f, 0xd6, //0x00003438 movdqa       %xmm6, %xmm2
	0x66, 0x44, 0x0f, 0x6f, 0xef, //0x0000343c movdqa       %xmm7, %xmm13
	0xe9, 0xd0, 0xf3, 0xff, 0xff, //0x00003441 jmp          LBB0_170
	//0x00003446 LBB0_544
	0x66, 0x41, 0x0f, 0x6f, 0xf5, //0x00003446 movdqa       %xmm13, %xmm6
	0x66, 0x0f, 0x6f, 0xea, //0x0000344b movdqa       %xmm2, %xmm5
	0x48, 0x83, 0x7d, 0xc8, 0xff, //0x0000344f cmpq         $-1, $-56(%rbp)
	0x0f, 0x85, 0x1f, 0x00, 0x00, 0x00, //0x00003454 jne          LBB0_547
	0x48, 0x89, 0xc1, //0x0000345a movq         %rax, %rcx
	0x4c, 0x29, 0xe1, //0x0000345d subq         %r12, %rcx
	0x48, 0x0f, 0xbc, 0xfe, //0x00003460 bsfq         %rsi, %rdi
	0x48, 0x01, 0xcf, //0x00003464 addq         %rcx, %rdi
	0x48, 0x89, 0x7d, 0xc8, //0x00003467 movq         %rdi, $-56(%rbp)
	0xe9, 0x09, 0x00, 0x00, 0x00, //0x0000346b jmp          LBB0_547
	//0x00003470 LBB0_546
	0x66, 0x41, 0x0f, 0x6f, 0xf5, //0x00003470 movdqa       %xmm13, %xmm6
	0x66, 0x0f, 0x6f, 0xea, //0x00003475 movdqa       %xmm2, %xmm5
	//0x00003479 LBB0_547
	0x44, 0x89, 0xc1, //0x00003479 movl         %r8d, %ecx
	0xf7, 0xd1, //0x0000347c notl         %ecx
	0x21, 0xf1, //0x0000347e andl         %esi, %ecx
	0x41, 0x8d, 0x3c, 0x48, //0x00003480 leal         (%r8,%rcx,2), %edi
	0x8d, 0x1c, 0x09, //0x00003484 leal         (%rcx,%rcx), %ebx
	0xf7, 0xd3, //0x00003487 notl         %ebx
	0x21, 0xf3, //0x00003489 andl         %esi, %ebx
	0x81, 0xe3, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000348b andl         $-1431655766, %ebx
	0x45, 0x31, 0xc0, //0x00003491 xorl         %r8d, %r8d
	0x01, 0xcb, //0x00003494 addl         %ecx, %ebx
	0x41, 0x0f, 0x92, 0xc0, //0x00003496 setb         %r8b
	0x01, 0xdb, //0x0000349a addl         %ebx, %ebx
	0x81, 0xf3, 0x55, 0x55, 0x55, 0x55, //0x0000349c xorl         $1431655765, %ebx
	0x21, 0xfb, //0x000034a2 andl         %edi, %ebx
	0xf7, 0xd3, //0x000034a4 notl         %ebx
	0x21, 0xda, //0x000034a6 andl         %ebx, %edx
	0x4c, 0x8b, 0x75, 0xd0, //0x000034a8 movq         $-48(%rbp), %r14
	0x4c, 0x8b, 0x6d, 0xb8, //0x000034ac movq         $-72(%rbp), %r13
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000034b0 movabsq      $4294977024, %r11
	0x66, 0x0f, 0x6f, 0xd5, //0x000034ba movdqa       %xmm5, %xmm2
	0x66, 0x44, 0x0f, 0x6f, 0xee, //0x000034be movdqa       %xmm6, %xmm13
	0x48, 0x85, 0xd2, //0x000034c3 testq        %rdx, %rdx
	0x0f, 0x85, 0x44, 0xf4, 0xff, 0xff, //0x000034c6 jne          LBB0_77
	//0x000034cc LBB0_548
	0x48, 0x83, 0xc0, 0x20, //0x000034cc addq         $32, %rax
	0x49, 0x83, 0xc1, 0xe0, //0x000034d0 addq         $-32, %r9
	//0x000034d4 LBB0_549
	0x4d, 0x85, 0xc0, //0x000034d4 testq        %r8, %r8
	0x0f, 0x85, 0xc4, 0x01, 0x00, 0x00, //0x000034d7 jne          LBB0_568
	0x4c, 0x89, 0xe1, //0x000034dd movq         %r12, %rcx
	0x48, 0xf7, 0xd1, //0x000034e0 notq         %rcx
	0x48, 0x8b, 0x55, 0xc8, //0x000034e3 movq         $-56(%rbp), %rdx
	0x4d, 0x85, 0xc9, //0x000034e7 testq        %r9, %r9
	0x0f, 0x84, 0x9e, 0x00, 0x00, 0x00, //0x000034ea je           LBB0_561
	//0x000034f0 LBB0_551
	0x48, 0x83, 0xc1, 0x01, //0x000034f0 addq         $1, %rcx
	//0x000034f4 LBB0_552
	0x31, 0xf6, //0x000034f4 xorl         %esi, %esi
	//0x000034f6 LBB0_553
	0x0f, 0xb6, 0x1c, 0x30, //0x000034f6 movzbl       (%rax,%rsi), %ebx
	0x80, 0xfb, 0x22, //0x000034fa cmpb         $34, %bl
	0x0f, 0x84, 0x84, 0x00, 0x00, 0x00, //0x000034fd je           LBB0_560
	0x80, 0xfb, 0x5c, //0x00003503 cmpb         $92, %bl
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00003506 je           LBB0_558
	0x48, 0x83, 0xc6, 0x01, //0x0000350c addq         $1, %rsi
	0x49, 0x39, 0xf1, //0x00003510 cmpq         %rsi, %r9
	0x0f, 0x85, 0xdd, 0xff, 0xff, 0xff, //0x00003513 jne          LBB0_553
	0xe9, 0x78, 0x00, 0x00, 0x00, //0x00003519 jmp          LBB0_556
	//0x0000351e LBB0_558
	0x49, 0x8d, 0x79, 0xff, //0x0000351e leaq         $-1(%r9), %rdi
	0x48, 0x39, 0xf7, //0x00003522 cmpq         %rsi, %rdi
	0x0f, 0x84, 0x23, 0x07, 0x00, 0x00, //0x00003525 je           LBB0_507
	0x66, 0x41, 0x0f, 0x6f, 0xf5, //0x0000352b movdqa       %xmm13, %xmm6
	0x66, 0x0f, 0x6f, 0xea, //0x00003530 movdqa       %xmm2, %xmm5
	0x48, 0x8d, 0x3c, 0x01, //0x00003534 leaq         (%rcx,%rax), %rdi
	0x48, 0x01, 0xf7, //0x00003538 addq         %rsi, %rdi
	0x48, 0x83, 0xfa, 0xff, //0x0000353b cmpq         $-1, %rdx
	0x48, 0x8b, 0x5d, 0xc8, //0x0000353f movq         $-56(%rbp), %rbx
	0x48, 0x0f, 0x44, 0xdf, //0x00003543 cmoveq       %rdi, %rbx
	0x48, 0x89, 0x5d, 0xc8, //0x00003547 movq         %rbx, $-56(%rbp)
	0x48, 0x0f, 0x44, 0xd7, //0x0000354b cmoveq       %rdi, %rdx
	0x48, 0x01, 0xf0, //0x0000354f addq         %rsi, %rax
	0x48, 0x83, 0xc0, 0x02, //0x00003552 addq         $2, %rax
	0x4c, 0x89, 0xcf, //0x00003556 movq         %r9, %rdi
	0x48, 0x29, 0xf7, //0x00003559 subq         %rsi, %rdi
	0x48, 0x83, 0xc7, 0xfe, //0x0000355c addq         $-2, %rdi
	0x49, 0x83, 0xc1, 0xfe, //0x00003560 addq         $-2, %r9
	0x49, 0x39, 0xf1, //0x00003564 cmpq         %rsi, %r9
	0x49, 0x89, 0xf9, //0x00003567 movq         %rdi, %r9
	0x4c, 0x8b, 0x75, 0xd0, //0x0000356a movq         $-48(%rbp), %r14
	0x4c, 0x8b, 0x6d, 0xb8, //0x0000356e movq         $-72(%rbp), %r13
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00003572 movabsq      $4294977024, %r11
	0x0f, 0x85, 0x72, 0xff, 0xff, 0xff, //0x0000357c jne          LBB0_552
	0xe9, 0xec, 0x04, 0x00, 0x00, //0x00003582 jmp          LBB0_616
	//0x00003587 LBB0_560
	0x48, 0x01, 0xf0, //0x00003587 addq         %rsi, %rax
	0x48, 0x83, 0xc0, 0x01, //0x0000358a addq         $1, %rax
	//0x0000358e LBB0_561
	0x4c, 0x29, 0xe0, //0x0000358e subq         %r12, %rax
	0xe9, 0x6d, 0xe2, 0xff, 0xff, //0x00003591 jmp          LBB0_304
	//0x00003596 LBB0_556
	0x80, 0xfb, 0x22, //0x00003596 cmpb         $34, %bl
	0x0f, 0x85, 0xaf, 0x06, 0x00, 0x00, //0x00003599 jne          LBB0_507
	0x66, 0x41, 0x0f, 0x6f, 0xf5, //0x0000359f movdqa       %xmm13, %xmm6
	0x66, 0x0f, 0x6f, 0xea, //0x000035a4 movdqa       %xmm2, %xmm5
	0x4c, 0x01, 0xc8, //0x000035a8 addq         %r9, %rax
	0x4c, 0x8b, 0x75, 0xd0, //0x000035ab movq         $-48(%rbp), %r14
	0x4c, 0x8b, 0x6d, 0xb8, //0x000035af movq         $-72(%rbp), %r13
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000035b3 movabsq      $4294977024, %r11
	0xe9, 0xcc, 0xff, 0xff, 0xff, //0x000035bd jmp          LBB0_561
	//0x000035c2 LBB0_562
	0x66, 0x0f, 0x6f, 0xf2, //0x000035c2 movdqa       %xmm2, %xmm6
	0x48, 0x83, 0x7d, 0xc8, 0xff, //0x000035c6 cmpq         $-1, $-56(%rbp)
	0x0f, 0x85, 0x1b, 0x00, 0x00, 0x00, //0x000035cb jne          LBB0_565
	0x49, 0x89, 0xc0, //0x000035d1 movq         %rax, %r8
	0x4c, 0x2b, 0x45, 0xc0, //0x000035d4 subq         $-64(%rbp), %r8
	0x49, 0x0f, 0xbc, 0xda, //0x000035d8 bsfq         %r10, %rbx
	0x4c, 0x01, 0xc3, //0x000035dc addq         %r8, %rbx
	0x48, 0x89, 0x5d, 0xc8, //0x000035df movq         %rbx, $-56(%rbp)
	0xe9, 0x04, 0x00, 0x00, 0x00, //0x000035e3 jmp          LBB0_565
	//0x000035e8 LBB0_564
	0x66, 0x0f, 0x6f, 0xf2, //0x000035e8 movdqa       %xmm2, %xmm6
	//0x000035ec LBB0_565
	0x44, 0x89, 0xe2, //0x000035ec movl         %r12d, %edx
	0xf7, 0xd2, //0x000035ef notl         %edx
	0x44, 0x21, 0xd2, //0x000035f1 andl         %r10d, %edx
	0x45, 0x8d, 0x04, 0x54, //0x000035f4 leal         (%r12,%rdx,2), %r8d
	0x8d, 0x0c, 0x12, //0x000035f8 leal         (%rdx,%rdx), %ecx
	0xf7, 0xd1, //0x000035fb notl         %ecx
	0x44, 0x21, 0xd1, //0x000035fd andl         %r10d, %ecx
	0x81, 0xe1, 0xaa, 0xaa, 0xaa, 0xaa, //0x00003600 andl         $-1431655766, %ecx
	0x45, 0x31, 0xe4, //0x00003606 xorl         %r12d, %r12d
	0x01, 0xd1, //0x00003609 addl         %edx, %ecx
	0x41, 0x0f, 0x92, 0xc4, //0x0000360b setb         %r12b
	0x01, 0xc9, //0x0000360f addl         %ecx, %ecx
	0x81, 0xf1, 0x55, 0x55, 0x55, 0x55, //0x00003611 xorl         $1431655765, %ecx
	0x44, 0x21, 0xc1, //0x00003617 andl         %r8d, %ecx
	0xf7, 0xd1, //0x0000361a notl         %ecx
	0x21, 0xce, //0x0000361c andl         %ecx, %esi
	0x4c, 0x8b, 0x75, 0xd0, //0x0000361e movq         $-48(%rbp), %r14
	0x4c, 0x8b, 0x6d, 0xb8, //0x00003622 movq         $-72(%rbp), %r13
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00003626 movabsq      $4294977024, %r11
	0x66, 0x0f, 0x6f, 0xd6, //0x00003630 movdqa       %xmm6, %xmm2
	0xf3, 0x44, 0x0f, 0x6f, 0x2d, 0x03, 0xca, 0xff, 0xff, //0x00003634 movdqu       $-13821(%rip), %xmm13  /* LCPI0_4+0(%rip) */
	0x4c, 0x8b, 0x45, 0xc0, //0x0000363d movq         $-64(%rbp), %r8
	0xe9, 0x79, 0xf3, 0xff, 0xff, //0x00003641 jmp          LBB0_199
	//0x00003646 LBB0_566
	0x4d, 0x85, 0xc9, //0x00003646 testq        %r9, %r9
	0x0f, 0x84, 0xe0, 0x05, 0x00, 0x00, //0x00003649 je           LBB0_490
	0x66, 0x41, 0x0f, 0x6f, 0xf5, //0x0000364f movdqa       %xmm13, %xmm6
	0x66, 0x0f, 0x6f, 0xea, //0x00003654 movdqa       %xmm2, %xmm5
	0x4c, 0x89, 0xe1, //0x00003658 movq         %r12, %rcx
	0x48, 0xf7, 0xd1, //0x0000365b notq         %rcx
	0x48, 0x8d, 0x34, 0x08, //0x0000365e leaq         (%rax,%rcx), %rsi
	0x48, 0x8b, 0x7d, 0xc8, //0x00003662 movq         $-56(%rbp), %rdi
	0x48, 0x83, 0xff, 0xff, //0x00003666 cmpq         $-1, %rdi
	0x48, 0x89, 0xfa, //0x0000366a movq         %rdi, %rdx
	0x48, 0x0f, 0x44, 0xfe, //0x0000366d cmoveq       %rsi, %rdi
	0x48, 0x0f, 0x44, 0xd6, //0x00003671 cmoveq       %rsi, %rdx
	0x48, 0x83, 0xc0, 0x01, //0x00003675 addq         $1, %rax
	0x49, 0x83, 0xc1, 0xff, //0x00003679 addq         $-1, %r9
	0x48, 0x89, 0x7d, 0xc8, //0x0000367d movq         %rdi, $-56(%rbp)
	0x4c, 0x8b, 0x75, 0xd0, //0x00003681 movq         $-48(%rbp), %r14
	0x4c, 0x8b, 0x6d, 0xb8, //0x00003685 movq         $-72(%rbp), %r13
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00003689 movabsq      $4294977024, %r11
	0x4d, 0x85, 0xc9, //0x00003693 testq        %r9, %r9
	0x0f, 0x85, 0xa5, 0xfb, 0xff, 0xff, //0x00003696 jne          LBB0_522
	0xe9, 0x3e, 0xfc, 0xff, 0xff, //0x0000369c jmp          LBB0_532
	//0x000036a1 LBB0_568
	0x4d, 0x85, 0xc9, //0x000036a1 testq        %r9, %r9
	0x0f, 0x84, 0xa4, 0x05, 0x00, 0x00, //0x000036a4 je           LBB0_507
	0x66, 0x41, 0x0f, 0x6f, 0xf5, //0x000036aa movdqa       %xmm13, %xmm6
	0x66, 0x0f, 0x6f, 0xea, //0x000036af movdqa       %xmm2, %xmm5
	0x4c, 0x89, 0xe1, //0x000036b3 movq         %r12, %rcx
	0x48, 0xf7, 0xd1, //0x000036b6 notq         %rcx
	0x48, 0x8d, 0x34, 0x08, //0x000036b9 leaq         (%rax,%rcx), %rsi
	0x48, 0x8b, 0x7d, 0xc8, //0x000036bd movq         $-56(%rbp), %rdi
	0x48, 0x83, 0xff, 0xff, //0x000036c1 cmpq         $-1, %rdi
	0x48, 0x89, 0xfa, //0x000036c5 movq         %rdi, %rdx
	0x48, 0x0f, 0x44, 0xfe, //0x000036c8 cmoveq       %rsi, %rdi
	0x48, 0x0f, 0x44, 0xd6, //0x000036cc cmoveq       %rsi, %rdx
	0x48, 0x83, 0xc0, 0x01, //0x000036d0 addq         $1, %rax
	0x49, 0x83, 0xc1, 0xff, //0x000036d4 addq         $-1, %r9
	0x48, 0x89, 0x7d, 0xc8, //0x000036d8 movq         %rdi, $-56(%rbp)
	0x4c, 0x8b, 0x75, 0xd0, //0x000036dc movq         $-48(%rbp), %r14
	0x4c, 0x8b, 0x6d, 0xb8, //0x000036e0 movq         $-72(%rbp), %r13
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000036e4 movabsq      $4294977024, %r11
	0x4d, 0x85, 0xc9, //0x000036ee testq        %r9, %r9
	0x0f, 0x85, 0xf9, 0xfd, 0xff, 0xff, //0x000036f1 jne          LBB0_551
	0xe9, 0x92, 0xfe, 0xff, 0xff, //0x000036f7 jmp          LBB0_561
	//0x000036fc LBB0_570
	0x49, 0x39, 0xd1, //0x000036fc cmpq         %rdx, %r9
	0x0f, 0x84, 0x42, 0x03, 0x00, 0x00, //0x000036ff je           LBB0_638
	0x66, 0x41, 0x0f, 0x6f, 0xf5, //0x00003705 movdqa       %xmm13, %xmm6
	0x66, 0x0f, 0x6f, 0xea, //0x0000370a movdqa       %xmm2, %xmm5
	0x49, 0x01, 0xd2, //0x0000370e addq         %rdx, %r10
	0x49, 0x83, 0xc2, 0x01, //0x00003711 addq         $1, %r10
	0x48, 0xf7, 0xd2, //0x00003715 notq         %rdx
	0x49, 0x01, 0xd1, //0x00003718 addq         %rdx, %r9
	0x4c, 0x8b, 0x75, 0xd0, //0x0000371b movq         $-48(%rbp), %r14
	0x4c, 0x8b, 0x6d, 0xb8, //0x0000371f movq         $-72(%rbp), %r13
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00003723 movabsq      $4294977024, %r11
	0x4d, 0x85, 0xc9, //0x0000372d testq        %r9, %r9
	0x0f, 0x8f, 0x24, 0x00, 0x00, 0x00, //0x00003730 jg           LBB0_574
	0xe9, 0x0c, 0x03, 0x00, 0x00, //0x00003736 jmp          LBB0_638
	//0x0000373b LBB0_572
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x0000373b movq         $-2, %rcx
	0xb8, 0x02, 0x00, 0x00, 0x00, //0x00003742 movl         $2, %eax
	0x49, 0x01, 0xc2, //0x00003747 addq         %rax, %r10
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x0000374a movq         $-1, %rax
	0x49, 0x01, 0xc9, //0x00003751 addq         %rcx, %r9
	0x0f, 0x8e, 0xed, 0x02, 0x00, 0x00, //0x00003754 jle          LBB0_638
	//0x0000375a LBB0_574
	0x41, 0x0f, 0xb6, 0x02, //0x0000375a movzbl       (%r10), %eax
	0x3c, 0x5c, //0x0000375e cmpb         $92, %al
	0x0f, 0x84, 0xd5, 0xff, 0xff, 0xff, //0x00003760 je           LBB0_572
	0x3c, 0x22, //0x00003766 cmpb         $34, %al
	0x0f, 0x84, 0x24, 0x00, 0x00, 0x00, //0x00003768 je           LBB0_577
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x0000376e movq         $-1, %rcx
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x00003775 movl         $1, %eax
	0x49, 0x01, 0xc2, //0x0000377a addq         %rax, %r10
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x0000377d movq         $-1, %rax
	0x49, 0x01, 0xc9, //0x00003784 addq         %rcx, %r9
	0x0f, 0x8f, 0xcd, 0xff, 0xff, 0xff, //0x00003787 jg           LBB0_574
	0xe9, 0xb5, 0x02, 0x00, 0x00, //0x0000378d jmp          LBB0_638
	//0x00003792 LBB0_577
	0x4d, 0x29, 0xe2, //0x00003792 subq         %r12, %r10
	0x49, 0x83, 0xc2, 0x01, //0x00003795 addq         $1, %r10
	0x4d, 0x89, 0x16, //0x00003799 movq         %r10, (%r14)
	0x4d, 0x85, 0xc0, //0x0000379c testq        %r8, %r8
	0x0f, 0x8f, 0x1b, 0xca, 0xff, 0xff, //0x0000379f jg           LBB0_4
	0xe9, 0x57, 0x02, 0x00, 0x00, //0x000037a5 jmp          LBB0_608
	//0x000037aa LBB0_578
	0x66, 0x41, 0x0f, 0x6f, 0xf5, //0x000037aa movdqa       %xmm13, %xmm6
	0x66, 0x0f, 0x6f, 0xea, //0x000037af movdqa       %xmm2, %xmm5
	0x48, 0x83, 0x7d, 0xc8, 0xff, //0x000037b3 cmpq         $-1, $-56(%rbp)
	0x0f, 0x85, 0x1f, 0x00, 0x00, 0x00, //0x000037b8 jne          LBB0_581
	0x48, 0x89, 0xc1, //0x000037be movq         %rax, %rcx
	0x4c, 0x29, 0xe1, //0x000037c1 subq         %r12, %rcx
	0x48, 0x0f, 0xbc, 0xfe, //0x000037c4 bsfq         %rsi, %rdi
	0x48, 0x01, 0xcf, //0x000037c8 addq         %rcx, %rdi
	0x48, 0x89, 0x7d, 0xc8, //0x000037cb movq         %rdi, $-56(%rbp)
	0xe9, 0x09, 0x00, 0x00, 0x00, //0x000037cf jmp          LBB0_581
	//0x000037d4 LBB0_580
	0x66, 0x41, 0x0f, 0x6f, 0xf5, //0x000037d4 movdqa       %xmm13, %xmm6
	0x66, 0x0f, 0x6f, 0xea, //0x000037d9 movdqa       %xmm2, %xmm5
	//0x000037dd LBB0_581
	0x44, 0x89, 0xc1, //0x000037dd movl         %r8d, %ecx
	0xf7, 0xd1, //0x000037e0 notl         %ecx
	0x21, 0xf1, //0x000037e2 andl         %esi, %ecx
	0x41, 0x8d, 0x3c, 0x48, //0x000037e4 leal         (%r8,%rcx,2), %edi
	0x8d, 0x1c, 0x09, //0x000037e8 leal         (%rcx,%rcx), %ebx
	0xf7, 0xd3, //0x000037eb notl         %ebx
	0x21, 0xf3, //0x000037ed andl         %esi, %ebx
	0x81, 0xe3, 0xaa, 0xaa, 0xaa, 0xaa, //0x000037ef andl         $-1431655766, %ebx
	0x45, 0x31, 0xc0, //0x000037f5 xorl         %r8d, %r8d
	0x01, 0xcb, //0x000037f8 addl         %ecx, %ebx
	0x41, 0x0f, 0x92, 0xc0, //0x000037fa setb         %r8b
	0x01, 0xdb, //0x000037fe addl         %ebx, %ebx
	0x81, 0xf3, 0x55, 0x55, 0x55, 0x55, //0x00003800 xorl         $1431655765, %ebx
	0x21, 0xfb, //0x00003806 andl         %edi, %ebx
	0xf7, 0xd3, //0x00003808 notl         %ebx
	0x21, 0xda, //0x0000380a andl         %ebx, %edx
	0x4c, 0x8b, 0x75, 0xd0, //0x0000380c movq         $-48(%rbp), %r14
	0x4c, 0x8b, 0x6d, 0xb8, //0x00003810 movq         $-72(%rbp), %r13
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00003814 movabsq      $4294977024, %r11
	0x66, 0x0f, 0x6f, 0xd5, //0x0000381e movdqa       %xmm5, %xmm2
	0x66, 0x44, 0x0f, 0x6f, 0xee, //0x00003822 movdqa       %xmm6, %xmm13
	0x48, 0x85, 0xd2, //0x00003827 testq        %rdx, %rdx
	0x0f, 0x85, 0x89, 0xf4, 0xff, 0xff, //0x0000382a jne          LBB0_245
	//0x00003830 LBB0_582
	0x66, 0x41, 0x0f, 0x6f, 0xf5, //0x00003830 movdqa       %xmm13, %xmm6
	0x66, 0x0f, 0x6f, 0xea, //0x00003835 movdqa       %xmm2, %xmm5
	0x48, 0x83, 0xc0, 0x20, //0x00003839 addq         $32, %rax
	0x49, 0x83, 0xc1, 0xe0, //0x0000383d addq         $-32, %r9
	//0x00003841 LBB0_583
	0x4d, 0x85, 0xc0, //0x00003841 testq        %r8, %r8
	0x0f, 0x85, 0x53, 0x01, 0x00, 0x00, //0x00003844 jne          LBB0_601
	0x4c, 0x89, 0xe2, //0x0000384a movq         %r12, %rdx
	0x48, 0xf7, 0xd2, //0x0000384d notq         %rdx
	0x48, 0x8b, 0x75, 0xc8, //0x00003850 movq         $-56(%rbp), %rsi
	0x4d, 0x85, 0xc9, //0x00003854 testq        %r9, %r9
	0x0f, 0x84, 0x83, 0x00, 0x00, 0x00, //0x00003857 je           LBB0_595
	//0x0000385d LBB0_585
	0x48, 0x83, 0xc2, 0x01, //0x0000385d addq         $1, %rdx
	//0x00003861 LBB0_586
	0x31, 0xff, //0x00003861 xorl         %edi, %edi
	//0x00003863 LBB0_587
	0x0f, 0xb6, 0x0c, 0x38, //0x00003863 movzbl       (%rax,%rdi), %ecx
	0x80, 0xf9, 0x22, //0x00003867 cmpb         $34, %cl
	0x0f, 0x84, 0x69, 0x00, 0x00, 0x00, //0x0000386a je           LBB0_594
	0x80, 0xf9, 0x5c, //0x00003870 cmpb         $92, %cl
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00003873 je           LBB0_592
	0x48, 0x83, 0xc7, 0x01, //0x00003879 addq         $1, %rdi
	0x49, 0x39, 0xf9, //0x0000387d cmpq         %rdi, %r9
	0x0f, 0x85, 0xdd, 0xff, 0xff, 0xff, //0x00003880 jne          LBB0_587
	0xe9, 0x78, 0x00, 0x00, 0x00, //0x00003886 jmp          LBB0_590
	//0x0000388b LBB0_592
	0x49, 0x8d, 0x49, 0xff, //0x0000388b leaq         $-1(%r9), %rcx
	0x48, 0x39, 0xf9, //0x0000388f cmpq         %rdi, %rcx
	0x0f, 0x84, 0xb6, 0x03, 0x00, 0x00, //0x00003892 je           LBB0_507
	0x48, 0x8d, 0x0c, 0x02, //0x00003898 leaq         (%rdx,%rax), %rcx
	0x48, 0x01, 0xf9, //0x0000389c addq         %rdi, %rcx
	0x48, 0x83, 0xfe, 0xff, //0x0000389f cmpq         $-1, %rsi
	0x48, 0x8b, 0x5d, 0xc8, //0x000038a3 movq         $-56(%rbp), %rbx
	0x48, 0x0f, 0x44, 0xd9, //0x000038a7 cmoveq       %rcx, %rbx
	0x48, 0x89, 0x5d, 0xc8, //0x000038ab movq         %rbx, $-56(%rbp)
	0x48, 0x0f, 0x44, 0xf1, //0x000038af cmoveq       %rcx, %rsi
	0x48, 0x01, 0xf8, //0x000038b3 addq         %rdi, %rax
	0x48, 0x83, 0xc0, 0x02, //0x000038b6 addq         $2, %rax
	0x4c, 0x89, 0xc9, //0x000038ba movq         %r9, %rcx
	0x48, 0x29, 0xf9, //0x000038bd subq         %rdi, %rcx
	0x48, 0x83, 0xc1, 0xfe, //0x000038c0 addq         $-2, %rcx
	0x49, 0x83, 0xc1, 0xfe, //0x000038c4 addq         $-2, %r9
	0x49, 0x39, 0xf9, //0x000038c8 cmpq         %rdi, %r9
	0x49, 0x89, 0xc9, //0x000038cb movq         %rcx, %r9
	0x0f, 0x85, 0x8d, 0xff, 0xff, 0xff, //0x000038ce jne          LBB0_586
	0xe9, 0x75, 0x03, 0x00, 0x00, //0x000038d4 jmp          LBB0_507
	//0x000038d9 LBB0_594
	0x48, 0x01, 0xf8, //0x000038d9 addq         %rdi, %rax
	0x48, 0x83, 0xc0, 0x01, //0x000038dc addq         $1, %rax
	//0x000038e0 LBB0_595
	0x4c, 0x29, 0xe0, //0x000038e0 subq         %r12, %rax
	//0x000038e3 LBB0_596
	0x4c, 0x8b, 0x75, 0xd0, //0x000038e3 movq         $-48(%rbp), %r14
	0x4c, 0x8b, 0x6d, 0xb8, //0x000038e7 movq         $-72(%rbp), %r13
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000038eb movabsq      $4294977024, %r11
	0x66, 0x0f, 0x6f, 0xd5, //0x000038f5 movdqa       %xmm5, %xmm2
	0x66, 0x44, 0x0f, 0x6f, 0xee, //0x000038f9 movdqa       %xmm6, %xmm13
	0xe9, 0x3a, 0xed, 0xff, 0xff, //0x000038fe jmp          LBB0_426
	//0x00003903 LBB0_590
	0x80, 0xf9, 0x22, //0x00003903 cmpb         $34, %cl
	0x0f, 0x85, 0x42, 0x03, 0x00, 0x00, //0x00003906 jne          LBB0_507
	0x4c, 0x01, 0xc8, //0x0000390c addq         %r9, %rax
	0xe9, 0xcc, 0xff, 0xff, 0xff, //0x0000390f jmp          LBB0_595
	//0x00003914 LBB0_597
	0x48, 0x89, 0xd9, //0x00003914 movq         %rbx, %rcx
	0x66, 0x41, 0x0f, 0x6f, 0xfd, //0x00003917 movdqa       %xmm13, %xmm7
	0x66, 0x0f, 0x6f, 0xf2, //0x0000391c movdqa       %xmm2, %xmm6
	0x48, 0x83, 0x7d, 0xc8, 0xff, //0x00003920 cmpq         $-1, $-56(%rbp)
	0x0f, 0x85, 0x22, 0x00, 0x00, 0x00, //0x00003925 jne          LBB0_600
	0x49, 0x89, 0xc0, //0x0000392b movq         %rax, %r8
	0x4d, 0x29, 0xe0, //0x0000392e subq         %r12, %r8
	0x49, 0x0f, 0xbc, 0xd9, //0x00003931 bsfq         %r9, %rbx
	0x4c, 0x01, 0xc3, //0x00003935 addq         %r8, %rbx
	0x48, 0x89, 0x5d, 0xc8, //0x00003938 movq         %rbx, $-56(%rbp)
	0xe9, 0x0c, 0x00, 0x00, 0x00, //0x0000393c jmp          LBB0_600
	//0x00003941 LBB0_599
	0x48, 0x89, 0xd9, //0x00003941 movq         %rbx, %rcx
	0x66, 0x41, 0x0f, 0x6f, 0xfd, //0x00003944 movdqa       %xmm13, %xmm7
	0x66, 0x0f, 0x6f, 0xf2, //0x00003949 movdqa       %xmm2, %xmm6
	//0x0000394d LBB0_600
	0x48, 0x89, 0xcb, //0x0000394d movq         %rcx, %rbx
	0xf7, 0xd1, //0x00003950 notl         %ecx
	0x44, 0x21, 0xc9, //0x00003952 andl         %r9d, %ecx
	0x44, 0x8d, 0x04, 0x4b, //0x00003955 leal         (%rbx,%rcx,2), %r8d
	0x8d, 0x14, 0x09, //0x00003959 leal         (%rcx,%rcx), %edx
	0xf7, 0xd2, //0x0000395c notl         %edx
	0x44, 0x21, 0xca, //0x0000395e andl         %r9d, %edx
	0x81, 0xe2, 0xaa, 0xaa, 0xaa, 0xaa, //0x00003961 andl         $-1431655766, %edx
	0x31, 0xdb, //0x00003967 xorl         %ebx, %ebx
	0x01, 0xca, //0x00003969 addl         %ecx, %edx
	0x0f, 0x92, 0xc3, //0x0000396b setb         %bl
	0x01, 0xd2, //0x0000396e addl         %edx, %edx
	0x81, 0xf2, 0x55, 0x55, 0x55, 0x55, //0x00003970 xorl         $1431655765, %edx
	0x44, 0x21, 0xc2, //0x00003976 andl         %r8d, %edx
	0xf7, 0xd2, //0x00003979 notl         %edx
	0x21, 0xd6, //0x0000397b andl         %edx, %esi
	0x4c, 0x8b, 0x75, 0xd0, //0x0000397d movq         $-48(%rbp), %r14
	0x4c, 0x8b, 0x6d, 0xb8, //0x00003981 movq         $-72(%rbp), %r13
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00003985 movabsq      $4294977024, %r11
	0x66, 0x0f, 0x6f, 0xd6, //0x0000398f movdqa       %xmm6, %xmm2
	0x66, 0x44, 0x0f, 0x6f, 0xef, //0x00003993 movdqa       %xmm7, %xmm13
	0xe9, 0xc0, 0xf3, 0xff, 0xff, //0x00003998 jmp          LBB0_398
	//0x0000399d LBB0_601
	0x4d, 0x85, 0xc9, //0x0000399d testq        %r9, %r9
	0x0f, 0x84, 0xa8, 0x02, 0x00, 0x00, //0x000039a0 je           LBB0_507
	0x4c, 0x89, 0xe2, //0x000039a6 movq         %r12, %rdx
	0x48, 0xf7, 0xd2, //0x000039a9 notq         %rdx
	0x48, 0x8d, 0x0c, 0x10, //0x000039ac leaq         (%rax,%rdx), %rcx
	0x48, 0x8b, 0x7d, 0xc8, //0x000039b0 movq         $-56(%rbp), %rdi
	0x48, 0x83, 0xff, 0xff, //0x000039b4 cmpq         $-1, %rdi
	0x48, 0x89, 0xfe, //0x000039b8 movq         %rdi, %rsi
	0x48, 0x0f, 0x44, 0xf9, //0x000039bb cmoveq       %rcx, %rdi
	0x48, 0x0f, 0x44, 0xf1, //0x000039bf cmoveq       %rcx, %rsi
	0x48, 0x83, 0xc0, 0x01, //0x000039c3 addq         $1, %rax
	0x49, 0x83, 0xc1, 0xff, //0x000039c7 addq         $-1, %r9
	0x48, 0x89, 0x7d, 0xc8, //0x000039cb movq         %rdi, $-56(%rbp)
	0x4d, 0x85, 0xc9, //0x000039cf testq        %r9, %r9
	0x0f, 0x85, 0x85, 0xfe, 0xff, 0xff, //0x000039d2 jne          LBB0_585
	0xe9, 0x03, 0xff, 0xff, 0xff, //0x000039d8 jmp          LBB0_595
	//0x000039dd LBB0_603
	0x49, 0x89, 0x16, //0x000039dd movq         %rdx, (%r14)
	//0x000039e0 LBB0_604
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000039e0 movq         $-1, %rax
	0xe9, 0x5b, 0x00, 0x00, 0x00, //0x000039e7 jmp          LBB0_638
	//0x000039ec LBB0_634
	0x48, 0xc7, 0xc0, 0xf9, 0xff, 0xff, 0xff, //0x000039ec movq         $-7, %rax
	0xe9, 0x4f, 0x00, 0x00, 0x00, //0x000039f3 jmp          LBB0_638
	//0x000039f8 LBB0_606
	0x48, 0x83, 0xc0, 0xff, //0x000039f8 addq         $-1, %rax
	0xe9, 0x46, 0x00, 0x00, 0x00, //0x000039fc jmp          LBB0_638
	//0x00003a01 LBB0_608
	0x49, 0x83, 0xc0, 0xff, //0x00003a01 addq         $-1, %r8
	0x4c, 0x89, 0xc0, //0x00003a05 movq         %r8, %rax
	0xe9, 0x3a, 0x00, 0x00, 0x00, //0x00003a08 jmp          LBB0_638
	//0x00003a0d LBB0_609
	0x48, 0x83, 0xf8, 0xff, //0x00003a0d cmpq         $-1, %rax
	0x0f, 0x85, 0x90, 0x00, 0x00, 0x00, //0x00003a11 jne          LBB0_621
	//0x00003a17 LBB0_610
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00003a17 movq         $-1, %rax
	0x48, 0x8b, 0x4d, 0xa0, //0x00003a1e movq         $-96(%rbp), %rcx
	0x48, 0x89, 0x4d, 0xc8, //0x00003a22 movq         %rcx, $-56(%rbp)
	0xe9, 0x7c, 0x00, 0x00, 0x00, //0x00003a26 jmp          LBB0_621
	//0x00003a2b LBB0_611
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x00003a2b movq         $-1, %r13
	//0x00003a32 LBB0_612
	0x4c, 0x29, 0xee, //0x00003a32 subq         %r13, %rsi
	0x48, 0x83, 0xc6, 0xfe, //0x00003a35 addq         $-2, %rsi
	0x48, 0x8b, 0x45, 0xd0, //0x00003a39 movq         $-48(%rbp), %rax
	0x48, 0x89, 0x30, //0x00003a3d movq         %rsi, (%rax)
	//0x00003a40 LBB0_637
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00003a40 movq         $-2, %rax
	//0x00003a47 LBB0_638
	0x48, 0x81, 0xc4, 0x88, 0x00, 0x00, 0x00, //0x00003a47 addq         $136, %rsp
	0x5b, //0x00003a4e popq         %rbx
	0x41, 0x5c, //0x00003a4f popq         %r12
	0x41, 0x5d, //0x00003a51 popq         %r13
	0x41, 0x5e, //0x00003a53 popq         %r14
	0x41, 0x5f, //0x00003a55 popq         %r15
	0x5d, //0x00003a57 popq         %rbp
	0xc3, //0x00003a58 retq         
	//0x00003a59 LBB0_614
	0x48, 0x83, 0xc6, 0xff, //0x00003a59 addq         $-1, %rsi
	0x48, 0x89, 0xf0, //0x00003a5d movq         %rsi, %rax
	0xe9, 0xe2, 0xff, 0xff, 0xff, //0x00003a60 jmp          LBB0_638
	//0x00003a65 LBB0_615
	0x48, 0x83, 0xf8, 0xff, //0x00003a65 cmpq         $-1, %rax
	0x48, 0x8b, 0x55, 0xc8, //0x00003a69 movq         $-56(%rbp), %rdx
	0x0f, 0x85, 0x0b, 0x00, 0x00, 0x00, //0x00003a6d jne          LBB0_617
	//0x00003a73 LBB0_616
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00003a73 movq         $-1, %rax
	0x48, 0x8b, 0x55, 0xa0, //0x00003a7a movq         $-96(%rbp), %rdx
	//0x00003a7e LBB0_617
	0x49, 0x89, 0x16, //0x00003a7e movq         %rdx, (%r14)
	0xe9, 0xc1, 0xff, 0xff, 0xff, //0x00003a81 jmp          LBB0_638
	//0x00003a86 LBB0_618
	0x48, 0x83, 0x7d, 0xc8, 0xff, //0x00003a86 cmpq         $-1, $-56(%rbp)
	0x0f, 0x85, 0x0b, 0x00, 0x00, 0x00, //0x00003a8b jne          LBB0_620
	0x48, 0x0f, 0xbc, 0xce, //0x00003a91 bsfq         %rsi, %rcx
	0x48, 0x01, 0xc1, //0x00003a95 addq         %rax, %rcx
	0x48, 0x89, 0x4d, 0xc8, //0x00003a98 movq         %rcx, $-56(%rbp)
	//0x00003a9c LBB0_620
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00003a9c movq         $-2, %rax
	0x4c, 0x8b, 0x75, 0xd0, //0x00003aa3 movq         $-48(%rbp), %r14
	//0x00003aa7 LBB0_621
	0x48, 0x8b, 0x4d, 0xc8, //0x00003aa7 movq         $-56(%rbp), %rcx
	0x49, 0x89, 0x0e, //0x00003aab movq         %rcx, (%r14)
	0xe9, 0x94, 0xff, 0xff, 0xff, //0x00003aae jmp          LBB0_638
	//0x00003ab3 LBB0_622
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00003ab3 movq         $-2, %rax
	0x80, 0xfa, 0x61, //0x00003aba cmpb         $97, %dl
	0x0f, 0x85, 0x84, 0xff, 0xff, 0xff, //0x00003abd jne          LBB0_638
	0x48, 0x8d, 0x51, 0x01, //0x00003ac3 leaq         $1(%rcx), %rdx
	0x49, 0x89, 0x16, //0x00003ac7 movq         %rdx, (%r14)
	0x41, 0x80, 0x7c, 0x0c, 0x01, 0x6c, //0x00003aca cmpb         $108, $1(%r12,%rcx)
	0x0f, 0x85, 0x71, 0xff, 0xff, 0xff, //0x00003ad0 jne          LBB0_638
	0x48, 0x8d, 0x51, 0x02, //0x00003ad6 leaq         $2(%rcx), %rdx
	0x49, 0x89, 0x16, //0x00003ada movq         %rdx, (%r14)
	0x41, 0x80, 0x7c, 0x0c, 0x02, 0x73, //0x00003add cmpb         $115, $2(%r12,%rcx)
	0x0f, 0x85, 0x5e, 0xff, 0xff, 0xff, //0x00003ae3 jne          LBB0_638
	0x48, 0x8d, 0x51, 0x03, //0x00003ae9 leaq         $3(%rcx), %rdx
	0x49, 0x89, 0x16, //0x00003aed movq         %rdx, (%r14)
	0x41, 0x80, 0x7c, 0x0c, 0x03, 0x65, //0x00003af0 cmpb         $101, $3(%r12,%rcx)
	0x0f, 0x85, 0x4b, 0xff, 0xff, 0xff, //0x00003af6 jne          LBB0_638
	0x48, 0x83, 0xc1, 0x04, //0x00003afc addq         $4, %rcx
	0x49, 0x89, 0x0e, //0x00003b00 movq         %rcx, (%r14)
	0xe9, 0x3f, 0xff, 0xff, 0xff, //0x00003b03 jmp          LBB0_638
	//0x00003b08 LBB0_627
	0x49, 0x89, 0x06, //0x00003b08 movq         %rax, (%r14)
	0x41, 0x80, 0x3c, 0x04, 0x74, //0x00003b0b cmpb         $116, (%r12,%rax)
	0x0f, 0x85, 0x2a, 0xff, 0xff, 0xff, //0x00003b10 jne          LBB0_637
	0x49, 0x89, 0x0e, //0x00003b16 movq         %rcx, (%r14)
	0x41, 0x80, 0x3c, 0x0c, 0x72, //0x00003b19 cmpb         $114, (%r12,%rcx)
	0x0f, 0x85, 0x1c, 0xff, 0xff, 0xff, //0x00003b1e jne          LBB0_637
	0x48, 0x8d, 0x41, 0x01, //0x00003b24 leaq         $1(%rcx), %rax
	0x49, 0x89, 0x06, //0x00003b28 movq         %rax, (%r14)
	0x41, 0x80, 0x7c, 0x0c, 0x01, 0x75, //0x00003b2b cmpb         $117, $1(%r12,%rcx)
	0x0f, 0x85, 0x09, 0xff, 0xff, 0xff, //0x00003b31 jne          LBB0_637
	0x48, 0x8d, 0x41, 0x02, //0x00003b37 leaq         $2(%rcx), %rax
	0x49, 0x89, 0x06, //0x00003b3b movq         %rax, (%r14)
	0x41, 0x80, 0x7c, 0x0c, 0x02, 0x65, //0x00003b3e cmpb         $101, $2(%r12,%rcx)
	0x0f, 0x85, 0xf6, 0xfe, 0xff, 0xff, //0x00003b44 jne          LBB0_637
	0xe9, 0x42, 0x00, 0x00, 0x00, //0x00003b4a jmp          LBB0_631
	//0x00003b4f LBB0_289
	0x49, 0x89, 0x06, //0x00003b4f movq         %rax, (%r14)
	0x41, 0x80, 0x3c, 0x04, 0x6e, //0x00003b52 cmpb         $110, (%r12,%rax)
	0x0f, 0x85, 0xe3, 0xfe, 0xff, 0xff, //0x00003b57 jne          LBB0_637
	0x49, 0x89, 0x0e, //0x00003b5d movq         %rcx, (%r14)
	0x41, 0x80, 0x3c, 0x0c, 0x75, //0x00003b60 cmpb         $117, (%r12,%rcx)
	0x0f, 0x85, 0xd5, 0xfe, 0xff, 0xff, //0x00003b65 jne          LBB0_637
	0x48, 0x8d, 0x41, 0x01, //0x00003b6b leaq         $1(%rcx), %rax
	0x49, 0x89, 0x06, //0x00003b6f movq         %rax, (%r14)
	0x41, 0x80, 0x7c, 0x0c, 0x01, 0x6c, //0x00003b72 cmpb         $108, $1(%r12,%rcx)
	0x0f, 0x85, 0xc2, 0xfe, 0xff, 0xff, //0x00003b78 jne          LBB0_637
	0x48, 0x8d, 0x41, 0x02, //0x00003b7e leaq         $2(%rcx), %rax
	0x49, 0x89, 0x06, //0x00003b82 movq         %rax, (%r14)
	0x41, 0x80, 0x7c, 0x0c, 0x02, 0x6c, //0x00003b85 cmpb         $108, $2(%r12,%rcx)
	0x0f, 0x85, 0xaf, 0xfe, 0xff, 0xff, //0x00003b8b jne          LBB0_637
	//0x00003b91 LBB0_631
	0x48, 0x83, 0xc1, 0x03, //0x00003b91 addq         $3, %rcx
	0x49, 0x89, 0x0e, //0x00003b95 movq         %rcx, (%r14)
	0xe9, 0xa3, 0xfe, 0xff, 0xff, //0x00003b98 jmp          LBB0_637
	//0x00003b9d LBB0_228
	0x48, 0x83, 0xc1, 0xff, //0x00003b9d addq         $-1, %rcx
	0x48, 0x89, 0xc8, //0x00003ba1 movq         %rcx, %rax
	0xe9, 0x9e, 0xfe, 0xff, 0xff, //0x00003ba4 jmp          LBB0_638
	//0x00003ba9 LBB0_632
	0x48, 0x8b, 0x55, 0xc8, //0x00003ba9 movq         $-56(%rbp), %rdx
	0x48, 0x83, 0xfa, 0xff, //0x00003bad cmpq         $-1, %rdx
	0x0f, 0x84, 0x31, 0x00, 0x00, 0x00, //0x00003bb1 je           LBB0_639
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00003bb7 movq         $-2, %rax
	0x49, 0x89, 0x16, //0x00003bbe movq         %rdx, (%r14)
	0xe9, 0x81, 0xfe, 0xff, 0xff, //0x00003bc1 jmp          LBB0_638
	//0x00003bc6 LBB0_185
	0x48, 0x8b, 0x75, 0xc0, //0x00003bc6 movq         $-64(%rbp), %rsi
	0xe9, 0x63, 0xfe, 0xff, 0xff, //0x00003bca jmp          LBB0_612
	//0x00003bcf LBB0_635
	0x48, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00003bcf movq         $-1, %rbx
	//0x00003bd6 LBB0_636
	0x48, 0xf7, 0xd3, //0x00003bd6 notq         %rbx
	0x48, 0x01, 0xd8, //0x00003bd9 addq         %rbx, %rax
	0x48, 0x8b, 0x4d, 0xd0, //0x00003bdc movq         $-48(%rbp), %rcx
	0x48, 0x89, 0x01, //0x00003be0 movq         %rax, (%rcx)
	0xe9, 0x58, 0xfe, 0xff, 0xff, //0x00003be3 jmp          LBB0_637
	//0x00003be8 LBB0_639
	0x48, 0x0f, 0xbc, 0xd7, //0x00003be8 bsfq         %rdi, %rdx
	//0x00003bec LBB0_640
	0x48, 0x01, 0xc2, //0x00003bec addq         %rax, %rdx
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00003bef movq         $-2, %rax
	0x49, 0x89, 0x16, //0x00003bf6 movq         %rdx, (%r14)
	0xe9, 0x49, 0xfe, 0xff, 0xff, //0x00003bf9 jmp          LBB0_638
	//0x00003bfe LBB0_642
	0x48, 0x8b, 0x45, 0xb0, //0x00003bfe movq         $-80(%rbp), %rax
	0x48, 0x89, 0x45, 0xa0, //0x00003c02 movq         %rax, $-96(%rbp)
	0xe9, 0x0c, 0xfe, 0xff, 0xff, //0x00003c06 jmp          LBB0_610
	//0x00003c0b LBB0_643
	0x48, 0x8b, 0x45, 0xb0, //0x00003c0b movq         $-80(%rbp), %rax
	0x48, 0x89, 0x45, 0xa0, //0x00003c0f movq         %rax, $-96(%rbp)
	0x4c, 0x8b, 0x75, 0xd0, //0x00003c13 movq         $-48(%rbp), %r14
	0xe9, 0xfb, 0xfd, 0xff, 0xff, //0x00003c17 jmp          LBB0_610
	//0x00003c1c LBB0_182
	0x48, 0x01, 0xc2, //0x00003c1c addq         %rax, %rdx
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00003c1f movq         $-2, %rax
	0x48, 0x89, 0x55, 0xc8, //0x00003c26 movq         %rdx, $-56(%rbp)
	0xe9, 0x78, 0xfe, 0xff, 0xff, //0x00003c2a jmp          LBB0_621
	//0x00003c2f LBB0_490
	0x4c, 0x8b, 0x75, 0xd0, //0x00003c2f movq         $-48(%rbp), %r14
	0xe9, 0xdf, 0xfd, 0xff, 0xff, //0x00003c33 jmp          LBB0_610
	//0x00003c38 LBB0_646
	0x48, 0x8b, 0x45, 0xb0, //0x00003c38 movq         $-80(%rbp), %rax
	0x48, 0x89, 0x45, 0xa0, //0x00003c3c movq         %rax, $-96(%rbp)
	0xe9, 0x2e, 0xfe, 0xff, 0xff, //0x00003c40 jmp          LBB0_616
	//0x00003c45 LBB0_644
	0x48, 0x89, 0x4d, 0xa0, //0x00003c45 movq         %rcx, $-96(%rbp)
	0xe9, 0x25, 0xfe, 0xff, 0xff, //0x00003c49 jmp          LBB0_616
	//0x00003c4e LBB0_507
	0x4c, 0x8b, 0x75, 0xd0, //0x00003c4e movq         $-48(%rbp), %r14
	0xe9, 0x1c, 0xfe, 0xff, 0xff, //0x00003c52 jmp          LBB0_616
	//0x00003c57 LBB0_648
	0x48, 0x8b, 0x4d, 0xa8, //0x00003c57 movq         $-88(%rbp), %rcx
	0x48, 0x8b, 0x49, 0x08, //0x00003c5b movq         $8(%rcx), %rcx
	0x48, 0x8b, 0x55, 0xd0, //0x00003c5f movq         $-48(%rbp), %rdx
	0x48, 0x89, 0x0a, //0x00003c63 movq         %rcx, (%rdx)
	0xe9, 0xdc, 0xfd, 0xff, 0xff, //0x00003c66 jmp          LBB0_638
	//0x00003c6b LBB0_649
	0x4c, 0x29, 0xe0, //0x00003c6b subq         %r12, %rax
	0x48, 0x01, 0xd0, //0x00003c6e addq         %rdx, %rax
	0x48, 0x89, 0x45, 0xc8, //0x00003c71 movq         %rax, $-56(%rbp)
	0xe9, 0x22, 0xfe, 0xff, 0xff, //0x00003c75 jmp          LBB0_620
	//0x00003c7a LBB0_650
	0x4c, 0x29, 0xc0, //0x00003c7a subq         %r8, %rax
	0x48, 0x01, 0xc8, //0x00003c7d addq         %rcx, %rax
	0xe9, 0x0f, 0x00, 0x00, 0x00, //0x00003c80 jmp          LBB0_653
	//0x00003c85 LBB0_651
	0x4c, 0x29, 0xe0, //0x00003c85 subq         %r12, %rax
	0x48, 0x89, 0x45, 0xc8, //0x00003c88 movq         %rax, $-56(%rbp)
	0xe9, 0x0b, 0xfe, 0xff, 0xff, //0x00003c8c jmp          LBB0_620
	//0x00003c91 LBB0_652
	0x4c, 0x29, 0xc0, //0x00003c91 subq         %r8, %rax
	//0x00003c94 LBB0_653
	0x48, 0x89, 0xc2, //0x00003c94 movq         %rax, %rdx
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00003c97 movq         $-2, %rax
	0x49, 0x89, 0x16, //0x00003c9e movq         %rdx, (%r14)
	0xe9, 0xa1, 0xfd, 0xff, 0xff, //0x00003ca1 jmp          LBB0_638
	//0x00003ca6 LBB0_654
	0x48, 0x01, 0xc8, //0x00003ca6 addq         %rcx, %rax
	0x48, 0x89, 0xc2, //0x00003ca9 movq         %rax, %rdx
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00003cac movq         $-2, %rax
	0x49, 0x89, 0x16, //0x00003cb3 movq         %rdx, (%r14)
	0xe9, 0x8c, 0xfd, 0xff, 0xff, //0x00003cb6 jmp          LBB0_638
	//0x00003cbb LBB0_655
	0x4c, 0x29, 0xe0, //0x00003cbb subq         %r12, %rax
	0x48, 0x01, 0xc8, //0x00003cbe addq         %rcx, %rax
	0xe9, 0x03, 0x00, 0x00, 0x00, //0x00003cc1 jmp          LBB0_657
	//0x00003cc6 LBB0_656
	0x4c, 0x29, 0xe0, //0x00003cc6 subq         %r12, %rax
	//0x00003cc9 LBB0_657
	0x48, 0x89, 0xc2, //0x00003cc9 movq         %rax, %rdx
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00003ccc movq         $-2, %rax
	0x4c, 0x8b, 0x75, 0xd0, //0x00003cd3 movq         $-48(%rbp), %r14
	0x49, 0x89, 0x16, //0x00003cd7 movq         %rdx, (%r14)
	0xe9, 0x68, 0xfd, 0xff, 0xff, //0x00003cda jmp          LBB0_638
	0x90, //0x00003cdf .p2align 2, 0x90
	// // .set L0_0_set_35, LBB0_35-LJTI0_0
	// // .set L0_0_set_61, LBB0_61-LJTI0_0
	// // .set L0_0_set_40, LBB0_40-LJTI0_0
	// // .set L0_0_set_59, LBB0_59-LJTI0_0
	// // .set L0_0_set_38, LBB0_38-LJTI0_0
	// // .set L0_0_set_63, LBB0_63-LJTI0_0
	//0x00003ce0 LJTI0_0
	0xd4, 0xc6, 0xff, 0xff, //0x00003ce0 .long L0_0_set_35
	0xec, 0xc8, 0xff, 0xff, //0x00003ce4 .long L0_0_set_61
	0x10, 0xc7, 0xff, 0xff, //0x00003ce8 .long L0_0_set_40
	0xd0, 0xc8, 0xff, 0xff, //0x00003cec .long L0_0_set_59
	0xeb, 0xc6, 0xff, 0xff, //0x00003cf0 .long L0_0_set_38
	0x1c, 0xc9, 0xff, 0xff, //0x00003cf4 .long L0_0_set_63
	// // .set L0_1_set_638, LBB0_638-LJTI0_1
	// // .set L0_1_set_637, LBB0_637-LJTI0_1
	// // .set L0_1_set_232, LBB0_232-LJTI0_1
	// // .set L0_1_set_250, LBB0_250-LJTI0_1
	// // .set L0_1_set_80, LBB0_80-LJTI0_1
	// // .set L0_1_set_229, LBB0_229-LJTI0_1
	// // .set L0_1_set_225, LBB0_225-LJTI0_1
	// // .set L0_1_set_287, LBB0_287-LJTI0_1
	// // .set L0_1_set_296, LBB0_296-LJTI0_1
	// // .set L0_1_set_293, LBB0_293-LJTI0_1
	//0x00003cf8 LJTI0_1
	0x4f, 0xfd, 0xff, 0xff, //0x00003cf8 .long L0_1_set_638
	0x48, 0xfd, 0xff, 0xff, //0x00003cfc .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003d00 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003d04 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003d08 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003d0c .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003d10 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003d14 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003d18 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003d1c .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003d20 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003d24 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003d28 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003d2c .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003d30 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003d34 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003d38 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003d3c .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003d40 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003d44 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003d48 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003d4c .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003d50 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003d54 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003d58 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003d5c .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003d60 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003d64 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003d68 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003d6c .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003d70 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003d74 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003d78 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003d7c .long L0_1_set_637
	0x19, 0xd6, 0xff, 0xff, //0x00003d80 .long L0_1_set_232
	0x48, 0xfd, 0xff, 0xff, //0x00003d84 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003d88 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003d8c .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003d90 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003d94 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003d98 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003d9c .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003da0 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003da4 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003da8 .long L0_1_set_637
	0xd2, 0xd7, 0xff, 0xff, //0x00003dac .long L0_1_set_250
	0x48, 0xfd, 0xff, 0xff, //0x00003db0 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003db4 .long L0_1_set_637
	0x51, 0xc9, 0xff, 0xff, //0x00003db8 .long L0_1_set_80
	0x51, 0xc9, 0xff, 0xff, //0x00003dbc .long L0_1_set_80
	0x51, 0xc9, 0xff, 0xff, //0x00003dc0 .long L0_1_set_80
	0x51, 0xc9, 0xff, 0xff, //0x00003dc4 .long L0_1_set_80
	0x51, 0xc9, 0xff, 0xff, //0x00003dc8 .long L0_1_set_80
	0x51, 0xc9, 0xff, 0xff, //0x00003dcc .long L0_1_set_80
	0x51, 0xc9, 0xff, 0xff, //0x00003dd0 .long L0_1_set_80
	0x51, 0xc9, 0xff, 0xff, //0x00003dd4 .long L0_1_set_80
	0x51, 0xc9, 0xff, 0xff, //0x00003dd8 .long L0_1_set_80
	0x51, 0xc9, 0xff, 0xff, //0x00003ddc .long L0_1_set_80
	0x48, 0xfd, 0xff, 0xff, //0x00003de0 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003de4 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003de8 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003dec .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003df0 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003df4 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003df8 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003dfc .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003e00 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003e04 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003e08 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003e0c .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003e10 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003e14 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003e18 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003e1c .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003e20 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003e24 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003e28 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003e2c .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003e30 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003e34 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003e38 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003e3c .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003e40 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003e44 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003e48 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003e4c .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003e50 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003e54 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003e58 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003e5c .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003e60 .long L0_1_set_637
	0xe9, 0xd5, 0xff, 0xff, //0x00003e64 .long L0_1_set_229
	0x48, 0xfd, 0xff, 0xff, //0x00003e68 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003e6c .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003e70 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003e74 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003e78 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003e7c .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003e80 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003e84 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003e88 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003e8c .long L0_1_set_637
	0xac, 0xd5, 0xff, 0xff, //0x00003e90 .long L0_1_set_225
	0x48, 0xfd, 0xff, 0xff, //0x00003e94 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003e98 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003e9c .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003ea0 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003ea4 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003ea8 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003eac .long L0_1_set_637
	0x20, 0xda, 0xff, 0xff, //0x00003eb0 .long L0_1_set_287
	0x48, 0xfd, 0xff, 0xff, //0x00003eb4 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003eb8 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003ebc .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003ec0 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003ec4 .long L0_1_set_637
	0x80, 0xda, 0xff, 0xff, //0x00003ec8 .long L0_1_set_296
	0x48, 0xfd, 0xff, 0xff, //0x00003ecc .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003ed0 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003ed4 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003ed8 .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003edc .long L0_1_set_637
	0x48, 0xfd, 0xff, 0xff, //0x00003ee0 .long L0_1_set_637
	0x50, 0xda, 0xff, 0xff, //0x00003ee4 .long L0_1_set_293
	// // .set L0_2_set_276, LBB0_276-LJTI0_2
	// // .set L0_2_set_412, LBB0_412-LJTI0_2
	// // .set L0_2_set_282, LBB0_282-LJTI0_2
	// // .set L0_2_set_285, LBB0_285-LJTI0_2
	//0x00003ee8 LJTI0_2
	0xaf, 0xd7, 0xff, 0xff, //0x00003ee8 .long L0_2_set_276
	0xc0, 0xe6, 0xff, 0xff, //0x00003eec .long L0_2_set_412
	0xaf, 0xd7, 0xff, 0xff, //0x00003ef0 .long L0_2_set_276
	0x01, 0xd8, 0xff, 0xff, //0x00003ef4 .long L0_2_set_282
	0xc0, 0xe6, 0xff, 0xff, //0x00003ef8 .long L0_2_set_412
	0xc0, 0xe6, 0xff, 0xff, //0x00003efc .long L0_2_set_412
	0xc0, 0xe6, 0xff, 0xff, //0x00003f00 .long L0_2_set_412
	0xc0, 0xe6, 0xff, 0xff, //0x00003f04 .long L0_2_set_412
	0xc0, 0xe6, 0xff, 0xff, //0x00003f08 .long L0_2_set_412
	0xc0, 0xe6, 0xff, 0xff, //0x00003f0c .long L0_2_set_412
	0xc0, 0xe6, 0xff, 0xff, //0x00003f10 .long L0_2_set_412
	0xc0, 0xe6, 0xff, 0xff, //0x00003f14 .long L0_2_set_412
	0xc0, 0xe6, 0xff, 0xff, //0x00003f18 .long L0_2_set_412
	0xc0, 0xe6, 0xff, 0xff, //0x00003f1c .long L0_2_set_412
	0xc0, 0xe6, 0xff, 0xff, //0x00003f20 .long L0_2_set_412
	0xc0, 0xe6, 0xff, 0xff, //0x00003f24 .long L0_2_set_412
	0xc0, 0xe6, 0xff, 0xff, //0x00003f28 .long L0_2_set_412
	0xc0, 0xe6, 0xff, 0xff, //0x00003f2c .long L0_2_set_412
	0xc0, 0xe6, 0xff, 0xff, //0x00003f30 .long L0_2_set_412
	0xc0, 0xe6, 0xff, 0xff, //0x00003f34 .long L0_2_set_412
	0xc0, 0xe6, 0xff, 0xff, //0x00003f38 .long L0_2_set_412
	0xc0, 0xe6, 0xff, 0xff, //0x00003f3c .long L0_2_set_412
	0xc0, 0xe6, 0xff, 0xff, //0x00003f40 .long L0_2_set_412
	0xc0, 0xe6, 0xff, 0xff, //0x00003f44 .long L0_2_set_412
	0xc0, 0xe6, 0xff, 0xff, //0x00003f48 .long L0_2_set_412
	0xc0, 0xe6, 0xff, 0xff, //0x00003f4c .long L0_2_set_412
	0x1d, 0xd8, 0xff, 0xff, //0x00003f50 .long L0_2_set_285
	// // .set L0_3_set_113, LBB0_113-LJTI0_3
	// // .set L0_3_set_212, LBB0_212-LJTI0_3
	// // .set L0_3_set_115, LBB0_115-LJTI0_3
	// // .set L0_3_set_107, LBB0_107-LJTI0_3
	//0x00003f54 LJTI0_3
	0x2f, 0xc9, 0xff, 0xff, //0x00003f54 .long L0_3_set_113
	0xa0, 0xd2, 0xff, 0xff, //0x00003f58 .long L0_3_set_212
	0x2f, 0xc9, 0xff, 0xff, //0x00003f5c .long L0_3_set_113
	0x42, 0xc9, 0xff, 0xff, //0x00003f60 .long L0_3_set_115
	0xa0, 0xd2, 0xff, 0xff, //0x00003f64 .long L0_3_set_212
	0xa0, 0xd2, 0xff, 0xff, //0x00003f68 .long L0_3_set_212
	0xa0, 0xd2, 0xff, 0xff, //0x00003f6c .long L0_3_set_212
	0xa0, 0xd2, 0xff, 0xff, //0x00003f70 .long L0_3_set_212
	0xa0, 0xd2, 0xff, 0xff, //0x00003f74 .long L0_3_set_212
	0xa0, 0xd2, 0xff, 0xff, //0x00003f78 .long L0_3_set_212
	0xa0, 0xd2, 0xff, 0xff, //0x00003f7c .long L0_3_set_212
	0xa0, 0xd2, 0xff, 0xff, //0x00003f80 .long L0_3_set_212
	0xa0, 0xd2, 0xff, 0xff, //0x00003f84 .long L0_3_set_212
	0xa0, 0xd2, 0xff, 0xff, //0x00003f88 .long L0_3_set_212
	0xa0, 0xd2, 0xff, 0xff, //0x00003f8c .long L0_3_set_212
	0xa0, 0xd2, 0xff, 0xff, //0x00003f90 .long L0_3_set_212
	0xa0, 0xd2, 0xff, 0xff, //0x00003f94 .long L0_3_set_212
	0xa0, 0xd2, 0xff, 0xff, //0x00003f98 .long L0_3_set_212
	0xa0, 0xd2, 0xff, 0xff, //0x00003f9c .long L0_3_set_212
	0xa0, 0xd2, 0xff, 0xff, //0x00003fa0 .long L0_3_set_212
	0xa0, 0xd2, 0xff, 0xff, //0x00003fa4 .long L0_3_set_212
	0xa0, 0xd2, 0xff, 0xff, //0x00003fa8 .long L0_3_set_212
	0xa0, 0xd2, 0xff, 0xff, //0x00003fac .long L0_3_set_212
	0xa0, 0xd2, 0xff, 0xff, //0x00003fb0 .long L0_3_set_212
	0xa0, 0xd2, 0xff, 0xff, //0x00003fb4 .long L0_3_set_212
	0xa0, 0xd2, 0xff, 0xff, //0x00003fb8 .long L0_3_set_212
	0xe4, 0xc8, 0xff, 0xff, //0x00003fbc .long L0_3_set_107
	//0x00003fc0 .p2align 2, 0x00
	//0x00003fc0 _MASK_USE_NUMBER
	0x02, 0x00, 0x00, 0x00, //0x00003fc0 .long 2
}
 
