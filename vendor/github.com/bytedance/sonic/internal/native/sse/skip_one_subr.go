// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package sse

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__skip_one = 256
)

const (
    _stack__skip_one = 232
)

const (
    _size__skip_one = 13880
)

var (
    _pcsp__skip_one = [][2]uint32{
        {0x1, 0},
        {0x6, 8},
        {0x8, 16},
        {0xa, 24},
        {0xc, 32},
        {0xd, 40},
        {0x14, 48},
        {0x3330, 232},
        {0x3331, 48},
        {0x3333, 40},
        {0x3335, 32},
        {0x3337, 24},
        {0x3339, 16},
        {0x333a, 8},
        {0x333b, 0},
        {0x3638, 232},
    }
)

var _cfunc_skip_one = []loader.CFunc{
    {"_skip_one_entry", 0,  _entry__skip_one, 0, nil},
    {"_skip_one", _entry__skip_one, _size__skip_one, _stack__skip_one, _pcsp__skip_one},
}
