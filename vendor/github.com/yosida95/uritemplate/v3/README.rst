uritemplate
===========

`uritemplate`_ is a Go implementation of `URI Template`_ [RFC6570] with
full functionality of URI Template Level 4.

uritemplate can also generate a regexp that matches expansion of the
URI Template from a URI Template.

Getting Started
---------------

Installation
~~~~~~~~~~~~

.. code-block:: sh

   $ go get -u github.com/yosida95/uritemplate/v3

Documentation
~~~~~~~~~~~~~

The documentation is available on GoDoc_.

Examples
--------

See `examples on GoDoc`_.

License
-------

`uritemplate`_ is distributed under the BSD 3-Clause license.
PLEASE READ ./LICENSE carefully and follow its clauses to use this software.

Author
------

yosida95_


.. _`URI Template`: https://tools.ietf.org/html/rfc6570
.. _Godoc: https://godoc.org/github.com/yosida95/uritemplate
.. _`examples on GoDoc`: https://godoc.org/github.com/yosida95/uritemplate#pkg-examples
.. _yosida95: https://yosida95.com/
.. _uritemplate: https://github.com/yosida95/uritemplate
