version: "2"
linters:
  default: none
  enable:
    - bodyclose
    - dogsled
    - dupl
    - errcheck
    - exhaustive
    - gochecknoinits
    - goconst
    - gocritic
    - gocyclo
    - goprintffuncname
    - gosec
    - govet
    - ineffassign
    - lll
    - misspell
    - nakedret
    - noctx
    - nolintlint
    - rowserrcheck
    - staticcheck
    - unconvert
    - unparam
    - unused
    - whitespace
  exclusions:
    generated: lax
    presets:
      - comments
      - common-false-positives
      - legacy
      - std-error-handling
    paths:
      - third_party$
      - builtin$
      - examples$
formatters:
  enable:
    - gofmt
    - gofumpt
    - goimports
  exclusions:
    generated: lax
    paths:
      - third_party$
      - builtin$
      - examples$
