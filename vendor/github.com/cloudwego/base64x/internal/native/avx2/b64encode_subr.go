// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package avx2

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__b64encode = 288
)

const (
    _stack__b64encode = 40
)

const (
    _size__b64encode = 832
)

var (
    _pcsp__b64encode = [][2]uint32{
        {0x1, 0},
        {0x6, 8},
        {0x8, 16},
        {0xa, 24},
        {0xb, 32},
        {0x32a, 40},
        {0x32c, 32},
        {0x32e, 24},
        {0x330, 16},
        {0x331, 8},
        {0x340, 0},
    }
)

var _cfunc_b64encode = []loader.CFunc{
    {"_b64encode_entry", 0,  _entry__b64encode, 0, nil},
    {"_b64encode", _entry__b64encode, _size__b64encode, _stack__b64encode, _pcsp__b64encode},
}
