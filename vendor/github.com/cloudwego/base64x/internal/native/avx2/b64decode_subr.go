// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package avx2

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__b64decode = 464
)

const (
    _stack__b64decode = 136
)

const (
    _size__b64decode = 13488
)

var (
    _pcsp__b64decode = [][2]uint32{
        {0x1, 0},
        {0x6, 8},
        {0x8, 16},
        {0xa, 24},
        {0xc, 32},
        {0xd, 40},
        {0x11, 48},
        {0x349e, 136},
        {0x349f, 48},
        {0x34a1, 40},
        {0x34a3, 32},
        {0x34a5, 24},
        {0x34a7, 16},
        {0x34a8, 8},
        {0x34b0, 0},
    }
)

var _cfunc_b64decode = []loader.CFunc{
    {"_b64decode_entry", 0,  _entry__b64decode, 0, nil},
    {"_b64decode", _entry__b64decode, _size__b64decode, _stack__b64decode, _pcsp__b64decode},
}
