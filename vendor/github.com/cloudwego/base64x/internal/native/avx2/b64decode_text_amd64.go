// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package avx2

var _text_b64decode = []byte{
	// .p2align 5, 0x00
	// LCPI0_0
	0xa8, // .byte 168
	0xf8, //0x00000001 .byte 248
	0xf8, //0x00000002 .byte 248
	0xf8, //0x00000003 .byte 248
	0xf8, //0x00000004 .byte 248
	0xf8, //0x00000005 .byte 248
	0xf8, //0x00000006 .byte 248
	0xf8, //0x00000007 .byte 248
	0xf8, //0x00000008 .byte 248
	0xf8, //0x00000009 .byte 248
	0xf0, //0x0000000a .byte 240
	0x54, //0x0000000b .byte 84
	0x50, //0x0000000c .byte 80
	0x50, //0x0000000d .byte 80
	0x50, //0x0000000e .byte 80
	0x54, //0x0000000f .byte 84
	0xa8, //0x00000010 .byte 168
	0xf8, //0x00000011 .byte 248
	0xf8, //0x00000012 .byte 248
	0xf8, //0x00000013 .byte 248
	0xf8, //0x00000014 .byte 248
	0xf8, //0x00000015 .byte 248
	0xf8, //0x00000016 .byte 248
	0xf8, //0x00000017 .byte 248
	0xf8, //0x00000018 .byte 248
	0xf8, //0x00000019 .byte 248
	0xf0, //0x0000001a .byte 240
	0x54, //0x0000001b .byte 84
	0x50, //0x0000001c .byte 80
	0x50, //0x0000001d .byte 80
	0x50, //0x0000001e .byte 80
	0x54, //0x0000001f .byte 84
	//0x00000020 LCPI0_1
	0xa8, //0x00000020 .byte 168
	0xf8, //0x00000021 .byte 248
	0xf8, //0x00000022 .byte 248
	0xf8, //0x00000023 .byte 248
	0xf8, //0x00000024 .byte 248
	0xf8, //0x00000025 .byte 248
	0xf8, //0x00000026 .byte 248
	0xf8, //0x00000027 .byte 248
	0xf8, //0x00000028 .byte 248
	0xf8, //0x00000029 .byte 248
	0xf0, //0x0000002a .byte 240
	0x50, //0x0000002b .byte 80
	0x50, //0x0000002c .byte 80
	0x54, //0x0000002d .byte 84
	0x50, //0x0000002e .byte 80
	0x70, //0x0000002f .byte 112
	0xa8, //0x00000030 .byte 168
	0xf8, //0x00000031 .byte 248
	0xf8, //0x00000032 .byte 248
	0xf8, //0x00000033 .byte 248
	0xf8, //0x00000034 .byte 248
	0xf8, //0x00000035 .byte 248
	0xf8, //0x00000036 .byte 248
	0xf8, //0x00000037 .byte 248
	0xf8, //0x00000038 .byte 248
	0xf8, //0x00000039 .byte 248
	0xf0, //0x0000003a .byte 240
	0x50, //0x0000003b .byte 80
	0x50, //0x0000003c .byte 80
	0x54, //0x0000003d .byte 84
	0x50, //0x0000003e .byte 80
	0x70, //0x0000003f .byte 112
	//0x00000040 LCPI0_2
	0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, //0x00000040 QUAD $0x2f2f2f2f2f2f2f2f; QUAD $0x2f2f2f2f2f2f2f2f  // .space 16, '////////////////'
	0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, //0x00000050 QUAD $0x2f2f2f2f2f2f2f2f; QUAD $0x2f2f2f2f2f2f2f2f  // .space 16, '////////////////'
	//0x00000060 LCPI0_3
	0x5f, 0x5f, 0x5f, 0x5f, 0x5f, 0x5f, 0x5f, 0x5f, 0x5f, 0x5f, 0x5f, 0x5f, 0x5f, 0x5f, 0x5f, 0x5f, //0x00000060 QUAD $0x5f5f5f5f5f5f5f5f; QUAD $0x5f5f5f5f5f5f5f5f  // .space 16, '________________'
	0x5f, 0x5f, 0x5f, 0x5f, 0x5f, 0x5f, 0x5f, 0x5f, 0x5f, 0x5f, 0x5f, 0x5f, 0x5f, 0x5f, 0x5f, 0x5f, //0x00000070 QUAD $0x5f5f5f5f5f5f5f5f; QUAD $0x5f5f5f5f5f5f5f5f  // .space 16, '________________'
	//0x00000080 LCPI0_4
	0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, //0x00000080 QUAD $0x1010101010101010; QUAD $0x1010101010101010  // .space 16, '\x10\x10\x10\x10\x10\x10\x10\x10\x10\x10\x10\x10\x10\x10\x10\x10'
	0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, //0x00000090 QUAD $0x1010101010101010; QUAD $0x1010101010101010  // .space 16, '\x10\x10\x10\x10\x10\x10\x10\x10\x10\x10\x10\x10\x10\x10\x10\x10'
	//0x000000a0 LCPI0_5
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, //0x000000a0 QUAD $0x2020202020202020; QUAD $0x2020202020202020  // .space 16, '                '
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, //0x000000b0 QUAD $0x2020202020202020; QUAD $0x2020202020202020  // .space 16, '                '
	//0x000000c0 LCPI0_6
	0x00, //0x000000c0 .byte 0
	0x00, //0x000000c1 .byte 0
	0x13, //0x000000c2 .byte 19
	0x04, //0x000000c3 .byte 4
	0xbf, //0x000000c4 .byte 191
	0xbf, //0x000000c5 .byte 191
	0xb9, //0x000000c6 .byte 185
	0xb9, //0x000000c7 .byte 185
	0x00, //0x000000c8 .byte 0
	0x00, //0x000000c9 .byte 0
	0x00, //0x000000ca .byte 0
	0x00, //0x000000cb .byte 0
	0x00, //0x000000cc .byte 0
	0x00, //0x000000cd .byte 0
	0x00, //0x000000ce .byte 0
	0x00, //0x000000cf .byte 0
	0x00, //0x000000d0 .byte 0
	0x00, //0x000000d1 .byte 0
	0x13, //0x000000d2 .byte 19
	0x04, //0x000000d3 .byte 4
	0xbf, //0x000000d4 .byte 191
	0xbf, //0x000000d5 .byte 191
	0xb9, //0x000000d6 .byte 185
	0xb9, //0x000000d7 .byte 185
	0x00, //0x000000d8 .byte 0
	0x00, //0x000000d9 .byte 0
	0x00, //0x000000da .byte 0
	0x00, //0x000000db .byte 0
	0x00, //0x000000dc .byte 0
	0x00, //0x000000dd .byte 0
	0x00, //0x000000de .byte 0
	0x00, //0x000000df .byte 0
	//0x000000e0 LCPI0_7
	0x00, //0x000000e0 .byte 0
	0x00, //0x000000e1 .byte 0
	0x11, //0x000000e2 .byte 17
	0x04, //0x000000e3 .byte 4
	0xbf, //0x000000e4 .byte 191
	0xbf, //0x000000e5 .byte 191
	0xb9, //0x000000e6 .byte 185
	0xb9, //0x000000e7 .byte 185
	0x00, //0x000000e8 .byte 0
	0x00, //0x000000e9 .byte 0
	0x00, //0x000000ea .byte 0
	0x00, //0x000000eb .byte 0
	0x00, //0x000000ec .byte 0
	0x00, //0x000000ed .byte 0
	0x00, //0x000000ee .byte 0
	0x00, //0x000000ef .byte 0
	0x00, //0x000000f0 .byte 0
	0x00, //0x000000f1 .byte 0
	0x11, //0x000000f2 .byte 17
	0x04, //0x000000f3 .byte 4
	0xbf, //0x000000f4 .byte 191
	0xbf, //0x000000f5 .byte 191
	0xb9, //0x000000f6 .byte 185
	0xb9, //0x000000f7 .byte 185
	0x00, //0x000000f8 .byte 0
	0x00, //0x000000f9 .byte 0
	0x00, //0x000000fa .byte 0
	0x00, //0x000000fb .byte 0
	0x00, //0x000000fc .byte 0
	0x00, //0x000000fd .byte 0
	0x00, //0x000000fe .byte 0
	0x00, //0x000000ff .byte 0
	//0x00000100 LCPI0_8
	0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, //0x00000100 QUAD $0x0f0f0f0f0f0f0f0f; QUAD $0x0f0f0f0f0f0f0f0f  // .space 16, '\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f'
	0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, //0x00000110 QUAD $0x0f0f0f0f0f0f0f0f; QUAD $0x0f0f0f0f0f0f0f0f  // .space 16, '\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f'
	//0x00000120 LCPI0_9
	0x01, //0x00000120 .byte 1
	0x02, //0x00000121 .byte 2
	0x04, //0x00000122 .byte 4
	0x08, //0x00000123 .byte 8
	0x10, //0x00000124 .byte 16
	0x20, //0x00000125 .byte 32
	0x40, //0x00000126 .byte 64
	0x80, //0x00000127 .byte 128
	0x00, //0x00000128 .byte 0
	0x00, //0x00000129 .byte 0
	0x00, //0x0000012a .byte 0
	0x00, //0x0000012b .byte 0
	0x00, //0x0000012c .byte 0
	0x00, //0x0000012d .byte 0
	0x00, //0x0000012e .byte 0
	0x00, //0x0000012f .byte 0
	0x01, //0x00000130 .byte 1
	0x02, //0x00000131 .byte 2
	0x04, //0x00000132 .byte 4
	0x08, //0x00000133 .byte 8
	0x10, //0x00000134 .byte 16
	0x20, //0x00000135 .byte 32
	0x40, //0x00000136 .byte 64
	0x80, //0x00000137 .byte 128
	0x00, //0x00000138 .byte 0
	0x00, //0x00000139 .byte 0
	0x00, //0x0000013a .byte 0
	0x00, //0x0000013b .byte 0
	0x00, //0x0000013c .byte 0
	0x00, //0x0000013d .byte 0
	0x00, //0x0000013e .byte 0
	0x00, //0x0000013f .byte 0
	//0x00000140 LCPI0_10
	0x3f, 0x3f, 0x3f, 0x3f, 0x3f, 0x3f, 0x3f, 0x3f, 0x3f, 0x3f, 0x3f, 0x3f, 0x3f, 0x3f, 0x3f, 0x3f, //0x00000140 QUAD $0x3f3f3f3f3f3f3f3f; QUAD $0x3f3f3f3f3f3f3f3f  // .space 16, '????????????????'
	0x3f, 0x3f, 0x3f, 0x3f, 0x3f, 0x3f, 0x3f, 0x3f, 0x3f, 0x3f, 0x3f, 0x3f, 0x3f, 0x3f, 0x3f, 0x3f, //0x00000150 QUAD $0x3f3f3f3f3f3f3f3f; QUAD $0x3f3f3f3f3f3f3f3f  // .space 16, '????????????????'
	//0x00000160 LCPI0_11
	0x40, //0x00000160 .byte 64
	0x01, //0x00000161 .byte 1
	0x40, //0x00000162 .byte 64
	0x01, //0x00000163 .byte 1
	0x40, //0x00000164 .byte 64
	0x01, //0x00000165 .byte 1
	0x40, //0x00000166 .byte 64
	0x01, //0x00000167 .byte 1
	0x40, //0x00000168 .byte 64
	0x01, //0x00000169 .byte 1
	0x40, //0x0000016a .byte 64
	0x01, //0x0000016b .byte 1
	0x40, //0x0000016c .byte 64
	0x01, //0x0000016d .byte 1
	0x40, //0x0000016e .byte 64
	0x01, //0x0000016f .byte 1
	0x40, //0x00000170 .byte 64
	0x01, //0x00000171 .byte 1
	0x40, //0x00000172 .byte 64
	0x01, //0x00000173 .byte 1
	0x40, //0x00000174 .byte 64
	0x01, //0x00000175 .byte 1
	0x40, //0x00000176 .byte 64
	0x01, //0x00000177 .byte 1
	0x40, //0x00000178 .byte 64
	0x01, //0x00000179 .byte 1
	0x40, //0x0000017a .byte 64
	0x01, //0x0000017b .byte 1
	0x40, //0x0000017c .byte 64
	0x01, //0x0000017d .byte 1
	0x40, //0x0000017e .byte 64
	0x01, //0x0000017f .byte 1
	//0x00000180 LCPI0_12
	0x00, 0x10, //0x00000180 .word 4096
	0x01, 0x00, //0x00000182 .word 1
	0x00, 0x10, //0x00000184 .word 4096
	0x01, 0x00, //0x00000186 .word 1
	0x00, 0x10, //0x00000188 .word 4096
	0x01, 0x00, //0x0000018a .word 1
	0x00, 0x10, //0x0000018c .word 4096
	0x01, 0x00, //0x0000018e .word 1
	0x00, 0x10, //0x00000190 .word 4096
	0x01, 0x00, //0x00000192 .word 1
	0x00, 0x10, //0x00000194 .word 4096
	0x01, 0x00, //0x00000196 .word 1
	0x00, 0x10, //0x00000198 .word 4096
	0x01, 0x00, //0x0000019a .word 1
	0x00, 0x10, //0x0000019c .word 4096
	0x01, 0x00, //0x0000019e .word 1
	//0x000001a0 LCPI0_14
	0x02, //0x000001a0 .byte 2
	0x01, //0x000001a1 .byte 1
	0x00, //0x000001a2 .byte 0
	0x06, //0x000001a3 .byte 6
	0x05, //0x000001a4 .byte 5
	0x04, //0x000001a5 .byte 4
	0x0a, //0x000001a6 .byte 10
	0x09, //0x000001a7 .byte 9
	0x08, //0x000001a8 .byte 8
	0x0e, //0x000001a9 .byte 14
	0x0d, //0x000001aa .byte 13
	0x0c, //0x000001ab .byte 12
	0x00, //0x000001ac BYTE $0x00  // .space 1, '\x00'
	0x00, //0x000001ad BYTE $0x00  // .space 1, '\x00'
	0x00, //0x000001ae BYTE $0x00  // .space 1, '\x00'
	0x00, //0x000001af BYTE $0x00  // .space 1, '\x00'
	0x05, //0x000001b0 .byte 5
	0x04, //0x000001b1 .byte 4
	0x0a, //0x000001b2 .byte 10
	0x09, //0x000001b3 .byte 9
	0x08, //0x000001b4 .byte 8
	0x0e, //0x000001b5 .byte 14
	0x0d, //0x000001b6 .byte 13
	0x0c, //0x000001b7 .byte 12
	0x00, //0x000001b8 BYTE $0x00  // .space 1, '\x00'
	0x00, //0x000001b9 BYTE $0x00  // .space 1, '\x00'
	0x00, //0x000001ba BYTE $0x00  // .space 1, '\x00'
	0x00, //0x000001bb BYTE $0x00  // .space 1, '\x00'
	0x00, //0x000001bc BYTE $0x00  // .space 1, '\x00'
	0x00, //0x000001bd BYTE $0x00  // .space 1, '\x00'
	0x00, //0x000001be BYTE $0x00  // .space 1, '\x00'
	0x00, //0x000001bf BYTE $0x00  // .space 1, '\x00'
	//0x000001c0 .p2align 4, 0x00
	//0x000001c0 LCPI0_13
	0x00, //0x000001c0 BYTE $0x00  // .space 1, '\x00'
	0x00, //0x000001c1 BYTE $0x00  // .space 1, '\x00'
	0x00, //0x000001c2 BYTE $0x00  // .space 1, '\x00'
	0x00, //0x000001c3 BYTE $0x00  // .space 1, '\x00'
	0x00, //0x000001c4 BYTE $0x00  // .space 1, '\x00'
	0x00, //0x000001c5 BYTE $0x00  // .space 1, '\x00'
	0x00, //0x000001c6 BYTE $0x00  // .space 1, '\x00'
	0x00, //0x000001c7 BYTE $0x00  // .space 1, '\x00'
	0x00, //0x000001c8 BYTE $0x00  // .space 1, '\x00'
	0x00, //0x000001c9 BYTE $0x00  // .space 1, '\x00'
	0x00, //0x000001ca BYTE $0x00  // .space 1, '\x00'
	0x00, //0x000001cb BYTE $0x00  // .space 1, '\x00'
	0x02, //0x000001cc .byte 2
	0x01, //0x000001cd .byte 1
	0x00, //0x000001ce .byte 0
	0x06, //0x000001cf .byte 6
	//0x000001d0 .p2align 4, 0x90
	//0x000001d0 _b64decode
	0x55, //0x000001d0 pushq        %rbp
	0x48, 0x89, 0xe5, //0x000001d1 movq         %rsp, %rbp
	0x41, 0x57, //0x000001d4 pushq        %r15
	0x41, 0x56, //0x000001d6 pushq        %r14
	0x41, 0x55, //0x000001d8 pushq        %r13
	0x41, 0x54, //0x000001da pushq        %r12
	0x53, //0x000001dc pushq        %rbx
	0x48, 0x83, 0xec, 0x58, //0x000001dd subq         $88, %rsp
	0x48, 0x85, 0xd2, //0x000001e1 testq        %rdx, %rdx
	0x0f, 0x84, 0x85, 0x00, 0x00, 0x00, //0x000001e4 je           LBB0_6
	0x41, 0x89, 0xca, //0x000001ea movl         %ecx, %r10d
	0x48, 0x8b, 0x07, //0x000001ed movq         (%rdi), %rax
	0x48, 0x8b, 0x4f, 0x10, //0x000001f0 movq         $16(%rdi), %rcx
	0x48, 0x89, 0x7d, 0x80, //0x000001f4 movq         %rdi, $-128(%rbp)
	0x48, 0x8b, 0x7f, 0x08, //0x000001f8 movq         $8(%rdi), %rdi
	0x48, 0x01, 0xc7, //0x000001fc addq         %rax, %rdi
	0x48, 0x89, 0x7d, 0x98, //0x000001ff movq         %rdi, $-104(%rbp)
	0x48, 0x8d, 0x3c, 0x08, //0x00000203 leaq         (%rax,%rcx), %rdi
	0x48, 0x89, 0x7d, 0xb8, //0x00000207 movq         %rdi, $-72(%rbp)
	0x4c, 0x8d, 0x0c, 0x16, //0x0000020b leaq         (%rsi,%rdx), %r9
	0x48, 0x8d, 0x1d, 0x6a, 0x34, 0x00, 0x00, //0x0000020f leaq         $13418(%rip), %rbx  /* _VecDecodeCharsetStd+0(%rip) */
	0x48, 0x8d, 0x3d, 0x63, 0x35, 0x00, 0x00, //0x00000216 leaq         $13667(%rip), %rdi  /* _VecDecodeCharsetURL+0(%rip) */
	0x41, 0xf6, 0xc2, 0x01, //0x0000021d testb        $1, %r10b
	0x48, 0x0f, 0x44, 0xfb, //0x00000221 cmoveq       %rbx, %rdi
	0x48, 0x89, 0x7d, 0xd0, //0x00000225 movq         %rdi, $-48(%rbp)
	0x0f, 0x84, 0x47, 0x00, 0x00, 0x00, //0x00000229 je           LBB0_7
	0xc5, 0xfe, 0x6f, 0x05, 0xe9, 0xfd, 0xff, 0xff, //0x0000022f vmovdqu      $-535(%rip), %ymm0  /* LCPI0_1+0(%rip) */
	0x0f, 0x85, 0x47, 0x00, 0x00, 0x00, //0x00000237 jne          LBB0_8
	//0x0000023d LBB0_3
	0xc5, 0xfe, 0x6f, 0x0d, 0xfb, 0xfd, 0xff, 0xff, //0x0000023d vmovdqu      $-517(%rip), %ymm1  /* LCPI0_2+0(%rip) */
	0x0f, 0x85, 0x47, 0x00, 0x00, 0x00, //0x00000245 jne          LBB0_9
	//0x0000024b LBB0_4
	0xc5, 0xfe, 0x6f, 0x15, 0x2d, 0xfe, 0xff, 0xff, //0x0000024b vmovdqu      $-467(%rip), %ymm2  /* LCPI0_4+0(%rip) */
	0x4c, 0x8d, 0x74, 0x16, 0xe0, //0x00000253 leaq         $-32(%rsi,%rdx), %r14
	0x48, 0x89, 0x55, 0xa0, //0x00000258 movq         %rdx, $-96(%rbp)
	0x0f, 0x85, 0x47, 0x00, 0x00, 0x00, //0x0000025c jne          LBB0_10
	//0x00000262 LBB0_5
	0xc5, 0xfe, 0x6f, 0x1d, 0x56, 0xfe, 0xff, 0xff, //0x00000262 vmovdqu      $-426(%rip), %ymm3  /* LCPI0_6+0(%rip) */
	0xe9, 0x42, 0x00, 0x00, 0x00, //0x0000026a jmp          LBB0_11
	//0x0000026f LBB0_6
	0x31, 0xc0, //0x0000026f xorl         %eax, %eax
	0xe9, 0xf4, 0x33, 0x00, 0x00, //0x00000271 jmp          LBB0_865
	//0x00000276 LBB0_7
	0xc5, 0xfe, 0x6f, 0x05, 0x82, 0xfd, 0xff, 0xff, //0x00000276 vmovdqu      $-638(%rip), %ymm0  /* LCPI0_0+0(%rip) */
	0x0f, 0x84, 0xb9, 0xff, 0xff, 0xff, //0x0000027e je           LBB0_3
	//0x00000284 LBB0_8
	0xc5, 0xfe, 0x6f, 0x0d, 0xd4, 0xfd, 0xff, 0xff, //0x00000284 vmovdqu      $-556(%rip), %ymm1  /* LCPI0_3+0(%rip) */
	0x0f, 0x84, 0xb9, 0xff, 0xff, 0xff, //0x0000028c je           LBB0_4
	//0x00000292 LBB0_9
	0xc5, 0xfe, 0x6f, 0x15, 0x06, 0xfe, 0xff, 0xff, //0x00000292 vmovdqu      $-506(%rip), %ymm2  /* LCPI0_5+0(%rip) */
	0x4c, 0x8d, 0x74, 0x16, 0xe0, //0x0000029a leaq         $-32(%rsi,%rdx), %r14
	0x48, 0x89, 0x55, 0xa0, //0x0000029f movq         %rdx, $-96(%rbp)
	0x0f, 0x84, 0xb9, 0xff, 0xff, 0xff, //0x000002a3 je           LBB0_5
	//0x000002a9 LBB0_10
	0xc5, 0xfe, 0x6f, 0x1d, 0x2f, 0xfe, 0xff, 0xff, //0x000002a9 vmovdqu      $-465(%rip), %ymm3  /* LCPI0_7+0(%rip) */
	//0x000002b1 LBB0_11
	0x48, 0x8b, 0x7d, 0x98, //0x000002b1 movq         $-104(%rbp), %rdi
	0x49, 0x89, 0xf3, //0x000002b5 movq         %rsi, %r11
	0x48, 0x89, 0xf2, //0x000002b8 movq         %rsi, %rdx
	0x48, 0x89, 0x75, 0xb0, //0x000002bb movq         %rsi, $-80(%rbp)
	0x49, 0x39, 0xf6, //0x000002bf cmpq         %rsi, %r14
	0x48, 0x89, 0xfa, //0x000002c2 movq         %rdi, %rdx
	0x44, 0x89, 0x55, 0xcc, //0x000002c5 movl         %r10d, $-52(%rbp)
	0x0f, 0x82, 0x6e, 0x0d, 0x00, 0x00, //0x000002c9 jb           LBB0_227
	0x4c, 0x8d, 0x3c, 0x08, //0x000002cf leaq         (%rax,%rcx), %r15
	0x49, 0x83, 0xc7, 0xe0, //0x000002d3 addq         $-32, %r15
	0x48, 0x8b, 0x45, 0x98, //0x000002d7 movq         $-104(%rbp), %rax
	0x48, 0x89, 0xc2, //0x000002db movq         %rax, %rdx
	0x4c, 0x8b, 0x5d, 0xb0, //0x000002de movq         $-80(%rbp), %r11
	0x4c, 0x39, 0xf8, //0x000002e2 cmpq         %r15, %rax
	0x0f, 0x87, 0x52, 0x0d, 0x00, 0x00, //0x000002e5 ja           LBB0_227
	0x4c, 0x8b, 0x5d, 0xb0, //0x000002eb movq         $-80(%rbp), %r11
	0x48, 0x8b, 0x45, 0xa0, //0x000002ef movq         $-96(%rbp), %rax
	0x4c, 0x01, 0xd8, //0x000002f3 addq         %r11, %rax
	0x48, 0x83, 0xc0, 0xff, //0x000002f6 addq         $-1, %rax
	0x48, 0x89, 0x45, 0xa8, //0x000002fa movq         %rax, $-88(%rbp)
	0xc5, 0xfe, 0x6f, 0x25, 0xfa, 0xfd, 0xff, 0xff, //0x000002fe vmovdqu      $-518(%rip), %ymm4  /* LCPI0_8+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x2d, 0x12, 0xfe, 0xff, 0xff, //0x00000306 vmovdqu      $-494(%rip), %ymm5  /* LCPI0_9+0(%rip) */
	0xc5, 0xc9, 0xef, 0xf6, //0x0000030e vpxor        %xmm6, %xmm6, %xmm6
	0x49, 0xb8, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, //0x00000312 movabsq      $-4294967296, %r8
	0xc5, 0x7e, 0x6f, 0x05, 0x3c, 0xfe, 0xff, 0xff, //0x0000031c vmovdqu      $-452(%rip), %ymm8  /* LCPI0_11+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x0d, 0x54, 0xfe, 0xff, 0xff, //0x00000324 vmovdqu      $-428(%rip), %ymm9  /* LCPI0_12+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x15, 0x8c, 0xfe, 0xff, 0xff, //0x0000032c vmovdqu      $-372(%rip), %xmm10  /* LCPI0_13+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0x64, 0xfe, 0xff, 0xff, //0x00000334 vmovdqu      $-412(%rip), %ymm11  /* LCPI0_14+0(%rip) */
	0x48, 0x8b, 0x55, 0x98, //0x0000033c movq         $-104(%rbp), %rdx
	//0x00000340 .p2align 4, 0x90
	//0x00000340 LBB0_14
	0xc4, 0x41, 0x7e, 0x6f, 0x23, //0x00000340 vmovdqu      (%r11), %ymm12
	0xc4, 0xc1, 0x15, 0x72, 0xd4, 0x04, //0x00000345 vpsrld       $4, %ymm12, %ymm13
	0xc5, 0x15, 0xdb, 0xec, //0x0000034b vpand        %ymm4, %ymm13, %ymm13
	0xc5, 0x1d, 0xdb, 0xf4, //0x0000034f vpand        %ymm4, %ymm12, %ymm14
	0xc4, 0x42, 0x7d, 0x00, 0xf6, //0x00000353 vpshufb      %ymm14, %ymm0, %ymm14
	0xc4, 0x42, 0x55, 0x00, 0xfd, //0x00000358 vpshufb      %ymm13, %ymm5, %ymm15
	0xc4, 0x41, 0x05, 0xdb, 0xf6, //0x0000035d vpand        %ymm14, %ymm15, %ymm14
	0xc5, 0x0d, 0x74, 0xf6, //0x00000362 vpcmpeqb     %ymm6, %ymm14, %ymm14
	0xc4, 0xc1, 0x7d, 0xd7, 0xc6, //0x00000366 vpmovmskb    %ymm14, %eax
	0x4c, 0x09, 0xc0, //0x0000036b orq          %r8, %rax
	0x48, 0x0f, 0xbc, 0xc0, //0x0000036e bsfq         %rax, %rax
	0x83, 0xf8, 0x1f, //0x00000372 cmpl         $31, %eax
	0x0f, 0x87, 0x85, 0x00, 0x00, 0x00, //0x00000375 ja           LBB0_22
	0x4d, 0x39, 0xcb, //0x0000037b cmpq         %r9, %r11
	0x0f, 0x83, 0xca, 0x00, 0x00, 0x00, //0x0000037e jae          LBB0_23
	0x48, 0x89, 0x55, 0xc0, //0x00000384 movq         %rdx, $-64(%rbp)
	0x4d, 0x89, 0xdc, //0x00000388 movq         %r11, %r12
	0x41, 0xf6, 0xc2, 0x08, //0x0000038b testb        $8, %r10b
	0x0f, 0x85, 0x47, 0x01, 0x00, 0x00, //0x0000038f jne          LBB0_32
	0xe9, 0x13, 0x00, 0x00, 0x00, //0x00000395 jmp          LBB0_18
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x0000039a .p2align 4, 0x90
	//0x000003a0 LBB0_17
	0x49, 0x83, 0xc4, 0x01, //0x000003a0 addq         $1, %r12
	0x4d, 0x39, 0xcc, //0x000003a4 cmpq         %r9, %r12
	0x0f, 0x83, 0x5d, 0x02, 0x00, 0x00, //0x000003a7 jae          LBB0_48
	//0x000003ad LBB0_18
	0x41, 0x0f, 0xb6, 0x34, 0x24, //0x000003ad movzbl       (%r12), %esi
	0x48, 0x83, 0xfe, 0x0d, //0x000003b2 cmpq         $13, %rsi
	0x0f, 0x84, 0xe4, 0xff, 0xff, 0xff, //0x000003b6 je           LBB0_17
	0x40, 0x80, 0xfe, 0x0a, //0x000003bc cmpb         $10, %sil
	0x0f, 0x84, 0xda, 0xff, 0xff, 0xff, //0x000003c0 je           LBB0_17
	0x48, 0x8b, 0x45, 0xd0, //0x000003c6 movq         $-48(%rbp), %rax
	0x44, 0x0f, 0xb6, 0x2c, 0x30, //0x000003ca movzbl       (%rax,%rsi), %r13d
	0x49, 0x83, 0xc4, 0x01, //0x000003cf addq         $1, %r12
	0x41, 0x81, 0xfd, 0xff, 0x00, 0x00, 0x00, //0x000003d3 cmpl         $255, %r13d
	0x0f, 0x84, 0x5f, 0x03, 0x00, 0x00, //0x000003da je           LBB0_72
	0xbf, 0x01, 0x00, 0x00, 0x00, //0x000003e0 movl         $1, %edi
	0x4d, 0x39, 0xcc, //0x000003e5 cmpq         %r9, %r12
	0x0f, 0x82, 0x8f, 0x00, 0x00, 0x00, //0x000003e8 jb           LBB0_26
	0xe9, 0xd8, 0x02, 0x00, 0x00, //0x000003ee jmp          LBB0_62
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000003f3 .p2align 4, 0x90
	//0x00000400 LBB0_22
	0xc5, 0x1d, 0x74, 0xf1, //0x00000400 vpcmpeqb     %ymm1, %ymm12, %ymm14
	0xc4, 0x42, 0x65, 0x00, 0xed, //0x00000404 vpshufb      %ymm13, %ymm3, %ymm13
	0xc4, 0x63, 0x15, 0x4c, 0xea, 0xe0, //0x00000409 vpblendvb    %ymm14, %ymm2, %ymm13, %ymm13
	0xc4, 0x41, 0x15, 0xfc, 0xe4, //0x0000040f vpaddb       %ymm12, %ymm13, %ymm12
	0xc5, 0x1d, 0xdb, 0x25, 0x24, 0xfd, 0xff, 0xff, //0x00000414 vpand        $-732(%rip), %ymm12, %ymm12  /* LCPI0_10+0(%rip) */
	0xc4, 0x42, 0x1d, 0x04, 0xe0, //0x0000041c vpmaddubsw   %ymm8, %ymm12, %ymm12
	0xc4, 0x41, 0x1d, 0xf5, 0xe1, //0x00000421 vpmaddwd     %ymm9, %ymm12, %ymm12
	0xc4, 0x63, 0x7d, 0x39, 0xe7, 0x01, //0x00000426 vextracti128 $1, %ymm12, %xmm7
	0xc4, 0xc2, 0x41, 0x00, 0xfa, //0x0000042c vpshufb      %xmm10, %xmm7, %xmm7
	0xc4, 0x42, 0x1d, 0x00, 0xe3, //0x00000431 vpshufb      %ymm11, %ymm12, %ymm12
	0xc4, 0xe3, 0x1d, 0x02, 0xff, 0x08, //0x00000436 vpblendd     $8, %ymm7, %ymm12, %ymm7
	0xc4, 0xe3, 0x45, 0x02, 0xfe, 0xc0, //0x0000043c vpblendd     $192, %ymm6, %ymm7, %ymm7
	0xc5, 0xfe, 0x7f, 0x3a, //0x00000442 vmovdqu      %ymm7, (%rdx)
	0x49, 0x83, 0xc3, 0x20, //0x00000446 addq         $32, %r11
	0x48, 0x83, 0xc2, 0x18, //0x0000044a addq         $24, %rdx
	//0x0000044e LBB0_23
	0x4d, 0x39, 0xf3, //0x0000044e cmpq         %r14, %r11
	0x0f, 0x87, 0xe6, 0x0b, 0x00, 0x00, //0x00000451 ja           LBB0_227
	0x4c, 0x39, 0xfa, //0x00000457 cmpq         %r15, %rdx
	0x0f, 0x86, 0xe0, 0xfe, 0xff, 0xff, //0x0000045a jbe          LBB0_14
	0xe9, 0xd8, 0x0b, 0x00, 0x00, //0x00000460 jmp          LBB0_227
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000465 .p2align 4, 0x90
	//0x00000470 LBB0_25
	0x49, 0x83, 0xc4, 0x01, //0x00000470 addq         $1, %r12
	0x4d, 0x39, 0xcc, //0x00000474 cmpq         %r9, %r12
	0x0f, 0x83, 0xbd, 0x04, 0x00, 0x00, //0x00000477 jae          LBB0_105
	//0x0000047d LBB0_26
	0x41, 0x0f, 0xb6, 0x34, 0x24, //0x0000047d movzbl       (%r12), %esi
	0x48, 0x83, 0xfe, 0x0d, //0x00000482 cmpq         $13, %rsi
	0x0f, 0x84, 0xe4, 0xff, 0xff, 0xff, //0x00000486 je           LBB0_25
	0x40, 0x80, 0xfe, 0x0a, //0x0000048c cmpb         $10, %sil
	0x0f, 0x84, 0xda, 0xff, 0xff, 0xff, //0x00000490 je           LBB0_25
	0x48, 0x8b, 0x45, 0xd0, //0x00000496 movq         $-48(%rbp), %rax
	0x0f, 0xb6, 0x04, 0x30, //0x0000049a movzbl       (%rax,%rsi), %eax
	0x49, 0x83, 0xc4, 0x01, //0x0000049e addq         $1, %r12
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x000004a2 cmpl         $255, %eax
	0x0f, 0x84, 0x2d, 0x08, 0x00, 0x00, //0x000004a7 je           LBB0_158
	0x41, 0xc1, 0xe5, 0x06, //0x000004ad shll         $6, %r13d
	0x41, 0x09, 0xc5, //0x000004b1 orl          %eax, %r13d
	0xbf, 0x02, 0x00, 0x00, 0x00, //0x000004b4 movl         $2, %edi
	0x4d, 0x39, 0xcc, //0x000004b9 cmpq         %r9, %r12
	0x0f, 0x82, 0x80, 0x01, 0x00, 0x00, //0x000004bc jb           LBB0_52
	0xe9, 0x04, 0x02, 0x00, 0x00, //0x000004c2 jmp          LBB0_62
	//0x000004c7 LBB0_46
	0x80, 0xf9, 0x6e, //0x000004c7 cmpb         $110, %cl
	0x0f, 0x85, 0xc4, 0x01, 0x00, 0x00, //0x000004ca jne          LBB0_57
	//0x000004d0 LBB0_30
	0x49, 0x89, 0xc4, //0x000004d0 movq         %rax, %r12
	//0x000004d3 LBB0_31
	0x4d, 0x39, 0xcc, //0x000004d3 cmpq         %r9, %r12
	0x0f, 0x83, 0x2e, 0x01, 0x00, 0x00, //0x000004d6 jae          LBB0_48
	//0x000004dc LBB0_32
	0x49, 0x8d, 0x4c, 0x24, 0x01, //0x000004dc leaq         $1(%r12), %rcx
	0x41, 0x0f, 0xb6, 0x04, 0x24, //0x000004e1 movzbl       (%r12), %eax
	0x3c, 0x5c, //0x000004e6 cmpb         $92, %al
	0x0f, 0x85, 0x02, 0x01, 0x00, 0x00, //0x000004e8 jne          LBB0_44
	0x49, 0x8d, 0x44, 0x24, 0x02, //0x000004ee leaq         $2(%r12), %rax
	0x40, 0xb6, 0xff, //0x000004f3 movb         $-1, %sil
	0x4c, 0x39, 0xc8, //0x000004f6 cmpq         %r9, %rax
	0x0f, 0x87, 0x8d, 0x01, 0x00, 0x00, //0x000004f9 ja           LBB0_56
	0x0f, 0xb6, 0x09, //0x000004ff movzbl       (%rcx), %ecx
	0x80, 0xf9, 0x71, //0x00000502 cmpb         $113, %cl
	0x0f, 0x8e, 0xbc, 0xff, 0xff, 0xff, //0x00000505 jle          LBB0_46
	0x80, 0xf9, 0x72, //0x0000050b cmpb         $114, %cl
	0x0f, 0x84, 0xbc, 0xff, 0xff, 0xff, //0x0000050e je           LBB0_30
	0x80, 0xf9, 0x75, //0x00000514 cmpb         $117, %cl
	0x0f, 0x85, 0x83, 0x01, 0x00, 0x00, //0x00000517 jne          LBB0_59
	0x4c, 0x89, 0xc9, //0x0000051d movq         %r9, %rcx
	0x48, 0x29, 0xc1, //0x00000520 subq         %rax, %rcx
	0x48, 0x83, 0xf9, 0x04, //0x00000523 cmpq         $4, %rcx
	0x0f, 0x8c, 0x73, 0x01, 0x00, 0x00, //0x00000527 jl           LBB0_59
	0x8b, 0x08, //0x0000052d movl         (%rax), %ecx
	0x89, 0xca, //0x0000052f movl         %ecx, %edx
	0xf7, 0xd2, //0x00000531 notl         %edx
	0x8d, 0xb9, 0xd0, 0xcf, 0xcf, 0xcf, //0x00000533 leal         $-808464432(%rcx), %edi
	0x81, 0xe2, 0x80, 0x80, 0x80, 0x80, //0x00000539 andl         $-2139062144, %edx
	0x85, 0xfa, //0x0000053f testl        %edi, %edx
	0x0f, 0x85, 0x59, 0x01, 0x00, 0x00, //0x00000541 jne          LBB0_59
	0x8d, 0xb9, 0x19, 0x19, 0x19, 0x19, //0x00000547 leal         $421075225(%rcx), %edi
	0x09, 0xcf, //0x0000054d orl          %ecx, %edi
	0xf7, 0xc7, 0x80, 0x80, 0x80, 0x80, //0x0000054f testl        $-2139062144, %edi
	0x0f, 0x85, 0x45, 0x01, 0x00, 0x00, //0x00000555 jne          LBB0_59
	0x89, 0xcf, //0x0000055b movl         %ecx, %edi
	0x81, 0xe7, 0x7f, 0x7f, 0x7f, 0x7f, //0x0000055d andl         $2139062143, %edi
	0xbb, 0xc0, 0xc0, 0xc0, 0xc0, //0x00000563 movl         $-1061109568, %ebx
	0x29, 0xfb, //0x00000568 subl         %edi, %ebx
	0x44, 0x8d, 0xaf, 0x46, 0x46, 0x46, 0x46, //0x0000056a leal         $1179010630(%rdi), %r13d
	0x21, 0xd3, //0x00000571 andl         %edx, %ebx
	0x44, 0x85, 0xeb, //0x00000573 testl        %r13d, %ebx
	0x0f, 0x85, 0x2f, 0x05, 0x00, 0x00, //0x00000576 jne          LBB0_125
	0xbb, 0xe0, 0xe0, 0xe0, 0xe0, //0x0000057c movl         $-522133280, %ebx
	0x29, 0xfb, //0x00000581 subl         %edi, %ebx
	0x81, 0xc7, 0x39, 0x39, 0x39, 0x39, //0x00000583 addl         $960051513, %edi
	0x21, 0xda, //0x00000589 andl         %ebx, %edx
	0x85, 0xfa, //0x0000058b testl        %edi, %edx
	0x44, 0x8b, 0x55, 0xcc, //0x0000058d movl         $-52(%rbp), %r10d
	0x0f, 0x85, 0x09, 0x01, 0x00, 0x00, //0x00000591 jne          LBB0_59
	0x0f, 0xc9, //0x00000597 bswapl       %ecx
	0x89, 0xc8, //0x00000599 movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x0000059b shrl         $4, %eax
	0xf7, 0xd0, //0x0000059e notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x000005a0 andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x000005a5 leal         (%rax,%rax,8), %eax
	0x81, 0xe1, 0x0f, 0x0f, 0x0f, 0x0f, //0x000005a8 andl         $252645135, %ecx
	0x01, 0xc1, //0x000005ae addl         %eax, %ecx
	0x89, 0xc8, //0x000005b0 movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x000005b2 shrl         $4, %eax
	0x09, 0xc8, //0x000005b5 orl          %ecx, %eax
	0x89, 0xc1, //0x000005b7 movl         %eax, %ecx
	0xc1, 0xe9, 0x08, //0x000005b9 shrl         $8, %ecx
	0x81, 0xe1, 0x00, 0xff, 0x00, 0x00, //0x000005bc andl         $65280, %ecx
	0x89, 0xc2, //0x000005c2 movl         %eax, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x000005c4 andl         $128, %edx
	0x49, 0x83, 0xc4, 0x06, //0x000005ca addq         $6, %r12
	0x09, 0xca, //0x000005ce orl          %ecx, %edx
	0x0f, 0x85, 0xcd, 0x00, 0x00, 0x00, //0x000005d0 jne          LBB0_60
	0x3c, 0x0d, //0x000005d6 cmpb         $13, %al
	0x0f, 0x85, 0x1d, 0x00, 0x00, 0x00, //0x000005d8 jne          LBB0_45
	0xe9, 0xf0, 0xfe, 0xff, 0xff, //0x000005de jmp          LBB0_31
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000005e3 .p2align 4, 0x90
	//0x000005f0 LBB0_44
	0x49, 0x89, 0xcc, //0x000005f0 movq         %rcx, %r12
	0x3c, 0x0d, //0x000005f3 cmpb         $13, %al
	0x0f, 0x84, 0xd8, 0xfe, 0xff, 0xff, //0x000005f5 je           LBB0_31
	//0x000005fb LBB0_45
	0x89, 0xc6, //0x000005fb movl         %eax, %esi
	0x3c, 0x0a, //0x000005fd cmpb         $10, %al
	0x0f, 0x84, 0xce, 0xfe, 0xff, 0xff, //0x000005ff je           LBB0_31
	0xe9, 0x99, 0x00, 0x00, 0x00, //0x00000605 jmp          LBB0_60
	//0x0000060a LBB0_48
	0x31, 0xff, //0x0000060a xorl         %edi, %edi
	0x45, 0x31, 0xed, //0x0000060c xorl         %r13d, %r13d
	//0x0000060f LBB0_49
	0x85, 0xff, //0x0000060f testl        %edi, %edi
	0x48, 0x8b, 0x55, 0xc0, //0x00000611 movq         $-64(%rbp), %rdx
	0x0f, 0x85, 0xb0, 0x00, 0x00, 0x00, //0x00000615 jne          LBB0_62
	0x4d, 0x89, 0xe3, //0x0000061b movq         %r12, %r11
	0xe9, 0x2b, 0xfe, 0xff, 0xff, //0x0000061e jmp          LBB0_23
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000623 .p2align 4, 0x90
	//0x00000630 LBB0_51
	0x49, 0x83, 0xc4, 0x01, //0x00000630 addq         $1, %r12
	0xbf, 0x02, 0x00, 0x00, 0x00, //0x00000634 movl         $2, %edi
	0x4d, 0x39, 0xcc, //0x00000639 cmpq         %r9, %r12
	0x0f, 0x83, 0xcd, 0xff, 0xff, 0xff, //0x0000063c jae          LBB0_49
	//0x00000642 LBB0_52
	0x41, 0x0f, 0xb6, 0x34, 0x24, //0x00000642 movzbl       (%r12), %esi
	0x48, 0x83, 0xfe, 0x0d, //0x00000647 cmpq         $13, %rsi
	0x0f, 0x84, 0xdf, 0xff, 0xff, 0xff, //0x0000064b je           LBB0_51
	0x40, 0x80, 0xfe, 0x0a, //0x00000651 cmpb         $10, %sil
	0x0f, 0x84, 0xd5, 0xff, 0xff, 0xff, //0x00000655 je           LBB0_51
	0x48, 0x8b, 0x45, 0xd0, //0x0000065b movq         $-48(%rbp), %rax
	0x0f, 0xb6, 0x04, 0x30, //0x0000065f movzbl       (%rax,%rsi), %eax
	0x49, 0x83, 0xc4, 0x01, //0x00000663 addq         $1, %r12
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00000667 cmpl         $255, %eax
	0x0f, 0x84, 0x3e, 0x09, 0x00, 0x00, //0x0000066c je           LBB0_191
	0x41, 0xc1, 0xe5, 0x06, //0x00000672 shll         $6, %r13d
	0x41, 0x09, 0xc5, //0x00000676 orl          %eax, %r13d
	0xbf, 0x03, 0x00, 0x00, 0x00, //0x00000679 movl         $3, %edi
	0x4d, 0x39, 0xcc, //0x0000067e cmpq         %r9, %r12
	0x0f, 0x82, 0x91, 0x04, 0x00, 0x00, //0x00000681 jb           LBB0_131
	0xe9, 0x3f, 0x00, 0x00, 0x00, //0x00000687 jmp          LBB0_62
	//0x0000068c LBB0_56
	0x49, 0x89, 0xcc, //0x0000068c movq         %rcx, %r12
	0xe9, 0x0f, 0x00, 0x00, 0x00, //0x0000068f jmp          LBB0_60
	//0x00000694 LBB0_57
	0x80, 0xf9, 0x2f, //0x00000694 cmpb         $47, %cl
	0x0f, 0x85, 0x03, 0x00, 0x00, 0x00, //0x00000697 jne          LBB0_59
	0x40, 0xb6, 0x2f, //0x0000069d movb         $47, %sil
	//0x000006a0 LBB0_59
	0x49, 0x89, 0xc4, //0x000006a0 movq         %rax, %r12
	//0x000006a3 LBB0_60
	0x40, 0x0f, 0xb6, 0xc6, //0x000006a3 movzbl       %sil, %eax
	0x48, 0x8b, 0x4d, 0xd0, //0x000006a7 movq         $-48(%rbp), %rcx
	0x44, 0x0f, 0xb6, 0x2c, 0x01, //0x000006ab movzbl       (%rcx,%rax), %r13d
	0x41, 0x81, 0xfd, 0xff, 0x00, 0x00, 0x00, //0x000006b0 cmpl         $255, %r13d
	0x0f, 0x84, 0x82, 0x00, 0x00, 0x00, //0x000006b7 je           LBB0_72
	0xbf, 0x01, 0x00, 0x00, 0x00, //0x000006bd movl         $1, %edi
	0x4d, 0x39, 0xcc, //0x000006c2 cmpq         %r9, %r12
	0x0f, 0x82, 0x39, 0x01, 0x00, 0x00, //0x000006c5 jb           LBB0_88
	//0x000006cb LBB0_62
	0x41, 0xf6, 0xc2, 0x02, //0x000006cb testb        $2, %r10b
	0x0f, 0x94, 0xc0, //0x000006cf sete         %al
	0x83, 0xff, 0x01, //0x000006d2 cmpl         $1, %edi
	0x0f, 0x94, 0xc1, //0x000006d5 sete         %cl
	0x4d, 0x39, 0xcc, //0x000006d8 cmpq         %r9, %r12
	0x0f, 0x82, 0x11, 0x00, 0x00, 0x00, //0x000006db jb           LBB0_65
	0x83, 0xff, 0x04, //0x000006e1 cmpl         $4, %edi
	0x0f, 0x84, 0x08, 0x00, 0x00, 0x00, //0x000006e4 je           LBB0_65
	0x08, 0xc8, //0x000006ea orb          %cl, %al
	0x0f, 0x85, 0xef, 0x03, 0x00, 0x00, //0x000006ec jne          LBB0_167
	//0x000006f2 LBB0_65
	0xb0, 0x04, //0x000006f2 movb         $4, %al
	0x40, 0x28, 0xf8, //0x000006f4 subb         %dil, %al
	0x0f, 0xb6, 0xc0, //0x000006f7 movzbl       %al, %eax
	0x01, 0xc0, //0x000006fa addl         %eax, %eax
	0x8d, 0x0c, 0x40, //0x000006fc leal         (%rax,%rax,2), %ecx
	0x44, 0x89, 0xe8, //0x000006ff movl         %r13d, %eax
	0xd3, 0xe0, //0x00000702 shll         %cl, %eax
	0x83, 0xff, 0x02, //0x00000704 cmpl         $2, %edi
	0x48, 0x8b, 0x55, 0xc0, //0x00000707 movq         $-64(%rbp), %rdx
	0x0f, 0x84, 0x18, 0x00, 0x00, 0x00, //0x0000070b je           LBB0_70
	0x83, 0xff, 0x03, //0x00000711 cmpl         $3, %edi
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x00000714 je           LBB0_69
	0x83, 0xff, 0x04, //0x0000071a cmpl         $4, %edi
	0x0f, 0x85, 0x0b, 0x00, 0x00, 0x00, //0x0000071d jne          LBB0_71
	0x88, 0x42, 0x02, //0x00000723 movb         %al, $2(%rdx)
	//0x00000726 LBB0_69
	0x88, 0x62, 0x01, //0x00000726 movb         %ah, $1(%rdx)
	//0x00000729 LBB0_70
	0xc1, 0xe8, 0x10, //0x00000729 shrl         $16, %eax
	0x88, 0x02, //0x0000072c movb         %al, (%rdx)
	//0x0000072e LBB0_71
	0x89, 0xf8, //0x0000072e movl         %edi, %eax
	0x48, 0x01, 0xc2, //0x00000730 addq         %rax, %rdx
	0x48, 0x83, 0xc2, 0xff, //0x00000733 addq         $-1, %rdx
	0x4d, 0x89, 0xe3, //0x00000737 movq         %r12, %r11
	0xe9, 0x0f, 0xfd, 0xff, 0xff, //0x0000073a jmp          LBB0_23
	//0x0000073f LBB0_72
	0x31, 0xff, //0x0000073f xorl         %edi, %edi
	0x45, 0x31, 0xed, //0x00000741 xorl         %r13d, %r13d
	//0x00000744 LBB0_73
	0x41, 0xf6, 0xc2, 0x02, //0x00000744 testb        $2, %r10b
	0x0f, 0x85, 0x93, 0x03, 0x00, 0x00, //0x00000748 jne          LBB0_167
	0x40, 0x80, 0xfe, 0x3d, //0x0000074e cmpb         $61, %sil
	0x0f, 0x85, 0x89, 0x03, 0x00, 0x00, //0x00000752 jne          LBB0_167
	0x83, 0xff, 0x02, //0x00000758 cmpl         $2, %edi
	0x0f, 0x82, 0x80, 0x03, 0x00, 0x00, //0x0000075b jb           LBB0_167
	0xba, 0x05, 0x00, 0x00, 0x00, //0x00000761 movl         $5, %edx
	0x29, 0xfa, //0x00000766 subl         %edi, %edx
	0x41, 0xf6, 0xc2, 0x08, //0x00000768 testb        $8, %r10b
	0x0f, 0x85, 0xd2, 0x01, 0x00, 0x00, //0x0000076c jne          LBB0_106
	0x4d, 0x39, 0xcc, //0x00000772 cmpq         %r9, %r12
	0x0f, 0x83, 0x77, 0xff, 0xff, 0xff, //0x00000775 jae          LBB0_65
	0x49, 0x8d, 0x4c, 0x24, 0x01, //0x0000077b leaq         $1(%r12), %rcx
	0x48, 0x8b, 0x45, 0xa8, //0x00000780 movq         $-88(%rbp), %rax
	0x4c, 0x29, 0xe0, //0x00000784 subq         %r12, %rax
	0x49, 0x83, 0xc4, 0x02, //0x00000787 addq         $2, %r12
	0x4c, 0x89, 0xe6, //0x0000078b movq         %r12, %rsi
	0x49, 0x89, 0xcc, //0x0000078e movq         %rcx, %r12
	0xe9, 0x1c, 0x00, 0x00, 0x00, //0x00000791 jmp          LBB0_80
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000796 .p2align 4, 0x90
	//0x000007a0 LBB0_79
	0x49, 0x83, 0xc4, 0x01, //0x000007a0 addq         $1, %r12
	0x48, 0x83, 0xc6, 0x01, //0x000007a4 addq         $1, %rsi
	0x48, 0x83, 0xc0, 0xff, //0x000007a8 addq         $-1, %rax
	0x0f, 0x83, 0x32, 0x05, 0x00, 0x00, //0x000007ac jae          LBB0_159
	//0x000007b2 LBB0_80
	0x41, 0x0f, 0xb6, 0x4c, 0x24, 0xff, //0x000007b2 movzbl       $-1(%r12), %ecx
	0x80, 0xf9, 0x0a, //0x000007b8 cmpb         $10, %cl
	0x0f, 0x84, 0xdf, 0xff, 0xff, 0xff, //0x000007bb je           LBB0_79
	0x80, 0xf9, 0x0d, //0x000007c1 cmpb         $13, %cl
	0x0f, 0x84, 0xd6, 0xff, 0xff, 0xff, //0x000007c4 je           LBB0_79
	0x80, 0xf9, 0x3d, //0x000007ca cmpb         $61, %cl
	0x0f, 0x85, 0x0e, 0x03, 0x00, 0x00, //0x000007cd jne          LBB0_167
	0x83, 0xfa, 0x02, //0x000007d3 cmpl         $2, %edx
	0x0f, 0x84, 0x05, 0x03, 0x00, 0x00, //0x000007d6 je           LBB0_167
	0x4d, 0x39, 0xcc, //0x000007dc cmpq         %r9, %r12
	0x0f, 0x83, 0x0d, 0xff, 0xff, 0xff, //0x000007df jae          LBB0_65
	0x49, 0x01, 0xc4, //0x000007e5 addq         %rax, %r12
	0x31, 0xc9, //0x000007e8 xorl         %ecx, %ecx
	0xe9, 0xd9, 0x02, 0x00, 0x00, //0x000007ea jmp          LBB0_127
	//0x000007ef LBB0_102
	0x80, 0xf9, 0x6e, //0x000007ef cmpb         $110, %cl
	0x0f, 0x85, 0x5a, 0x03, 0x00, 0x00, //0x000007f2 jne          LBB0_135
	//0x000007f8 LBB0_86
	0x49, 0x89, 0xc4, //0x000007f8 movq         %rax, %r12
	//0x000007fb LBB0_87
	0x4d, 0x39, 0xcc, //0x000007fb cmpq         %r9, %r12
	0x0f, 0x83, 0x36, 0x01, 0x00, 0x00, //0x000007fe jae          LBB0_105
	//0x00000804 LBB0_88
	0x49, 0x8d, 0x4c, 0x24, 0x01, //0x00000804 leaq         $1(%r12), %rcx
	0x41, 0x0f, 0xb6, 0x04, 0x24, //0x00000809 movzbl       (%r12), %eax
	0x3c, 0x5c, //0x0000080e cmpb         $92, %al
	0x0f, 0x85, 0x0a, 0x01, 0x00, 0x00, //0x00000810 jne          LBB0_100
	0x49, 0x8d, 0x44, 0x24, 0x02, //0x00000816 leaq         $2(%r12), %rax
	0x40, 0xb6, 0xff, //0x0000081b movb         $-1, %sil
	0x4c, 0x39, 0xc8, //0x0000081e cmpq         %r9, %rax
	0x0f, 0x87, 0xd7, 0x02, 0x00, 0x00, //0x00000821 ja           LBB0_129
	0x0f, 0xb6, 0x09, //0x00000827 movzbl       (%rcx), %ecx
	0x80, 0xf9, 0x71, //0x0000082a cmpb         $113, %cl
	0x0f, 0x8e, 0xbc, 0xff, 0xff, 0xff, //0x0000082d jle          LBB0_102
	0x80, 0xf9, 0x72, //0x00000833 cmpb         $114, %cl
	0x0f, 0x84, 0xbc, 0xff, 0xff, 0xff, //0x00000836 je           LBB0_86
	0x80, 0xf9, 0x75, //0x0000083c cmpb         $117, %cl
	0x0f, 0x85, 0x19, 0x03, 0x00, 0x00, //0x0000083f jne          LBB0_137
	0x4c, 0x89, 0xc9, //0x00000845 movq         %r9, %rcx
	0x48, 0x29, 0xc1, //0x00000848 subq         %rax, %rcx
	0x48, 0x83, 0xf9, 0x04, //0x0000084b cmpq         $4, %rcx
	0x0f, 0x8c, 0x09, 0x03, 0x00, 0x00, //0x0000084f jl           LBB0_137
	0x8b, 0x08, //0x00000855 movl         (%rax), %ecx
	0x89, 0xcf, //0x00000857 movl         %ecx, %edi
	0xf7, 0xd7, //0x00000859 notl         %edi
	0x8d, 0x91, 0xd0, 0xcf, 0xcf, 0xcf, //0x0000085b leal         $-808464432(%rcx), %edx
	0x81, 0xe7, 0x80, 0x80, 0x80, 0x80, //0x00000861 andl         $-2139062144, %edi
	0x85, 0xd7, //0x00000867 testl        %edx, %edi
	0x0f, 0x85, 0xef, 0x02, 0x00, 0x00, //0x00000869 jne          LBB0_137
	0x8d, 0x91, 0x19, 0x19, 0x19, 0x19, //0x0000086f leal         $421075225(%rcx), %edx
	0x09, 0xca, //0x00000875 orl          %ecx, %edx
	0xf7, 0xc2, 0x80, 0x80, 0x80, 0x80, //0x00000877 testl        $-2139062144, %edx
	0x0f, 0x85, 0xdb, 0x02, 0x00, 0x00, //0x0000087d jne          LBB0_137
	0x89, 0xcb, //0x00000883 movl         %ecx, %ebx
	0x81, 0xe3, 0x7f, 0x7f, 0x7f, 0x7f, //0x00000885 andl         $2139062143, %ebx
	0xba, 0xc0, 0xc0, 0xc0, 0xc0, //0x0000088b movl         $-1061109568, %edx
	0x29, 0xda, //0x00000890 subl         %ebx, %edx
	0x44, 0x8d, 0x83, 0x46, 0x46, 0x46, 0x46, //0x00000892 leal         $1179010630(%rbx), %r8d
	0x21, 0xfa, //0x00000899 andl         %edi, %edx
	0x44, 0x85, 0xc2, //0x0000089b testl        %r8d, %edx
	0x0f, 0x85, 0x48, 0x04, 0x00, 0x00, //0x0000089e jne          LBB0_161
	0xba, 0xe0, 0xe0, 0xe0, 0xe0, //0x000008a4 movl         $-522133280, %edx
	0x29, 0xda, //0x000008a9 subl         %ebx, %edx
	0x81, 0xc3, 0x39, 0x39, 0x39, 0x39, //0x000008ab addl         $960051513, %ebx
	0x21, 0xd7, //0x000008b1 andl         %edx, %edi
	0x85, 0xdf, //0x000008b3 testl        %ebx, %edi
	0x49, 0xb8, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, //0x000008b5 movabsq      $-4294967296, %r8
	0x0f, 0x85, 0x99, 0x02, 0x00, 0x00, //0x000008bf jne          LBB0_137
	0x0f, 0xc9, //0x000008c5 bswapl       %ecx
	0x89, 0xc8, //0x000008c7 movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x000008c9 shrl         $4, %eax
	0xf7, 0xd0, //0x000008cc notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x000008ce andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x000008d3 leal         (%rax,%rax,8), %eax
	0x81, 0xe1, 0x0f, 0x0f, 0x0f, 0x0f, //0x000008d6 andl         $252645135, %ecx
	0x01, 0xc1, //0x000008dc addl         %eax, %ecx
	0x89, 0xc8, //0x000008de movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x000008e0 shrl         $4, %eax
	0x09, 0xc8, //0x000008e3 orl          %ecx, %eax
	0x89, 0xc1, //0x000008e5 movl         %eax, %ecx
	0xc1, 0xe9, 0x08, //0x000008e7 shrl         $8, %ecx
	0x81, 0xe1, 0x00, 0xff, 0x00, 0x00, //0x000008ea andl         $65280, %ecx
	0x89, 0xc2, //0x000008f0 movl         %eax, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x000008f2 andl         $128, %edx
	0x49, 0x83, 0xc4, 0x06, //0x000008f8 addq         $6, %r12
	0x09, 0xca, //0x000008fc orl          %ecx, %edx
	0x0f, 0x85, 0x5d, 0x02, 0x00, 0x00, //0x000008fe jne          LBB0_138
	0x3c, 0x0d, //0x00000904 cmpb         $13, %al
	0x0f, 0x85, 0x1f, 0x00, 0x00, 0x00, //0x00000906 jne          LBB0_101
	0xe9, 0xea, 0xfe, 0xff, 0xff, //0x0000090c jmp          LBB0_87
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000911 .p2align 4, 0x90
	//0x00000920 LBB0_100
	0x49, 0x89, 0xcc, //0x00000920 movq         %rcx, %r12
	0x3c, 0x0d, //0x00000923 cmpb         $13, %al
	0x0f, 0x84, 0xd0, 0xfe, 0xff, 0xff, //0x00000925 je           LBB0_87
	//0x0000092b LBB0_101
	0x89, 0xc6, //0x0000092b movl         %eax, %esi
	0x3c, 0x0a, //0x0000092d cmpb         $10, %al
	0x0f, 0x84, 0xc6, 0xfe, 0xff, 0xff, //0x0000092f je           LBB0_87
	0xe9, 0x27, 0x02, 0x00, 0x00, //0x00000935 jmp          LBB0_138
	//0x0000093a LBB0_105
	0xbf, 0x01, 0x00, 0x00, 0x00, //0x0000093a movl         $1, %edi
	0xe9, 0xcb, 0xfc, 0xff, 0xff, //0x0000093f jmp          LBB0_49
	//0x00000944 LBB0_106
	0x4d, 0x39, 0xcc, //0x00000944 cmpq         %r9, %r12
	0x0f, 0x83, 0xa5, 0xfd, 0xff, 0xff, //0x00000947 jae          LBB0_65
	0x89, 0x55, 0x90, //0x0000094d movl         %edx, $-112(%rbp)
	0x4c, 0x89, 0xe3, //0x00000950 movq         %r12, %rbx
	0xe9, 0x25, 0x00, 0x00, 0x00, //0x00000953 jmp          LBB0_110
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000958 .p2align 4, 0x90
	//0x00000960 LBB0_124
	0x48, 0x89, 0xf3, //0x00000960 movq         %rsi, %rbx
	0x4c, 0x39, 0xcb, //0x00000963 cmpq         %r9, %rbx
	0x0f, 0x82, 0x11, 0x00, 0x00, 0x00, //0x00000966 jb           LBB0_110
	0xe9, 0x8d, 0x03, 0x00, 0x00, //0x0000096c jmp          LBB0_162
	//0x00000971 LBB0_108
	0x4c, 0x89, 0xe3, //0x00000971 movq         %r12, %rbx
	0x4c, 0x39, 0xcb, //0x00000974 cmpq         %r9, %rbx
	0x0f, 0x83, 0x81, 0x03, 0x00, 0x00, //0x00000977 jae          LBB0_162
	//0x0000097d LBB0_110
	0x48, 0x8d, 0x73, 0x01, //0x0000097d leaq         $1(%rbx), %rsi
	0x0f, 0xb6, 0x0b, //0x00000981 movzbl       (%rbx), %ecx
	0x80, 0xf9, 0x5c, //0x00000984 cmpb         $92, %cl
	0x0f, 0x85, 0xe8, 0x00, 0x00, 0x00, //0x00000987 jne          LBB0_121
	0x4c, 0x8d, 0x63, 0x02, //0x0000098d leaq         $2(%rbx), %r12
	0x4d, 0x39, 0xcc, //0x00000991 cmpq         %r9, %r12
	0x0f, 0x87, 0x44, 0x01, 0x00, 0x00, //0x00000994 ja           LBB0_166
	0x0f, 0xb6, 0x06, //0x0000099a movzbl       (%rsi), %eax
	0x3c, 0x6e, //0x0000099d cmpb         $110, %al
	0x0f, 0x84, 0xcc, 0xff, 0xff, 0xff, //0x0000099f je           LBB0_108
	0x3c, 0x72, //0x000009a5 cmpb         $114, %al
	0x0f, 0x84, 0xc4, 0xff, 0xff, 0xff, //0x000009a7 je           LBB0_108
	0x3c, 0x75, //0x000009ad cmpb         $117, %al
	0x0f, 0x85, 0x2c, 0x01, 0x00, 0x00, //0x000009af jne          LBB0_167
	0x4c, 0x89, 0xc8, //0x000009b5 movq         %r9, %rax
	0x4c, 0x29, 0xe0, //0x000009b8 subq         %r12, %rax
	0x48, 0x83, 0xf8, 0x04, //0x000009bb cmpq         $4, %rax
	0x0f, 0x8c, 0x1c, 0x01, 0x00, 0x00, //0x000009bf jl           LBB0_167
	0x41, 0x8b, 0x04, 0x24, //0x000009c5 movl         (%r12), %eax
	0x89, 0xc1, //0x000009c9 movl         %eax, %ecx
	0xf7, 0xd1, //0x000009cb notl         %ecx
	0x8d, 0xb0, 0xd0, 0xcf, 0xcf, 0xcf, //0x000009cd leal         $-808464432(%rax), %esi
	0x81, 0xe1, 0x80, 0x80, 0x80, 0x80, //0x000009d3 andl         $-2139062144, %ecx
	0x85, 0xf1, //0x000009d9 testl        %esi, %ecx
	0x0f, 0x85, 0x00, 0x01, 0x00, 0x00, //0x000009db jne          LBB0_167
	0x8d, 0xb0, 0x19, 0x19, 0x19, 0x19, //0x000009e1 leal         $421075225(%rax), %esi
	0x09, 0xc6, //0x000009e7 orl          %eax, %esi
	0xf7, 0xc6, 0x80, 0x80, 0x80, 0x80, //0x000009e9 testl        $-2139062144, %esi
	0x0f, 0x85, 0xec, 0x00, 0x00, 0x00, //0x000009ef jne          LBB0_167
	0x89, 0xc6, //0x000009f5 movl         %eax, %esi
	0x81, 0xe6, 0x7f, 0x7f, 0x7f, 0x7f, //0x000009f7 andl         $2139062143, %esi
	0xba, 0xc0, 0xc0, 0xc0, 0xc0, //0x000009fd movl         $-1061109568, %edx
	0x29, 0xf2, //0x00000a02 subl         %esi, %edx
	0x44, 0x8d, 0x96, 0x46, 0x46, 0x46, 0x46, //0x00000a04 leal         $1179010630(%rsi), %r10d
	0x21, 0xca, //0x00000a0b andl         %ecx, %edx
	0x44, 0x85, 0xd2, //0x00000a0d testl        %r10d, %edx
	0x0f, 0x85, 0xbe, 0x05, 0x00, 0x00, //0x00000a10 jne          LBB0_215
	0xba, 0xe0, 0xe0, 0xe0, 0xe0, //0x00000a16 movl         $-522133280, %edx
	0x29, 0xf2, //0x00000a1b subl         %esi, %edx
	0x81, 0xc6, 0x39, 0x39, 0x39, 0x39, //0x00000a1d addl         $960051513, %esi
	0x21, 0xd1, //0x00000a23 andl         %edx, %ecx
	0x85, 0xf1, //0x00000a25 testl        %esi, %ecx
	0x44, 0x8b, 0x55, 0xcc, //0x00000a27 movl         $-52(%rbp), %r10d
	0x0f, 0x85, 0xb0, 0x00, 0x00, 0x00, //0x00000a2b jne          LBB0_167
	0x0f, 0xc8, //0x00000a31 bswapl       %eax
	0x89, 0xc1, //0x00000a33 movl         %eax, %ecx
	0xc1, 0xe9, 0x04, //0x00000a35 shrl         $4, %ecx
	0xf7, 0xd1, //0x00000a38 notl         %ecx
	0x81, 0xe1, 0x01, 0x01, 0x01, 0x01, //0x00000a3a andl         $16843009, %ecx
	0x8d, 0x0c, 0xc9, //0x00000a40 leal         (%rcx,%rcx,8), %ecx
	0x25, 0x0f, 0x0f, 0x0f, 0x0f, //0x00000a43 andl         $252645135, %eax
	0x01, 0xc8, //0x00000a48 addl         %ecx, %eax
	0x89, 0xc1, //0x00000a4a movl         %eax, %ecx
	0xc1, 0xe9, 0x04, //0x00000a4c shrl         $4, %ecx
	0x09, 0xc1, //0x00000a4f orl          %eax, %ecx
	0x89, 0xc8, //0x00000a51 movl         %ecx, %eax
	0xc1, 0xe8, 0x08, //0x00000a53 shrl         $8, %eax
	0x25, 0x00, 0xff, 0x00, 0x00, //0x00000a56 andl         $65280, %eax
	0x89, 0xca, //0x00000a5b movl         %ecx, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x00000a5d andl         $128, %edx
	0x48, 0x83, 0xc3, 0x06, //0x00000a63 addq         $6, %rbx
	0x09, 0xc2, //0x00000a67 orl          %eax, %edx
	0x48, 0x89, 0xde, //0x00000a69 movq         %rbx, %rsi
	0x49, 0x89, 0xdc, //0x00000a6c movq         %rbx, %r12
	0x0f, 0x85, 0x6c, 0x00, 0x00, 0x00, //0x00000a6f jne          LBB0_167
	//0x00000a75 LBB0_121
	0x80, 0xf9, 0x0a, //0x00000a75 cmpb         $10, %cl
	0x0f, 0x84, 0xe2, 0xfe, 0xff, 0xff, //0x00000a78 je           LBB0_124
	0x80, 0xf9, 0x0d, //0x00000a7e cmpb         $13, %cl
	0x0f, 0x84, 0xd9, 0xfe, 0xff, 0xff, //0x00000a81 je           LBB0_124
	0x80, 0xf9, 0x3d, //0x00000a87 cmpb         $61, %cl
	0x0f, 0x85, 0x4e, 0x00, 0x00, 0x00, //0x00000a8a jne          LBB0_166
	0x83, 0x7d, 0x90, 0x02, //0x00000a90 cmpl         $2, $-112(%rbp)
	0x0f, 0x84, 0x44, 0x00, 0x00, 0x00, //0x00000a94 je           LBB0_166
	0x4c, 0x39, 0xce, //0x00000a9a cmpq         %r9, %rsi
	0x0f, 0x82, 0x77, 0x02, 0x00, 0x00, //0x00000a9d jb           LBB0_198
	//0x00000aa3 LBB0_195
	0x49, 0x89, 0xf4, //0x00000aa3 movq         %rsi, %r12
	0xe9, 0x47, 0xfc, 0xff, 0xff, //0x00000aa6 jmp          LBB0_65
	//0x00000aab LBB0_125
	0x49, 0x89, 0xc4, //0x00000aab movq         %rax, %r12
	0x44, 0x8b, 0x55, 0xcc, //0x00000aae movl         $-52(%rbp), %r10d
	0xe9, 0xec, 0xfb, 0xff, 0xff, //0x00000ab2 jmp          LBB0_60
	//0x00000ab7 LBB0_126
	0x48, 0x83, 0xc6, 0x01, //0x00000ab7 addq         $1, %rsi
	0x48, 0x83, 0xc1, 0x01, //0x00000abb addq         $1, %rcx
	0x48, 0x39, 0xc8, //0x00000abf cmpq         %rcx, %rax
	0x0f, 0x84, 0x2a, 0xfc, 0xff, 0xff, //0x00000ac2 je           LBB0_65
	//0x00000ac8 LBB0_127
	0x0f, 0xb6, 0x5e, 0xff, //0x00000ac8 movzbl       $-1(%rsi), %ebx
	0x80, 0xfb, 0x0d, //0x00000acc cmpb         $13, %bl
	0x0f, 0x84, 0xe2, 0xff, 0xff, 0xff, //0x00000acf je           LBB0_126
	0x80, 0xfb, 0x0a, //0x00000ad5 cmpb         $10, %bl
	0x0f, 0x84, 0xd9, 0xff, 0xff, 0xff, //0x00000ad8 je           LBB0_126
	//0x00000ade LBB0_166
	0x49, 0x89, 0xf4, //0x00000ade movq         %rsi, %r12
	//0x00000ae1 LBB0_167
	0x31, 0xc9, //0x00000ae1 xorl         %ecx, %ecx
	0x4d, 0x39, 0xcc, //0x00000ae3 cmpq         %r9, %r12
	0x0f, 0x94, 0xc1, //0x00000ae6 sete         %cl
	0x4c, 0x01, 0xe1, //0x00000ae9 addq         %r12, %rcx
	0x4c, 0x39, 0xd9, //0x00000aec cmpq         %r11, %rcx
	0x48, 0x8b, 0x55, 0xc0, //0x00000aef movq         $-64(%rbp), %rdx
	0x0f, 0x84, 0x55, 0xf9, 0xff, 0xff, //0x00000af3 je           LBB0_23
	0xe9, 0x65, 0x2b, 0x00, 0x00, //0x00000af9 jmp          LBB0_864
	//0x00000afe LBB0_129
	0x49, 0x89, 0xcc, //0x00000afe movq         %rcx, %r12
	0xe9, 0x5b, 0x00, 0x00, 0x00, //0x00000b01 jmp          LBB0_138
	//0x00000b06 LBB0_130
	0x49, 0x83, 0xc4, 0x01, //0x00000b06 addq         $1, %r12
	0xbf, 0x03, 0x00, 0x00, 0x00, //0x00000b0a movl         $3, %edi
	0x4d, 0x39, 0xcc, //0x00000b0f cmpq         %r9, %r12
	0x0f, 0x83, 0xf7, 0xfa, 0xff, 0xff, //0x00000b12 jae          LBB0_49
	//0x00000b18 LBB0_131
	0x41, 0x0f, 0xb6, 0x34, 0x24, //0x00000b18 movzbl       (%r12), %esi
	0x48, 0x83, 0xfe, 0x0d, //0x00000b1d cmpq         $13, %rsi
	0x0f, 0x84, 0xdf, 0xff, 0xff, 0xff, //0x00000b21 je           LBB0_130
	0x40, 0x80, 0xfe, 0x0a, //0x00000b27 cmpb         $10, %sil
	0x0f, 0x84, 0xd5, 0xff, 0xff, 0xff, //0x00000b2b je           LBB0_130
	0x48, 0x8b, 0x45, 0xd0, //0x00000b31 movq         $-48(%rbp), %rax
	0x0f, 0xb6, 0x04, 0x30, //0x00000b35 movzbl       (%rax,%rsi), %eax
	0x49, 0x83, 0xc4, 0x01, //0x00000b39 addq         $1, %r12
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00000b3d cmpl         $255, %eax
	0x0f, 0x85, 0xbb, 0x04, 0x00, 0x00, //0x00000b42 jne          LBB0_220
	//0x00000b48 LBB0_221
	0xbf, 0x03, 0x00, 0x00, 0x00, //0x00000b48 movl         $3, %edi
	0xe9, 0xf2, 0xfb, 0xff, 0xff, //0x00000b4d jmp          LBB0_73
	//0x00000b52 LBB0_135
	0x80, 0xf9, 0x2f, //0x00000b52 cmpb         $47, %cl
	0x0f, 0x85, 0x03, 0x00, 0x00, 0x00, //0x00000b55 jne          LBB0_137
	0x40, 0xb6, 0x2f, //0x00000b5b movb         $47, %sil
	//0x00000b5e LBB0_137
	0x49, 0x89, 0xc4, //0x00000b5e movq         %rax, %r12
	//0x00000b61 LBB0_138
	0x40, 0x0f, 0xb6, 0xc6, //0x00000b61 movzbl       %sil, %eax
	0x48, 0x8b, 0x4d, 0xd0, //0x00000b65 movq         $-48(%rbp), %rcx
	0x0f, 0xb6, 0x04, 0x01, //0x00000b69 movzbl       (%rcx,%rax), %eax
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00000b6d cmpl         $255, %eax
	0x0f, 0x84, 0x62, 0x01, 0x00, 0x00, //0x00000b72 je           LBB0_158
	0x41, 0xc1, 0xe5, 0x06, //0x00000b78 shll         $6, %r13d
	0x41, 0x09, 0xc5, //0x00000b7c orl          %eax, %r13d
	0xbf, 0x02, 0x00, 0x00, 0x00, //0x00000b7f movl         $2, %edi
	0x4d, 0x39, 0xcc, //0x00000b84 cmpq         %r9, %r12
	0x0f, 0x82, 0x1f, 0x00, 0x00, 0x00, //0x00000b87 jb           LBB0_142
	0xe9, 0x39, 0xfb, 0xff, 0xff, //0x00000b8d jmp          LBB0_62
	//0x00000b92 LBB0_156
	0x80, 0xf9, 0x6e, //0x00000b92 cmpb         $110, %cl
	0x0f, 0x85, 0x9c, 0x02, 0x00, 0x00, //0x00000b95 jne          LBB0_168
	//0x00000b9b LBB0_140
	0x49, 0x89, 0xc4, //0x00000b9b movq         %rax, %r12
	//0x00000b9e LBB0_141
	0xbf, 0x02, 0x00, 0x00, 0x00, //0x00000b9e movl         $2, %edi
	0x4d, 0x39, 0xcc, //0x00000ba3 cmpq         %r9, %r12
	0x0f, 0x83, 0x63, 0xfa, 0xff, 0xff, //0x00000ba6 jae          LBB0_49
	//0x00000bac LBB0_142
	0x49, 0x8d, 0x4c, 0x24, 0x01, //0x00000bac leaq         $1(%r12), %rcx
	0x41, 0x0f, 0xb6, 0x04, 0x24, //0x00000bb1 movzbl       (%r12), %eax
	0x3c, 0x5c, //0x00000bb6 cmpb         $92, %al
	0x0f, 0x85, 0x02, 0x01, 0x00, 0x00, //0x00000bb8 jne          LBB0_154
	0x49, 0x8d, 0x44, 0x24, 0x02, //0x00000bbe leaq         $2(%r12), %rax
	0x40, 0xb6, 0xff, //0x00000bc3 movb         $-1, %sil
	0x4c, 0x39, 0xc8, //0x00000bc6 cmpq         %r9, %rax
	0x0f, 0x87, 0x37, 0x01, 0x00, 0x00, //0x00000bc9 ja           LBB0_163
	0x0f, 0xb6, 0x09, //0x00000bcf movzbl       (%rcx), %ecx
	0x80, 0xf9, 0x71, //0x00000bd2 cmpb         $113, %cl
	0x0f, 0x8e, 0xb7, 0xff, 0xff, 0xff, //0x00000bd5 jle          LBB0_156
	0x80, 0xf9, 0x72, //0x00000bdb cmpb         $114, %cl
	0x0f, 0x84, 0xb7, 0xff, 0xff, 0xff, //0x00000bde je           LBB0_140
	0x80, 0xf9, 0x75, //0x00000be4 cmpb         $117, %cl
	0x0f, 0x85, 0x56, 0x02, 0x00, 0x00, //0x00000be7 jne          LBB0_170
	0x4c, 0x89, 0xc9, //0x00000bed movq         %r9, %rcx
	0x48, 0x29, 0xc1, //0x00000bf0 subq         %rax, %rcx
	0x48, 0x83, 0xf9, 0x04, //0x00000bf3 cmpq         $4, %rcx
	0x0f, 0x8c, 0x46, 0x02, 0x00, 0x00, //0x00000bf7 jl           LBB0_170
	0x8b, 0x08, //0x00000bfd movl         (%rax), %ecx
	0x89, 0xcf, //0x00000bff movl         %ecx, %edi
	0xf7, 0xd7, //0x00000c01 notl         %edi
	0x8d, 0x91, 0xd0, 0xcf, 0xcf, 0xcf, //0x00000c03 leal         $-808464432(%rcx), %edx
	0x81, 0xe7, 0x80, 0x80, 0x80, 0x80, //0x00000c09 andl         $-2139062144, %edi
	0x85, 0xd7, //0x00000c0f testl        %edx, %edi
	0x0f, 0x85, 0x2c, 0x02, 0x00, 0x00, //0x00000c11 jne          LBB0_170
	0x8d, 0x91, 0x19, 0x19, 0x19, 0x19, //0x00000c17 leal         $421075225(%rcx), %edx
	0x09, 0xca, //0x00000c1d orl          %ecx, %edx
	0xf7, 0xc2, 0x80, 0x80, 0x80, 0x80, //0x00000c1f testl        $-2139062144, %edx
	0x0f, 0x85, 0x18, 0x02, 0x00, 0x00, //0x00000c25 jne          LBB0_170
	0x89, 0xcb, //0x00000c2b movl         %ecx, %ebx
	0x81, 0xe3, 0x7f, 0x7f, 0x7f, 0x7f, //0x00000c2d andl         $2139062143, %ebx
	0xba, 0xc0, 0xc0, 0xc0, 0xc0, //0x00000c33 movl         $-1061109568, %edx
	0x29, 0xda, //0x00000c38 subl         %ebx, %edx
	0x44, 0x8d, 0x83, 0x46, 0x46, 0x46, 0x46, //0x00000c3a leal         $1179010630(%rbx), %r8d
	0x21, 0xfa, //0x00000c41 andl         %edi, %edx
	0x44, 0x85, 0xc2, //0x00000c43 testl        %r8d, %edx
	0x0f, 0x85, 0x6e, 0x03, 0x00, 0x00, //0x00000c46 jne          LBB0_192
	0xba, 0xe0, 0xe0, 0xe0, 0xe0, //0x00000c4c movl         $-522133280, %edx
	0x29, 0xda, //0x00000c51 subl         %ebx, %edx
	0x81, 0xc3, 0x39, 0x39, 0x39, 0x39, //0x00000c53 addl         $960051513, %ebx
	0x21, 0xd7, //0x00000c59 andl         %edx, %edi
	0x85, 0xdf, //0x00000c5b testl        %ebx, %edi
	0x49, 0xb8, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, //0x00000c5d movabsq      $-4294967296, %r8
	0x0f, 0x85, 0xd6, 0x01, 0x00, 0x00, //0x00000c67 jne          LBB0_170
	0x0f, 0xc9, //0x00000c6d bswapl       %ecx
	0x89, 0xc8, //0x00000c6f movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x00000c71 shrl         $4, %eax
	0xf7, 0xd0, //0x00000c74 notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x00000c76 andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x00000c7b leal         (%rax,%rax,8), %eax
	0x81, 0xe1, 0x0f, 0x0f, 0x0f, 0x0f, //0x00000c7e andl         $252645135, %ecx
	0x01, 0xc1, //0x00000c84 addl         %eax, %ecx
	0x89, 0xc8, //0x00000c86 movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x00000c88 shrl         $4, %eax
	0x09, 0xc8, //0x00000c8b orl          %ecx, %eax
	0x89, 0xc1, //0x00000c8d movl         %eax, %ecx
	0xc1, 0xe9, 0x08, //0x00000c8f shrl         $8, %ecx
	0x81, 0xe1, 0x00, 0xff, 0x00, 0x00, //0x00000c92 andl         $65280, %ecx
	0x89, 0xc2, //0x00000c98 movl         %eax, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x00000c9a andl         $128, %edx
	0x49, 0x83, 0xc4, 0x06, //0x00000ca0 addq         $6, %r12
	0x09, 0xca, //0x00000ca4 orl          %ecx, %edx
	0x0f, 0x85, 0x9a, 0x01, 0x00, 0x00, //0x00000ca6 jne          LBB0_171
	0x3c, 0x0d, //0x00000cac cmpb         $13, %al
	0x0f, 0x85, 0x17, 0x00, 0x00, 0x00, //0x00000cae jne          LBB0_155
	0xe9, 0xe5, 0xfe, 0xff, 0xff, //0x00000cb4 jmp          LBB0_141
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000cb9 .p2align 4, 0x90
	//0x00000cc0 LBB0_154
	0x49, 0x89, 0xcc, //0x00000cc0 movq         %rcx, %r12
	0x3c, 0x0d, //0x00000cc3 cmpb         $13, %al
	0x0f, 0x84, 0xd3, 0xfe, 0xff, 0xff, //0x00000cc5 je           LBB0_141
	//0x00000ccb LBB0_155
	0x89, 0xc6, //0x00000ccb movl         %eax, %esi
	0x3c, 0x0a, //0x00000ccd cmpb         $10, %al
	0x0f, 0x84, 0xc9, 0xfe, 0xff, 0xff, //0x00000ccf je           LBB0_141
	0xe9, 0x6c, 0x01, 0x00, 0x00, //0x00000cd5 jmp          LBB0_171
	//0x00000cda LBB0_158
	0xbf, 0x01, 0x00, 0x00, 0x00, //0x00000cda movl         $1, %edi
	0xe9, 0x60, 0xfa, 0xff, 0xff, //0x00000cdf jmp          LBB0_73
	//0x00000ce4 LBB0_159
	0x4d, 0x89, 0xcc, //0x00000ce4 movq         %r9, %r12
	0xe9, 0x06, 0xfa, 0xff, 0xff, //0x00000ce7 jmp          LBB0_65
	//0x00000cec LBB0_161
	0x49, 0x89, 0xc4, //0x00000cec movq         %rax, %r12
	0x49, 0xb8, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, //0x00000cef movabsq      $-4294967296, %r8
	0xe9, 0x63, 0xfe, 0xff, 0xff, //0x00000cf9 jmp          LBB0_138
	//0x00000cfe LBB0_162
	0x49, 0x89, 0xdc, //0x00000cfe movq         %rbx, %r12
	0xe9, 0xec, 0xf9, 0xff, 0xff, //0x00000d01 jmp          LBB0_65
	//0x00000d06 LBB0_163
	0x49, 0x89, 0xcc, //0x00000d06 movq         %rcx, %r12
	0xe9, 0x38, 0x01, 0x00, 0x00, //0x00000d09 jmp          LBB0_171
	//0x00000d0e LBB0_196
	0x4c, 0x89, 0xe6, //0x00000d0e movq         %r12, %rsi
	//0x00000d11 LBB0_197
	0x4c, 0x39, 0xce, //0x00000d11 cmpq         %r9, %rsi
	0x0f, 0x83, 0x89, 0xfd, 0xff, 0xff, //0x00000d14 jae          LBB0_195
	//0x00000d1a LBB0_198
	0x48, 0x8d, 0x46, 0x01, //0x00000d1a leaq         $1(%rsi), %rax
	0x0f, 0xb6, 0x0e, //0x00000d1e movzbl       (%rsi), %ecx
	0x80, 0xf9, 0x5c, //0x00000d21 cmpb         $92, %cl
	0x0f, 0x85, 0xf0, 0x00, 0x00, 0x00, //0x00000d24 jne          LBB0_209
	0x4c, 0x8d, 0x66, 0x02, //0x00000d2a leaq         $2(%rsi), %r12
	0x4d, 0x39, 0xcc, //0x00000d2e cmpq         %r9, %r12
	0x0f, 0x87, 0xef, 0x02, 0x00, 0x00, //0x00000d31 ja           LBB0_224
	0x0f, 0xb6, 0x00, //0x00000d37 movzbl       (%rax), %eax
	0x3c, 0x6e, //0x00000d3a cmpb         $110, %al
	0x0f, 0x84, 0xcc, 0xff, 0xff, 0xff, //0x00000d3c je           LBB0_196
	0x3c, 0x72, //0x00000d42 cmpb         $114, %al
	0x0f, 0x84, 0xc4, 0xff, 0xff, 0xff, //0x00000d44 je           LBB0_196
	0x3c, 0x75, //0x00000d4a cmpb         $117, %al
	0x0f, 0x85, 0x8f, 0xfd, 0xff, 0xff, //0x00000d4c jne          LBB0_167
	0x4c, 0x89, 0xc8, //0x00000d52 movq         %r9, %rax
	0x4c, 0x29, 0xe0, //0x00000d55 subq         %r12, %rax
	0x48, 0x83, 0xf8, 0x04, //0x00000d58 cmpq         $4, %rax
	0x0f, 0x8c, 0x7f, 0xfd, 0xff, 0xff, //0x00000d5c jl           LBB0_167
	0x41, 0x8b, 0x04, 0x24, //0x00000d62 movl         (%r12), %eax
	0x89, 0xc1, //0x00000d66 movl         %eax, %ecx
	0xf7, 0xd1, //0x00000d68 notl         %ecx
	0x8d, 0x90, 0xd0, 0xcf, 0xcf, 0xcf, //0x00000d6a leal         $-808464432(%rax), %edx
	0x81, 0xe1, 0x80, 0x80, 0x80, 0x80, //0x00000d70 andl         $-2139062144, %ecx
	0x85, 0xd1, //0x00000d76 testl        %edx, %ecx
	0x0f, 0x85, 0x63, 0xfd, 0xff, 0xff, //0x00000d78 jne          LBB0_167
	0x8d, 0x90, 0x19, 0x19, 0x19, 0x19, //0x00000d7e leal         $421075225(%rax), %edx
	0x09, 0xc2, //0x00000d84 orl          %eax, %edx
	0xf7, 0xc2, 0x80, 0x80, 0x80, 0x80, //0x00000d86 testl        $-2139062144, %edx
	0x0f, 0x85, 0x9c, 0x02, 0x00, 0x00, //0x00000d8c jne          LBB0_226
	0x89, 0xc3, //0x00000d92 movl         %eax, %ebx
	0x81, 0xe3, 0x7f, 0x7f, 0x7f, 0x7f, //0x00000d94 andl         $2139062143, %ebx
	0xba, 0xc0, 0xc0, 0xc0, 0xc0, //0x00000d9a movl         $-1061109568, %edx
	0x29, 0xda, //0x00000d9f subl         %ebx, %edx
	0x44, 0x8d, 0x83, 0x46, 0x46, 0x46, 0x46, //0x00000da1 leal         $1179010630(%rbx), %r8d
	0x21, 0xca, //0x00000da8 andl         %ecx, %edx
	0x44, 0x85, 0xc2, //0x00000daa testl        %r8d, %edx
	0x0f, 0x85, 0x7b, 0x02, 0x00, 0x00, //0x00000dad jne          LBB0_226
	0xba, 0xe0, 0xe0, 0xe0, 0xe0, //0x00000db3 movl         $-522133280, %edx
	0x29, 0xda, //0x00000db8 subl         %ebx, %edx
	0x81, 0xc3, 0x39, 0x39, 0x39, 0x39, //0x00000dba addl         $960051513, %ebx
	0x21, 0xd1, //0x00000dc0 andl         %edx, %ecx
	0x85, 0xd9, //0x00000dc2 testl        %ebx, %ecx
	0x49, 0xb8, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, //0x00000dc4 movabsq      $-4294967296, %r8
	0x0f, 0x85, 0x0d, 0xfd, 0xff, 0xff, //0x00000dce jne          LBB0_167
	0x0f, 0xc8, //0x00000dd4 bswapl       %eax
	0x89, 0xc1, //0x00000dd6 movl         %eax, %ecx
	0xc1, 0xe9, 0x04, //0x00000dd8 shrl         $4, %ecx
	0xf7, 0xd1, //0x00000ddb notl         %ecx
	0x81, 0xe1, 0x01, 0x01, 0x01, 0x01, //0x00000ddd andl         $16843009, %ecx
	0x8d, 0x0c, 0xc9, //0x00000de3 leal         (%rcx,%rcx,8), %ecx
	0x25, 0x0f, 0x0f, 0x0f, 0x0f, //0x00000de6 andl         $252645135, %eax
	0x01, 0xc8, //0x00000deb addl         %ecx, %eax
	0x89, 0xc1, //0x00000ded movl         %eax, %ecx
	0xc1, 0xe9, 0x04, //0x00000def shrl         $4, %ecx
	0x09, 0xc1, //0x00000df2 orl          %eax, %ecx
	0x89, 0xc8, //0x00000df4 movl         %ecx, %eax
	0xc1, 0xe8, 0x08, //0x00000df6 shrl         $8, %eax
	0x25, 0x00, 0xff, 0x00, 0x00, //0x00000df9 andl         $65280, %eax
	0x89, 0xca, //0x00000dfe movl         %ecx, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x00000e00 andl         $128, %edx
	0x48, 0x83, 0xc6, 0x06, //0x00000e06 addq         $6, %rsi
	0x09, 0xc2, //0x00000e0a orl          %eax, %edx
	0x49, 0x89, 0xf4, //0x00000e0c movq         %rsi, %r12
	0x0f, 0x84, 0x08, 0x00, 0x00, 0x00, //0x00000e0f je           LBB0_210
	0xe9, 0xc7, 0xfc, 0xff, 0xff, //0x00000e15 jmp          LBB0_167
	//0x00000e1a LBB0_209
	0x49, 0x89, 0xc4, //0x00000e1a movq         %rax, %r12
	//0x00000e1d LBB0_210
	0x80, 0xf9, 0x0d, //0x00000e1d cmpb         $13, %cl
	0x0f, 0x84, 0xe8, 0xfe, 0xff, 0xff, //0x00000e20 je           LBB0_196
	0x4c, 0x89, 0xe6, //0x00000e26 movq         %r12, %rsi
	0x80, 0xf9, 0x0a, //0x00000e29 cmpb         $10, %cl
	0x0f, 0x84, 0xdf, 0xfe, 0xff, 0xff, //0x00000e2c je           LBB0_197
	0xe9, 0xaa, 0xfc, 0xff, 0xff, //0x00000e32 jmp          LBB0_167
	//0x00000e37 LBB0_168
	0x80, 0xf9, 0x2f, //0x00000e37 cmpb         $47, %cl
	0x0f, 0x85, 0x03, 0x00, 0x00, 0x00, //0x00000e3a jne          LBB0_170
	0x40, 0xb6, 0x2f, //0x00000e40 movb         $47, %sil
	//0x00000e43 LBB0_170
	0x49, 0x89, 0xc4, //0x00000e43 movq         %rax, %r12
	//0x00000e46 LBB0_171
	0x40, 0x0f, 0xb6, 0xc6, //0x00000e46 movzbl       %sil, %eax
	0x48, 0x8b, 0x4d, 0xd0, //0x00000e4a movq         $-48(%rbp), %rcx
	0x0f, 0xb6, 0x04, 0x01, //0x00000e4e movzbl       (%rcx,%rax), %eax
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00000e52 cmpl         $255, %eax
	0x0f, 0x84, 0x53, 0x01, 0x00, 0x00, //0x00000e57 je           LBB0_191
	0x41, 0xc1, 0xe5, 0x06, //0x00000e5d shll         $6, %r13d
	0x41, 0x09, 0xc5, //0x00000e61 orl          %eax, %r13d
	0xbf, 0x03, 0x00, 0x00, 0x00, //0x00000e64 movl         $3, %edi
	0x4d, 0x39, 0xcc, //0x00000e69 cmpq         %r9, %r12
	0x0f, 0x82, 0x1f, 0x00, 0x00, 0x00, //0x00000e6c jb           LBB0_175
	0xe9, 0x54, 0xf8, 0xff, 0xff, //0x00000e72 jmp          LBB0_62
	//0x00000e77 LBB0_189
	0x80, 0xf9, 0x6e, //0x00000e77 cmpb         $110, %cl
	0x0f, 0x85, 0x5d, 0x01, 0x00, 0x00, //0x00000e7a jne          LBB0_216
	//0x00000e80 LBB0_173
	0x49, 0x89, 0xc4, //0x00000e80 movq         %rax, %r12
	//0x00000e83 LBB0_174
	0xbf, 0x03, 0x00, 0x00, 0x00, //0x00000e83 movl         $3, %edi
	0x4d, 0x39, 0xcc, //0x00000e88 cmpq         %r9, %r12
	0x0f, 0x83, 0x7e, 0xf7, 0xff, 0xff, //0x00000e8b jae          LBB0_49
	//0x00000e91 LBB0_175
	0x49, 0x8d, 0x4c, 0x24, 0x01, //0x00000e91 leaq         $1(%r12), %rcx
	0x41, 0x0f, 0xb6, 0x04, 0x24, //0x00000e96 movzbl       (%r12), %eax
	0x3c, 0x5c, //0x00000e9b cmpb         $92, %al
	0x0f, 0x85, 0xf3, 0x00, 0x00, 0x00, //0x00000e9d jne          LBB0_186
	0x49, 0x8d, 0x44, 0x24, 0x02, //0x00000ea3 leaq         $2(%r12), %rax
	0x40, 0xb6, 0xff, //0x00000ea8 movb         $-1, %sil
	0x4c, 0x39, 0xc8, //0x00000eab cmpq         %r9, %rax
	0x0f, 0x87, 0x18, 0x01, 0x00, 0x00, //0x00000eae ja           LBB0_193
	0x0f, 0xb6, 0x09, //0x00000eb4 movzbl       (%rcx), %ecx
	0x80, 0xf9, 0x71, //0x00000eb7 cmpb         $113, %cl
	0x0f, 0x8e, 0xb7, 0xff, 0xff, 0xff, //0x00000eba jle          LBB0_189
	0x80, 0xf9, 0x72, //0x00000ec0 cmpb         $114, %cl
	0x0f, 0x84, 0xb7, 0xff, 0xff, 0xff, //0x00000ec3 je           LBB0_173
	0x80, 0xf9, 0x75, //0x00000ec9 cmpb         $117, %cl
	0x0f, 0x85, 0x17, 0x01, 0x00, 0x00, //0x00000ecc jne          LBB0_218
	0x4c, 0x89, 0xc9, //0x00000ed2 movq         %r9, %rcx
	0x48, 0x29, 0xc1, //0x00000ed5 subq         %rax, %rcx
	0x48, 0x83, 0xf9, 0x04, //0x00000ed8 cmpq         $4, %rcx
	0x0f, 0x8c, 0x07, 0x01, 0x00, 0x00, //0x00000edc jl           LBB0_218
	0x8b, 0x08, //0x00000ee2 movl         (%rax), %ecx
	0x89, 0xcf, //0x00000ee4 movl         %ecx, %edi
	0xf7, 0xd7, //0x00000ee6 notl         %edi
	0x8d, 0x91, 0xd0, 0xcf, 0xcf, 0xcf, //0x00000ee8 leal         $-808464432(%rcx), %edx
	0x81, 0xe7, 0x80, 0x80, 0x80, 0x80, //0x00000eee andl         $-2139062144, %edi
	0x85, 0xd7, //0x00000ef4 testl        %edx, %edi
	0x0f, 0x85, 0xed, 0x00, 0x00, 0x00, //0x00000ef6 jne          LBB0_218
	0x8d, 0x91, 0x19, 0x19, 0x19, 0x19, //0x00000efc leal         $421075225(%rcx), %edx
	0x09, 0xca, //0x00000f02 orl          %ecx, %edx
	0xf7, 0xc2, 0x80, 0x80, 0x80, 0x80, //0x00000f04 testl        $-2139062144, %edx
	0x0f, 0x85, 0xd9, 0x00, 0x00, 0x00, //0x00000f0a jne          LBB0_218
	0x89, 0xcb, //0x00000f10 movl         %ecx, %ebx
	0x81, 0xe3, 0x7f, 0x7f, 0x7f, 0x7f, //0x00000f12 andl         $2139062143, %ebx
	0xba, 0xc0, 0xc0, 0xc0, 0xc0, //0x00000f18 movl         $-1061109568, %edx
	0x29, 0xda, //0x00000f1d subl         %ebx, %edx
	0x44, 0x8d, 0x83, 0x46, 0x46, 0x46, 0x46, //0x00000f1f leal         $1179010630(%rbx), %r8d
	0x21, 0xfa, //0x00000f26 andl         %edi, %edx
	0x44, 0x85, 0xc2, //0x00000f28 testl        %r8d, %edx
	0x0f, 0x85, 0xe3, 0x00, 0x00, 0x00, //0x00000f2b jne          LBB0_222
	0xba, 0xe0, 0xe0, 0xe0, 0xe0, //0x00000f31 movl         $-522133280, %edx
	0x29, 0xda, //0x00000f36 subl         %ebx, %edx
	0x81, 0xc3, 0x39, 0x39, 0x39, 0x39, //0x00000f38 addl         $960051513, %ebx
	0x21, 0xd7, //0x00000f3e andl         %edx, %edi
	0x85, 0xdf, //0x00000f40 testl        %ebx, %edi
	0x49, 0xb8, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, //0x00000f42 movabsq      $-4294967296, %r8
	0x0f, 0x85, 0x97, 0x00, 0x00, 0x00, //0x00000f4c jne          LBB0_218
	0x0f, 0xc9, //0x00000f52 bswapl       %ecx
	0x89, 0xc8, //0x00000f54 movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x00000f56 shrl         $4, %eax
	0xf7, 0xd0, //0x00000f59 notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x00000f5b andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x00000f60 leal         (%rax,%rax,8), %eax
	0x81, 0xe1, 0x0f, 0x0f, 0x0f, 0x0f, //0x00000f63 andl         $252645135, %ecx
	0x01, 0xc1, //0x00000f69 addl         %eax, %ecx
	0x89, 0xc8, //0x00000f6b movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x00000f6d shrl         $4, %eax
	0x09, 0xc8, //0x00000f70 orl          %ecx, %eax
	0x89, 0xc1, //0x00000f72 movl         %eax, %ecx
	0xc1, 0xe9, 0x08, //0x00000f74 shrl         $8, %ecx
	0x81, 0xe1, 0x00, 0xff, 0x00, 0x00, //0x00000f77 andl         $65280, %ecx
	0x89, 0xc2, //0x00000f7d movl         %eax, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x00000f7f andl         $128, %edx
	0x49, 0x83, 0xc4, 0x06, //0x00000f85 addq         $6, %r12
	0x09, 0xca, //0x00000f89 orl          %ecx, %edx
	0x0f, 0x84, 0x08, 0x00, 0x00, 0x00, //0x00000f8b je           LBB0_187
	0xe9, 0x56, 0x00, 0x00, 0x00, //0x00000f91 jmp          LBB0_219
	//0x00000f96 LBB0_186
	0x49, 0x89, 0xcc, //0x00000f96 movq         %rcx, %r12
	//0x00000f99 LBB0_187
	0x3c, 0x0d, //0x00000f99 cmpb         $13, %al
	0x0f, 0x84, 0xe2, 0xfe, 0xff, 0xff, //0x00000f9b je           LBB0_174
	0x89, 0xc6, //0x00000fa1 movl         %eax, %esi
	0x3c, 0x0a, //0x00000fa3 cmpb         $10, %al
	0x0f, 0x84, 0xd8, 0xfe, 0xff, 0xff, //0x00000fa5 je           LBB0_174
	0xe9, 0x3c, 0x00, 0x00, 0x00, //0x00000fab jmp          LBB0_219
	//0x00000fb0 LBB0_191
	0xbf, 0x02, 0x00, 0x00, 0x00, //0x00000fb0 movl         $2, %edi
	0xe9, 0x8a, 0xf7, 0xff, 0xff, //0x00000fb5 jmp          LBB0_73
	//0x00000fba LBB0_192
	0x49, 0x89, 0xc4, //0x00000fba movq         %rax, %r12
	0x49, 0xb8, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, //0x00000fbd movabsq      $-4294967296, %r8
	0xe9, 0x7a, 0xfe, 0xff, 0xff, //0x00000fc7 jmp          LBB0_171
	//0x00000fcc LBB0_193
	0x49, 0x89, 0xcc, //0x00000fcc movq         %rcx, %r12
	0xe9, 0x18, 0x00, 0x00, 0x00, //0x00000fcf jmp          LBB0_219
	//0x00000fd4 LBB0_215
	0x44, 0x8b, 0x55, 0xcc, //0x00000fd4 movl         $-52(%rbp), %r10d
	0xe9, 0x04, 0xfb, 0xff, 0xff, //0x00000fd8 jmp          LBB0_167
	//0x00000fdd LBB0_216
	0x80, 0xf9, 0x2f, //0x00000fdd cmpb         $47, %cl
	0x0f, 0x85, 0x03, 0x00, 0x00, 0x00, //0x00000fe0 jne          LBB0_218
	0x40, 0xb6, 0x2f, //0x00000fe6 movb         $47, %sil
	//0x00000fe9 LBB0_218
	0x49, 0x89, 0xc4, //0x00000fe9 movq         %rax, %r12
	//0x00000fec LBB0_219
	0x40, 0x0f, 0xb6, 0xc6, //0x00000fec movzbl       %sil, %eax
	0x48, 0x8b, 0x4d, 0xd0, //0x00000ff0 movq         $-48(%rbp), %rcx
	0x0f, 0xb6, 0x04, 0x01, //0x00000ff4 movzbl       (%rcx,%rax), %eax
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00000ff8 cmpl         $255, %eax
	0x0f, 0x84, 0x45, 0xfb, 0xff, 0xff, //0x00000ffd je           LBB0_221
	//0x00001003 LBB0_220
	0x41, 0xc1, 0xe5, 0x06, //0x00001003 shll         $6, %r13d
	0x41, 0x09, 0xc5, //0x00001007 orl          %eax, %r13d
	0xbf, 0x04, 0x00, 0x00, 0x00, //0x0000100a movl         $4, %edi
	0xe9, 0xb7, 0xf6, 0xff, 0xff, //0x0000100f jmp          LBB0_62
	//0x00001014 LBB0_222
	0x49, 0x89, 0xc4, //0x00001014 movq         %rax, %r12
	0x49, 0xb8, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, //0x00001017 movabsq      $-4294967296, %r8
	0xe9, 0xc6, 0xff, 0xff, 0xff, //0x00001021 jmp          LBB0_219
	//0x00001026 LBB0_224
	0x49, 0x89, 0xc4, //0x00001026 movq         %rax, %r12
	0xe9, 0xb3, 0xfa, 0xff, 0xff, //0x00001029 jmp          LBB0_167
	//0x0000102e LBB0_226
	0x49, 0xb8, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, //0x0000102e movabsq      $-4294967296, %r8
	0xe9, 0xa4, 0xfa, 0xff, 0xff, //0x00001038 jmp          LBB0_167
	//0x0000103d LBB0_227
	0x49, 0x8d, 0x41, 0xf8, //0x0000103d leaq         $-8(%r9), %rax
	0x48, 0x89, 0x45, 0xa8, //0x00001041 movq         %rax, $-88(%rbp)
	0x49, 0x39, 0xc3, //0x00001045 cmpq         %rax, %r11
	0x0f, 0x87, 0x06, 0x0d, 0x00, 0x00, //0x00001048 ja           LBB0_441
	0x48, 0x8b, 0x45, 0xb8, //0x0000104e movq         $-72(%rbp), %rax
	0x48, 0x83, 0xc0, 0xf8, //0x00001052 addq         $-8, %rax
	0x48, 0x89, 0x45, 0x90, //0x00001056 movq         %rax, $-112(%rbp)
	0x48, 0x39, 0xc2, //0x0000105a cmpq         %rax, %rdx
	0x0f, 0x87, 0xf1, 0x0c, 0x00, 0x00, //0x0000105d ja           LBB0_441
	0x48, 0x8b, 0x45, 0xb0, //0x00001063 movq         $-80(%rbp), %rax
	0x48, 0x8b, 0x4d, 0xa0, //0x00001067 movq         $-96(%rbp), %rcx
	0x48, 0x01, 0xc8, //0x0000106b addq         %rcx, %rax
	0x48, 0x83, 0xc0, 0xff, //0x0000106e addq         $-1, %rax
	0x48, 0x89, 0x45, 0x88, //0x00001072 movq         %rax, $-120(%rbp)
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001076 .p2align 4, 0x90
	//0x00001080 LBB0_230
	0x48, 0x89, 0x55, 0xc0, //0x00001080 movq         %rdx, $-64(%rbp)
	0x41, 0x0f, 0xb6, 0x03, //0x00001084 movzbl       (%r11), %eax
	0x48, 0x8b, 0x55, 0xd0, //0x00001088 movq         $-48(%rbp), %rdx
	0x44, 0x0f, 0xb6, 0x24, 0x02, //0x0000108c movzbl       (%rdx,%rax), %r12d
	0x41, 0x0f, 0xb6, 0x43, 0x01, //0x00001091 movzbl       $1(%r11), %eax
	0x44, 0x0f, 0xb6, 0x14, 0x02, //0x00001096 movzbl       (%rdx,%rax), %r10d
	0x41, 0x0f, 0xb6, 0x43, 0x02, //0x0000109b movzbl       $2(%r11), %eax
	0x44, 0x0f, 0xb6, 0x2c, 0x02, //0x000010a0 movzbl       (%rdx,%rax), %r13d
	0x41, 0x0f, 0xb6, 0x43, 0x03, //0x000010a5 movzbl       $3(%r11), %eax
	0x0f, 0xb6, 0x04, 0x02, //0x000010aa movzbl       (%rdx,%rax), %eax
	0x41, 0x0f, 0xb6, 0x4b, 0x04, //0x000010ae movzbl       $4(%r11), %ecx
	0x44, 0x0f, 0xb6, 0x34, 0x0a, //0x000010b3 movzbl       (%rdx,%rcx), %r14d
	0x41, 0x0f, 0xb6, 0x4b, 0x05, //0x000010b8 movzbl       $5(%r11), %ecx
	0x0f, 0xb6, 0x0c, 0x0a, //0x000010bd movzbl       (%rdx,%rcx), %ecx
	0x41, 0x0f, 0xb6, 0x7b, 0x06, //0x000010c1 movzbl       $6(%r11), %edi
	0x0f, 0xb6, 0x3c, 0x3a, //0x000010c6 movzbl       (%rdx,%rdi), %edi
	0x41, 0x0f, 0xb6, 0x5b, 0x07, //0x000010ca movzbl       $7(%r11), %ebx
	0x0f, 0xb6, 0x1c, 0x1a, //0x000010cf movzbl       (%rdx,%rbx), %ebx
	0x45, 0x89, 0xd0, //0x000010d3 movl         %r10d, %r8d
	0x45, 0x09, 0xe0, //0x000010d6 orl          %r12d, %r8d
	0x41, 0x89, 0xc7, //0x000010d9 movl         %eax, %r15d
	0x45, 0x09, 0xef, //0x000010dc orl          %r13d, %r15d
	0x45, 0x09, 0xc7, //0x000010df orl          %r8d, %r15d
	0x89, 0xce, //0x000010e2 movl         %ecx, %esi
	0x44, 0x09, 0xf6, //0x000010e4 orl          %r14d, %esi
	0x89, 0xfa, //0x000010e7 movl         %edi, %edx
	0x09, 0xf2, //0x000010e9 orl          %esi, %edx
	0x44, 0x09, 0xfa, //0x000010eb orl          %r15d, %edx
	0x89, 0xde, //0x000010ee movl         %ebx, %esi
	0x09, 0xd6, //0x000010f0 orl          %edx, %esi
	0x40, 0x80, 0xfe, 0xff, //0x000010f2 cmpb         $-1, %sil
	0x0f, 0x84, 0x54, 0x00, 0x00, 0x00, //0x000010f6 je           LBB0_232
	0x49, 0xc1, 0xe4, 0x3a, //0x000010fc shlq         $58, %r12
	0x49, 0xc1, 0xe2, 0x34, //0x00001100 shlq         $52, %r10
	0x4d, 0x09, 0xe2, //0x00001104 orq          %r12, %r10
	0x49, 0xc1, 0xe5, 0x2e, //0x00001107 shlq         $46, %r13
	0x48, 0xc1, 0xe0, 0x28, //0x0000110b shlq         $40, %rax
	0x4c, 0x09, 0xe8, //0x0000110f orq          %r13, %rax
	0x4c, 0x09, 0xd0, //0x00001112 orq          %r10, %rax
	0x49, 0xc1, 0xe6, 0x22, //0x00001115 shlq         $34, %r14
	0x48, 0xc1, 0xe1, 0x1c, //0x00001119 shlq         $28, %rcx
	0x4c, 0x09, 0xf1, //0x0000111d orq          %r14, %rcx
	0x48, 0xc1, 0xe7, 0x16, //0x00001120 shlq         $22, %rdi
	0x48, 0x09, 0xcf, //0x00001124 orq          %rcx, %rdi
	0x48, 0x09, 0xc7, //0x00001127 orq          %rax, %rdi
	0x48, 0xc1, 0xe3, 0x10, //0x0000112a shlq         $16, %rbx
	0x48, 0x09, 0xfb, //0x0000112e orq          %rdi, %rbx
	0x48, 0x0f, 0xcb, //0x00001131 bswapq       %rbx
	0x48, 0x8b, 0x55, 0xc0, //0x00001134 movq         $-64(%rbp), %rdx
	0x48, 0x89, 0x1a, //0x00001138 movq         %rbx, (%rdx)
	0x49, 0x83, 0xc3, 0x08, //0x0000113b addq         $8, %r11
	0x48, 0x83, 0xc2, 0x06, //0x0000113f addq         $6, %rdx
	0xe9, 0xb8, 0x06, 0x00, 0x00, //0x00001143 jmp          LBB0_384
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001148 .p2align 4, 0x90
	//0x00001150 LBB0_232
	0x4d, 0x39, 0xcb, //0x00001150 cmpq         %r9, %r11
	0x0f, 0x83, 0xcc, 0x00, 0x00, 0x00, //0x00001153 jae          LBB0_244
	0x4c, 0x89, 0xde, //0x00001159 movq         %r11, %rsi
	0x44, 0x8b, 0x75, 0xcc, //0x0000115c movl         $-52(%rbp), %r14d
	0x41, 0xf6, 0xc6, 0x08, //0x00001160 testb        $8, %r14b
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x00001164 je           LBB0_235
	0xe9, 0xd4, 0x00, 0x00, 0x00, //0x0000116a jmp          LBB0_247
	0x90, //0x0000116f .p2align 4, 0x90
	//0x00001170 LBB0_234
	0x48, 0x83, 0xc6, 0x01, //0x00001170 addq         $1, %rsi
	0x4c, 0x39, 0xce, //0x00001174 cmpq         %r9, %rsi
	0x0f, 0x83, 0xee, 0x01, 0x00, 0x00, //0x00001177 jae          LBB0_263
	//0x0000117d LBB0_235
	0x44, 0x0f, 0xb6, 0x06, //0x0000117d movzbl       (%rsi), %r8d
	0x49, 0x83, 0xf8, 0x0d, //0x00001181 cmpq         $13, %r8
	0x0f, 0x84, 0xe5, 0xff, 0xff, 0xff, //0x00001185 je           LBB0_234
	0x41, 0x80, 0xf8, 0x0a, //0x0000118b cmpb         $10, %r8b
	0x0f, 0x84, 0xdb, 0xff, 0xff, 0xff, //0x0000118f je           LBB0_234
	0x48, 0x8b, 0x45, 0xd0, //0x00001195 movq         $-48(%rbp), %rax
	0x42, 0x0f, 0xb6, 0x14, 0x00, //0x00001199 movzbl       (%rax,%r8), %edx
	0x48, 0x83, 0xc6, 0x01, //0x0000119e addq         $1, %rsi
	0x81, 0xfa, 0xff, 0x00, 0x00, 0x00, //0x000011a2 cmpl         $255, %edx
	0x0f, 0x84, 0xf0, 0x02, 0x00, 0x00, //0x000011a8 je           LBB0_287
	0xbb, 0x01, 0x00, 0x00, 0x00, //0x000011ae movl         $1, %ebx
	0x4c, 0x39, 0xce, //0x000011b3 cmpq         %r9, %rsi
	0x0f, 0x82, 0x21, 0x00, 0x00, 0x00, //0x000011b6 jb           LBB0_240
	0xe9, 0x59, 0x02, 0x00, 0x00, //0x000011bc jmp          LBB0_277
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000011c1 .p2align 4, 0x90
	//0x000011d0 LBB0_239
	0x48, 0x83, 0xc6, 0x01, //0x000011d0 addq         $1, %rsi
	0x4c, 0x39, 0xce, //0x000011d4 cmpq         %r9, %rsi
	0x0f, 0x83, 0xae, 0x04, 0x00, 0x00, //0x000011d7 jae          LBB0_320
	//0x000011dd LBB0_240
	0x44, 0x0f, 0xb6, 0x06, //0x000011dd movzbl       (%rsi), %r8d
	0x49, 0x83, 0xf8, 0x0d, //0x000011e1 cmpq         $13, %r8
	0x0f, 0x84, 0xe5, 0xff, 0xff, 0xff, //0x000011e5 je           LBB0_239
	0x41, 0x80, 0xf8, 0x0a, //0x000011eb cmpb         $10, %r8b
	0x0f, 0x84, 0xdb, 0xff, 0xff, 0xff, //0x000011ef je           LBB0_239
	0x48, 0x8b, 0x45, 0xd0, //0x000011f5 movq         $-48(%rbp), %rax
	0x42, 0x0f, 0xb6, 0x04, 0x00, //0x000011f9 movzbl       (%rax,%r8), %eax
	0x48, 0x83, 0xc6, 0x01, //0x000011fe addq         $1, %rsi
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00001202 cmpl         $255, %eax
	0x0f, 0x84, 0x1e, 0x08, 0x00, 0x00, //0x00001207 je           LBB0_374
	0xc1, 0xe2, 0x06, //0x0000120d shll         $6, %edx
	0x09, 0xc2, //0x00001210 orl          %eax, %edx
	0xbb, 0x02, 0x00, 0x00, 0x00, //0x00001212 movl         $2, %ebx
	0x4c, 0x39, 0xce, //0x00001217 cmpq         %r9, %rsi
	0x0f, 0x82, 0x75, 0x01, 0x00, 0x00, //0x0000121a jb           LBB0_267
	0xe9, 0xf5, 0x01, 0x00, 0x00, //0x00001220 jmp          LBB0_277
	//0x00001225 LBB0_244
	0x48, 0x8b, 0x55, 0xc0, //0x00001225 movq         $-64(%rbp), %rdx
	0xe9, 0xd2, 0x05, 0x00, 0x00, //0x00001229 jmp          LBB0_384
	//0x0000122e LBB0_261
	0x80, 0xf9, 0x6e, //0x0000122e cmpb         $110, %cl
	0x0f, 0x85, 0xae, 0x01, 0x00, 0x00, //0x00001231 jne          LBB0_272
	//0x00001237 LBB0_245
	0x48, 0x89, 0xc6, //0x00001237 movq         %rax, %rsi
	//0x0000123a LBB0_246
	0x4c, 0x39, 0xce, //0x0000123a cmpq         %r9, %rsi
	0x0f, 0x83, 0x28, 0x01, 0x00, 0x00, //0x0000123d jae          LBB0_263
	//0x00001243 LBB0_247
	0x48, 0x8d, 0x4e, 0x01, //0x00001243 leaq         $1(%rsi), %rcx
	0x0f, 0xb6, 0x06, //0x00001247 movzbl       (%rsi), %eax
	0x3c, 0x5c, //0x0000124a cmpb         $92, %al
	0x0f, 0x85, 0xfe, 0x00, 0x00, 0x00, //0x0000124c jne          LBB0_259
	0x48, 0x8d, 0x46, 0x02, //0x00001252 leaq         $2(%rsi), %rax
	0x41, 0xb0, 0xff, //0x00001256 movb         $-1, %r8b
	0x4c, 0x39, 0xc8, //0x00001259 cmpq         %r9, %rax
	0x0f, 0x87, 0x7b, 0x01, 0x00, 0x00, //0x0000125c ja           LBB0_271
	0x0f, 0xb6, 0x09, //0x00001262 movzbl       (%rcx), %ecx
	0x80, 0xf9, 0x71, //0x00001265 cmpb         $113, %cl
	0x0f, 0x8e, 0xc0, 0xff, 0xff, 0xff, //0x00001268 jle          LBB0_261
	0x80, 0xf9, 0x72, //0x0000126e cmpb         $114, %cl
	0x0f, 0x84, 0xc0, 0xff, 0xff, 0xff, //0x00001271 je           LBB0_245
	0x80, 0xf9, 0x75, //0x00001277 cmpb         $117, %cl
	0x0f, 0x85, 0x71, 0x01, 0x00, 0x00, //0x0000127a jne          LBB0_274
	0x4c, 0x89, 0xc9, //0x00001280 movq         %r9, %rcx
	0x48, 0x29, 0xc1, //0x00001283 subq         %rax, %rcx
	0x48, 0x83, 0xf9, 0x04, //0x00001286 cmpq         $4, %rcx
	0x0f, 0x8c, 0x61, 0x01, 0x00, 0x00, //0x0000128a jl           LBB0_274
	0x8b, 0x08, //0x00001290 movl         (%rax), %ecx
	0x89, 0xca, //0x00001292 movl         %ecx, %edx
	0xf7, 0xd2, //0x00001294 notl         %edx
	0x8d, 0x99, 0xd0, 0xcf, 0xcf, 0xcf, //0x00001296 leal         $-808464432(%rcx), %ebx
	0x81, 0xe2, 0x80, 0x80, 0x80, 0x80, //0x0000129c andl         $-2139062144, %edx
	0x85, 0xda, //0x000012a2 testl        %ebx, %edx
	0x0f, 0x85, 0x47, 0x01, 0x00, 0x00, //0x000012a4 jne          LBB0_274
	0x8d, 0x99, 0x19, 0x19, 0x19, 0x19, //0x000012aa leal         $421075225(%rcx), %ebx
	0x09, 0xcb, //0x000012b0 orl          %ecx, %ebx
	0xf7, 0xc3, 0x80, 0x80, 0x80, 0x80, //0x000012b2 testl        $-2139062144, %ebx
	0x0f, 0x85, 0x33, 0x01, 0x00, 0x00, //0x000012b8 jne          LBB0_274
	0x89, 0xcb, //0x000012be movl         %ecx, %ebx
	0x81, 0xe3, 0x7f, 0x7f, 0x7f, 0x7f, //0x000012c0 andl         $2139062143, %ebx
	0xbf, 0xc0, 0xc0, 0xc0, 0xc0, //0x000012c6 movl         $-1061109568, %edi
	0x29, 0xdf, //0x000012cb subl         %ebx, %edi
	0x44, 0x8d, 0x93, 0x46, 0x46, 0x46, 0x46, //0x000012cd leal         $1179010630(%rbx), %r10d
	0x21, 0xd7, //0x000012d4 andl         %edx, %edi
	0x44, 0x85, 0xd7, //0x000012d6 testl        %r10d, %edi
	0x0f, 0x85, 0x12, 0x01, 0x00, 0x00, //0x000012d9 jne          LBB0_274
	0xbf, 0xe0, 0xe0, 0xe0, 0xe0, //0x000012df movl         $-522133280, %edi
	0x29, 0xdf, //0x000012e4 subl         %ebx, %edi
	0x81, 0xc3, 0x39, 0x39, 0x39, 0x39, //0x000012e6 addl         $960051513, %ebx
	0x21, 0xfa, //0x000012ec andl         %edi, %edx
	0x85, 0xda, //0x000012ee testl        %ebx, %edx
	0x0f, 0x85, 0xfb, 0x00, 0x00, 0x00, //0x000012f0 jne          LBB0_274
	0x0f, 0xc9, //0x000012f6 bswapl       %ecx
	0x89, 0xc8, //0x000012f8 movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x000012fa shrl         $4, %eax
	0xf7, 0xd0, //0x000012fd notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x000012ff andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x00001304 leal         (%rax,%rax,8), %eax
	0x81, 0xe1, 0x0f, 0x0f, 0x0f, 0x0f, //0x00001307 andl         $252645135, %ecx
	0x01, 0xc1, //0x0000130d addl         %eax, %ecx
	0x89, 0xc8, //0x0000130f movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x00001311 shrl         $4, %eax
	0x09, 0xc8, //0x00001314 orl          %ecx, %eax
	0x89, 0xc1, //0x00001316 movl         %eax, %ecx
	0xc1, 0xe9, 0x08, //0x00001318 shrl         $8, %ecx
	0x81, 0xe1, 0x00, 0xff, 0x00, 0x00, //0x0000131b andl         $65280, %ecx
	0x89, 0xc2, //0x00001321 movl         %eax, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x00001323 andl         $128, %edx
	0x48, 0x83, 0xc6, 0x06, //0x00001329 addq         $6, %rsi
	0x09, 0xca, //0x0000132d orl          %ecx, %edx
	0x0f, 0x85, 0xbf, 0x00, 0x00, 0x00, //0x0000132f jne          LBB0_275
	0x3c, 0x0d, //0x00001335 cmpb         $13, %al
	0x0f, 0x85, 0x1e, 0x00, 0x00, 0x00, //0x00001337 jne          LBB0_260
	0xe9, 0xf8, 0xfe, 0xff, 0xff, //0x0000133d jmp          LBB0_246
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001342 .p2align 4, 0x90
	//0x00001350 LBB0_259
	0x48, 0x89, 0xce, //0x00001350 movq         %rcx, %rsi
	0x3c, 0x0d, //0x00001353 cmpb         $13, %al
	0x0f, 0x84, 0xdf, 0xfe, 0xff, 0xff, //0x00001355 je           LBB0_246
	//0x0000135b LBB0_260
	0x41, 0x89, 0xc0, //0x0000135b movl         %eax, %r8d
	0x3c, 0x0a, //0x0000135e cmpb         $10, %al
	0x0f, 0x84, 0xd4, 0xfe, 0xff, 0xff, //0x00001360 je           LBB0_246
	0xe9, 0x89, 0x00, 0x00, 0x00, //0x00001366 jmp          LBB0_275
	//0x0000136b LBB0_263
	0x31, 0xdb, //0x0000136b xorl         %ebx, %ebx
	0x31, 0xd2, //0x0000136d xorl         %edx, %edx
	//0x0000136f LBB0_264
	0x85, 0xdb, //0x0000136f testl        %ebx, %ebx
	0x0f, 0x85, 0xa3, 0x00, 0x00, 0x00, //0x00001371 jne          LBB0_277
	0x49, 0x89, 0xf3, //0x00001377 movq         %rsi, %r11
	0x48, 0x8b, 0x55, 0xc0, //0x0000137a movq         $-64(%rbp), %rdx
	0xe9, 0x7d, 0x04, 0x00, 0x00, //0x0000137e jmp          LBB0_384
	//0x00001383 LBB0_266
	0x48, 0x83, 0xc6, 0x01, //0x00001383 addq         $1, %rsi
	0xbb, 0x02, 0x00, 0x00, 0x00, //0x00001387 movl         $2, %ebx
	0x4c, 0x39, 0xce, //0x0000138c cmpq         %r9, %rsi
	0x0f, 0x83, 0xda, 0xff, 0xff, 0xff, //0x0000138f jae          LBB0_264
	//0x00001395 LBB0_267
	0x44, 0x0f, 0xb6, 0x06, //0x00001395 movzbl       (%rsi), %r8d
	0x49, 0x83, 0xf8, 0x0d, //0x00001399 cmpq         $13, %r8
	0x0f, 0x84, 0xe0, 0xff, 0xff, 0xff, //0x0000139d je           LBB0_266
	0x41, 0x80, 0xf8, 0x0a, //0x000013a3 cmpb         $10, %r8b
	0x0f, 0x84, 0xd6, 0xff, 0xff, 0xff, //0x000013a7 je           LBB0_266
	0x48, 0x8b, 0x45, 0xd0, //0x000013ad movq         $-48(%rbp), %rax
	0x42, 0x0f, 0xb6, 0x04, 0x00, //0x000013b1 movzbl       (%rax,%r8), %eax
	0x48, 0x83, 0xc6, 0x01, //0x000013b6 addq         $1, %rsi
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x000013ba cmpl         $255, %eax
	0x0f, 0x84, 0x03, 0x08, 0x00, 0x00, //0x000013bf je           LBB0_409
	0xc1, 0xe2, 0x06, //0x000013c5 shll         $6, %edx
	0x09, 0xc2, //0x000013c8 orl          %eax, %edx
	0xbb, 0x03, 0x00, 0x00, 0x00, //0x000013ca movl         $3, %ebx
	0x4c, 0x39, 0xce, //0x000013cf cmpq         %r9, %rsi
	0x0f, 0x82, 0x95, 0x04, 0x00, 0x00, //0x000013d2 jb           LBB0_347
	0xe9, 0x3d, 0x00, 0x00, 0x00, //0x000013d8 jmp          LBB0_277
	//0x000013dd LBB0_271
	0x48, 0x89, 0xce, //0x000013dd movq         %rcx, %rsi
	0xe9, 0x0f, 0x00, 0x00, 0x00, //0x000013e0 jmp          LBB0_275
	//0x000013e5 LBB0_272
	0x80, 0xf9, 0x2f, //0x000013e5 cmpb         $47, %cl
	0x0f, 0x85, 0x03, 0x00, 0x00, 0x00, //0x000013e8 jne          LBB0_274
	0x41, 0xb0, 0x2f, //0x000013ee movb         $47, %r8b
	//0x000013f1 LBB0_274
	0x48, 0x89, 0xc6, //0x000013f1 movq         %rax, %rsi
	//0x000013f4 LBB0_275
	0x41, 0x0f, 0xb6, 0xc0, //0x000013f4 movzbl       %r8b, %eax
	0x48, 0x8b, 0x4d, 0xd0, //0x000013f8 movq         $-48(%rbp), %rcx
	0x0f, 0xb6, 0x14, 0x01, //0x000013fc movzbl       (%rcx,%rax), %edx
	0x81, 0xfa, 0xff, 0x00, 0x00, 0x00, //0x00001400 cmpl         $255, %edx
	0x0f, 0x84, 0x92, 0x00, 0x00, 0x00, //0x00001406 je           LBB0_287
	0xbb, 0x01, 0x00, 0x00, 0x00, //0x0000140c movl         $1, %ebx
	0x4c, 0x39, 0xce, //0x00001411 cmpq         %r9, %rsi
	0x0f, 0x82, 0x49, 0x01, 0x00, 0x00, //0x00001414 jb           LBB0_303
	//0x0000141a LBB0_277
	0x41, 0xf6, 0xc6, 0x02, //0x0000141a testb        $2, %r14b
	0x0f, 0x94, 0xc0, //0x0000141e sete         %al
	0x83, 0xfb, 0x01, //0x00001421 cmpl         $1, %ebx
	0x0f, 0x94, 0xc1, //0x00001424 sete         %cl
	0x41, 0x89, 0xd6, //0x00001427 movl         %edx, %r14d
	0x4c, 0x39, 0xce, //0x0000142a cmpq         %r9, %rsi
	0x0f, 0x82, 0x11, 0x00, 0x00, 0x00, //0x0000142d jb           LBB0_280
	0x83, 0xfb, 0x04, //0x00001433 cmpl         $4, %ebx
	0x0f, 0x84, 0x08, 0x00, 0x00, 0x00, //0x00001436 je           LBB0_280
	0x08, 0xc8, //0x0000143c orb          %cl, %al
	0x0f, 0x85, 0x9e, 0x03, 0x00, 0x00, //0x0000143e jne          LBB0_383
	//0x00001444 LBB0_280
	0xb0, 0x04, //0x00001444 movb         $4, %al
	0x28, 0xd8, //0x00001446 subb         %bl, %al
	0x0f, 0xb6, 0xc0, //0x00001448 movzbl       %al, %eax
	0x01, 0xc0, //0x0000144b addl         %eax, %eax
	0x8d, 0x0c, 0x40, //0x0000144d leal         (%rax,%rax,2), %ecx
	0x44, 0x89, 0xf2, //0x00001450 movl         %r14d, %edx
	0xd3, 0xe2, //0x00001453 shll         %cl, %edx
	0x83, 0xfb, 0x02, //0x00001455 cmpl         $2, %ebx
	0x0f, 0x84, 0x24, 0x00, 0x00, 0x00, //0x00001458 je           LBB0_285
	0x83, 0xfb, 0x03, //0x0000145e cmpl         $3, %ebx
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x00001461 je           LBB0_284
	0x89, 0xd0, //0x00001467 movl         %edx, %eax
	0x83, 0xfb, 0x04, //0x00001469 cmpl         $4, %ebx
	0x48, 0x8b, 0x55, 0xc0, //0x0000146c movq         $-64(%rbp), %rdx
	0x0f, 0x85, 0x17, 0x00, 0x00, 0x00, //0x00001470 jne          LBB0_286
	0x88, 0x42, 0x02, //0x00001476 movb         %al, $2(%rdx)
	0x89, 0xc2, //0x00001479 movl         %eax, %edx
	//0x0000147b LBB0_284
	0x48, 0x8b, 0x45, 0xc0, //0x0000147b movq         $-64(%rbp), %rax
	0x88, 0x70, 0x01, //0x0000147f movb         %dh, $1(%rax)
	//0x00001482 LBB0_285
	0xc1, 0xea, 0x10, //0x00001482 shrl         $16, %edx
	0x89, 0xd0, //0x00001485 movl         %edx, %eax
	0x48, 0x8b, 0x55, 0xc0, //0x00001487 movq         $-64(%rbp), %rdx
	0x88, 0x02, //0x0000148b movb         %al, (%rdx)
	//0x0000148d LBB0_286
	0x89, 0xd8, //0x0000148d movl         %ebx, %eax
	0x48, 0x01, 0xc2, //0x0000148f addq         %rax, %rdx
	0x48, 0x83, 0xc2, 0xff, //0x00001492 addq         $-1, %rdx
	0x49, 0x89, 0xf3, //0x00001496 movq         %rsi, %r11
	0xe9, 0x62, 0x03, 0x00, 0x00, //0x00001499 jmp          LBB0_384
	//0x0000149e LBB0_287
	0x31, 0xdb, //0x0000149e xorl         %ebx, %ebx
	0x31, 0xd2, //0x000014a0 xorl         %edx, %edx
	//0x000014a2 LBB0_288
	0x41, 0xf6, 0xc6, 0x02, //0x000014a2 testb        $2, %r14b
	0x0f, 0x85, 0x36, 0x03, 0x00, 0x00, //0x000014a6 jne          LBB0_383
	0x41, 0x80, 0xf8, 0x3d, //0x000014ac cmpb         $61, %r8b
	0x0f, 0x85, 0x2c, 0x03, 0x00, 0x00, //0x000014b0 jne          LBB0_383
	0x83, 0xfb, 0x02, //0x000014b6 cmpl         $2, %ebx
	0x0f, 0x82, 0x23, 0x03, 0x00, 0x00, //0x000014b9 jb           LBB0_383
	0x41, 0x89, 0xd6, //0x000014bf movl         %edx, %r14d
	0x41, 0xbd, 0x05, 0x00, 0x00, 0x00, //0x000014c2 movl         $5, %r13d
	0x41, 0x29, 0xdd, //0x000014c8 subl         %ebx, %r13d
	0xf6, 0x45, 0xcc, 0x08, //0x000014cb testb        $8, $-52(%rbp)
	0x0f, 0x85, 0xc0, 0x01, 0x00, 0x00, //0x000014cf jne          LBB0_321
	0x4c, 0x39, 0xce, //0x000014d5 cmpq         %r9, %rsi
	0x0f, 0x83, 0x66, 0xff, 0xff, 0xff, //0x000014d8 jae          LBB0_280
	0x48, 0x8d, 0x4e, 0x01, //0x000014de leaq         $1(%rsi), %rcx
	0x48, 0x8b, 0x45, 0x88, //0x000014e2 movq         $-120(%rbp), %rax
	0x48, 0x29, 0xf0, //0x000014e6 subq         %rsi, %rax
	0x48, 0x83, 0xc6, 0x02, //0x000014e9 addq         $2, %rsi
	0x48, 0x89, 0xf7, //0x000014ed movq         %rsi, %rdi
	0x48, 0x89, 0xce, //0x000014f0 movq         %rcx, %rsi
	0xe9, 0x1a, 0x00, 0x00, 0x00, //0x000014f3 jmp          LBB0_295
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000014f8 .p2align 4, 0x90
	//0x00001500 LBB0_294
	0x48, 0x83, 0xc6, 0x01, //0x00001500 addq         $1, %rsi
	0x48, 0x83, 0xc7, 0x01, //0x00001504 addq         $1, %rdi
	0x48, 0x83, 0xc0, 0xff, //0x00001508 addq         $-1, %rax
	0x0f, 0x83, 0x23, 0x05, 0x00, 0x00, //0x0000150c jae          LBB0_375
	//0x00001512 LBB0_295
	0x0f, 0xb6, 0x4e, 0xff, //0x00001512 movzbl       $-1(%rsi), %ecx
	0x80, 0xf9, 0x0a, //0x00001516 cmpb         $10, %cl
	0x0f, 0x84, 0xe1, 0xff, 0xff, 0xff, //0x00001519 je           LBB0_294
	0x80, 0xf9, 0x0d, //0x0000151f cmpb         $13, %cl
	0x0f, 0x84, 0xd8, 0xff, 0xff, 0xff, //0x00001522 je           LBB0_294
	0x80, 0xf9, 0x3d, //0x00001528 cmpb         $61, %cl
	0x0f, 0x85, 0xb1, 0x02, 0x00, 0x00, //0x0000152b jne          LBB0_383
	0x41, 0x83, 0xfd, 0x02, //0x00001531 cmpl         $2, %r13d
	0x0f, 0x84, 0xa7, 0x02, 0x00, 0x00, //0x00001535 je           LBB0_383
	0x4c, 0x39, 0xce, //0x0000153b cmpq         %r9, %rsi
	0x0f, 0x83, 0x00, 0xff, 0xff, 0xff, //0x0000153e jae          LBB0_280
	0x48, 0x01, 0xc6, //0x00001544 addq         %rax, %rsi
	0x31, 0xc9, //0x00001547 xorl         %ecx, %ecx
	0xe9, 0xe7, 0x02, 0x00, 0x00, //0x00001549 jmp          LBB0_342
	//0x0000154e LBB0_317
	0x80, 0xf9, 0x6e, //0x0000154e cmpb         $110, %cl
	0x0f, 0x85, 0x50, 0x03, 0x00, 0x00, //0x00001551 jne          LBB0_351
	//0x00001557 LBB0_301
	0x48, 0x89, 0xc6, //0x00001557 movq         %rax, %rsi
	//0x0000155a LBB0_302
	0x4c, 0x39, 0xce, //0x0000155a cmpq         %r9, %rsi
	0x0f, 0x83, 0x28, 0x01, 0x00, 0x00, //0x0000155d jae          LBB0_320
	//0x00001563 LBB0_303
	0x48, 0x8d, 0x4e, 0x01, //0x00001563 leaq         $1(%rsi), %rcx
	0x0f, 0xb6, 0x06, //0x00001567 movzbl       (%rsi), %eax
	0x3c, 0x5c, //0x0000156a cmpb         $92, %al
	0x0f, 0x85, 0xfe, 0x00, 0x00, 0x00, //0x0000156c jne          LBB0_315
	0x48, 0x8d, 0x46, 0x02, //0x00001572 leaq         $2(%rsi), %rax
	0x41, 0xb0, 0xff, //0x00001576 movb         $-1, %r8b
	0x4c, 0x39, 0xc8, //0x00001579 cmpq         %r9, %rax
	0x0f, 0x87, 0xd1, 0x02, 0x00, 0x00, //0x0000157c ja           LBB0_345
	0x0f, 0xb6, 0x09, //0x00001582 movzbl       (%rcx), %ecx
	0x80, 0xf9, 0x71, //0x00001585 cmpb         $113, %cl
	0x0f, 0x8e, 0xc0, 0xff, 0xff, 0xff, //0x00001588 jle          LBB0_317
	0x80, 0xf9, 0x72, //0x0000158e cmpb         $114, %cl
	0x0f, 0x84, 0xc0, 0xff, 0xff, 0xff, //0x00001591 je           LBB0_301
	0x80, 0xf9, 0x75, //0x00001597 cmpb         $117, %cl
	0x0f, 0x85, 0x13, 0x03, 0x00, 0x00, //0x0000159a jne          LBB0_353
	0x4c, 0x89, 0xc9, //0x000015a0 movq         %r9, %rcx
	0x48, 0x29, 0xc1, //0x000015a3 subq         %rax, %rcx
	0x48, 0x83, 0xf9, 0x04, //0x000015a6 cmpq         $4, %rcx
	0x0f, 0x8c, 0x03, 0x03, 0x00, 0x00, //0x000015aa jl           LBB0_353
	0x41, 0x89, 0xd7, //0x000015b0 movl         %edx, %r15d
	0x8b, 0x08, //0x000015b3 movl         (%rax), %ecx
	0x89, 0xcb, //0x000015b5 movl         %ecx, %ebx
	0xf7, 0xd3, //0x000015b7 notl         %ebx
	0x8d, 0x91, 0xd0, 0xcf, 0xcf, 0xcf, //0x000015b9 leal         $-808464432(%rcx), %edx
	0x81, 0xe3, 0x80, 0x80, 0x80, 0x80, //0x000015bf andl         $-2139062144, %ebx
	0x85, 0xd3, //0x000015c5 testl        %edx, %ebx
	0x0f, 0x85, 0x4c, 0x02, 0x00, 0x00, //0x000015c7 jne          LBB0_340
	0x8d, 0x91, 0x19, 0x19, 0x19, 0x19, //0x000015cd leal         $421075225(%rcx), %edx
	0x09, 0xca, //0x000015d3 orl          %ecx, %edx
	0xf7, 0xc2, 0x80, 0x80, 0x80, 0x80, //0x000015d5 testl        $-2139062144, %edx
	0x0f, 0x85, 0x38, 0x02, 0x00, 0x00, //0x000015db jne          LBB0_340
	0x89, 0xca, //0x000015e1 movl         %ecx, %edx
	0x81, 0xe2, 0x7f, 0x7f, 0x7f, 0x7f, //0x000015e3 andl         $2139062143, %edx
	0xbf, 0xc0, 0xc0, 0xc0, 0xc0, //0x000015e9 movl         $-1061109568, %edi
	0x29, 0xd7, //0x000015ee subl         %edx, %edi
	0x44, 0x8d, 0x92, 0x46, 0x46, 0x46, 0x46, //0x000015f0 leal         $1179010630(%rdx), %r10d
	0x21, 0xdf, //0x000015f7 andl         %ebx, %edi
	0x44, 0x85, 0xd7, //0x000015f9 testl        %r10d, %edi
	0x0f, 0x85, 0x17, 0x02, 0x00, 0x00, //0x000015fc jne          LBB0_340
	0xbf, 0xe0, 0xe0, 0xe0, 0xe0, //0x00001602 movl         $-522133280, %edi
	0x29, 0xd7, //0x00001607 subl         %edx, %edi
	0x81, 0xc2, 0x39, 0x39, 0x39, 0x39, //0x00001609 addl         $960051513, %edx
	0x21, 0xfb, //0x0000160f andl         %edi, %ebx
	0x85, 0xd3, //0x00001611 testl        %edx, %ebx
	0x0f, 0x85, 0x00, 0x02, 0x00, 0x00, //0x00001613 jne          LBB0_340
	0x0f, 0xc9, //0x00001619 bswapl       %ecx
	0x89, 0xc8, //0x0000161b movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x0000161d shrl         $4, %eax
	0xf7, 0xd0, //0x00001620 notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x00001622 andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x00001627 leal         (%rax,%rax,8), %eax
	0x81, 0xe1, 0x0f, 0x0f, 0x0f, 0x0f, //0x0000162a andl         $252645135, %ecx
	0x01, 0xc1, //0x00001630 addl         %eax, %ecx
	0x89, 0xc8, //0x00001632 movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x00001634 shrl         $4, %eax
	0x09, 0xc8, //0x00001637 orl          %ecx, %eax
	0x89, 0xc1, //0x00001639 movl         %eax, %ecx
	0xc1, 0xe9, 0x08, //0x0000163b shrl         $8, %ecx
	0x81, 0xe1, 0x00, 0xff, 0x00, 0x00, //0x0000163e andl         $65280, %ecx
	0x89, 0xc2, //0x00001644 movl         %eax, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x00001646 andl         $128, %edx
	0x48, 0x83, 0xc6, 0x06, //0x0000164c addq         $6, %rsi
	0x09, 0xca, //0x00001650 orl          %ecx, %edx
	0x44, 0x89, 0xfa, //0x00001652 movl         %r15d, %edx
	0x0f, 0x85, 0x5b, 0x02, 0x00, 0x00, //0x00001655 jne          LBB0_354
	0x3c, 0x0d, //0x0000165b cmpb         $13, %al
	0x0f, 0x85, 0x18, 0x00, 0x00, 0x00, //0x0000165d jne          LBB0_316
	0xe9, 0xf2, 0xfe, 0xff, 0xff, //0x00001663 jmp          LBB0_302
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001668 .p2align 4, 0x90
	//0x00001670 LBB0_315
	0x48, 0x89, 0xce, //0x00001670 movq         %rcx, %rsi
	0x3c, 0x0d, //0x00001673 cmpb         $13, %al
	0x0f, 0x84, 0xdf, 0xfe, 0xff, 0xff, //0x00001675 je           LBB0_302
	//0x0000167b LBB0_316
	0x41, 0x89, 0xc0, //0x0000167b movl         %eax, %r8d
	0x3c, 0x0a, //0x0000167e cmpb         $10, %al
	0x0f, 0x84, 0xd4, 0xfe, 0xff, 0xff, //0x00001680 je           LBB0_302
	0xe9, 0x2b, 0x02, 0x00, 0x00, //0x00001686 jmp          LBB0_354
	//0x0000168b LBB0_320
	0xbb, 0x01, 0x00, 0x00, 0x00, //0x0000168b movl         $1, %ebx
	0xe9, 0xda, 0xfc, 0xff, 0xff, //0x00001690 jmp          LBB0_264
	//0x00001695 LBB0_321
	0x4c, 0x39, 0xce, //0x00001695 cmpq         %r9, %rsi
	0x0f, 0x83, 0xa6, 0xfd, 0xff, 0xff, //0x00001698 jae          LBB0_280
	0x48, 0x89, 0xf7, //0x0000169e movq         %rsi, %rdi
	0xe9, 0x1d, 0x00, 0x00, 0x00, //0x000016a1 jmp          LBB0_325
	//0x000016a6 LBB0_339
	0x4c, 0x89, 0xe7, //0x000016a6 movq         %r12, %rdi
	0x4c, 0x39, 0xcf, //0x000016a9 cmpq         %r9, %rdi
	0x0f, 0x82, 0x11, 0x00, 0x00, 0x00, //0x000016ac jb           LBB0_325
	0xe9, 0x91, 0x03, 0x00, 0x00, //0x000016b2 jmp          LBB0_378
	//0x000016b7 LBB0_323
	0x48, 0x89, 0xf7, //0x000016b7 movq         %rsi, %rdi
	0x4c, 0x39, 0xcf, //0x000016ba cmpq         %r9, %rdi
	0x0f, 0x83, 0x85, 0x03, 0x00, 0x00, //0x000016bd jae          LBB0_378
	//0x000016c3 LBB0_325
	0x4c, 0x8d, 0x67, 0x01, //0x000016c3 leaq         $1(%rdi), %r12
	0x0f, 0xb6, 0x0f, //0x000016c7 movzbl       (%rdi), %ecx
	0x80, 0xf9, 0x5c, //0x000016ca cmpb         $92, %cl
	0x0f, 0x85, 0xe7, 0x00, 0x00, 0x00, //0x000016cd jne          LBB0_336
	0x48, 0x8d, 0x77, 0x02, //0x000016d3 leaq         $2(%rdi), %rsi
	0x4c, 0x39, 0xce, //0x000016d7 cmpq         %r9, %rsi
	0x0f, 0x87, 0xff, 0x00, 0x00, 0x00, //0x000016da ja           LBB0_382
	0x41, 0x0f, 0xb6, 0x04, 0x24, //0x000016e0 movzbl       (%r12), %eax
	0x3c, 0x6e, //0x000016e5 cmpb         $110, %al
	0x0f, 0x84, 0xca, 0xff, 0xff, 0xff, //0x000016e7 je           LBB0_323
	0x3c, 0x72, //0x000016ed cmpb         $114, %al
	0x0f, 0x84, 0xc2, 0xff, 0xff, 0xff, //0x000016ef je           LBB0_323
	0x3c, 0x75, //0x000016f5 cmpb         $117, %al
	0x0f, 0x85, 0xe5, 0x00, 0x00, 0x00, //0x000016f7 jne          LBB0_383
	0x4c, 0x89, 0xc8, //0x000016fd movq         %r9, %rax
	0x48, 0x29, 0xf0, //0x00001700 subq         %rsi, %rax
	0x48, 0x83, 0xf8, 0x04, //0x00001703 cmpq         $4, %rax
	0x0f, 0x8c, 0xd5, 0x00, 0x00, 0x00, //0x00001707 jl           LBB0_383
	0x8b, 0x16, //0x0000170d movl         (%rsi), %edx
	0x89, 0xd1, //0x0000170f movl         %edx, %ecx
	0xf7, 0xd1, //0x00001711 notl         %ecx
	0x8d, 0x82, 0xd0, 0xcf, 0xcf, 0xcf, //0x00001713 leal         $-808464432(%rdx), %eax
	0x81, 0xe1, 0x80, 0x80, 0x80, 0x80, //0x00001719 andl         $-2139062144, %ecx
	0x85, 0xc1, //0x0000171f testl        %eax, %ecx
	0x0f, 0x85, 0xbb, 0x00, 0x00, 0x00, //0x00001721 jne          LBB0_383
	0x8d, 0x82, 0x19, 0x19, 0x19, 0x19, //0x00001727 leal         $421075225(%rdx), %eax
	0x09, 0xd0, //0x0000172d orl          %edx, %eax
	0xa9, 0x80, 0x80, 0x80, 0x80, //0x0000172f testl        $-2139062144, %eax
	0x0f, 0x85, 0xa8, 0x00, 0x00, 0x00, //0x00001734 jne          LBB0_383
	0x89, 0xd0, //0x0000173a movl         %edx, %eax
	0x25, 0x7f, 0x7f, 0x7f, 0x7f, //0x0000173c andl         $2139062143, %eax
	0x41, 0xb8, 0xc0, 0xc0, 0xc0, 0xc0, //0x00001741 movl         $-1061109568, %r8d
	0x41, 0x29, 0xc0, //0x00001747 subl         %eax, %r8d
	0x44, 0x8d, 0x90, 0x46, 0x46, 0x46, 0x46, //0x0000174a leal         $1179010630(%rax), %r10d
	0x41, 0x21, 0xc8, //0x00001751 andl         %ecx, %r8d
	0x45, 0x85, 0xd0, //0x00001754 testl        %r10d, %r8d
	0x0f, 0x85, 0x85, 0x00, 0x00, 0x00, //0x00001757 jne          LBB0_383
	0x41, 0xb8, 0xe0, 0xe0, 0xe0, 0xe0, //0x0000175d movl         $-522133280, %r8d
	0x41, 0x29, 0xc0, //0x00001763 subl         %eax, %r8d
	0x05, 0x39, 0x39, 0x39, 0x39, //0x00001766 addl         $960051513, %eax
	0x44, 0x21, 0xc1, //0x0000176b andl         %r8d, %ecx
	0x85, 0xc1, //0x0000176e testl        %eax, %ecx
	0x0f, 0x85, 0x6c, 0x00, 0x00, 0x00, //0x00001770 jne          LBB0_383
	0x0f, 0xca, //0x00001776 bswapl       %edx
	0x89, 0xd0, //0x00001778 movl         %edx, %eax
	0xc1, 0xe8, 0x04, //0x0000177a shrl         $4, %eax
	0xf7, 0xd0, //0x0000177d notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x0000177f andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x00001784 leal         (%rax,%rax,8), %eax
	0x81, 0xe2, 0x0f, 0x0f, 0x0f, 0x0f, //0x00001787 andl         $252645135, %edx
	0x01, 0xc2, //0x0000178d addl         %eax, %edx
	0x89, 0xd1, //0x0000178f movl         %edx, %ecx
	0xc1, 0xe9, 0x04, //0x00001791 shrl         $4, %ecx
	0x09, 0xd1, //0x00001794 orl          %edx, %ecx
	0x89, 0xc8, //0x00001796 movl         %ecx, %eax
	0xc1, 0xe8, 0x08, //0x00001798 shrl         $8, %eax
	0x25, 0x00, 0xff, 0x00, 0x00, //0x0000179b andl         $65280, %eax
	0x89, 0xca, //0x000017a0 movl         %ecx, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x000017a2 andl         $128, %edx
	0x48, 0x83, 0xc7, 0x06, //0x000017a8 addq         $6, %rdi
	0x09, 0xc2, //0x000017ac orl          %eax, %edx
	0x49, 0x89, 0xfc, //0x000017ae movq         %rdi, %r12
	0x48, 0x89, 0xfe, //0x000017b1 movq         %rdi, %rsi
	0x0f, 0x85, 0x28, 0x00, 0x00, 0x00, //0x000017b4 jne          LBB0_383
	//0x000017ba LBB0_336
	0x80, 0xf9, 0x0a, //0x000017ba cmpb         $10, %cl
	0x0f, 0x84, 0xe3, 0xfe, 0xff, 0xff, //0x000017bd je           LBB0_339
	0x80, 0xf9, 0x0d, //0x000017c3 cmpb         $13, %cl
	0x0f, 0x84, 0xda, 0xfe, 0xff, 0xff, //0x000017c6 je           LBB0_339
	0x80, 0xf9, 0x3d, //0x000017cc cmpb         $61, %cl
	0x0f, 0x85, 0x0a, 0x00, 0x00, 0x00, //0x000017cf jne          LBB0_382
	0x41, 0x83, 0xfd, 0x02, //0x000017d5 cmpl         $2, %r13d
	0x0f, 0x85, 0x06, 0x04, 0x00, 0x00, //0x000017d9 jne          LBB0_412
	//0x000017df LBB0_382
	0x4c, 0x89, 0xe6, //0x000017df movq         %r12, %rsi
	//0x000017e2 LBB0_383
	0x31, 0xc9, //0x000017e2 xorl         %ecx, %ecx
	0x4c, 0x39, 0xce, //0x000017e4 cmpq         %r9, %rsi
	0x0f, 0x94, 0xc1, //0x000017e7 sete         %cl
	0x48, 0x01, 0xf1, //0x000017ea addq         %rsi, %rcx
	0x4c, 0x39, 0xd9, //0x000017ed cmpq         %r11, %rcx
	0x48, 0x8b, 0x55, 0xc0, //0x000017f0 movq         $-64(%rbp), %rdx
	0x0f, 0x85, 0x69, 0x1e, 0x00, 0x00, //0x000017f4 jne          LBB0_864
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000017fa .p2align 4, 0x90
	//0x00001800 LBB0_384
	0x4c, 0x3b, 0x5d, 0xa8, //0x00001800 cmpq         $-88(%rbp), %r11
	0x0f, 0x87, 0x4a, 0x05, 0x00, 0x00, //0x00001804 ja           LBB0_441
	0x48, 0x3b, 0x55, 0x90, //0x0000180a cmpq         $-112(%rbp), %rdx
	0x0f, 0x86, 0x6c, 0xf8, 0xff, 0xff, //0x0000180e jbe          LBB0_230
	0xe9, 0x3b, 0x05, 0x00, 0x00, //0x00001814 jmp          LBB0_441
	//0x00001819 LBB0_340
	0x48, 0x89, 0xc6, //0x00001819 movq         %rax, %rsi
	0x44, 0x89, 0xfa, //0x0000181c movl         %r15d, %edx
	0xe9, 0x92, 0x00, 0x00, 0x00, //0x0000181f jmp          LBB0_354
	//0x00001824 LBB0_341
	0x48, 0x83, 0xc7, 0x01, //0x00001824 addq         $1, %rdi
	0x48, 0x83, 0xc1, 0x01, //0x00001828 addq         $1, %rcx
	0x48, 0x39, 0xc8, //0x0000182c cmpq         %rcx, %rax
	0x0f, 0x84, 0x0f, 0xfc, 0xff, 0xff, //0x0000182f je           LBB0_280
	//0x00001835 LBB0_342
	0x0f, 0xb6, 0x57, 0xff, //0x00001835 movzbl       $-1(%rdi), %edx
	0x80, 0xfa, 0x0d, //0x00001839 cmpb         $13, %dl
	0x0f, 0x84, 0xe2, 0xff, 0xff, 0xff, //0x0000183c je           LBB0_341
	0x80, 0xfa, 0x0a, //0x00001842 cmpb         $10, %dl
	0x0f, 0x84, 0xd9, 0xff, 0xff, 0xff, //0x00001845 je           LBB0_341
	0x48, 0x89, 0xfe, //0x0000184b movq         %rdi, %rsi
	0xe9, 0x8f, 0xff, 0xff, 0xff, //0x0000184e jmp          LBB0_383
	//0x00001853 LBB0_345
	0x48, 0x89, 0xce, //0x00001853 movq         %rcx, %rsi
	0xe9, 0x5b, 0x00, 0x00, 0x00, //0x00001856 jmp          LBB0_354
	//0x0000185b LBB0_346
	0x48, 0x83, 0xc6, 0x01, //0x0000185b addq         $1, %rsi
	0xbb, 0x03, 0x00, 0x00, 0x00, //0x0000185f movl         $3, %ebx
	0x4c, 0x39, 0xce, //0x00001864 cmpq         %r9, %rsi
	0x0f, 0x83, 0x02, 0xfb, 0xff, 0xff, //0x00001867 jae          LBB0_264
	//0x0000186d LBB0_347
	0x44, 0x0f, 0xb6, 0x06, //0x0000186d movzbl       (%rsi), %r8d
	0x49, 0x83, 0xf8, 0x0d, //0x00001871 cmpq         $13, %r8
	0x0f, 0x84, 0xe0, 0xff, 0xff, 0xff, //0x00001875 je           LBB0_346
	0x41, 0x80, 0xf8, 0x0a, //0x0000187b cmpb         $10, %r8b
	0x0f, 0x84, 0xd6, 0xff, 0xff, 0xff, //0x0000187f je           LBB0_346
	0x48, 0x8b, 0x45, 0xd0, //0x00001885 movq         $-48(%rbp), %rax
	0x42, 0x0f, 0xb6, 0x04, 0x00, //0x00001889 movzbl       (%rax,%r8), %eax
	0x48, 0x83, 0xc6, 0x01, //0x0000188e addq         $1, %rsi
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00001892 cmpl         $255, %eax
	0x0f, 0x85, 0xa0, 0x04, 0x00, 0x00, //0x00001897 jne          LBB0_437
	//0x0000189d LBB0_438
	0xbb, 0x03, 0x00, 0x00, 0x00, //0x0000189d movl         $3, %ebx
	0xe9, 0xfb, 0xfb, 0xff, 0xff, //0x000018a2 jmp          LBB0_288
	//0x000018a7 LBB0_351
	0x80, 0xf9, 0x2f, //0x000018a7 cmpb         $47, %cl
	0x0f, 0x85, 0x03, 0x00, 0x00, 0x00, //0x000018aa jne          LBB0_353
	0x41, 0xb0, 0x2f, //0x000018b0 movb         $47, %r8b
	//0x000018b3 LBB0_353
	0x48, 0x89, 0xc6, //0x000018b3 movq         %rax, %rsi
	//0x000018b6 LBB0_354
	0x41, 0x0f, 0xb6, 0xc0, //0x000018b6 movzbl       %r8b, %eax
	0x48, 0x8b, 0x4d, 0xd0, //0x000018ba movq         $-48(%rbp), %rcx
	0x0f, 0xb6, 0x04, 0x01, //0x000018be movzbl       (%rcx,%rax), %eax
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x000018c2 cmpl         $255, %eax
	0x0f, 0x84, 0x5e, 0x01, 0x00, 0x00, //0x000018c7 je           LBB0_374
	0xc1, 0xe2, 0x06, //0x000018cd shll         $6, %edx
	0x09, 0xc2, //0x000018d0 orl          %eax, %edx
	0xbb, 0x02, 0x00, 0x00, 0x00, //0x000018d2 movl         $2, %ebx
	0x4c, 0x39, 0xce, //0x000018d7 cmpq         %r9, %rsi
	0x0f, 0x82, 0x1f, 0x00, 0x00, 0x00, //0x000018da jb           LBB0_358
	0xe9, 0x35, 0xfb, 0xff, 0xff, //0x000018e0 jmp          LBB0_277
	//0x000018e5 LBB0_372
	0x80, 0xf9, 0x6e, //0x000018e5 cmpb         $110, %cl
	0x0f, 0x85, 0x6a, 0x01, 0x00, 0x00, //0x000018e8 jne          LBB0_386
	//0x000018ee LBB0_356
	0x48, 0x89, 0xc6, //0x000018ee movq         %rax, %rsi
	//0x000018f1 LBB0_357
	0xbb, 0x02, 0x00, 0x00, 0x00, //0x000018f1 movl         $2, %ebx
	0x4c, 0x39, 0xce, //0x000018f6 cmpq         %r9, %rsi
	0x0f, 0x83, 0x70, 0xfa, 0xff, 0xff, //0x000018f9 jae          LBB0_264
	//0x000018ff LBB0_358
	0x48, 0x8d, 0x4e, 0x01, //0x000018ff leaq         $1(%rsi), %rcx
	0x0f, 0xb6, 0x06, //0x00001903 movzbl       (%rsi), %eax
	0x3c, 0x5c, //0x00001906 cmpb         $92, %al
	0x0f, 0x85, 0x02, 0x01, 0x00, 0x00, //0x00001908 jne          LBB0_370
	0x48, 0x8d, 0x46, 0x02, //0x0000190e leaq         $2(%rsi), %rax
	0x41, 0xb0, 0xff, //0x00001912 movb         $-1, %r8b
	0x4c, 0x39, 0xc8, //0x00001915 cmpq         %r9, %rax
	0x0f, 0x87, 0x32, 0x01, 0x00, 0x00, //0x00001918 ja           LBB0_379
	0x0f, 0xb6, 0x09, //0x0000191e movzbl       (%rcx), %ecx
	0x80, 0xf9, 0x71, //0x00001921 cmpb         $113, %cl
	0x0f, 0x8e, 0xbb, 0xff, 0xff, 0xff, //0x00001924 jle          LBB0_372
	0x80, 0xf9, 0x72, //0x0000192a cmpb         $114, %cl
	0x0f, 0x84, 0xbb, 0xff, 0xff, 0xff, //0x0000192d je           LBB0_356
	0x80, 0xf9, 0x75, //0x00001933 cmpb         $117, %cl
	0x0f, 0x85, 0x28, 0x01, 0x00, 0x00, //0x00001936 jne          LBB0_388
	0x4c, 0x89, 0xc9, //0x0000193c movq         %r9, %rcx
	0x48, 0x29, 0xc1, //0x0000193f subq         %rax, %rcx
	0x48, 0x83, 0xf9, 0x04, //0x00001942 cmpq         $4, %rcx
	0x0f, 0x8c, 0x18, 0x01, 0x00, 0x00, //0x00001946 jl           LBB0_388
	0x41, 0x89, 0xd7, //0x0000194c movl         %edx, %r15d
	0x8b, 0x08, //0x0000194f movl         (%rax), %ecx
	0x89, 0xcb, //0x00001951 movl         %ecx, %ebx
	0xf7, 0xd3, //0x00001953 notl         %ebx
	0x8d, 0x91, 0xd0, 0xcf, 0xcf, 0xcf, //0x00001955 leal         $-808464432(%rcx), %edx
	0x81, 0xe3, 0x80, 0x80, 0x80, 0x80, //0x0000195b andl         $-2139062144, %ebx
	0x85, 0xd3, //0x00001961 testl        %edx, %ebx
	0x0f, 0x85, 0xd4, 0x00, 0x00, 0x00, //0x00001963 jne          LBB0_377
	0x8d, 0x91, 0x19, 0x19, 0x19, 0x19, //0x00001969 leal         $421075225(%rcx), %edx
	0x09, 0xca, //0x0000196f orl          %ecx, %edx
	0xf7, 0xc2, 0x80, 0x80, 0x80, 0x80, //0x00001971 testl        $-2139062144, %edx
	0x0f, 0x85, 0xc0, 0x00, 0x00, 0x00, //0x00001977 jne          LBB0_377
	0x89, 0xca, //0x0000197d movl         %ecx, %edx
	0x81, 0xe2, 0x7f, 0x7f, 0x7f, 0x7f, //0x0000197f andl         $2139062143, %edx
	0xbf, 0xc0, 0xc0, 0xc0, 0xc0, //0x00001985 movl         $-1061109568, %edi
	0x29, 0xd7, //0x0000198a subl         %edx, %edi
	0x44, 0x8d, 0x92, 0x46, 0x46, 0x46, 0x46, //0x0000198c leal         $1179010630(%rdx), %r10d
	0x21, 0xdf, //0x00001993 andl         %ebx, %edi
	0x44, 0x85, 0xd7, //0x00001995 testl        %r10d, %edi
	0x0f, 0x85, 0x9f, 0x00, 0x00, 0x00, //0x00001998 jne          LBB0_377
	0xbf, 0xe0, 0xe0, 0xe0, 0xe0, //0x0000199e movl         $-522133280, %edi
	0x29, 0xd7, //0x000019a3 subl         %edx, %edi
	0x81, 0xc2, 0x39, 0x39, 0x39, 0x39, //0x000019a5 addl         $960051513, %edx
	0x21, 0xfb, //0x000019ab andl         %edi, %ebx
	0x85, 0xd3, //0x000019ad testl        %edx, %ebx
	0x0f, 0x85, 0x88, 0x00, 0x00, 0x00, //0x000019af jne          LBB0_377
	0x0f, 0xc9, //0x000019b5 bswapl       %ecx
	0x89, 0xc8, //0x000019b7 movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x000019b9 shrl         $4, %eax
	0xf7, 0xd0, //0x000019bc notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x000019be andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x000019c3 leal         (%rax,%rax,8), %eax
	0x81, 0xe1, 0x0f, 0x0f, 0x0f, 0x0f, //0x000019c6 andl         $252645135, %ecx
	0x01, 0xc1, //0x000019cc addl         %eax, %ecx
	0x89, 0xc8, //0x000019ce movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x000019d0 shrl         $4, %eax
	0x09, 0xc8, //0x000019d3 orl          %ecx, %eax
	0x89, 0xc1, //0x000019d5 movl         %eax, %ecx
	0xc1, 0xe9, 0x08, //0x000019d7 shrl         $8, %ecx
	0x81, 0xe1, 0x00, 0xff, 0x00, 0x00, //0x000019da andl         $65280, %ecx
	0x89, 0xc2, //0x000019e0 movl         %eax, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x000019e2 andl         $128, %edx
	0x48, 0x83, 0xc6, 0x06, //0x000019e8 addq         $6, %rsi
	0x09, 0xca, //0x000019ec orl          %ecx, %edx
	0x44, 0x89, 0xfa, //0x000019ee movl         %r15d, %edx
	0x0f, 0x85, 0x70, 0x00, 0x00, 0x00, //0x000019f1 jne          LBB0_389
	0x3c, 0x0d, //0x000019f7 cmpb         $13, %al
	0x0f, 0x85, 0x1c, 0x00, 0x00, 0x00, //0x000019f9 jne          LBB0_371
	0xe9, 0xed, 0xfe, 0xff, 0xff, //0x000019ff jmp          LBB0_357
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001a04 .p2align 4, 0x90
	//0x00001a10 LBB0_370
	0x48, 0x89, 0xce, //0x00001a10 movq         %rcx, %rsi
	0x3c, 0x0d, //0x00001a13 cmpb         $13, %al
	0x0f, 0x84, 0xd6, 0xfe, 0xff, 0xff, //0x00001a15 je           LBB0_357
	//0x00001a1b LBB0_371
	0x41, 0x89, 0xc0, //0x00001a1b movl         %eax, %r8d
	0x3c, 0x0a, //0x00001a1e cmpb         $10, %al
	0x0f, 0x84, 0xcb, 0xfe, 0xff, 0xff, //0x00001a20 je           LBB0_357
	0xe9, 0x3c, 0x00, 0x00, 0x00, //0x00001a26 jmp          LBB0_389
	//0x00001a2b LBB0_374
	0xbb, 0x01, 0x00, 0x00, 0x00, //0x00001a2b movl         $1, %ebx
	0xe9, 0x6d, 0xfa, 0xff, 0xff, //0x00001a30 jmp          LBB0_288
	//0x00001a35 LBB0_375
	0x4c, 0x89, 0xce, //0x00001a35 movq         %r9, %rsi
	0xe9, 0x07, 0xfa, 0xff, 0xff, //0x00001a38 jmp          LBB0_280
	//0x00001a3d LBB0_377
	0x48, 0x89, 0xc6, //0x00001a3d movq         %rax, %rsi
	0x44, 0x89, 0xfa, //0x00001a40 movl         %r15d, %edx
	0xe9, 0x1f, 0x00, 0x00, 0x00, //0x00001a43 jmp          LBB0_389
	//0x00001a48 LBB0_378
	0x48, 0x89, 0xfe, //0x00001a48 movq         %rdi, %rsi
	0xe9, 0xf4, 0xf9, 0xff, 0xff, //0x00001a4b jmp          LBB0_280
	//0x00001a50 LBB0_379
	0x48, 0x89, 0xce, //0x00001a50 movq         %rcx, %rsi
	0xe9, 0x0f, 0x00, 0x00, 0x00, //0x00001a53 jmp          LBB0_389
	//0x00001a58 LBB0_386
	0x80, 0xf9, 0x2f, //0x00001a58 cmpb         $47, %cl
	0x0f, 0x85, 0x03, 0x00, 0x00, 0x00, //0x00001a5b jne          LBB0_388
	0x41, 0xb0, 0x2f, //0x00001a61 movb         $47, %r8b
	//0x00001a64 LBB0_388
	0x48, 0x89, 0xc6, //0x00001a64 movq         %rax, %rsi
	//0x00001a67 LBB0_389
	0x41, 0x0f, 0xb6, 0xc0, //0x00001a67 movzbl       %r8b, %eax
	0x48, 0x8b, 0x4d, 0xd0, //0x00001a6b movq         $-48(%rbp), %rcx
	0x0f, 0xb6, 0x04, 0x01, //0x00001a6f movzbl       (%rcx,%rax), %eax
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00001a73 cmpl         $255, %eax
	0x0f, 0x84, 0x4a, 0x01, 0x00, 0x00, //0x00001a78 je           LBB0_409
	0xc1, 0xe2, 0x06, //0x00001a7e shll         $6, %edx
	0x09, 0xc2, //0x00001a81 orl          %eax, %edx
	0xbb, 0x03, 0x00, 0x00, 0x00, //0x00001a83 movl         $3, %ebx
	0x4c, 0x39, 0xce, //0x00001a88 cmpq         %r9, %rsi
	0x0f, 0x82, 0x1f, 0x00, 0x00, 0x00, //0x00001a8b jb           LBB0_393
	0xe9, 0x84, 0xf9, 0xff, 0xff, //0x00001a91 jmp          LBB0_277
	//0x00001a96 LBB0_407
	0x80, 0xf9, 0x6e, //0x00001a96 cmpb         $110, %cl
	0x0f, 0x85, 0x78, 0x02, 0x00, 0x00, //0x00001a99 jne          LBB0_433
	//0x00001a9f LBB0_391
	0x48, 0x89, 0xc6, //0x00001a9f movq         %rax, %rsi
	//0x00001aa2 LBB0_392
	0xbb, 0x03, 0x00, 0x00, 0x00, //0x00001aa2 movl         $3, %ebx
	0x4c, 0x39, 0xce, //0x00001aa7 cmpq         %r9, %rsi
	0x0f, 0x83, 0xbf, 0xf8, 0xff, 0xff, //0x00001aaa jae          LBB0_264
	//0x00001ab0 LBB0_393
	0x48, 0x8d, 0x4e, 0x01, //0x00001ab0 leaq         $1(%rsi), %rcx
	0x0f, 0xb6, 0x06, //0x00001ab4 movzbl       (%rsi), %eax
	0x3c, 0x5c, //0x00001ab7 cmpb         $92, %al
	0x0f, 0x85, 0xee, 0x00, 0x00, 0x00, //0x00001ab9 jne          LBB0_404
	0x48, 0x8d, 0x46, 0x02, //0x00001abf leaq         $2(%rsi), %rax
	0x41, 0xb0, 0xff, //0x00001ac3 movb         $-1, %r8b
	0x4c, 0x39, 0xc8, //0x00001ac6 cmpq         %r9, %rax
	0x0f, 0x87, 0x0e, 0x01, 0x00, 0x00, //0x00001ac9 ja           LBB0_411
	0x0f, 0xb6, 0x09, //0x00001acf movzbl       (%rcx), %ecx
	0x80, 0xf9, 0x71, //0x00001ad2 cmpb         $113, %cl
	0x0f, 0x8e, 0xbb, 0xff, 0xff, 0xff, //0x00001ad5 jle          LBB0_407
	0x80, 0xf9, 0x72, //0x00001adb cmpb         $114, %cl
	0x0f, 0x84, 0xbb, 0xff, 0xff, 0xff, //0x00001ade je           LBB0_391
	0x80, 0xf9, 0x75, //0x00001ae4 cmpb         $117, %cl
	0x0f, 0x85, 0x36, 0x02, 0x00, 0x00, //0x00001ae7 jne          LBB0_435
	0x4c, 0x89, 0xc9, //0x00001aed movq         %r9, %rcx
	0x48, 0x29, 0xc1, //0x00001af0 subq         %rax, %rcx
	0x48, 0x83, 0xf9, 0x04, //0x00001af3 cmpq         $4, %rcx
	0x0f, 0x8c, 0x26, 0x02, 0x00, 0x00, //0x00001af7 jl           LBB0_435
	0x41, 0x89, 0xd7, //0x00001afd movl         %edx, %r15d
	0x8b, 0x08, //0x00001b00 movl         (%rax), %ecx
	0x89, 0xcb, //0x00001b02 movl         %ecx, %ebx
	0xf7, 0xd3, //0x00001b04 notl         %ebx
	0x8d, 0x91, 0xd0, 0xcf, 0xcf, 0xcf, //0x00001b06 leal         $-808464432(%rcx), %edx
	0x81, 0xe3, 0x80, 0x80, 0x80, 0x80, //0x00001b0c andl         $-2139062144, %ebx
	0x85, 0xd3, //0x00001b12 testl        %edx, %ebx
	0x0f, 0x85, 0xb8, 0x00, 0x00, 0x00, //0x00001b14 jne          LBB0_410
	0x8d, 0x91, 0x19, 0x19, 0x19, 0x19, //0x00001b1a leal         $421075225(%rcx), %edx
	0x09, 0xca, //0x00001b20 orl          %ecx, %edx
	0xf7, 0xc2, 0x80, 0x80, 0x80, 0x80, //0x00001b22 testl        $-2139062144, %edx
	0x0f, 0x85, 0xa4, 0x00, 0x00, 0x00, //0x00001b28 jne          LBB0_410
	0x89, 0xca, //0x00001b2e movl         %ecx, %edx
	0x81, 0xe2, 0x7f, 0x7f, 0x7f, 0x7f, //0x00001b30 andl         $2139062143, %edx
	0xbf, 0xc0, 0xc0, 0xc0, 0xc0, //0x00001b36 movl         $-1061109568, %edi
	0x29, 0xd7, //0x00001b3b subl         %edx, %edi
	0x44, 0x8d, 0x92, 0x46, 0x46, 0x46, 0x46, //0x00001b3d leal         $1179010630(%rdx), %r10d
	0x21, 0xdf, //0x00001b44 andl         %ebx, %edi
	0x44, 0x85, 0xd7, //0x00001b46 testl        %r10d, %edi
	0x0f, 0x85, 0x83, 0x00, 0x00, 0x00, //0x00001b49 jne          LBB0_410
	0xbf, 0xe0, 0xe0, 0xe0, 0xe0, //0x00001b4f movl         $-522133280, %edi
	0x29, 0xd7, //0x00001b54 subl         %edx, %edi
	0x81, 0xc2, 0x39, 0x39, 0x39, 0x39, //0x00001b56 addl         $960051513, %edx
	0x21, 0xfb, //0x00001b5c andl         %edi, %ebx
	0x85, 0xd3, //0x00001b5e testl        %edx, %ebx
	0x0f, 0x85, 0x6c, 0x00, 0x00, 0x00, //0x00001b60 jne          LBB0_410
	0x0f, 0xc9, //0x00001b66 bswapl       %ecx
	0x89, 0xc8, //0x00001b68 movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x00001b6a shrl         $4, %eax
	0xf7, 0xd0, //0x00001b6d notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x00001b6f andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x00001b74 leal         (%rax,%rax,8), %eax
	0x81, 0xe1, 0x0f, 0x0f, 0x0f, 0x0f, //0x00001b77 andl         $252645135, %ecx
	0x01, 0xc1, //0x00001b7d addl         %eax, %ecx
	0x89, 0xc8, //0x00001b7f movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x00001b81 shrl         $4, %eax
	0x09, 0xc8, //0x00001b84 orl          %ecx, %eax
	0x89, 0xc1, //0x00001b86 movl         %eax, %ecx
	0xc1, 0xe9, 0x08, //0x00001b88 shrl         $8, %ecx
	0x81, 0xe1, 0x00, 0xff, 0x00, 0x00, //0x00001b8b andl         $65280, %ecx
	0x89, 0xc2, //0x00001b91 movl         %eax, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x00001b93 andl         $128, %edx
	0x48, 0x83, 0xc6, 0x06, //0x00001b99 addq         $6, %rsi
	0x09, 0xca, //0x00001b9d orl          %ecx, %edx
	0x44, 0x89, 0xfa, //0x00001b9f movl         %r15d, %edx
	0x0f, 0x84, 0x08, 0x00, 0x00, 0x00, //0x00001ba2 je           LBB0_405
	0xe9, 0x79, 0x01, 0x00, 0x00, //0x00001ba8 jmp          LBB0_436
	//0x00001bad LBB0_404
	0x48, 0x89, 0xce, //0x00001bad movq         %rcx, %rsi
	//0x00001bb0 LBB0_405
	0x3c, 0x0d, //0x00001bb0 cmpb         $13, %al
	0x0f, 0x84, 0xea, 0xfe, 0xff, 0xff, //0x00001bb2 je           LBB0_392
	0x41, 0x89, 0xc0, //0x00001bb8 movl         %eax, %r8d
	0x3c, 0x0a, //0x00001bbb cmpb         $10, %al
	0x0f, 0x84, 0xdf, 0xfe, 0xff, 0xff, //0x00001bbd je           LBB0_392
	0xe9, 0x5e, 0x01, 0x00, 0x00, //0x00001bc3 jmp          LBB0_436
	//0x00001bc8 LBB0_409
	0xbb, 0x02, 0x00, 0x00, 0x00, //0x00001bc8 movl         $2, %ebx
	0xe9, 0xd0, 0xf8, 0xff, 0xff, //0x00001bcd jmp          LBB0_288
	//0x00001bd2 LBB0_410
	0x48, 0x89, 0xc6, //0x00001bd2 movq         %rax, %rsi
	0x44, 0x89, 0xfa, //0x00001bd5 movl         %r15d, %edx
	0xe9, 0x49, 0x01, 0x00, 0x00, //0x00001bd8 jmp          LBB0_436
	//0x00001bdd LBB0_411
	0x48, 0x89, 0xce, //0x00001bdd movq         %rcx, %rsi
	0xe9, 0x41, 0x01, 0x00, 0x00, //0x00001be0 jmp          LBB0_436
	//0x00001be5 LBB0_412
	0x4d, 0x39, 0xcc, //0x00001be5 cmpq         %r9, %r12
	0x0f, 0x82, 0x14, 0x00, 0x00, 0x00, //0x00001be8 jb           LBB0_416
	//0x00001bee LBB0_413
	0x4c, 0x89, 0xe6, //0x00001bee movq         %r12, %rsi
	0xe9, 0x4e, 0xf8, 0xff, 0xff, //0x00001bf1 jmp          LBB0_280
	//0x00001bf6 LBB0_414
	0x49, 0x89, 0xf4, //0x00001bf6 movq         %rsi, %r12
	//0x00001bf9 LBB0_415
	0x4d, 0x39, 0xcc, //0x00001bf9 cmpq         %r9, %r12
	0x0f, 0x83, 0xec, 0xff, 0xff, 0xff, //0x00001bfc jae          LBB0_413
	//0x00001c02 LBB0_416
	0x49, 0x8d, 0x44, 0x24, 0x01, //0x00001c02 leaq         $1(%r12), %rax
	0x41, 0x0f, 0xb6, 0x0c, 0x24, //0x00001c07 movzbl       (%r12), %ecx
	0x80, 0xf9, 0x5c, //0x00001c0c cmpb         $92, %cl
	0x0f, 0x85, 0xe5, 0x00, 0x00, 0x00, //0x00001c0f jne          LBB0_427
	0x49, 0x8d, 0x74, 0x24, 0x02, //0x00001c15 leaq         $2(%r12), %rsi
	0x4c, 0x39, 0xce, //0x00001c1a cmpq         %r9, %rsi
	0x0f, 0x87, 0x29, 0x01, 0x00, 0x00, //0x00001c1d ja           LBB0_440
	0x0f, 0xb6, 0x00, //0x00001c23 movzbl       (%rax), %eax
	0x3c, 0x6e, //0x00001c26 cmpb         $110, %al
	0x0f, 0x84, 0xc8, 0xff, 0xff, 0xff, //0x00001c28 je           LBB0_414
	0x3c, 0x72, //0x00001c2e cmpb         $114, %al
	0x0f, 0x84, 0xc0, 0xff, 0xff, 0xff, //0x00001c30 je           LBB0_414
	0x3c, 0x75, //0x00001c36 cmpb         $117, %al
	0x0f, 0x85, 0xa4, 0xfb, 0xff, 0xff, //0x00001c38 jne          LBB0_383
	0x4c, 0x89, 0xc8, //0x00001c3e movq         %r9, %rax
	0x48, 0x29, 0xf0, //0x00001c41 subq         %rsi, %rax
	0x48, 0x83, 0xf8, 0x04, //0x00001c44 cmpq         $4, %rax
	0x0f, 0x8c, 0x94, 0xfb, 0xff, 0xff, //0x00001c48 jl           LBB0_383
	0x8b, 0x06, //0x00001c4e movl         (%rsi), %eax
	0x89, 0xc1, //0x00001c50 movl         %eax, %ecx
	0xf7, 0xd1, //0x00001c52 notl         %ecx
	0x8d, 0x90, 0xd0, 0xcf, 0xcf, 0xcf, //0x00001c54 leal         $-808464432(%rax), %edx
	0x81, 0xe1, 0x80, 0x80, 0x80, 0x80, //0x00001c5a andl         $-2139062144, %ecx
	0x85, 0xd1, //0x00001c60 testl        %edx, %ecx
	0x0f, 0x85, 0x7a, 0xfb, 0xff, 0xff, //0x00001c62 jne          LBB0_383
	0x8d, 0x90, 0x19, 0x19, 0x19, 0x19, //0x00001c68 leal         $421075225(%rax), %edx
	0x09, 0xc2, //0x00001c6e orl          %eax, %edx
	0xf7, 0xc2, 0x80, 0x80, 0x80, 0x80, //0x00001c70 testl        $-2139062144, %edx
	0x0f, 0x85, 0x66, 0xfb, 0xff, 0xff, //0x00001c76 jne          LBB0_383
	0x89, 0xc2, //0x00001c7c movl         %eax, %edx
	0x81, 0xe2, 0x7f, 0x7f, 0x7f, 0x7f, //0x00001c7e andl         $2139062143, %edx
	0xbf, 0xc0, 0xc0, 0xc0, 0xc0, //0x00001c84 movl         $-1061109568, %edi
	0x29, 0xd7, //0x00001c89 subl         %edx, %edi
	0x44, 0x8d, 0x82, 0x46, 0x46, 0x46, 0x46, //0x00001c8b leal         $1179010630(%rdx), %r8d
	0x21, 0xcf, //0x00001c92 andl         %ecx, %edi
	0x44, 0x85, 0xc7, //0x00001c94 testl        %r8d, %edi
	0x0f, 0x85, 0x45, 0xfb, 0xff, 0xff, //0x00001c97 jne          LBB0_383
	0xbf, 0xe0, 0xe0, 0xe0, 0xe0, //0x00001c9d movl         $-522133280, %edi
	0x29, 0xd7, //0x00001ca2 subl         %edx, %edi
	0x81, 0xc2, 0x39, 0x39, 0x39, 0x39, //0x00001ca4 addl         $960051513, %edx
	0x21, 0xf9, //0x00001caa andl         %edi, %ecx
	0x85, 0xd1, //0x00001cac testl        %edx, %ecx
	0x0f, 0x85, 0x2e, 0xfb, 0xff, 0xff, //0x00001cae jne          LBB0_383
	0x0f, 0xc8, //0x00001cb4 bswapl       %eax
	0x89, 0xc1, //0x00001cb6 movl         %eax, %ecx
	0xc1, 0xe9, 0x04, //0x00001cb8 shrl         $4, %ecx
	0xf7, 0xd1, //0x00001cbb notl         %ecx
	0x81, 0xe1, 0x01, 0x01, 0x01, 0x01, //0x00001cbd andl         $16843009, %ecx
	0x8d, 0x0c, 0xc9, //0x00001cc3 leal         (%rcx,%rcx,8), %ecx
	0x25, 0x0f, 0x0f, 0x0f, 0x0f, //0x00001cc6 andl         $252645135, %eax
	0x01, 0xc8, //0x00001ccb addl         %ecx, %eax
	0x89, 0xc1, //0x00001ccd movl         %eax, %ecx
	0xc1, 0xe9, 0x04, //0x00001ccf shrl         $4, %ecx
	0x09, 0xc1, //0x00001cd2 orl          %eax, %ecx
	0x89, 0xc8, //0x00001cd4 movl         %ecx, %eax
	0xc1, 0xe8, 0x08, //0x00001cd6 shrl         $8, %eax
	0x25, 0x00, 0xff, 0x00, 0x00, //0x00001cd9 andl         $65280, %eax
	0x89, 0xca, //0x00001cde movl         %ecx, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x00001ce0 andl         $128, %edx
	0x49, 0x83, 0xc4, 0x06, //0x00001ce6 addq         $6, %r12
	0x09, 0xc2, //0x00001cea orl          %eax, %edx
	0x4c, 0x89, 0xe6, //0x00001cec movq         %r12, %rsi
	0x0f, 0x84, 0x08, 0x00, 0x00, 0x00, //0x00001cef je           LBB0_428
	0xe9, 0xe8, 0xfa, 0xff, 0xff, //0x00001cf5 jmp          LBB0_383
	//0x00001cfa LBB0_427
	0x48, 0x89, 0xc6, //0x00001cfa movq         %rax, %rsi
	//0x00001cfd LBB0_428
	0x80, 0xf9, 0x0d, //0x00001cfd cmpb         $13, %cl
	0x0f, 0x84, 0xf0, 0xfe, 0xff, 0xff, //0x00001d00 je           LBB0_414
	0x49, 0x89, 0xf4, //0x00001d06 movq         %rsi, %r12
	0x80, 0xf9, 0x0a, //0x00001d09 cmpb         $10, %cl
	0x0f, 0x84, 0xe7, 0xfe, 0xff, 0xff, //0x00001d0c je           LBB0_415
	0xe9, 0xcb, 0xfa, 0xff, 0xff, //0x00001d12 jmp          LBB0_383
	//0x00001d17 LBB0_433
	0x80, 0xf9, 0x2f, //0x00001d17 cmpb         $47, %cl
	0x0f, 0x85, 0x03, 0x00, 0x00, 0x00, //0x00001d1a jne          LBB0_435
	0x41, 0xb0, 0x2f, //0x00001d20 movb         $47, %r8b
	//0x00001d23 LBB0_435
	0x48, 0x89, 0xc6, //0x00001d23 movq         %rax, %rsi
	//0x00001d26 LBB0_436
	0x41, 0x0f, 0xb6, 0xc0, //0x00001d26 movzbl       %r8b, %eax
	0x48, 0x8b, 0x4d, 0xd0, //0x00001d2a movq         $-48(%rbp), %rcx
	0x0f, 0xb6, 0x04, 0x01, //0x00001d2e movzbl       (%rcx,%rax), %eax
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00001d32 cmpl         $255, %eax
	0x0f, 0x84, 0x60, 0xfb, 0xff, 0xff, //0x00001d37 je           LBB0_438
	//0x00001d3d LBB0_437
	0xc1, 0xe2, 0x06, //0x00001d3d shll         $6, %edx
	0x09, 0xc2, //0x00001d40 orl          %eax, %edx
	0xbb, 0x04, 0x00, 0x00, 0x00, //0x00001d42 movl         $4, %ebx
	0xe9, 0xce, 0xf6, 0xff, 0xff, //0x00001d47 jmp          LBB0_277
	//0x00001d4c LBB0_440
	0x48, 0x89, 0xc6, //0x00001d4c movq         %rax, %rsi
	0xe9, 0x8e, 0xfa, 0xff, 0xff, //0x00001d4f jmp          LBB0_383
	//0x00001d54 LBB0_441
	0x4d, 0x8d, 0x71, 0xfc, //0x00001d54 leaq         $-4(%r9), %r14
	0x4d, 0x39, 0xf3, //0x00001d58 cmpq         %r14, %r11
	0x0f, 0x87, 0xbd, 0x0c, 0x00, 0x00, //0x00001d5b ja           LBB0_657
	0x4c, 0x8b, 0x55, 0xb8, //0x00001d61 movq         $-72(%rbp), %r10
	0x49, 0x83, 0xc2, 0xfc, //0x00001d65 addq         $-4, %r10
	0x4c, 0x39, 0xd2, //0x00001d69 cmpq         %r10, %rdx
	0x44, 0x8b, 0x6d, 0xcc, //0x00001d6c movl         $-52(%rbp), %r13d
	0x0f, 0x87, 0xac, 0x0c, 0x00, 0x00, //0x00001d70 ja           LBB0_658
	0x48, 0x8b, 0x45, 0xb0, //0x00001d76 movq         $-80(%rbp), %rax
	0x48, 0x8b, 0x4d, 0xa0, //0x00001d7a movq         $-96(%rbp), %rcx
	0x48, 0x01, 0xc8, //0x00001d7e addq         %rcx, %rax
	0x48, 0x83, 0xc0, 0xff, //0x00001d81 addq         $-1, %rax
	0x48, 0x89, 0x45, 0xc0, //0x00001d85 movq         %rax, $-64(%rbp)
	0x4c, 0x89, 0x55, 0xb8, //0x00001d89 movq         %r10, $-72(%rbp)
	0x90, 0x90, 0x90, //0x00001d8d .p2align 4, 0x90
	//0x00001d90 LBB0_444
	0x49, 0x89, 0xd7, //0x00001d90 movq         %rdx, %r15
	0x41, 0x0f, 0xb6, 0x03, //0x00001d93 movzbl       (%r11), %eax
	0x48, 0x8b, 0x75, 0xd0, //0x00001d97 movq         $-48(%rbp), %rsi
	0x0f, 0xb6, 0x14, 0x06, //0x00001d9b movzbl       (%rsi,%rax), %edx
	0x41, 0x0f, 0xb6, 0x43, 0x01, //0x00001d9f movzbl       $1(%r11), %eax
	0x0f, 0xb6, 0x04, 0x06, //0x00001da4 movzbl       (%rsi,%rax), %eax
	0x41, 0x0f, 0xb6, 0x4b, 0x02, //0x00001da8 movzbl       $2(%r11), %ecx
	0x0f, 0xb6, 0x1c, 0x0e, //0x00001dad movzbl       (%rsi,%rcx), %ebx
	0x41, 0x0f, 0xb6, 0x4b, 0x03, //0x00001db1 movzbl       $3(%r11), %ecx
	0x0f, 0xb6, 0x0c, 0x0e, //0x00001db6 movzbl       (%rsi,%rcx), %ecx
	0x89, 0xc7, //0x00001dba movl         %eax, %edi
	0x09, 0xd7, //0x00001dbc orl          %edx, %edi
	0x89, 0xde, //0x00001dbe movl         %ebx, %esi
	0x09, 0xce, //0x00001dc0 orl          %ecx, %esi
	0x09, 0xfe, //0x00001dc2 orl          %edi, %esi
	0x40, 0x80, 0xfe, 0xff, //0x00001dc4 cmpb         $-1, %sil
	0x0f, 0x84, 0x32, 0x00, 0x00, 0x00, //0x00001dc8 je           LBB0_446
	0xc1, 0xe2, 0x1a, //0x00001dce shll         $26, %edx
	0xc1, 0xe0, 0x14, //0x00001dd1 shll         $20, %eax
	0x09, 0xd0, //0x00001dd4 orl          %edx, %eax
	0xc1, 0xe3, 0x0e, //0x00001dd6 shll         $14, %ebx
	0xc1, 0xe1, 0x08, //0x00001dd9 shll         $8, %ecx
	0x09, 0xd9, //0x00001ddc orl          %ebx, %ecx
	0x09, 0xc1, //0x00001dde orl          %eax, %ecx
	0x0f, 0xc9, //0x00001de0 bswapl       %ecx
	0x4c, 0x89, 0xfa, //0x00001de2 movq         %r15, %rdx
	0x41, 0x89, 0x0f, //0x00001de5 movl         %ecx, (%r15)
	0x49, 0x83, 0xc3, 0x04, //0x00001de8 addq         $4, %r11
	0x48, 0x83, 0xc2, 0x03, //0x00001dec addq         $3, %rdx
	0xe9, 0xcb, 0x06, 0x00, 0x00, //0x00001df0 jmp          LBB0_598
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001df5 .p2align 4, 0x90
	//0x00001e00 LBB0_446
	0x4d, 0x39, 0xcb, //0x00001e00 cmpq         %r9, %r11
	0x0f, 0x83, 0xce, 0x00, 0x00, 0x00, //0x00001e03 jae          LBB0_458
	0x4c, 0x89, 0xde, //0x00001e09 movq         %r11, %rsi
	0x41, 0xf6, 0xc5, 0x08, //0x00001e0c testb        $8, %r13b
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00001e10 je           LBB0_449
	0xe9, 0xd9, 0x00, 0x00, 0x00, //0x00001e16 jmp          LBB0_461
	0x90, 0x90, 0x90, 0x90, 0x90, //0x00001e1b .p2align 4, 0x90
	//0x00001e20 LBB0_448
	0x48, 0x83, 0xc6, 0x01, //0x00001e20 addq         $1, %rsi
	0x4c, 0x39, 0xce, //0x00001e24 cmpq         %r9, %rsi
	0x0f, 0x83, 0xee, 0x01, 0x00, 0x00, //0x00001e27 jae          LBB0_477
	//0x00001e2d LBB0_449
	0x44, 0x0f, 0xb6, 0x06, //0x00001e2d movzbl       (%rsi), %r8d
	0x49, 0x83, 0xf8, 0x0d, //0x00001e31 cmpq         $13, %r8
	0x0f, 0x84, 0xe5, 0xff, 0xff, 0xff, //0x00001e35 je           LBB0_448
	0x41, 0x80, 0xf8, 0x0a, //0x00001e3b cmpb         $10, %r8b
	0x0f, 0x84, 0xdb, 0xff, 0xff, 0xff, //0x00001e3f je           LBB0_448
	0x48, 0x8b, 0x45, 0xd0, //0x00001e45 movq         $-48(%rbp), %rax
	0x46, 0x0f, 0xb6, 0x24, 0x00, //0x00001e49 movzbl       (%rax,%r8), %r12d
	0x48, 0x83, 0xc6, 0x01, //0x00001e4e addq         $1, %rsi
	0x41, 0x81, 0xfc, 0xff, 0x00, 0x00, 0x00, //0x00001e52 cmpl         $255, %r12d
	0x0f, 0x84, 0xf6, 0x02, 0x00, 0x00, //0x00001e59 je           LBB0_501
	0xbb, 0x01, 0x00, 0x00, 0x00, //0x00001e5f movl         $1, %ebx
	0x4c, 0x39, 0xce, //0x00001e64 cmpq         %r9, %rsi
	0x0f, 0x82, 0x20, 0x00, 0x00, 0x00, //0x00001e67 jb           LBB0_454
	0xe9, 0x5c, 0x02, 0x00, 0x00, //0x00001e6d jmp          LBB0_491
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001e72 .p2align 4, 0x90
	//0x00001e80 LBB0_453
	0x48, 0x83, 0xc6, 0x01, //0x00001e80 addq         $1, %rsi
	0x4c, 0x39, 0xce, //0x00001e84 cmpq         %r9, %rsi
	0x0f, 0x83, 0xae, 0x04, 0x00, 0x00, //0x00001e87 jae          LBB0_534
	//0x00001e8d LBB0_454
	0x44, 0x0f, 0xb6, 0x06, //0x00001e8d movzbl       (%rsi), %r8d
	0x49, 0x83, 0xf8, 0x0d, //0x00001e91 cmpq         $13, %r8
	0x0f, 0x84, 0xe5, 0xff, 0xff, 0xff, //0x00001e95 je           LBB0_453
	0x41, 0x80, 0xf8, 0x0a, //0x00001e9b cmpb         $10, %r8b
	0x0f, 0x84, 0xdb, 0xff, 0xff, 0xff, //0x00001e9f je           LBB0_453
	0x48, 0x8b, 0x45, 0xd0, //0x00001ea5 movq         $-48(%rbp), %rax
	0x42, 0x0f, 0xb6, 0x04, 0x00, //0x00001ea9 movzbl       (%rax,%r8), %eax
	0x48, 0x83, 0xc6, 0x01, //0x00001eae addq         $1, %rsi
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00001eb2 cmpl         $255, %eax
	0x0f, 0x84, 0x21, 0x08, 0x00, 0x00, //0x00001eb7 je           LBB0_588
	0x41, 0xc1, 0xe4, 0x06, //0x00001ebd shll         $6, %r12d
	0x41, 0x09, 0xc4, //0x00001ec1 orl          %eax, %r12d
	0xbb, 0x02, 0x00, 0x00, 0x00, //0x00001ec4 movl         $2, %ebx
	0x4c, 0x39, 0xce, //0x00001ec9 cmpq         %r9, %rsi
	0x0f, 0x82, 0x73, 0x01, 0x00, 0x00, //0x00001ecc jb           LBB0_481
	0xe9, 0xf7, 0x01, 0x00, 0x00, //0x00001ed2 jmp          LBB0_491
	//0x00001ed7 LBB0_458
	0x4c, 0x89, 0xfa, //0x00001ed7 movq         %r15, %rdx
	0xe9, 0xe1, 0x05, 0x00, 0x00, //0x00001eda jmp          LBB0_598
	//0x00001edf LBB0_475
	0x80, 0xf9, 0x6e, //0x00001edf cmpb         $110, %cl
	0x0f, 0x85, 0xaf, 0x01, 0x00, 0x00, //0x00001ee2 jne          LBB0_486
	//0x00001ee8 LBB0_459
	0x48, 0x89, 0xc6, //0x00001ee8 movq         %rax, %rsi
	//0x00001eeb LBB0_460
	0x4c, 0x39, 0xce, //0x00001eeb cmpq         %r9, %rsi
	0x0f, 0x83, 0x27, 0x01, 0x00, 0x00, //0x00001eee jae          LBB0_477
	//0x00001ef4 LBB0_461
	0x48, 0x8d, 0x4e, 0x01, //0x00001ef4 leaq         $1(%rsi), %rcx
	0x0f, 0xb6, 0x06, //0x00001ef8 movzbl       (%rsi), %eax
	0x3c, 0x5c, //0x00001efb cmpb         $92, %al
	0x0f, 0x85, 0xfd, 0x00, 0x00, 0x00, //0x00001efd jne          LBB0_473
	0x48, 0x8d, 0x46, 0x02, //0x00001f03 leaq         $2(%rsi), %rax
	0x41, 0xb0, 0xff, //0x00001f07 movb         $-1, %r8b
	0x4c, 0x39, 0xc8, //0x00001f0a cmpq         %r9, %rax
	0x0f, 0x87, 0x7c, 0x01, 0x00, 0x00, //0x00001f0d ja           LBB0_485
	0x0f, 0xb6, 0x09, //0x00001f13 movzbl       (%rcx), %ecx
	0x80, 0xf9, 0x71, //0x00001f16 cmpb         $113, %cl
	0x0f, 0x8e, 0xc0, 0xff, 0xff, 0xff, //0x00001f19 jle          LBB0_475
	0x80, 0xf9, 0x72, //0x00001f1f cmpb         $114, %cl
	0x0f, 0x84, 0xc0, 0xff, 0xff, 0xff, //0x00001f22 je           LBB0_459
	0x80, 0xf9, 0x75, //0x00001f28 cmpb         $117, %cl
	0x0f, 0x85, 0x72, 0x01, 0x00, 0x00, //0x00001f2b jne          LBB0_488
	0x4c, 0x89, 0xc9, //0x00001f31 movq         %r9, %rcx
	0x48, 0x29, 0xc1, //0x00001f34 subq         %rax, %rcx
	0x48, 0x83, 0xf9, 0x04, //0x00001f37 cmpq         $4, %rcx
	0x0f, 0x8c, 0x62, 0x01, 0x00, 0x00, //0x00001f3b jl           LBB0_488
	0x8b, 0x08, //0x00001f41 movl         (%rax), %ecx
	0x89, 0xca, //0x00001f43 movl         %ecx, %edx
	0xf7, 0xd2, //0x00001f45 notl         %edx
	0x8d, 0x99, 0xd0, 0xcf, 0xcf, 0xcf, //0x00001f47 leal         $-808464432(%rcx), %ebx
	0x81, 0xe2, 0x80, 0x80, 0x80, 0x80, //0x00001f4d andl         $-2139062144, %edx
	0x85, 0xda, //0x00001f53 testl        %ebx, %edx
	0x0f, 0x85, 0x48, 0x01, 0x00, 0x00, //0x00001f55 jne          LBB0_488
	0x8d, 0x99, 0x19, 0x19, 0x19, 0x19, //0x00001f5b leal         $421075225(%rcx), %ebx
	0x09, 0xcb, //0x00001f61 orl          %ecx, %ebx
	0xf7, 0xc3, 0x80, 0x80, 0x80, 0x80, //0x00001f63 testl        $-2139062144, %ebx
	0x0f, 0x85, 0x34, 0x01, 0x00, 0x00, //0x00001f69 jne          LBB0_488
	0x89, 0xcb, //0x00001f6f movl         %ecx, %ebx
	0x81, 0xe3, 0x7f, 0x7f, 0x7f, 0x7f, //0x00001f71 andl         $2139062143, %ebx
	0xbf, 0xc0, 0xc0, 0xc0, 0xc0, //0x00001f77 movl         $-1061109568, %edi
	0x29, 0xdf, //0x00001f7c subl         %ebx, %edi
	0x44, 0x8d, 0x93, 0x46, 0x46, 0x46, 0x46, //0x00001f7e leal         $1179010630(%rbx), %r10d
	0x21, 0xd7, //0x00001f85 andl         %edx, %edi
	0x44, 0x85, 0xd7, //0x00001f87 testl        %r10d, %edi
	0x0f, 0x85, 0x47, 0x05, 0x00, 0x00, //0x00001f8a jne          LBB0_554
	0xbf, 0xe0, 0xe0, 0xe0, 0xe0, //0x00001f90 movl         $-522133280, %edi
	0x29, 0xdf, //0x00001f95 subl         %ebx, %edi
	0x81, 0xc3, 0x39, 0x39, 0x39, 0x39, //0x00001f97 addl         $960051513, %ebx
	0x21, 0xfa, //0x00001f9d andl         %edi, %edx
	0x85, 0xda, //0x00001f9f testl        %ebx, %edx
	0x4c, 0x8b, 0x55, 0xb8, //0x00001fa1 movq         $-72(%rbp), %r10
	0x0f, 0x85, 0xf8, 0x00, 0x00, 0x00, //0x00001fa5 jne          LBB0_488
	0x0f, 0xc9, //0x00001fab bswapl       %ecx
	0x89, 0xc8, //0x00001fad movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x00001faf shrl         $4, %eax
	0xf7, 0xd0, //0x00001fb2 notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x00001fb4 andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x00001fb9 leal         (%rax,%rax,8), %eax
	0x81, 0xe1, 0x0f, 0x0f, 0x0f, 0x0f, //0x00001fbc andl         $252645135, %ecx
	0x01, 0xc1, //0x00001fc2 addl         %eax, %ecx
	0x89, 0xc8, //0x00001fc4 movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x00001fc6 shrl         $4, %eax
	0x09, 0xc8, //0x00001fc9 orl          %ecx, %eax
	0x89, 0xc1, //0x00001fcb movl         %eax, %ecx
	0xc1, 0xe9, 0x08, //0x00001fcd shrl         $8, %ecx
	0x81, 0xe1, 0x00, 0xff, 0x00, 0x00, //0x00001fd0 andl         $65280, %ecx
	0x89, 0xc2, //0x00001fd6 movl         %eax, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x00001fd8 andl         $128, %edx
	0x48, 0x83, 0xc6, 0x06, //0x00001fde addq         $6, %rsi
	0x09, 0xca, //0x00001fe2 orl          %ecx, %edx
	0x0f, 0x85, 0xbc, 0x00, 0x00, 0x00, //0x00001fe4 jne          LBB0_489
	0x3c, 0x0d, //0x00001fea cmpb         $13, %al
	0x0f, 0x85, 0x19, 0x00, 0x00, 0x00, //0x00001fec jne          LBB0_474
	0xe9, 0xf4, 0xfe, 0xff, 0xff, //0x00001ff2 jmp          LBB0_460
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001ff7 .p2align 4, 0x90
	//0x00002000 LBB0_473
	0x48, 0x89, 0xce, //0x00002000 movq         %rcx, %rsi
	0x3c, 0x0d, //0x00002003 cmpb         $13, %al
	0x0f, 0x84, 0xe0, 0xfe, 0xff, 0xff, //0x00002005 je           LBB0_460
	//0x0000200b LBB0_474
	0x41, 0x89, 0xc0, //0x0000200b movl         %eax, %r8d
	0x3c, 0x0a, //0x0000200e cmpb         $10, %al
	0x0f, 0x84, 0xd5, 0xfe, 0xff, 0xff, //0x00002010 je           LBB0_460
	0xe9, 0x8b, 0x00, 0x00, 0x00, //0x00002016 jmp          LBB0_489
	//0x0000201b LBB0_477
	0x31, 0xdb, //0x0000201b xorl         %ebx, %ebx
	0x45, 0x31, 0xe4, //0x0000201d xorl         %r12d, %r12d
	//0x00002020 LBB0_478
	0x85, 0xdb, //0x00002020 testl        %ebx, %ebx
	0x0f, 0x85, 0xa6, 0x00, 0x00, 0x00, //0x00002022 jne          LBB0_491
	0x49, 0x89, 0xf3, //0x00002028 movq         %rsi, %r11
	0x4c, 0x89, 0xfa, //0x0000202b movq         %r15, %rdx
	0xe9, 0x8d, 0x04, 0x00, 0x00, //0x0000202e jmp          LBB0_598
	//0x00002033 LBB0_480
	0x48, 0x83, 0xc6, 0x01, //0x00002033 addq         $1, %rsi
	0xbb, 0x02, 0x00, 0x00, 0x00, //0x00002037 movl         $2, %ebx
	0x4c, 0x39, 0xce, //0x0000203c cmpq         %r9, %rsi
	0x0f, 0x83, 0xdb, 0xff, 0xff, 0xff, //0x0000203f jae          LBB0_478
	//0x00002045 LBB0_481
	0x44, 0x0f, 0xb6, 0x06, //0x00002045 movzbl       (%rsi), %r8d
	0x49, 0x83, 0xf8, 0x0d, //0x00002049 cmpq         $13, %r8
	0x0f, 0x84, 0xe0, 0xff, 0xff, 0xff, //0x0000204d je           LBB0_480
	0x41, 0x80, 0xf8, 0x0a, //0x00002053 cmpb         $10, %r8b
	0x0f, 0x84, 0xd6, 0xff, 0xff, 0xff, //0x00002057 je           LBB0_480
	0x48, 0x8b, 0x45, 0xd0, //0x0000205d movq         $-48(%rbp), %rax
	0x42, 0x0f, 0xb6, 0x04, 0x00, //0x00002061 movzbl       (%rax,%r8), %eax
	0x48, 0x83, 0xc6, 0x01, //0x00002066 addq         $1, %rsi
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x0000206a cmpl         $255, %eax
	0x0f, 0x84, 0x07, 0x08, 0x00, 0x00, //0x0000206f je           LBB0_623
	0x41, 0xc1, 0xe4, 0x06, //0x00002075 shll         $6, %r12d
	0x41, 0x09, 0xc4, //0x00002079 orl          %eax, %r12d
	0xbb, 0x03, 0x00, 0x00, 0x00, //0x0000207c movl         $3, %ebx
	0x4c, 0x39, 0xce, //0x00002081 cmpq         %r9, %rsi
	0x0f, 0x82, 0xa2, 0x04, 0x00, 0x00, //0x00002084 jb           LBB0_561
	0xe9, 0x3f, 0x00, 0x00, 0x00, //0x0000208a jmp          LBB0_491
	//0x0000208f LBB0_485
	0x48, 0x89, 0xce, //0x0000208f movq         %rcx, %rsi
	0xe9, 0x0f, 0x00, 0x00, 0x00, //0x00002092 jmp          LBB0_489
	//0x00002097 LBB0_486
	0x80, 0xf9, 0x2f, //0x00002097 cmpb         $47, %cl
	0x0f, 0x85, 0x03, 0x00, 0x00, 0x00, //0x0000209a jne          LBB0_488
	0x41, 0xb0, 0x2f, //0x000020a0 movb         $47, %r8b
	//0x000020a3 LBB0_488
	0x48, 0x89, 0xc6, //0x000020a3 movq         %rax, %rsi
	//0x000020a6 LBB0_489
	0x41, 0x0f, 0xb6, 0xc0, //0x000020a6 movzbl       %r8b, %eax
	0x48, 0x8b, 0x4d, 0xd0, //0x000020aa movq         $-48(%rbp), %rcx
	0x44, 0x0f, 0xb6, 0x24, 0x01, //0x000020ae movzbl       (%rcx,%rax), %r12d
	0x41, 0x81, 0xfc, 0xff, 0x00, 0x00, 0x00, //0x000020b3 cmpl         $255, %r12d
	0x0f, 0x84, 0x95, 0x00, 0x00, 0x00, //0x000020ba je           LBB0_501
	0xbb, 0x01, 0x00, 0x00, 0x00, //0x000020c0 movl         $1, %ebx
	0x4c, 0x39, 0xce, //0x000020c5 cmpq         %r9, %rsi
	0x0f, 0x82, 0x44, 0x01, 0x00, 0x00, //0x000020c8 jb           LBB0_517
	//0x000020ce LBB0_491
	0x41, 0xf6, 0xc5, 0x02, //0x000020ce testb        $2, %r13b
	0x0f, 0x94, 0xc0, //0x000020d2 sete         %al
	0x83, 0xfb, 0x01, //0x000020d5 cmpl         $1, %ebx
	0x0f, 0x94, 0xc1, //0x000020d8 sete         %cl
	0x4c, 0x39, 0xce, //0x000020db cmpq         %r9, %rsi
	0x0f, 0x82, 0x11, 0x00, 0x00, 0x00, //0x000020de jb           LBB0_494
	0x83, 0xfb, 0x04, //0x000020e4 cmpl         $4, %ebx
	0x0f, 0x84, 0x08, 0x00, 0x00, 0x00, //0x000020e7 je           LBB0_494
	0x08, 0xc8, //0x000020ed orb          %cl, %al
	0x0f, 0x85, 0xa4, 0x03, 0x00, 0x00, //0x000020ef jne          LBB0_597
	//0x000020f5 LBB0_494
	0xb0, 0x04, //0x000020f5 movb         $4, %al
	0x28, 0xd8, //0x000020f7 subb         %bl, %al
	0x0f, 0xb6, 0xc0, //0x000020f9 movzbl       %al, %eax
	0x01, 0xc0, //0x000020fc addl         %eax, %eax
	0x8d, 0x0c, 0x40, //0x000020fe leal         (%rax,%rax,2), %ecx
	0x44, 0x89, 0xe2, //0x00002101 movl         %r12d, %edx
	0xd3, 0xe2, //0x00002104 shll         %cl, %edx
	0x83, 0xfb, 0x02, //0x00002106 cmpl         $2, %ebx
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x00002109 je           LBB0_499
	0x83, 0xfb, 0x03, //0x0000210f cmpl         $3, %ebx
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00002112 je           LBB0_498
	0x89, 0xd0, //0x00002118 movl         %edx, %eax
	0x83, 0xfb, 0x04, //0x0000211a cmpl         $4, %ebx
	0x44, 0x8b, 0x6d, 0xcc, //0x0000211d movl         $-52(%rbp), %r13d
	0x4c, 0x89, 0xfa, //0x00002121 movq         %r15, %rdx
	0x0f, 0x85, 0x1a, 0x00, 0x00, 0x00, //0x00002124 jne          LBB0_500
	0x88, 0x42, 0x02, //0x0000212a movb         %al, $2(%rdx)
	0x89, 0xc2, //0x0000212d movl         %eax, %edx
	//0x0000212f LBB0_498
	0x4c, 0x89, 0xf8, //0x0000212f movq         %r15, %rax
	0x88, 0x70, 0x01, //0x00002132 movb         %dh, $1(%rax)
	//0x00002135 LBB0_499
	0xc1, 0xea, 0x10, //0x00002135 shrl         $16, %edx
	0x89, 0xd0, //0x00002138 movl         %edx, %eax
	0x4c, 0x89, 0xfa, //0x0000213a movq         %r15, %rdx
	0x41, 0x88, 0x07, //0x0000213d movb         %al, (%r15)
	0x44, 0x8b, 0x6d, 0xcc, //0x00002140 movl         $-52(%rbp), %r13d
	//0x00002144 LBB0_500
	0x89, 0xd8, //0x00002144 movl         %ebx, %eax
	0x48, 0x01, 0xc2, //0x00002146 addq         %rax, %rdx
	0x48, 0x83, 0xc2, 0xff, //0x00002149 addq         $-1, %rdx
	0x49, 0x89, 0xf3, //0x0000214d movq         %rsi, %r11
	0xe9, 0x6b, 0x03, 0x00, 0x00, //0x00002150 jmp          LBB0_598
	//0x00002155 LBB0_501
	0x31, 0xdb, //0x00002155 xorl         %ebx, %ebx
	0x45, 0x31, 0xe4, //0x00002157 xorl         %r12d, %r12d
	//0x0000215a LBB0_502
	0x41, 0xf6, 0xc5, 0x02, //0x0000215a testb        $2, %r13b
	0x0f, 0x85, 0x35, 0x03, 0x00, 0x00, //0x0000215e jne          LBB0_597
	0x41, 0x80, 0xf8, 0x3d, //0x00002164 cmpb         $61, %r8b
	0x0f, 0x85, 0x2b, 0x03, 0x00, 0x00, //0x00002168 jne          LBB0_597
	0x83, 0xfb, 0x02, //0x0000216e cmpl         $2, %ebx
	0x0f, 0x82, 0x22, 0x03, 0x00, 0x00, //0x00002171 jb           LBB0_597
	0xba, 0x05, 0x00, 0x00, 0x00, //0x00002177 movl         $5, %edx
	0x29, 0xda, //0x0000217c subl         %ebx, %edx
	0x41, 0xf6, 0xc5, 0x08, //0x0000217e testb        $8, %r13b
	0x0f, 0x85, 0xbd, 0x01, 0x00, 0x00, //0x00002182 jne          LBB0_535
	0x4c, 0x39, 0xce, //0x00002188 cmpq         %r9, %rsi
	0x0f, 0x83, 0x64, 0xff, 0xff, 0xff, //0x0000218b jae          LBB0_494
	0x48, 0x8d, 0x4e, 0x01, //0x00002191 leaq         $1(%rsi), %rcx
	0x48, 0x8b, 0x45, 0xc0, //0x00002195 movq         $-64(%rbp), %rax
	0x48, 0x29, 0xf0, //0x00002199 subq         %rsi, %rax
	0x48, 0x83, 0xc6, 0x02, //0x0000219c addq         $2, %rsi
	0x48, 0x89, 0xf7, //0x000021a0 movq         %rsi, %rdi
	0x48, 0x89, 0xce, //0x000021a3 movq         %rcx, %rsi
	0xe9, 0x17, 0x00, 0x00, 0x00, //0x000021a6 jmp          LBB0_509
	0x90, 0x90, 0x90, 0x90, 0x90, //0x000021ab .p2align 4, 0x90
	//0x000021b0 LBB0_508
	0x48, 0x83, 0xc6, 0x01, //0x000021b0 addq         $1, %rsi
	0x48, 0x83, 0xc7, 0x01, //0x000021b4 addq         $1, %rdi
	0x48, 0x83, 0xc0, 0xff, //0x000021b8 addq         $-1, %rax
	0x0f, 0x83, 0x26, 0x05, 0x00, 0x00, //0x000021bc jae          LBB0_589
	//0x000021c2 LBB0_509
	0x0f, 0xb6, 0x4e, 0xff, //0x000021c2 movzbl       $-1(%rsi), %ecx
	0x80, 0xf9, 0x0a, //0x000021c6 cmpb         $10, %cl
	0x0f, 0x84, 0xe1, 0xff, 0xff, 0xff, //0x000021c9 je           LBB0_508
	0x80, 0xf9, 0x0d, //0x000021cf cmpb         $13, %cl
	0x0f, 0x84, 0xd8, 0xff, 0xff, 0xff, //0x000021d2 je           LBB0_508
	0x80, 0xf9, 0x3d, //0x000021d8 cmpb         $61, %cl
	0x0f, 0x85, 0xb8, 0x02, 0x00, 0x00, //0x000021db jne          LBB0_597
	0x83, 0xfa, 0x02, //0x000021e1 cmpl         $2, %edx
	0x0f, 0x84, 0xaf, 0x02, 0x00, 0x00, //0x000021e4 je           LBB0_597
	0x4c, 0x39, 0xce, //0x000021ea cmpq         %r9, %rsi
	0x0f, 0x83, 0x02, 0xff, 0xff, 0xff, //0x000021ed jae          LBB0_494
	0x48, 0x01, 0xc6, //0x000021f3 addq         %rax, %rsi
	0x31, 0xc9, //0x000021f6 xorl         %ecx, %ecx
	0xe9, 0xf7, 0x02, 0x00, 0x00, //0x000021f8 jmp          LBB0_556
	//0x000021fd LBB0_531
	0x80, 0xf9, 0x6e, //0x000021fd cmpb         $110, %cl
	0x0f, 0x85, 0x60, 0x03, 0x00, 0x00, //0x00002200 jne          LBB0_565
	//0x00002206 LBB0_515
	0x48, 0x89, 0xc6, //0x00002206 movq         %rax, %rsi
	//0x00002209 LBB0_516
	0x4c, 0x39, 0xce, //0x00002209 cmpq         %r9, %rsi
	0x0f, 0x83, 0x29, 0x01, 0x00, 0x00, //0x0000220c jae          LBB0_534
	//0x00002212 LBB0_517
	0x48, 0x8d, 0x4e, 0x01, //0x00002212 leaq         $1(%rsi), %rcx
	0x0f, 0xb6, 0x06, //0x00002216 movzbl       (%rsi), %eax
	0x3c, 0x5c, //0x00002219 cmpb         $92, %al
	0x0f, 0x85, 0xff, 0x00, 0x00, 0x00, //0x0000221b jne          LBB0_529
	0x48, 0x8d, 0x46, 0x02, //0x00002221 leaq         $2(%rsi), %rax
	0x41, 0xb0, 0xff, //0x00002225 movb         $-1, %r8b
	0x4c, 0x39, 0xc8, //0x00002228 cmpq         %r9, %rax
	0x0f, 0x87, 0xe1, 0x02, 0x00, 0x00, //0x0000222b ja           LBB0_559
	0x0f, 0xb6, 0x09, //0x00002231 movzbl       (%rcx), %ecx
	0x80, 0xf9, 0x71, //0x00002234 cmpb         $113, %cl
	0x0f, 0x8e, 0xc0, 0xff, 0xff, 0xff, //0x00002237 jle          LBB0_531
	0x80, 0xf9, 0x72, //0x0000223d cmpb         $114, %cl
	0x0f, 0x84, 0xc0, 0xff, 0xff, 0xff, //0x00002240 je           LBB0_515
	0x80, 0xf9, 0x75, //0x00002246 cmpb         $117, %cl
	0x0f, 0x85, 0x23, 0x03, 0x00, 0x00, //0x00002249 jne          LBB0_567
	0x4c, 0x89, 0xc9, //0x0000224f movq         %r9, %rcx
	0x48, 0x29, 0xc1, //0x00002252 subq         %rax, %rcx
	0x48, 0x83, 0xf9, 0x04, //0x00002255 cmpq         $4, %rcx
	0x0f, 0x8c, 0x13, 0x03, 0x00, 0x00, //0x00002259 jl           LBB0_567
	0x8b, 0x08, //0x0000225f movl         (%rax), %ecx
	0x89, 0xcb, //0x00002261 movl         %ecx, %ebx
	0xf7, 0xd3, //0x00002263 notl         %ebx
	0x8d, 0x91, 0xd0, 0xcf, 0xcf, 0xcf, //0x00002265 leal         $-808464432(%rcx), %edx
	0x81, 0xe3, 0x80, 0x80, 0x80, 0x80, //0x0000226b andl         $-2139062144, %ebx
	0x85, 0xd3, //0x00002271 testl        %edx, %ebx
	0x0f, 0x85, 0xf9, 0x02, 0x00, 0x00, //0x00002273 jne          LBB0_567
	0x8d, 0x91, 0x19, 0x19, 0x19, 0x19, //0x00002279 leal         $421075225(%rcx), %edx
	0x09, 0xca, //0x0000227f orl          %ecx, %edx
	0xf7, 0xc2, 0x80, 0x80, 0x80, 0x80, //0x00002281 testl        $-2139062144, %edx
	0x0f, 0x85, 0xe5, 0x02, 0x00, 0x00, //0x00002287 jne          LBB0_567
	0x89, 0xca, //0x0000228d movl         %ecx, %edx
	0x81, 0xe2, 0x7f, 0x7f, 0x7f, 0x7f, //0x0000228f andl         $2139062143, %edx
	0xbf, 0xc0, 0xc0, 0xc0, 0xc0, //0x00002295 movl         $-1061109568, %edi
	0x29, 0xd7, //0x0000229a subl         %edx, %edi
	0x44, 0x8d, 0x92, 0x46, 0x46, 0x46, 0x46, //0x0000229c leal         $1179010630(%rdx), %r10d
	0x21, 0xdf, //0x000022a3 andl         %ebx, %edi
	0x44, 0x85, 0xd7, //0x000022a5 testl        %r10d, %edi
	0x0f, 0x85, 0x42, 0x04, 0x00, 0x00, //0x000022a8 jne          LBB0_591
	0xbf, 0xe0, 0xe0, 0xe0, 0xe0, //0x000022ae movl         $-522133280, %edi
	0x29, 0xd7, //0x000022b3 subl         %edx, %edi
	0x81, 0xc2, 0x39, 0x39, 0x39, 0x39, //0x000022b5 addl         $960051513, %edx
	0x21, 0xfb, //0x000022bb andl         %edi, %ebx
	0x85, 0xd3, //0x000022bd testl        %edx, %ebx
	0x4c, 0x8b, 0x55, 0xb8, //0x000022bf movq         $-72(%rbp), %r10
	0x0f, 0x85, 0xa9, 0x02, 0x00, 0x00, //0x000022c3 jne          LBB0_567
	0x0f, 0xc9, //0x000022c9 bswapl       %ecx
	0x89, 0xc8, //0x000022cb movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x000022cd shrl         $4, %eax
	0xf7, 0xd0, //0x000022d0 notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x000022d2 andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x000022d7 leal         (%rax,%rax,8), %eax
	0x81, 0xe1, 0x0f, 0x0f, 0x0f, 0x0f, //0x000022da andl         $252645135, %ecx
	0x01, 0xc1, //0x000022e0 addl         %eax, %ecx
	0x89, 0xc8, //0x000022e2 movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x000022e4 shrl         $4, %eax
	0x09, 0xc8, //0x000022e7 orl          %ecx, %eax
	0x89, 0xc1, //0x000022e9 movl         %eax, %ecx
	0xc1, 0xe9, 0x08, //0x000022eb shrl         $8, %ecx
	0x81, 0xe1, 0x00, 0xff, 0x00, 0x00, //0x000022ee andl         $65280, %ecx
	0x89, 0xc2, //0x000022f4 movl         %eax, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x000022f6 andl         $128, %edx
	0x48, 0x83, 0xc6, 0x06, //0x000022fc addq         $6, %rsi
	0x09, 0xca, //0x00002300 orl          %ecx, %edx
	0x0f, 0x85, 0x6d, 0x02, 0x00, 0x00, //0x00002302 jne          LBB0_568
	0x3c, 0x0d, //0x00002308 cmpb         $13, %al
	0x0f, 0x85, 0x1b, 0x00, 0x00, 0x00, //0x0000230a jne          LBB0_530
	0xe9, 0xf4, 0xfe, 0xff, 0xff, //0x00002310 jmp          LBB0_516
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002315 .p2align 4, 0x90
	//0x00002320 LBB0_529
	0x48, 0x89, 0xce, //0x00002320 movq         %rcx, %rsi
	0x3c, 0x0d, //0x00002323 cmpb         $13, %al
	0x0f, 0x84, 0xde, 0xfe, 0xff, 0xff, //0x00002325 je           LBB0_516
	//0x0000232b LBB0_530
	0x41, 0x89, 0xc0, //0x0000232b movl         %eax, %r8d
	0x3c, 0x0a, //0x0000232e cmpb         $10, %al
	0x0f, 0x84, 0xd3, 0xfe, 0xff, 0xff, //0x00002330 je           LBB0_516
	0xe9, 0x3a, 0x02, 0x00, 0x00, //0x00002336 jmp          LBB0_568
	//0x0000233b LBB0_534
	0xbb, 0x01, 0x00, 0x00, 0x00, //0x0000233b movl         $1, %ebx
	0xe9, 0xdb, 0xfc, 0xff, 0xff, //0x00002340 jmp          LBB0_478
	//0x00002345 LBB0_535
	0x4c, 0x39, 0xce, //0x00002345 cmpq         %r9, %rsi
	0x0f, 0x83, 0xa7, 0xfd, 0xff, 0xff, //0x00002348 jae          LBB0_494
	0x89, 0x55, 0xa8, //0x0000234e movl         %edx, $-88(%rbp)
	0x48, 0x89, 0xf7, //0x00002351 movq         %rsi, %rdi
	0xe9, 0x1d, 0x00, 0x00, 0x00, //0x00002354 jmp          LBB0_539
	//0x00002359 LBB0_553
	0x4c, 0x89, 0xef, //0x00002359 movq         %r13, %rdi
	0x4c, 0x39, 0xcf, //0x0000235c cmpq         %r9, %rdi
	0x0f, 0x82, 0x11, 0x00, 0x00, 0x00, //0x0000235f jb           LBB0_539
	0xe9, 0x92, 0x03, 0x00, 0x00, //0x00002365 jmp          LBB0_592
	//0x0000236a LBB0_537
	0x48, 0x89, 0xf7, //0x0000236a movq         %rsi, %rdi
	0x4c, 0x39, 0xcf, //0x0000236d cmpq         %r9, %rdi
	0x0f, 0x83, 0x86, 0x03, 0x00, 0x00, //0x00002370 jae          LBB0_592
	//0x00002376 LBB0_539
	0x4c, 0x8d, 0x6f, 0x01, //0x00002376 leaq         $1(%rdi), %r13
	0x0f, 0xb6, 0x0f, //0x0000237a movzbl       (%rdi), %ecx
	0x80, 0xf9, 0x5c, //0x0000237d cmpb         $92, %cl
	0x0f, 0x85, 0xeb, 0x00, 0x00, 0x00, //0x00002380 jne          LBB0_550
	0x48, 0x8d, 0x77, 0x02, //0x00002386 leaq         $2(%rdi), %rsi
	0x4c, 0x39, 0xce, //0x0000238a cmpq         %r9, %rsi
	0x0f, 0x87, 0x03, 0x01, 0x00, 0x00, //0x0000238d ja           LBB0_596
	0x41, 0x0f, 0xb6, 0x45, 0x00, //0x00002393 movzbl       (%r13), %eax
	0x3c, 0x6e, //0x00002398 cmpb         $110, %al
	0x0f, 0x84, 0xca, 0xff, 0xff, 0xff, //0x0000239a je           LBB0_537
	0x3c, 0x72, //0x000023a0 cmpb         $114, %al
	0x0f, 0x84, 0xc2, 0xff, 0xff, 0xff, //0x000023a2 je           LBB0_537
	0x3c, 0x75, //0x000023a8 cmpb         $117, %al
	0x0f, 0x85, 0xe9, 0x00, 0x00, 0x00, //0x000023aa jne          LBB0_597
	0x4c, 0x89, 0xc8, //0x000023b0 movq         %r9, %rax
	0x48, 0x29, 0xf0, //0x000023b3 subq         %rsi, %rax
	0x48, 0x83, 0xf8, 0x04, //0x000023b6 cmpq         $4, %rax
	0x0f, 0x8c, 0xd9, 0x00, 0x00, 0x00, //0x000023ba jl           LBB0_597
	0x8b, 0x16, //0x000023c0 movl         (%rsi), %edx
	0x89, 0xd1, //0x000023c2 movl         %edx, %ecx
	0xf7, 0xd1, //0x000023c4 notl         %ecx
	0x8d, 0x82, 0xd0, 0xcf, 0xcf, 0xcf, //0x000023c6 leal         $-808464432(%rdx), %eax
	0x81, 0xe1, 0x80, 0x80, 0x80, 0x80, //0x000023cc andl         $-2139062144, %ecx
	0x85, 0xc1, //0x000023d2 testl        %eax, %ecx
	0x0f, 0x85, 0xbf, 0x00, 0x00, 0x00, //0x000023d4 jne          LBB0_597
	0x8d, 0x82, 0x19, 0x19, 0x19, 0x19, //0x000023da leal         $421075225(%rdx), %eax
	0x09, 0xd0, //0x000023e0 orl          %edx, %eax
	0xa9, 0x80, 0x80, 0x80, 0x80, //0x000023e2 testl        $-2139062144, %eax
	0x0f, 0x85, 0xac, 0x00, 0x00, 0x00, //0x000023e7 jne          LBB0_597
	0x89, 0xd0, //0x000023ed movl         %edx, %eax
	0x25, 0x7f, 0x7f, 0x7f, 0x7f, //0x000023ef andl         $2139062143, %eax
	0x41, 0xb8, 0xc0, 0xc0, 0xc0, 0xc0, //0x000023f4 movl         $-1061109568, %r8d
	0x41, 0x29, 0xc0, //0x000023fa subl         %eax, %r8d
	0x44, 0x8d, 0x90, 0x46, 0x46, 0x46, 0x46, //0x000023fd leal         $1179010630(%rax), %r10d
	0x41, 0x21, 0xc8, //0x00002404 andl         %ecx, %r8d
	0x45, 0x85, 0xd0, //0x00002407 testl        %r10d, %r8d
	0x0f, 0x85, 0xba, 0x05, 0x00, 0x00, //0x0000240a jne          LBB0_647
	0x41, 0xb8, 0xe0, 0xe0, 0xe0, 0xe0, //0x00002410 movl         $-522133280, %r8d
	0x41, 0x29, 0xc0, //0x00002416 subl         %eax, %r8d
	0x05, 0x39, 0x39, 0x39, 0x39, //0x00002419 addl         $960051513, %eax
	0x44, 0x21, 0xc1, //0x0000241e andl         %r8d, %ecx
	0x85, 0xc1, //0x00002421 testl        %eax, %ecx
	0x4c, 0x8b, 0x55, 0xb8, //0x00002423 movq         $-72(%rbp), %r10
	0x0f, 0x85, 0x6c, 0x00, 0x00, 0x00, //0x00002427 jne          LBB0_597
	0x0f, 0xca, //0x0000242d bswapl       %edx
	0x89, 0xd0, //0x0000242f movl         %edx, %eax
	0xc1, 0xe8, 0x04, //0x00002431 shrl         $4, %eax
	0xf7, 0xd0, //0x00002434 notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x00002436 andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x0000243b leal         (%rax,%rax,8), %eax
	0x81, 0xe2, 0x0f, 0x0f, 0x0f, 0x0f, //0x0000243e andl         $252645135, %edx
	0x01, 0xc2, //0x00002444 addl         %eax, %edx
	0x89, 0xd1, //0x00002446 movl         %edx, %ecx
	0xc1, 0xe9, 0x04, //0x00002448 shrl         $4, %ecx
	0x09, 0xd1, //0x0000244b orl          %edx, %ecx
	0x89, 0xc8, //0x0000244d movl         %ecx, %eax
	0xc1, 0xe8, 0x08, //0x0000244f shrl         $8, %eax
	0x25, 0x00, 0xff, 0x00, 0x00, //0x00002452 andl         $65280, %eax
	0x89, 0xca, //0x00002457 movl         %ecx, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x00002459 andl         $128, %edx
	0x48, 0x83, 0xc7, 0x06, //0x0000245f addq         $6, %rdi
	0x09, 0xc2, //0x00002463 orl          %eax, %edx
	0x49, 0x89, 0xfd, //0x00002465 movq         %rdi, %r13
	0x48, 0x89, 0xfe, //0x00002468 movq         %rdi, %rsi
	0x0f, 0x85, 0x28, 0x00, 0x00, 0x00, //0x0000246b jne          LBB0_597
	//0x00002471 LBB0_550
	0x80, 0xf9, 0x0a, //0x00002471 cmpb         $10, %cl
	0x0f, 0x84, 0xdf, 0xfe, 0xff, 0xff, //0x00002474 je           LBB0_553
	0x80, 0xf9, 0x0d, //0x0000247a cmpb         $13, %cl
	0x0f, 0x84, 0xd6, 0xfe, 0xff, 0xff, //0x0000247d je           LBB0_553
	0x80, 0xf9, 0x3d, //0x00002483 cmpb         $61, %cl
	0x0f, 0x85, 0x0a, 0x00, 0x00, 0x00, //0x00002486 jne          LBB0_596
	0x83, 0x7d, 0xa8, 0x02, //0x0000248c cmpl         $2, $-88(%rbp)
	0x0f, 0x85, 0x04, 0x04, 0x00, 0x00, //0x00002490 jne          LBB0_626
	//0x00002496 LBB0_596
	0x4c, 0x89, 0xee, //0x00002496 movq         %r13, %rsi
	//0x00002499 LBB0_597
	0x31, 0xc9, //0x00002499 xorl         %ecx, %ecx
	0x4c, 0x39, 0xce, //0x0000249b cmpq         %r9, %rsi
	0x0f, 0x94, 0xc1, //0x0000249e sete         %cl
	0x48, 0x01, 0xf1, //0x000024a1 addq         %rsi, %rcx
	0x4c, 0x39, 0xd9, //0x000024a4 cmpq         %r11, %rcx
	0x44, 0x8b, 0x6d, 0xcc, //0x000024a7 movl         $-52(%rbp), %r13d
	0x4c, 0x89, 0xfa, //0x000024ab movq         %r15, %rdx
	0x0f, 0x85, 0xaf, 0x11, 0x00, 0x00, //0x000024ae jne          LBB0_864
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000024b4 .p2align 4, 0x90
	//0x000024c0 LBB0_598
	0x4d, 0x39, 0xf3, //0x000024c0 cmpq         %r14, %r11
	0x0f, 0x87, 0x59, 0x05, 0x00, 0x00, //0x000024c3 ja           LBB0_658
	0x4c, 0x39, 0xd2, //0x000024c9 cmpq         %r10, %rdx
	0x0f, 0x86, 0xbe, 0xf8, 0xff, 0xff, //0x000024cc jbe          LBB0_444
	0xe9, 0x4b, 0x05, 0x00, 0x00, //0x000024d2 jmp          LBB0_658
	//0x000024d7 LBB0_554
	0x48, 0x89, 0xc6, //0x000024d7 movq         %rax, %rsi
	0x4c, 0x8b, 0x55, 0xb8, //0x000024da movq         $-72(%rbp), %r10
	0xe9, 0xc3, 0xfb, 0xff, 0xff, //0x000024de jmp          LBB0_489
	//0x000024e3 LBB0_555
	0x48, 0x83, 0xc7, 0x01, //0x000024e3 addq         $1, %rdi
	0x48, 0x83, 0xc1, 0x01, //0x000024e7 addq         $1, %rcx
	0x48, 0x39, 0xc8, //0x000024eb cmpq         %rcx, %rax
	0x0f, 0x84, 0x01, 0xfc, 0xff, 0xff, //0x000024ee je           LBB0_494
	//0x000024f4 LBB0_556
	0x0f, 0xb6, 0x57, 0xff, //0x000024f4 movzbl       $-1(%rdi), %edx
	0x80, 0xfa, 0x0d, //0x000024f8 cmpb         $13, %dl
	0x0f, 0x84, 0xe2, 0xff, 0xff, 0xff, //0x000024fb je           LBB0_555
	0x80, 0xfa, 0x0a, //0x00002501 cmpb         $10, %dl
	0x0f, 0x84, 0xd9, 0xff, 0xff, 0xff, //0x00002504 je           LBB0_555
	0x48, 0x89, 0xfe, //0x0000250a movq         %rdi, %rsi
	0xe9, 0x87, 0xff, 0xff, 0xff, //0x0000250d jmp          LBB0_597
	//0x00002512 LBB0_559
	0x48, 0x89, 0xce, //0x00002512 movq         %rcx, %rsi
	0xe9, 0x5b, 0x00, 0x00, 0x00, //0x00002515 jmp          LBB0_568
	//0x0000251a LBB0_560
	0x48, 0x83, 0xc6, 0x01, //0x0000251a addq         $1, %rsi
	0xbb, 0x03, 0x00, 0x00, 0x00, //0x0000251e movl         $3, %ebx
	0x4c, 0x39, 0xce, //0x00002523 cmpq         %r9, %rsi
	0x0f, 0x83, 0xf4, 0xfa, 0xff, 0xff, //0x00002526 jae          LBB0_478
	//0x0000252c LBB0_561
	0x44, 0x0f, 0xb6, 0x06, //0x0000252c movzbl       (%rsi), %r8d
	0x49, 0x83, 0xf8, 0x0d, //0x00002530 cmpq         $13, %r8
	0x0f, 0x84, 0xe0, 0xff, 0xff, 0xff, //0x00002534 je           LBB0_560
	0x41, 0x80, 0xf8, 0x0a, //0x0000253a cmpb         $10, %r8b
	0x0f, 0x84, 0xd6, 0xff, 0xff, 0xff, //0x0000253e je           LBB0_560
	0x48, 0x8b, 0x45, 0xd0, //0x00002544 movq         $-48(%rbp), %rax
	0x42, 0x0f, 0xb6, 0x04, 0x00, //0x00002548 movzbl       (%rax,%r8), %eax
	0x48, 0x83, 0xc6, 0x01, //0x0000254d addq         $1, %rsi
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00002551 cmpl         $255, %eax
	0x0f, 0x85, 0x9d, 0x04, 0x00, 0x00, //0x00002556 jne          LBB0_652
	//0x0000255c LBB0_653
	0xbb, 0x03, 0x00, 0x00, 0x00, //0x0000255c movl         $3, %ebx
	0xe9, 0xf4, 0xfb, 0xff, 0xff, //0x00002561 jmp          LBB0_502
	//0x00002566 LBB0_565
	0x80, 0xf9, 0x2f, //0x00002566 cmpb         $47, %cl
	0x0f, 0x85, 0x03, 0x00, 0x00, 0x00, //0x00002569 jne          LBB0_567
	0x41, 0xb0, 0x2f, //0x0000256f movb         $47, %r8b
	//0x00002572 LBB0_567
	0x48, 0x89, 0xc6, //0x00002572 movq         %rax, %rsi
	//0x00002575 LBB0_568
	0x41, 0x0f, 0xb6, 0xc0, //0x00002575 movzbl       %r8b, %eax
	0x48, 0x8b, 0x4d, 0xd0, //0x00002579 movq         $-48(%rbp), %rcx
	0x0f, 0xb6, 0x04, 0x01, //0x0000257d movzbl       (%rcx,%rax), %eax
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00002581 cmpl         $255, %eax
	0x0f, 0x84, 0x52, 0x01, 0x00, 0x00, //0x00002586 je           LBB0_588
	0x41, 0xc1, 0xe4, 0x06, //0x0000258c shll         $6, %r12d
	0x41, 0x09, 0xc4, //0x00002590 orl          %eax, %r12d
	0xbb, 0x02, 0x00, 0x00, 0x00, //0x00002593 movl         $2, %ebx
	0x4c, 0x39, 0xce, //0x00002598 cmpq         %r9, %rsi
	0x0f, 0x82, 0x1f, 0x00, 0x00, 0x00, //0x0000259b jb           LBB0_572
	0xe9, 0x28, 0xfb, 0xff, 0xff, //0x000025a1 jmp          LBB0_491
	//0x000025a6 LBB0_586
	0x80, 0xf9, 0x6e, //0x000025a6 cmpb         $110, %cl
	0x0f, 0x85, 0x5d, 0x01, 0x00, 0x00, //0x000025a9 jne          LBB0_600
	//0x000025af LBB0_570
	0x48, 0x89, 0xc6, //0x000025af movq         %rax, %rsi
	//0x000025b2 LBB0_571
	0xbb, 0x02, 0x00, 0x00, 0x00, //0x000025b2 movl         $2, %ebx
	0x4c, 0x39, 0xce, //0x000025b7 cmpq         %r9, %rsi
	0x0f, 0x83, 0x60, 0xfa, 0xff, 0xff, //0x000025ba jae          LBB0_478
	//0x000025c0 LBB0_572
	0x48, 0x8d, 0x4e, 0x01, //0x000025c0 leaq         $1(%rsi), %rcx
	0x0f, 0xb6, 0x06, //0x000025c4 movzbl       (%rsi), %eax
	0x3c, 0x5c, //0x000025c7 cmpb         $92, %al
	0x0f, 0x85, 0xf4, 0x00, 0x00, 0x00, //0x000025c9 jne          LBB0_584
	0x48, 0x8d, 0x46, 0x02, //0x000025cf leaq         $2(%rsi), %rax
	0x41, 0xb0, 0xff, //0x000025d3 movb         $-1, %r8b
	0x4c, 0x39, 0xc8, //0x000025d6 cmpq         %r9, %rax
	0x0f, 0x87, 0x25, 0x01, 0x00, 0x00, //0x000025d9 ja           LBB0_593
	0x0f, 0xb6, 0x09, //0x000025df movzbl       (%rcx), %ecx
	0x80, 0xf9, 0x71, //0x000025e2 cmpb         $113, %cl
	0x0f, 0x8e, 0xbb, 0xff, 0xff, 0xff, //0x000025e5 jle          LBB0_586
	0x80, 0xf9, 0x72, //0x000025eb cmpb         $114, %cl
	0x0f, 0x84, 0xbb, 0xff, 0xff, 0xff, //0x000025ee je           LBB0_570
	0x80, 0xf9, 0x75, //0x000025f4 cmpb         $117, %cl
	0x0f, 0x85, 0x1b, 0x01, 0x00, 0x00, //0x000025f7 jne          LBB0_602
	0x4c, 0x89, 0xc9, //0x000025fd movq         %r9, %rcx
	0x48, 0x29, 0xc1, //0x00002600 subq         %rax, %rcx
	0x48, 0x83, 0xf9, 0x04, //0x00002603 cmpq         $4, %rcx
	0x0f, 0x8c, 0x0b, 0x01, 0x00, 0x00, //0x00002607 jl           LBB0_602
	0x8b, 0x08, //0x0000260d movl         (%rax), %ecx
	0x89, 0xcb, //0x0000260f movl         %ecx, %ebx
	0xf7, 0xd3, //0x00002611 notl         %ebx
	0x8d, 0x91, 0xd0, 0xcf, 0xcf, 0xcf, //0x00002613 leal         $-808464432(%rcx), %edx
	0x81, 0xe3, 0x80, 0x80, 0x80, 0x80, //0x00002619 andl         $-2139062144, %ebx
	0x85, 0xd3, //0x0000261f testl        %edx, %ebx
	0x0f, 0x85, 0xf1, 0x00, 0x00, 0x00, //0x00002621 jne          LBB0_602
	0x8d, 0x91, 0x19, 0x19, 0x19, 0x19, //0x00002627 leal         $421075225(%rcx), %edx
	0x09, 0xca, //0x0000262d orl          %ecx, %edx
	0xf7, 0xc2, 0x80, 0x80, 0x80, 0x80, //0x0000262f testl        $-2139062144, %edx
	0x0f, 0x85, 0xdd, 0x00, 0x00, 0x00, //0x00002635 jne          LBB0_602
	0x89, 0xca, //0x0000263b movl         %ecx, %edx
	0x81, 0xe2, 0x7f, 0x7f, 0x7f, 0x7f, //0x0000263d andl         $2139062143, %edx
	0xbf, 0xc0, 0xc0, 0xc0, 0xc0, //0x00002643 movl         $-1061109568, %edi
	0x29, 0xd7, //0x00002648 subl         %edx, %edi
	0x44, 0x8d, 0x92, 0x46, 0x46, 0x46, 0x46, //0x0000264a leal         $1179010630(%rdx), %r10d
	0x21, 0xdf, //0x00002651 andl         %ebx, %edi
	0x44, 0x85, 0xd7, //0x00002653 testl        %r10d, %edi
	0x0f, 0x85, 0x2a, 0x02, 0x00, 0x00, //0x00002656 jne          LBB0_624
	0xbf, 0xe0, 0xe0, 0xe0, 0xe0, //0x0000265c movl         $-522133280, %edi
	0x29, 0xd7, //0x00002661 subl         %edx, %edi
	0x81, 0xc2, 0x39, 0x39, 0x39, 0x39, //0x00002663 addl         $960051513, %edx
	0x21, 0xfb, //0x00002669 andl         %edi, %ebx
	0x85, 0xd3, //0x0000266b testl        %edx, %ebx
	0x4c, 0x8b, 0x55, 0xb8, //0x0000266d movq         $-72(%rbp), %r10
	0x0f, 0x85, 0xa1, 0x00, 0x00, 0x00, //0x00002671 jne          LBB0_602
	0x0f, 0xc9, //0x00002677 bswapl       %ecx
	0x89, 0xc8, //0x00002679 movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x0000267b shrl         $4, %eax
	0xf7, 0xd0, //0x0000267e notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x00002680 andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x00002685 leal         (%rax,%rax,8), %eax
	0x81, 0xe1, 0x0f, 0x0f, 0x0f, 0x0f, //0x00002688 andl         $252645135, %ecx
	0x01, 0xc1, //0x0000268e addl         %eax, %ecx
	0x89, 0xc8, //0x00002690 movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x00002692 shrl         $4, %eax
	0x09, 0xc8, //0x00002695 orl          %ecx, %eax
	0x89, 0xc1, //0x00002697 movl         %eax, %ecx
	0xc1, 0xe9, 0x08, //0x00002699 shrl         $8, %ecx
	0x81, 0xe1, 0x00, 0xff, 0x00, 0x00, //0x0000269c andl         $65280, %ecx
	0x89, 0xc2, //0x000026a2 movl         %eax, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x000026a4 andl         $128, %edx
	0x48, 0x83, 0xc6, 0x06, //0x000026aa addq         $6, %rsi
	0x09, 0xca, //0x000026ae orl          %ecx, %edx
	0x0f, 0x85, 0x65, 0x00, 0x00, 0x00, //0x000026b0 jne          LBB0_603
	0x3c, 0x0d, //0x000026b6 cmpb         $13, %al
	0x0f, 0x85, 0x10, 0x00, 0x00, 0x00, //0x000026b8 jne          LBB0_585
	0xe9, 0xef, 0xfe, 0xff, 0xff, //0x000026be jmp          LBB0_571
	//0x000026c3 LBB0_584
	0x48, 0x89, 0xce, //0x000026c3 movq         %rcx, %rsi
	0x3c, 0x0d, //0x000026c6 cmpb         $13, %al
	0x0f, 0x84, 0xe4, 0xfe, 0xff, 0xff, //0x000026c8 je           LBB0_571
	//0x000026ce LBB0_585
	0x41, 0x89, 0xc0, //0x000026ce movl         %eax, %r8d
	0x3c, 0x0a, //0x000026d1 cmpb         $10, %al
	0x0f, 0x84, 0xd9, 0xfe, 0xff, 0xff, //0x000026d3 je           LBB0_571
	0xe9, 0x3d, 0x00, 0x00, 0x00, //0x000026d9 jmp          LBB0_603
	//0x000026de LBB0_588
	0xbb, 0x01, 0x00, 0x00, 0x00, //0x000026de movl         $1, %ebx
	0xe9, 0x72, 0xfa, 0xff, 0xff, //0x000026e3 jmp          LBB0_502
	//0x000026e8 LBB0_589
	0x4c, 0x89, 0xce, //0x000026e8 movq         %r9, %rsi
	0xe9, 0x05, 0xfa, 0xff, 0xff, //0x000026eb jmp          LBB0_494
	//0x000026f0 LBB0_591
	0x48, 0x89, 0xc6, //0x000026f0 movq         %rax, %rsi
	0x4c, 0x8b, 0x55, 0xb8, //0x000026f3 movq         $-72(%rbp), %r10
	0xe9, 0x79, 0xfe, 0xff, 0xff, //0x000026f7 jmp          LBB0_568
	//0x000026fc LBB0_592
	0x48, 0x89, 0xfe, //0x000026fc movq         %rdi, %rsi
	0xe9, 0xf1, 0xf9, 0xff, 0xff, //0x000026ff jmp          LBB0_494
	//0x00002704 LBB0_593
	0x48, 0x89, 0xce, //0x00002704 movq         %rcx, %rsi
	0xe9, 0x0f, 0x00, 0x00, 0x00, //0x00002707 jmp          LBB0_603
	//0x0000270c LBB0_600
	0x80, 0xf9, 0x2f, //0x0000270c cmpb         $47, %cl
	0x0f, 0x85, 0x03, 0x00, 0x00, 0x00, //0x0000270f jne          LBB0_602
	0x41, 0xb0, 0x2f, //0x00002715 movb         $47, %r8b
	//0x00002718 LBB0_602
	0x48, 0x89, 0xc6, //0x00002718 movq         %rax, %rsi
	//0x0000271b LBB0_603
	0x41, 0x0f, 0xb6, 0xc0, //0x0000271b movzbl       %r8b, %eax
	0x48, 0x8b, 0x4d, 0xd0, //0x0000271f movq         $-48(%rbp), %rcx
	0x0f, 0xb6, 0x04, 0x01, //0x00002723 movzbl       (%rcx,%rax), %eax
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00002727 cmpl         $255, %eax
	0x0f, 0x84, 0x4a, 0x01, 0x00, 0x00, //0x0000272c je           LBB0_623
	0x41, 0xc1, 0xe4, 0x06, //0x00002732 shll         $6, %r12d
	0x41, 0x09, 0xc4, //0x00002736 orl          %eax, %r12d
	0xbb, 0x03, 0x00, 0x00, 0x00, //0x00002739 movl         $3, %ebx
	0x4c, 0x39, 0xce, //0x0000273e cmpq         %r9, %rsi
	0x0f, 0x82, 0x1f, 0x00, 0x00, 0x00, //0x00002741 jb           LBB0_607
	0xe9, 0x82, 0xf9, 0xff, 0xff, //0x00002747 jmp          LBB0_491
	//0x0000274c LBB0_621
	0x80, 0xf9, 0x6e, //0x0000274c cmpb         $110, %cl
	0x0f, 0x85, 0x7e, 0x02, 0x00, 0x00, //0x0000274f jne          LBB0_648
	//0x00002755 LBB0_605
	0x48, 0x89, 0xc6, //0x00002755 movq         %rax, %rsi
	//0x00002758 LBB0_606
	0xbb, 0x03, 0x00, 0x00, 0x00, //0x00002758 movl         $3, %ebx
	0x4c, 0x39, 0xce, //0x0000275d cmpq         %r9, %rsi
	0x0f, 0x83, 0xba, 0xf8, 0xff, 0xff, //0x00002760 jae          LBB0_478
	//0x00002766 LBB0_607
	0x48, 0x8d, 0x4e, 0x01, //0x00002766 leaq         $1(%rsi), %rcx
	0x0f, 0xb6, 0x06, //0x0000276a movzbl       (%rsi), %eax
	0x3c, 0x5c, //0x0000276d cmpb         $92, %al
	0x0f, 0x85, 0xec, 0x00, 0x00, 0x00, //0x0000276f jne          LBB0_618
	0x48, 0x8d, 0x46, 0x02, //0x00002775 leaq         $2(%rsi), %rax
	0x41, 0xb0, 0xff, //0x00002779 movb         $-1, %r8b
	0x4c, 0x39, 0xc8, //0x0000277c cmpq         %r9, %rax
	0x0f, 0x87, 0x0d, 0x01, 0x00, 0x00, //0x0000277f ja           LBB0_625
	0x0f, 0xb6, 0x09, //0x00002785 movzbl       (%rcx), %ecx
	0x80, 0xf9, 0x71, //0x00002788 cmpb         $113, %cl
	0x0f, 0x8e, 0xbb, 0xff, 0xff, 0xff, //0x0000278b jle          LBB0_621
	0x80, 0xf9, 0x72, //0x00002791 cmpb         $114, %cl
	0x0f, 0x84, 0xbb, 0xff, 0xff, 0xff, //0x00002794 je           LBB0_605
	0x80, 0xf9, 0x75, //0x0000279a cmpb         $117, %cl
	0x0f, 0x85, 0x3c, 0x02, 0x00, 0x00, //0x0000279d jne          LBB0_650
	0x4c, 0x89, 0xc9, //0x000027a3 movq         %r9, %rcx
	0x48, 0x29, 0xc1, //0x000027a6 subq         %rax, %rcx
	0x48, 0x83, 0xf9, 0x04, //0x000027a9 cmpq         $4, %rcx
	0x0f, 0x8c, 0x2c, 0x02, 0x00, 0x00, //0x000027ad jl           LBB0_650
	0x8b, 0x08, //0x000027b3 movl         (%rax), %ecx
	0x89, 0xcb, //0x000027b5 movl         %ecx, %ebx
	0xf7, 0xd3, //0x000027b7 notl         %ebx
	0x8d, 0x91, 0xd0, 0xcf, 0xcf, 0xcf, //0x000027b9 leal         $-808464432(%rcx), %edx
	0x81, 0xe3, 0x80, 0x80, 0x80, 0x80, //0x000027bf andl         $-2139062144, %ebx
	0x85, 0xd3, //0x000027c5 testl        %edx, %ebx
	0x0f, 0x85, 0x12, 0x02, 0x00, 0x00, //0x000027c7 jne          LBB0_650
	0x8d, 0x91, 0x19, 0x19, 0x19, 0x19, //0x000027cd leal         $421075225(%rcx), %edx
	0x09, 0xca, //0x000027d3 orl          %ecx, %edx
	0xf7, 0xc2, 0x80, 0x80, 0x80, 0x80, //0x000027d5 testl        $-2139062144, %edx
	0x0f, 0x85, 0xfe, 0x01, 0x00, 0x00, //0x000027db jne          LBB0_650
	0x89, 0xca, //0x000027e1 movl         %ecx, %edx
	0x81, 0xe2, 0x7f, 0x7f, 0x7f, 0x7f, //0x000027e3 andl         $2139062143, %edx
	0xbf, 0xc0, 0xc0, 0xc0, 0xc0, //0x000027e9 movl         $-1061109568, %edi
	0x29, 0xd7, //0x000027ee subl         %edx, %edi
	0x44, 0x8d, 0x92, 0x46, 0x46, 0x46, 0x46, //0x000027f0 leal         $1179010630(%rdx), %r10d
	0x21, 0xdf, //0x000027f7 andl         %ebx, %edi
	0x44, 0x85, 0xd7, //0x000027f9 testl        %r10d, %edi
	0x0f, 0x85, 0x08, 0x02, 0x00, 0x00, //0x000027fc jne          LBB0_654
	0xbf, 0xe0, 0xe0, 0xe0, 0xe0, //0x00002802 movl         $-522133280, %edi
	0x29, 0xd7, //0x00002807 subl         %edx, %edi
	0x81, 0xc2, 0x39, 0x39, 0x39, 0x39, //0x00002809 addl         $960051513, %edx
	0x21, 0xfb, //0x0000280f andl         %edi, %ebx
	0x85, 0xd3, //0x00002811 testl        %edx, %ebx
	0x4c, 0x8b, 0x55, 0xb8, //0x00002813 movq         $-72(%rbp), %r10
	0x0f, 0x85, 0xc2, 0x01, 0x00, 0x00, //0x00002817 jne          LBB0_650
	0x0f, 0xc9, //0x0000281d bswapl       %ecx
	0x89, 0xc8, //0x0000281f movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x00002821 shrl         $4, %eax
	0xf7, 0xd0, //0x00002824 notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x00002826 andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x0000282b leal         (%rax,%rax,8), %eax
	0x81, 0xe1, 0x0f, 0x0f, 0x0f, 0x0f, //0x0000282e andl         $252645135, %ecx
	0x01, 0xc1, //0x00002834 addl         %eax, %ecx
	0x89, 0xc8, //0x00002836 movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x00002838 shrl         $4, %eax
	0x09, 0xc8, //0x0000283b orl          %ecx, %eax
	0x89, 0xc1, //0x0000283d movl         %eax, %ecx
	0xc1, 0xe9, 0x08, //0x0000283f shrl         $8, %ecx
	0x81, 0xe1, 0x00, 0xff, 0x00, 0x00, //0x00002842 andl         $65280, %ecx
	0x89, 0xc2, //0x00002848 movl         %eax, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x0000284a andl         $128, %edx
	0x48, 0x83, 0xc6, 0x06, //0x00002850 addq         $6, %rsi
	0x09, 0xca, //0x00002854 orl          %ecx, %edx
	0x0f, 0x84, 0x08, 0x00, 0x00, 0x00, //0x00002856 je           LBB0_619
	0xe9, 0x81, 0x01, 0x00, 0x00, //0x0000285c jmp          LBB0_651
	//0x00002861 LBB0_618
	0x48, 0x89, 0xce, //0x00002861 movq         %rcx, %rsi
	//0x00002864 LBB0_619
	0x3c, 0x0d, //0x00002864 cmpb         $13, %al
	0x0f, 0x84, 0xec, 0xfe, 0xff, 0xff, //0x00002866 je           LBB0_606
	0x41, 0x89, 0xc0, //0x0000286c movl         %eax, %r8d
	0x3c, 0x0a, //0x0000286f cmpb         $10, %al
	0x0f, 0x84, 0xe1, 0xfe, 0xff, 0xff, //0x00002871 je           LBB0_606
	0xe9, 0x66, 0x01, 0x00, 0x00, //0x00002877 jmp          LBB0_651
	//0x0000287c LBB0_623
	0xbb, 0x02, 0x00, 0x00, 0x00, //0x0000287c movl         $2, %ebx
	0xe9, 0xd4, 0xf8, 0xff, 0xff, //0x00002881 jmp          LBB0_502
	//0x00002886 LBB0_624
	0x48, 0x89, 0xc6, //0x00002886 movq         %rax, %rsi
	0x4c, 0x8b, 0x55, 0xb8, //0x00002889 movq         $-72(%rbp), %r10
	0xe9, 0x89, 0xfe, 0xff, 0xff, //0x0000288d jmp          LBB0_603
	//0x00002892 LBB0_625
	0x48, 0x89, 0xce, //0x00002892 movq         %rcx, %rsi
	0xe9, 0x48, 0x01, 0x00, 0x00, //0x00002895 jmp          LBB0_651
	//0x0000289a LBB0_626
	0x4d, 0x39, 0xcd, //0x0000289a cmpq         %r9, %r13
	0x0f, 0x82, 0x14, 0x00, 0x00, 0x00, //0x0000289d jb           LBB0_630
	//0x000028a3 LBB0_627
	0x4c, 0x89, 0xee, //0x000028a3 movq         %r13, %rsi
	0xe9, 0x4a, 0xf8, 0xff, 0xff, //0x000028a6 jmp          LBB0_494
	//0x000028ab LBB0_628
	0x49, 0x89, 0xf5, //0x000028ab movq         %rsi, %r13
	//0x000028ae LBB0_629
	0x4d, 0x39, 0xcd, //0x000028ae cmpq         %r9, %r13
	0x0f, 0x83, 0xec, 0xff, 0xff, 0xff, //0x000028b1 jae          LBB0_627
	//0x000028b7 LBB0_630
	0x49, 0x8d, 0x45, 0x01, //0x000028b7 leaq         $1(%r13), %rax
	0x41, 0x0f, 0xb6, 0x4d, 0x00, //0x000028bb movzbl       (%r13), %ecx
	0x80, 0xf9, 0x5c, //0x000028c0 cmpb         $92, %cl
	0x0f, 0x85, 0xe4, 0x00, 0x00, 0x00, //0x000028c3 jne          LBB0_641
	0x49, 0x8d, 0x75, 0x02, //0x000028c9 leaq         $2(%r13), %rsi
	0x4c, 0x39, 0xce, //0x000028cd cmpq         %r9, %rsi
	0x0f, 0x87, 0x40, 0x01, 0x00, 0x00, //0x000028d0 ja           LBB0_656
	0x0f, 0xb6, 0x00, //0x000028d6 movzbl       (%rax), %eax
	0x3c, 0x6e, //0x000028d9 cmpb         $110, %al
	0x0f, 0x84, 0xca, 0xff, 0xff, 0xff, //0x000028db je           LBB0_628
	0x3c, 0x72, //0x000028e1 cmpb         $114, %al
	0x0f, 0x84, 0xc2, 0xff, 0xff, 0xff, //0x000028e3 je           LBB0_628
	0x3c, 0x75, //0x000028e9 cmpb         $117, %al
	0x0f, 0x85, 0xa8, 0xfb, 0xff, 0xff, //0x000028eb jne          LBB0_597
	0x4c, 0x89, 0xc8, //0x000028f1 movq         %r9, %rax
	0x48, 0x29, 0xf0, //0x000028f4 subq         %rsi, %rax
	0x48, 0x83, 0xf8, 0x04, //0x000028f7 cmpq         $4, %rax
	0x0f, 0x8c, 0x98, 0xfb, 0xff, 0xff, //0x000028fb jl           LBB0_597
	0x8b, 0x06, //0x00002901 movl         (%rsi), %eax
	0x89, 0xc1, //0x00002903 movl         %eax, %ecx
	0xf7, 0xd1, //0x00002905 notl         %ecx
	0x8d, 0x90, 0xd0, 0xcf, 0xcf, 0xcf, //0x00002907 leal         $-808464432(%rax), %edx
	0x81, 0xe1, 0x80, 0x80, 0x80, 0x80, //0x0000290d andl         $-2139062144, %ecx
	0x85, 0xd1, //0x00002913 testl        %edx, %ecx
	0x0f, 0x85, 0x7e, 0xfb, 0xff, 0xff, //0x00002915 jne          LBB0_597
	0x8d, 0x90, 0x19, 0x19, 0x19, 0x19, //0x0000291b leal         $421075225(%rax), %edx
	0x09, 0xc2, //0x00002921 orl          %eax, %edx
	0xf7, 0xc2, 0x80, 0x80, 0x80, 0x80, //0x00002923 testl        $-2139062144, %edx
	0x0f, 0x85, 0x6a, 0xfb, 0xff, 0xff, //0x00002929 jne          LBB0_597
	0x89, 0xc2, //0x0000292f movl         %eax, %edx
	0x81, 0xe2, 0x7f, 0x7f, 0x7f, 0x7f, //0x00002931 andl         $2139062143, %edx
	0xbf, 0xc0, 0xc0, 0xc0, 0xc0, //0x00002937 movl         $-1061109568, %edi
	0x29, 0xd7, //0x0000293c subl         %edx, %edi
	0x44, 0x8d, 0x82, 0x46, 0x46, 0x46, 0x46, //0x0000293e leal         $1179010630(%rdx), %r8d
	0x21, 0xcf, //0x00002945 andl         %ecx, %edi
	0x44, 0x85, 0xc7, //0x00002947 testl        %r8d, %edi
	0x0f, 0x85, 0x49, 0xfb, 0xff, 0xff, //0x0000294a jne          LBB0_597
	0xbf, 0xe0, 0xe0, 0xe0, 0xe0, //0x00002950 movl         $-522133280, %edi
	0x29, 0xd7, //0x00002955 subl         %edx, %edi
	0x81, 0xc2, 0x39, 0x39, 0x39, 0x39, //0x00002957 addl         $960051513, %edx
	0x21, 0xf9, //0x0000295d andl         %edi, %ecx
	0x85, 0xd1, //0x0000295f testl        %edx, %ecx
	0x0f, 0x85, 0x32, 0xfb, 0xff, 0xff, //0x00002961 jne          LBB0_597
	0x0f, 0xc8, //0x00002967 bswapl       %eax
	0x89, 0xc1, //0x00002969 movl         %eax, %ecx
	0xc1, 0xe9, 0x04, //0x0000296b shrl         $4, %ecx
	0xf7, 0xd1, //0x0000296e notl         %ecx
	0x81, 0xe1, 0x01, 0x01, 0x01, 0x01, //0x00002970 andl         $16843009, %ecx
	0x8d, 0x0c, 0xc9, //0x00002976 leal         (%rcx,%rcx,8), %ecx
	0x25, 0x0f, 0x0f, 0x0f, 0x0f, //0x00002979 andl         $252645135, %eax
	0x01, 0xc8, //0x0000297e addl         %ecx, %eax
	0x89, 0xc1, //0x00002980 movl         %eax, %ecx
	0xc1, 0xe9, 0x04, //0x00002982 shrl         $4, %ecx
	0x09, 0xc1, //0x00002985 orl          %eax, %ecx
	0x89, 0xc8, //0x00002987 movl         %ecx, %eax
	0xc1, 0xe8, 0x08, //0x00002989 shrl         $8, %eax
	0x25, 0x00, 0xff, 0x00, 0x00, //0x0000298c andl         $65280, %eax
	0x89, 0xca, //0x00002991 movl         %ecx, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x00002993 andl         $128, %edx
	0x49, 0x83, 0xc5, 0x06, //0x00002999 addq         $6, %r13
	0x09, 0xc2, //0x0000299d orl          %eax, %edx
	0x4c, 0x89, 0xee, //0x0000299f movq         %r13, %rsi
	0x0f, 0x84, 0x08, 0x00, 0x00, 0x00, //0x000029a2 je           LBB0_642
	0xe9, 0xec, 0xfa, 0xff, 0xff, //0x000029a8 jmp          LBB0_597
	//0x000029ad LBB0_641
	0x48, 0x89, 0xc6, //0x000029ad movq         %rax, %rsi
	//0x000029b0 LBB0_642
	0x80, 0xf9, 0x0d, //0x000029b0 cmpb         $13, %cl
	0x0f, 0x84, 0xf2, 0xfe, 0xff, 0xff, //0x000029b3 je           LBB0_628
	0x49, 0x89, 0xf5, //0x000029b9 movq         %rsi, %r13
	0x80, 0xf9, 0x0a, //0x000029bc cmpb         $10, %cl
	0x0f, 0x84, 0xe9, 0xfe, 0xff, 0xff, //0x000029bf je           LBB0_629
	0xe9, 0xcf, 0xfa, 0xff, 0xff, //0x000029c5 jmp          LBB0_597
	//0x000029ca LBB0_647
	0x4c, 0x8b, 0x55, 0xb8, //0x000029ca movq         $-72(%rbp), %r10
	0xe9, 0xc6, 0xfa, 0xff, 0xff, //0x000029ce jmp          LBB0_597
	//0x000029d3 LBB0_648
	0x80, 0xf9, 0x2f, //0x000029d3 cmpb         $47, %cl
	0x0f, 0x85, 0x03, 0x00, 0x00, 0x00, //0x000029d6 jne          LBB0_650
	0x41, 0xb0, 0x2f, //0x000029dc movb         $47, %r8b
	//0x000029df LBB0_650
	0x48, 0x89, 0xc6, //0x000029df movq         %rax, %rsi
	//0x000029e2 LBB0_651
	0x41, 0x0f, 0xb6, 0xc0, //0x000029e2 movzbl       %r8b, %eax
	0x48, 0x8b, 0x4d, 0xd0, //0x000029e6 movq         $-48(%rbp), %rcx
	0x0f, 0xb6, 0x04, 0x01, //0x000029ea movzbl       (%rcx,%rax), %eax
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x000029ee cmpl         $255, %eax
	0x0f, 0x84, 0x63, 0xfb, 0xff, 0xff, //0x000029f3 je           LBB0_653
	//0x000029f9 LBB0_652
	0x41, 0xc1, 0xe4, 0x06, //0x000029f9 shll         $6, %r12d
	0x41, 0x09, 0xc4, //0x000029fd orl          %eax, %r12d
	0xbb, 0x04, 0x00, 0x00, 0x00, //0x00002a00 movl         $4, %ebx
	0xe9, 0xc4, 0xf6, 0xff, 0xff, //0x00002a05 jmp          LBB0_491
	//0x00002a0a LBB0_654
	0x48, 0x89, 0xc6, //0x00002a0a movq         %rax, %rsi
	0x4c, 0x8b, 0x55, 0xb8, //0x00002a0d movq         $-72(%rbp), %r10
	0xe9, 0xcc, 0xff, 0xff, 0xff, //0x00002a11 jmp          LBB0_651
	//0x00002a16 LBB0_656
	0x48, 0x89, 0xc6, //0x00002a16 movq         %rax, %rsi
	0xe9, 0x7b, 0xfa, 0xff, 0xff, //0x00002a19 jmp          LBB0_597
	//0x00002a1e LBB0_657
	0x44, 0x8b, 0x6d, 0xcc, //0x00002a1e movl         $-52(%rbp), %r13d
	//0x00002a22 LBB0_658
	0x48, 0x8b, 0x45, 0xb0, //0x00002a22 movq         $-80(%rbp), %rax
	0x48, 0x8b, 0x4d, 0xa0, //0x00002a26 movq         $-96(%rbp), %rcx
	0x48, 0x01, 0xc8, //0x00002a2a addq         %rcx, %rax
	0x48, 0x83, 0xc0, 0xff, //0x00002a2d addq         $-1, %rax
	0x48, 0x89, 0x45, 0xc0, //0x00002a31 movq         %rax, $-64(%rbp)
	0xe9, 0x20, 0x00, 0x00, 0x00, //0x00002a35 jmp          LBB0_661
	//0x00002a3a LBB0_659
	0x48, 0x89, 0xc6, //0x00002a3a movq         %rax, %rsi
	0x90, 0x90, 0x90, //0x00002a3d .p2align 4, 0x90
	//0x00002a40 LBB0_660
	0x31, 0xc9, //0x00002a40 xorl         %ecx, %ecx
	0x4c, 0x39, 0xce, //0x00002a42 cmpq         %r9, %rsi
	0x0f, 0x94, 0xc1, //0x00002a45 sete         %cl
	0x48, 0x01, 0xf1, //0x00002a48 addq         %rsi, %rcx
	0x4c, 0x29, 0xd9, //0x00002a4b subq         %r11, %rcx
	0x48, 0x85, 0xc9, //0x00002a4e testq        %rcx, %rcx
	0x4c, 0x89, 0xe2, //0x00002a51 movq         %r12, %rdx
	0x0f, 0x85, 0x06, 0x0c, 0x00, 0x00, //0x00002a54 jne          LBB0_863
	//0x00002a5a LBB0_661
	0x4d, 0x39, 0xcb, //0x00002a5a cmpq         %r9, %r11
	0x0f, 0x83, 0xe9, 0x0b, 0x00, 0x00, //0x00002a5d jae          LBB0_862
	0x49, 0x89, 0xd4, //0x00002a63 movq         %rdx, %r12
	0x4c, 0x89, 0xde, //0x00002a66 movq         %r11, %rsi
	0x41, 0xf6, 0xc5, 0x08, //0x00002a69 testb        $8, %r13b
	0x0f, 0x85, 0xd9, 0x00, 0x00, 0x00, //0x00002a6d jne          LBB0_675
	0xe9, 0x15, 0x00, 0x00, 0x00, //0x00002a73 jmp          LBB0_664
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002a78 .p2align 4, 0x90
	//0x00002a80 LBB0_663
	0x48, 0x83, 0xc6, 0x01, //0x00002a80 addq         $1, %rsi
	0x4c, 0x39, 0xce, //0x00002a84 cmpq         %r9, %rsi
	0x0f, 0x83, 0xe3, 0x01, 0x00, 0x00, //0x00002a87 jae          LBB0_691
	//0x00002a8d LBB0_664
	0x44, 0x0f, 0xb6, 0x06, //0x00002a8d movzbl       (%rsi), %r8d
	0x49, 0x83, 0xf8, 0x0d, //0x00002a91 cmpq         $13, %r8
	0x0f, 0x84, 0xe5, 0xff, 0xff, 0xff, //0x00002a95 je           LBB0_663
	0x41, 0x80, 0xf8, 0x0a, //0x00002a9b cmpb         $10, %r8b
	0x0f, 0x84, 0xdb, 0xff, 0xff, 0xff, //0x00002a9f je           LBB0_663
	0x48, 0x8b, 0x45, 0xd0, //0x00002aa5 movq         $-48(%rbp), %rax
	0x46, 0x0f, 0xb6, 0x3c, 0x00, //0x00002aa9 movzbl       (%rax,%r8), %r15d
	0x48, 0x83, 0xc6, 0x01, //0x00002aae addq         $1, %rsi
	0x41, 0x81, 0xff, 0xff, 0x00, 0x00, 0x00, //0x00002ab2 cmpl         $255, %r15d
	0x0f, 0x84, 0xfa, 0x02, 0x00, 0x00, //0x00002ab9 je           LBB0_715
	0xbb, 0x01, 0x00, 0x00, 0x00, //0x00002abf movl         $1, %ebx
	0x4c, 0x39, 0xce, //0x00002ac4 cmpq         %r9, %rsi
	0x0f, 0x82, 0x20, 0x00, 0x00, 0x00, //0x00002ac7 jb           LBB0_669
	0xe9, 0x5e, 0x02, 0x00, 0x00, //0x00002acd jmp          LBB0_704
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002ad2 .p2align 4, 0x90
	//0x00002ae0 LBB0_668
	0x48, 0x83, 0xc6, 0x01, //0x00002ae0 addq         $1, %rsi
	0x4c, 0x39, 0xce, //0x00002ae4 cmpq         %r9, %rsi
	0x0f, 0x83, 0xbe, 0x04, 0x00, 0x00, //0x00002ae7 jae          LBB0_748
	//0x00002aed LBB0_669
	0x44, 0x0f, 0xb6, 0x06, //0x00002aed movzbl       (%rsi), %r8d
	0x49, 0x83, 0xf8, 0x0d, //0x00002af1 cmpq         $13, %r8
	0x0f, 0x84, 0xe5, 0xff, 0xff, 0xff, //0x00002af5 je           LBB0_668
	0x41, 0x80, 0xf8, 0x0a, //0x00002afb cmpb         $10, %r8b
	0x0f, 0x84, 0xdb, 0xff, 0xff, 0xff, //0x00002aff je           LBB0_668
	0x48, 0x8b, 0x45, 0xd0, //0x00002b05 movq         $-48(%rbp), %rax
	0x42, 0x0f, 0xb6, 0x04, 0x00, //0x00002b09 movzbl       (%rax,%r8), %eax
	0x48, 0x83, 0xc6, 0x01, //0x00002b0e addq         $1, %rsi
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00002b12 cmpl         $255, %eax
	0x0f, 0x84, 0x0e, 0x08, 0x00, 0x00, //0x00002b17 je           LBB0_801
	0x41, 0xc1, 0xe7, 0x06, //0x00002b1d shll         $6, %r15d
	0x41, 0x09, 0xc7, //0x00002b21 orl          %eax, %r15d
	0xbb, 0x02, 0x00, 0x00, 0x00, //0x00002b24 movl         $2, %ebx
	0x4c, 0x39, 0xce, //0x00002b29 cmpq         %r9, %rsi
	0x0f, 0x82, 0x70, 0x01, 0x00, 0x00, //0x00002b2c jb           LBB0_694
	0xe9, 0xf9, 0x01, 0x00, 0x00, //0x00002b32 jmp          LBB0_704
	//0x00002b37 LBB0_689
	0x80, 0xf9, 0x6e, //0x00002b37 cmpb         $110, %cl
	0x0f, 0x85, 0xb4, 0x01, 0x00, 0x00, //0x00002b3a jne          LBB0_699
	//0x00002b40 LBB0_673
	0x48, 0x89, 0xc6, //0x00002b40 movq         %rax, %rsi
	//0x00002b43 LBB0_674
	0x4c, 0x39, 0xce, //0x00002b43 cmpq         %r9, %rsi
	0x0f, 0x83, 0x24, 0x01, 0x00, 0x00, //0x00002b46 jae          LBB0_691
	//0x00002b4c LBB0_675
	0x48, 0x8d, 0x4e, 0x01, //0x00002b4c leaq         $1(%rsi), %rcx
	0x0f, 0xb6, 0x06, //0x00002b50 movzbl       (%rsi), %eax
	0x3c, 0x5c, //0x00002b53 cmpb         $92, %al
	0x0f, 0x85, 0xf5, 0x00, 0x00, 0x00, //0x00002b55 jne          LBB0_687
	0x48, 0x8d, 0x46, 0x02, //0x00002b5b leaq         $2(%rsi), %rax
	0x41, 0xb0, 0xff, //0x00002b5f movb         $-1, %r8b
	0x4c, 0x39, 0xc8, //0x00002b62 cmpq         %r9, %rax
	0x0f, 0x87, 0x81, 0x01, 0x00, 0x00, //0x00002b65 ja           LBB0_698
	0x0f, 0xb6, 0x09, //0x00002b6b movzbl       (%rcx), %ecx
	0x80, 0xf9, 0x71, //0x00002b6e cmpb         $113, %cl
	0x0f, 0x8e, 0xc0, 0xff, 0xff, 0xff, //0x00002b71 jle          LBB0_689
	0x80, 0xf9, 0x72, //0x00002b77 cmpb         $114, %cl
	0x0f, 0x84, 0xc0, 0xff, 0xff, 0xff, //0x00002b7a je           LBB0_673
	0x80, 0xf9, 0x75, //0x00002b80 cmpb         $117, %cl
	0x0f, 0x85, 0x77, 0x01, 0x00, 0x00, //0x00002b83 jne          LBB0_701
	0x4c, 0x89, 0xc9, //0x00002b89 movq         %r9, %rcx
	0x48, 0x29, 0xc1, //0x00002b8c subq         %rax, %rcx
	0x48, 0x83, 0xf9, 0x04, //0x00002b8f cmpq         $4, %rcx
	0x0f, 0x8c, 0x67, 0x01, 0x00, 0x00, //0x00002b93 jl           LBB0_701
	0x8b, 0x08, //0x00002b99 movl         (%rax), %ecx
	0x89, 0xca, //0x00002b9b movl         %ecx, %edx
	0xf7, 0xd2, //0x00002b9d notl         %edx
	0x8d, 0x99, 0xd0, 0xcf, 0xcf, 0xcf, //0x00002b9f leal         $-808464432(%rcx), %ebx
	0x81, 0xe2, 0x80, 0x80, 0x80, 0x80, //0x00002ba5 andl         $-2139062144, %edx
	0x85, 0xda, //0x00002bab testl        %ebx, %edx
	0x0f, 0x85, 0x4d, 0x01, 0x00, 0x00, //0x00002bad jne          LBB0_701
	0x8d, 0x99, 0x19, 0x19, 0x19, 0x19, //0x00002bb3 leal         $421075225(%rcx), %ebx
	0x09, 0xcb, //0x00002bb9 orl          %ecx, %ebx
	0xf7, 0xc3, 0x80, 0x80, 0x80, 0x80, //0x00002bbb testl        $-2139062144, %ebx
	0x0f, 0x85, 0x39, 0x01, 0x00, 0x00, //0x00002bc1 jne          LBB0_701
	0x89, 0xcb, //0x00002bc7 movl         %ecx, %ebx
	0x81, 0xe3, 0x7f, 0x7f, 0x7f, 0x7f, //0x00002bc9 andl         $2139062143, %ebx
	0xbf, 0xc0, 0xc0, 0xc0, 0xc0, //0x00002bcf movl         $-1061109568, %edi
	0x29, 0xdf, //0x00002bd4 subl         %ebx, %edi
	0x44, 0x8d, 0x93, 0x46, 0x46, 0x46, 0x46, //0x00002bd6 leal         $1179010630(%rbx), %r10d
	0x21, 0xd7, //0x00002bdd andl         %edx, %edi
	0x44, 0x85, 0xd7, //0x00002bdf testl        %r10d, %edi
	0x0f, 0x85, 0x18, 0x01, 0x00, 0x00, //0x00002be2 jne          LBB0_701
	0xbf, 0xe0, 0xe0, 0xe0, 0xe0, //0x00002be8 movl         $-522133280, %edi
	0x29, 0xdf, //0x00002bed subl         %ebx, %edi
	0x81, 0xc3, 0x39, 0x39, 0x39, 0x39, //0x00002bef addl         $960051513, %ebx
	0x21, 0xfa, //0x00002bf5 andl         %edi, %edx
	0x85, 0xda, //0x00002bf7 testl        %ebx, %edx
	0x0f, 0x85, 0x01, 0x01, 0x00, 0x00, //0x00002bf9 jne          LBB0_701
	0x0f, 0xc9, //0x00002bff bswapl       %ecx
	0x89, 0xc8, //0x00002c01 movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x00002c03 shrl         $4, %eax
	0xf7, 0xd0, //0x00002c06 notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x00002c08 andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x00002c0d leal         (%rax,%rax,8), %eax
	0x81, 0xe1, 0x0f, 0x0f, 0x0f, 0x0f, //0x00002c10 andl         $252645135, %ecx
	0x01, 0xc1, //0x00002c16 addl         %eax, %ecx
	0x89, 0xc8, //0x00002c18 movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x00002c1a shrl         $4, %eax
	0x09, 0xc8, //0x00002c1d orl          %ecx, %eax
	0x89, 0xc1, //0x00002c1f movl         %eax, %ecx
	0xc1, 0xe9, 0x08, //0x00002c21 shrl         $8, %ecx
	0x81, 0xe1, 0x00, 0xff, 0x00, 0x00, //0x00002c24 andl         $65280, %ecx
	0x89, 0xc2, //0x00002c2a movl         %eax, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x00002c2c andl         $128, %edx
	0x48, 0x83, 0xc6, 0x06, //0x00002c32 addq         $6, %rsi
	0x09, 0xca, //0x00002c36 orl          %ecx, %edx
	0x0f, 0x85, 0xc5, 0x00, 0x00, 0x00, //0x00002c38 jne          LBB0_702
	0x3c, 0x0d, //0x00002c3e cmpb         $13, %al
	0x0f, 0x85, 0x15, 0x00, 0x00, 0x00, //0x00002c40 jne          LBB0_688
	0xe9, 0xf8, 0xfe, 0xff, 0xff, //0x00002c46 jmp          LBB0_674
	0x90, 0x90, 0x90, 0x90, 0x90, //0x00002c4b .p2align 4, 0x90
	//0x00002c50 LBB0_687
	0x48, 0x89, 0xce, //0x00002c50 movq         %rcx, %rsi
	0x3c, 0x0d, //0x00002c53 cmpb         $13, %al
	0x0f, 0x84, 0xe8, 0xfe, 0xff, 0xff, //0x00002c55 je           LBB0_674
	//0x00002c5b LBB0_688
	0x41, 0x89, 0xc0, //0x00002c5b movl         %eax, %r8d
	0x3c, 0x0a, //0x00002c5e cmpb         $10, %al
	0x0f, 0x84, 0xdd, 0xfe, 0xff, 0xff, //0x00002c60 je           LBB0_674
	0xe9, 0x98, 0x00, 0x00, 0x00, //0x00002c66 jmp          LBB0_702
	0x90, 0x90, 0x90, 0x90, 0x90, //0x00002c6b .p2align 4, 0x90
	//0x00002c70 LBB0_691
	0x31, 0xdb, //0x00002c70 xorl         %ebx, %ebx
	0x45, 0x31, 0xff, //0x00002c72 xorl         %r15d, %r15d
	//0x00002c75 LBB0_692
	0x85, 0xdb, //0x00002c75 testl        %ebx, %ebx
	0x0f, 0x85, 0xb3, 0x00, 0x00, 0x00, //0x00002c77 jne          LBB0_704
	0xe9, 0x21, 0x01, 0x00, 0x00, //0x00002c7d jmp          LBB0_714
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002c82 .p2align 4, 0x90
	//0x00002c90 LBB0_693
	0x48, 0x83, 0xc6, 0x01, //0x00002c90 addq         $1, %rsi
	0xbb, 0x02, 0x00, 0x00, 0x00, //0x00002c94 movl         $2, %ebx
	0x4c, 0x39, 0xce, //0x00002c99 cmpq         %r9, %rsi
	0x0f, 0x83, 0xd3, 0xff, 0xff, 0xff, //0x00002c9c jae          LBB0_692
	//0x00002ca2 LBB0_694
	0x44, 0x0f, 0xb6, 0x06, //0x00002ca2 movzbl       (%rsi), %r8d
	0x49, 0x83, 0xf8, 0x0d, //0x00002ca6 cmpq         $13, %r8
	0x0f, 0x84, 0xe0, 0xff, 0xff, 0xff, //0x00002caa je           LBB0_693
	0x41, 0x80, 0xf8, 0x0a, //0x00002cb0 cmpb         $10, %r8b
	0x0f, 0x84, 0xd6, 0xff, 0xff, 0xff, //0x00002cb4 je           LBB0_693
	0x48, 0x8b, 0x45, 0xd0, //0x00002cba movq         $-48(%rbp), %rax
	0x42, 0x0f, 0xb6, 0x04, 0x00, //0x00002cbe movzbl       (%rax,%r8), %eax
	0x48, 0x83, 0xc6, 0x01, //0x00002cc3 addq         $1, %rsi
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00002cc7 cmpl         $255, %eax
	0x0f, 0x84, 0x29, 0x09, 0x00, 0x00, //0x00002ccc je           LBB0_833
	0x41, 0xc1, 0xe7, 0x06, //0x00002cd2 shll         $6, %r15d
	0x41, 0x09, 0xc7, //0x00002cd6 orl          %eax, %r15d
	0xbb, 0x03, 0x00, 0x00, 0x00, //0x00002cd9 movl         $3, %ebx
	0x4c, 0x39, 0xce, //0x00002cde cmpq         %r9, %rsi
	0x0f, 0x82, 0x89, 0x04, 0x00, 0x00, //0x00002ce1 jb           LBB0_774
	0xe9, 0x44, 0x00, 0x00, 0x00, //0x00002ce7 jmp          LBB0_704
	//0x00002cec LBB0_698
	0x48, 0x89, 0xce, //0x00002cec movq         %rcx, %rsi
	0xe9, 0x0f, 0x00, 0x00, 0x00, //0x00002cef jmp          LBB0_702
	//0x00002cf4 LBB0_699
	0x80, 0xf9, 0x2f, //0x00002cf4 cmpb         $47, %cl
	0x0f, 0x85, 0x03, 0x00, 0x00, 0x00, //0x00002cf7 jne          LBB0_701
	0x41, 0xb0, 0x2f, //0x00002cfd movb         $47, %r8b
	//0x00002d00 LBB0_701
	0x48, 0x89, 0xc6, //0x00002d00 movq         %rax, %rsi
	//0x00002d03 LBB0_702
	0x41, 0x0f, 0xb6, 0xc0, //0x00002d03 movzbl       %r8b, %eax
	0x48, 0x8b, 0x4d, 0xd0, //0x00002d07 movq         $-48(%rbp), %rcx
	0x44, 0x0f, 0xb6, 0x3c, 0x01, //0x00002d0b movzbl       (%rcx,%rax), %r15d
	0x41, 0x81, 0xff, 0xff, 0x00, 0x00, 0x00, //0x00002d10 cmpl         $255, %r15d
	0x0f, 0x84, 0x9c, 0x00, 0x00, 0x00, //0x00002d17 je           LBB0_715
	0xbb, 0x01, 0x00, 0x00, 0x00, //0x00002d1d movl         $1, %ebx
	0x4c, 0x39, 0xce, //0x00002d22 cmpq         %r9, %rsi
	0x0f, 0x82, 0x58, 0x01, 0x00, 0x00, //0x00002d25 jb           LBB0_731
	0x90, 0x90, 0x90, 0x90, 0x90, //0x00002d2b .p2align 4, 0x90
	//0x00002d30 LBB0_704
	0x41, 0xf6, 0xc5, 0x02, //0x00002d30 testb        $2, %r13b
	0x0f, 0x94, 0xc0, //0x00002d34 sete         %al
	0x83, 0xfb, 0x01, //0x00002d37 cmpl         $1, %ebx
	0x0f, 0x94, 0xc1, //0x00002d3a sete         %cl
	0x4c, 0x39, 0xce, //0x00002d3d cmpq         %r9, %rsi
	0x0f, 0x82, 0x11, 0x00, 0x00, 0x00, //0x00002d40 jb           LBB0_707
	0x83, 0xfb, 0x04, //0x00002d46 cmpl         $4, %ebx
	0x0f, 0x84, 0x08, 0x00, 0x00, 0x00, //0x00002d49 je           LBB0_707
	0x08, 0xc8, //0x00002d4f orb          %cl, %al
	0x0f, 0x85, 0xe9, 0xfc, 0xff, 0xff, //0x00002d51 jne          LBB0_660
	//0x00002d57 LBB0_707
	0xb0, 0x04, //0x00002d57 movb         $4, %al
	0x28, 0xd8, //0x00002d59 subb         %bl, %al
	0x0f, 0xb6, 0xc0, //0x00002d5b movzbl       %al, %eax
	0x01, 0xc0, //0x00002d5e addl         %eax, %eax
	0x8d, 0x0c, 0x40, //0x00002d60 leal         (%rax,%rax,2), %ecx
	0x44, 0x89, 0xfa, //0x00002d63 movl         %r15d, %edx
	0xd3, 0xe2, //0x00002d66 shll         %cl, %edx
	0x83, 0xfb, 0x02, //0x00002d68 cmpl         $2, %ebx
	0x0f, 0x84, 0x1e, 0x00, 0x00, 0x00, //0x00002d6b je           LBB0_712
	0x83, 0xfb, 0x03, //0x00002d71 cmpl         $3, %ebx
	0x0f, 0x84, 0x0f, 0x00, 0x00, 0x00, //0x00002d74 je           LBB0_711
	0x83, 0xfb, 0x04, //0x00002d7a cmpl         $4, %ebx
	0x4c, 0x89, 0xe1, //0x00002d7d movq         %r12, %rcx
	0x0f, 0x85, 0x13, 0x00, 0x00, 0x00, //0x00002d80 jne          LBB0_713
	0x88, 0x51, 0x02, //0x00002d86 movb         %dl, $2(%rcx)
	//0x00002d89 LBB0_711
	0x4c, 0x89, 0xe0, //0x00002d89 movq         %r12, %rax
	0x88, 0x70, 0x01, //0x00002d8c movb         %dh, $1(%rax)
	//0x00002d8f LBB0_712
	0xc1, 0xea, 0x10, //0x00002d8f shrl         $16, %edx
	0x4c, 0x89, 0xe1, //0x00002d92 movq         %r12, %rcx
	0x41, 0x88, 0x14, 0x24, //0x00002d95 movb         %dl, (%r12)
	//0x00002d99 LBB0_713
	0x89, 0xd8, //0x00002d99 movl         %ebx, %eax
	0x4c, 0x8d, 0x24, 0x08, //0x00002d9b leaq         (%rax,%rcx), %r12
	0x49, 0x83, 0xc4, 0xff, //0x00002d9f addq         $-1, %r12
	//0x00002da3 LBB0_714
	0x49, 0x89, 0xf3, //0x00002da3 movq         %rsi, %r11
	0x31, 0xc9, //0x00002da6 xorl         %ecx, %ecx
	0x48, 0x85, 0xc9, //0x00002da8 testq        %rcx, %rcx
	0x4c, 0x89, 0xe2, //0x00002dab movq         %r12, %rdx
	0x0f, 0x84, 0xa6, 0xfc, 0xff, 0xff, //0x00002dae je           LBB0_661
	0xe9, 0xa7, 0x08, 0x00, 0x00, //0x00002db4 jmp          LBB0_863
	//0x00002db9 LBB0_715
	0x31, 0xdb, //0x00002db9 xorl         %ebx, %ebx
	0x45, 0x31, 0xff, //0x00002dbb xorl         %r15d, %r15d
	//0x00002dbe LBB0_716
	0x41, 0xf6, 0xc5, 0x02, //0x00002dbe testb        $2, %r13b
	0x0f, 0x85, 0x78, 0xfc, 0xff, 0xff, //0x00002dc2 jne          LBB0_660
	0x41, 0x80, 0xf8, 0x3d, //0x00002dc8 cmpb         $61, %r8b
	0x0f, 0x85, 0x6e, 0xfc, 0xff, 0xff, //0x00002dcc jne          LBB0_660
	0x83, 0xfb, 0x02, //0x00002dd2 cmpl         $2, %ebx
	0x0f, 0x82, 0x65, 0xfc, 0xff, 0xff, //0x00002dd5 jb           LBB0_660
	0x41, 0xbe, 0x05, 0x00, 0x00, 0x00, //0x00002ddb movl         $5, %r14d
	0x41, 0x29, 0xde, //0x00002de1 subl         %ebx, %r14d
	0x41, 0xf6, 0xc5, 0x08, //0x00002de4 testb        $8, %r13b
	0x0f, 0x85, 0xc7, 0x01, 0x00, 0x00, //0x00002de8 jne          LBB0_749
	0x4c, 0x39, 0xce, //0x00002dee cmpq         %r9, %rsi
	0x0f, 0x83, 0x60, 0xff, 0xff, 0xff, //0x00002df1 jae          LBB0_707
	0x48, 0x8d, 0x4e, 0x01, //0x00002df7 leaq         $1(%rsi), %rcx
	0x48, 0x8b, 0x45, 0xc0, //0x00002dfb movq         $-64(%rbp), %rax
	0x48, 0x29, 0xf0, //0x00002dff subq         %rsi, %rax
	0x48, 0x83, 0xc6, 0x02, //0x00002e02 addq         $2, %rsi
	0x48, 0x89, 0xf7, //0x00002e06 movq         %rsi, %rdi
	0x48, 0x89, 0xce, //0x00002e09 movq         %rcx, %rsi
	0xe9, 0x21, 0x00, 0x00, 0x00, //0x00002e0c jmp          LBB0_723
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002e11 .p2align 4, 0x90
	//0x00002e20 LBB0_722
	0x48, 0x83, 0xc6, 0x01, //0x00002e20 addq         $1, %rsi
	0x48, 0x83, 0xc7, 0x01, //0x00002e24 addq         $1, %rdi
	0x48, 0x83, 0xc0, 0xff, //0x00002e28 addq         $-1, %rax
	0x0f, 0x83, 0x03, 0x05, 0x00, 0x00, //0x00002e2c jae          LBB0_802
	//0x00002e32 LBB0_723
	0x0f, 0xb6, 0x4e, 0xff, //0x00002e32 movzbl       $-1(%rsi), %ecx
	0x80, 0xf9, 0x0a, //0x00002e36 cmpb         $10, %cl
	0x0f, 0x84, 0xe1, 0xff, 0xff, 0xff, //0x00002e39 je           LBB0_722
	0x80, 0xf9, 0x0d, //0x00002e3f cmpb         $13, %cl
	0x0f, 0x84, 0xd8, 0xff, 0xff, 0xff, //0x00002e42 je           LBB0_722
	0x80, 0xf9, 0x3d, //0x00002e48 cmpb         $61, %cl
	0x0f, 0x85, 0xef, 0xfb, 0xff, 0xff, //0x00002e4b jne          LBB0_660
	0x41, 0x83, 0xfe, 0x02, //0x00002e51 cmpl         $2, %r14d
	0x0f, 0x84, 0xe5, 0xfb, 0xff, 0xff, //0x00002e55 je           LBB0_660
	0x4c, 0x39, 0xce, //0x00002e5b cmpq         %r9, %rsi
	0x0f, 0x83, 0xf3, 0xfe, 0xff, 0xff, //0x00002e5e jae          LBB0_707
	0x48, 0x01, 0xc6, //0x00002e64 addq         %rax, %rsi
	0x31, 0xc9, //0x00002e67 xorl         %ecx, %ecx
	0xe9, 0xca, 0x02, 0x00, 0x00, //0x00002e69 jmp          LBB0_769
	//0x00002e6e LBB0_745
	0x80, 0xf9, 0x6e, //0x00002e6e cmpb         $110, %cl
	0x0f, 0x85, 0x33, 0x03, 0x00, 0x00, //0x00002e71 jne          LBB0_778
	//0x00002e77 LBB0_729
	0x48, 0x89, 0xc6, //0x00002e77 movq         %rax, %rsi
	//0x00002e7a LBB0_730
	0x4c, 0x39, 0xce, //0x00002e7a cmpq         %r9, %rsi
	0x0f, 0x83, 0x28, 0x01, 0x00, 0x00, //0x00002e7d jae          LBB0_748
	//0x00002e83 LBB0_731
	0x48, 0x8d, 0x4e, 0x01, //0x00002e83 leaq         $1(%rsi), %rcx
	0x0f, 0xb6, 0x06, //0x00002e87 movzbl       (%rsi), %eax
	0x3c, 0x5c, //0x00002e8a cmpb         $92, %al
	0x0f, 0x85, 0xfe, 0x00, 0x00, 0x00, //0x00002e8c jne          LBB0_743
	0x48, 0x8d, 0x46, 0x02, //0x00002e92 leaq         $2(%rsi), %rax
	0x41, 0xb0, 0xff, //0x00002e96 movb         $-1, %r8b
	0x4c, 0x39, 0xc8, //0x00002e99 cmpq         %r9, %rax
	0x0f, 0x87, 0xb4, 0x02, 0x00, 0x00, //0x00002e9c ja           LBB0_772
	0x0f, 0xb6, 0x09, //0x00002ea2 movzbl       (%rcx), %ecx
	0x80, 0xf9, 0x71, //0x00002ea5 cmpb         $113, %cl
	0x0f, 0x8e, 0xc0, 0xff, 0xff, 0xff, //0x00002ea8 jle          LBB0_745
	0x80, 0xf9, 0x72, //0x00002eae cmpb         $114, %cl
	0x0f, 0x84, 0xc0, 0xff, 0xff, 0xff, //0x00002eb1 je           LBB0_729
	0x80, 0xf9, 0x75, //0x00002eb7 cmpb         $117, %cl
	0x0f, 0x85, 0xf6, 0x02, 0x00, 0x00, //0x00002eba jne          LBB0_780
	0x4c, 0x89, 0xc9, //0x00002ec0 movq         %r9, %rcx
	0x48, 0x29, 0xc1, //0x00002ec3 subq         %rax, %rcx
	0x48, 0x83, 0xf9, 0x04, //0x00002ec6 cmpq         $4, %rcx
	0x0f, 0x8c, 0xe6, 0x02, 0x00, 0x00, //0x00002eca jl           LBB0_780
	0x8b, 0x08, //0x00002ed0 movl         (%rax), %ecx
	0x89, 0xcb, //0x00002ed2 movl         %ecx, %ebx
	0xf7, 0xd3, //0x00002ed4 notl         %ebx
	0x8d, 0x91, 0xd0, 0xcf, 0xcf, 0xcf, //0x00002ed6 leal         $-808464432(%rcx), %edx
	0x81, 0xe3, 0x80, 0x80, 0x80, 0x80, //0x00002edc andl         $-2139062144, %ebx
	0x85, 0xd3, //0x00002ee2 testl        %edx, %ebx
	0x0f, 0x85, 0xcc, 0x02, 0x00, 0x00, //0x00002ee4 jne          LBB0_780
	0x8d, 0x91, 0x19, 0x19, 0x19, 0x19, //0x00002eea leal         $421075225(%rcx), %edx
	0x09, 0xca, //0x00002ef0 orl          %ecx, %edx
	0xf7, 0xc2, 0x80, 0x80, 0x80, 0x80, //0x00002ef2 testl        $-2139062144, %edx
	0x0f, 0x85, 0xb8, 0x02, 0x00, 0x00, //0x00002ef8 jne          LBB0_780
	0x89, 0xca, //0x00002efe movl         %ecx, %edx
	0x81, 0xe2, 0x7f, 0x7f, 0x7f, 0x7f, //0x00002f00 andl         $2139062143, %edx
	0xbf, 0xc0, 0xc0, 0xc0, 0xc0, //0x00002f06 movl         $-1061109568, %edi
	0x29, 0xd7, //0x00002f0b subl         %edx, %edi
	0x44, 0x8d, 0x92, 0x46, 0x46, 0x46, 0x46, //0x00002f0d leal         $1179010630(%rdx), %r10d
	0x21, 0xdf, //0x00002f14 andl         %ebx, %edi
	0x44, 0x85, 0xd7, //0x00002f16 testl        %r10d, %edi
	0x0f, 0x85, 0x97, 0x02, 0x00, 0x00, //0x00002f19 jne          LBB0_780
	0xbf, 0xe0, 0xe0, 0xe0, 0xe0, //0x00002f1f movl         $-522133280, %edi
	0x29, 0xd7, //0x00002f24 subl         %edx, %edi
	0x81, 0xc2, 0x39, 0x39, 0x39, 0x39, //0x00002f26 addl         $960051513, %edx
	0x21, 0xfb, //0x00002f2c andl         %edi, %ebx
	0x85, 0xd3, //0x00002f2e testl        %edx, %ebx
	0x0f, 0x85, 0x80, 0x02, 0x00, 0x00, //0x00002f30 jne          LBB0_780
	0x0f, 0xc9, //0x00002f36 bswapl       %ecx
	0x89, 0xc8, //0x00002f38 movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x00002f3a shrl         $4, %eax
	0xf7, 0xd0, //0x00002f3d notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x00002f3f andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x00002f44 leal         (%rax,%rax,8), %eax
	0x81, 0xe1, 0x0f, 0x0f, 0x0f, 0x0f, //0x00002f47 andl         $252645135, %ecx
	0x01, 0xc1, //0x00002f4d addl         %eax, %ecx
	0x89, 0xc8, //0x00002f4f movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x00002f51 shrl         $4, %eax
	0x09, 0xc8, //0x00002f54 orl          %ecx, %eax
	0x89, 0xc1, //0x00002f56 movl         %eax, %ecx
	0xc1, 0xe9, 0x08, //0x00002f58 shrl         $8, %ecx
	0x81, 0xe1, 0x00, 0xff, 0x00, 0x00, //0x00002f5b andl         $65280, %ecx
	0x89, 0xc2, //0x00002f61 movl         %eax, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x00002f63 andl         $128, %edx
	0x48, 0x83, 0xc6, 0x06, //0x00002f69 addq         $6, %rsi
	0x09, 0xca, //0x00002f6d orl          %ecx, %edx
	0x0f, 0x85, 0x44, 0x02, 0x00, 0x00, //0x00002f6f jne          LBB0_781
	0x3c, 0x0d, //0x00002f75 cmpb         $13, %al
	0x0f, 0x85, 0x1e, 0x00, 0x00, 0x00, //0x00002f77 jne          LBB0_744
	0xe9, 0xf8, 0xfe, 0xff, 0xff, //0x00002f7d jmp          LBB0_730
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002f82 .p2align 4, 0x90
	//0x00002f90 LBB0_743
	0x48, 0x89, 0xce, //0x00002f90 movq         %rcx, %rsi
	0x3c, 0x0d, //0x00002f93 cmpb         $13, %al
	0x0f, 0x84, 0xdf, 0xfe, 0xff, 0xff, //0x00002f95 je           LBB0_730
	//0x00002f9b LBB0_744
	0x41, 0x89, 0xc0, //0x00002f9b movl         %eax, %r8d
	0x3c, 0x0a, //0x00002f9e cmpb         $10, %al
	0x0f, 0x84, 0xd4, 0xfe, 0xff, 0xff, //0x00002fa0 je           LBB0_730
	0xe9, 0x0e, 0x02, 0x00, 0x00, //0x00002fa6 jmp          LBB0_781
	//0x00002fab LBB0_748
	0xbb, 0x01, 0x00, 0x00, 0x00, //0x00002fab movl         $1, %ebx
	0xe9, 0xc0, 0xfc, 0xff, 0xff, //0x00002fb0 jmp          LBB0_692
	//0x00002fb5 LBB0_749
	0x4c, 0x39, 0xce, //0x00002fb5 cmpq         %r9, %rsi
	0x0f, 0x83, 0x99, 0xfd, 0xff, 0xff, //0x00002fb8 jae          LBB0_707
	0x48, 0x89, 0xf7, //0x00002fbe movq         %rsi, %rdi
	0xe9, 0x36, 0x00, 0x00, 0x00, //0x00002fc1 jmp          LBB0_753
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002fc6 .p2align 4, 0x90
	//0x00002fd0 LBB0_767
	0x48, 0x89, 0xc7, //0x00002fd0 movq         %rax, %rdi
	0x4c, 0x39, 0xcf, //0x00002fd3 cmpq         %r9, %rdi
	0x0f, 0x82, 0x20, 0x00, 0x00, 0x00, //0x00002fd6 jb           LBB0_753
	0xe9, 0x5c, 0x03, 0x00, 0x00, //0x00002fdc jmp          LBB0_804
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002fe1 .p2align 4, 0x90
	//0x00002ff0 LBB0_751
	0x48, 0x89, 0xf7, //0x00002ff0 movq         %rsi, %rdi
	0x4c, 0x39, 0xcf, //0x00002ff3 cmpq         %r9, %rdi
	0x0f, 0x83, 0x41, 0x03, 0x00, 0x00, //0x00002ff6 jae          LBB0_804
	//0x00002ffc LBB0_753
	0x48, 0x8d, 0x47, 0x01, //0x00002ffc leaq         $1(%rdi), %rax
	0x0f, 0xb6, 0x0f, //0x00003000 movzbl       (%rdi), %ecx
	0x80, 0xf9, 0x5c, //0x00003003 cmpb         $92, %cl
	0x0f, 0x85, 0xe5, 0x00, 0x00, 0x00, //0x00003006 jne          LBB0_764
	0x48, 0x8d, 0x77, 0x02, //0x0000300c leaq         $2(%rdi), %rsi
	0x4c, 0x39, 0xce, //0x00003010 cmpq         %r9, %rsi
	0x0f, 0x87, 0x21, 0xfa, 0xff, 0xff, //0x00003013 ja           LBB0_659
	0x0f, 0xb6, 0x00, //0x00003019 movzbl       (%rax), %eax
	0x3c, 0x6e, //0x0000301c cmpb         $110, %al
	0x0f, 0x84, 0xcc, 0xff, 0xff, 0xff, //0x0000301e je           LBB0_751
	0x3c, 0x72, //0x00003024 cmpb         $114, %al
	0x0f, 0x84, 0xc4, 0xff, 0xff, 0xff, //0x00003026 je           LBB0_751
	0x3c, 0x75, //0x0000302c cmpb         $117, %al
	0x0f, 0x85, 0x0c, 0xfa, 0xff, 0xff, //0x0000302e jne          LBB0_660
	0x4c, 0x89, 0xc8, //0x00003034 movq         %r9, %rax
	0x48, 0x29, 0xf0, //0x00003037 subq         %rsi, %rax
	0x48, 0x83, 0xf8, 0x04, //0x0000303a cmpq         $4, %rax
	0x0f, 0x8c, 0xfc, 0xf9, 0xff, 0xff, //0x0000303e jl           LBB0_660
	0x8b, 0x16, //0x00003044 movl         (%rsi), %edx
	0x89, 0xd1, //0x00003046 movl         %edx, %ecx
	0xf7, 0xd1, //0x00003048 notl         %ecx
	0x8d, 0x82, 0xd0, 0xcf, 0xcf, 0xcf, //0x0000304a leal         $-808464432(%rdx), %eax
	0x81, 0xe1, 0x80, 0x80, 0x80, 0x80, //0x00003050 andl         $-2139062144, %ecx
	0x85, 0xc1, //0x00003056 testl        %eax, %ecx
	0x0f, 0x85, 0xe2, 0xf9, 0xff, 0xff, //0x00003058 jne          LBB0_660
	0x8d, 0x82, 0x19, 0x19, 0x19, 0x19, //0x0000305e leal         $421075225(%rdx), %eax
	0x09, 0xd0, //0x00003064 orl          %edx, %eax
	0xa9, 0x80, 0x80, 0x80, 0x80, //0x00003066 testl        $-2139062144, %eax
	0x0f, 0x85, 0xcf, 0xf9, 0xff, 0xff, //0x0000306b jne          LBB0_660
	0x89, 0xd0, //0x00003071 movl         %edx, %eax
	0x25, 0x7f, 0x7f, 0x7f, 0x7f, //0x00003073 andl         $2139062143, %eax
	0x41, 0xb8, 0xc0, 0xc0, 0xc0, 0xc0, //0x00003078 movl         $-1061109568, %r8d
	0x41, 0x29, 0xc0, //0x0000307e subl         %eax, %r8d
	0x44, 0x8d, 0x90, 0x46, 0x46, 0x46, 0x46, //0x00003081 leal         $1179010630(%rax), %r10d
	0x41, 0x21, 0xc8, //0x00003088 andl         %ecx, %r8d
	0x45, 0x85, 0xd0, //0x0000308b testl        %r10d, %r8d
	0x0f, 0x85, 0xac, 0xf9, 0xff, 0xff, //0x0000308e jne          LBB0_660
	0x41, 0xb8, 0xe0, 0xe0, 0xe0, 0xe0, //0x00003094 movl         $-522133280, %r8d
	0x41, 0x29, 0xc0, //0x0000309a subl         %eax, %r8d
	0x05, 0x39, 0x39, 0x39, 0x39, //0x0000309d addl         $960051513, %eax
	0x44, 0x21, 0xc1, //0x000030a2 andl         %r8d, %ecx
	0x85, 0xc1, //0x000030a5 testl        %eax, %ecx
	0x0f, 0x85, 0x93, 0xf9, 0xff, 0xff, //0x000030a7 jne          LBB0_660
	0x0f, 0xca, //0x000030ad bswapl       %edx
	0x89, 0xd0, //0x000030af movl         %edx, %eax
	0xc1, 0xe8, 0x04, //0x000030b1 shrl         $4, %eax
	0xf7, 0xd0, //0x000030b4 notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x000030b6 andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x000030bb leal         (%rax,%rax,8), %eax
	0x81, 0xe2, 0x0f, 0x0f, 0x0f, 0x0f, //0x000030be andl         $252645135, %edx
	0x01, 0xc2, //0x000030c4 addl         %eax, %edx
	0x89, 0xd1, //0x000030c6 movl         %edx, %ecx
	0xc1, 0xe9, 0x04, //0x000030c8 shrl         $4, %ecx
	0x09, 0xd1, //0x000030cb orl          %edx, %ecx
	0x89, 0xc8, //0x000030cd movl         %ecx, %eax
	0xc1, 0xe8, 0x08, //0x000030cf shrl         $8, %eax
	0x25, 0x00, 0xff, 0x00, 0x00, //0x000030d2 andl         $65280, %eax
	0x89, 0xca, //0x000030d7 movl         %ecx, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x000030d9 andl         $128, %edx
	0x48, 0x83, 0xc7, 0x06, //0x000030df addq         $6, %rdi
	0x09, 0xc2, //0x000030e3 orl          %eax, %edx
	0x48, 0x89, 0xf8, //0x000030e5 movq         %rdi, %rax
	0x48, 0x89, 0xfe, //0x000030e8 movq         %rdi, %rsi
	0x0f, 0x85, 0x4f, 0xf9, 0xff, 0xff, //0x000030eb jne          LBB0_660
	//0x000030f1 LBB0_764
	0x80, 0xf9, 0x0a, //0x000030f1 cmpb         $10, %cl
	0x0f, 0x84, 0xd6, 0xfe, 0xff, 0xff, //0x000030f4 je           LBB0_767
	0x80, 0xf9, 0x0d, //0x000030fa cmpb         $13, %cl
	0x0f, 0x84, 0xcd, 0xfe, 0xff, 0xff, //0x000030fd je           LBB0_767
	0x80, 0xf9, 0x3d, //0x00003103 cmpb         $61, %cl
	0x0f, 0x85, 0x2e, 0xf9, 0xff, 0xff, //0x00003106 jne          LBB0_659
	0x41, 0x83, 0xfe, 0x02, //0x0000310c cmpl         $2, %r14d
	0x0f, 0x84, 0x24, 0xf9, 0xff, 0xff, //0x00003110 je           LBB0_659
	0x4c, 0x39, 0xc8, //0x00003116 cmpq         %r9, %rax
	0x0f, 0x82, 0x3a, 0x02, 0x00, 0x00, //0x00003119 jb           LBB0_836
	//0x0000311f LBB0_809
	0x48, 0x89, 0xc6, //0x0000311f movq         %rax, %rsi
	0xe9, 0x30, 0xfc, 0xff, 0xff, //0x00003122 jmp          LBB0_707
	//0x00003127 LBB0_768
	0x48, 0x83, 0xc7, 0x01, //0x00003127 addq         $1, %rdi
	0x48, 0x83, 0xc1, 0x01, //0x0000312b addq         $1, %rcx
	0x48, 0x39, 0xc8, //0x0000312f cmpq         %rcx, %rax
	0x0f, 0x84, 0x1f, 0xfc, 0xff, 0xff, //0x00003132 je           LBB0_707
	//0x00003138 LBB0_769
	0x0f, 0xb6, 0x57, 0xff, //0x00003138 movzbl       $-1(%rdi), %edx
	0x80, 0xfa, 0x0d, //0x0000313c cmpb         $13, %dl
	0x0f, 0x84, 0xe2, 0xff, 0xff, 0xff, //0x0000313f je           LBB0_768
	0x80, 0xfa, 0x0a, //0x00003145 cmpb         $10, %dl
	0x0f, 0x84, 0xd9, 0xff, 0xff, 0xff, //0x00003148 je           LBB0_768
	0x48, 0x89, 0xfe, //0x0000314e movq         %rdi, %rsi
	0xe9, 0xea, 0xf8, 0xff, 0xff, //0x00003151 jmp          LBB0_660
	//0x00003156 LBB0_772
	0x48, 0x89, 0xce, //0x00003156 movq         %rcx, %rsi
	0xe9, 0x5b, 0x00, 0x00, 0x00, //0x00003159 jmp          LBB0_781
	//0x0000315e LBB0_773
	0x48, 0x83, 0xc6, 0x01, //0x0000315e addq         $1, %rsi
	0xbb, 0x03, 0x00, 0x00, 0x00, //0x00003162 movl         $3, %ebx
	0x4c, 0x39, 0xce, //0x00003167 cmpq         %r9, %rsi
	0x0f, 0x83, 0x05, 0xfb, 0xff, 0xff, //0x0000316a jae          LBB0_692
	//0x00003170 LBB0_774
	0x44, 0x0f, 0xb6, 0x06, //0x00003170 movzbl       (%rsi), %r8d
	0x49, 0x83, 0xf8, 0x0d, //0x00003174 cmpq         $13, %r8
	0x0f, 0x84, 0xe0, 0xff, 0xff, 0xff, //0x00003178 je           LBB0_773
	0x41, 0x80, 0xf8, 0x0a, //0x0000317e cmpb         $10, %r8b
	0x0f, 0x84, 0xd6, 0xff, 0xff, 0xff, //0x00003182 je           LBB0_773
	0x48, 0x8b, 0x45, 0xd0, //0x00003188 movq         $-48(%rbp), %rax
	0x42, 0x0f, 0xb6, 0x04, 0x00, //0x0000318c movzbl       (%rax,%r8), %eax
	0x48, 0x83, 0xc6, 0x01, //0x00003191 addq         $1, %rsi
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00003195 cmpl         $255, %eax
	0x0f, 0x85, 0x93, 0x04, 0x00, 0x00, //0x0000319a jne          LBB0_858
	//0x000031a0 LBB0_859
	0xbb, 0x03, 0x00, 0x00, 0x00, //0x000031a0 movl         $3, %ebx
	0xe9, 0x14, 0xfc, 0xff, 0xff, //0x000031a5 jmp          LBB0_716
	//0x000031aa LBB0_778
	0x80, 0xf9, 0x2f, //0x000031aa cmpb         $47, %cl
	0x0f, 0x85, 0x03, 0x00, 0x00, 0x00, //0x000031ad jne          LBB0_780
	0x41, 0xb0, 0x2f, //0x000031b3 movb         $47, %r8b
	//0x000031b6 LBB0_780
	0x48, 0x89, 0xc6, //0x000031b6 movq         %rax, %rsi
	//0x000031b9 LBB0_781
	0x41, 0x0f, 0xb6, 0xc0, //0x000031b9 movzbl       %r8b, %eax
	0x48, 0x8b, 0x4d, 0xd0, //0x000031bd movq         $-48(%rbp), %rcx
	0x0f, 0xb6, 0x04, 0x01, //0x000031c1 movzbl       (%rcx,%rax), %eax
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x000031c5 cmpl         $255, %eax
	0x0f, 0x84, 0x5b, 0x01, 0x00, 0x00, //0x000031ca je           LBB0_801
	0x41, 0xc1, 0xe7, 0x06, //0x000031d0 shll         $6, %r15d
	0x41, 0x09, 0xc7, //0x000031d4 orl          %eax, %r15d
	0xbb, 0x02, 0x00, 0x00, 0x00, //0x000031d7 movl         $2, %ebx
	0x4c, 0x39, 0xce, //0x000031dc cmpq         %r9, %rsi
	0x0f, 0x82, 0x1f, 0x00, 0x00, 0x00, //0x000031df jb           LBB0_785
	0xe9, 0x46, 0xfb, 0xff, 0xff, //0x000031e5 jmp          LBB0_704
	//0x000031ea LBB0_799
	0x80, 0xf9, 0x6e, //0x000031ea cmpb         $110, %cl
	0x0f, 0x85, 0x85, 0x02, 0x00, 0x00, //0x000031ed jne          LBB0_810
	//0x000031f3 LBB0_783
	0x48, 0x89, 0xc6, //0x000031f3 movq         %rax, %rsi
	//0x000031f6 LBB0_784
	0xbb, 0x02, 0x00, 0x00, 0x00, //0x000031f6 movl         $2, %ebx
	0x4c, 0x39, 0xce, //0x000031fb cmpq         %r9, %rsi
	0x0f, 0x83, 0x71, 0xfa, 0xff, 0xff, //0x000031fe jae          LBB0_692
	//0x00003204 LBB0_785
	0x48, 0x8d, 0x4e, 0x01, //0x00003204 leaq         $1(%rsi), %rcx
	0x0f, 0xb6, 0x06, //0x00003208 movzbl       (%rsi), %eax
	0x3c, 0x5c, //0x0000320b cmpb         $92, %al
	0x0f, 0x85, 0xfd, 0x00, 0x00, 0x00, //0x0000320d jne          LBB0_797
	0x48, 0x8d, 0x46, 0x02, //0x00003213 leaq         $2(%rsi), %rax
	0x41, 0xb0, 0xff, //0x00003217 movb         $-1, %r8b
	0x4c, 0x39, 0xc8, //0x0000321a cmpq         %r9, %rax
	0x0f, 0x87, 0x22, 0x01, 0x00, 0x00, //0x0000321d ja           LBB0_805
	0x0f, 0xb6, 0x09, //0x00003223 movzbl       (%rcx), %ecx
	0x80, 0xf9, 0x71, //0x00003226 cmpb         $113, %cl
	0x0f, 0x8e, 0xbb, 0xff, 0xff, 0xff, //0x00003229 jle          LBB0_799
	0x80, 0xf9, 0x72, //0x0000322f cmpb         $114, %cl
	0x0f, 0x84, 0xbb, 0xff, 0xff, 0xff, //0x00003232 je           LBB0_783
	0x80, 0xf9, 0x75, //0x00003238 cmpb         $117, %cl
	0x0f, 0x85, 0x43, 0x02, 0x00, 0x00, //0x0000323b jne          LBB0_812
	0x4c, 0x89, 0xc9, //0x00003241 movq         %r9, %rcx
	0x48, 0x29, 0xc1, //0x00003244 subq         %rax, %rcx
	0x48, 0x83, 0xf9, 0x04, //0x00003247 cmpq         $4, %rcx
	0x0f, 0x8c, 0x33, 0x02, 0x00, 0x00, //0x0000324b jl           LBB0_812
	0x8b, 0x08, //0x00003251 movl         (%rax), %ecx
	0x89, 0xcb, //0x00003253 movl         %ecx, %ebx
	0xf7, 0xd3, //0x00003255 notl         %ebx
	0x8d, 0x91, 0xd0, 0xcf, 0xcf, 0xcf, //0x00003257 leal         $-808464432(%rcx), %edx
	0x81, 0xe3, 0x80, 0x80, 0x80, 0x80, //0x0000325d andl         $-2139062144, %ebx
	0x85, 0xd3, //0x00003263 testl        %edx, %ebx
	0x0f, 0x85, 0x19, 0x02, 0x00, 0x00, //0x00003265 jne          LBB0_812
	0x8d, 0x91, 0x19, 0x19, 0x19, 0x19, //0x0000326b leal         $421075225(%rcx), %edx
	0x09, 0xca, //0x00003271 orl          %ecx, %edx
	0xf7, 0xc2, 0x80, 0x80, 0x80, 0x80, //0x00003273 testl        $-2139062144, %edx
	0x0f, 0x85, 0x05, 0x02, 0x00, 0x00, //0x00003279 jne          LBB0_812
	0x89, 0xca, //0x0000327f movl         %ecx, %edx
	0x81, 0xe2, 0x7f, 0x7f, 0x7f, 0x7f, //0x00003281 andl         $2139062143, %edx
	0xbf, 0xc0, 0xc0, 0xc0, 0xc0, //0x00003287 movl         $-1061109568, %edi
	0x29, 0xd7, //0x0000328c subl         %edx, %edi
	0x44, 0x8d, 0x92, 0x46, 0x46, 0x46, 0x46, //0x0000328e leal         $1179010630(%rdx), %r10d
	0x21, 0xdf, //0x00003295 andl         %ebx, %edi
	0x44, 0x85, 0xd7, //0x00003297 testl        %r10d, %edi
	0x0f, 0x85, 0xe4, 0x01, 0x00, 0x00, //0x0000329a jne          LBB0_812
	0xbf, 0xe0, 0xe0, 0xe0, 0xe0, //0x000032a0 movl         $-522133280, %edi
	0x29, 0xd7, //0x000032a5 subl         %edx, %edi
	0x81, 0xc2, 0x39, 0x39, 0x39, 0x39, //0x000032a7 addl         $960051513, %edx
	0x21, 0xfb, //0x000032ad andl         %edi, %ebx
	0x85, 0xd3, //0x000032af testl        %edx, %ebx
	0x0f, 0x85, 0xcd, 0x01, 0x00, 0x00, //0x000032b1 jne          LBB0_812
	0x0f, 0xc9, //0x000032b7 bswapl       %ecx
	0x89, 0xc8, //0x000032b9 movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x000032bb shrl         $4, %eax
	0xf7, 0xd0, //0x000032be notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x000032c0 andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x000032c5 leal         (%rax,%rax,8), %eax
	0x81, 0xe1, 0x0f, 0x0f, 0x0f, 0x0f, //0x000032c8 andl         $252645135, %ecx
	0x01, 0xc1, //0x000032ce addl         %eax, %ecx
	0x89, 0xc8, //0x000032d0 movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x000032d2 shrl         $4, %eax
	0x09, 0xc8, //0x000032d5 orl          %ecx, %eax
	0x89, 0xc1, //0x000032d7 movl         %eax, %ecx
	0xc1, 0xe9, 0x08, //0x000032d9 shrl         $8, %ecx
	0x81, 0xe1, 0x00, 0xff, 0x00, 0x00, //0x000032dc andl         $65280, %ecx
	0x89, 0xc2, //0x000032e2 movl         %eax, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x000032e4 andl         $128, %edx
	0x48, 0x83, 0xc6, 0x06, //0x000032ea addq         $6, %rsi
	0x09, 0xca, //0x000032ee orl          %ecx, %edx
	0x0f, 0x85, 0x91, 0x01, 0x00, 0x00, //0x000032f0 jne          LBB0_813
	0x3c, 0x0d, //0x000032f6 cmpb         $13, %al
	0x0f, 0x85, 0x1d, 0x00, 0x00, 0x00, //0x000032f8 jne          LBB0_798
	0xe9, 0xf3, 0xfe, 0xff, 0xff, //0x000032fe jmp          LBB0_784
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00003303 .p2align 4, 0x90
	//0x00003310 LBB0_797
	0x48, 0x89, 0xce, //0x00003310 movq         %rcx, %rsi
	0x3c, 0x0d, //0x00003313 cmpb         $13, %al
	0x0f, 0x84, 0xdb, 0xfe, 0xff, 0xff, //0x00003315 je           LBB0_784
	//0x0000331b LBB0_798
	0x41, 0x89, 0xc0, //0x0000331b movl         %eax, %r8d
	0x3c, 0x0a, //0x0000331e cmpb         $10, %al
	0x0f, 0x84, 0xd0, 0xfe, 0xff, 0xff, //0x00003320 je           LBB0_784
	0xe9, 0x5c, 0x01, 0x00, 0x00, //0x00003326 jmp          LBB0_813
	//0x0000332b LBB0_801
	0xbb, 0x01, 0x00, 0x00, 0x00, //0x0000332b movl         $1, %ebx
	0xe9, 0x89, 0xfa, 0xff, 0xff, //0x00003330 jmp          LBB0_716
	//0x00003335 LBB0_802
	0x4c, 0x89, 0xce, //0x00003335 movq         %r9, %rsi
	0xe9, 0x1a, 0xfa, 0xff, 0xff, //0x00003338 jmp          LBB0_707
	//0x0000333d LBB0_804
	0x48, 0x89, 0xfe, //0x0000333d movq         %rdi, %rsi
	0xe9, 0x12, 0xfa, 0xff, 0xff, //0x00003340 jmp          LBB0_707
	//0x00003345 LBB0_805
	0x48, 0x89, 0xce, //0x00003345 movq         %rcx, %rsi
	0xe9, 0x3a, 0x01, 0x00, 0x00, //0x00003348 jmp          LBB0_813
	//0x0000334d LBB0_834
	0x48, 0x89, 0xf0, //0x0000334d movq         %rsi, %rax
	//0x00003350 LBB0_835
	0x4c, 0x39, 0xc8, //0x00003350 cmpq         %r9, %rax
	0x0f, 0x83, 0xc6, 0xfd, 0xff, 0xff, //0x00003353 jae          LBB0_809
	//0x00003359 LBB0_836
	0x48, 0x8d, 0x48, 0x01, //0x00003359 leaq         $1(%rax), %rcx
	0x0f, 0xb6, 0x38, //0x0000335d movzbl       (%rax), %edi
	0x40, 0x80, 0xff, 0x5c, //0x00003360 cmpb         $92, %dil
	0x0f, 0x85, 0xef, 0x00, 0x00, 0x00, //0x00003364 jne          LBB0_847
	0x48, 0x8d, 0x70, 0x02, //0x0000336a leaq         $2(%rax), %rsi
	0x4c, 0x39, 0xce, //0x0000336e cmpq         %r9, %rsi
	0x0f, 0x87, 0xcd, 0x02, 0x00, 0x00, //0x00003371 ja           LBB0_861
	0x0f, 0xb6, 0x09, //0x00003377 movzbl       (%rcx), %ecx
	0x80, 0xf9, 0x6e, //0x0000337a cmpb         $110, %cl
	0x0f, 0x84, 0xca, 0xff, 0xff, 0xff, //0x0000337d je           LBB0_834
	0x80, 0xf9, 0x72, //0x00003383 cmpb         $114, %cl
	0x0f, 0x84, 0xc1, 0xff, 0xff, 0xff, //0x00003386 je           LBB0_834
	0x80, 0xf9, 0x75, //0x0000338c cmpb         $117, %cl
	0x0f, 0x85, 0xab, 0xf6, 0xff, 0xff, //0x0000338f jne          LBB0_660
	0x4c, 0x89, 0xc9, //0x00003395 movq         %r9, %rcx
	0x48, 0x29, 0xf1, //0x00003398 subq         %rsi, %rcx
	0x48, 0x83, 0xf9, 0x04, //0x0000339b cmpq         $4, %rcx
	0x0f, 0x8c, 0x9b, 0xf6, 0xff, 0xff, //0x0000339f jl           LBB0_660
	0x8b, 0x0e, //0x000033a5 movl         (%rsi), %ecx
	0x89, 0xcf, //0x000033a7 movl         %ecx, %edi
	0xf7, 0xd7, //0x000033a9 notl         %edi
	0x8d, 0x91, 0xd0, 0xcf, 0xcf, 0xcf, //0x000033ab leal         $-808464432(%rcx), %edx
	0x81, 0xe7, 0x80, 0x80, 0x80, 0x80, //0x000033b1 andl         $-2139062144, %edi
	0x85, 0xd7, //0x000033b7 testl        %edx, %edi
	0x0f, 0x85, 0x81, 0xf6, 0xff, 0xff, //0x000033b9 jne          LBB0_660
	0x8d, 0x91, 0x19, 0x19, 0x19, 0x19, //0x000033bf leal         $421075225(%rcx), %edx
	0x09, 0xca, //0x000033c5 orl          %ecx, %edx
	0xf7, 0xc2, 0x80, 0x80, 0x80, 0x80, //0x000033c7 testl        $-2139062144, %edx
	0x0f, 0x85, 0x6d, 0xf6, 0xff, 0xff, //0x000033cd jne          LBB0_660
	0x89, 0xca, //0x000033d3 movl         %ecx, %edx
	0x81, 0xe2, 0x7f, 0x7f, 0x7f, 0x7f, //0x000033d5 andl         $2139062143, %edx
	0x41, 0xb8, 0xc0, 0xc0, 0xc0, 0xc0, //0x000033db movl         $-1061109568, %r8d
	0x41, 0x29, 0xd0, //0x000033e1 subl         %edx, %r8d
	0x44, 0x8d, 0x92, 0x46, 0x46, 0x46, 0x46, //0x000033e4 leal         $1179010630(%rdx), %r10d
	0x41, 0x21, 0xf8, //0x000033eb andl         %edi, %r8d
	0x45, 0x85, 0xd0, //0x000033ee testl        %r10d, %r8d
	0x0f, 0x85, 0x49, 0xf6, 0xff, 0xff, //0x000033f1 jne          LBB0_660
	0x41, 0xb8, 0xe0, 0xe0, 0xe0, 0xe0, //0x000033f7 movl         $-522133280, %r8d
	0x41, 0x29, 0xd0, //0x000033fd subl         %edx, %r8d
	0x81, 0xc2, 0x39, 0x39, 0x39, 0x39, //0x00003400 addl         $960051513, %edx
	0x44, 0x21, 0xc7, //0x00003406 andl         %r8d, %edi
	0x85, 0xd7, //0x00003409 testl        %edx, %edi
	0x0f, 0x85, 0x2f, 0xf6, 0xff, 0xff, //0x0000340b jne          LBB0_660
	0x0f, 0xc9, //0x00003411 bswapl       %ecx
	0x89, 0xca, //0x00003413 movl         %ecx, %edx
	0xc1, 0xea, 0x04, //0x00003415 shrl         $4, %edx
	0xf7, 0xd2, //0x00003418 notl         %edx
	0x81, 0xe2, 0x01, 0x01, 0x01, 0x01, //0x0000341a andl         $16843009, %edx
	0x8d, 0x14, 0xd2, //0x00003420 leal         (%rdx,%rdx,8), %edx
	0x81, 0xe1, 0x0f, 0x0f, 0x0f, 0x0f, //0x00003423 andl         $252645135, %ecx
	0x01, 0xd1, //0x00003429 addl         %edx, %ecx
	0x89, 0xcf, //0x0000342b movl         %ecx, %edi
	0xc1, 0xef, 0x04, //0x0000342d shrl         $4, %edi
	0x09, 0xcf, //0x00003430 orl          %ecx, %edi
	0x89, 0xf9, //0x00003432 movl         %edi, %ecx
	0xc1, 0xe9, 0x08, //0x00003434 shrl         $8, %ecx
	0x81, 0xe1, 0x00, 0xff, 0x00, 0x00, //0x00003437 andl         $65280, %ecx
	0x89, 0xfa, //0x0000343d movl         %edi, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x0000343f andl         $128, %edx
	0x48, 0x83, 0xc0, 0x06, //0x00003445 addq         $6, %rax
	0x09, 0xca, //0x00003449 orl          %ecx, %edx
	0x48, 0x89, 0xc6, //0x0000344b movq         %rax, %rsi
	0x0f, 0x84, 0x08, 0x00, 0x00, 0x00, //0x0000344e je           LBB0_848
	0xe9, 0xe7, 0xf5, 0xff, 0xff, //0x00003454 jmp          LBB0_660
	//0x00003459 LBB0_847
	0x48, 0x89, 0xce, //0x00003459 movq         %rcx, %rsi
	//0x0000345c LBB0_848
	0x40, 0x80, 0xff, 0x0d, //0x0000345c cmpb         $13, %dil
	0x0f, 0x84, 0xe7, 0xfe, 0xff, 0xff, //0x00003460 je           LBB0_834
	0x48, 0x89, 0xf0, //0x00003466 movq         %rsi, %rax
	0x40, 0x80, 0xff, 0x0a, //0x00003469 cmpb         $10, %dil
	0x0f, 0x84, 0xdd, 0xfe, 0xff, 0xff, //0x0000346d je           LBB0_835
	0xe9, 0xc8, 0xf5, 0xff, 0xff, //0x00003473 jmp          LBB0_660
	//0x00003478 LBB0_810
	0x80, 0xf9, 0x2f, //0x00003478 cmpb         $47, %cl
	0x0f, 0x85, 0x03, 0x00, 0x00, 0x00, //0x0000347b jne          LBB0_812
	0x41, 0xb0, 0x2f, //0x00003481 movb         $47, %r8b
	//0x00003484 LBB0_812
	0x48, 0x89, 0xc6, //0x00003484 movq         %rax, %rsi
	//0x00003487 LBB0_813
	0x41, 0x0f, 0xb6, 0xc0, //0x00003487 movzbl       %r8b, %eax
	0x48, 0x8b, 0x4d, 0xd0, //0x0000348b movq         $-48(%rbp), %rcx
	0x0f, 0xb6, 0x04, 0x01, //0x0000348f movzbl       (%rcx,%rax), %eax
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00003493 cmpl         $255, %eax
	0x0f, 0x84, 0x5d, 0x01, 0x00, 0x00, //0x00003498 je           LBB0_833
	0x41, 0xc1, 0xe7, 0x06, //0x0000349e shll         $6, %r15d
	0x41, 0x09, 0xc7, //0x000034a2 orl          %eax, %r15d
	0xbb, 0x03, 0x00, 0x00, 0x00, //0x000034a5 movl         $3, %ebx
	0x4c, 0x39, 0xce, //0x000034aa cmpq         %r9, %rsi
	0x0f, 0x82, 0x1f, 0x00, 0x00, 0x00, //0x000034ad jb           LBB0_817
	0xe9, 0x78, 0xf8, 0xff, 0xff, //0x000034b3 jmp          LBB0_704
	//0x000034b8 LBB0_831
	0x80, 0xf9, 0x6e, //0x000034b8 cmpb         $110, %cl
	0x0f, 0x85, 0x4c, 0x01, 0x00, 0x00, //0x000034bb jne          LBB0_854
	//0x000034c1 LBB0_815
	0x48, 0x89, 0xc6, //0x000034c1 movq         %rax, %rsi
	//0x000034c4 LBB0_816
	0xbb, 0x03, 0x00, 0x00, 0x00, //0x000034c4 movl         $3, %ebx
	0x4c, 0x39, 0xce, //0x000034c9 cmpq         %r9, %rsi
	0x0f, 0x83, 0xa3, 0xf7, 0xff, 0xff, //0x000034cc jae          LBB0_692
	//0x000034d2 LBB0_817
	0x48, 0x8d, 0x4e, 0x01, //0x000034d2 leaq         $1(%rsi), %rcx
	0x0f, 0xb6, 0x06, //0x000034d6 movzbl       (%rsi), %eax
	0x3c, 0x5c, //0x000034d9 cmpb         $92, %al
	0x0f, 0x85, 0xff, 0x00, 0x00, 0x00, //0x000034db jne          LBB0_829
	0x48, 0x8d, 0x46, 0x02, //0x000034e1 leaq         $2(%rsi), %rax
	0x41, 0xb0, 0xff, //0x000034e5 movb         $-1, %r8b
	0x4c, 0x39, 0xc8, //0x000034e8 cmpq         %r9, %rax
	0x0f, 0x87, 0x14, 0x01, 0x00, 0x00, //0x000034eb ja           LBB0_852
	0x0f, 0xb6, 0x09, //0x000034f1 movzbl       (%rcx), %ecx
	0x80, 0xf9, 0x71, //0x000034f4 cmpb         $113, %cl
	0x0f, 0x8e, 0xbb, 0xff, 0xff, 0xff, //0x000034f7 jle          LBB0_831
	0x80, 0xf9, 0x72, //0x000034fd cmpb         $114, %cl
	0x0f, 0x84, 0xbb, 0xff, 0xff, 0xff, //0x00003500 je           LBB0_815
	0x80, 0xf9, 0x75, //0x00003506 cmpb         $117, %cl
	0x0f, 0x85, 0x0a, 0x01, 0x00, 0x00, //0x00003509 jne          LBB0_856
	0x4c, 0x89, 0xc9, //0x0000350f movq         %r9, %rcx
	0x48, 0x29, 0xc1, //0x00003512 subq         %rax, %rcx
	0x48, 0x83, 0xf9, 0x04, //0x00003515 cmpq         $4, %rcx
	0x0f, 0x8c, 0xfa, 0x00, 0x00, 0x00, //0x00003519 jl           LBB0_856
	0x8b, 0x08, //0x0000351f movl         (%rax), %ecx
	0x89, 0xcb, //0x00003521 movl         %ecx, %ebx
	0xf7, 0xd3, //0x00003523 notl         %ebx
	0x8d, 0x91, 0xd0, 0xcf, 0xcf, 0xcf, //0x00003525 leal         $-808464432(%rcx), %edx
	0x81, 0xe3, 0x80, 0x80, 0x80, 0x80, //0x0000352b andl         $-2139062144, %ebx
	0x85, 0xd3, //0x00003531 testl        %edx, %ebx
	0x0f, 0x85, 0xe0, 0x00, 0x00, 0x00, //0x00003533 jne          LBB0_856
	0x8d, 0x91, 0x19, 0x19, 0x19, 0x19, //0x00003539 leal         $421075225(%rcx), %edx
	0x09, 0xca, //0x0000353f orl          %ecx, %edx
	0xf7, 0xc2, 0x80, 0x80, 0x80, 0x80, //0x00003541 testl        $-2139062144, %edx
	0x0f, 0x85, 0xcc, 0x00, 0x00, 0x00, //0x00003547 jne          LBB0_856
	0x89, 0xca, //0x0000354d movl         %ecx, %edx
	0x81, 0xe2, 0x7f, 0x7f, 0x7f, 0x7f, //0x0000354f andl         $2139062143, %edx
	0xbf, 0xc0, 0xc0, 0xc0, 0xc0, //0x00003555 movl         $-1061109568, %edi
	0x29, 0xd7, //0x0000355a subl         %edx, %edi
	0x44, 0x8d, 0x92, 0x46, 0x46, 0x46, 0x46, //0x0000355c leal         $1179010630(%rdx), %r10d
	0x21, 0xdf, //0x00003563 andl         %ebx, %edi
	0x44, 0x85, 0xd7, //0x00003565 testl        %r10d, %edi
	0x0f, 0x85, 0xab, 0x00, 0x00, 0x00, //0x00003568 jne          LBB0_856
	0xbf, 0xe0, 0xe0, 0xe0, 0xe0, //0x0000356e movl         $-522133280, %edi
	0x29, 0xd7, //0x00003573 subl         %edx, %edi
	0x81, 0xc2, 0x39, 0x39, 0x39, 0x39, //0x00003575 addl         $960051513, %edx
	0x21, 0xfb, //0x0000357b andl         %edi, %ebx
	0x85, 0xd3, //0x0000357d testl        %edx, %ebx
	0x0f, 0x85, 0x94, 0x00, 0x00, 0x00, //0x0000357f jne          LBB0_856
	0x0f, 0xc9, //0x00003585 bswapl       %ecx
	0x89, 0xc8, //0x00003587 movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x00003589 shrl         $4, %eax
	0xf7, 0xd0, //0x0000358c notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x0000358e andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x00003593 leal         (%rax,%rax,8), %eax
	0x81, 0xe1, 0x0f, 0x0f, 0x0f, 0x0f, //0x00003596 andl         $252645135, %ecx
	0x01, 0xc1, //0x0000359c addl         %eax, %ecx
	0x89, 0xc8, //0x0000359e movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x000035a0 shrl         $4, %eax
	0x09, 0xc8, //0x000035a3 orl          %ecx, %eax
	0x89, 0xc1, //0x000035a5 movl         %eax, %ecx
	0xc1, 0xe9, 0x08, //0x000035a7 shrl         $8, %ecx
	0x81, 0xe1, 0x00, 0xff, 0x00, 0x00, //0x000035aa andl         $65280, %ecx
	0x89, 0xc2, //0x000035b0 movl         %eax, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x000035b2 andl         $128, %edx
	0x48, 0x83, 0xc6, 0x06, //0x000035b8 addq         $6, %rsi
	0x09, 0xca, //0x000035bc orl          %ecx, %edx
	0x0f, 0x85, 0x58, 0x00, 0x00, 0x00, //0x000035be jne          LBB0_857
	0x3c, 0x0d, //0x000035c4 cmpb         $13, %al
	0x0f, 0x85, 0x1f, 0x00, 0x00, 0x00, //0x000035c6 jne          LBB0_830
	0xe9, 0xf3, 0xfe, 0xff, 0xff, //0x000035cc jmp          LBB0_816
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000035d1 .p2align 4, 0x90
	//0x000035e0 LBB0_829
	0x48, 0x89, 0xce, //0x000035e0 movq         %rcx, %rsi
	0x3c, 0x0d, //0x000035e3 cmpb         $13, %al
	0x0f, 0x84, 0xd9, 0xfe, 0xff, 0xff, //0x000035e5 je           LBB0_816
	//0x000035eb LBB0_830
	0x41, 0x89, 0xc0, //0x000035eb movl         %eax, %r8d
	0x3c, 0x0a, //0x000035ee cmpb         $10, %al
	0x0f, 0x84, 0xce, 0xfe, 0xff, 0xff, //0x000035f0 je           LBB0_816
	0xe9, 0x21, 0x00, 0x00, 0x00, //0x000035f6 jmp          LBB0_857
	//0x000035fb LBB0_833
	0xbb, 0x02, 0x00, 0x00, 0x00, //0x000035fb movl         $2, %ebx
	0xe9, 0xb9, 0xf7, 0xff, 0xff, //0x00003600 jmp          LBB0_716
	//0x00003605 LBB0_852
	0x48, 0x89, 0xce, //0x00003605 movq         %rcx, %rsi
	0xe9, 0x0f, 0x00, 0x00, 0x00, //0x00003608 jmp          LBB0_857
	//0x0000360d LBB0_854
	0x80, 0xf9, 0x2f, //0x0000360d cmpb         $47, %cl
	0x0f, 0x85, 0x03, 0x00, 0x00, 0x00, //0x00003610 jne          LBB0_856
	0x41, 0xb0, 0x2f, //0x00003616 movb         $47, %r8b
	//0x00003619 LBB0_856
	0x48, 0x89, 0xc6, //0x00003619 movq         %rax, %rsi
	//0x0000361c LBB0_857
	0x41, 0x0f, 0xb6, 0xc0, //0x0000361c movzbl       %r8b, %eax
	0x48, 0x8b, 0x4d, 0xd0, //0x00003620 movq         $-48(%rbp), %rcx
	0x0f, 0xb6, 0x04, 0x01, //0x00003624 movzbl       (%rcx,%rax), %eax
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00003628 cmpl         $255, %eax
	0x0f, 0x84, 0x6d, 0xfb, 0xff, 0xff, //0x0000362d je           LBB0_859
	//0x00003633 LBB0_858
	0x41, 0xc1, 0xe7, 0x06, //0x00003633 shll         $6, %r15d
	0x41, 0x09, 0xc7, //0x00003637 orl          %eax, %r15d
	0xbb, 0x04, 0x00, 0x00, 0x00, //0x0000363a movl         $4, %ebx
	0xe9, 0xec, 0xf6, 0xff, 0xff, //0x0000363f jmp          LBB0_704
	//0x00003644 LBB0_861
	0x48, 0x89, 0xce, //0x00003644 movq         %rcx, %rsi
	0xe9, 0xf4, 0xf3, 0xff, 0xff, //0x00003647 jmp          LBB0_660
	//0x0000364c LBB0_862
	0x48, 0x2b, 0x55, 0x98, //0x0000364c subq         $-104(%rbp), %rdx
	0x48, 0x8b, 0x45, 0x80, //0x00003650 movq         $-128(%rbp), %rax
	0x48, 0x01, 0x50, 0x08, //0x00003654 addq         %rdx, $8(%rax)
	0x48, 0x89, 0xd0, //0x00003658 movq         %rdx, %rax
	0xe9, 0x0a, 0x00, 0x00, 0x00, //0x0000365b jmp          LBB0_865
	//0x00003660 LBB0_863
	0x4c, 0x01, 0xd9, //0x00003660 addq         %r11, %rcx
	//0x00003663 LBB0_864
	0x48, 0x8b, 0x45, 0xb0, //0x00003663 movq         $-80(%rbp), %rax
	0x48, 0x29, 0xc8, //0x00003667 subq         %rcx, %rax
	//0x0000366a LBB0_865
	0x48, 0x83, 0xc4, 0x58, //0x0000366a addq         $88, %rsp
	0x5b, //0x0000366e popq         %rbx
	0x41, 0x5c, //0x0000366f popq         %r12
	0x41, 0x5d, //0x00003671 popq         %r13
	0x41, 0x5e, //0x00003673 popq         %r14
	0x41, 0x5f, //0x00003675 popq         %r15
	0x5d, //0x00003677 popq         %rbp
	0xc5, 0xf8, 0x77, //0x00003678 vzeroupper   
	0xc3, //0x0000367b retq         
	0x00, 0x00, 0x00, 0x00, //0x0000367c .p2align 4, 0x00
	//0x00003680 _VecDecodeCharsetStd
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00003680 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00003690 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x3e, 0xff, 0xff, 0xff, 0x3f, //0x000036a0 QUAD $0xffffffffffffffff; QUAD $0x3fffffff3effffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff>\xff\xff\xff?'
	0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x3d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x000036b0 QUAD $0x3b3a393837363534; QUAD $0xffffffffffff3d3c  // .ascii 16, '456789:;<=\xff\xff\xff\xff\xff\xff'
	0xff, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, //0x000036c0 QUAD $0x06050403020100ff; QUAD $0x0e0d0c0b0a090807  // .ascii 16, '\xff\x00\x01\x02\x03\x04\x05\x06\x07\x08\t\n\x0b\x0c\r\x0e'
	0x0f, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19, 0xff, 0xff, 0xff, 0xff, 0xff, //0x000036d0 QUAD $0x161514131211100f; QUAD $0xffffffffff191817  // .ascii 16, '\x0f\x10\x11\x12\x13\x14\x15\x16\x17\x18\x19\xff\xff\xff\xff\xff'
	0xff, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, //0x000036e0 QUAD $0x201f1e1d1c1b1aff; QUAD $0x2827262524232221  // .ascii 16, '\xff\x1a\x1b\x1c\x1d\x1e\x1f !"#$%&\'('
	0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f, 0x30, 0x31, 0x32, 0x33, 0xff, 0xff, 0xff, 0xff, 0xff, //0x000036f0 QUAD $0x302f2e2d2c2b2a29; QUAD $0xffffffffff333231  // .ascii 16, ')*+,-./0123\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00003700 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00003710 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00003720 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00003730 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00003740 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00003750 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00003760 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00003770 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	//0x00003780 .p2align 4, 0x00
	//0x00003780 _VecDecodeCharsetURL
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00003780 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00003790 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x3e, 0xff, 0xff, //0x000037a0 QUAD $0xffffffffffffffff; QUAD $0xffff3effffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff>\xff\xff'
	0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x3d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x000037b0 QUAD $0x3b3a393837363534; QUAD $0xffffffffffff3d3c  // .ascii 16, '456789:;<=\xff\xff\xff\xff\xff\xff'
	0xff, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, //0x000037c0 QUAD $0x06050403020100ff; QUAD $0x0e0d0c0b0a090807  // .ascii 16, '\xff\x00\x01\x02\x03\x04\x05\x06\x07\x08\t\n\x0b\x0c\r\x0e'
	0x0f, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19, 0xff, 0xff, 0xff, 0xff, 0x3f, //0x000037d0 QUAD $0x161514131211100f; QUAD $0x3fffffffff191817  // .ascii 16, '\x0f\x10\x11\x12\x13\x14\x15\x16\x17\x18\x19\xff\xff\xff\xff?'
	0xff, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, //0x000037e0 QUAD $0x201f1e1d1c1b1aff; QUAD $0x2827262524232221  // .ascii 16, '\xff\x1a\x1b\x1c\x1d\x1e\x1f !"#$%&\'('
	0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f, 0x30, 0x31, 0x32, 0x33, 0xff, 0xff, 0xff, 0xff, 0xff, //0x000037f0 QUAD $0x302f2e2d2c2b2a29; QUAD $0xffffffffff333231  // .ascii 16, ')*+,-./0123\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00003800 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00003810 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00003820 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00003830 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00003840 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00003850 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00003860 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00003870 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
}
 
