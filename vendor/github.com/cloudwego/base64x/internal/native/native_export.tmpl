
// Code generated by <PERSON><PERSON>, DO NOT EDIT.

/*
 * Copyright 2025 ByteDance Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package {{PACKAGE}}

import (
    `github.com/bytedance/sonic/loader`
)

func Use() {
    loader.WrapGoC(_text_b64encode, _cfunc_b64encode, []loader.GoC{{"_b64encode", &S_b64encode, &F_b64encode}}, "{{PACKAGE}}", "{{PACKAGE}}/b64encode.c")
    loader.WrapGoC(_text_b64decode, _cfunc_b64decode, []loader.GoC{{"_b64decode", &S_b64decode, &F_b64decode}}, "{{PACKAGE}}", "{{PACKAGE}}/b64decode.c")
}