// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package sse

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__b64decode = 0
)

const (
    _stack__b64decode = 144
)

const (
    _size__b64decode = 9968
)

var (
    _pcsp__b64decode = [][2]uint32{
        {0x1, 0},
        {0x6, 8},
        {0x8, 16},
        {0xa, 24},
        {0xc, 32},
        {0xd, 40},
        {0x11, 48},
        {0x26e1, 144},
        {0x26e2, 48},
        {0x26e4, 40},
        {0x26e6, 32},
        {0x26e8, 24},
        {0x26ea, 16},
        {0x26eb, 8},
        {0x26f0, 0},
    }
)

var _cfunc_b64decode = []loader.CFunc{
    {"_b64decode_entry", 0,  _entry__b64decode, 0, nil},
    {"_b64decode", _entry__b64decode, _size__b64decode, _stack__b64decode, _pcsp__b64decode},
}
