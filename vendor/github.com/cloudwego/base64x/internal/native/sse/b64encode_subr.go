// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package sse

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__b64encode = 0
)

const (
    _stack__b64encode = 32
)

const (
    _size__b64encode = 448
)

var (
    _pcsp__b64encode = [][2]uint32{
        {0x1, 0},
        {0x6, 8},
        {0x8, 16},
        {0x9, 24},
        {0x1ab, 32},
        {0x1ad, 24},
        {0x1af, 16},
        {0x1b0, 8},
        {0x1c0, 0},
    }
)

var _cfunc_b64encode = []loader.CFunc{
    {"_b64encode_entry", 0,  _entry__b64encode, 0, nil},
    {"_b64encode", _entry__b64encode, _size__b64encode, _stack__b64encode, _pcsp__b64encode},
}
