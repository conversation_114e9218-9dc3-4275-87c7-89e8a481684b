// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package sse

var _text_b64encode = []byte{
	// .p2align 4, 0x90
	// _b64encode
	0x55, // pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000001 movq         %rsp, %rbp
	0x41, 0x57, //0x00000004 pushq        %r15
	0x41, 0x56, //0x00000006 pushq        %r14
	0x53, //0x00000008 pushq        %rbx
	0x4c, 0x8b, 0x4e, 0x08, //0x00000009 movq         $8(%rsi), %r9
	0x4d, 0x85, 0xc9, //0x0000000d testq        %r9, %r9
	0x0f, 0x84, 0x94, 0x01, 0x00, 0x00, //0x00000010 je           LBB0_15
	0x4c, 0x8b, 0x3e, //0x00000016 movq         (%rsi), %r15
	0x4c, 0x8b, 0x07, //0x00000019 movq         (%rdi), %r8
	0x4c, 0x03, 0x47, 0x08, //0x0000001c addq         $8(%rdi), %r8
	0x4d, 0x01, 0xf9, //0x00000020 addq         %r15, %r9
	0xf6, 0xc2, 0x01, //0x00000023 testb        $1, %dl
	0x48, 0x8d, 0x0d, 0x93, 0x01, 0x00, 0x00, //0x00000026 leaq         $403(%rip), %rcx  /* _TabEncodeCharsetStd+0(%rip) */
	0x4c, 0x8d, 0x1d, 0xcc, 0x01, 0x00, 0x00, //0x0000002d leaq         $460(%rip), %r11  /* _TabEncodeCharsetURL+0(%rip) */
	0x4c, 0x0f, 0x44, 0xd9, //0x00000034 cmoveq       %rcx, %r11
	0x4d, 0x8d, 0x51, 0xfc, //0x00000038 leaq         $-4(%r9), %r10
	0x4d, 0x89, 0xc6, //0x0000003c movq         %r8, %r14
	0x4d, 0x39, 0xfa, //0x0000003f cmpq         %r15, %r10
	0x0f, 0x82, 0x5e, 0x00, 0x00, 0x00, //0x00000042 jb           LBB0_3
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000048 .p2align 4, 0x90
	//0x00000050 LBB0_2
	0x41, 0x8b, 0x37, //0x00000050 movl         (%r15), %esi
	0x0f, 0xce, //0x00000053 bswapl       %esi
	0x48, 0x89, 0xf3, //0x00000055 movq         %rsi, %rbx
	0x48, 0xc1, 0xeb, 0x1a, //0x00000058 shrq         $26, %rbx
	0x89, 0xf1, //0x0000005c movl         %esi, %ecx
	0xc1, 0xe9, 0x14, //0x0000005e shrl         $20, %ecx
	0x83, 0xe1, 0x3f, //0x00000061 andl         $63, %ecx
	0x89, 0xf0, //0x00000064 movl         %esi, %eax
	0xc1, 0xe8, 0x0e, //0x00000066 shrl         $14, %eax
	0x83, 0xe0, 0x3f, //0x00000069 andl         $63, %eax
	0xc1, 0xee, 0x08, //0x0000006c shrl         $8, %esi
	0x83, 0xe6, 0x3f, //0x0000006f andl         $63, %esi
	0x49, 0x83, 0xc7, 0x03, //0x00000072 addq         $3, %r15
	0x41, 0x0f, 0xb6, 0x1c, 0x1b, //0x00000076 movzbl       (%r11,%rbx), %ebx
	0x41, 0x88, 0x1e, //0x0000007b movb         %bl, (%r14)
	0x41, 0x0f, 0xb6, 0x0c, 0x0b, //0x0000007e movzbl       (%r11,%rcx), %ecx
	0x41, 0x88, 0x4e, 0x01, //0x00000083 movb         %cl, $1(%r14)
	0x41, 0x0f, 0xb6, 0x04, 0x03, //0x00000087 movzbl       (%r11,%rax), %eax
	0x41, 0x88, 0x46, 0x02, //0x0000008c movb         %al, $2(%r14)
	0x41, 0x0f, 0xb6, 0x04, 0x33, //0x00000090 movzbl       (%r11,%rsi), %eax
	0x41, 0x88, 0x46, 0x03, //0x00000095 movb         %al, $3(%r14)
	0x49, 0x83, 0xc6, 0x04, //0x00000099 addq         $4, %r14
	0x4d, 0x39, 0xd7, //0x0000009d cmpq         %r10, %r15
	0x0f, 0x86, 0xaa, 0xff, 0xff, 0xff, //0x000000a0 jbe          LBB0_2
	//0x000000a6 LBB0_3
	0x4d, 0x29, 0xf9, //0x000000a6 subq         %r15, %r9
	0x45, 0x0f, 0xb6, 0x17, //0x000000a9 movzbl       (%r15), %r10d
	0x49, 0x83, 0xf9, 0x01, //0x000000ad cmpq         $1, %r9
	0x0f, 0x84, 0xa8, 0x00, 0x00, 0x00, //0x000000b1 je           LBB0_10
	0x4c, 0x89, 0xd6, //0x000000b7 movq         %r10, %rsi
	0x48, 0xc1, 0xe6, 0x10, //0x000000ba shlq         $16, %rsi
	0x49, 0x83, 0xf9, 0x02, //0x000000be cmpq         $2, %r9
	0x0f, 0x84, 0x54, 0x00, 0x00, 0x00, //0x000000c2 je           LBB0_7
	0x49, 0x83, 0xf9, 0x03, //0x000000c8 cmpq         $3, %r9
	0x0f, 0x85, 0xd1, 0x00, 0x00, 0x00, //0x000000cc jne          LBB0_14
	0x41, 0x0f, 0xb6, 0x57, 0x02, //0x000000d2 movzbl       $2(%r15), %edx
	0x09, 0xd6, //0x000000d7 orl          %edx, %esi
	0x41, 0x0f, 0xb6, 0x47, 0x01, //0x000000d9 movzbl       $1(%r15), %eax
	0xc1, 0xe0, 0x08, //0x000000de shll         $8, %eax
	0x09, 0xf0, //0x000000e1 orl          %esi, %eax
	0x49, 0xc1, 0xea, 0x02, //0x000000e3 shrq         $2, %r10
	0x43, 0x8a, 0x0c, 0x13, //0x000000e7 movb         (%r11,%r10), %cl
	0x41, 0x88, 0x0e, //0x000000eb movb         %cl, (%r14)
	0x89, 0xc1, //0x000000ee movl         %eax, %ecx
	0xc1, 0xe9, 0x0c, //0x000000f0 shrl         $12, %ecx
	0x83, 0xe1, 0x3f, //0x000000f3 andl         $63, %ecx
	0x41, 0x8a, 0x0c, 0x0b, //0x000000f6 movb         (%r11,%rcx), %cl
	0x41, 0x88, 0x4e, 0x01, //0x000000fa movb         %cl, $1(%r14)
	0xc1, 0xe8, 0x06, //0x000000fe shrl         $6, %eax
	0x83, 0xe0, 0x3f, //0x00000101 andl         $63, %eax
	0x41, 0x8a, 0x04, 0x03, //0x00000104 movb         (%r11,%rax), %al
	0x41, 0x88, 0x46, 0x02, //0x00000108 movb         %al, $2(%r14)
	0x83, 0xe2, 0x3f, //0x0000010c andl         $63, %edx
	0x41, 0x8a, 0x04, 0x13, //0x0000010f movb         (%r11,%rdx), %al
	0x41, 0x88, 0x46, 0x03, //0x00000113 movb         %al, $3(%r14)
	0xe9, 0x71, 0x00, 0x00, 0x00, //0x00000117 jmp          LBB0_13
	//0x0000011c LBB0_7
	0x41, 0x0f, 0xb6, 0x47, 0x01, //0x0000011c movzbl       $1(%r15), %eax
	0x89, 0xc1, //0x00000121 movl         %eax, %ecx
	0xc1, 0xe1, 0x08, //0x00000123 shll         $8, %ecx
	0x09, 0xf1, //0x00000126 orl          %esi, %ecx
	0x49, 0xc1, 0xea, 0x02, //0x00000128 shrq         $2, %r10
	0x43, 0x8a, 0x1c, 0x13, //0x0000012c movb         (%r11,%r10), %bl
	0x41, 0x88, 0x1e, //0x00000130 movb         %bl, (%r14)
	0xc1, 0xe9, 0x0c, //0x00000133 shrl         $12, %ecx
	0x83, 0xe1, 0x3f, //0x00000136 andl         $63, %ecx
	0x41, 0x8a, 0x0c, 0x0b, //0x00000139 movb         (%r11,%rcx), %cl
	0x41, 0x88, 0x4e, 0x01, //0x0000013d movb         %cl, $1(%r14)
	0x83, 0xe0, 0x0f, //0x00000141 andl         $15, %eax
	0x41, 0x8a, 0x04, 0x83, //0x00000144 movb         (%r11,%rax,4), %al
	0x41, 0x88, 0x46, 0x02, //0x00000148 movb         %al, $2(%r14)
	0xf6, 0xc2, 0x02, //0x0000014c testb        $2, %dl
	0x0f, 0x85, 0x41, 0x00, 0x00, 0x00, //0x0000014f jne          LBB0_8
	0x41, 0xc6, 0x46, 0x03, 0x3d, //0x00000155 movb         $61, $3(%r14)
	0xe9, 0x2e, 0x00, 0x00, 0x00, //0x0000015a jmp          LBB0_13
	//0x0000015f LBB0_10
	0x4c, 0x89, 0xd0, //0x0000015f movq         %r10, %rax
	0x48, 0xc1, 0xe8, 0x02, //0x00000162 shrq         $2, %rax
	0x41, 0x8a, 0x04, 0x03, //0x00000166 movb         (%r11,%rax), %al
	0x41, 0x88, 0x06, //0x0000016a movb         %al, (%r14)
	0x41, 0xc1, 0xe2, 0x04, //0x0000016d shll         $4, %r10d
	0x41, 0x83, 0xe2, 0x30, //0x00000171 andl         $48, %r10d
	0x43, 0x8a, 0x04, 0x13, //0x00000175 movb         (%r11,%r10), %al
	0x41, 0x88, 0x46, 0x01, //0x00000179 movb         %al, $1(%r14)
	0xf6, 0xc2, 0x02, //0x0000017d testb        $2, %dl
	0x0f, 0x85, 0x19, 0x00, 0x00, 0x00, //0x00000180 jne          LBB0_11
	0x66, 0x41, 0xc7, 0x46, 0x02, 0x3d, 0x3d, //0x00000186 movw         $15677, $2(%r14)
	//0x0000018d LBB0_13
	0x49, 0x83, 0xc6, 0x04, //0x0000018d addq         $4, %r14
	0xe9, 0x0d, 0x00, 0x00, 0x00, //0x00000191 jmp          LBB0_14
	//0x00000196 LBB0_8
	0x49, 0x83, 0xc6, 0x03, //0x00000196 addq         $3, %r14
	0xe9, 0x04, 0x00, 0x00, 0x00, //0x0000019a jmp          LBB0_14
	//0x0000019f LBB0_11
	0x49, 0x83, 0xc6, 0x02, //0x0000019f addq         $2, %r14
	//0x000001a3 LBB0_14
	0x4d, 0x29, 0xc6, //0x000001a3 subq         %r8, %r14
	0x4c, 0x01, 0x77, 0x08, //0x000001a6 addq         %r14, $8(%rdi)
	//0x000001aa LBB0_15
	0x5b, //0x000001aa popq         %rbx
	0x41, 0x5e, //0x000001ab popq         %r14
	0x41, 0x5f, //0x000001ad popq         %r15
	0x5d, //0x000001af popq         %rbp
	0xc3, //0x000001b0 retq         
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000001b1 .p2align 4, 0x00
	//0x000001c0 _TabEncodeCharsetStd
	0x41, 0x42, 0x43, 0x44, 0x45, 0x46, 0x47, 0x48, 0x49, 0x4a, 0x4b, 0x4c, 0x4d, 0x4e, 0x4f, 0x50, //0x000001c0 QUAD $0x4847464544434241; QUAD $0x504f4e4d4c4b4a49  // .ascii 16, 'ABCDEFGHIJKLMNOP'
	0x51, 0x52, 0x53, 0x54, 0x55, 0x56, 0x57, 0x58, 0x59, 0x5a, 0x61, 0x62, 0x63, 0x64, 0x65, 0x66, //0x000001d0 QUAD $0x5857565554535251; QUAD $0x6665646362615a59  // .ascii 16, 'QRSTUVWXYZabcdef'
	0x67, 0x68, 0x69, 0x6a, 0x6b, 0x6c, 0x6d, 0x6e, 0x6f, 0x70, 0x71, 0x72, 0x73, 0x74, 0x75, 0x76, //0x000001e0 QUAD $0x6e6d6c6b6a696867; QUAD $0x767574737271706f  // .ascii 16, 'ghijklmnopqrstuv'
	0x77, 0x78, 0x79, 0x7a, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x2b, 0x2f, //0x000001f0 QUAD $0x333231307a797877; QUAD $0x2f2b393837363534  // .ascii 16, 'wxyz0123456789+/'
	//0x00000200 .p2align 4, 0x00
	//0x00000200 _TabEncodeCharsetURL
	0x41, 0x42, 0x43, 0x44, 0x45, 0x46, 0x47, 0x48, 0x49, 0x4a, 0x4b, 0x4c, 0x4d, 0x4e, 0x4f, 0x50, //0x00000200 QUAD $0x4847464544434241; QUAD $0x504f4e4d4c4b4a49  // .ascii 16, 'ABCDEFGHIJKLMNOP'
	0x51, 0x52, 0x53, 0x54, 0x55, 0x56, 0x57, 0x58, 0x59, 0x5a, 0x61, 0x62, 0x63, 0x64, 0x65, 0x66, //0x00000210 QUAD $0x5857565554535251; QUAD $0x6665646362615a59  // .ascii 16, 'QRSTUVWXYZabcdef'
	0x67, 0x68, 0x69, 0x6a, 0x6b, 0x6c, 0x6d, 0x6e, 0x6f, 0x70, 0x71, 0x72, 0x73, 0x74, 0x75, 0x76, //0x00000220 QUAD $0x6e6d6c6b6a696867; QUAD $0x767574737271706f  // .ascii 16, 'ghijklmnopqrstuv'
	0x77, 0x78, 0x79, 0x7a, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x2d, 0x5f, //0x00000230 QUAD $0x333231307a797877; QUAD $0x5f2d393837363534  // .ascii 16, 'wxyz0123456789-_'
}
 
