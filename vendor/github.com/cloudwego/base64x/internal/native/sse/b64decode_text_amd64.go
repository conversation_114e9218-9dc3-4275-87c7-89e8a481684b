// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package sse

var _text_b64decode = []byte{
	// .p2align 4, 0x90
	// _b64decode
	0x55, // pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000001 movq         %rsp, %rbp
	0x41, 0x57, //0x00000004 pushq        %r15
	0x41, 0x56, //0x00000006 pushq        %r14
	0x41, 0x55, //0x00000008 pushq        %r13
	0x41, 0x54, //0x0000000a pushq        %r12
	0x53, //0x0000000c pushq        %rbx
	0x48, 0x83, 0xec, 0x60, //0x0000000d subq         $96, %rsp
	0x48, 0x85, 0xd2, //0x00000011 testq        %rdx, %rdx
	0x0f, 0x84, 0x54, 0x1a, 0x00, 0x00, //0x00000014 je           LBB0_432
	0x48, 0x8b, 0x07, //0x0000001a movq         (%rdi), %rax
	0x48, 0x8b, 0x5f, 0x10, //0x0000001d movq         $16(%rdi), %rbx
	0x48, 0x89, 0x5d, 0x88, //0x00000021 movq         %rbx, $-120(%rbp)
	0x48, 0x89, 0xbd, 0x78, 0xff, 0xff, 0xff, //0x00000025 movq         %rdi, $-136(%rbp)
	0x48, 0x8b, 0x7f, 0x08, //0x0000002c movq         $8(%rdi), %rdi
	0x48, 0x89, 0x45, 0xb0, //0x00000030 movq         %rax, $-80(%rbp)
	0x48, 0x01, 0xc7, //0x00000034 addq         %rax, %rdi
	0x4c, 0x8d, 0x0c, 0x16, //0x00000037 leaq         (%rsi,%rdx), %r9
	0x89, 0x4d, 0xc4, //0x0000003b movl         %ecx, $-60(%rbp)
	0xf6, 0xc1, 0x01, //0x0000003e testb        $1, %cl
	0x48, 0x8d, 0x05, 0xa8, 0x26, 0x00, 0x00, //0x00000041 leaq         $9896(%rip), %rax  /* _VecDecodeCharsetStd+0(%rip) */
	0x48, 0x8d, 0x0d, 0xa1, 0x27, 0x00, 0x00, //0x00000048 leaq         $10145(%rip), %rcx  /* _VecDecodeCharsetURL+0(%rip) */
	0x48, 0x0f, 0x44, 0xc8, //0x0000004f cmoveq       %rax, %rcx
	0x48, 0x89, 0x4d, 0xd0, //0x00000053 movq         %rcx, $-48(%rbp)
	0x48, 0x89, 0x55, 0xa0, //0x00000057 movq         %rdx, $-96(%rbp)
	0x48, 0x8d, 0x4c, 0x16, 0xf8, //0x0000005b leaq         $-8(%rsi,%rdx), %rcx
	0x48, 0x89, 0x7d, 0xa8, //0x00000060 movq         %rdi, $-88(%rbp)
	0x49, 0x89, 0xf6, //0x00000064 movq         %rsi, %r14
	0x48, 0x89, 0xf0, //0x00000067 movq         %rsi, %rax
	0x48, 0x89, 0x75, 0xb8, //0x0000006a movq         %rsi, $-72(%rbp)
	0x48, 0x89, 0x4d, 0x90, //0x0000006e movq         %rcx, $-112(%rbp)
	0x48, 0x39, 0xf1, //0x00000072 cmpq         %rsi, %rcx
	0x0f, 0x82, 0x09, 0x0d, 0x00, 0x00, //0x00000075 jb           LBB0_214
	0x48, 0x8b, 0x45, 0xb0, //0x0000007b movq         $-80(%rbp), %rax
	0x48, 0x8b, 0x4d, 0x88, //0x0000007f movq         $-120(%rbp), %rcx
	0x48, 0x01, 0xc1, //0x00000083 addq         %rax, %rcx
	0x48, 0x83, 0xc1, 0xf8, //0x00000086 addq         $-8, %rcx
	0x48, 0x8b, 0x45, 0xa8, //0x0000008a movq         $-88(%rbp), %rax
	0x48, 0x89, 0xc7, //0x0000008e movq         %rax, %rdi
	0x4c, 0x8b, 0x75, 0xb8, //0x00000091 movq         $-72(%rbp), %r14
	0x48, 0x89, 0x4d, 0x98, //0x00000095 movq         %rcx, $-104(%rbp)
	0x48, 0x39, 0xc8, //0x00000099 cmpq         %rcx, %rax
	0x0f, 0x87, 0xe2, 0x0c, 0x00, 0x00, //0x0000009c ja           LBB0_214
	0x4c, 0x8b, 0x75, 0xb8, //0x000000a2 movq         $-72(%rbp), %r14
	0x48, 0x8b, 0x45, 0xa0, //0x000000a6 movq         $-96(%rbp), %rax
	0x4c, 0x01, 0xf0, //0x000000aa addq         %r14, %rax
	0x48, 0x83, 0xc0, 0xff, //0x000000ad addq         $-1, %rax
	0x48, 0x89, 0x45, 0x80, //0x000000b1 movq         %rax, $-128(%rbp)
	0x48, 0x8b, 0x7d, 0xa8, //0x000000b5 movq         $-88(%rbp), %rdi
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000000b9 .p2align 4, 0x90
	//0x000000c0 LBB0_4
	0x48, 0x89, 0x7d, 0xc8, //0x000000c0 movq         %rdi, $-56(%rbp)
	0x41, 0x0f, 0xb6, 0x06, //0x000000c4 movzbl       (%r14), %eax
	0x48, 0x8b, 0x4d, 0xd0, //0x000000c8 movq         $-48(%rbp), %rcx
	0x44, 0x0f, 0xb6, 0x2c, 0x01, //0x000000cc movzbl       (%rcx,%rax), %r13d
	0x41, 0x0f, 0xb6, 0x46, 0x01, //0x000000d1 movzbl       $1(%r14), %eax
	0x0f, 0xb6, 0x14, 0x01, //0x000000d6 movzbl       (%rcx,%rax), %edx
	0x41, 0x0f, 0xb6, 0x46, 0x02, //0x000000da movzbl       $2(%r14), %eax
	0x44, 0x0f, 0xb6, 0x3c, 0x01, //0x000000df movzbl       (%rcx,%rax), %r15d
	0x41, 0x0f, 0xb6, 0x46, 0x03, //0x000000e4 movzbl       $3(%r14), %eax
	0x0f, 0xb6, 0x04, 0x01, //0x000000e9 movzbl       (%rcx,%rax), %eax
	0x41, 0x0f, 0xb6, 0x76, 0x04, //0x000000ed movzbl       $4(%r14), %esi
	0x44, 0x0f, 0xb6, 0x14, 0x31, //0x000000f2 movzbl       (%rcx,%rsi), %r10d
	0x41, 0x0f, 0xb6, 0x7e, 0x05, //0x000000f7 movzbl       $5(%r14), %edi
	0x44, 0x0f, 0xb6, 0x1c, 0x39, //0x000000fc movzbl       (%rcx,%rdi), %r11d
	0x41, 0x0f, 0xb6, 0x7e, 0x06, //0x00000101 movzbl       $6(%r14), %edi
	0x0f, 0xb6, 0x3c, 0x39, //0x00000106 movzbl       (%rcx,%rdi), %edi
	0x41, 0x0f, 0xb6, 0x5e, 0x07, //0x0000010a movzbl       $7(%r14), %ebx
	0x0f, 0xb6, 0x1c, 0x19, //0x0000010f movzbl       (%rcx,%rbx), %ebx
	0x41, 0x89, 0xd0, //0x00000113 movl         %edx, %r8d
	0x45, 0x09, 0xe8, //0x00000116 orl          %r13d, %r8d
	0x41, 0x89, 0xc4, //0x00000119 movl         %eax, %r12d
	0x45, 0x09, 0xfc, //0x0000011c orl          %r15d, %r12d
	0x45, 0x09, 0xc4, //0x0000011f orl          %r8d, %r12d
	0x44, 0x89, 0xd9, //0x00000122 movl         %r11d, %ecx
	0x44, 0x09, 0xd1, //0x00000125 orl          %r10d, %ecx
	0x89, 0xfe, //0x00000128 movl         %edi, %esi
	0x09, 0xce, //0x0000012a orl          %ecx, %esi
	0x44, 0x09, 0xe6, //0x0000012c orl          %r12d, %esi
	0x89, 0xd9, //0x0000012f movl         %ebx, %ecx
	0x09, 0xf1, //0x00000131 orl          %esi, %ecx
	0x80, 0xf9, 0xff, //0x00000133 cmpb         $-1, %cl
	0x0f, 0x84, 0x54, 0x00, 0x00, 0x00, //0x00000136 je           LBB0_6
	0x49, 0xc1, 0xe5, 0x3a, //0x0000013c shlq         $58, %r13
	0x48, 0xc1, 0xe2, 0x34, //0x00000140 shlq         $52, %rdx
	0x4c, 0x09, 0xea, //0x00000144 orq          %r13, %rdx
	0x49, 0xc1, 0xe7, 0x2e, //0x00000147 shlq         $46, %r15
	0x48, 0xc1, 0xe0, 0x28, //0x0000014b shlq         $40, %rax
	0x4c, 0x09, 0xf8, //0x0000014f orq          %r15, %rax
	0x48, 0x09, 0xd0, //0x00000152 orq          %rdx, %rax
	0x49, 0xc1, 0xe2, 0x22, //0x00000155 shlq         $34, %r10
	0x49, 0xc1, 0xe3, 0x1c, //0x00000159 shlq         $28, %r11
	0x4d, 0x09, 0xd3, //0x0000015d orq          %r10, %r11
	0x48, 0xc1, 0xe7, 0x16, //0x00000160 shlq         $22, %rdi
	0x4c, 0x09, 0xdf, //0x00000164 orq          %r11, %rdi
	0x48, 0x09, 0xc7, //0x00000167 orq          %rax, %rdi
	0x48, 0xc1, 0xe3, 0x10, //0x0000016a shlq         $16, %rbx
	0x48, 0x09, 0xfb, //0x0000016e orq          %rdi, %rbx
	0x48, 0x0f, 0xcb, //0x00000171 bswapq       %rbx
	0x48, 0x8b, 0x7d, 0xc8, //0x00000174 movq         $-56(%rbp), %rdi
	0x48, 0x89, 0x1f, //0x00000178 movq         %rbx, (%rdi)
	0x49, 0x83, 0xc6, 0x08, //0x0000017b addq         $8, %r14
	0x48, 0x83, 0xc7, 0x06, //0x0000017f addq         $6, %rdi
	0xe9, 0xe8, 0x06, 0x00, 0x00, //0x00000183 jmp          LBB0_156
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000188 .p2align 4, 0x90
	//0x00000190 LBB0_6
	0x4d, 0x39, 0xce, //0x00000190 cmpq         %r9, %r14
	0x0f, 0x83, 0xcc, 0x00, 0x00, 0x00, //0x00000193 jae          LBB0_18
	0x4d, 0x89, 0xf5, //0x00000199 movq         %r14, %r13
	0xf6, 0x45, 0xc4, 0x08, //0x0000019c testb        $8, $-60(%rbp)
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x000001a0 je           LBB0_9
	0xe9, 0xd8, 0x00, 0x00, 0x00, //0x000001a6 jmp          LBB0_21
	0x90, 0x90, 0x90, 0x90, 0x90, //0x000001ab .p2align 4, 0x90
	//0x000001b0 LBB0_8
	0x49, 0x83, 0xc5, 0x01, //0x000001b0 addq         $1, %r13
	0x4d, 0x39, 0xcd, //0x000001b4 cmpq         %r9, %r13
	0x0f, 0x83, 0xed, 0x01, 0x00, 0x00, //0x000001b7 jae          LBB0_37
	//0x000001bd LBB0_9
	0x41, 0x0f, 0xb6, 0x7d, 0x00, //0x000001bd movzbl       (%r13), %edi
	0x48, 0x83, 0xff, 0x0d, //0x000001c2 cmpq         $13, %rdi
	0x0f, 0x84, 0xe4, 0xff, 0xff, 0xff, //0x000001c6 je           LBB0_8
	0x40, 0x80, 0xff, 0x0a, //0x000001cc cmpb         $10, %dil
	0x0f, 0x84, 0xda, 0xff, 0xff, 0xff, //0x000001d0 je           LBB0_8
	0x48, 0x8b, 0x45, 0xd0, //0x000001d6 movq         $-48(%rbp), %rax
	0x0f, 0xb6, 0x1c, 0x38, //0x000001da movzbl       (%rax,%rdi), %ebx
	0x49, 0x83, 0xc5, 0x01, //0x000001de addq         $1, %r13
	0x81, 0xfb, 0xff, 0x00, 0x00, 0x00, //0x000001e2 cmpl         $255, %ebx
	0x0f, 0x84, 0xea, 0x02, 0x00, 0x00, //0x000001e8 je           LBB0_61
	0xbe, 0x01, 0x00, 0x00, 0x00, //0x000001ee movl         $1, %esi
	0x4d, 0x39, 0xcd, //0x000001f3 cmpq         %r9, %r13
	0x0f, 0x82, 0x21, 0x00, 0x00, 0x00, //0x000001f6 jb           LBB0_14
	0xe9, 0x66, 0x02, 0x00, 0x00, //0x000001fc jmp          LBB0_51
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000201 .p2align 4, 0x90
	//0x00000210 LBB0_13
	0x49, 0x83, 0xc5, 0x01, //0x00000210 addq         $1, %r13
	0x4d, 0x39, 0xcd, //0x00000214 cmpq         %r9, %r13
	0x0f, 0x83, 0x9d, 0x04, 0x00, 0x00, //0x00000217 jae          LBB0_94
	//0x0000021d LBB0_14
	0x41, 0x0f, 0xb6, 0x7d, 0x00, //0x0000021d movzbl       (%r13), %edi
	0x48, 0x83, 0xff, 0x0d, //0x00000222 cmpq         $13, %rdi
	0x0f, 0x84, 0xe4, 0xff, 0xff, 0xff, //0x00000226 je           LBB0_13
	0x40, 0x80, 0xff, 0x0a, //0x0000022c cmpb         $10, %dil
	0x0f, 0x84, 0xda, 0xff, 0xff, 0xff, //0x00000230 je           LBB0_13
	0x48, 0x8b, 0x45, 0xd0, //0x00000236 movq         $-48(%rbp), %rax
	0x0f, 0xb6, 0x04, 0x38, //0x0000023a movzbl       (%rax,%rdi), %eax
	0x49, 0x83, 0xc5, 0x01, //0x0000023e addq         $1, %r13
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00000242 cmpl         $255, %eax
	0x0f, 0x84, 0x1d, 0x08, 0x00, 0x00, //0x00000247 je           LBB0_147
	0xc1, 0xe3, 0x06, //0x0000024d shll         $6, %ebx
	0x09, 0xc3, //0x00000250 orl          %eax, %ebx
	0xbe, 0x02, 0x00, 0x00, 0x00, //0x00000252 movl         $2, %esi
	0x4d, 0x39, 0xcd, //0x00000257 cmpq         %r9, %r13
	0x0f, 0x82, 0x82, 0x01, 0x00, 0x00, //0x0000025a jb           LBB0_41
	0xe9, 0x02, 0x02, 0x00, 0x00, //0x00000260 jmp          LBB0_51
	//0x00000265 LBB0_18
	0x48, 0x8b, 0x7d, 0xc8, //0x00000265 movq         $-56(%rbp), %rdi
	0xe9, 0x02, 0x06, 0x00, 0x00, //0x00000269 jmp          LBB0_156
	//0x0000026e LBB0_35
	0x80, 0xf9, 0x6e, //0x0000026e cmpb         $110, %cl
	0x0f, 0x85, 0xbb, 0x01, 0x00, 0x00, //0x00000271 jne          LBB0_46
	//0x00000277 LBB0_19
	0x49, 0x89, 0xc5, //0x00000277 movq         %rax, %r13
	//0x0000027a LBB0_20
	0x4d, 0x39, 0xcd, //0x0000027a cmpq         %r9, %r13
	0x0f, 0x83, 0x27, 0x01, 0x00, 0x00, //0x0000027d jae          LBB0_37
	//0x00000283 LBB0_21
	0x49, 0x8d, 0x4d, 0x01, //0x00000283 leaq         $1(%r13), %rcx
	0x41, 0x0f, 0xb6, 0x45, 0x00, //0x00000287 movzbl       (%r13), %eax
	0x3c, 0x5c, //0x0000028c cmpb         $92, %al
	0x0f, 0x85, 0xfc, 0x00, 0x00, 0x00, //0x0000028e jne          LBB0_33
	0x49, 0x8d, 0x45, 0x02, //0x00000294 leaq         $2(%r13), %rax
	0x40, 0xb7, 0xff, //0x00000298 movb         $-1, %dil
	0x4c, 0x39, 0xc8, //0x0000029b cmpq         %r9, %rax
	0x0f, 0x87, 0x86, 0x01, 0x00, 0x00, //0x0000029e ja           LBB0_45
	0x0f, 0xb6, 0x09, //0x000002a4 movzbl       (%rcx), %ecx
	0x80, 0xf9, 0x71, //0x000002a7 cmpb         $113, %cl
	0x0f, 0x8e, 0xbe, 0xff, 0xff, 0xff, //0x000002aa jle          LBB0_35
	0x80, 0xf9, 0x72, //0x000002b0 cmpb         $114, %cl
	0x0f, 0x84, 0xbe, 0xff, 0xff, 0xff, //0x000002b3 je           LBB0_19
	0x80, 0xf9, 0x75, //0x000002b9 cmpb         $117, %cl
	0x0f, 0x85, 0x7c, 0x01, 0x00, 0x00, //0x000002bc jne          LBB0_48
	0x4c, 0x89, 0xc9, //0x000002c2 movq         %r9, %rcx
	0x48, 0x29, 0xc1, //0x000002c5 subq         %rax, %rcx
	0x48, 0x83, 0xf9, 0x04, //0x000002c8 cmpq         $4, %rcx
	0x0f, 0x8c, 0x6c, 0x01, 0x00, 0x00, //0x000002cc jl           LBB0_48
	0x8b, 0x08, //0x000002d2 movl         (%rax), %ecx
	0x89, 0xca, //0x000002d4 movl         %ecx, %edx
	0xf7, 0xd2, //0x000002d6 notl         %edx
	0x8d, 0xb1, 0xd0, 0xcf, 0xcf, 0xcf, //0x000002d8 leal         $-808464432(%rcx), %esi
	0x81, 0xe2, 0x80, 0x80, 0x80, 0x80, //0x000002de andl         $-2139062144, %edx
	0x85, 0xf2, //0x000002e4 testl        %esi, %edx
	0x0f, 0x85, 0x52, 0x01, 0x00, 0x00, //0x000002e6 jne          LBB0_48
	0x8d, 0xb1, 0x19, 0x19, 0x19, 0x19, //0x000002ec leal         $421075225(%rcx), %esi
	0x09, 0xce, //0x000002f2 orl          %ecx, %esi
	0xf7, 0xc6, 0x80, 0x80, 0x80, 0x80, //0x000002f4 testl        $-2139062144, %esi
	0x0f, 0x85, 0x3e, 0x01, 0x00, 0x00, //0x000002fa jne          LBB0_48
	0x89, 0xce, //0x00000300 movl         %ecx, %esi
	0x81, 0xe6, 0x7f, 0x7f, 0x7f, 0x7f, //0x00000302 andl         $2139062143, %esi
	0xbb, 0xc0, 0xc0, 0xc0, 0xc0, //0x00000308 movl         $-1061109568, %ebx
	0x29, 0xf3, //0x0000030d subl         %esi, %ebx
	0x44, 0x8d, 0x86, 0x46, 0x46, 0x46, 0x46, //0x0000030f leal         $1179010630(%rsi), %r8d
	0x21, 0xd3, //0x00000316 andl         %edx, %ebx
	0x44, 0x85, 0xc3, //0x00000318 testl        %r8d, %ebx
	0x0f, 0x85, 0x1d, 0x01, 0x00, 0x00, //0x0000031b jne          LBB0_48
	0xbb, 0xe0, 0xe0, 0xe0, 0xe0, //0x00000321 movl         $-522133280, %ebx
	0x29, 0xf3, //0x00000326 subl         %esi, %ebx
	0x81, 0xc6, 0x39, 0x39, 0x39, 0x39, //0x00000328 addl         $960051513, %esi
	0x21, 0xda, //0x0000032e andl         %ebx, %edx
	0x85, 0xf2, //0x00000330 testl        %esi, %edx
	0x0f, 0x85, 0x06, 0x01, 0x00, 0x00, //0x00000332 jne          LBB0_48
	0x0f, 0xc9, //0x00000338 bswapl       %ecx
	0x89, 0xc8, //0x0000033a movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x0000033c shrl         $4, %eax
	0xf7, 0xd0, //0x0000033f notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x00000341 andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x00000346 leal         (%rax,%rax,8), %eax
	0x81, 0xe1, 0x0f, 0x0f, 0x0f, 0x0f, //0x00000349 andl         $252645135, %ecx
	0x01, 0xc1, //0x0000034f addl         %eax, %ecx
	0x89, 0xc8, //0x00000351 movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x00000353 shrl         $4, %eax
	0x09, 0xc8, //0x00000356 orl          %ecx, %eax
	0x89, 0xc1, //0x00000358 movl         %eax, %ecx
	0xc1, 0xe9, 0x08, //0x0000035a shrl         $8, %ecx
	0x81, 0xe1, 0x00, 0xff, 0x00, 0x00, //0x0000035d andl         $65280, %ecx
	0x89, 0xc2, //0x00000363 movl         %eax, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x00000365 andl         $128, %edx
	0x49, 0x83, 0xc5, 0x06, //0x0000036b addq         $6, %r13
	0x09, 0xca, //0x0000036f orl          %ecx, %edx
	0x0f, 0x85, 0xca, 0x00, 0x00, 0x00, //0x00000371 jne          LBB0_49
	0x3c, 0x0d, //0x00000377 cmpb         $13, %al
	0x0f, 0x85, 0x1c, 0x00, 0x00, 0x00, //0x00000379 jne          LBB0_34
	0xe9, 0xf6, 0xfe, 0xff, 0xff, //0x0000037f jmp          LBB0_20
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000384 .p2align 4, 0x90
	//0x00000390 LBB0_33
	0x49, 0x89, 0xcd, //0x00000390 movq         %rcx, %r13
	0x3c, 0x0d, //0x00000393 cmpb         $13, %al
	0x0f, 0x84, 0xdf, 0xfe, 0xff, 0xff, //0x00000395 je           LBB0_20
	//0x0000039b LBB0_34
	0x89, 0xc7, //0x0000039b movl         %eax, %edi
	0x3c, 0x0a, //0x0000039d cmpb         $10, %al
	0x0f, 0x84, 0xd5, 0xfe, 0xff, 0xff, //0x0000039f je           LBB0_20
	0xe9, 0x97, 0x00, 0x00, 0x00, //0x000003a5 jmp          LBB0_49
	//0x000003aa LBB0_37
	0x31, 0xf6, //0x000003aa xorl         %esi, %esi
	0x31, 0xdb, //0x000003ac xorl         %ebx, %ebx
	//0x000003ae LBB0_38
	0x85, 0xf6, //0x000003ae testl        %esi, %esi
	0x48, 0x8b, 0x7d, 0xc8, //0x000003b0 movq         $-56(%rbp), %rdi
	0x0f, 0x85, 0xad, 0x00, 0x00, 0x00, //0x000003b4 jne          LBB0_51
	0x4d, 0x89, 0xee, //0x000003ba movq         %r13, %r14
	0xe9, 0xae, 0x04, 0x00, 0x00, //0x000003bd jmp          LBB0_156
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000003c2 .p2align 4, 0x90
	//0x000003d0 LBB0_40
	0x49, 0x83, 0xc5, 0x01, //0x000003d0 addq         $1, %r13
	0xbe, 0x02, 0x00, 0x00, 0x00, //0x000003d4 movl         $2, %esi
	0x4d, 0x39, 0xcd, //0x000003d9 cmpq         %r9, %r13
	0x0f, 0x83, 0xcc, 0xff, 0xff, 0xff, //0x000003dc jae          LBB0_38
	//0x000003e2 LBB0_41
	0x41, 0x0f, 0xb6, 0x7d, 0x00, //0x000003e2 movzbl       (%r13), %edi
	0x48, 0x83, 0xff, 0x0d, //0x000003e7 cmpq         $13, %rdi
	0x0f, 0x84, 0xdf, 0xff, 0xff, 0xff, //0x000003eb je           LBB0_40
	0x40, 0x80, 0xff, 0x0a, //0x000003f1 cmpb         $10, %dil
	0x0f, 0x84, 0xd5, 0xff, 0xff, 0xff, //0x000003f5 je           LBB0_40
	0x48, 0x8b, 0x45, 0xd0, //0x000003fb movq         $-48(%rbp), %rax
	0x0f, 0xb6, 0x04, 0x38, //0x000003ff movzbl       (%rax,%rdi), %eax
	0x49, 0x83, 0xc5, 0x01, //0x00000403 addq         $1, %r13
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00000407 cmpl         $255, %eax
	0x0f, 0x84, 0x18, 0x09, 0x00, 0x00, //0x0000040c je           LBB0_182
	0xc1, 0xe3, 0x06, //0x00000412 shll         $6, %ebx
	0x09, 0xc3, //0x00000415 orl          %eax, %ebx
	0xbe, 0x03, 0x00, 0x00, 0x00, //0x00000417 movl         $3, %esi
	0x4d, 0x39, 0xcd, //0x0000041c cmpq         %r9, %r13
	0x0f, 0x82, 0x7e, 0x04, 0x00, 0x00, //0x0000041f jb           LBB0_119
	0xe9, 0x3d, 0x00, 0x00, 0x00, //0x00000425 jmp          LBB0_51
	//0x0000042a LBB0_45
	0x49, 0x89, 0xcd, //0x0000042a movq         %rcx, %r13
	0xe9, 0x0f, 0x00, 0x00, 0x00, //0x0000042d jmp          LBB0_49
	//0x00000432 LBB0_46
	0x80, 0xf9, 0x2f, //0x00000432 cmpb         $47, %cl
	0x0f, 0x85, 0x03, 0x00, 0x00, 0x00, //0x00000435 jne          LBB0_48
	0x40, 0xb7, 0x2f, //0x0000043b movb         $47, %dil
	//0x0000043e LBB0_48
	0x49, 0x89, 0xc5, //0x0000043e movq         %rax, %r13
	//0x00000441 LBB0_49
	0x40, 0x0f, 0xb6, 0xc7, //0x00000441 movzbl       %dil, %eax
	0x48, 0x8b, 0x4d, 0xd0, //0x00000445 movq         $-48(%rbp), %rcx
	0x0f, 0xb6, 0x1c, 0x01, //0x00000449 movzbl       (%rcx,%rax), %ebx
	0x81, 0xfb, 0xff, 0x00, 0x00, 0x00, //0x0000044d cmpl         $255, %ebx
	0x0f, 0x84, 0x7f, 0x00, 0x00, 0x00, //0x00000453 je           LBB0_61
	0xbe, 0x01, 0x00, 0x00, 0x00, //0x00000459 movl         $1, %esi
	0x4d, 0x39, 0xcd, //0x0000045e cmpq         %r9, %r13
	0x0f, 0x82, 0x2d, 0x01, 0x00, 0x00, //0x00000461 jb           LBB0_77
	//0x00000467 LBB0_51
	0xf6, 0x45, 0xc4, 0x02, //0x00000467 testb        $2, $-60(%rbp)
	0x0f, 0x94, 0xc0, //0x0000046b sete         %al
	0x83, 0xfe, 0x01, //0x0000046e cmpl         $1, %esi
	0x0f, 0x94, 0xc1, //0x00000471 sete         %cl
	0x4d, 0x39, 0xcd, //0x00000474 cmpq         %r9, %r13
	0x0f, 0x82, 0x11, 0x00, 0x00, 0x00, //0x00000477 jb           LBB0_54
	0x83, 0xfe, 0x04, //0x0000047d cmpl         $4, %esi
	0x0f, 0x84, 0x08, 0x00, 0x00, 0x00, //0x00000480 je           LBB0_54
	0x08, 0xc8, //0x00000486 orb          %cl, %al
	0x0f, 0x85, 0xc0, 0x03, 0x00, 0x00, //0x00000488 jne          LBB0_155
	//0x0000048e LBB0_54
	0xb0, 0x04, //0x0000048e movb         $4, %al
	0x40, 0x28, 0xf0, //0x00000490 subb         %sil, %al
	0x0f, 0xb6, 0xc0, //0x00000493 movzbl       %al, %eax
	0x01, 0xc0, //0x00000496 addl         %eax, %eax
	0x8d, 0x0c, 0x40, //0x00000498 leal         (%rax,%rax,2), %ecx
	0xd3, 0xe3, //0x0000049b shll         %cl, %ebx
	0x83, 0xfe, 0x02, //0x0000049d cmpl         $2, %esi
	0x48, 0x8b, 0x7d, 0xc8, //0x000004a0 movq         $-56(%rbp), %rdi
	0x0f, 0x84, 0x18, 0x00, 0x00, 0x00, //0x000004a4 je           LBB0_59
	0x83, 0xfe, 0x03, //0x000004aa cmpl         $3, %esi
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x000004ad je           LBB0_58
	0x83, 0xfe, 0x04, //0x000004b3 cmpl         $4, %esi
	0x0f, 0x85, 0x0b, 0x00, 0x00, 0x00, //0x000004b6 jne          LBB0_60
	0x88, 0x5f, 0x02, //0x000004bc movb         %bl, $2(%rdi)
	//0x000004bf LBB0_58
	0x88, 0x7f, 0x01, //0x000004bf movb         %bh, $1(%rdi)
	//0x000004c2 LBB0_59
	0xc1, 0xeb, 0x10, //0x000004c2 shrl         $16, %ebx
	0x88, 0x1f, //0x000004c5 movb         %bl, (%rdi)
	//0x000004c7 LBB0_60
	0x89, 0xf0, //0x000004c7 movl         %esi, %eax
	0x48, 0x01, 0xc7, //0x000004c9 addq         %rax, %rdi
	0x48, 0x83, 0xc7, 0xff, //0x000004cc addq         $-1, %rdi
	0x4d, 0x89, 0xee, //0x000004d0 movq         %r13, %r14
	0xe9, 0x98, 0x03, 0x00, 0x00, //0x000004d3 jmp          LBB0_156
	//0x000004d8 LBB0_61
	0x31, 0xf6, //0x000004d8 xorl         %esi, %esi
	0x31, 0xdb, //0x000004da xorl         %ebx, %ebx
	//0x000004dc LBB0_62
	0xf6, 0x45, 0xc4, 0x02, //0x000004dc testb        $2, $-60(%rbp)
	0x0f, 0x85, 0x68, 0x03, 0x00, 0x00, //0x000004e0 jne          LBB0_155
	0x40, 0x80, 0xff, 0x3d, //0x000004e6 cmpb         $61, %dil
	0x0f, 0x85, 0x5e, 0x03, 0x00, 0x00, //0x000004ea jne          LBB0_155
	0x83, 0xfe, 0x02, //0x000004f0 cmpl         $2, %esi
	0x0f, 0x82, 0x55, 0x03, 0x00, 0x00, //0x000004f3 jb           LBB0_155
	0x41, 0xbb, 0x05, 0x00, 0x00, 0x00, //0x000004f9 movl         $5, %r11d
	0x41, 0x29, 0xf3, //0x000004ff subl         %esi, %r11d
	0xf6, 0x45, 0xc4, 0x08, //0x00000502 testb        $8, $-60(%rbp)
	0x0f, 0x85, 0xb8, 0x01, 0x00, 0x00, //0x00000506 jne          LBB0_95
	0x4d, 0x39, 0xcd, //0x0000050c cmpq         %r9, %r13
	0x0f, 0x83, 0x79, 0xff, 0xff, 0xff, //0x0000050f jae          LBB0_54
	0x49, 0x8d, 0x4d, 0x01, //0x00000515 leaq         $1(%r13), %rcx
	0x48, 0x8b, 0x45, 0x80, //0x00000519 movq         $-128(%rbp), %rax
	0x4c, 0x29, 0xe8, //0x0000051d subq         %r13, %rax
	0x49, 0x83, 0xc5, 0x02, //0x00000520 addq         $2, %r13
	0x4c, 0x89, 0xea, //0x00000524 movq         %r13, %rdx
	0x49, 0x89, 0xcd, //0x00000527 movq         %rcx, %r13
	0xe9, 0x13, 0x00, 0x00, 0x00, //0x0000052a jmp          LBB0_69
	0x90, //0x0000052f .p2align 4, 0x90
	//0x00000530 LBB0_68
	0x49, 0x83, 0xc5, 0x01, //0x00000530 addq         $1, %r13
	0x48, 0x83, 0xc2, 0x01, //0x00000534 addq         $1, %rdx
	0x48, 0x83, 0xc0, 0xff, //0x00000538 addq         $-1, %rax
	0x0f, 0x83, 0x32, 0x05, 0x00, 0x00, //0x0000053c jae          LBB0_148
	//0x00000542 LBB0_69
	0x41, 0x0f, 0xb6, 0x4d, 0xff, //0x00000542 movzbl       $-1(%r13), %ecx
	0x80, 0xf9, 0x0a, //0x00000547 cmpb         $10, %cl
	0x0f, 0x84, 0xe0, 0xff, 0xff, 0xff, //0x0000054a je           LBB0_68
	0x80, 0xf9, 0x0d, //0x00000550 cmpb         $13, %cl
	0x0f, 0x84, 0xd7, 0xff, 0xff, 0xff, //0x00000553 je           LBB0_68
	0x80, 0xf9, 0x3d, //0x00000559 cmpb         $61, %cl
	0x0f, 0x85, 0xec, 0x02, 0x00, 0x00, //0x0000055c jne          LBB0_155
	0x41, 0x83, 0xfb, 0x02, //0x00000562 cmpl         $2, %r11d
	0x0f, 0x84, 0xe2, 0x02, 0x00, 0x00, //0x00000566 je           LBB0_155
	0x4d, 0x39, 0xcd, //0x0000056c cmpq         %r9, %r13
	0x0f, 0x83, 0x19, 0xff, 0xff, 0xff, //0x0000056f jae          LBB0_54
	0x49, 0x01, 0xc5, //0x00000575 addq         %rax, %r13
	0x31, 0xc9, //0x00000578 xorl         %ecx, %ecx
	0xe9, 0xb4, 0x02, 0x00, 0x00, //0x0000057a jmp          LBB0_115
	//0x0000057f LBB0_91
	0x80, 0xf9, 0x6e, //0x0000057f cmpb         $110, %cl
	0x0f, 0x85, 0x60, 0x03, 0x00, 0x00, //0x00000582 jne          LBB0_124
	//0x00000588 LBB0_75
	0x49, 0x89, 0xc5, //0x00000588 movq         %rax, %r13
	//0x0000058b LBB0_76
	0x4d, 0x39, 0xcd, //0x0000058b cmpq         %r9, %r13
	0x0f, 0x83, 0x26, 0x01, 0x00, 0x00, //0x0000058e jae          LBB0_94
	//0x00000594 LBB0_77
	0x49, 0x8d, 0x4d, 0x01, //0x00000594 leaq         $1(%r13), %rcx
	0x41, 0x0f, 0xb6, 0x45, 0x00, //0x00000598 movzbl       (%r13), %eax
	0x3c, 0x5c, //0x0000059d cmpb         $92, %al
	0x0f, 0x85, 0xfb, 0x00, 0x00, 0x00, //0x0000059f jne          LBB0_89
	0x49, 0x8d, 0x45, 0x02, //0x000005a5 leaq         $2(%r13), %rax
	0x40, 0xb7, 0xff, //0x000005a9 movb         $-1, %dil
	0x4c, 0x39, 0xc8, //0x000005ac cmpq         %r9, %rax
	0x0f, 0x87, 0xd4, 0x02, 0x00, 0x00, //0x000005af ja           LBB0_117
	0x0f, 0xb6, 0x09, //0x000005b5 movzbl       (%rcx), %ecx
	0x80, 0xf9, 0x71, //0x000005b8 cmpb         $113, %cl
	0x0f, 0x8e, 0xbe, 0xff, 0xff, 0xff, //0x000005bb jle          LBB0_91
	0x80, 0xf9, 0x72, //0x000005c1 cmpb         $114, %cl
	0x0f, 0x84, 0xbe, 0xff, 0xff, 0xff, //0x000005c4 je           LBB0_75
	0x80, 0xf9, 0x75, //0x000005ca cmpb         $117, %cl
	0x0f, 0x85, 0x21, 0x03, 0x00, 0x00, //0x000005cd jne          LBB0_126
	0x4c, 0x89, 0xc9, //0x000005d3 movq         %r9, %rcx
	0x48, 0x29, 0xc1, //0x000005d6 subq         %rax, %rcx
	0x48, 0x83, 0xf9, 0x04, //0x000005d9 cmpq         $4, %rcx
	0x0f, 0x8c, 0x11, 0x03, 0x00, 0x00, //0x000005dd jl           LBB0_126
	0x8b, 0x08, //0x000005e3 movl         (%rax), %ecx
	0x89, 0xca, //0x000005e5 movl         %ecx, %edx
	0xf7, 0xd2, //0x000005e7 notl         %edx
	0x8d, 0xb1, 0xd0, 0xcf, 0xcf, 0xcf, //0x000005e9 leal         $-808464432(%rcx), %esi
	0x81, 0xe2, 0x80, 0x80, 0x80, 0x80, //0x000005ef andl         $-2139062144, %edx
	0x85, 0xf2, //0x000005f5 testl        %esi, %edx
	0x0f, 0x85, 0xf7, 0x02, 0x00, 0x00, //0x000005f7 jne          LBB0_126
	0x8d, 0xb1, 0x19, 0x19, 0x19, 0x19, //0x000005fd leal         $421075225(%rcx), %esi
	0x09, 0xce, //0x00000603 orl          %ecx, %esi
	0xf7, 0xc6, 0x80, 0x80, 0x80, 0x80, //0x00000605 testl        $-2139062144, %esi
	0x0f, 0x85, 0xe3, 0x02, 0x00, 0x00, //0x0000060b jne          LBB0_126
	0x41, 0x89, 0xda, //0x00000611 movl         %ebx, %r10d
	0x89, 0xce, //0x00000614 movl         %ecx, %esi
	0x81, 0xe6, 0x7f, 0x7f, 0x7f, 0x7f, //0x00000616 andl         $2139062143, %esi
	0xbb, 0xc0, 0xc0, 0xc0, 0xc0, //0x0000061c movl         $-1061109568, %ebx
	0x29, 0xf3, //0x00000621 subl         %esi, %ebx
	0x44, 0x8d, 0x86, 0x46, 0x46, 0x46, 0x46, //0x00000623 leal         $1179010630(%rsi), %r8d
	0x21, 0xd3, //0x0000062a andl         %edx, %ebx
	0x44, 0x85, 0xc3, //0x0000062c testl        %r8d, %ebx
	0x0f, 0x85, 0xa8, 0x02, 0x00, 0x00, //0x0000062f jne          LBB0_123
	0xbb, 0xe0, 0xe0, 0xe0, 0xe0, //0x00000635 movl         $-522133280, %ebx
	0x29, 0xf3, //0x0000063a subl         %esi, %ebx
	0x81, 0xc6, 0x39, 0x39, 0x39, 0x39, //0x0000063c addl         $960051513, %esi
	0x21, 0xda, //0x00000642 andl         %ebx, %edx
	0x85, 0xf2, //0x00000644 testl        %esi, %edx
	0x0f, 0x85, 0x91, 0x02, 0x00, 0x00, //0x00000646 jne          LBB0_123
	0x0f, 0xc9, //0x0000064c bswapl       %ecx
	0x89, 0xc8, //0x0000064e movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x00000650 shrl         $4, %eax
	0xf7, 0xd0, //0x00000653 notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x00000655 andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x0000065a leal         (%rax,%rax,8), %eax
	0x81, 0xe1, 0x0f, 0x0f, 0x0f, 0x0f, //0x0000065d andl         $252645135, %ecx
	0x01, 0xc1, //0x00000663 addl         %eax, %ecx
	0x89, 0xc8, //0x00000665 movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x00000667 shrl         $4, %eax
	0x09, 0xc8, //0x0000066a orl          %ecx, %eax
	0x89, 0xc1, //0x0000066c movl         %eax, %ecx
	0xc1, 0xe9, 0x08, //0x0000066e shrl         $8, %ecx
	0x81, 0xe1, 0x00, 0xff, 0x00, 0x00, //0x00000671 andl         $65280, %ecx
	0x89, 0xc2, //0x00000677 movl         %eax, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x00000679 andl         $128, %edx
	0x49, 0x83, 0xc5, 0x06, //0x0000067f addq         $6, %r13
	0x09, 0xca, //0x00000683 orl          %ecx, %edx
	0x44, 0x89, 0xd3, //0x00000685 movl         %r10d, %ebx
	0x0f, 0x85, 0x69, 0x02, 0x00, 0x00, //0x00000688 jne          LBB0_127
	0x3c, 0x0d, //0x0000068e cmpb         $13, %al
	0x0f, 0x85, 0x15, 0x00, 0x00, 0x00, //0x00000690 jne          LBB0_90
	0xe9, 0xf0, 0xfe, 0xff, 0xff, //0x00000696 jmp          LBB0_76
	0x90, 0x90, 0x90, 0x90, 0x90, //0x0000069b .p2align 4, 0x90
	//0x000006a0 LBB0_89
	0x49, 0x89, 0xcd, //0x000006a0 movq         %rcx, %r13
	0x3c, 0x0d, //0x000006a3 cmpb         $13, %al
	0x0f, 0x84, 0xe0, 0xfe, 0xff, 0xff, //0x000006a5 je           LBB0_76
	//0x000006ab LBB0_90
	0x89, 0xc7, //0x000006ab movl         %eax, %edi
	0x3c, 0x0a, //0x000006ad cmpb         $10, %al
	0x0f, 0x84, 0xd6, 0xfe, 0xff, 0xff, //0x000006af je           LBB0_76
	0xe9, 0x3d, 0x02, 0x00, 0x00, //0x000006b5 jmp          LBB0_127
	//0x000006ba LBB0_94
	0xbe, 0x01, 0x00, 0x00, 0x00, //0x000006ba movl         $1, %esi
	0xe9, 0xea, 0xfc, 0xff, 0xff, //0x000006bf jmp          LBB0_38
	//0x000006c4 LBB0_95
	0x4d, 0x39, 0xcd, //0x000006c4 cmpq         %r9, %r13
	0x0f, 0x83, 0xc1, 0xfd, 0xff, 0xff, //0x000006c7 jae          LBB0_54
	0x4c, 0x89, 0xef, //0x000006cd movq         %r13, %rdi
	0x41, 0x89, 0xda, //0x000006d0 movl         %ebx, %r10d
	0xe9, 0x1d, 0x00, 0x00, 0x00, //0x000006d3 jmp          LBB0_99
	//0x000006d8 LBB0_113
	0x48, 0x89, 0xd7, //0x000006d8 movq         %rdx, %rdi
	0x4c, 0x39, 0xcf, //0x000006db cmpq         %r9, %rdi
	0x0f, 0x82, 0x11, 0x00, 0x00, 0x00, //0x000006de jb           LBB0_99
	0xe9, 0x93, 0x03, 0x00, 0x00, //0x000006e4 jmp          LBB0_150
	//0x000006e9 LBB0_97
	0x4c, 0x89, 0xef, //0x000006e9 movq         %r13, %rdi
	0x4c, 0x39, 0xcf, //0x000006ec cmpq         %r9, %rdi
	0x0f, 0x83, 0x87, 0x03, 0x00, 0x00, //0x000006ef jae          LBB0_150
	//0x000006f5 LBB0_99
	0x48, 0x8d, 0x57, 0x01, //0x000006f5 leaq         $1(%rdi), %rdx
	0x0f, 0xb6, 0x0f, //0x000006f9 movzbl       (%rdi), %ecx
	0x80, 0xf9, 0x5c, //0x000006fc cmpb         $92, %cl
	0x0f, 0x85, 0xe7, 0x00, 0x00, 0x00, //0x000006ff jne          LBB0_110
	0x4c, 0x8d, 0x6f, 0x02, //0x00000705 leaq         $2(%rdi), %r13
	0x4d, 0x39, 0xcd, //0x00000709 cmpq         %r9, %r13
	0x0f, 0x87, 0x39, 0x01, 0x00, 0x00, //0x0000070c ja           LBB0_154
	0x0f, 0xb6, 0x02, //0x00000712 movzbl       (%rdx), %eax
	0x3c, 0x6e, //0x00000715 cmpb         $110, %al
	0x0f, 0x84, 0xcc, 0xff, 0xff, 0xff, //0x00000717 je           LBB0_97
	0x3c, 0x72, //0x0000071d cmpb         $114, %al
	0x0f, 0x84, 0xc4, 0xff, 0xff, 0xff, //0x0000071f je           LBB0_97
	0x3c, 0x75, //0x00000725 cmpb         $117, %al
	0x0f, 0x85, 0x21, 0x01, 0x00, 0x00, //0x00000727 jne          LBB0_155
	0x4c, 0x89, 0xc8, //0x0000072d movq         %r9, %rax
	0x4c, 0x29, 0xe8, //0x00000730 subq         %r13, %rax
	0x48, 0x83, 0xf8, 0x04, //0x00000733 cmpq         $4, %rax
	0x0f, 0x8c, 0x11, 0x01, 0x00, 0x00, //0x00000737 jl           LBB0_155
	0x41, 0x8b, 0x45, 0x00, //0x0000073d movl         (%r13), %eax
	0x89, 0xc1, //0x00000741 movl         %eax, %ecx
	0xf7, 0xd1, //0x00000743 notl         %ecx
	0x8d, 0x90, 0xd0, 0xcf, 0xcf, 0xcf, //0x00000745 leal         $-808464432(%rax), %edx
	0x81, 0xe1, 0x80, 0x80, 0x80, 0x80, //0x0000074b andl         $-2139062144, %ecx
	0x85, 0xd1, //0x00000751 testl        %edx, %ecx
	0x0f, 0x85, 0xf5, 0x00, 0x00, 0x00, //0x00000753 jne          LBB0_155
	0x8d, 0x90, 0x19, 0x19, 0x19, 0x19, //0x00000759 leal         $421075225(%rax), %edx
	0x09, 0xc2, //0x0000075f orl          %eax, %edx
	0xf7, 0xc2, 0x80, 0x80, 0x80, 0x80, //0x00000761 testl        $-2139062144, %edx
	0x0f, 0x85, 0xe1, 0x00, 0x00, 0x00, //0x00000767 jne          LBB0_155
	0x89, 0xc2, //0x0000076d movl         %eax, %edx
	0x81, 0xe2, 0x7f, 0x7f, 0x7f, 0x7f, //0x0000076f andl         $2139062143, %edx
	0xbb, 0xc0, 0xc0, 0xc0, 0xc0, //0x00000775 movl         $-1061109568, %ebx
	0x29, 0xd3, //0x0000077a subl         %edx, %ebx
	0x44, 0x8d, 0x82, 0x46, 0x46, 0x46, 0x46, //0x0000077c leal         $1179010630(%rdx), %r8d
	0x21, 0xcb, //0x00000783 andl         %ecx, %ebx
	0x44, 0x85, 0xc3, //0x00000785 testl        %r8d, %ebx
	0x0f, 0x85, 0xc0, 0x00, 0x00, 0x00, //0x00000788 jne          LBB0_155
	0xbb, 0xe0, 0xe0, 0xe0, 0xe0, //0x0000078e movl         $-522133280, %ebx
	0x29, 0xd3, //0x00000793 subl         %edx, %ebx
	0x81, 0xc2, 0x39, 0x39, 0x39, 0x39, //0x00000795 addl         $960051513, %edx
	0x21, 0xd9, //0x0000079b andl         %ebx, %ecx
	0x85, 0xd1, //0x0000079d testl        %edx, %ecx
	0x0f, 0x85, 0xa9, 0x00, 0x00, 0x00, //0x0000079f jne          LBB0_155
	0x44, 0x89, 0xd3, //0x000007a5 movl         %r10d, %ebx
	0x0f, 0xc8, //0x000007a8 bswapl       %eax
	0x89, 0xc1, //0x000007aa movl         %eax, %ecx
	0xc1, 0xe9, 0x04, //0x000007ac shrl         $4, %ecx
	0xf7, 0xd1, //0x000007af notl         %ecx
	0x81, 0xe1, 0x01, 0x01, 0x01, 0x01, //0x000007b1 andl         $16843009, %ecx
	0x8d, 0x0c, 0xc9, //0x000007b7 leal         (%rcx,%rcx,8), %ecx
	0x25, 0x0f, 0x0f, 0x0f, 0x0f, //0x000007ba andl         $252645135, %eax
	0x01, 0xc8, //0x000007bf addl         %ecx, %eax
	0x89, 0xc1, //0x000007c1 movl         %eax, %ecx
	0xc1, 0xe9, 0x04, //0x000007c3 shrl         $4, %ecx
	0x09, 0xc1, //0x000007c6 orl          %eax, %ecx
	0x89, 0xc8, //0x000007c8 movl         %ecx, %eax
	0xc1, 0xe8, 0x08, //0x000007ca shrl         $8, %eax
	0x25, 0x00, 0xff, 0x00, 0x00, //0x000007cd andl         $65280, %eax
	0x89, 0xca, //0x000007d2 movl         %ecx, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x000007d4 andl         $128, %edx
	0x48, 0x83, 0xc7, 0x06, //0x000007da addq         $6, %rdi
	0x09, 0xc2, //0x000007de orl          %eax, %edx
	0x48, 0x89, 0xfa, //0x000007e0 movq         %rdi, %rdx
	0x49, 0x89, 0xfd, //0x000007e3 movq         %rdi, %r13
	0x0f, 0x85, 0x62, 0x00, 0x00, 0x00, //0x000007e6 jne          LBB0_155
	//0x000007ec LBB0_110
	0x80, 0xf9, 0x0a, //0x000007ec cmpb         $10, %cl
	0x0f, 0x84, 0xe3, 0xfe, 0xff, 0xff, //0x000007ef je           LBB0_113
	0x80, 0xf9, 0x0d, //0x000007f5 cmpb         $13, %cl
	0x0f, 0x84, 0xda, 0xfe, 0xff, 0xff, //0x000007f8 je           LBB0_113
	0x80, 0xf9, 0x3d, //0x000007fe cmpb         $61, %cl
	0x0f, 0x85, 0x44, 0x00, 0x00, 0x00, //0x00000801 jne          LBB0_154
	0x41, 0x83, 0xfb, 0x02, //0x00000807 cmpl         $2, %r11d
	0x0f, 0x84, 0x3a, 0x00, 0x00, 0x00, //0x0000080b je           LBB0_154
	0x4c, 0x39, 0xca, //0x00000811 cmpq         %r9, %rdx
	0x0f, 0x82, 0x7e, 0x02, 0x00, 0x00, //0x00000814 jb           LBB0_188
	//0x0000081a LBB0_185
	0x49, 0x89, 0xd5, //0x0000081a movq         %rdx, %r13
	0xe9, 0x6c, 0xfc, 0xff, 0xff, //0x0000081d jmp          LBB0_54
	//0x00000822 LBB0_114
	0x48, 0x83, 0xc2, 0x01, //0x00000822 addq         $1, %rdx
	0x48, 0x83, 0xc1, 0x01, //0x00000826 addq         $1, %rcx
	0x48, 0x39, 0xc8, //0x0000082a cmpq         %rcx, %rax
	0x0f, 0x84, 0x5b, 0xfc, 0xff, 0xff, //0x0000082d je           LBB0_54
	//0x00000833 LBB0_115
	0x0f, 0xb6, 0x7a, 0xff, //0x00000833 movzbl       $-1(%rdx), %edi
	0x40, 0x80, 0xff, 0x0d, //0x00000837 cmpb         $13, %dil
	0x0f, 0x84, 0xe1, 0xff, 0xff, 0xff, //0x0000083b je           LBB0_114
	0x40, 0x80, 0xff, 0x0a, //0x00000841 cmpb         $10, %dil
	0x0f, 0x84, 0xd7, 0xff, 0xff, 0xff, //0x00000845 je           LBB0_114
	//0x0000084b LBB0_154
	0x49, 0x89, 0xd5, //0x0000084b movq         %rdx, %r13
	//0x0000084e LBB0_155
	0x31, 0xc9, //0x0000084e xorl         %ecx, %ecx
	0x4d, 0x39, 0xcd, //0x00000850 cmpq         %r9, %r13
	0x0f, 0x94, 0xc1, //0x00000853 sete         %cl
	0x4c, 0x01, 0xe9, //0x00000856 addq         %r13, %rcx
	0x4c, 0x39, 0xf1, //0x00000859 cmpq         %r14, %rcx
	0x48, 0x8b, 0x7d, 0xc8, //0x0000085c movq         $-56(%rbp), %rdi
	0x0f, 0x85, 0x70, 0x1e, 0x00, 0x00, //0x00000860 jne          LBB0_643
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000866 .p2align 4, 0x90
	//0x00000870 LBB0_156
	0x4c, 0x3b, 0x75, 0x90, //0x00000870 cmpq         $-112(%rbp), %r14
	0x0f, 0x87, 0x0a, 0x05, 0x00, 0x00, //0x00000874 ja           LBB0_214
	0x48, 0x3b, 0x7d, 0x98, //0x0000087a cmpq         $-104(%rbp), %rdi
	0x0f, 0x86, 0x3c, 0xf8, 0xff, 0xff, //0x0000087e jbe          LBB0_4
	0xe9, 0xfb, 0x04, 0x00, 0x00, //0x00000884 jmp          LBB0_214
	//0x00000889 LBB0_117
	0x49, 0x89, 0xcd, //0x00000889 movq         %rcx, %r13
	0xe9, 0x66, 0x00, 0x00, 0x00, //0x0000088c jmp          LBB0_127
	//0x00000891 LBB0_118
	0x49, 0x83, 0xc5, 0x01, //0x00000891 addq         $1, %r13
	0xbe, 0x03, 0x00, 0x00, 0x00, //0x00000895 movl         $3, %esi
	0x4d, 0x39, 0xcd, //0x0000089a cmpq         %r9, %r13
	0x0f, 0x83, 0x0b, 0xfb, 0xff, 0xff, //0x0000089d jae          LBB0_38
	//0x000008a3 LBB0_119
	0x41, 0x0f, 0xb6, 0x7d, 0x00, //0x000008a3 movzbl       (%r13), %edi
	0x48, 0x83, 0xff, 0x0d, //0x000008a8 cmpq         $13, %rdi
	0x0f, 0x84, 0xdf, 0xff, 0xff, 0xff, //0x000008ac je           LBB0_118
	0x40, 0x80, 0xff, 0x0a, //0x000008b2 cmpb         $10, %dil
	0x0f, 0x84, 0xd5, 0xff, 0xff, 0xff, //0x000008b6 je           LBB0_118
	0x48, 0x8b, 0x45, 0xd0, //0x000008bc movq         $-48(%rbp), %rax
	0x0f, 0xb6, 0x04, 0x38, //0x000008c0 movzbl       (%rax,%rdi), %eax
	0x49, 0x83, 0xc5, 0x01, //0x000008c4 addq         $1, %r13
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x000008c8 cmpl         $255, %eax
	0x0f, 0x85, 0x9a, 0x04, 0x00, 0x00, //0x000008cd jne          LBB0_210
	//0x000008d3 LBB0_211
	0xbe, 0x03, 0x00, 0x00, 0x00, //0x000008d3 movl         $3, %esi
	0xe9, 0xff, 0xfb, 0xff, 0xff, //0x000008d8 jmp          LBB0_62
	//0x000008dd LBB0_123
	0x49, 0x89, 0xc5, //0x000008dd movq         %rax, %r13
	0x44, 0x89, 0xd3, //0x000008e0 movl         %r10d, %ebx
	0xe9, 0x0f, 0x00, 0x00, 0x00, //0x000008e3 jmp          LBB0_127
	//0x000008e8 LBB0_124
	0x80, 0xf9, 0x2f, //0x000008e8 cmpb         $47, %cl
	0x0f, 0x85, 0x03, 0x00, 0x00, 0x00, //0x000008eb jne          LBB0_126
	0x40, 0xb7, 0x2f, //0x000008f1 movb         $47, %dil
	//0x000008f4 LBB0_126
	0x49, 0x89, 0xc5, //0x000008f4 movq         %rax, %r13
	//0x000008f7 LBB0_127
	0x40, 0x0f, 0xb6, 0xc7, //0x000008f7 movzbl       %dil, %eax
	0x48, 0x8b, 0x4d, 0xd0, //0x000008fb movq         $-48(%rbp), %rcx
	0x0f, 0xb6, 0x04, 0x01, //0x000008ff movzbl       (%rcx,%rax), %eax
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00000903 cmpl         $255, %eax
	0x0f, 0x84, 0x5c, 0x01, 0x00, 0x00, //0x00000908 je           LBB0_147
	0xc1, 0xe3, 0x06, //0x0000090e shll         $6, %ebx
	0x09, 0xc3, //0x00000911 orl          %eax, %ebx
	0xbe, 0x02, 0x00, 0x00, 0x00, //0x00000913 movl         $2, %esi
	0x4d, 0x39, 0xcd, //0x00000918 cmpq         %r9, %r13
	0x0f, 0x82, 0x1f, 0x00, 0x00, 0x00, //0x0000091b jb           LBB0_131
	0xe9, 0x41, 0xfb, 0xff, 0xff, //0x00000921 jmp          LBB0_51
	//0x00000926 LBB0_145
	0x80, 0xf9, 0x6e, //0x00000926 cmpb         $110, %cl
	0x0f, 0x85, 0x8a, 0x02, 0x00, 0x00, //0x00000929 jne          LBB0_159
	//0x0000092f LBB0_129
	0x49, 0x89, 0xc5, //0x0000092f movq         %rax, %r13
	//0x00000932 LBB0_130
	0xbe, 0x02, 0x00, 0x00, 0x00, //0x00000932 movl         $2, %esi
	0x4d, 0x39, 0xcd, //0x00000937 cmpq         %r9, %r13
	0x0f, 0x83, 0x6e, 0xfa, 0xff, 0xff, //0x0000093a jae          LBB0_38
	//0x00000940 LBB0_131
	0x49, 0x8d, 0x4d, 0x01, //0x00000940 leaq         $1(%r13), %rcx
	0x41, 0x0f, 0xb6, 0x45, 0x00, //0x00000944 movzbl       (%r13), %eax
	0x3c, 0x5c, //0x00000949 cmpb         $92, %al
	0x0f, 0x85, 0xff, 0x00, 0x00, 0x00, //0x0000094b jne          LBB0_143
	0x49, 0x8d, 0x45, 0x02, //0x00000951 leaq         $2(%r13), %rax
	0x40, 0xb7, 0xff, //0x00000955 movb         $-1, %dil
	0x4c, 0x39, 0xc8, //0x00000958 cmpq         %r9, %rax
	0x0f, 0x87, 0x23, 0x01, 0x00, 0x00, //0x0000095b ja           LBB0_151
	0x0f, 0xb6, 0x09, //0x00000961 movzbl       (%rcx), %ecx
	0x80, 0xf9, 0x71, //0x00000964 cmpb         $113, %cl
	0x0f, 0x8e, 0xb9, 0xff, 0xff, 0xff, //0x00000967 jle          LBB0_145
	0x80, 0xf9, 0x72, //0x0000096d cmpb         $114, %cl
	0x0f, 0x84, 0xb9, 0xff, 0xff, 0xff, //0x00000970 je           LBB0_129
	0x80, 0xf9, 0x75, //0x00000976 cmpb         $117, %cl
	0x0f, 0x85, 0x46, 0x02, 0x00, 0x00, //0x00000979 jne          LBB0_161
	0x4c, 0x89, 0xc9, //0x0000097f movq         %r9, %rcx
	0x48, 0x29, 0xc1, //0x00000982 subq         %rax, %rcx
	0x48, 0x83, 0xf9, 0x04, //0x00000985 cmpq         $4, %rcx
	0x0f, 0x8c, 0x36, 0x02, 0x00, 0x00, //0x00000989 jl           LBB0_161
	0x8b, 0x08, //0x0000098f movl         (%rax), %ecx
	0x89, 0xca, //0x00000991 movl         %ecx, %edx
	0xf7, 0xd2, //0x00000993 notl         %edx
	0x8d, 0xb1, 0xd0, 0xcf, 0xcf, 0xcf, //0x00000995 leal         $-808464432(%rcx), %esi
	0x81, 0xe2, 0x80, 0x80, 0x80, 0x80, //0x0000099b andl         $-2139062144, %edx
	0x85, 0xf2, //0x000009a1 testl        %esi, %edx
	0x0f, 0x85, 0x1c, 0x02, 0x00, 0x00, //0x000009a3 jne          LBB0_161
	0x8d, 0xb1, 0x19, 0x19, 0x19, 0x19, //0x000009a9 leal         $421075225(%rcx), %esi
	0x09, 0xce, //0x000009af orl          %ecx, %esi
	0xf7, 0xc6, 0x80, 0x80, 0x80, 0x80, //0x000009b1 testl        $-2139062144, %esi
	0x0f, 0x85, 0x08, 0x02, 0x00, 0x00, //0x000009b7 jne          LBB0_161
	0x41, 0x89, 0xda, //0x000009bd movl         %ebx, %r10d
	0x89, 0xce, //0x000009c0 movl         %ecx, %esi
	0x81, 0xe6, 0x7f, 0x7f, 0x7f, 0x7f, //0x000009c2 andl         $2139062143, %esi
	0xbb, 0xc0, 0xc0, 0xc0, 0xc0, //0x000009c8 movl         $-1061109568, %ebx
	0x29, 0xf3, //0x000009cd subl         %esi, %ebx
	0x44, 0x8d, 0x86, 0x46, 0x46, 0x46, 0x46, //0x000009cf leal         $1179010630(%rsi), %r8d
	0x21, 0xd3, //0x000009d6 andl         %edx, %ebx
	0x44, 0x85, 0xc3, //0x000009d8 testl        %r8d, %ebx
	0x0f, 0x85, 0xcd, 0x01, 0x00, 0x00, //0x000009db jne          LBB0_158
	0xbb, 0xe0, 0xe0, 0xe0, 0xe0, //0x000009e1 movl         $-522133280, %ebx
	0x29, 0xf3, //0x000009e6 subl         %esi, %ebx
	0x81, 0xc6, 0x39, 0x39, 0x39, 0x39, //0x000009e8 addl         $960051513, %esi
	0x21, 0xda, //0x000009ee andl         %ebx, %edx
	0x85, 0xf2, //0x000009f0 testl        %esi, %edx
	0x0f, 0x85, 0xb6, 0x01, 0x00, 0x00, //0x000009f2 jne          LBB0_158
	0x0f, 0xc9, //0x000009f8 bswapl       %ecx
	0x89, 0xc8, //0x000009fa movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x000009fc shrl         $4, %eax
	0xf7, 0xd0, //0x000009ff notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x00000a01 andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x00000a06 leal         (%rax,%rax,8), %eax
	0x81, 0xe1, 0x0f, 0x0f, 0x0f, 0x0f, //0x00000a09 andl         $252645135, %ecx
	0x01, 0xc1, //0x00000a0f addl         %eax, %ecx
	0x89, 0xc8, //0x00000a11 movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x00000a13 shrl         $4, %eax
	0x09, 0xc8, //0x00000a16 orl          %ecx, %eax
	0x89, 0xc1, //0x00000a18 movl         %eax, %ecx
	0xc1, 0xe9, 0x08, //0x00000a1a shrl         $8, %ecx
	0x81, 0xe1, 0x00, 0xff, 0x00, 0x00, //0x00000a1d andl         $65280, %ecx
	0x89, 0xc2, //0x00000a23 movl         %eax, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x00000a25 andl         $128, %edx
	0x49, 0x83, 0xc5, 0x06, //0x00000a2b addq         $6, %r13
	0x09, 0xca, //0x00000a2f orl          %ecx, %edx
	0x44, 0x89, 0xd3, //0x00000a31 movl         %r10d, %ebx
	0x0f, 0x85, 0x8e, 0x01, 0x00, 0x00, //0x00000a34 jne          LBB0_162
	0x3c, 0x0d, //0x00000a3a cmpb         $13, %al
	0x0f, 0x85, 0x19, 0x00, 0x00, 0x00, //0x00000a3c jne          LBB0_144
	0xe9, 0xeb, 0xfe, 0xff, 0xff, //0x00000a42 jmp          LBB0_130
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000a47 .p2align 4, 0x90
	//0x00000a50 LBB0_143
	0x49, 0x89, 0xcd, //0x00000a50 movq         %rcx, %r13
	0x3c, 0x0d, //0x00000a53 cmpb         $13, %al
	0x0f, 0x84, 0xd7, 0xfe, 0xff, 0xff, //0x00000a55 je           LBB0_130
	//0x00000a5b LBB0_144
	0x89, 0xc7, //0x00000a5b movl         %eax, %edi
	0x3c, 0x0a, //0x00000a5d cmpb         $10, %al
	0x0f, 0x84, 0xcd, 0xfe, 0xff, 0xff, //0x00000a5f je           LBB0_130
	0xe9, 0x5e, 0x01, 0x00, 0x00, //0x00000a65 jmp          LBB0_162
	//0x00000a6a LBB0_147
	0xbe, 0x01, 0x00, 0x00, 0x00, //0x00000a6a movl         $1, %esi
	0xe9, 0x68, 0xfa, 0xff, 0xff, //0x00000a6f jmp          LBB0_62
	//0x00000a74 LBB0_148
	0x4d, 0x89, 0xcd, //0x00000a74 movq         %r9, %r13
	0xe9, 0x12, 0xfa, 0xff, 0xff, //0x00000a77 jmp          LBB0_54
	//0x00000a7c LBB0_150
	0x49, 0x89, 0xfd, //0x00000a7c movq         %rdi, %r13
	0xe9, 0x0a, 0xfa, 0xff, 0xff, //0x00000a7f jmp          LBB0_54
	//0x00000a84 LBB0_151
	0x49, 0x89, 0xcd, //0x00000a84 movq         %rcx, %r13
	0xe9, 0x3c, 0x01, 0x00, 0x00, //0x00000a87 jmp          LBB0_162
	//0x00000a8c LBB0_186
	0x4c, 0x89, 0xea, //0x00000a8c movq         %r13, %rdx
	//0x00000a8f LBB0_187
	0x4c, 0x39, 0xca, //0x00000a8f cmpq         %r9, %rdx
	0x0f, 0x83, 0x82, 0xfd, 0xff, 0xff, //0x00000a92 jae          LBB0_185
	//0x00000a98 LBB0_188
	0x48, 0x8d, 0x42, 0x01, //0x00000a98 leaq         $1(%rdx), %rax
	0x0f, 0xb6, 0x0a, //0x00000a9c movzbl       (%rdx), %ecx
	0x80, 0xf9, 0x5c, //0x00000a9f cmpb         $92, %cl
	0x0f, 0x85, 0xe9, 0x00, 0x00, 0x00, //0x00000aa2 jne          LBB0_199
	0x4c, 0x8d, 0x6a, 0x02, //0x00000aa8 leaq         $2(%rdx), %r13
	0x4d, 0x39, 0xcd, //0x00000aac cmpq         %r9, %r13
	0x0f, 0x87, 0xc7, 0x02, 0x00, 0x00, //0x00000aaf ja           LBB0_213
	0x0f, 0xb6, 0x00, //0x00000ab5 movzbl       (%rax), %eax
	0x3c, 0x6e, //0x00000ab8 cmpb         $110, %al
	0x0f, 0x84, 0xcc, 0xff, 0xff, 0xff, //0x00000aba je           LBB0_186
	0x3c, 0x72, //0x00000ac0 cmpb         $114, %al
	0x0f, 0x84, 0xc4, 0xff, 0xff, 0xff, //0x00000ac2 je           LBB0_186
	0x3c, 0x75, //0x00000ac8 cmpb         $117, %al
	0x0f, 0x85, 0x7e, 0xfd, 0xff, 0xff, //0x00000aca jne          LBB0_155
	0x4c, 0x89, 0xc8, //0x00000ad0 movq         %r9, %rax
	0x4c, 0x29, 0xe8, //0x00000ad3 subq         %r13, %rax
	0x48, 0x83, 0xf8, 0x04, //0x00000ad6 cmpq         $4, %rax
	0x0f, 0x8c, 0x6e, 0xfd, 0xff, 0xff, //0x00000ada jl           LBB0_155
	0x41, 0x8b, 0x45, 0x00, //0x00000ae0 movl         (%r13), %eax
	0x89, 0xc1, //0x00000ae4 movl         %eax, %ecx
	0xf7, 0xd1, //0x00000ae6 notl         %ecx
	0x8d, 0xb8, 0xd0, 0xcf, 0xcf, 0xcf, //0x00000ae8 leal         $-808464432(%rax), %edi
	0x81, 0xe1, 0x80, 0x80, 0x80, 0x80, //0x00000aee andl         $-2139062144, %ecx
	0x85, 0xf9, //0x00000af4 testl        %edi, %ecx
	0x0f, 0x85, 0x52, 0xfd, 0xff, 0xff, //0x00000af6 jne          LBB0_155
	0x8d, 0xb8, 0x19, 0x19, 0x19, 0x19, //0x00000afc leal         $421075225(%rax), %edi
	0x09, 0xc7, //0x00000b02 orl          %eax, %edi
	0xf7, 0xc7, 0x80, 0x80, 0x80, 0x80, //0x00000b04 testl        $-2139062144, %edi
	0x0f, 0x85, 0x3e, 0xfd, 0xff, 0xff, //0x00000b0a jne          LBB0_155
	0x89, 0xc7, //0x00000b10 movl         %eax, %edi
	0x81, 0xe7, 0x7f, 0x7f, 0x7f, 0x7f, //0x00000b12 andl         $2139062143, %edi
	0xbb, 0xc0, 0xc0, 0xc0, 0xc0, //0x00000b18 movl         $-1061109568, %ebx
	0x29, 0xfb, //0x00000b1d subl         %edi, %ebx
	0x44, 0x8d, 0x87, 0x46, 0x46, 0x46, 0x46, //0x00000b1f leal         $1179010630(%rdi), %r8d
	0x21, 0xcb, //0x00000b26 andl         %ecx, %ebx
	0x44, 0x85, 0xc3, //0x00000b28 testl        %r8d, %ebx
	0x0f, 0x85, 0x1d, 0xfd, 0xff, 0xff, //0x00000b2b jne          LBB0_155
	0xbb, 0xe0, 0xe0, 0xe0, 0xe0, //0x00000b31 movl         $-522133280, %ebx
	0x29, 0xfb, //0x00000b36 subl         %edi, %ebx
	0x81, 0xc7, 0x39, 0x39, 0x39, 0x39, //0x00000b38 addl         $960051513, %edi
	0x21, 0xd9, //0x00000b3e andl         %ebx, %ecx
	0x85, 0xf9, //0x00000b40 testl        %edi, %ecx
	0x0f, 0x85, 0x06, 0xfd, 0xff, 0xff, //0x00000b42 jne          LBB0_155
	0x44, 0x89, 0xd3, //0x00000b48 movl         %r10d, %ebx
	0x0f, 0xc8, //0x00000b4b bswapl       %eax
	0x89, 0xc1, //0x00000b4d movl         %eax, %ecx
	0xc1, 0xe9, 0x04, //0x00000b4f shrl         $4, %ecx
	0xf7, 0xd1, //0x00000b52 notl         %ecx
	0x81, 0xe1, 0x01, 0x01, 0x01, 0x01, //0x00000b54 andl         $16843009, %ecx
	0x8d, 0x0c, 0xc9, //0x00000b5a leal         (%rcx,%rcx,8), %ecx
	0x25, 0x0f, 0x0f, 0x0f, 0x0f, //0x00000b5d andl         $252645135, %eax
	0x01, 0xc8, //0x00000b62 addl         %ecx, %eax
	0x89, 0xc1, //0x00000b64 movl         %eax, %ecx
	0xc1, 0xe9, 0x04, //0x00000b66 shrl         $4, %ecx
	0x09, 0xc1, //0x00000b69 orl          %eax, %ecx
	0x89, 0xc8, //0x00000b6b movl         %ecx, %eax
	0xc1, 0xe8, 0x08, //0x00000b6d shrl         $8, %eax
	0x25, 0x00, 0xff, 0x00, 0x00, //0x00000b70 andl         $65280, %eax
	0x89, 0xcf, //0x00000b75 movl         %ecx, %edi
	0x81, 0xe7, 0x80, 0x00, 0x00, 0x00, //0x00000b77 andl         $128, %edi
	0x48, 0x83, 0xc2, 0x06, //0x00000b7d addq         $6, %rdx
	0x09, 0xc7, //0x00000b81 orl          %eax, %edi
	0x49, 0x89, 0xd5, //0x00000b83 movq         %rdx, %r13
	0x0f, 0x84, 0x08, 0x00, 0x00, 0x00, //0x00000b86 je           LBB0_200
	0xe9, 0xbd, 0xfc, 0xff, 0xff, //0x00000b8c jmp          LBB0_155
	//0x00000b91 LBB0_199
	0x49, 0x89, 0xc5, //0x00000b91 movq         %rax, %r13
	//0x00000b94 LBB0_200
	0x80, 0xf9, 0x0d, //0x00000b94 cmpb         $13, %cl
	0x0f, 0x84, 0xef, 0xfe, 0xff, 0xff, //0x00000b97 je           LBB0_186
	0x4c, 0x89, 0xea, //0x00000b9d movq         %r13, %rdx
	0x80, 0xf9, 0x0a, //0x00000ba0 cmpb         $10, %cl
	0x0f, 0x84, 0xe6, 0xfe, 0xff, 0xff, //0x00000ba3 je           LBB0_187
	0xe9, 0xa0, 0xfc, 0xff, 0xff, //0x00000ba9 jmp          LBB0_155
	//0x00000bae LBB0_158
	0x49, 0x89, 0xc5, //0x00000bae movq         %rax, %r13
	0x44, 0x89, 0xd3, //0x00000bb1 movl         %r10d, %ebx
	0xe9, 0x0f, 0x00, 0x00, 0x00, //0x00000bb4 jmp          LBB0_162
	//0x00000bb9 LBB0_159
	0x80, 0xf9, 0x2f, //0x00000bb9 cmpb         $47, %cl
	0x0f, 0x85, 0x03, 0x00, 0x00, 0x00, //0x00000bbc jne          LBB0_161
	0x40, 0xb7, 0x2f, //0x00000bc2 movb         $47, %dil
	//0x00000bc5 LBB0_161
	0x49, 0x89, 0xc5, //0x00000bc5 movq         %rax, %r13
	//0x00000bc8 LBB0_162
	0x40, 0x0f, 0xb6, 0xc7, //0x00000bc8 movzbl       %dil, %eax
	0x48, 0x8b, 0x4d, 0xd0, //0x00000bcc movq         $-48(%rbp), %rcx
	0x0f, 0xb6, 0x04, 0x01, //0x00000bd0 movzbl       (%rcx,%rax), %eax
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00000bd4 cmpl         $255, %eax
	0x0f, 0x84, 0x4b, 0x01, 0x00, 0x00, //0x00000bd9 je           LBB0_182
	0xc1, 0xe3, 0x06, //0x00000bdf shll         $6, %ebx
	0x09, 0xc3, //0x00000be2 orl          %eax, %ebx
	0xbe, 0x03, 0x00, 0x00, 0x00, //0x00000be4 movl         $3, %esi
	0x4d, 0x39, 0xcd, //0x00000be9 cmpq         %r9, %r13
	0x0f, 0x82, 0x1f, 0x00, 0x00, 0x00, //0x00000bec jb           LBB0_166
	0xe9, 0x70, 0xf8, 0xff, 0xff, //0x00000bf2 jmp          LBB0_51
	//0x00000bf7 LBB0_180
	0x80, 0xf9, 0x6e, //0x00000bf7 cmpb         $110, %cl
	0x0f, 0x85, 0x47, 0x01, 0x00, 0x00, //0x00000bfa jne          LBB0_206
	//0x00000c00 LBB0_164
	0x49, 0x89, 0xc5, //0x00000c00 movq         %rax, %r13
	//0x00000c03 LBB0_165
	0xbe, 0x03, 0x00, 0x00, 0x00, //0x00000c03 movl         $3, %esi
	0x4d, 0x39, 0xcd, //0x00000c08 cmpq         %r9, %r13
	0x0f, 0x83, 0x9d, 0xf7, 0xff, 0xff, //0x00000c0b jae          LBB0_38
	//0x00000c11 LBB0_166
	0x49, 0x8d, 0x4d, 0x01, //0x00000c11 leaq         $1(%r13), %rcx
	0x41, 0x0f, 0xb6, 0x45, 0x00, //0x00000c15 movzbl       (%r13), %eax
	0x3c, 0x5c, //0x00000c1a cmpb         $92, %al
	0x0f, 0x85, 0xee, 0x00, 0x00, 0x00, //0x00000c1c jne          LBB0_177
	0x49, 0x8d, 0x45, 0x02, //0x00000c22 leaq         $2(%r13), %rax
	0x40, 0xb7, 0xff, //0x00000c26 movb         $-1, %dil
	0x4c, 0x39, 0xc8, //0x00000c29 cmpq         %r9, %rax
	0x0f, 0x87, 0x02, 0x01, 0x00, 0x00, //0x00000c2c ja           LBB0_183
	0x0f, 0xb6, 0x09, //0x00000c32 movzbl       (%rcx), %ecx
	0x80, 0xf9, 0x71, //0x00000c35 cmpb         $113, %cl
	0x0f, 0x8e, 0xb9, 0xff, 0xff, 0xff, //0x00000c38 jle          LBB0_180
	0x80, 0xf9, 0x72, //0x00000c3e cmpb         $114, %cl
	0x0f, 0x84, 0xb9, 0xff, 0xff, 0xff, //0x00000c41 je           LBB0_164
	0x80, 0xf9, 0x75, //0x00000c47 cmpb         $117, %cl
	0x0f, 0x85, 0x03, 0x01, 0x00, 0x00, //0x00000c4a jne          LBB0_208
	0x4c, 0x89, 0xc9, //0x00000c50 movq         %r9, %rcx
	0x48, 0x29, 0xc1, //0x00000c53 subq         %rax, %rcx
	0x48, 0x83, 0xf9, 0x04, //0x00000c56 cmpq         $4, %rcx
	0x0f, 0x8c, 0xf3, 0x00, 0x00, 0x00, //0x00000c5a jl           LBB0_208
	0x8b, 0x08, //0x00000c60 movl         (%rax), %ecx
	0x89, 0xca, //0x00000c62 movl         %ecx, %edx
	0xf7, 0xd2, //0x00000c64 notl         %edx
	0x8d, 0xb1, 0xd0, 0xcf, 0xcf, 0xcf, //0x00000c66 leal         $-808464432(%rcx), %esi
	0x81, 0xe2, 0x80, 0x80, 0x80, 0x80, //0x00000c6c andl         $-2139062144, %edx
	0x85, 0xf2, //0x00000c72 testl        %esi, %edx
	0x0f, 0x85, 0xd9, 0x00, 0x00, 0x00, //0x00000c74 jne          LBB0_208
	0x8d, 0xb1, 0x19, 0x19, 0x19, 0x19, //0x00000c7a leal         $421075225(%rcx), %esi
	0x09, 0xce, //0x00000c80 orl          %ecx, %esi
	0xf7, 0xc6, 0x80, 0x80, 0x80, 0x80, //0x00000c82 testl        $-2139062144, %esi
	0x0f, 0x85, 0xc5, 0x00, 0x00, 0x00, //0x00000c88 jne          LBB0_208
	0x41, 0x89, 0xda, //0x00000c8e movl         %ebx, %r10d
	0x89, 0xce, //0x00000c91 movl         %ecx, %esi
	0x81, 0xe6, 0x7f, 0x7f, 0x7f, 0x7f, //0x00000c93 andl         $2139062143, %esi
	0xbb, 0xc0, 0xc0, 0xc0, 0xc0, //0x00000c99 movl         $-1061109568, %ebx
	0x29, 0xf3, //0x00000c9e subl         %esi, %ebx
	0x44, 0x8d, 0x86, 0x46, 0x46, 0x46, 0x46, //0x00000ca0 leal         $1179010630(%rsi), %r8d
	0x21, 0xd3, //0x00000ca7 andl         %edx, %ebx
	0x44, 0x85, 0xc3, //0x00000ca9 testl        %r8d, %ebx
	0x0f, 0x85, 0x8a, 0x00, 0x00, 0x00, //0x00000cac jne          LBB0_205
	0xbb, 0xe0, 0xe0, 0xe0, 0xe0, //0x00000cb2 movl         $-522133280, %ebx
	0x29, 0xf3, //0x00000cb7 subl         %esi, %ebx
	0x81, 0xc6, 0x39, 0x39, 0x39, 0x39, //0x00000cb9 addl         $960051513, %esi
	0x21, 0xda, //0x00000cbf andl         %ebx, %edx
	0x85, 0xf2, //0x00000cc1 testl        %esi, %edx
	0x0f, 0x85, 0x73, 0x00, 0x00, 0x00, //0x00000cc3 jne          LBB0_205
	0x0f, 0xc9, //0x00000cc9 bswapl       %ecx
	0x89, 0xc8, //0x00000ccb movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x00000ccd shrl         $4, %eax
	0xf7, 0xd0, //0x00000cd0 notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x00000cd2 andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x00000cd7 leal         (%rax,%rax,8), %eax
	0x81, 0xe1, 0x0f, 0x0f, 0x0f, 0x0f, //0x00000cda andl         $252645135, %ecx
	0x01, 0xc1, //0x00000ce0 addl         %eax, %ecx
	0x89, 0xc8, //0x00000ce2 movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x00000ce4 shrl         $4, %eax
	0x09, 0xc8, //0x00000ce7 orl          %ecx, %eax
	0x89, 0xc1, //0x00000ce9 movl         %eax, %ecx
	0xc1, 0xe9, 0x08, //0x00000ceb shrl         $8, %ecx
	0x81, 0xe1, 0x00, 0xff, 0x00, 0x00, //0x00000cee andl         $65280, %ecx
	0x89, 0xc2, //0x00000cf4 movl         %eax, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x00000cf6 andl         $128, %edx
	0x49, 0x83, 0xc5, 0x06, //0x00000cfc addq         $6, %r13
	0x09, 0xca, //0x00000d00 orl          %ecx, %edx
	0x44, 0x89, 0xd3, //0x00000d02 movl         %r10d, %ebx
	0x0f, 0x84, 0x08, 0x00, 0x00, 0x00, //0x00000d05 je           LBB0_178
	0xe9, 0x46, 0x00, 0x00, 0x00, //0x00000d0b jmp          LBB0_209
	//0x00000d10 LBB0_177
	0x49, 0x89, 0xcd, //0x00000d10 movq         %rcx, %r13
	//0x00000d13 LBB0_178
	0x3c, 0x0d, //0x00000d13 cmpb         $13, %al
	0x0f, 0x84, 0xe8, 0xfe, 0xff, 0xff, //0x00000d15 je           LBB0_165
	0x89, 0xc7, //0x00000d1b movl         %eax, %edi
	0x3c, 0x0a, //0x00000d1d cmpb         $10, %al
	0x0f, 0x84, 0xde, 0xfe, 0xff, 0xff, //0x00000d1f je           LBB0_165
	0xe9, 0x2c, 0x00, 0x00, 0x00, //0x00000d25 jmp          LBB0_209
	//0x00000d2a LBB0_182
	0xbe, 0x02, 0x00, 0x00, 0x00, //0x00000d2a movl         $2, %esi
	0xe9, 0xa8, 0xf7, 0xff, 0xff, //0x00000d2f jmp          LBB0_62
	//0x00000d34 LBB0_183
	0x49, 0x89, 0xcd, //0x00000d34 movq         %rcx, %r13
	0xe9, 0x1a, 0x00, 0x00, 0x00, //0x00000d37 jmp          LBB0_209
	//0x00000d3c LBB0_205
	0x49, 0x89, 0xc5, //0x00000d3c movq         %rax, %r13
	0x44, 0x89, 0xd3, //0x00000d3f movl         %r10d, %ebx
	0xe9, 0x0f, 0x00, 0x00, 0x00, //0x00000d42 jmp          LBB0_209
	//0x00000d47 LBB0_206
	0x80, 0xf9, 0x2f, //0x00000d47 cmpb         $47, %cl
	0x0f, 0x85, 0x03, 0x00, 0x00, 0x00, //0x00000d4a jne          LBB0_208
	0x40, 0xb7, 0x2f, //0x00000d50 movb         $47, %dil
	//0x00000d53 LBB0_208
	0x49, 0x89, 0xc5, //0x00000d53 movq         %rax, %r13
	//0x00000d56 LBB0_209
	0x40, 0x0f, 0xb6, 0xc7, //0x00000d56 movzbl       %dil, %eax
	0x48, 0x8b, 0x4d, 0xd0, //0x00000d5a movq         $-48(%rbp), %rcx
	0x0f, 0xb6, 0x04, 0x01, //0x00000d5e movzbl       (%rcx,%rax), %eax
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00000d62 cmpl         $255, %eax
	0x0f, 0x84, 0x66, 0xfb, 0xff, 0xff, //0x00000d67 je           LBB0_211
	//0x00000d6d LBB0_210
	0xc1, 0xe3, 0x06, //0x00000d6d shll         $6, %ebx
	0x09, 0xc3, //0x00000d70 orl          %eax, %ebx
	0xbe, 0x04, 0x00, 0x00, 0x00, //0x00000d72 movl         $4, %esi
	0xe9, 0xeb, 0xf6, 0xff, 0xff, //0x00000d77 jmp          LBB0_51
	//0x00000d7c LBB0_213
	0x49, 0x89, 0xc5, //0x00000d7c movq         %rax, %r13
	0xe9, 0xca, 0xfa, 0xff, 0xff, //0x00000d7f jmp          LBB0_155
	//0x00000d84 LBB0_214
	0x4d, 0x8d, 0x79, 0xfc, //0x00000d84 leaq         $-4(%r9), %r15
	0x4d, 0x39, 0xfe, //0x00000d88 cmpq         %r15, %r14
	0x0f, 0x87, 0xe4, 0x0c, 0x00, 0x00, //0x00000d8b ja           LBB0_433
	0x4c, 0x8b, 0x6d, 0xb0, //0x00000d91 movq         $-80(%rbp), %r13
	0x4c, 0x03, 0x6d, 0x88, //0x00000d95 addq         $-120(%rbp), %r13
	0x49, 0x83, 0xc5, 0xfc, //0x00000d99 addq         $-4, %r13
	0x4c, 0x39, 0xef, //0x00000d9d cmpq         %r13, %rdi
	0x44, 0x8b, 0x65, 0xc4, //0x00000da0 movl         $-60(%rbp), %r12d
	0x0f, 0x87, 0xcf, 0x0c, 0x00, 0x00, //0x00000da4 ja           LBB0_434
	0x48, 0x8b, 0x45, 0xb8, //0x00000daa movq         $-72(%rbp), %rax
	0x48, 0x8b, 0x4d, 0xa0, //0x00000dae movq         $-96(%rbp), %rcx
	0x48, 0x01, 0xc8, //0x00000db2 addq         %rcx, %rax
	0x48, 0x83, 0xc0, 0xff, //0x00000db5 addq         $-1, %rax
	0x48, 0x89, 0x45, 0x90, //0x00000db9 movq         %rax, $-112(%rbp)
	0x4c, 0x89, 0x6d, 0xb0, //0x00000dbd movq         %r13, $-80(%rbp)
	0x4c, 0x89, 0x7d, 0xc8, //0x00000dc1 movq         %r15, $-56(%rbp)
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000dc5 .p2align 4, 0x90
	//0x00000dd0 LBB0_217
	0x49, 0x89, 0xfb, //0x00000dd0 movq         %rdi, %r11
	0x41, 0x0f, 0xb6, 0x06, //0x00000dd3 movzbl       (%r14), %eax
	0x48, 0x8b, 0x75, 0xd0, //0x00000dd7 movq         $-48(%rbp), %rsi
	0x0f, 0xb6, 0x14, 0x06, //0x00000ddb movzbl       (%rsi,%rax), %edx
	0x41, 0x0f, 0xb6, 0x46, 0x01, //0x00000ddf movzbl       $1(%r14), %eax
	0x0f, 0xb6, 0x04, 0x06, //0x00000de4 movzbl       (%rsi,%rax), %eax
	0x41, 0x0f, 0xb6, 0x4e, 0x02, //0x00000de8 movzbl       $2(%r14), %ecx
	0x0f, 0xb6, 0x1c, 0x0e, //0x00000ded movzbl       (%rsi,%rcx), %ebx
	0x41, 0x0f, 0xb6, 0x4e, 0x03, //0x00000df1 movzbl       $3(%r14), %ecx
	0x0f, 0xb6, 0x0c, 0x0e, //0x00000df6 movzbl       (%rsi,%rcx), %ecx
	0x89, 0xc7, //0x00000dfa movl         %eax, %edi
	0x09, 0xd7, //0x00000dfc orl          %edx, %edi
	0x89, 0xde, //0x00000dfe movl         %ebx, %esi
	0x09, 0xce, //0x00000e00 orl          %ecx, %esi
	0x09, 0xfe, //0x00000e02 orl          %edi, %esi
	0x40, 0x80, 0xfe, 0xff, //0x00000e04 cmpb         $-1, %sil
	0x0f, 0x84, 0x32, 0x00, 0x00, 0x00, //0x00000e08 je           LBB0_219
	0xc1, 0xe2, 0x1a, //0x00000e0e shll         $26, %edx
	0xc1, 0xe0, 0x14, //0x00000e11 shll         $20, %eax
	0x09, 0xd0, //0x00000e14 orl          %edx, %eax
	0xc1, 0xe3, 0x0e, //0x00000e16 shll         $14, %ebx
	0xc1, 0xe1, 0x08, //0x00000e19 shll         $8, %ecx
	0x09, 0xd9, //0x00000e1c orl          %ebx, %ecx
	0x09, 0xc1, //0x00000e1e orl          %eax, %ecx
	0x0f, 0xc9, //0x00000e20 bswapl       %ecx
	0x4c, 0x89, 0xdf, //0x00000e22 movq         %r11, %rdi
	0x41, 0x89, 0x0b, //0x00000e25 movl         %ecx, (%r11)
	0x49, 0x83, 0xc6, 0x04, //0x00000e28 addq         $4, %r14
	0x48, 0x83, 0xc7, 0x03, //0x00000e2c addq         $3, %rdi
	0xe9, 0xbb, 0x06, 0x00, 0x00, //0x00000e30 jmp          LBB0_372
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000e35 .p2align 4, 0x90
	//0x00000e40 LBB0_219
	0x4d, 0x39, 0xce, //0x00000e40 cmpq         %r9, %r14
	0x4c, 0x89, 0xdf, //0x00000e43 movq         %r11, %rdi
	0x0f, 0x83, 0xa4, 0x06, 0x00, 0x00, //0x00000e46 jae          LBB0_372
	0x4c, 0x89, 0xf6, //0x00000e4c movq         %r14, %rsi
	0x41, 0xf6, 0xc4, 0x08, //0x00000e4f testb        $8, %r12b
	0x0f, 0x85, 0xd1, 0x00, 0x00, 0x00, //0x00000e53 jne          LBB0_233
	0xe9, 0x0f, 0x00, 0x00, 0x00, //0x00000e59 jmp          LBB0_222
	0x90, 0x90, //0x00000e5e .p2align 4, 0x90
	//0x00000e60 LBB0_221
	0x48, 0x83, 0xc6, 0x01, //0x00000e60 addq         $1, %rsi
	0x4c, 0x39, 0xce, //0x00000e64 cmpq         %r9, %rsi
	0x0f, 0x83, 0xde, 0x01, 0x00, 0x00, //0x00000e67 jae          LBB0_249
	//0x00000e6d LBB0_222
	0x44, 0x0f, 0xb6, 0x06, //0x00000e6d movzbl       (%rsi), %r8d
	0x49, 0x83, 0xf8, 0x0d, //0x00000e71 cmpq         $13, %r8
	0x0f, 0x84, 0xe5, 0xff, 0xff, 0xff, //0x00000e75 je           LBB0_221
	0x41, 0x80, 0xf8, 0x0a, //0x00000e7b cmpb         $10, %r8b
	0x0f, 0x84, 0xdb, 0xff, 0xff, 0xff, //0x00000e7f je           LBB0_221
	0x48, 0x8b, 0x45, 0xd0, //0x00000e85 movq         $-48(%rbp), %rax
	0x42, 0x0f, 0xb6, 0x14, 0x00, //0x00000e89 movzbl       (%rax,%r8), %edx
	0x48, 0x83, 0xc6, 0x01, //0x00000e8e addq         $1, %rsi
	0x81, 0xfa, 0xff, 0x00, 0x00, 0x00, //0x00000e92 cmpl         $255, %edx
	0x0f, 0x84, 0xe4, 0x02, 0x00, 0x00, //0x00000e98 je           LBB0_274
	0xbb, 0x01, 0x00, 0x00, 0x00, //0x00000e9e movl         $1, %ebx
	0x4c, 0x39, 0xce, //0x00000ea3 cmpq         %r9, %rsi
	0x0f, 0x82, 0x21, 0x00, 0x00, 0x00, //0x00000ea6 jb           LBB0_227
	0xe9, 0x50, 0x02, 0x00, 0x00, //0x00000eac jmp          LBB0_264
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000eb1 .p2align 4, 0x90
	//0x00000ec0 LBB0_226
	0x48, 0x83, 0xc6, 0x01, //0x00000ec0 addq         $1, %rsi
	0x4c, 0x39, 0xce, //0x00000ec4 cmpq         %r9, %rsi
	0x0f, 0x83, 0x9e, 0x04, 0x00, 0x00, //0x00000ec7 jae          LBB0_307
	//0x00000ecd LBB0_227
	0x44, 0x0f, 0xb6, 0x06, //0x00000ecd movzbl       (%rsi), %r8d
	0x49, 0x83, 0xf8, 0x0d, //0x00000ed1 cmpq         $13, %r8
	0x0f, 0x84, 0xe5, 0xff, 0xff, 0xff, //0x00000ed5 je           LBB0_226
	0x41, 0x80, 0xf8, 0x0a, //0x00000edb cmpb         $10, %r8b
	0x0f, 0x84, 0xdb, 0xff, 0xff, 0xff, //0x00000edf je           LBB0_226
	0x48, 0x8b, 0x45, 0xd0, //0x00000ee5 movq         $-48(%rbp), %rax
	0x42, 0x0f, 0xb6, 0x04, 0x00, //0x00000ee9 movzbl       (%rax,%r8), %eax
	0x48, 0x83, 0xc6, 0x01, //0x00000eee addq         $1, %rsi
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00000ef2 cmpl         $255, %eax
	0x0f, 0x84, 0x2e, 0x08, 0x00, 0x00, //0x00000ef7 je           LBB0_363
	0xc1, 0xe2, 0x06, //0x00000efd shll         $6, %edx
	0x09, 0xc2, //0x00000f00 orl          %eax, %edx
	0xbb, 0x02, 0x00, 0x00, 0x00, //0x00000f02 movl         $2, %ebx
	0x4c, 0x39, 0xce, //0x00000f07 cmpq         %r9, %rsi
	0x0f, 0x82, 0x61, 0x01, 0x00, 0x00, //0x00000f0a jb           LBB0_253
	0xe9, 0xec, 0x01, 0x00, 0x00, //0x00000f10 jmp          LBB0_264
	//0x00000f15 LBB0_247
	0x80, 0xf9, 0x6e, //0x00000f15 cmpb         $110, %cl
	0x0f, 0x85, 0xae, 0x01, 0x00, 0x00, //0x00000f18 jne          LBB0_259
	//0x00000f1e LBB0_231
	0x48, 0x89, 0xc6, //0x00000f1e movq         %rax, %rsi
	//0x00000f21 LBB0_232
	0x4c, 0x39, 0xce, //0x00000f21 cmpq         %r9, %rsi
	0x0f, 0x83, 0x21, 0x01, 0x00, 0x00, //0x00000f24 jae          LBB0_249
	//0x00000f2a LBB0_233
	0x48, 0x8d, 0x4e, 0x01, //0x00000f2a leaq         $1(%rsi), %rcx
	0x0f, 0xb6, 0x06, //0x00000f2e movzbl       (%rsi), %eax
	0x3c, 0x5c, //0x00000f31 cmpb         $92, %al
	0x0f, 0x85, 0xf7, 0x00, 0x00, 0x00, //0x00000f33 jne          LBB0_245
	0x48, 0x8d, 0x46, 0x02, //0x00000f39 leaq         $2(%rsi), %rax
	0x41, 0xb0, 0xff, //0x00000f3d movb         $-1, %r8b
	0x4c, 0x39, 0xc8, //0x00000f40 cmpq         %r9, %rax
	0x0f, 0x87, 0x70, 0x01, 0x00, 0x00, //0x00000f43 ja           LBB0_257
	0x0f, 0xb6, 0x09, //0x00000f49 movzbl       (%rcx), %ecx
	0x80, 0xf9, 0x71, //0x00000f4c cmpb         $113, %cl
	0x0f, 0x8e, 0xc0, 0xff, 0xff, 0xff, //0x00000f4f jle          LBB0_247
	0x80, 0xf9, 0x72, //0x00000f55 cmpb         $114, %cl
	0x0f, 0x84, 0xc0, 0xff, 0xff, 0xff, //0x00000f58 je           LBB0_231
	0x80, 0xf9, 0x75, //0x00000f5e cmpb         $117, %cl
	0x0f, 0x85, 0x71, 0x01, 0x00, 0x00, //0x00000f61 jne          LBB0_261
	0x4c, 0x89, 0xc9, //0x00000f67 movq         %r9, %rcx
	0x48, 0x29, 0xc1, //0x00000f6a subq         %rax, %rcx
	0x48, 0x83, 0xf9, 0x04, //0x00000f6d cmpq         $4, %rcx
	0x0f, 0x8c, 0x61, 0x01, 0x00, 0x00, //0x00000f71 jl           LBB0_261
	0x8b, 0x18, //0x00000f77 movl         (%rax), %ebx
	0x89, 0xda, //0x00000f79 movl         %ebx, %edx
	0xf7, 0xd2, //0x00000f7b notl         %edx
	0x8d, 0x8b, 0xd0, 0xcf, 0xcf, 0xcf, //0x00000f7d leal         $-808464432(%rbx), %ecx
	0x81, 0xe2, 0x80, 0x80, 0x80, 0x80, //0x00000f83 andl         $-2139062144, %edx
	0x85, 0xca, //0x00000f89 testl        %ecx, %edx
	0x0f, 0x85, 0x47, 0x01, 0x00, 0x00, //0x00000f8b jne          LBB0_261
	0x8d, 0x8b, 0x19, 0x19, 0x19, 0x19, //0x00000f91 leal         $421075225(%rbx), %ecx
	0x09, 0xd9, //0x00000f97 orl          %ebx, %ecx
	0xf7, 0xc1, 0x80, 0x80, 0x80, 0x80, //0x00000f99 testl        $-2139062144, %ecx
	0x0f, 0x85, 0x33, 0x01, 0x00, 0x00, //0x00000f9f jne          LBB0_261
	0x89, 0xd9, //0x00000fa5 movl         %ebx, %ecx
	0x81, 0xe1, 0x7f, 0x7f, 0x7f, 0x7f, //0x00000fa7 andl         $2139062143, %ecx
	0xbf, 0xc0, 0xc0, 0xc0, 0xc0, //0x00000fad movl         $-1061109568, %edi
	0x29, 0xcf, //0x00000fb2 subl         %ecx, %edi
	0x44, 0x8d, 0x91, 0x46, 0x46, 0x46, 0x46, //0x00000fb4 leal         $1179010630(%rcx), %r10d
	0x21, 0xd7, //0x00000fbb andl         %edx, %edi
	0x44, 0x85, 0xd7, //0x00000fbd testl        %r10d, %edi
	0x0f, 0x85, 0xfb, 0x00, 0x00, 0x00, //0x00000fc0 jne          LBB0_258
	0xbf, 0xe0, 0xe0, 0xe0, 0xe0, //0x00000fc6 movl         $-522133280, %edi
	0x29, 0xcf, //0x00000fcb subl         %ecx, %edi
	0x81, 0xc1, 0x39, 0x39, 0x39, 0x39, //0x00000fcd addl         $960051513, %ecx
	0x21, 0xfa, //0x00000fd3 andl         %edi, %edx
	0x85, 0xca, //0x00000fd5 testl        %ecx, %edx
	0x0f, 0x85, 0xe4, 0x00, 0x00, 0x00, //0x00000fd7 jne          LBB0_258
	0x0f, 0xcb, //0x00000fdd bswapl       %ebx
	0x89, 0xd8, //0x00000fdf movl         %ebx, %eax
	0xc1, 0xe8, 0x04, //0x00000fe1 shrl         $4, %eax
	0xf7, 0xd0, //0x00000fe4 notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x00000fe6 andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x00000feb leal         (%rax,%rax,8), %eax
	0x81, 0xe3, 0x0f, 0x0f, 0x0f, 0x0f, //0x00000fee andl         $252645135, %ebx
	0x01, 0xc3, //0x00000ff4 addl         %eax, %ebx
	0x89, 0xd8, //0x00000ff6 movl         %ebx, %eax
	0xc1, 0xe8, 0x04, //0x00000ff8 shrl         $4, %eax
	0x09, 0xd8, //0x00000ffb orl          %ebx, %eax
	0x89, 0xc1, //0x00000ffd movl         %eax, %ecx
	0xc1, 0xe9, 0x08, //0x00000fff shrl         $8, %ecx
	0x81, 0xe1, 0x00, 0xff, 0x00, 0x00, //0x00001002 andl         $65280, %ecx
	0x89, 0xc2, //0x00001008 movl         %eax, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x0000100a andl         $128, %edx
	0x48, 0x83, 0xc6, 0x06, //0x00001010 addq         $6, %rsi
	0x09, 0xca, //0x00001014 orl          %ecx, %edx
	0x4c, 0x89, 0xdf, //0x00001016 movq         %r11, %rdi
	0x0f, 0x85, 0xbc, 0x00, 0x00, 0x00, //0x00001019 jne          LBB0_262
	0x3c, 0x0d, //0x0000101f cmpb         $13, %al
	0x0f, 0x85, 0x14, 0x00, 0x00, 0x00, //0x00001021 jne          LBB0_246
	0xe9, 0xf5, 0xfe, 0xff, 0xff, //0x00001027 jmp          LBB0_232
	0x90, 0x90, 0x90, 0x90, //0x0000102c .p2align 4, 0x90
	//0x00001030 LBB0_245
	0x48, 0x89, 0xce, //0x00001030 movq         %rcx, %rsi
	0x3c, 0x0d, //0x00001033 cmpb         $13, %al
	0x0f, 0x84, 0xe6, 0xfe, 0xff, 0xff, //0x00001035 je           LBB0_232
	//0x0000103b LBB0_246
	0x41, 0x89, 0xc0, //0x0000103b movl         %eax, %r8d
	0x3c, 0x0a, //0x0000103e cmpb         $10, %al
	0x0f, 0x84, 0xdb, 0xfe, 0xff, 0xff, //0x00001040 je           LBB0_232
	0xe9, 0x90, 0x00, 0x00, 0x00, //0x00001046 jmp          LBB0_262
	//0x0000104b LBB0_249
	0x31, 0xdb, //0x0000104b xorl         %ebx, %ebx
	0x31, 0xd2, //0x0000104d xorl         %edx, %edx
	//0x0000104f LBB0_250
	0x85, 0xdb, //0x0000104f testl        %ebx, %ebx
	0x0f, 0x85, 0xaa, 0x00, 0x00, 0x00, //0x00001051 jne          LBB0_264
	0x49, 0x89, 0xf6, //0x00001057 movq         %rsi, %r14
	0xe9, 0x91, 0x04, 0x00, 0x00, //0x0000105a jmp          LBB0_372
	//0x0000105f LBB0_252
	0x48, 0x83, 0xc6, 0x01, //0x0000105f addq         $1, %rsi
	0xbb, 0x02, 0x00, 0x00, 0x00, //0x00001063 movl         $2, %ebx
	0x4c, 0x39, 0xce, //0x00001068 cmpq         %r9, %rsi
	0x0f, 0x83, 0xde, 0xff, 0xff, 0xff, //0x0000106b jae          LBB0_250
	//0x00001071 LBB0_253
	0x44, 0x0f, 0xb6, 0x06, //0x00001071 movzbl       (%rsi), %r8d
	0x49, 0x83, 0xf8, 0x0d, //0x00001075 cmpq         $13, %r8
	0x0f, 0x84, 0xe0, 0xff, 0xff, 0xff, //0x00001079 je           LBB0_252
	0x41, 0x80, 0xf8, 0x0a, //0x0000107f cmpb         $10, %r8b
	0x0f, 0x84, 0xd6, 0xff, 0xff, 0xff, //0x00001083 je           LBB0_252
	0x48, 0x8b, 0x45, 0xd0, //0x00001089 movq         $-48(%rbp), %rax
	0x42, 0x0f, 0xb6, 0x04, 0x00, //0x0000108d movzbl       (%rax,%r8), %eax
	0x48, 0x83, 0xc6, 0x01, //0x00001092 addq         $1, %rsi
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00001096 cmpl         $255, %eax
	0x0f, 0x84, 0x3d, 0x08, 0x00, 0x00, //0x0000109b je           LBB0_400
	0xc1, 0xe2, 0x06, //0x000010a1 shll         $6, %edx
	0x09, 0xc2, //0x000010a4 orl          %eax, %edx
	0xbb, 0x03, 0x00, 0x00, 0x00, //0x000010a6 movl         $3, %ebx
	0x4c, 0x39, 0xce, //0x000010ab cmpq         %r9, %rsi
	0x0f, 0x82, 0x9c, 0x04, 0x00, 0x00, //0x000010ae jb           LBB0_333
	0xe9, 0x48, 0x00, 0x00, 0x00, //0x000010b4 jmp          LBB0_264
	//0x000010b9 LBB0_257
	0x48, 0x89, 0xce, //0x000010b9 movq         %rcx, %rsi
	0xe9, 0x1a, 0x00, 0x00, 0x00, //0x000010bc jmp          LBB0_262
	//0x000010c1 LBB0_258
	0x48, 0x89, 0xc6, //0x000010c1 movq         %rax, %rsi
	0x4c, 0x89, 0xdf, //0x000010c4 movq         %r11, %rdi
	0xe9, 0x0f, 0x00, 0x00, 0x00, //0x000010c7 jmp          LBB0_262
	//0x000010cc LBB0_259
	0x80, 0xf9, 0x2f, //0x000010cc cmpb         $47, %cl
	0x0f, 0x85, 0x03, 0x00, 0x00, 0x00, //0x000010cf jne          LBB0_261
	0x41, 0xb0, 0x2f, //0x000010d5 movb         $47, %r8b
	//0x000010d8 LBB0_261
	0x48, 0x89, 0xc6, //0x000010d8 movq         %rax, %rsi
	//0x000010db LBB0_262
	0x41, 0x0f, 0xb6, 0xc0, //0x000010db movzbl       %r8b, %eax
	0x48, 0x8b, 0x4d, 0xd0, //0x000010df movq         $-48(%rbp), %rcx
	0x0f, 0xb6, 0x14, 0x01, //0x000010e3 movzbl       (%rcx,%rax), %edx
	0x81, 0xfa, 0xff, 0x00, 0x00, 0x00, //0x000010e7 cmpl         $255, %edx
	0x0f, 0x84, 0x8f, 0x00, 0x00, 0x00, //0x000010ed je           LBB0_274
	0xbb, 0x01, 0x00, 0x00, 0x00, //0x000010f3 movl         $1, %ebx
	0x4c, 0x39, 0xce, //0x000010f8 cmpq         %r9, %rsi
	0x0f, 0x82, 0x41, 0x01, 0x00, 0x00, //0x000010fb jb           LBB0_290
	//0x00001101 LBB0_264
	0x41, 0xf6, 0xc4, 0x02, //0x00001101 testb        $2, %r12b
	0x0f, 0x94, 0xc0, //0x00001105 sete         %al
	0x83, 0xfb, 0x01, //0x00001108 cmpl         $1, %ebx
	0x0f, 0x94, 0xc1, //0x0000110b sete         %cl
	0x41, 0x89, 0xd5, //0x0000110e movl         %edx, %r13d
	0x4c, 0x39, 0xce, //0x00001111 cmpq         %r9, %rsi
	0x0f, 0x82, 0x11, 0x00, 0x00, 0x00, //0x00001114 jb           LBB0_267
	0x83, 0xfb, 0x04, //0x0000111a cmpl         $4, %ebx
	0x0f, 0x84, 0x08, 0x00, 0x00, 0x00, //0x0000111d je           LBB0_267
	0x08, 0xc8, //0x00001123 orb          %cl, %al
	0x0f, 0x85, 0x9a, 0x03, 0x00, 0x00, //0x00001125 jne          LBB0_371
	//0x0000112b LBB0_267
	0xb0, 0x04, //0x0000112b movb         $4, %al
	0x28, 0xd8, //0x0000112d subb         %bl, %al
	0x0f, 0xb6, 0xc0, //0x0000112f movzbl       %al, %eax
	0x01, 0xc0, //0x00001132 addl         %eax, %eax
	0x8d, 0x0c, 0x40, //0x00001134 leal         (%rax,%rax,2), %ecx
	0x44, 0x89, 0xe8, //0x00001137 movl         %r13d, %eax
	0xd3, 0xe0, //0x0000113a shll         %cl, %eax
	0x83, 0xfb, 0x02, //0x0000113c cmpl         $2, %ebx
	0x4c, 0x89, 0xdf, //0x0000113f movq         %r11, %rdi
	0x0f, 0x84, 0x1c, 0x00, 0x00, 0x00, //0x00001142 je           LBB0_272
	0x83, 0xfb, 0x03, //0x00001148 cmpl         $3, %ebx
	0x0f, 0x84, 0x10, 0x00, 0x00, 0x00, //0x0000114b je           LBB0_271
	0x83, 0xfb, 0x04, //0x00001151 cmpl         $4, %ebx
	0x44, 0x8b, 0x65, 0xc4, //0x00001154 movl         $-60(%rbp), %r12d
	0x0f, 0x85, 0x0f, 0x00, 0x00, 0x00, //0x00001158 jne          LBB0_273
	0x88, 0x47, 0x02, //0x0000115e movb         %al, $2(%rdi)
	//0x00001161 LBB0_271
	0x88, 0x67, 0x01, //0x00001161 movb         %ah, $1(%rdi)
	//0x00001164 LBB0_272
	0xc1, 0xe8, 0x10, //0x00001164 shrl         $16, %eax
	0x88, 0x07, //0x00001167 movb         %al, (%rdi)
	0x44, 0x8b, 0x65, 0xc4, //0x00001169 movl         $-60(%rbp), %r12d
	//0x0000116d LBB0_273
	0x89, 0xd8, //0x0000116d movl         %ebx, %eax
	0x48, 0x01, 0xc7, //0x0000116f addq         %rax, %rdi
	0x48, 0x83, 0xc7, 0xff, //0x00001172 addq         $-1, %rdi
	0x49, 0x89, 0xf6, //0x00001176 movq         %rsi, %r14
	0x4c, 0x8b, 0x6d, 0xb0, //0x00001179 movq         $-80(%rbp), %r13
	0xe9, 0x6e, 0x03, 0x00, 0x00, //0x0000117d jmp          LBB0_372
	//0x00001182 LBB0_274
	0x31, 0xdb, //0x00001182 xorl         %ebx, %ebx
	0x31, 0xd2, //0x00001184 xorl         %edx, %edx
	//0x00001186 LBB0_275
	0x41, 0xf6, 0xc4, 0x02, //0x00001186 testb        $2, %r12b
	0x0f, 0x85, 0x35, 0x03, 0x00, 0x00, //0x0000118a jne          LBB0_371
	0x41, 0x80, 0xf8, 0x3d, //0x00001190 cmpb         $61, %r8b
	0x0f, 0x85, 0x2b, 0x03, 0x00, 0x00, //0x00001194 jne          LBB0_371
	0x83, 0xfb, 0x02, //0x0000119a cmpl         $2, %ebx
	0x0f, 0x82, 0x22, 0x03, 0x00, 0x00, //0x0000119d jb           LBB0_371
	0x41, 0x89, 0xd5, //0x000011a3 movl         %edx, %r13d
	0xba, 0x05, 0x00, 0x00, 0x00, //0x000011a6 movl         $5, %edx
	0x29, 0xda, //0x000011ab subl         %ebx, %edx
	0xf6, 0x45, 0xc4, 0x08, //0x000011ad testb        $8, $-60(%rbp)
	0x0f, 0x85, 0xbe, 0x01, 0x00, 0x00, //0x000011b1 jne          LBB0_308
	0x4c, 0x39, 0xce, //0x000011b7 cmpq         %r9, %rsi
	0x0f, 0x83, 0x6b, 0xff, 0xff, 0xff, //0x000011ba jae          LBB0_267
	0x48, 0x8d, 0x4e, 0x01, //0x000011c0 leaq         $1(%rsi), %rcx
	0x48, 0x8b, 0x45, 0x90, //0x000011c4 movq         $-112(%rbp), %rax
	0x48, 0x29, 0xf0, //0x000011c8 subq         %rsi, %rax
	0x48, 0x83, 0xc6, 0x02, //0x000011cb addq         $2, %rsi
	0x48, 0x89, 0xf7, //0x000011cf movq         %rsi, %rdi
	0x48, 0x89, 0xce, //0x000011d2 movq         %rcx, %rsi
	0xe9, 0x18, 0x00, 0x00, 0x00, //0x000011d5 jmp          LBB0_282
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000011da .p2align 4, 0x90
	//0x000011e0 LBB0_281
	0x48, 0x83, 0xc6, 0x01, //0x000011e0 addq         $1, %rsi
	0x48, 0x83, 0xc7, 0x01, //0x000011e4 addq         $1, %rdi
	0x48, 0x83, 0xc0, 0xff, //0x000011e8 addq         $-1, %rax
	0x0f, 0x83, 0x43, 0x05, 0x00, 0x00, //0x000011ec jae          LBB0_364
	//0x000011f2 LBB0_282
	0x0f, 0xb6, 0x4e, 0xff, //0x000011f2 movzbl       $-1(%rsi), %ecx
	0x80, 0xf9, 0x0a, //0x000011f6 cmpb         $10, %cl
	0x0f, 0x84, 0xe1, 0xff, 0xff, 0xff, //0x000011f9 je           LBB0_281
	0x80, 0xf9, 0x0d, //0x000011ff cmpb         $13, %cl
	0x0f, 0x84, 0xd8, 0xff, 0xff, 0xff, //0x00001202 je           LBB0_281
	0x80, 0xf9, 0x3d, //0x00001208 cmpb         $61, %cl
	0x0f, 0x85, 0xb4, 0x02, 0x00, 0x00, //0x0000120b jne          LBB0_371
	0x83, 0xfa, 0x02, //0x00001211 cmpl         $2, %edx
	0x0f, 0x84, 0xab, 0x02, 0x00, 0x00, //0x00001214 je           LBB0_371
	0x4c, 0x39, 0xce, //0x0000121a cmpq         %r9, %rsi
	0x0f, 0x83, 0x08, 0xff, 0xff, 0xff, //0x0000121d jae          LBB0_267
	0x48, 0x01, 0xc6, //0x00001223 addq         %rax, %rsi
	0x31, 0xc9, //0x00001226 xorl         %ecx, %ecx
	0xe9, 0xeb, 0x02, 0x00, 0x00, //0x00001228 jmp          LBB0_328
	//0x0000122d LBB0_304
	0x80, 0xf9, 0x6e, //0x0000122d cmpb         $110, %cl
	0x0f, 0x85, 0x6e, 0x03, 0x00, 0x00, //0x00001230 jne          LBB0_340
	//0x00001236 LBB0_288
	0x48, 0x89, 0xc6, //0x00001236 movq         %rax, %rsi
	//0x00001239 LBB0_289
	0x4c, 0x39, 0xce, //0x00001239 cmpq         %r9, %rsi
	0x0f, 0x83, 0x29, 0x01, 0x00, 0x00, //0x0000123c jae          LBB0_307
	//0x00001242 LBB0_290
	0x48, 0x8d, 0x4e, 0x01, //0x00001242 leaq         $1(%rsi), %rcx
	0x0f, 0xb6, 0x06, //0x00001246 movzbl       (%rsi), %eax
	0x3c, 0x5c, //0x00001249 cmpb         $92, %al
	0x0f, 0x85, 0xff, 0x00, 0x00, 0x00, //0x0000124b jne          LBB0_302
	0x48, 0x8d, 0x46, 0x02, //0x00001251 leaq         $2(%rsi), %rax
	0x41, 0xb0, 0xff, //0x00001255 movb         $-1, %r8b
	0x4c, 0x39, 0xc8, //0x00001258 cmpq         %r9, %rax
	0x0f, 0x87, 0xd5, 0x02, 0x00, 0x00, //0x0000125b ja           LBB0_331
	0x0f, 0xb6, 0x09, //0x00001261 movzbl       (%rcx), %ecx
	0x80, 0xf9, 0x71, //0x00001264 cmpb         $113, %cl
	0x0f, 0x8e, 0xc0, 0xff, 0xff, 0xff, //0x00001267 jle          LBB0_304
	0x80, 0xf9, 0x72, //0x0000126d cmpb         $114, %cl
	0x0f, 0x84, 0xc0, 0xff, 0xff, 0xff, //0x00001270 je           LBB0_288
	0x80, 0xf9, 0x75, //0x00001276 cmpb         $117, %cl
	0x0f, 0x85, 0x31, 0x03, 0x00, 0x00, //0x00001279 jne          LBB0_342
	0x4c, 0x89, 0xc9, //0x0000127f movq         %r9, %rcx
	0x48, 0x29, 0xc1, //0x00001282 subq         %rax, %rcx
	0x48, 0x83, 0xf9, 0x04, //0x00001285 cmpq         $4, %rcx
	0x0f, 0x8c, 0x21, 0x03, 0x00, 0x00, //0x00001289 jl           LBB0_342
	0x41, 0x89, 0xd7, //0x0000128f movl         %edx, %r15d
	0x8b, 0x08, //0x00001292 movl         (%rax), %ecx
	0x89, 0xcb, //0x00001294 movl         %ecx, %ebx
	0xf7, 0xd3, //0x00001296 notl         %ebx
	0x8d, 0x91, 0xd0, 0xcf, 0xcf, 0xcf, //0x00001298 leal         $-808464432(%rcx), %edx
	0x81, 0xe3, 0x80, 0x80, 0x80, 0x80, //0x0000129e andl         $-2139062144, %ebx
	0x85, 0xd3, //0x000012a4 testl        %edx, %ebx
	0x0f, 0x85, 0xde, 0x02, 0x00, 0x00, //0x000012a6 jne          LBB0_337
	0x8d, 0x91, 0x19, 0x19, 0x19, 0x19, //0x000012ac leal         $421075225(%rcx), %edx
	0x09, 0xca, //0x000012b2 orl          %ecx, %edx
	0xf7, 0xc2, 0x80, 0x80, 0x80, 0x80, //0x000012b4 testl        $-2139062144, %edx
	0x0f, 0x85, 0xca, 0x02, 0x00, 0x00, //0x000012ba jne          LBB0_337
	0x89, 0xca, //0x000012c0 movl         %ecx, %edx
	0x81, 0xe2, 0x7f, 0x7f, 0x7f, 0x7f, //0x000012c2 andl         $2139062143, %edx
	0xbf, 0xc0, 0xc0, 0xc0, 0xc0, //0x000012c8 movl         $-1061109568, %edi
	0x29, 0xd7, //0x000012cd subl         %edx, %edi
	0x44, 0x8d, 0x92, 0x46, 0x46, 0x46, 0x46, //0x000012cf leal         $1179010630(%rdx), %r10d
	0x21, 0xdf, //0x000012d6 andl         %ebx, %edi
	0x44, 0x85, 0xd7, //0x000012d8 testl        %r10d, %edi
	0x0f, 0x85, 0xb1, 0x02, 0x00, 0x00, //0x000012db jne          LBB0_338
	0xbf, 0xe0, 0xe0, 0xe0, 0xe0, //0x000012e1 movl         $-522133280, %edi
	0x29, 0xd7, //0x000012e6 subl         %edx, %edi
	0x81, 0xc2, 0x39, 0x39, 0x39, 0x39, //0x000012e8 addl         $960051513, %edx
	0x21, 0xfb, //0x000012ee andl         %edi, %ebx
	0x85, 0xd3, //0x000012f0 testl        %edx, %ebx
	0x0f, 0x85, 0x9a, 0x02, 0x00, 0x00, //0x000012f2 jne          LBB0_338
	0x0f, 0xc9, //0x000012f8 bswapl       %ecx
	0x89, 0xc8, //0x000012fa movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x000012fc shrl         $4, %eax
	0xf7, 0xd0, //0x000012ff notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x00001301 andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x00001306 leal         (%rax,%rax,8), %eax
	0x81, 0xe1, 0x0f, 0x0f, 0x0f, 0x0f, //0x00001309 andl         $252645135, %ecx
	0x01, 0xc1, //0x0000130f addl         %eax, %ecx
	0x89, 0xc8, //0x00001311 movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x00001313 shrl         $4, %eax
	0x09, 0xc8, //0x00001316 orl          %ecx, %eax
	0x89, 0xc1, //0x00001318 movl         %eax, %ecx
	0xc1, 0xe9, 0x08, //0x0000131a shrl         $8, %ecx
	0x81, 0xe1, 0x00, 0xff, 0x00, 0x00, //0x0000131d andl         $65280, %ecx
	0x89, 0xc2, //0x00001323 movl         %eax, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x00001325 andl         $128, %edx
	0x48, 0x83, 0xc6, 0x06, //0x0000132b addq         $6, %rsi
	0x09, 0xca, //0x0000132f orl          %ecx, %edx
	0x4c, 0x89, 0xdf, //0x00001331 movq         %r11, %rdi
	0x44, 0x89, 0xfa, //0x00001334 movl         %r15d, %edx
	0x4c, 0x8b, 0x7d, 0xc8, //0x00001337 movq         $-56(%rbp), %r15
	0x0f, 0x85, 0x72, 0x02, 0x00, 0x00, //0x0000133b jne          LBB0_343
	0x3c, 0x0d, //0x00001341 cmpb         $13, %al
	0x0f, 0x85, 0x12, 0x00, 0x00, 0x00, //0x00001343 jne          LBB0_303
	0xe9, 0xeb, 0xfe, 0xff, 0xff, //0x00001349 jmp          LBB0_289
	0x90, 0x90, //0x0000134e .p2align 4, 0x90
	//0x00001350 LBB0_302
	0x48, 0x89, 0xce, //0x00001350 movq         %rcx, %rsi
	0x3c, 0x0d, //0x00001353 cmpb         $13, %al
	0x0f, 0x84, 0xde, 0xfe, 0xff, 0xff, //0x00001355 je           LBB0_289
	//0x0000135b LBB0_303
	0x41, 0x89, 0xc0, //0x0000135b movl         %eax, %r8d
	0x3c, 0x0a, //0x0000135e cmpb         $10, %al
	0x0f, 0x84, 0xd3, 0xfe, 0xff, 0xff, //0x00001360 je           LBB0_289
	0xe9, 0x48, 0x02, 0x00, 0x00, //0x00001366 jmp          LBB0_343
	//0x0000136b LBB0_307
	0xbb, 0x01, 0x00, 0x00, 0x00, //0x0000136b movl         $1, %ebx
	0xe9, 0xda, 0xfc, 0xff, 0xff, //0x00001370 jmp          LBB0_250
	//0x00001375 LBB0_308
	0x4c, 0x39, 0xce, //0x00001375 cmpq         %r9, %rsi
	0x0f, 0x83, 0xad, 0xfd, 0xff, 0xff, //0x00001378 jae          LBB0_267
	0x89, 0x55, 0x98, //0x0000137e movl         %edx, $-104(%rbp)
	0x48, 0x89, 0xf7, //0x00001381 movq         %rsi, %rdi
	0xe9, 0x1d, 0x00, 0x00, 0x00, //0x00001384 jmp          LBB0_312
	//0x00001389 LBB0_326
	0x4c, 0x89, 0xe7, //0x00001389 movq         %r12, %rdi
	0x4c, 0x39, 0xcf, //0x0000138c cmpq         %r9, %rdi
	0x0f, 0x82, 0x11, 0x00, 0x00, 0x00, //0x0000138f jb           LBB0_312
	0xe9, 0xa3, 0x03, 0x00, 0x00, //0x00001395 jmp          LBB0_366
	//0x0000139a LBB0_310
	0x48, 0x89, 0xf7, //0x0000139a movq         %rsi, %rdi
	0x4c, 0x39, 0xcf, //0x0000139d cmpq         %r9, %rdi
	0x0f, 0x83, 0x97, 0x03, 0x00, 0x00, //0x000013a0 jae          LBB0_366
	//0x000013a6 LBB0_312
	0x4c, 0x8d, 0x67, 0x01, //0x000013a6 leaq         $1(%rdi), %r12
	0x0f, 0xb6, 0x0f, //0x000013aa movzbl       (%rdi), %ecx
	0x80, 0xf9, 0x5c, //0x000013ad cmpb         $92, %cl
	0x0f, 0x85, 0xe7, 0x00, 0x00, 0x00, //0x000013b0 jne          LBB0_323
	0x48, 0x8d, 0x77, 0x02, //0x000013b6 leaq         $2(%rdi), %rsi
	0x4c, 0x39, 0xce, //0x000013ba cmpq         %r9, %rsi
	0x0f, 0x87, 0xff, 0x00, 0x00, 0x00, //0x000013bd ja           LBB0_370
	0x41, 0x0f, 0xb6, 0x04, 0x24, //0x000013c3 movzbl       (%r12), %eax
	0x3c, 0x6e, //0x000013c8 cmpb         $110, %al
	0x0f, 0x84, 0xca, 0xff, 0xff, 0xff, //0x000013ca je           LBB0_310
	0x3c, 0x72, //0x000013d0 cmpb         $114, %al
	0x0f, 0x84, 0xc2, 0xff, 0xff, 0xff, //0x000013d2 je           LBB0_310
	0x3c, 0x75, //0x000013d8 cmpb         $117, %al
	0x0f, 0x85, 0xe5, 0x00, 0x00, 0x00, //0x000013da jne          LBB0_371
	0x4c, 0x89, 0xc8, //0x000013e0 movq         %r9, %rax
	0x48, 0x29, 0xf0, //0x000013e3 subq         %rsi, %rax
	0x48, 0x83, 0xf8, 0x04, //0x000013e6 cmpq         $4, %rax
	0x0f, 0x8c, 0xd5, 0x00, 0x00, 0x00, //0x000013ea jl           LBB0_371
	0x8b, 0x16, //0x000013f0 movl         (%rsi), %edx
	0x89, 0xd1, //0x000013f2 movl         %edx, %ecx
	0xf7, 0xd1, //0x000013f4 notl         %ecx
	0x8d, 0x82, 0xd0, 0xcf, 0xcf, 0xcf, //0x000013f6 leal         $-808464432(%rdx), %eax
	0x81, 0xe1, 0x80, 0x80, 0x80, 0x80, //0x000013fc andl         $-2139062144, %ecx
	0x85, 0xc1, //0x00001402 testl        %eax, %ecx
	0x0f, 0x85, 0xbb, 0x00, 0x00, 0x00, //0x00001404 jne          LBB0_371
	0x8d, 0x82, 0x19, 0x19, 0x19, 0x19, //0x0000140a leal         $421075225(%rdx), %eax
	0x09, 0xd0, //0x00001410 orl          %edx, %eax
	0xa9, 0x80, 0x80, 0x80, 0x80, //0x00001412 testl        $-2139062144, %eax
	0x0f, 0x85, 0xa8, 0x00, 0x00, 0x00, //0x00001417 jne          LBB0_371
	0x89, 0xd0, //0x0000141d movl         %edx, %eax
	0x25, 0x7f, 0x7f, 0x7f, 0x7f, //0x0000141f andl         $2139062143, %eax
	0x41, 0xb8, 0xc0, 0xc0, 0xc0, 0xc0, //0x00001424 movl         $-1061109568, %r8d
	0x41, 0x29, 0xc0, //0x0000142a subl         %eax, %r8d
	0x44, 0x8d, 0x90, 0x46, 0x46, 0x46, 0x46, //0x0000142d leal         $1179010630(%rax), %r10d
	0x41, 0x21, 0xc8, //0x00001434 andl         %ecx, %r8d
	0x45, 0x85, 0xd0, //0x00001437 testl        %r10d, %r8d
	0x0f, 0x85, 0x85, 0x00, 0x00, 0x00, //0x0000143a jne          LBB0_371
	0x41, 0xb8, 0xe0, 0xe0, 0xe0, 0xe0, //0x00001440 movl         $-522133280, %r8d
	0x41, 0x29, 0xc0, //0x00001446 subl         %eax, %r8d
	0x05, 0x39, 0x39, 0x39, 0x39, //0x00001449 addl         $960051513, %eax
	0x44, 0x21, 0xc1, //0x0000144e andl         %r8d, %ecx
	0x85, 0xc1, //0x00001451 testl        %eax, %ecx
	0x0f, 0x85, 0x6c, 0x00, 0x00, 0x00, //0x00001453 jne          LBB0_371
	0x0f, 0xca, //0x00001459 bswapl       %edx
	0x89, 0xd0, //0x0000145b movl         %edx, %eax
	0xc1, 0xe8, 0x04, //0x0000145d shrl         $4, %eax
	0xf7, 0xd0, //0x00001460 notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x00001462 andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x00001467 leal         (%rax,%rax,8), %eax
	0x81, 0xe2, 0x0f, 0x0f, 0x0f, 0x0f, //0x0000146a andl         $252645135, %edx
	0x01, 0xc2, //0x00001470 addl         %eax, %edx
	0x89, 0xd1, //0x00001472 movl         %edx, %ecx
	0xc1, 0xe9, 0x04, //0x00001474 shrl         $4, %ecx
	0x09, 0xd1, //0x00001477 orl          %edx, %ecx
	0x89, 0xc8, //0x00001479 movl         %ecx, %eax
	0xc1, 0xe8, 0x08, //0x0000147b shrl         $8, %eax
	0x25, 0x00, 0xff, 0x00, 0x00, //0x0000147e andl         $65280, %eax
	0x89, 0xca, //0x00001483 movl         %ecx, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x00001485 andl         $128, %edx
	0x48, 0x83, 0xc7, 0x06, //0x0000148b addq         $6, %rdi
	0x09, 0xc2, //0x0000148f orl          %eax, %edx
	0x49, 0x89, 0xfc, //0x00001491 movq         %rdi, %r12
	0x48, 0x89, 0xfe, //0x00001494 movq         %rdi, %rsi
	0x0f, 0x85, 0x28, 0x00, 0x00, 0x00, //0x00001497 jne          LBB0_371
	//0x0000149d LBB0_323
	0x80, 0xf9, 0x0a, //0x0000149d cmpb         $10, %cl
	0x0f, 0x84, 0xe3, 0xfe, 0xff, 0xff, //0x000014a0 je           LBB0_326
	0x80, 0xf9, 0x0d, //0x000014a6 cmpb         $13, %cl
	0x0f, 0x84, 0xda, 0xfe, 0xff, 0xff, //0x000014a9 je           LBB0_326
	0x80, 0xf9, 0x3d, //0x000014af cmpb         $61, %cl
	0x0f, 0x85, 0x0a, 0x00, 0x00, 0x00, //0x000014b2 jne          LBB0_370
	0x83, 0x7d, 0x98, 0x02, //0x000014b8 cmpl         $2, $-104(%rbp)
	0x0f, 0x85, 0x3d, 0x04, 0x00, 0x00, //0x000014bc jne          LBB0_403
	//0x000014c2 LBB0_370
	0x4c, 0x89, 0xe6, //0x000014c2 movq         %r12, %rsi
	//0x000014c5 LBB0_371
	0x31, 0xc9, //0x000014c5 xorl         %ecx, %ecx
	0x4c, 0x39, 0xce, //0x000014c7 cmpq         %r9, %rsi
	0x0f, 0x94, 0xc1, //0x000014ca sete         %cl
	0x48, 0x01, 0xf1, //0x000014cd addq         %rsi, %rcx
	0x4c, 0x39, 0xf1, //0x000014d0 cmpq         %r14, %rcx
	0x44, 0x8b, 0x65, 0xc4, //0x000014d3 movl         $-60(%rbp), %r12d
	0x4c, 0x89, 0xdf, //0x000014d7 movq         %r11, %rdi
	0x4c, 0x8b, 0x6d, 0xb0, //0x000014da movq         $-80(%rbp), %r13
	0x0f, 0x85, 0xf2, 0x11, 0x00, 0x00, //0x000014de jne          LBB0_643
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000014e4 .p2align 4, 0x90
	//0x000014f0 LBB0_372
	0x4d, 0x39, 0xfe, //0x000014f0 cmpq         %r15, %r14
	0x0f, 0x87, 0x80, 0x05, 0x00, 0x00, //0x000014f3 ja           LBB0_434
	0x4c, 0x39, 0xef, //0x000014f9 cmpq         %r13, %rdi
	0x0f, 0x86, 0xce, 0xf8, 0xff, 0xff, //0x000014fc jbe          LBB0_217
	0xe9, 0x72, 0x05, 0x00, 0x00, //0x00001502 jmp          LBB0_434
	//0x00001507 LBB0_327
	0x48, 0x83, 0xc7, 0x01, //0x00001507 addq         $1, %rdi
	0x48, 0x83, 0xc1, 0x01, //0x0000150b addq         $1, %rcx
	0x48, 0x39, 0xc8, //0x0000150f cmpq         %rcx, %rax
	0x0f, 0x84, 0x13, 0xfc, 0xff, 0xff, //0x00001512 je           LBB0_267
	//0x00001518 LBB0_328
	0x0f, 0xb6, 0x57, 0xff, //0x00001518 movzbl       $-1(%rdi), %edx
	0x80, 0xfa, 0x0d, //0x0000151c cmpb         $13, %dl
	0x0f, 0x84, 0xe2, 0xff, 0xff, 0xff, //0x0000151f je           LBB0_327
	0x80, 0xfa, 0x0a, //0x00001525 cmpb         $10, %dl
	0x0f, 0x84, 0xd9, 0xff, 0xff, 0xff, //0x00001528 je           LBB0_327
	0x48, 0x89, 0xfe, //0x0000152e movq         %rdi, %rsi
	0xe9, 0x8f, 0xff, 0xff, 0xff, //0x00001531 jmp          LBB0_371
	//0x00001536 LBB0_331
	0x48, 0x89, 0xce, //0x00001536 movq         %rcx, %rsi
	0xe9, 0x75, 0x00, 0x00, 0x00, //0x00001539 jmp          LBB0_343
	//0x0000153e LBB0_332
	0x48, 0x83, 0xc6, 0x01, //0x0000153e addq         $1, %rsi
	0xbb, 0x03, 0x00, 0x00, 0x00, //0x00001542 movl         $3, %ebx
	0x4c, 0x39, 0xce, //0x00001547 cmpq         %r9, %rsi
	0x0f, 0x83, 0xff, 0xfa, 0xff, 0xff, //0x0000154a jae          LBB0_250
	//0x00001550 LBB0_333
	0x44, 0x0f, 0xb6, 0x06, //0x00001550 movzbl       (%rsi), %r8d
	0x49, 0x83, 0xf8, 0x0d, //0x00001554 cmpq         $13, %r8
	0x0f, 0x84, 0xe0, 0xff, 0xff, 0xff, //0x00001558 je           LBB0_332
	0x41, 0x80, 0xf8, 0x0a, //0x0000155e cmpb         $10, %r8b
	0x0f, 0x84, 0xd6, 0xff, 0xff, 0xff, //0x00001562 je           LBB0_332
	0x48, 0x8b, 0x45, 0xd0, //0x00001568 movq         $-48(%rbp), %rax
	0x42, 0x0f, 0xb6, 0x04, 0x00, //0x0000156c movzbl       (%rax,%r8), %eax
	0x48, 0x83, 0xc6, 0x01, //0x00001571 addq         $1, %rsi
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00001575 cmpl         $255, %eax
	0x0f, 0x85, 0xd7, 0x04, 0x00, 0x00, //0x0000157a jne          LBB0_428
	//0x00001580 LBB0_429
	0xbb, 0x03, 0x00, 0x00, 0x00, //0x00001580 movl         $3, %ebx
	0xe9, 0xfc, 0xfb, 0xff, 0xff, //0x00001585 jmp          LBB0_275
	//0x0000158a LBB0_337
	0x48, 0x89, 0xc6, //0x0000158a movq         %rax, %rsi
	0xe9, 0x06, 0x00, 0x00, 0x00, //0x0000158d jmp          LBB0_339
	//0x00001592 LBB0_338
	0x48, 0x89, 0xc6, //0x00001592 movq         %rax, %rsi
	0x4c, 0x89, 0xdf, //0x00001595 movq         %r11, %rdi
	//0x00001598 LBB0_339
	0x44, 0x89, 0xfa, //0x00001598 movl         %r15d, %edx
	0x4c, 0x8b, 0x7d, 0xc8, //0x0000159b movq         $-56(%rbp), %r15
	0xe9, 0x0f, 0x00, 0x00, 0x00, //0x0000159f jmp          LBB0_343
	//0x000015a4 LBB0_340
	0x80, 0xf9, 0x2f, //0x000015a4 cmpb         $47, %cl
	0x0f, 0x85, 0x03, 0x00, 0x00, 0x00, //0x000015a7 jne          LBB0_342
	0x41, 0xb0, 0x2f, //0x000015ad movb         $47, %r8b
	//0x000015b0 LBB0_342
	0x48, 0x89, 0xc6, //0x000015b0 movq         %rax, %rsi
	//0x000015b3 LBB0_343
	0x41, 0x0f, 0xb6, 0xc0, //0x000015b3 movzbl       %r8b, %eax
	0x48, 0x8b, 0x4d, 0xd0, //0x000015b7 movq         $-48(%rbp), %rcx
	0x0f, 0xb6, 0x04, 0x01, //0x000015bb movzbl       (%rcx,%rax), %eax
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x000015bf cmpl         $255, %eax
	0x0f, 0x84, 0x61, 0x01, 0x00, 0x00, //0x000015c4 je           LBB0_363
	0xc1, 0xe2, 0x06, //0x000015ca shll         $6, %edx
	0x09, 0xc2, //0x000015cd orl          %eax, %edx
	0xbb, 0x02, 0x00, 0x00, 0x00, //0x000015cf movl         $2, %ebx
	0x4c, 0x39, 0xce, //0x000015d4 cmpq         %r9, %rsi
	0x0f, 0x82, 0x1f, 0x00, 0x00, 0x00, //0x000015d7 jb           LBB0_347
	0xe9, 0x1f, 0xfb, 0xff, 0xff, //0x000015dd jmp          LBB0_264
	//0x000015e2 LBB0_361
	0x80, 0xf9, 0x6e, //0x000015e2 cmpb         $110, %cl
	0x0f, 0x85, 0x7c, 0x01, 0x00, 0x00, //0x000015e5 jne          LBB0_377
	//0x000015eb LBB0_345
	0x48, 0x89, 0xc6, //0x000015eb movq         %rax, %rsi
	//0x000015ee LBB0_346
	0xbb, 0x02, 0x00, 0x00, 0x00, //0x000015ee movl         $2, %ebx
	0x4c, 0x39, 0xce, //0x000015f3 cmpq         %r9, %rsi
	0x0f, 0x83, 0x53, 0xfa, 0xff, 0xff, //0x000015f6 jae          LBB0_250
	//0x000015fc LBB0_347
	0x48, 0x8d, 0x4e, 0x01, //0x000015fc leaq         $1(%rsi), %rcx
	0x0f, 0xb6, 0x06, //0x00001600 movzbl       (%rsi), %eax
	0x3c, 0x5c, //0x00001603 cmpb         $92, %al
	0x0f, 0x85, 0x05, 0x01, 0x00, 0x00, //0x00001605 jne          LBB0_359
	0x48, 0x8d, 0x46, 0x02, //0x0000160b leaq         $2(%rsi), %rax
	0x41, 0xb0, 0xff, //0x0000160f movb         $-1, %r8b
	0x4c, 0x39, 0xc8, //0x00001612 cmpq         %r9, %rax
	0x0f, 0x87, 0x2a, 0x01, 0x00, 0x00, //0x00001615 ja           LBB0_367
	0x0f, 0xb6, 0x09, //0x0000161b movzbl       (%rcx), %ecx
	0x80, 0xf9, 0x71, //0x0000161e cmpb         $113, %cl
	0x0f, 0x8e, 0xbb, 0xff, 0xff, 0xff, //0x00001621 jle          LBB0_361
	0x80, 0xf9, 0x72, //0x00001627 cmpb         $114, %cl
	0x0f, 0x84, 0xbb, 0xff, 0xff, 0xff, //0x0000162a je           LBB0_345
	0x80, 0xf9, 0x75, //0x00001630 cmpb         $117, %cl
	0x0f, 0x85, 0x3a, 0x01, 0x00, 0x00, //0x00001633 jne          LBB0_379
	0x4c, 0x89, 0xc9, //0x00001639 movq         %r9, %rcx
	0x48, 0x29, 0xc1, //0x0000163c subq         %rax, %rcx
	0x48, 0x83, 0xf9, 0x04, //0x0000163f cmpq         $4, %rcx
	0x0f, 0x8c, 0x2a, 0x01, 0x00, 0x00, //0x00001643 jl           LBB0_379
	0x41, 0x89, 0xd7, //0x00001649 movl         %edx, %r15d
	0x8b, 0x08, //0x0000164c movl         (%rax), %ecx
	0x89, 0xcb, //0x0000164e movl         %ecx, %ebx
	0xf7, 0xd3, //0x00001650 notl         %ebx
	0x8d, 0x91, 0xd0, 0xcf, 0xcf, 0xcf, //0x00001652 leal         $-808464432(%rcx), %edx
	0x81, 0xe3, 0x80, 0x80, 0x80, 0x80, //0x00001658 andl         $-2139062144, %ebx
	0x85, 0xd3, //0x0000165e testl        %edx, %ebx
	0x0f, 0x85, 0xe7, 0x00, 0x00, 0x00, //0x00001660 jne          LBB0_374
	0x8d, 0x91, 0x19, 0x19, 0x19, 0x19, //0x00001666 leal         $421075225(%rcx), %edx
	0x09, 0xca, //0x0000166c orl          %ecx, %edx
	0xf7, 0xc2, 0x80, 0x80, 0x80, 0x80, //0x0000166e testl        $-2139062144, %edx
	0x0f, 0x85, 0xd3, 0x00, 0x00, 0x00, //0x00001674 jne          LBB0_374
	0x89, 0xca, //0x0000167a movl         %ecx, %edx
	0x81, 0xe2, 0x7f, 0x7f, 0x7f, 0x7f, //0x0000167c andl         $2139062143, %edx
	0xbf, 0xc0, 0xc0, 0xc0, 0xc0, //0x00001682 movl         $-1061109568, %edi
	0x29, 0xd7, //0x00001687 subl         %edx, %edi
	0x44, 0x8d, 0x92, 0x46, 0x46, 0x46, 0x46, //0x00001689 leal         $1179010630(%rdx), %r10d
	0x21, 0xdf, //0x00001690 andl         %ebx, %edi
	0x44, 0x85, 0xd7, //0x00001692 testl        %r10d, %edi
	0x0f, 0x85, 0xba, 0x00, 0x00, 0x00, //0x00001695 jne          LBB0_375
	0xbf, 0xe0, 0xe0, 0xe0, 0xe0, //0x0000169b movl         $-522133280, %edi
	0x29, 0xd7, //0x000016a0 subl         %edx, %edi
	0x81, 0xc2, 0x39, 0x39, 0x39, 0x39, //0x000016a2 addl         $960051513, %edx
	0x21, 0xfb, //0x000016a8 andl         %edi, %ebx
	0x85, 0xd3, //0x000016aa testl        %edx, %ebx
	0x0f, 0x85, 0xa3, 0x00, 0x00, 0x00, //0x000016ac jne          LBB0_375
	0x0f, 0xc9, //0x000016b2 bswapl       %ecx
	0x89, 0xc8, //0x000016b4 movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x000016b6 shrl         $4, %eax
	0xf7, 0xd0, //0x000016b9 notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x000016bb andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x000016c0 leal         (%rax,%rax,8), %eax
	0x81, 0xe1, 0x0f, 0x0f, 0x0f, 0x0f, //0x000016c3 andl         $252645135, %ecx
	0x01, 0xc1, //0x000016c9 addl         %eax, %ecx
	0x89, 0xc8, //0x000016cb movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x000016cd shrl         $4, %eax
	0x09, 0xc8, //0x000016d0 orl          %ecx, %eax
	0x89, 0xc1, //0x000016d2 movl         %eax, %ecx
	0xc1, 0xe9, 0x08, //0x000016d4 shrl         $8, %ecx
	0x81, 0xe1, 0x00, 0xff, 0x00, 0x00, //0x000016d7 andl         $65280, %ecx
	0x89, 0xc2, //0x000016dd movl         %eax, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x000016df andl         $128, %edx
	0x48, 0x83, 0xc6, 0x06, //0x000016e5 addq         $6, %rsi
	0x09, 0xca, //0x000016e9 orl          %ecx, %edx
	0x4c, 0x89, 0xdf, //0x000016eb movq         %r11, %rdi
	0x44, 0x89, 0xfa, //0x000016ee movl         %r15d, %edx
	0x4c, 0x8b, 0x7d, 0xc8, //0x000016f1 movq         $-56(%rbp), %r15
	0x0f, 0x85, 0x7b, 0x00, 0x00, 0x00, //0x000016f5 jne          LBB0_380
	0x3c, 0x0d, //0x000016fb cmpb         $13, %al
	0x0f, 0x85, 0x18, 0x00, 0x00, 0x00, //0x000016fd jne          LBB0_360
	0xe9, 0xe6, 0xfe, 0xff, 0xff, //0x00001703 jmp          LBB0_346
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001708 .p2align 4, 0x90
	//0x00001710 LBB0_359
	0x48, 0x89, 0xce, //0x00001710 movq         %rcx, %rsi
	0x3c, 0x0d, //0x00001713 cmpb         $13, %al
	0x0f, 0x84, 0xd3, 0xfe, 0xff, 0xff, //0x00001715 je           LBB0_346
	//0x0000171b LBB0_360
	0x41, 0x89, 0xc0, //0x0000171b movl         %eax, %r8d
	0x3c, 0x0a, //0x0000171e cmpb         $10, %al
	0x0f, 0x84, 0xc8, 0xfe, 0xff, 0xff, //0x00001720 je           LBB0_346
	0xe9, 0x4b, 0x00, 0x00, 0x00, //0x00001726 jmp          LBB0_380
	//0x0000172b LBB0_363
	0xbb, 0x01, 0x00, 0x00, 0x00, //0x0000172b movl         $1, %ebx
	0xe9, 0x51, 0xfa, 0xff, 0xff, //0x00001730 jmp          LBB0_275
	//0x00001735 LBB0_364
	0x4c, 0x89, 0xce, //0x00001735 movq         %r9, %rsi
	0xe9, 0xee, 0xf9, 0xff, 0xff, //0x00001738 jmp          LBB0_267
	//0x0000173d LBB0_366
	0x48, 0x89, 0xfe, //0x0000173d movq         %rdi, %rsi
	0xe9, 0xe6, 0xf9, 0xff, 0xff, //0x00001740 jmp          LBB0_267
	//0x00001745 LBB0_367
	0x48, 0x89, 0xce, //0x00001745 movq         %rcx, %rsi
	0xe9, 0x29, 0x00, 0x00, 0x00, //0x00001748 jmp          LBB0_380
	//0x0000174d LBB0_374
	0x48, 0x89, 0xc6, //0x0000174d movq         %rax, %rsi
	0xe9, 0x06, 0x00, 0x00, 0x00, //0x00001750 jmp          LBB0_376
	//0x00001755 LBB0_375
	0x48, 0x89, 0xc6, //0x00001755 movq         %rax, %rsi
	0x4c, 0x89, 0xdf, //0x00001758 movq         %r11, %rdi
	//0x0000175b LBB0_376
	0x44, 0x89, 0xfa, //0x0000175b movl         %r15d, %edx
	0x4c, 0x8b, 0x7d, 0xc8, //0x0000175e movq         $-56(%rbp), %r15
	0xe9, 0x0f, 0x00, 0x00, 0x00, //0x00001762 jmp          LBB0_380
	//0x00001767 LBB0_377
	0x80, 0xf9, 0x2f, //0x00001767 cmpb         $47, %cl
	0x0f, 0x85, 0x03, 0x00, 0x00, 0x00, //0x0000176a jne          LBB0_379
	0x41, 0xb0, 0x2f, //0x00001770 movb         $47, %r8b
	//0x00001773 LBB0_379
	0x48, 0x89, 0xc6, //0x00001773 movq         %rax, %rsi
	//0x00001776 LBB0_380
	0x41, 0x0f, 0xb6, 0xc0, //0x00001776 movzbl       %r8b, %eax
	0x48, 0x8b, 0x4d, 0xd0, //0x0000177a movq         $-48(%rbp), %rcx
	0x0f, 0xb6, 0x04, 0x01, //0x0000177e movzbl       (%rcx,%rax), %eax
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00001782 cmpl         $255, %eax
	0x0f, 0x84, 0x51, 0x01, 0x00, 0x00, //0x00001787 je           LBB0_400
	0xc1, 0xe2, 0x06, //0x0000178d shll         $6, %edx
	0x09, 0xc2, //0x00001790 orl          %eax, %edx
	0xbb, 0x03, 0x00, 0x00, 0x00, //0x00001792 movl         $3, %ebx
	0x4c, 0x39, 0xce, //0x00001797 cmpq         %r9, %rsi
	0x0f, 0x82, 0x1f, 0x00, 0x00, 0x00, //0x0000179a jb           LBB0_384
	0xe9, 0x5c, 0xf9, 0xff, 0xff, //0x000017a0 jmp          LBB0_264
	//0x000017a5 LBB0_398
	0x80, 0xf9, 0x6e, //0x000017a5 cmpb         $110, %cl
	0x0f, 0x85, 0x83, 0x02, 0x00, 0x00, //0x000017a8 jne          LBB0_424
	//0x000017ae LBB0_382
	0x48, 0x89, 0xc6, //0x000017ae movq         %rax, %rsi
	//0x000017b1 LBB0_383
	0xbb, 0x03, 0x00, 0x00, 0x00, //0x000017b1 movl         $3, %ebx
	0x4c, 0x39, 0xce, //0x000017b6 cmpq         %r9, %rsi
	0x0f, 0x83, 0x90, 0xf8, 0xff, 0xff, //0x000017b9 jae          LBB0_250
	//0x000017bf LBB0_384
	0x48, 0x8d, 0x4e, 0x01, //0x000017bf leaq         $1(%rsi), %rcx
	0x0f, 0xb6, 0x06, //0x000017c3 movzbl       (%rsi), %eax
	0x3c, 0x5c, //0x000017c6 cmpb         $92, %al
	0x0f, 0x85, 0xf5, 0x00, 0x00, 0x00, //0x000017c8 jne          LBB0_395
	0x48, 0x8d, 0x46, 0x02, //0x000017ce leaq         $2(%rsi), %rax
	0x41, 0xb0, 0xff, //0x000017d2 movb         $-1, %r8b
	0x4c, 0x39, 0xc8, //0x000017d5 cmpq         %r9, %rax
	0x0f, 0x87, 0x19, 0x01, 0x00, 0x00, //0x000017d8 ja           LBB0_402
	0x0f, 0xb6, 0x09, //0x000017de movzbl       (%rcx), %ecx
	0x80, 0xf9, 0x71, //0x000017e1 cmpb         $113, %cl
	0x0f, 0x8e, 0xbb, 0xff, 0xff, 0xff, //0x000017e4 jle          LBB0_398
	0x80, 0xf9, 0x72, //0x000017ea cmpb         $114, %cl
	0x0f, 0x84, 0xbb, 0xff, 0xff, 0xff, //0x000017ed je           LBB0_382
	0x80, 0xf9, 0x75, //0x000017f3 cmpb         $117, %cl
	0x0f, 0x85, 0x41, 0x02, 0x00, 0x00, //0x000017f6 jne          LBB0_426
	0x4c, 0x89, 0xc9, //0x000017fc movq         %r9, %rcx
	0x48, 0x29, 0xc1, //0x000017ff subq         %rax, %rcx
	0x48, 0x83, 0xf9, 0x04, //0x00001802 cmpq         $4, %rcx
	0x0f, 0x8c, 0x31, 0x02, 0x00, 0x00, //0x00001806 jl           LBB0_426
	0x41, 0x89, 0xd7, //0x0000180c movl         %edx, %r15d
	0x8b, 0x08, //0x0000180f movl         (%rax), %ecx
	0x89, 0xcb, //0x00001811 movl         %ecx, %ebx
	0xf7, 0xd3, //0x00001813 notl         %ebx
	0x8d, 0x91, 0xd0, 0xcf, 0xcf, 0xcf, //0x00001815 leal         $-808464432(%rcx), %edx
	0x81, 0xe3, 0x80, 0x80, 0x80, 0x80, //0x0000181b andl         $-2139062144, %ebx
	0x85, 0xd3, //0x00001821 testl        %edx, %ebx
	0x0f, 0x85, 0xbf, 0x00, 0x00, 0x00, //0x00001823 jne          LBB0_401
	0x8d, 0x91, 0x19, 0x19, 0x19, 0x19, //0x00001829 leal         $421075225(%rcx), %edx
	0x09, 0xca, //0x0000182f orl          %ecx, %edx
	0xf7, 0xc2, 0x80, 0x80, 0x80, 0x80, //0x00001831 testl        $-2139062144, %edx
	0x0f, 0x85, 0xab, 0x00, 0x00, 0x00, //0x00001837 jne          LBB0_401
	0x89, 0xca, //0x0000183d movl         %ecx, %edx
	0x81, 0xe2, 0x7f, 0x7f, 0x7f, 0x7f, //0x0000183f andl         $2139062143, %edx
	0xbf, 0xc0, 0xc0, 0xc0, 0xc0, //0x00001845 movl         $-1061109568, %edi
	0x29, 0xd7, //0x0000184a subl         %edx, %edi
	0x44, 0x8d, 0x92, 0x46, 0x46, 0x46, 0x46, //0x0000184c leal         $1179010630(%rdx), %r10d
	0x21, 0xdf, //0x00001853 andl         %ebx, %edi
	0x44, 0x85, 0xd7, //0x00001855 testl        %r10d, %edi
	0x0f, 0x85, 0x8a, 0x00, 0x00, 0x00, //0x00001858 jne          LBB0_401
	0xbf, 0xe0, 0xe0, 0xe0, 0xe0, //0x0000185e movl         $-522133280, %edi
	0x29, 0xd7, //0x00001863 subl         %edx, %edi
	0x81, 0xc2, 0x39, 0x39, 0x39, 0x39, //0x00001865 addl         $960051513, %edx
	0x21, 0xfb, //0x0000186b andl         %edi, %ebx
	0x85, 0xd3, //0x0000186d testl        %edx, %ebx
	0x0f, 0x85, 0x73, 0x00, 0x00, 0x00, //0x0000186f jne          LBB0_401
	0x0f, 0xc9, //0x00001875 bswapl       %ecx
	0x89, 0xc8, //0x00001877 movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x00001879 shrl         $4, %eax
	0xf7, 0xd0, //0x0000187c notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x0000187e andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x00001883 leal         (%rax,%rax,8), %eax
	0x81, 0xe1, 0x0f, 0x0f, 0x0f, 0x0f, //0x00001886 andl         $252645135, %ecx
	0x01, 0xc1, //0x0000188c addl         %eax, %ecx
	0x89, 0xc8, //0x0000188e movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x00001890 shrl         $4, %eax
	0x09, 0xc8, //0x00001893 orl          %ecx, %eax
	0x89, 0xc1, //0x00001895 movl         %eax, %ecx
	0xc1, 0xe9, 0x08, //0x00001897 shrl         $8, %ecx
	0x81, 0xe1, 0x00, 0xff, 0x00, 0x00, //0x0000189a andl         $65280, %ecx
	0x89, 0xc2, //0x000018a0 movl         %eax, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x000018a2 andl         $128, %edx
	0x48, 0x83, 0xc6, 0x06, //0x000018a8 addq         $6, %rsi
	0x09, 0xca, //0x000018ac orl          %ecx, %edx
	0x4c, 0x89, 0xdf, //0x000018ae movq         %r11, %rdi
	0x44, 0x89, 0xfa, //0x000018b1 movl         %r15d, %edx
	0x4c, 0x8b, 0x7d, 0xc8, //0x000018b4 movq         $-56(%rbp), %r15
	0x0f, 0x84, 0x08, 0x00, 0x00, 0x00, //0x000018b8 je           LBB0_396
	0xe9, 0x7d, 0x01, 0x00, 0x00, //0x000018be jmp          LBB0_427
	//0x000018c3 LBB0_395
	0x48, 0x89, 0xce, //0x000018c3 movq         %rcx, %rsi
	//0x000018c6 LBB0_396
	0x3c, 0x0d, //0x000018c6 cmpb         $13, %al
	0x0f, 0x84, 0xe3, 0xfe, 0xff, 0xff, //0x000018c8 je           LBB0_383
	0x41, 0x89, 0xc0, //0x000018ce movl         %eax, %r8d
	0x3c, 0x0a, //0x000018d1 cmpb         $10, %al
	0x0f, 0x84, 0xd8, 0xfe, 0xff, 0xff, //0x000018d3 je           LBB0_383
	0xe9, 0x62, 0x01, 0x00, 0x00, //0x000018d9 jmp          LBB0_427
	//0x000018de LBB0_400
	0xbb, 0x02, 0x00, 0x00, 0x00, //0x000018de movl         $2, %ebx
	0xe9, 0x9e, 0xf8, 0xff, 0xff, //0x000018e3 jmp          LBB0_275
	//0x000018e8 LBB0_401
	0x48, 0x89, 0xc6, //0x000018e8 movq         %rax, %rsi
	0x44, 0x89, 0xfa, //0x000018eb movl         %r15d, %edx
	0x4c, 0x8b, 0x7d, 0xc8, //0x000018ee movq         $-56(%rbp), %r15
	0xe9, 0x49, 0x01, 0x00, 0x00, //0x000018f2 jmp          LBB0_427
	//0x000018f7 LBB0_402
	0x48, 0x89, 0xce, //0x000018f7 movq         %rcx, %rsi
	0xe9, 0x41, 0x01, 0x00, 0x00, //0x000018fa jmp          LBB0_427
	//0x000018ff LBB0_403
	0x4d, 0x39, 0xcc, //0x000018ff cmpq         %r9, %r12
	0x0f, 0x82, 0x14, 0x00, 0x00, 0x00, //0x00001902 jb           LBB0_407
	//0x00001908 LBB0_404
	0x4c, 0x89, 0xe6, //0x00001908 movq         %r12, %rsi
	0xe9, 0x1b, 0xf8, 0xff, 0xff, //0x0000190b jmp          LBB0_267
	//0x00001910 LBB0_405
	0x49, 0x89, 0xf4, //0x00001910 movq         %rsi, %r12
	//0x00001913 LBB0_406
	0x4d, 0x39, 0xcc, //0x00001913 cmpq         %r9, %r12
	0x0f, 0x83, 0xec, 0xff, 0xff, 0xff, //0x00001916 jae          LBB0_404
	//0x0000191c LBB0_407
	0x49, 0x8d, 0x44, 0x24, 0x01, //0x0000191c leaq         $1(%r12), %rax
	0x41, 0x0f, 0xb6, 0x0c, 0x24, //0x00001921 movzbl       (%r12), %ecx
	0x80, 0xf9, 0x5c, //0x00001926 cmpb         $92, %cl
	0x0f, 0x85, 0xe5, 0x00, 0x00, 0x00, //0x00001929 jne          LBB0_418
	0x49, 0x8d, 0x74, 0x24, 0x02, //0x0000192f leaq         $2(%r12), %rsi
	0x4c, 0x39, 0xce, //0x00001934 cmpq         %r9, %rsi
	0x0f, 0x87, 0x29, 0x01, 0x00, 0x00, //0x00001937 ja           LBB0_431
	0x0f, 0xb6, 0x00, //0x0000193d movzbl       (%rax), %eax
	0x3c, 0x6e, //0x00001940 cmpb         $110, %al
	0x0f, 0x84, 0xc8, 0xff, 0xff, 0xff, //0x00001942 je           LBB0_405
	0x3c, 0x72, //0x00001948 cmpb         $114, %al
	0x0f, 0x84, 0xc0, 0xff, 0xff, 0xff, //0x0000194a je           LBB0_405
	0x3c, 0x75, //0x00001950 cmpb         $117, %al
	0x0f, 0x85, 0x6d, 0xfb, 0xff, 0xff, //0x00001952 jne          LBB0_371
	0x4c, 0x89, 0xc8, //0x00001958 movq         %r9, %rax
	0x48, 0x29, 0xf0, //0x0000195b subq         %rsi, %rax
	0x48, 0x83, 0xf8, 0x04, //0x0000195e cmpq         $4, %rax
	0x0f, 0x8c, 0x5d, 0xfb, 0xff, 0xff, //0x00001962 jl           LBB0_371
	0x8b, 0x06, //0x00001968 movl         (%rsi), %eax
	0x89, 0xc1, //0x0000196a movl         %eax, %ecx
	0xf7, 0xd1, //0x0000196c notl         %ecx
	0x8d, 0x90, 0xd0, 0xcf, 0xcf, 0xcf, //0x0000196e leal         $-808464432(%rax), %edx
	0x81, 0xe1, 0x80, 0x80, 0x80, 0x80, //0x00001974 andl         $-2139062144, %ecx
	0x85, 0xd1, //0x0000197a testl        %edx, %ecx
	0x0f, 0x85, 0x43, 0xfb, 0xff, 0xff, //0x0000197c jne          LBB0_371
	0x8d, 0x90, 0x19, 0x19, 0x19, 0x19, //0x00001982 leal         $421075225(%rax), %edx
	0x09, 0xc2, //0x00001988 orl          %eax, %edx
	0xf7, 0xc2, 0x80, 0x80, 0x80, 0x80, //0x0000198a testl        $-2139062144, %edx
	0x0f, 0x85, 0x2f, 0xfb, 0xff, 0xff, //0x00001990 jne          LBB0_371
	0x89, 0xc2, //0x00001996 movl         %eax, %edx
	0x81, 0xe2, 0x7f, 0x7f, 0x7f, 0x7f, //0x00001998 andl         $2139062143, %edx
	0xbf, 0xc0, 0xc0, 0xc0, 0xc0, //0x0000199e movl         $-1061109568, %edi
	0x29, 0xd7, //0x000019a3 subl         %edx, %edi
	0x44, 0x8d, 0x82, 0x46, 0x46, 0x46, 0x46, //0x000019a5 leal         $1179010630(%rdx), %r8d
	0x21, 0xcf, //0x000019ac andl         %ecx, %edi
	0x44, 0x85, 0xc7, //0x000019ae testl        %r8d, %edi
	0x0f, 0x85, 0x0e, 0xfb, 0xff, 0xff, //0x000019b1 jne          LBB0_371
	0xbf, 0xe0, 0xe0, 0xe0, 0xe0, //0x000019b7 movl         $-522133280, %edi
	0x29, 0xd7, //0x000019bc subl         %edx, %edi
	0x81, 0xc2, 0x39, 0x39, 0x39, 0x39, //0x000019be addl         $960051513, %edx
	0x21, 0xf9, //0x000019c4 andl         %edi, %ecx
	0x85, 0xd1, //0x000019c6 testl        %edx, %ecx
	0x0f, 0x85, 0xf7, 0xfa, 0xff, 0xff, //0x000019c8 jne          LBB0_371
	0x0f, 0xc8, //0x000019ce bswapl       %eax
	0x89, 0xc1, //0x000019d0 movl         %eax, %ecx
	0xc1, 0xe9, 0x04, //0x000019d2 shrl         $4, %ecx
	0xf7, 0xd1, //0x000019d5 notl         %ecx
	0x81, 0xe1, 0x01, 0x01, 0x01, 0x01, //0x000019d7 andl         $16843009, %ecx
	0x8d, 0x0c, 0xc9, //0x000019dd leal         (%rcx,%rcx,8), %ecx
	0x25, 0x0f, 0x0f, 0x0f, 0x0f, //0x000019e0 andl         $252645135, %eax
	0x01, 0xc8, //0x000019e5 addl         %ecx, %eax
	0x89, 0xc1, //0x000019e7 movl         %eax, %ecx
	0xc1, 0xe9, 0x04, //0x000019e9 shrl         $4, %ecx
	0x09, 0xc1, //0x000019ec orl          %eax, %ecx
	0x89, 0xc8, //0x000019ee movl         %ecx, %eax
	0xc1, 0xe8, 0x08, //0x000019f0 shrl         $8, %eax
	0x25, 0x00, 0xff, 0x00, 0x00, //0x000019f3 andl         $65280, %eax
	0x89, 0xca, //0x000019f8 movl         %ecx, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x000019fa andl         $128, %edx
	0x49, 0x83, 0xc4, 0x06, //0x00001a00 addq         $6, %r12
	0x09, 0xc2, //0x00001a04 orl          %eax, %edx
	0x4c, 0x89, 0xe6, //0x00001a06 movq         %r12, %rsi
	0x0f, 0x84, 0x08, 0x00, 0x00, 0x00, //0x00001a09 je           LBB0_419
	0xe9, 0xb1, 0xfa, 0xff, 0xff, //0x00001a0f jmp          LBB0_371
	//0x00001a14 LBB0_418
	0x48, 0x89, 0xc6, //0x00001a14 movq         %rax, %rsi
	//0x00001a17 LBB0_419
	0x80, 0xf9, 0x0d, //0x00001a17 cmpb         $13, %cl
	0x0f, 0x84, 0xf0, 0xfe, 0xff, 0xff, //0x00001a1a je           LBB0_405
	0x49, 0x89, 0xf4, //0x00001a20 movq         %rsi, %r12
	0x80, 0xf9, 0x0a, //0x00001a23 cmpb         $10, %cl
	0x0f, 0x84, 0xe7, 0xfe, 0xff, 0xff, //0x00001a26 je           LBB0_406
	0xe9, 0x94, 0xfa, 0xff, 0xff, //0x00001a2c jmp          LBB0_371
	//0x00001a31 LBB0_424
	0x80, 0xf9, 0x2f, //0x00001a31 cmpb         $47, %cl
	0x0f, 0x85, 0x03, 0x00, 0x00, 0x00, //0x00001a34 jne          LBB0_426
	0x41, 0xb0, 0x2f, //0x00001a3a movb         $47, %r8b
	//0x00001a3d LBB0_426
	0x48, 0x89, 0xc6, //0x00001a3d movq         %rax, %rsi
	//0x00001a40 LBB0_427
	0x41, 0x0f, 0xb6, 0xc0, //0x00001a40 movzbl       %r8b, %eax
	0x48, 0x8b, 0x4d, 0xd0, //0x00001a44 movq         $-48(%rbp), %rcx
	0x0f, 0xb6, 0x04, 0x01, //0x00001a48 movzbl       (%rcx,%rax), %eax
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00001a4c cmpl         $255, %eax
	0x0f, 0x84, 0x29, 0xfb, 0xff, 0xff, //0x00001a51 je           LBB0_429
	//0x00001a57 LBB0_428
	0xc1, 0xe2, 0x06, //0x00001a57 shll         $6, %edx
	0x09, 0xc2, //0x00001a5a orl          %eax, %edx
	0xbb, 0x04, 0x00, 0x00, 0x00, //0x00001a5c movl         $4, %ebx
	0xe9, 0x9b, 0xf6, 0xff, 0xff, //0x00001a61 jmp          LBB0_264
	//0x00001a66 LBB0_431
	0x48, 0x89, 0xc6, //0x00001a66 movq         %rax, %rsi
	0xe9, 0x57, 0xfa, 0xff, 0xff, //0x00001a69 jmp          LBB0_371
	//0x00001a6e LBB0_432
	0x31, 0xc0, //0x00001a6e xorl         %eax, %eax
	0xe9, 0x68, 0x0c, 0x00, 0x00, //0x00001a70 jmp          LBB0_644
	//0x00001a75 LBB0_433
	0x44, 0x8b, 0x65, 0xc4, //0x00001a75 movl         $-60(%rbp), %r12d
	//0x00001a79 LBB0_434
	0x48, 0x8b, 0x45, 0xb8, //0x00001a79 movq         $-72(%rbp), %rax
	0x48, 0x8b, 0x4d, 0xa0, //0x00001a7d movq         $-96(%rbp), %rcx
	0x48, 0x01, 0xc8, //0x00001a81 addq         %rcx, %rax
	0x48, 0x83, 0xc0, 0xff, //0x00001a84 addq         $-1, %rax
	0x48, 0x89, 0x45, 0xc8, //0x00001a88 movq         %rax, $-56(%rbp)
	0xe9, 0x29, 0x00, 0x00, 0x00, //0x00001a8c jmp          LBB0_437
	//0x00001a91 LBB0_435
	0x48, 0x89, 0xc6, //0x00001a91 movq         %rax, %rsi
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001a94 .p2align 4, 0x90
	//0x00001aa0 LBB0_436
	0x31, 0xc9, //0x00001aa0 xorl         %ecx, %ecx
	0x4c, 0x39, 0xce, //0x00001aa2 cmpq         %r9, %rsi
	0x0f, 0x94, 0xc1, //0x00001aa5 sete         %cl
	0x48, 0x01, 0xf1, //0x00001aa8 addq         %rsi, %rcx
	0x4c, 0x29, 0xf1, //0x00001aab subq         %r14, %rcx
	0x4c, 0x89, 0xef, //0x00001aae movq         %r13, %rdi
	0x48, 0x85, 0xc9, //0x00001ab1 testq        %rcx, %rcx
	0x0f, 0x85, 0x19, 0x0c, 0x00, 0x00, //0x00001ab4 jne          LBB0_642
	//0x00001aba LBB0_437
	0x4d, 0x39, 0xce, //0x00001aba cmpq         %r9, %r14
	0x0f, 0x83, 0xf9, 0x0b, 0x00, 0x00, //0x00001abd jae          LBB0_641
	0x4c, 0x89, 0xf6, //0x00001ac3 movq         %r14, %rsi
	0x41, 0xf6, 0xc4, 0x08, //0x00001ac6 testb        $8, %r12b
	0x49, 0x89, 0xfd, //0x00001aca movq         %rdi, %r13
	0x0f, 0x85, 0xd9, 0x00, 0x00, 0x00, //0x00001acd jne          LBB0_451
	0xe9, 0x15, 0x00, 0x00, 0x00, //0x00001ad3 jmp          LBB0_440
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001ad8 .p2align 4, 0x90
	//0x00001ae0 LBB0_439
	0x48, 0x83, 0xc6, 0x01, //0x00001ae0 addq         $1, %rsi
	0x4c, 0x39, 0xce, //0x00001ae4 cmpq         %r9, %rsi
	0x0f, 0x83, 0xe3, 0x01, 0x00, 0x00, //0x00001ae7 jae          LBB0_467
	//0x00001aed LBB0_440
	0x44, 0x0f, 0xb6, 0x06, //0x00001aed movzbl       (%rsi), %r8d
	0x49, 0x83, 0xf8, 0x0d, //0x00001af1 cmpq         $13, %r8
	0x0f, 0x84, 0xe5, 0xff, 0xff, 0xff, //0x00001af5 je           LBB0_439
	0x41, 0x80, 0xf8, 0x0a, //0x00001afb cmpb         $10, %r8b
	0x0f, 0x84, 0xdb, 0xff, 0xff, 0xff, //0x00001aff je           LBB0_439
	0x48, 0x8b, 0x45, 0xd0, //0x00001b05 movq         $-48(%rbp), %rax
	0x46, 0x0f, 0xb6, 0x1c, 0x00, //0x00001b09 movzbl       (%rax,%r8), %r11d
	0x48, 0x83, 0xc6, 0x01, //0x00001b0e addq         $1, %rsi
	0x41, 0x81, 0xfb, 0xff, 0x00, 0x00, 0x00, //0x00001b12 cmpl         $255, %r11d
	0x0f, 0x84, 0xfe, 0x02, 0x00, 0x00, //0x00001b19 je           LBB0_492
	0xbb, 0x01, 0x00, 0x00, 0x00, //0x00001b1f movl         $1, %ebx
	0x4c, 0x39, 0xce, //0x00001b24 cmpq         %r9, %rsi
	0x0f, 0x82, 0x20, 0x00, 0x00, 0x00, //0x00001b27 jb           LBB0_445
	0xe9, 0x6e, 0x02, 0x00, 0x00, //0x00001b2d jmp          LBB0_481
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001b32 .p2align 4, 0x90
	//0x00001b40 LBB0_444
	0x48, 0x83, 0xc6, 0x01, //0x00001b40 addq         $1, %rsi
	0x4c, 0x39, 0xce, //0x00001b44 cmpq         %r9, %rsi
	0x0f, 0x83, 0xbe, 0x04, 0x00, 0x00, //0x00001b47 jae          LBB0_525
	//0x00001b4d LBB0_445
	0x44, 0x0f, 0xb6, 0x06, //0x00001b4d movzbl       (%rsi), %r8d
	0x49, 0x83, 0xf8, 0x0d, //0x00001b51 cmpq         $13, %r8
	0x0f, 0x84, 0xe5, 0xff, 0xff, 0xff, //0x00001b55 je           LBB0_444
	0x41, 0x80, 0xf8, 0x0a, //0x00001b5b cmpb         $10, %r8b
	0x0f, 0x84, 0xdb, 0xff, 0xff, 0xff, //0x00001b5f je           LBB0_444
	0x48, 0x8b, 0x45, 0xd0, //0x00001b65 movq         $-48(%rbp), %rax
	0x42, 0x0f, 0xb6, 0x04, 0x00, //0x00001b69 movzbl       (%rax,%r8), %eax
	0x48, 0x83, 0xc6, 0x01, //0x00001b6e addq         $1, %rsi
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00001b72 cmpl         $255, %eax
	0x0f, 0x84, 0x1e, 0x08, 0x00, 0x00, //0x00001b77 je           LBB0_579
	0x41, 0xc1, 0xe3, 0x06, //0x00001b7d shll         $6, %r11d
	0x41, 0x09, 0xc3, //0x00001b81 orl          %eax, %r11d
	0xbb, 0x02, 0x00, 0x00, 0x00, //0x00001b84 movl         $2, %ebx
	0x4c, 0x39, 0xce, //0x00001b89 cmpq         %r9, %rsi
	0x0f, 0x82, 0x70, 0x01, 0x00, 0x00, //0x00001b8c jb           LBB0_470
	0xe9, 0x09, 0x02, 0x00, 0x00, //0x00001b92 jmp          LBB0_481
	//0x00001b97 LBB0_465
	0x80, 0xf9, 0x6e, //0x00001b97 cmpb         $110, %cl
	0x0f, 0x85, 0xbf, 0x01, 0x00, 0x00, //0x00001b9a jne          LBB0_476
	//0x00001ba0 LBB0_449
	0x48, 0x89, 0xc6, //0x00001ba0 movq         %rax, %rsi
	//0x00001ba3 LBB0_450
	0x4c, 0x39, 0xce, //0x00001ba3 cmpq         %r9, %rsi
	0x0f, 0x83, 0x24, 0x01, 0x00, 0x00, //0x00001ba6 jae          LBB0_467
	//0x00001bac LBB0_451
	0x48, 0x8d, 0x4e, 0x01, //0x00001bac leaq         $1(%rsi), %rcx
	0x0f, 0xb6, 0x06, //0x00001bb0 movzbl       (%rsi), %eax
	0x3c, 0x5c, //0x00001bb3 cmpb         $92, %al
	0x0f, 0x85, 0xf5, 0x00, 0x00, 0x00, //0x00001bb5 jne          LBB0_463
	0x48, 0x8d, 0x46, 0x02, //0x00001bbb leaq         $2(%rsi), %rax
	0x41, 0xb0, 0xff, //0x00001bbf movb         $-1, %r8b
	0x4c, 0x39, 0xc8, //0x00001bc2 cmpq         %r9, %rax
	0x0f, 0x87, 0x81, 0x01, 0x00, 0x00, //0x00001bc5 ja           LBB0_474
	0x0f, 0xb6, 0x09, //0x00001bcb movzbl       (%rcx), %ecx
	0x80, 0xf9, 0x71, //0x00001bce cmpb         $113, %cl
	0x0f, 0x8e, 0xc0, 0xff, 0xff, 0xff, //0x00001bd1 jle          LBB0_465
	0x80, 0xf9, 0x72, //0x00001bd7 cmpb         $114, %cl
	0x0f, 0x84, 0xc0, 0xff, 0xff, 0xff, //0x00001bda je           LBB0_449
	0x80, 0xf9, 0x75, //0x00001be0 cmpb         $117, %cl
	0x0f, 0x85, 0x82, 0x01, 0x00, 0x00, //0x00001be3 jne          LBB0_478
	0x4c, 0x89, 0xc9, //0x00001be9 movq         %r9, %rcx
	0x48, 0x29, 0xc1, //0x00001bec subq         %rax, %rcx
	0x48, 0x83, 0xf9, 0x04, //0x00001bef cmpq         $4, %rcx
	0x0f, 0x8c, 0x72, 0x01, 0x00, 0x00, //0x00001bf3 jl           LBB0_478
	0x8b, 0x08, //0x00001bf9 movl         (%rax), %ecx
	0x89, 0xca, //0x00001bfb movl         %ecx, %edx
	0xf7, 0xd2, //0x00001bfd notl         %edx
	0x8d, 0x99, 0xd0, 0xcf, 0xcf, 0xcf, //0x00001bff leal         $-808464432(%rcx), %ebx
	0x81, 0xe2, 0x80, 0x80, 0x80, 0x80, //0x00001c05 andl         $-2139062144, %edx
	0x85, 0xda, //0x00001c0b testl        %ebx, %edx
	0x0f, 0x85, 0x58, 0x01, 0x00, 0x00, //0x00001c0d jne          LBB0_478
	0x8d, 0x99, 0x19, 0x19, 0x19, 0x19, //0x00001c13 leal         $421075225(%rcx), %ebx
	0x09, 0xcb, //0x00001c19 orl          %ecx, %ebx
	0xf7, 0xc3, 0x80, 0x80, 0x80, 0x80, //0x00001c1b testl        $-2139062144, %ebx
	0x0f, 0x85, 0x44, 0x01, 0x00, 0x00, //0x00001c21 jne          LBB0_478
	0x89, 0xcb, //0x00001c27 movl         %ecx, %ebx
	0x81, 0xe3, 0x7f, 0x7f, 0x7f, 0x7f, //0x00001c29 andl         $2139062143, %ebx
	0xbf, 0xc0, 0xc0, 0xc0, 0xc0, //0x00001c2f movl         $-1061109568, %edi
	0x29, 0xdf, //0x00001c34 subl         %ebx, %edi
	0x44, 0x8d, 0x93, 0x46, 0x46, 0x46, 0x46, //0x00001c36 leal         $1179010630(%rbx), %r10d
	0x21, 0xd7, //0x00001c3d andl         %edx, %edi
	0x44, 0x85, 0xd7, //0x00001c3f testl        %r10d, %edi
	0x0f, 0x85, 0x0c, 0x01, 0x00, 0x00, //0x00001c42 jne          LBB0_475
	0xbf, 0xe0, 0xe0, 0xe0, 0xe0, //0x00001c48 movl         $-522133280, %edi
	0x29, 0xdf, //0x00001c4d subl         %ebx, %edi
	0x81, 0xc3, 0x39, 0x39, 0x39, 0x39, //0x00001c4f addl         $960051513, %ebx
	0x21, 0xfa, //0x00001c55 andl         %edi, %edx
	0x85, 0xda, //0x00001c57 testl        %ebx, %edx
	0x0f, 0x85, 0xf5, 0x00, 0x00, 0x00, //0x00001c59 jne          LBB0_475
	0x0f, 0xc9, //0x00001c5f bswapl       %ecx
	0x89, 0xc8, //0x00001c61 movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x00001c63 shrl         $4, %eax
	0xf7, 0xd0, //0x00001c66 notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x00001c68 andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x00001c6d leal         (%rax,%rax,8), %eax
	0x81, 0xe1, 0x0f, 0x0f, 0x0f, 0x0f, //0x00001c70 andl         $252645135, %ecx
	0x01, 0xc1, //0x00001c76 addl         %eax, %ecx
	0x89, 0xc8, //0x00001c78 movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x00001c7a shrl         $4, %eax
	0x09, 0xc8, //0x00001c7d orl          %ecx, %eax
	0x89, 0xc1, //0x00001c7f movl         %eax, %ecx
	0xc1, 0xe9, 0x08, //0x00001c81 shrl         $8, %ecx
	0x81, 0xe1, 0x00, 0xff, 0x00, 0x00, //0x00001c84 andl         $65280, %ecx
	0x89, 0xc2, //0x00001c8a movl         %eax, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x00001c8c andl         $128, %edx
	0x48, 0x83, 0xc6, 0x06, //0x00001c92 addq         $6, %rsi
	0x09, 0xca, //0x00001c96 orl          %ecx, %edx
	0x4c, 0x89, 0xef, //0x00001c98 movq         %r13, %rdi
	0x0f, 0x85, 0xcd, 0x00, 0x00, 0x00, //0x00001c9b jne          LBB0_479
	0x3c, 0x0d, //0x00001ca1 cmpb         $13, %al
	0x0f, 0x85, 0x12, 0x00, 0x00, 0x00, //0x00001ca3 jne          LBB0_464
	0xe9, 0xf5, 0xfe, 0xff, 0xff, //0x00001ca9 jmp          LBB0_450
	0x90, 0x90, //0x00001cae .p2align 4, 0x90
	//0x00001cb0 LBB0_463
	0x48, 0x89, 0xce, //0x00001cb0 movq         %rcx, %rsi
	0x3c, 0x0d, //0x00001cb3 cmpb         $13, %al
	0x0f, 0x84, 0xe8, 0xfe, 0xff, 0xff, //0x00001cb5 je           LBB0_450
	//0x00001cbb LBB0_464
	0x41, 0x89, 0xc0, //0x00001cbb movl         %eax, %r8d
	0x3c, 0x0a, //0x00001cbe cmpb         $10, %al
	0x0f, 0x84, 0xdd, 0xfe, 0xff, 0xff, //0x00001cc0 je           LBB0_450
	0xe9, 0xa3, 0x00, 0x00, 0x00, //0x00001cc6 jmp          LBB0_479
	0x90, 0x90, 0x90, 0x90, 0x90, //0x00001ccb .p2align 4, 0x90
	//0x00001cd0 LBB0_467
	0x31, 0xdb, //0x00001cd0 xorl         %ebx, %ebx
	0x45, 0x31, 0xdb, //0x00001cd2 xorl         %r11d, %r11d
	//0x00001cd5 LBB0_468
	0x85, 0xdb, //0x00001cd5 testl        %ebx, %ebx
	0x0f, 0x85, 0xc3, 0x00, 0x00, 0x00, //0x00001cd7 jne          LBB0_481
	0x49, 0x89, 0xf6, //0x00001cdd movq         %rsi, %r14
	0x31, 0xc9, //0x00001ce0 xorl         %ecx, %ecx
	0x48, 0x85, 0xc9, //0x00001ce2 testq        %rcx, %rcx
	0x0f, 0x84, 0xcf, 0xfd, 0xff, 0xff, //0x00001ce5 je           LBB0_437
	0xe9, 0xe3, 0x09, 0x00, 0x00, //0x00001ceb jmp          LBB0_642
	//0x00001cf0 .p2align 4, 0x90
	//0x00001cf0 LBB0_469
	0x48, 0x83, 0xc6, 0x01, //0x00001cf0 addq         $1, %rsi
	0xbb, 0x02, 0x00, 0x00, 0x00, //0x00001cf4 movl         $2, %ebx
	0x4c, 0x39, 0xce, //0x00001cf9 cmpq         %r9, %rsi
	0x0f, 0x83, 0xd3, 0xff, 0xff, 0xff, //0x00001cfc jae          LBB0_468
	//0x00001d02 LBB0_470
	0x44, 0x0f, 0xb6, 0x06, //0x00001d02 movzbl       (%rsi), %r8d
	0x49, 0x83, 0xf8, 0x0d, //0x00001d06 cmpq         $13, %r8
	0x0f, 0x84, 0xe0, 0xff, 0xff, 0xff, //0x00001d0a je           LBB0_469
	0x41, 0x80, 0xf8, 0x0a, //0x00001d10 cmpb         $10, %r8b
	0x0f, 0x84, 0xd6, 0xff, 0xff, 0xff, //0x00001d14 je           LBB0_469
	0x48, 0x8b, 0x45, 0xd0, //0x00001d1a movq         $-48(%rbp), %rax
	0x42, 0x0f, 0xb6, 0x04, 0x00, //0x00001d1e movzbl       (%rax,%r8), %eax
	0x48, 0x83, 0xc6, 0x01, //0x00001d23 addq         $1, %rsi
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00001d27 cmpl         $255, %eax
	0x0f, 0x84, 0x39, 0x09, 0x00, 0x00, //0x00001d2c je           LBB0_612
	0x41, 0xc1, 0xe3, 0x06, //0x00001d32 shll         $6, %r11d
	0x41, 0x09, 0xc3, //0x00001d36 orl          %eax, %r11d
	0xbb, 0x03, 0x00, 0x00, 0x00, //0x00001d39 movl         $3, %ebx
	0x4c, 0x39, 0xce, //0x00001d3e cmpq         %r9, %rsi
	0x0f, 0x82, 0x89, 0x04, 0x00, 0x00, //0x00001d41 jb           LBB0_551
	0xe9, 0x54, 0x00, 0x00, 0x00, //0x00001d47 jmp          LBB0_481
	//0x00001d4c LBB0_474
	0x48, 0x89, 0xce, //0x00001d4c movq         %rcx, %rsi
	0xe9, 0x1a, 0x00, 0x00, 0x00, //0x00001d4f jmp          LBB0_479
	//0x00001d54 LBB0_475
	0x48, 0x89, 0xc6, //0x00001d54 movq         %rax, %rsi
	0x4c, 0x89, 0xef, //0x00001d57 movq         %r13, %rdi
	0xe9, 0x0f, 0x00, 0x00, 0x00, //0x00001d5a jmp          LBB0_479
	//0x00001d5f LBB0_476
	0x80, 0xf9, 0x2f, //0x00001d5f cmpb         $47, %cl
	0x0f, 0x85, 0x03, 0x00, 0x00, 0x00, //0x00001d62 jne          LBB0_478
	0x41, 0xb0, 0x2f, //0x00001d68 movb         $47, %r8b
	//0x00001d6b LBB0_478
	0x48, 0x89, 0xc6, //0x00001d6b movq         %rax, %rsi
	//0x00001d6e LBB0_479
	0x41, 0x0f, 0xb6, 0xc0, //0x00001d6e movzbl       %r8b, %eax
	0x48, 0x8b, 0x4d, 0xd0, //0x00001d72 movq         $-48(%rbp), %rcx
	0x44, 0x0f, 0xb6, 0x1c, 0x01, //0x00001d76 movzbl       (%rcx,%rax), %r11d
	0x41, 0x81, 0xfb, 0xff, 0x00, 0x00, 0x00, //0x00001d7b cmpl         $255, %r11d
	0x0f, 0x84, 0x95, 0x00, 0x00, 0x00, //0x00001d82 je           LBB0_492
	0xbb, 0x01, 0x00, 0x00, 0x00, //0x00001d88 movl         $1, %ebx
	0x4c, 0x39, 0xce, //0x00001d8d cmpq         %r9, %rsi
	0x0f, 0x82, 0x4d, 0x01, 0x00, 0x00, //0x00001d90 jb           LBB0_508
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001d96 .p2align 4, 0x90
	//0x00001da0 LBB0_481
	0x41, 0xf6, 0xc4, 0x02, //0x00001da0 testb        $2, %r12b
	0x0f, 0x94, 0xc0, //0x00001da4 sete         %al
	0x83, 0xfb, 0x01, //0x00001da7 cmpl         $1, %ebx
	0x0f, 0x94, 0xc1, //0x00001daa sete         %cl
	0x4c, 0x39, 0xce, //0x00001dad cmpq         %r9, %rsi
	0x0f, 0x82, 0x11, 0x00, 0x00, 0x00, //0x00001db0 jb           LBB0_484
	0x83, 0xfb, 0x04, //0x00001db6 cmpl         $4, %ebx
	0x0f, 0x84, 0x08, 0x00, 0x00, 0x00, //0x00001db9 je           LBB0_484
	0x08, 0xc8, //0x00001dbf orb          %cl, %al
	0x0f, 0x85, 0xd9, 0xfc, 0xff, 0xff, //0x00001dc1 jne          LBB0_436
	//0x00001dc7 LBB0_484
	0xb0, 0x04, //0x00001dc7 movb         $4, %al
	0x28, 0xd8, //0x00001dc9 subb         %bl, %al
	0x0f, 0xb6, 0xc0, //0x00001dcb movzbl       %al, %eax
	0x01, 0xc0, //0x00001dce addl         %eax, %eax
	0x8d, 0x0c, 0x40, //0x00001dd0 leal         (%rax,%rax,2), %ecx
	0x44, 0x89, 0xd8, //0x00001dd3 movl         %r11d, %eax
	0xd3, 0xe0, //0x00001dd6 shll         %cl, %eax
	0x83, 0xfb, 0x02, //0x00001dd8 cmpl         $2, %ebx
	0x4c, 0x89, 0xef, //0x00001ddb movq         %r13, %rdi
	0x0f, 0x84, 0x18, 0x00, 0x00, 0x00, //0x00001dde je           LBB0_489
	0x83, 0xfb, 0x03, //0x00001de4 cmpl         $3, %ebx
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x00001de7 je           LBB0_488
	0x83, 0xfb, 0x04, //0x00001ded cmpl         $4, %ebx
	0x0f, 0x85, 0x0b, 0x00, 0x00, 0x00, //0x00001df0 jne          LBB0_490
	0x88, 0x47, 0x02, //0x00001df6 movb         %al, $2(%rdi)
	//0x00001df9 LBB0_488
	0x88, 0x67, 0x01, //0x00001df9 movb         %ah, $1(%rdi)
	//0x00001dfc LBB0_489
	0xc1, 0xe8, 0x10, //0x00001dfc shrl         $16, %eax
	0x88, 0x07, //0x00001dff movb         %al, (%rdi)
	//0x00001e01 LBB0_490
	0x89, 0xd8, //0x00001e01 movl         %ebx, %eax
	0x48, 0x01, 0xc7, //0x00001e03 addq         %rax, %rdi
	0x48, 0x83, 0xc7, 0xff, //0x00001e06 addq         $-1, %rdi
	0x49, 0x89, 0xf6, //0x00001e0a movq         %rsi, %r14
	0x31, 0xc9, //0x00001e0d xorl         %ecx, %ecx
	0x48, 0x85, 0xc9, //0x00001e0f testq        %rcx, %rcx
	0x0f, 0x84, 0xa2, 0xfc, 0xff, 0xff, //0x00001e12 je           LBB0_437
	0xe9, 0xb6, 0x08, 0x00, 0x00, //0x00001e18 jmp          LBB0_642
	//0x00001e1d LBB0_492
	0x31, 0xdb, //0x00001e1d xorl         %ebx, %ebx
	0x45, 0x31, 0xdb, //0x00001e1f xorl         %r11d, %r11d
	//0x00001e22 LBB0_493
	0x41, 0xf6, 0xc4, 0x02, //0x00001e22 testb        $2, %r12b
	0x0f, 0x85, 0x74, 0xfc, 0xff, 0xff, //0x00001e26 jne          LBB0_436
	0x41, 0x80, 0xf8, 0x3d, //0x00001e2c cmpb         $61, %r8b
	0x0f, 0x85, 0x6a, 0xfc, 0xff, 0xff, //0x00001e30 jne          LBB0_436
	0x83, 0xfb, 0x02, //0x00001e36 cmpl         $2, %ebx
	0x0f, 0x82, 0x61, 0xfc, 0xff, 0xff, //0x00001e39 jb           LBB0_436
	0x41, 0xbf, 0x05, 0x00, 0x00, 0x00, //0x00001e3f movl         $5, %r15d
	0x41, 0x29, 0xdf, //0x00001e45 subl         %ebx, %r15d
	0x41, 0xf6, 0xc4, 0x08, //0x00001e48 testb        $8, %r12b
	0x0f, 0x85, 0xc3, 0x01, 0x00, 0x00, //0x00001e4c jne          LBB0_526
	0x4c, 0x39, 0xce, //0x00001e52 cmpq         %r9, %rsi
	0x0f, 0x83, 0x6c, 0xff, 0xff, 0xff, //0x00001e55 jae          LBB0_484
	0x48, 0x8d, 0x4e, 0x01, //0x00001e5b leaq         $1(%rsi), %rcx
	0x48, 0x8b, 0x45, 0xc8, //0x00001e5f movq         $-56(%rbp), %rax
	0x48, 0x29, 0xf0, //0x00001e63 subq         %rsi, %rax
	0x48, 0x83, 0xc6, 0x02, //0x00001e66 addq         $2, %rsi
	0x48, 0x89, 0xf7, //0x00001e6a movq         %rsi, %rdi
	0x48, 0x89, 0xce, //0x00001e6d movq         %rcx, %rsi
	0xe9, 0x1d, 0x00, 0x00, 0x00, //0x00001e70 jmp          LBB0_500
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001e75 .p2align 4, 0x90
	//0x00001e80 LBB0_499
	0x48, 0x83, 0xc6, 0x01, //0x00001e80 addq         $1, %rsi
	0x48, 0x83, 0xc7, 0x01, //0x00001e84 addq         $1, %rdi
	0x48, 0x83, 0xc0, 0xff, //0x00001e88 addq         $-1, %rax
	0x0f, 0x83, 0x13, 0x05, 0x00, 0x00, //0x00001e8c jae          LBB0_580
	//0x00001e92 LBB0_500
	0x0f, 0xb6, 0x4e, 0xff, //0x00001e92 movzbl       $-1(%rsi), %ecx
	0x80, 0xf9, 0x0a, //0x00001e96 cmpb         $10, %cl
	0x0f, 0x84, 0xe1, 0xff, 0xff, 0xff, //0x00001e99 je           LBB0_499
	0x80, 0xf9, 0x0d, //0x00001e9f cmpb         $13, %cl
	0x0f, 0x84, 0xd8, 0xff, 0xff, 0xff, //0x00001ea2 je           LBB0_499
	0x80, 0xf9, 0x3d, //0x00001ea8 cmpb         $61, %cl
	0x0f, 0x85, 0xef, 0xfb, 0xff, 0xff, //0x00001eab jne          LBB0_436
	0x41, 0x83, 0xff, 0x02, //0x00001eb1 cmpl         $2, %r15d
	0x0f, 0x84, 0xe5, 0xfb, 0xff, 0xff, //0x00001eb5 je           LBB0_436
	0x4c, 0x39, 0xce, //0x00001ebb cmpq         %r9, %rsi
	0x0f, 0x83, 0x03, 0xff, 0xff, 0xff, //0x00001ebe jae          LBB0_484
	0x48, 0x01, 0xc6, //0x00001ec4 addq         %rax, %rsi
	0x31, 0xc9, //0x00001ec7 xorl         %ecx, %ecx
	0xe9, 0xca, 0x02, 0x00, 0x00, //0x00001ec9 jmp          LBB0_546
	//0x00001ece LBB0_522
	0x80, 0xf9, 0x6e, //0x00001ece cmpb         $110, %cl
	0x0f, 0x85, 0x3e, 0x03, 0x00, 0x00, //0x00001ed1 jne          LBB0_556
	//0x00001ed7 LBB0_506
	0x48, 0x89, 0xc6, //0x00001ed7 movq         %rax, %rsi
	//0x00001eda LBB0_507
	0x4c, 0x39, 0xce, //0x00001eda cmpq         %r9, %rsi
	0x0f, 0x83, 0x28, 0x01, 0x00, 0x00, //0x00001edd jae          LBB0_525
	//0x00001ee3 LBB0_508
	0x48, 0x8d, 0x4e, 0x01, //0x00001ee3 leaq         $1(%rsi), %rcx
	0x0f, 0xb6, 0x06, //0x00001ee7 movzbl       (%rsi), %eax
	0x3c, 0x5c, //0x00001eea cmpb         $92, %al
	0x0f, 0x85, 0xfe, 0x00, 0x00, 0x00, //0x00001eec jne          LBB0_520
	0x48, 0x8d, 0x46, 0x02, //0x00001ef2 leaq         $2(%rsi), %rax
	0x41, 0xb0, 0xff, //0x00001ef6 movb         $-1, %r8b
	0x4c, 0x39, 0xc8, //0x00001ef9 cmpq         %r9, %rax
	0x0f, 0x87, 0xb4, 0x02, 0x00, 0x00, //0x00001efc ja           LBB0_549
	0x0f, 0xb6, 0x09, //0x00001f02 movzbl       (%rcx), %ecx
	0x80, 0xf9, 0x71, //0x00001f05 cmpb         $113, %cl
	0x0f, 0x8e, 0xc0, 0xff, 0xff, 0xff, //0x00001f08 jle          LBB0_522
	0x80, 0xf9, 0x72, //0x00001f0e cmpb         $114, %cl
	0x0f, 0x84, 0xc0, 0xff, 0xff, 0xff, //0x00001f11 je           LBB0_506
	0x80, 0xf9, 0x75, //0x00001f17 cmpb         $117, %cl
	0x0f, 0x85, 0x01, 0x03, 0x00, 0x00, //0x00001f1a jne          LBB0_558
	0x4c, 0x89, 0xc9, //0x00001f20 movq         %r9, %rcx
	0x48, 0x29, 0xc1, //0x00001f23 subq         %rax, %rcx
	0x48, 0x83, 0xf9, 0x04, //0x00001f26 cmpq         $4, %rcx
	0x0f, 0x8c, 0xf1, 0x02, 0x00, 0x00, //0x00001f2a jl           LBB0_558
	0x8b, 0x08, //0x00001f30 movl         (%rax), %ecx
	0x89, 0xcb, //0x00001f32 movl         %ecx, %ebx
	0xf7, 0xd3, //0x00001f34 notl         %ebx
	0x8d, 0x91, 0xd0, 0xcf, 0xcf, 0xcf, //0x00001f36 leal         $-808464432(%rcx), %edx
	0x81, 0xe3, 0x80, 0x80, 0x80, 0x80, //0x00001f3c andl         $-2139062144, %ebx
	0x85, 0xd3, //0x00001f42 testl        %edx, %ebx
	0x0f, 0x85, 0xd7, 0x02, 0x00, 0x00, //0x00001f44 jne          LBB0_558
	0x8d, 0x91, 0x19, 0x19, 0x19, 0x19, //0x00001f4a leal         $421075225(%rcx), %edx
	0x09, 0xca, //0x00001f50 orl          %ecx, %edx
	0xf7, 0xc2, 0x80, 0x80, 0x80, 0x80, //0x00001f52 testl        $-2139062144, %edx
	0x0f, 0x85, 0xc3, 0x02, 0x00, 0x00, //0x00001f58 jne          LBB0_558
	0x89, 0xca, //0x00001f5e movl         %ecx, %edx
	0x81, 0xe2, 0x7f, 0x7f, 0x7f, 0x7f, //0x00001f60 andl         $2139062143, %edx
	0xbf, 0xc0, 0xc0, 0xc0, 0xc0, //0x00001f66 movl         $-1061109568, %edi
	0x29, 0xd7, //0x00001f6b subl         %edx, %edi
	0x44, 0x8d, 0x92, 0x46, 0x46, 0x46, 0x46, //0x00001f6d leal         $1179010630(%rdx), %r10d
	0x21, 0xdf, //0x00001f74 andl         %ebx, %edi
	0x44, 0x85, 0xd7, //0x00001f76 testl        %r10d, %edi
	0x0f, 0x85, 0x8b, 0x02, 0x00, 0x00, //0x00001f79 jne          LBB0_555
	0xbf, 0xe0, 0xe0, 0xe0, 0xe0, //0x00001f7f movl         $-522133280, %edi
	0x29, 0xd7, //0x00001f84 subl         %edx, %edi
	0x81, 0xc2, 0x39, 0x39, 0x39, 0x39, //0x00001f86 addl         $960051513, %edx
	0x21, 0xfb, //0x00001f8c andl         %edi, %ebx
	0x85, 0xd3, //0x00001f8e testl        %edx, %ebx
	0x0f, 0x85, 0x74, 0x02, 0x00, 0x00, //0x00001f90 jne          LBB0_555
	0x0f, 0xc9, //0x00001f96 bswapl       %ecx
	0x89, 0xc8, //0x00001f98 movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x00001f9a shrl         $4, %eax
	0xf7, 0xd0, //0x00001f9d notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x00001f9f andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x00001fa4 leal         (%rax,%rax,8), %eax
	0x81, 0xe1, 0x0f, 0x0f, 0x0f, 0x0f, //0x00001fa7 andl         $252645135, %ecx
	0x01, 0xc1, //0x00001fad addl         %eax, %ecx
	0x89, 0xc8, //0x00001faf movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x00001fb1 shrl         $4, %eax
	0x09, 0xc8, //0x00001fb4 orl          %ecx, %eax
	0x89, 0xc1, //0x00001fb6 movl         %eax, %ecx
	0xc1, 0xe9, 0x08, //0x00001fb8 shrl         $8, %ecx
	0x81, 0xe1, 0x00, 0xff, 0x00, 0x00, //0x00001fbb andl         $65280, %ecx
	0x89, 0xc2, //0x00001fc1 movl         %eax, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x00001fc3 andl         $128, %edx
	0x48, 0x83, 0xc6, 0x06, //0x00001fc9 addq         $6, %rsi
	0x09, 0xca, //0x00001fcd orl          %ecx, %edx
	0x4c, 0x89, 0xef, //0x00001fcf movq         %r13, %rdi
	0x0f, 0x85, 0x4c, 0x02, 0x00, 0x00, //0x00001fd2 jne          LBB0_559
	0x3c, 0x0d, //0x00001fd8 cmpb         $13, %al
	0x0f, 0x85, 0x1b, 0x00, 0x00, 0x00, //0x00001fda jne          LBB0_521
	0xe9, 0xf5, 0xfe, 0xff, 0xff, //0x00001fe0 jmp          LBB0_507
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001fe5 .p2align 4, 0x90
	//0x00001ff0 LBB0_520
	0x48, 0x89, 0xce, //0x00001ff0 movq         %rcx, %rsi
	0x3c, 0x0d, //0x00001ff3 cmpb         $13, %al
	0x0f, 0x84, 0xdf, 0xfe, 0xff, 0xff, //0x00001ff5 je           LBB0_507
	//0x00001ffb LBB0_521
	0x41, 0x89, 0xc0, //0x00001ffb movl         %eax, %r8d
	0x3c, 0x0a, //0x00001ffe cmpb         $10, %al
	0x0f, 0x84, 0xd4, 0xfe, 0xff, 0xff, //0x00002000 je           LBB0_507
	0xe9, 0x19, 0x02, 0x00, 0x00, //0x00002006 jmp          LBB0_559
	//0x0000200b LBB0_525
	0xbb, 0x01, 0x00, 0x00, 0x00, //0x0000200b movl         $1, %ebx
	0xe9, 0xc0, 0xfc, 0xff, 0xff, //0x00002010 jmp          LBB0_468
	//0x00002015 LBB0_526
	0x4c, 0x39, 0xce, //0x00002015 cmpq         %r9, %rsi
	0x0f, 0x83, 0xa9, 0xfd, 0xff, 0xff, //0x00002018 jae          LBB0_484
	0x48, 0x89, 0xf7, //0x0000201e movq         %rsi, %rdi
	0xe9, 0x36, 0x00, 0x00, 0x00, //0x00002021 jmp          LBB0_530
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002026 .p2align 4, 0x90
	//0x00002030 LBB0_544
	0x48, 0x89, 0xc7, //0x00002030 movq         %rax, %rdi
	0x4c, 0x39, 0xcf, //0x00002033 cmpq         %r9, %rdi
	0x0f, 0x82, 0x20, 0x00, 0x00, 0x00, //0x00002036 jb           LBB0_530
	0xe9, 0x6c, 0x03, 0x00, 0x00, //0x0000203c jmp          LBB0_582
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002041 .p2align 4, 0x90
	//0x00002050 LBB0_528
	0x48, 0x89, 0xf7, //0x00002050 movq         %rsi, %rdi
	0x4c, 0x39, 0xcf, //0x00002053 cmpq         %r9, %rdi
	0x0f, 0x83, 0x51, 0x03, 0x00, 0x00, //0x00002056 jae          LBB0_582
	//0x0000205c LBB0_530
	0x48, 0x8d, 0x47, 0x01, //0x0000205c leaq         $1(%rdi), %rax
	0x0f, 0xb6, 0x0f, //0x00002060 movzbl       (%rdi), %ecx
	0x80, 0xf9, 0x5c, //0x00002063 cmpb         $92, %cl
	0x0f, 0x85, 0xe5, 0x00, 0x00, 0x00, //0x00002066 jne          LBB0_541
	0x48, 0x8d, 0x77, 0x02, //0x0000206c leaq         $2(%rdi), %rsi
	0x4c, 0x39, 0xce, //0x00002070 cmpq         %r9, %rsi
	0x0f, 0x87, 0x18, 0xfa, 0xff, 0xff, //0x00002073 ja           LBB0_435
	0x0f, 0xb6, 0x00, //0x00002079 movzbl       (%rax), %eax
	0x3c, 0x6e, //0x0000207c cmpb         $110, %al
	0x0f, 0x84, 0xcc, 0xff, 0xff, 0xff, //0x0000207e je           LBB0_528
	0x3c, 0x72, //0x00002084 cmpb         $114, %al
	0x0f, 0x84, 0xc4, 0xff, 0xff, 0xff, //0x00002086 je           LBB0_528
	0x3c, 0x75, //0x0000208c cmpb         $117, %al
	0x0f, 0x85, 0x0c, 0xfa, 0xff, 0xff, //0x0000208e jne          LBB0_436
	0x4c, 0x89, 0xc8, //0x00002094 movq         %r9, %rax
	0x48, 0x29, 0xf0, //0x00002097 subq         %rsi, %rax
	0x48, 0x83, 0xf8, 0x04, //0x0000209a cmpq         $4, %rax
	0x0f, 0x8c, 0xfc, 0xf9, 0xff, 0xff, //0x0000209e jl           LBB0_436
	0x8b, 0x16, //0x000020a4 movl         (%rsi), %edx
	0x89, 0xd1, //0x000020a6 movl         %edx, %ecx
	0xf7, 0xd1, //0x000020a8 notl         %ecx
	0x8d, 0x82, 0xd0, 0xcf, 0xcf, 0xcf, //0x000020aa leal         $-808464432(%rdx), %eax
	0x81, 0xe1, 0x80, 0x80, 0x80, 0x80, //0x000020b0 andl         $-2139062144, %ecx
	0x85, 0xc1, //0x000020b6 testl        %eax, %ecx
	0x0f, 0x85, 0xe2, 0xf9, 0xff, 0xff, //0x000020b8 jne          LBB0_436
	0x8d, 0x82, 0x19, 0x19, 0x19, 0x19, //0x000020be leal         $421075225(%rdx), %eax
	0x09, 0xd0, //0x000020c4 orl          %edx, %eax
	0xa9, 0x80, 0x80, 0x80, 0x80, //0x000020c6 testl        $-2139062144, %eax
	0x0f, 0x85, 0xcf, 0xf9, 0xff, 0xff, //0x000020cb jne          LBB0_436
	0x89, 0xd0, //0x000020d1 movl         %edx, %eax
	0x25, 0x7f, 0x7f, 0x7f, 0x7f, //0x000020d3 andl         $2139062143, %eax
	0x41, 0xb8, 0xc0, 0xc0, 0xc0, 0xc0, //0x000020d8 movl         $-1061109568, %r8d
	0x41, 0x29, 0xc0, //0x000020de subl         %eax, %r8d
	0x44, 0x8d, 0x90, 0x46, 0x46, 0x46, 0x46, //0x000020e1 leal         $1179010630(%rax), %r10d
	0x41, 0x21, 0xc8, //0x000020e8 andl         %ecx, %r8d
	0x45, 0x85, 0xd0, //0x000020eb testl        %r10d, %r8d
	0x0f, 0x85, 0xac, 0xf9, 0xff, 0xff, //0x000020ee jne          LBB0_436
	0x41, 0xb8, 0xe0, 0xe0, 0xe0, 0xe0, //0x000020f4 movl         $-522133280, %r8d
	0x41, 0x29, 0xc0, //0x000020fa subl         %eax, %r8d
	0x05, 0x39, 0x39, 0x39, 0x39, //0x000020fd addl         $960051513, %eax
	0x44, 0x21, 0xc1, //0x00002102 andl         %r8d, %ecx
	0x85, 0xc1, //0x00002105 testl        %eax, %ecx
	0x0f, 0x85, 0x93, 0xf9, 0xff, 0xff, //0x00002107 jne          LBB0_436
	0x0f, 0xca, //0x0000210d bswapl       %edx
	0x89, 0xd0, //0x0000210f movl         %edx, %eax
	0xc1, 0xe8, 0x04, //0x00002111 shrl         $4, %eax
	0xf7, 0xd0, //0x00002114 notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x00002116 andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x0000211b leal         (%rax,%rax,8), %eax
	0x81, 0xe2, 0x0f, 0x0f, 0x0f, 0x0f, //0x0000211e andl         $252645135, %edx
	0x01, 0xc2, //0x00002124 addl         %eax, %edx
	0x89, 0xd1, //0x00002126 movl         %edx, %ecx
	0xc1, 0xe9, 0x04, //0x00002128 shrl         $4, %ecx
	0x09, 0xd1, //0x0000212b orl          %edx, %ecx
	0x89, 0xc8, //0x0000212d movl         %ecx, %eax
	0xc1, 0xe8, 0x08, //0x0000212f shrl         $8, %eax
	0x25, 0x00, 0xff, 0x00, 0x00, //0x00002132 andl         $65280, %eax
	0x89, 0xca, //0x00002137 movl         %ecx, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x00002139 andl         $128, %edx
	0x48, 0x83, 0xc7, 0x06, //0x0000213f addq         $6, %rdi
	0x09, 0xc2, //0x00002143 orl          %eax, %edx
	0x48, 0x89, 0xf8, //0x00002145 movq         %rdi, %rax
	0x48, 0x89, 0xfe, //0x00002148 movq         %rdi, %rsi
	0x0f, 0x85, 0x4f, 0xf9, 0xff, 0xff, //0x0000214b jne          LBB0_436
	//0x00002151 LBB0_541
	0x80, 0xf9, 0x0a, //0x00002151 cmpb         $10, %cl
	0x0f, 0x84, 0xd6, 0xfe, 0xff, 0xff, //0x00002154 je           LBB0_544
	0x80, 0xf9, 0x0d, //0x0000215a cmpb         $13, %cl
	0x0f, 0x84, 0xcd, 0xfe, 0xff, 0xff, //0x0000215d je           LBB0_544
	0x80, 0xf9, 0x3d, //0x00002163 cmpb         $61, %cl
	0x0f, 0x85, 0x25, 0xf9, 0xff, 0xff, //0x00002166 jne          LBB0_435
	0x41, 0x83, 0xff, 0x02, //0x0000216c cmpl         $2, %r15d
	0x0f, 0x84, 0x1b, 0xf9, 0xff, 0xff, //0x00002170 je           LBB0_435
	0x4c, 0x39, 0xc8, //0x00002176 cmpq         %r9, %rax
	0x0f, 0x82, 0x4a, 0x02, 0x00, 0x00, //0x00002179 jb           LBB0_615
	//0x0000217f LBB0_587
	0x48, 0x89, 0xc6, //0x0000217f movq         %rax, %rsi
	0xe9, 0x40, 0xfc, 0xff, 0xff, //0x00002182 jmp          LBB0_484
	//0x00002187 LBB0_545
	0x48, 0x83, 0xc7, 0x01, //0x00002187 addq         $1, %rdi
	0x48, 0x83, 0xc1, 0x01, //0x0000218b addq         $1, %rcx
	0x48, 0x39, 0xc8, //0x0000218f cmpq         %rcx, %rax
	0x0f, 0x84, 0x2f, 0xfc, 0xff, 0xff, //0x00002192 je           LBB0_484
	//0x00002198 LBB0_546
	0x0f, 0xb6, 0x57, 0xff, //0x00002198 movzbl       $-1(%rdi), %edx
	0x80, 0xfa, 0x0d, //0x0000219c cmpb         $13, %dl
	0x0f, 0x84, 0xe2, 0xff, 0xff, 0xff, //0x0000219f je           LBB0_545
	0x80, 0xfa, 0x0a, //0x000021a5 cmpb         $10, %dl
	0x0f, 0x84, 0xd9, 0xff, 0xff, 0xff, //0x000021a8 je           LBB0_545
	0x48, 0x89, 0xfe, //0x000021ae movq         %rdi, %rsi
	0xe9, 0xea, 0xf8, 0xff, 0xff, //0x000021b1 jmp          LBB0_436
	//0x000021b6 LBB0_549
	0x48, 0x89, 0xce, //0x000021b6 movq         %rcx, %rsi
	0xe9, 0x66, 0x00, 0x00, 0x00, //0x000021b9 jmp          LBB0_559
	//0x000021be LBB0_550
	0x48, 0x83, 0xc6, 0x01, //0x000021be addq         $1, %rsi
	0xbb, 0x03, 0x00, 0x00, 0x00, //0x000021c2 movl         $3, %ebx
	0x4c, 0x39, 0xce, //0x000021c7 cmpq         %r9, %rsi
	0x0f, 0x83, 0x05, 0xfb, 0xff, 0xff, //0x000021ca jae          LBB0_468
	//0x000021d0 LBB0_551
	0x44, 0x0f, 0xb6, 0x06, //0x000021d0 movzbl       (%rsi), %r8d
	0x49, 0x83, 0xf8, 0x0d, //0x000021d4 cmpq         $13, %r8
	0x0f, 0x84, 0xe0, 0xff, 0xff, 0xff, //0x000021d8 je           LBB0_550
	0x41, 0x80, 0xf8, 0x0a, //0x000021de cmpb         $10, %r8b
	0x0f, 0x84, 0xd6, 0xff, 0xff, 0xff, //0x000021e2 je           LBB0_550
	0x48, 0x8b, 0x45, 0xd0, //0x000021e8 movq         $-48(%rbp), %rax
	0x42, 0x0f, 0xb6, 0x04, 0x00, //0x000021ec movzbl       (%rax,%r8), %eax
	0x48, 0x83, 0xc6, 0x01, //0x000021f1 addq         $1, %rsi
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x000021f5 cmpl         $255, %eax
	0x0f, 0x85, 0xa3, 0x04, 0x00, 0x00, //0x000021fa jne          LBB0_637
	//0x00002200 LBB0_638
	0xbb, 0x03, 0x00, 0x00, 0x00, //0x00002200 movl         $3, %ebx
	0xe9, 0x18, 0xfc, 0xff, 0xff, //0x00002205 jmp          LBB0_493
	//0x0000220a LBB0_555
	0x48, 0x89, 0xc6, //0x0000220a movq         %rax, %rsi
	0x4c, 0x89, 0xef, //0x0000220d movq         %r13, %rdi
	0xe9, 0x0f, 0x00, 0x00, 0x00, //0x00002210 jmp          LBB0_559
	//0x00002215 LBB0_556
	0x80, 0xf9, 0x2f, //0x00002215 cmpb         $47, %cl
	0x0f, 0x85, 0x03, 0x00, 0x00, 0x00, //0x00002218 jne          LBB0_558
	0x41, 0xb0, 0x2f, //0x0000221e movb         $47, %r8b
	//0x00002221 LBB0_558
	0x48, 0x89, 0xc6, //0x00002221 movq         %rax, %rsi
	//0x00002224 LBB0_559
	0x41, 0x0f, 0xb6, 0xc0, //0x00002224 movzbl       %r8b, %eax
	0x48, 0x8b, 0x4d, 0xd0, //0x00002228 movq         $-48(%rbp), %rcx
	0x0f, 0xb6, 0x04, 0x01, //0x0000222c movzbl       (%rcx,%rax), %eax
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00002230 cmpl         $255, %eax
	0x0f, 0x84, 0x60, 0x01, 0x00, 0x00, //0x00002235 je           LBB0_579
	0x41, 0xc1, 0xe3, 0x06, //0x0000223b shll         $6, %r11d
	0x41, 0x09, 0xc3, //0x0000223f orl          %eax, %r11d
	0xbb, 0x02, 0x00, 0x00, 0x00, //0x00002242 movl         $2, %ebx
	0x4c, 0x39, 0xce, //0x00002247 cmpq         %r9, %rsi
	0x0f, 0x82, 0x1f, 0x00, 0x00, 0x00, //0x0000224a jb           LBB0_563
	0xe9, 0x4b, 0xfb, 0xff, 0xff, //0x00002250 jmp          LBB0_481
	//0x00002255 LBB0_577
	0x80, 0xf9, 0x6e, //0x00002255 cmpb         $110, %cl
	0x0f, 0x85, 0x95, 0x02, 0x00, 0x00, //0x00002258 jne          LBB0_589
	//0x0000225e LBB0_561
	0x48, 0x89, 0xc6, //0x0000225e movq         %rax, %rsi
	//0x00002261 LBB0_562
	0xbb, 0x02, 0x00, 0x00, 0x00, //0x00002261 movl         $2, %ebx
	0x4c, 0x39, 0xce, //0x00002266 cmpq         %r9, %rsi
	0x0f, 0x83, 0x66, 0xfa, 0xff, 0xff, //0x00002269 jae          LBB0_468
	//0x0000226f LBB0_563
	0x48, 0x8d, 0x4e, 0x01, //0x0000226f leaq         $1(%rsi), %rcx
	0x0f, 0xb6, 0x06, //0x00002273 movzbl       (%rsi), %eax
	0x3c, 0x5c, //0x00002276 cmpb         $92, %al
	0x0f, 0x85, 0x02, 0x01, 0x00, 0x00, //0x00002278 jne          LBB0_575
	0x48, 0x8d, 0x46, 0x02, //0x0000227e leaq         $2(%rsi), %rax
	0x41, 0xb0, 0xff, //0x00002282 movb         $-1, %r8b
	0x4c, 0x39, 0xc8, //0x00002285 cmpq         %r9, %rax
	0x0f, 0x87, 0x27, 0x01, 0x00, 0x00, //0x00002288 ja           LBB0_583
	0x0f, 0xb6, 0x09, //0x0000228e movzbl       (%rcx), %ecx
	0x80, 0xf9, 0x71, //0x00002291 cmpb         $113, %cl
	0x0f, 0x8e, 0xbb, 0xff, 0xff, 0xff, //0x00002294 jle          LBB0_577
	0x80, 0xf9, 0x72, //0x0000229a cmpb         $114, %cl
	0x0f, 0x84, 0xbb, 0xff, 0xff, 0xff, //0x0000229d je           LBB0_561
	0x80, 0xf9, 0x75, //0x000022a3 cmpb         $117, %cl
	0x0f, 0x85, 0x53, 0x02, 0x00, 0x00, //0x000022a6 jne          LBB0_591
	0x4c, 0x89, 0xc9, //0x000022ac movq         %r9, %rcx
	0x48, 0x29, 0xc1, //0x000022af subq         %rax, %rcx
	0x48, 0x83, 0xf9, 0x04, //0x000022b2 cmpq         $4, %rcx
	0x0f, 0x8c, 0x43, 0x02, 0x00, 0x00, //0x000022b6 jl           LBB0_591
	0x8b, 0x08, //0x000022bc movl         (%rax), %ecx
	0x89, 0xcb, //0x000022be movl         %ecx, %ebx
	0xf7, 0xd3, //0x000022c0 notl         %ebx
	0x8d, 0x91, 0xd0, 0xcf, 0xcf, 0xcf, //0x000022c2 leal         $-808464432(%rcx), %edx
	0x81, 0xe3, 0x80, 0x80, 0x80, 0x80, //0x000022c8 andl         $-2139062144, %ebx
	0x85, 0xd3, //0x000022ce testl        %edx, %ebx
	0x0f, 0x85, 0x29, 0x02, 0x00, 0x00, //0x000022d0 jne          LBB0_591
	0x8d, 0x91, 0x19, 0x19, 0x19, 0x19, //0x000022d6 leal         $421075225(%rcx), %edx
	0x09, 0xca, //0x000022dc orl          %ecx, %edx
	0xf7, 0xc2, 0x80, 0x80, 0x80, 0x80, //0x000022de testl        $-2139062144, %edx
	0x0f, 0x85, 0x15, 0x02, 0x00, 0x00, //0x000022e4 jne          LBB0_591
	0x89, 0xca, //0x000022ea movl         %ecx, %edx
	0x81, 0xe2, 0x7f, 0x7f, 0x7f, 0x7f, //0x000022ec andl         $2139062143, %edx
	0xbf, 0xc0, 0xc0, 0xc0, 0xc0, //0x000022f2 movl         $-1061109568, %edi
	0x29, 0xd7, //0x000022f7 subl         %edx, %edi
	0x44, 0x8d, 0x92, 0x46, 0x46, 0x46, 0x46, //0x000022f9 leal         $1179010630(%rdx), %r10d
	0x21, 0xdf, //0x00002300 andl         %ebx, %edi
	0x44, 0x85, 0xd7, //0x00002302 testl        %r10d, %edi
	0x0f, 0x85, 0xdd, 0x01, 0x00, 0x00, //0x00002305 jne          LBB0_588
	0xbf, 0xe0, 0xe0, 0xe0, 0xe0, //0x0000230b movl         $-522133280, %edi
	0x29, 0xd7, //0x00002310 subl         %edx, %edi
	0x81, 0xc2, 0x39, 0x39, 0x39, 0x39, //0x00002312 addl         $960051513, %edx
	0x21, 0xfb, //0x00002318 andl         %edi, %ebx
	0x85, 0xd3, //0x0000231a testl        %edx, %ebx
	0x0f, 0x85, 0xc6, 0x01, 0x00, 0x00, //0x0000231c jne          LBB0_588
	0x0f, 0xc9, //0x00002322 bswapl       %ecx
	0x89, 0xc8, //0x00002324 movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x00002326 shrl         $4, %eax
	0xf7, 0xd0, //0x00002329 notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x0000232b andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x00002330 leal         (%rax,%rax,8), %eax
	0x81, 0xe1, 0x0f, 0x0f, 0x0f, 0x0f, //0x00002333 andl         $252645135, %ecx
	0x01, 0xc1, //0x00002339 addl         %eax, %ecx
	0x89, 0xc8, //0x0000233b movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x0000233d shrl         $4, %eax
	0x09, 0xc8, //0x00002340 orl          %ecx, %eax
	0x89, 0xc1, //0x00002342 movl         %eax, %ecx
	0xc1, 0xe9, 0x08, //0x00002344 shrl         $8, %ecx
	0x81, 0xe1, 0x00, 0xff, 0x00, 0x00, //0x00002347 andl         $65280, %ecx
	0x89, 0xc2, //0x0000234d movl         %eax, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x0000234f andl         $128, %edx
	0x48, 0x83, 0xc6, 0x06, //0x00002355 addq         $6, %rsi
	0x09, 0xca, //0x00002359 orl          %ecx, %edx
	0x4c, 0x89, 0xef, //0x0000235b movq         %r13, %rdi
	0x0f, 0x85, 0x9e, 0x01, 0x00, 0x00, //0x0000235e jne          LBB0_592
	0x3c, 0x0d, //0x00002364 cmpb         $13, %al
	0x0f, 0x85, 0x1f, 0x00, 0x00, 0x00, //0x00002366 jne          LBB0_576
	0xe9, 0xf0, 0xfe, 0xff, 0xff, //0x0000236c jmp          LBB0_562
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002371 .p2align 4, 0x90
	//0x00002380 LBB0_575
	0x48, 0x89, 0xce, //0x00002380 movq         %rcx, %rsi
	0x3c, 0x0d, //0x00002383 cmpb         $13, %al
	0x0f, 0x84, 0xd6, 0xfe, 0xff, 0xff, //0x00002385 je           LBB0_562
	//0x0000238b LBB0_576
	0x41, 0x89, 0xc0, //0x0000238b movl         %eax, %r8d
	0x3c, 0x0a, //0x0000238e cmpb         $10, %al
	0x0f, 0x84, 0xcb, 0xfe, 0xff, 0xff, //0x00002390 je           LBB0_562
	0xe9, 0x67, 0x01, 0x00, 0x00, //0x00002396 jmp          LBB0_592
	//0x0000239b LBB0_579
	0xbb, 0x01, 0x00, 0x00, 0x00, //0x0000239b movl         $1, %ebx
	0xe9, 0x7d, 0xfa, 0xff, 0xff, //0x000023a0 jmp          LBB0_493
	//0x000023a5 LBB0_580
	0x4c, 0x89, 0xce, //0x000023a5 movq         %r9, %rsi
	0xe9, 0x1a, 0xfa, 0xff, 0xff, //0x000023a8 jmp          LBB0_484
	//0x000023ad LBB0_582
	0x48, 0x89, 0xfe, //0x000023ad movq         %rdi, %rsi
	0xe9, 0x12, 0xfa, 0xff, 0xff, //0x000023b0 jmp          LBB0_484
	//0x000023b5 LBB0_583
	0x48, 0x89, 0xce, //0x000023b5 movq         %rcx, %rsi
	0xe9, 0x45, 0x01, 0x00, 0x00, //0x000023b8 jmp          LBB0_592
	//0x000023bd LBB0_613
	0x48, 0x89, 0xf0, //0x000023bd movq         %rsi, %rax
	//0x000023c0 LBB0_614
	0x4c, 0x39, 0xc8, //0x000023c0 cmpq         %r9, %rax
	0x0f, 0x83, 0xb6, 0xfd, 0xff, 0xff, //0x000023c3 jae          LBB0_587
	//0x000023c9 LBB0_615
	0x48, 0x8d, 0x48, 0x01, //0x000023c9 leaq         $1(%rax), %rcx
	0x0f, 0xb6, 0x38, //0x000023cd movzbl       (%rax), %edi
	0x40, 0x80, 0xff, 0x5c, //0x000023d0 cmpb         $92, %dil
	0x0f, 0x85, 0xef, 0x00, 0x00, 0x00, //0x000023d4 jne          LBB0_626
	0x48, 0x8d, 0x70, 0x02, //0x000023da leaq         $2(%rax), %rsi
	0x4c, 0x39, 0xce, //0x000023de cmpq         %r9, %rsi
	0x0f, 0x87, 0xcd, 0x02, 0x00, 0x00, //0x000023e1 ja           LBB0_640
	0x0f, 0xb6, 0x09, //0x000023e7 movzbl       (%rcx), %ecx
	0x80, 0xf9, 0x6e, //0x000023ea cmpb         $110, %cl
	0x0f, 0x84, 0xca, 0xff, 0xff, 0xff, //0x000023ed je           LBB0_613
	0x80, 0xf9, 0x72, //0x000023f3 cmpb         $114, %cl
	0x0f, 0x84, 0xc1, 0xff, 0xff, 0xff, //0x000023f6 je           LBB0_613
	0x80, 0xf9, 0x75, //0x000023fc cmpb         $117, %cl
	0x0f, 0x85, 0x9b, 0xf6, 0xff, 0xff, //0x000023ff jne          LBB0_436
	0x4c, 0x89, 0xc9, //0x00002405 movq         %r9, %rcx
	0x48, 0x29, 0xf1, //0x00002408 subq         %rsi, %rcx
	0x48, 0x83, 0xf9, 0x04, //0x0000240b cmpq         $4, %rcx
	0x0f, 0x8c, 0x8b, 0xf6, 0xff, 0xff, //0x0000240f jl           LBB0_436
	0x8b, 0x0e, //0x00002415 movl         (%rsi), %ecx
	0x89, 0xcf, //0x00002417 movl         %ecx, %edi
	0xf7, 0xd7, //0x00002419 notl         %edi
	0x8d, 0x91, 0xd0, 0xcf, 0xcf, 0xcf, //0x0000241b leal         $-808464432(%rcx), %edx
	0x81, 0xe7, 0x80, 0x80, 0x80, 0x80, //0x00002421 andl         $-2139062144, %edi
	0x85, 0xd7, //0x00002427 testl        %edx, %edi
	0x0f, 0x85, 0x71, 0xf6, 0xff, 0xff, //0x00002429 jne          LBB0_436
	0x8d, 0x91, 0x19, 0x19, 0x19, 0x19, //0x0000242f leal         $421075225(%rcx), %edx
	0x09, 0xca, //0x00002435 orl          %ecx, %edx
	0xf7, 0xc2, 0x80, 0x80, 0x80, 0x80, //0x00002437 testl        $-2139062144, %edx
	0x0f, 0x85, 0x5d, 0xf6, 0xff, 0xff, //0x0000243d jne          LBB0_436
	0x89, 0xca, //0x00002443 movl         %ecx, %edx
	0x81, 0xe2, 0x7f, 0x7f, 0x7f, 0x7f, //0x00002445 andl         $2139062143, %edx
	0x41, 0xb8, 0xc0, 0xc0, 0xc0, 0xc0, //0x0000244b movl         $-1061109568, %r8d
	0x41, 0x29, 0xd0, //0x00002451 subl         %edx, %r8d
	0x44, 0x8d, 0x92, 0x46, 0x46, 0x46, 0x46, //0x00002454 leal         $1179010630(%rdx), %r10d
	0x41, 0x21, 0xf8, //0x0000245b andl         %edi, %r8d
	0x45, 0x85, 0xd0, //0x0000245e testl        %r10d, %r8d
	0x0f, 0x85, 0x39, 0xf6, 0xff, 0xff, //0x00002461 jne          LBB0_436
	0x41, 0xb8, 0xe0, 0xe0, 0xe0, 0xe0, //0x00002467 movl         $-522133280, %r8d
	0x41, 0x29, 0xd0, //0x0000246d subl         %edx, %r8d
	0x81, 0xc2, 0x39, 0x39, 0x39, 0x39, //0x00002470 addl         $960051513, %edx
	0x44, 0x21, 0xc7, //0x00002476 andl         %r8d, %edi
	0x85, 0xd7, //0x00002479 testl        %edx, %edi
	0x0f, 0x85, 0x1f, 0xf6, 0xff, 0xff, //0x0000247b jne          LBB0_436
	0x0f, 0xc9, //0x00002481 bswapl       %ecx
	0x89, 0xca, //0x00002483 movl         %ecx, %edx
	0xc1, 0xea, 0x04, //0x00002485 shrl         $4, %edx
	0xf7, 0xd2, //0x00002488 notl         %edx
	0x81, 0xe2, 0x01, 0x01, 0x01, 0x01, //0x0000248a andl         $16843009, %edx
	0x8d, 0x14, 0xd2, //0x00002490 leal         (%rdx,%rdx,8), %edx
	0x81, 0xe1, 0x0f, 0x0f, 0x0f, 0x0f, //0x00002493 andl         $252645135, %ecx
	0x01, 0xd1, //0x00002499 addl         %edx, %ecx
	0x89, 0xcf, //0x0000249b movl         %ecx, %edi
	0xc1, 0xef, 0x04, //0x0000249d shrl         $4, %edi
	0x09, 0xcf, //0x000024a0 orl          %ecx, %edi
	0x89, 0xf9, //0x000024a2 movl         %edi, %ecx
	0xc1, 0xe9, 0x08, //0x000024a4 shrl         $8, %ecx
	0x81, 0xe1, 0x00, 0xff, 0x00, 0x00, //0x000024a7 andl         $65280, %ecx
	0x89, 0xfa, //0x000024ad movl         %edi, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x000024af andl         $128, %edx
	0x48, 0x83, 0xc0, 0x06, //0x000024b5 addq         $6, %rax
	0x09, 0xca, //0x000024b9 orl          %ecx, %edx
	0x48, 0x89, 0xc6, //0x000024bb movq         %rax, %rsi
	0x0f, 0x84, 0x08, 0x00, 0x00, 0x00, //0x000024be je           LBB0_627
	0xe9, 0xd7, 0xf5, 0xff, 0xff, //0x000024c4 jmp          LBB0_436
	//0x000024c9 LBB0_626
	0x48, 0x89, 0xce, //0x000024c9 movq         %rcx, %rsi
	//0x000024cc LBB0_627
	0x40, 0x80, 0xff, 0x0d, //0x000024cc cmpb         $13, %dil
	0x0f, 0x84, 0xe7, 0xfe, 0xff, 0xff, //0x000024d0 je           LBB0_613
	0x48, 0x89, 0xf0, //0x000024d6 movq         %rsi, %rax
	0x40, 0x80, 0xff, 0x0a, //0x000024d9 cmpb         $10, %dil
	0x0f, 0x84, 0xdd, 0xfe, 0xff, 0xff, //0x000024dd je           LBB0_614
	0xe9, 0xb8, 0xf5, 0xff, 0xff, //0x000024e3 jmp          LBB0_436
	//0x000024e8 LBB0_588
	0x48, 0x89, 0xc6, //0x000024e8 movq         %rax, %rsi
	0x4c, 0x89, 0xef, //0x000024eb movq         %r13, %rdi
	0xe9, 0x0f, 0x00, 0x00, 0x00, //0x000024ee jmp          LBB0_592
	//0x000024f3 LBB0_589
	0x80, 0xf9, 0x2f, //0x000024f3 cmpb         $47, %cl
	0x0f, 0x85, 0x03, 0x00, 0x00, 0x00, //0x000024f6 jne          LBB0_591
	0x41, 0xb0, 0x2f, //0x000024fc movb         $47, %r8b
	//0x000024ff LBB0_591
	0x48, 0x89, 0xc6, //0x000024ff movq         %rax, %rsi
	//0x00002502 LBB0_592
	0x41, 0x0f, 0xb6, 0xc0, //0x00002502 movzbl       %r8b, %eax
	0x48, 0x8b, 0x4d, 0xd0, //0x00002506 movq         $-48(%rbp), %rcx
	0x0f, 0xb6, 0x04, 0x01, //0x0000250a movzbl       (%rcx,%rax), %eax
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x0000250e cmpl         $255, %eax
	0x0f, 0x84, 0x52, 0x01, 0x00, 0x00, //0x00002513 je           LBB0_612
	0x41, 0xc1, 0xe3, 0x06, //0x00002519 shll         $6, %r11d
	0x41, 0x09, 0xc3, //0x0000251d orl          %eax, %r11d
	0xbb, 0x03, 0x00, 0x00, 0x00, //0x00002520 movl         $3, %ebx
	0x4c, 0x39, 0xce, //0x00002525 cmpq         %r9, %rsi
	0x0f, 0x82, 0x1f, 0x00, 0x00, 0x00, //0x00002528 jb           LBB0_596
	0xe9, 0x6d, 0xf8, 0xff, 0xff, //0x0000252e jmp          LBB0_481
	//0x00002533 LBB0_610
	0x80, 0xf9, 0x6e, //0x00002533 cmpb         $110, %cl
	0x0f, 0x85, 0x41, 0x01, 0x00, 0x00, //0x00002536 jne          LBB0_633
	//0x0000253c LBB0_594
	0x48, 0x89, 0xc6, //0x0000253c movq         %rax, %rsi
	//0x0000253f LBB0_595
	0xbb, 0x03, 0x00, 0x00, 0x00, //0x0000253f movl         $3, %ebx
	0x4c, 0x39, 0xce, //0x00002544 cmpq         %r9, %rsi
	0x0f, 0x83, 0x88, 0xf7, 0xff, 0xff, //0x00002547 jae          LBB0_468
	//0x0000254d LBB0_596
	0x48, 0x8d, 0x4e, 0x01, //0x0000254d leaq         $1(%rsi), %rcx
	0x0f, 0xb6, 0x06, //0x00002551 movzbl       (%rsi), %eax
	0x3c, 0x5c, //0x00002554 cmpb         $92, %al
	0x0f, 0x85, 0xf4, 0x00, 0x00, 0x00, //0x00002556 jne          LBB0_608
	0x48, 0x8d, 0x46, 0x02, //0x0000255c leaq         $2(%rsi), %rax
	0x41, 0xb0, 0xff, //0x00002560 movb         $-1, %r8b
	0x4c, 0x39, 0xc8, //0x00002563 cmpq         %r9, %rax
	0x0f, 0x87, 0x09, 0x01, 0x00, 0x00, //0x00002566 ja           LBB0_631
	0x0f, 0xb6, 0x09, //0x0000256c movzbl       (%rcx), %ecx
	0x80, 0xf9, 0x71, //0x0000256f cmpb         $113, %cl
	0x0f, 0x8e, 0xbb, 0xff, 0xff, 0xff, //0x00002572 jle          LBB0_610
	0x80, 0xf9, 0x72, //0x00002578 cmpb         $114, %cl
	0x0f, 0x84, 0xbb, 0xff, 0xff, 0xff, //0x0000257b je           LBB0_594
	0x80, 0xf9, 0x75, //0x00002581 cmpb         $117, %cl
	0x0f, 0x85, 0xff, 0x00, 0x00, 0x00, //0x00002584 jne          LBB0_635
	0x4c, 0x89, 0xc9, //0x0000258a movq         %r9, %rcx
	0x48, 0x29, 0xc1, //0x0000258d subq         %rax, %rcx
	0x48, 0x83, 0xf9, 0x04, //0x00002590 cmpq         $4, %rcx
	0x0f, 0x8c, 0xef, 0x00, 0x00, 0x00, //0x00002594 jl           LBB0_635
	0x8b, 0x08, //0x0000259a movl         (%rax), %ecx
	0x89, 0xcb, //0x0000259c movl         %ecx, %ebx
	0xf7, 0xd3, //0x0000259e notl         %ebx
	0x8d, 0x91, 0xd0, 0xcf, 0xcf, 0xcf, //0x000025a0 leal         $-808464432(%rcx), %edx
	0x81, 0xe3, 0x80, 0x80, 0x80, 0x80, //0x000025a6 andl         $-2139062144, %ebx
	0x85, 0xd3, //0x000025ac testl        %edx, %ebx
	0x0f, 0x85, 0xd5, 0x00, 0x00, 0x00, //0x000025ae jne          LBB0_635
	0x8d, 0x91, 0x19, 0x19, 0x19, 0x19, //0x000025b4 leal         $421075225(%rcx), %edx
	0x09, 0xca, //0x000025ba orl          %ecx, %edx
	0xf7, 0xc2, 0x80, 0x80, 0x80, 0x80, //0x000025bc testl        $-2139062144, %edx
	0x0f, 0x85, 0xc1, 0x00, 0x00, 0x00, //0x000025c2 jne          LBB0_635
	0x89, 0xca, //0x000025c8 movl         %ecx, %edx
	0x81, 0xe2, 0x7f, 0x7f, 0x7f, 0x7f, //0x000025ca andl         $2139062143, %edx
	0xbf, 0xc0, 0xc0, 0xc0, 0xc0, //0x000025d0 movl         $-1061109568, %edi
	0x29, 0xd7, //0x000025d5 subl         %edx, %edi
	0x44, 0x8d, 0x92, 0x46, 0x46, 0x46, 0x46, //0x000025d7 leal         $1179010630(%rdx), %r10d
	0x21, 0xdf, //0x000025de andl         %ebx, %edi
	0x44, 0x85, 0xd7, //0x000025e0 testl        %r10d, %edi
	0x0f, 0x85, 0xa0, 0x00, 0x00, 0x00, //0x000025e3 jne          LBB0_635
	0xbf, 0xe0, 0xe0, 0xe0, 0xe0, //0x000025e9 movl         $-522133280, %edi
	0x29, 0xd7, //0x000025ee subl         %edx, %edi
	0x81, 0xc2, 0x39, 0x39, 0x39, 0x39, //0x000025f0 addl         $960051513, %edx
	0x21, 0xfb, //0x000025f6 andl         %edi, %ebx
	0x85, 0xd3, //0x000025f8 testl        %edx, %ebx
	0x0f, 0x85, 0x89, 0x00, 0x00, 0x00, //0x000025fa jne          LBB0_635
	0x0f, 0xc9, //0x00002600 bswapl       %ecx
	0x89, 0xc8, //0x00002602 movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x00002604 shrl         $4, %eax
	0xf7, 0xd0, //0x00002607 notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x00002609 andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x0000260e leal         (%rax,%rax,8), %eax
	0x81, 0xe1, 0x0f, 0x0f, 0x0f, 0x0f, //0x00002611 andl         $252645135, %ecx
	0x01, 0xc1, //0x00002617 addl         %eax, %ecx
	0x89, 0xc8, //0x00002619 movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x0000261b shrl         $4, %eax
	0x09, 0xc8, //0x0000261e orl          %ecx, %eax
	0x89, 0xc1, //0x00002620 movl         %eax, %ecx
	0xc1, 0xe9, 0x08, //0x00002622 shrl         $8, %ecx
	0x81, 0xe1, 0x00, 0xff, 0x00, 0x00, //0x00002625 andl         $65280, %ecx
	0x89, 0xc2, //0x0000262b movl         %eax, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x0000262d andl         $128, %edx
	0x48, 0x83, 0xc6, 0x06, //0x00002633 addq         $6, %rsi
	0x09, 0xca, //0x00002637 orl          %ecx, %edx
	0x4c, 0x89, 0xef, //0x00002639 movq         %r13, %rdi
	0x0f, 0x85, 0x4a, 0x00, 0x00, 0x00, //0x0000263c jne          LBB0_636
	0x3c, 0x0d, //0x00002642 cmpb         $13, %al
	0x0f, 0x85, 0x11, 0x00, 0x00, 0x00, //0x00002644 jne          LBB0_609
	0xe9, 0xf0, 0xfe, 0xff, 0xff, //0x0000264a jmp          LBB0_595
	0x90, //0x0000264f .p2align 4, 0x90
	//0x00002650 LBB0_608
	0x48, 0x89, 0xce, //0x00002650 movq         %rcx, %rsi
	0x3c, 0x0d, //0x00002653 cmpb         $13, %al
	0x0f, 0x84, 0xe4, 0xfe, 0xff, 0xff, //0x00002655 je           LBB0_595
	//0x0000265b LBB0_609
	0x41, 0x89, 0xc0, //0x0000265b movl         %eax, %r8d
	0x3c, 0x0a, //0x0000265e cmpb         $10, %al
	0x0f, 0x84, 0xd9, 0xfe, 0xff, 0xff, //0x00002660 je           LBB0_595
	0xe9, 0x21, 0x00, 0x00, 0x00, //0x00002666 jmp          LBB0_636
	//0x0000266b LBB0_612
	0xbb, 0x02, 0x00, 0x00, 0x00, //0x0000266b movl         $2, %ebx
	0xe9, 0xad, 0xf7, 0xff, 0xff, //0x00002670 jmp          LBB0_493
	//0x00002675 LBB0_631
	0x48, 0x89, 0xce, //0x00002675 movq         %rcx, %rsi
	0xe9, 0x0f, 0x00, 0x00, 0x00, //0x00002678 jmp          LBB0_636
	//0x0000267d LBB0_633
	0x80, 0xf9, 0x2f, //0x0000267d cmpb         $47, %cl
	0x0f, 0x85, 0x03, 0x00, 0x00, 0x00, //0x00002680 jne          LBB0_635
	0x41, 0xb0, 0x2f, //0x00002686 movb         $47, %r8b
	//0x00002689 LBB0_635
	0x48, 0x89, 0xc6, //0x00002689 movq         %rax, %rsi
	//0x0000268c LBB0_636
	0x41, 0x0f, 0xb6, 0xc0, //0x0000268c movzbl       %r8b, %eax
	0x48, 0x8b, 0x4d, 0xd0, //0x00002690 movq         $-48(%rbp), %rcx
	0x0f, 0xb6, 0x04, 0x01, //0x00002694 movzbl       (%rcx,%rax), %eax
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00002698 cmpl         $255, %eax
	0x0f, 0x84, 0x5d, 0xfb, 0xff, 0xff, //0x0000269d je           LBB0_638
	//0x000026a3 LBB0_637
	0x41, 0xc1, 0xe3, 0x06, //0x000026a3 shll         $6, %r11d
	0x41, 0x09, 0xc3, //0x000026a7 orl          %eax, %r11d
	0xbb, 0x04, 0x00, 0x00, 0x00, //0x000026aa movl         $4, %ebx
	0xe9, 0xec, 0xf6, 0xff, 0xff, //0x000026af jmp          LBB0_481
	//0x000026b4 LBB0_640
	0x48, 0x89, 0xce, //0x000026b4 movq         %rcx, %rsi
	0xe9, 0xe4, 0xf3, 0xff, 0xff, //0x000026b7 jmp          LBB0_436
	//0x000026bc LBB0_641
	0x48, 0x2b, 0x7d, 0xa8, //0x000026bc subq         $-88(%rbp), %rdi
	0x48, 0x8b, 0x85, 0x78, 0xff, 0xff, 0xff, //0x000026c0 movq         $-136(%rbp), %rax
	0x48, 0x01, 0x78, 0x08, //0x000026c7 addq         %rdi, $8(%rax)
	0x48, 0x89, 0xf8, //0x000026cb movq         %rdi, %rax
	0xe9, 0x0a, 0x00, 0x00, 0x00, //0x000026ce jmp          LBB0_644
	//0x000026d3 LBB0_642
	0x4c, 0x01, 0xf1, //0x000026d3 addq         %r14, %rcx
	//0x000026d6 LBB0_643
	0x48, 0x8b, 0x45, 0xb8, //0x000026d6 movq         $-72(%rbp), %rax
	0x48, 0x29, 0xc8, //0x000026da subq         %rcx, %rax
	//0x000026dd LBB0_644
	0x48, 0x83, 0xc4, 0x60, //0x000026dd addq         $96, %rsp
	0x5b, //0x000026e1 popq         %rbx
	0x41, 0x5c, //0x000026e2 popq         %r12
	0x41, 0x5d, //0x000026e4 popq         %r13
	0x41, 0x5e, //0x000026e6 popq         %r14
	0x41, 0x5f, //0x000026e8 popq         %r15
	0x5d, //0x000026ea popq         %rbp
	0xc3, //0x000026eb retq         
	0x00, 0x00, 0x00, 0x00, //0x000026ec .p2align 4, 0x00
	//0x000026f0 _VecDecodeCharsetStd
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x000026f0 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00002700 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x3e, 0xff, 0xff, 0xff, 0x3f, //0x00002710 QUAD $0xffffffffffffffff; QUAD $0x3fffffff3effffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff>\xff\xff\xff?'
	0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x3d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00002720 QUAD $0x3b3a393837363534; QUAD $0xffffffffffff3d3c  // .ascii 16, '456789:;<=\xff\xff\xff\xff\xff\xff'
	0xff, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, //0x00002730 QUAD $0x06050403020100ff; QUAD $0x0e0d0c0b0a090807  // .ascii 16, '\xff\x00\x01\x02\x03\x04\x05\x06\x07\x08\t\n\x0b\x0c\r\x0e'
	0x0f, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00002740 QUAD $0x161514131211100f; QUAD $0xffffffffff191817  // .ascii 16, '\x0f\x10\x11\x12\x13\x14\x15\x16\x17\x18\x19\xff\xff\xff\xff\xff'
	0xff, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, //0x00002750 QUAD $0x201f1e1d1c1b1aff; QUAD $0x2827262524232221  // .ascii 16, '\xff\x1a\x1b\x1c\x1d\x1e\x1f !"#$%&\'('
	0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f, 0x30, 0x31, 0x32, 0x33, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00002760 QUAD $0x302f2e2d2c2b2a29; QUAD $0xffffffffff333231  // .ascii 16, ')*+,-./0123\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00002770 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00002780 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00002790 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x000027a0 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x000027b0 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x000027c0 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x000027d0 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x000027e0 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	//0x000027f0 .p2align 4, 0x00
	//0x000027f0 _VecDecodeCharsetURL
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x000027f0 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00002800 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x3e, 0xff, 0xff, //0x00002810 QUAD $0xffffffffffffffff; QUAD $0xffff3effffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff>\xff\xff'
	0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x3d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00002820 QUAD $0x3b3a393837363534; QUAD $0xffffffffffff3d3c  // .ascii 16, '456789:;<=\xff\xff\xff\xff\xff\xff'
	0xff, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, //0x00002830 QUAD $0x06050403020100ff; QUAD $0x0e0d0c0b0a090807  // .ascii 16, '\xff\x00\x01\x02\x03\x04\x05\x06\x07\x08\t\n\x0b\x0c\r\x0e'
	0x0f, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19, 0xff, 0xff, 0xff, 0xff, 0x3f, //0x00002840 QUAD $0x161514131211100f; QUAD $0x3fffffffff191817  // .ascii 16, '\x0f\x10\x11\x12\x13\x14\x15\x16\x17\x18\x19\xff\xff\xff\xff?'
	0xff, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, //0x00002850 QUAD $0x201f1e1d1c1b1aff; QUAD $0x2827262524232221  // .ascii 16, '\xff\x1a\x1b\x1c\x1d\x1e\x1f !"#$%&\'('
	0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f, 0x30, 0x31, 0x32, 0x33, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00002860 QUAD $0x302f2e2d2c2b2a29; QUAD $0xffffffffff333231  // .ascii 16, ')*+,-./0123\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00002870 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00002880 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00002890 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x000028a0 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x000028b0 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x000028c0 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x000028d0 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x000028e0 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
}
 
