// Code generated by <PERSON><PERSON>, DO NOT EDIT.

/*
 * Copyright 2025 ByteDance Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package {{PACKAGE}}

import (
    `unsafe`

    `github.com/cloudwego/base64x/internal/rt`
)

var F_b64encode func(out unsafe.Pointer, src unsafe.Pointer, mod int)

var S_b64encode uintptr

//go:nosplit
func B64encode(out *[]byte, src *[]byte, mode int) {
    F_b64encode(rt.NoEscape(unsafe.Pointer(out)), rt.NoEscape(unsafe.Pointer(src)), mode)
}
