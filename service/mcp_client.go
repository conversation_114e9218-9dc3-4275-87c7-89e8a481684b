package service

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	mcpclient "github.com/mark3labs/mcp-go/client"
	"github.com/mark3labs/mcp-go/mcp"
	log "github.com/sirupsen/logrus"

	"gitlab.sz.sensetime.com/fin/nl2sql/nl2sql-api-service/components/client"
	"gitlab.sz.sensetime.com/fin/nl2sql/nl2sql-api-service/components/common"
)

func (s *Service) InitMCPClient() *mcpclient.Client {
	mc, err := mcpclient.NewStreamableHttpClient(
		s.Config.MCPServerEndpoint.String(),
	)
	if err != nil {
		log.Fatalf("Failed to create MCP client: %v", err)
	}

	return mc
}

const mcpToolsSelectPromptTemplate = `
您是一个资深的数据仓库工程师，负责处理用户的数据资产问题，能够分析用户的问题并根据具体情况给出相应的工具。同时你也熟悉 MCP 协议，能够透彻理解 MCP 协议的 server/client 架构模式，以及拥有丰富的实战经验。
======
下面是基于 MCP 协议的工具列表：

%s
======
现在用户的问题是：%s
用户有权限访问的分析主题列表有：%s 【主意此处是数组,生成参数时候是单个值】
请根据用户的问题，分析出需要使用的工具，直接给出具体的工具名字以及调用参数，使用 JSON 格式返回，示例如下， 不要做其他的额外输出。

=== JSON 示例格式 ===
{"name":"dimension_info","arguments":{"dimension_name":"维度 1","topic_name":"分析主题 1","user_id":"用户 1", "indicator_name": "指标 1"}}

请注意：
1. 上面的 JSON 格式是示例格式，具体的参数需要根据工具的输入参数进行调整。
2. 你可以根据工具的输入参数，补全默认参数。
3. 如果分析不出工具的参数请将 arguments 置空 {}。
4. 如果分析不出工具的名字，请将 name 置空 ""。
5. 如果分析不出topic_name参数，请将 topic_name 置空 ""。
6. 输出的 JSON 数据有且仅有一组，不能重复输出。

现在请开始执行任务
`

type DataAssetQARequest struct {
	Question string `json:"question"`
	Topic    string `json:"topic"`
	ThreadId string `json:"thread_id"`
	RunId    string `json:"run_id"`
	UserId   string `json:"user_id"`
	TaskId   string `json:"task_id"`
	Scene    int    `json:"scene"`
}

type ToolSelectResp struct {
	Name      string                 `json:"name"`
	Arguments map[string]interface{} `json:"arguments"`
}

// DataAssetQAAsync 数据资产问答 异步存储
func (s *Service) DataAssetQAAsync(ctx *gin.Context, req *DataAssetQARequest) {
	var (
		contentId = uuid.NewString()
		runId     = req.RunId
		threadId  = req.ThreadId
		taskId    = req.TaskId
		question  = req.Question
		startForU = "开始为您查找数据资产...\n"
	)
	WriteStreamNormalInfoResponse(ctx.Writer, &Response[common.ChatMessageChunk]{
		Err: nil,
		Result: common.ChatMessageChunk{
			ID:      contentId,
			RunId:   runId,
			Role:    RoleAssistant,
			Content: nil,
			Event: &common.MessageEvent{
				Stage:      StagePlanning,
				CreatedAt:  time.Now().Unix(),
				Text:       startForU,
				Done:       false,
				DoneReason: "",
				ContentID:  contentId,
			},
		},
		Header: nil,
	})

	mc := s.InitMCPClient()
	// Start the client
	if err := mc.Start(ctx); err != nil {
		log.Errorf("Failed to start MCP client: %v", err)
		WriteStreamWithErrorResponse(ctx.Writer, &Response[common.ChatMessageChunk]{
			Err: SSEError(http.StatusInternalServerError, err),
		})
		return
	}
	initializeResult, err := mc.Initialize(ctx, mcp.InitializeRequest{
		Request: mcp.Request{},
		Params: struct {
			ProtocolVersion string                 `json:"protocolVersion"`
			Capabilities    mcp.ClientCapabilities `json:"capabilities"`
			ClientInfo      mcp.Implementation     `json:"clientInfo"`
		}{},
	})
	if err != nil {
		log.Errorf("Failed to initialize MCP client: %v", err)
		WriteStreamWithErrorResponse(ctx.Writer, &Response[common.ChatMessageChunk]{
			Err: SSEError(http.StatusInternalServerError, err),
		})
		return
	}

	log.Infof("initializeResult: %v", initializeResult)
	msgInitMCPClient := "初始化 MCP Client 成功\n"
	startForU += msgInitMCPClient
	WriteStreamNormalInfoResponse(ctx.Writer, &Response[common.ChatMessageChunk]{
		Err: nil,
		Result: common.ChatMessageChunk{
			ID:      contentId,
			RunId:   runId,
			Role:    RoleAssistant,
			Content: nil,
			Event: &common.MessageEvent{
				Stage:      StagePlanning,
				CreatedAt:  time.Now().Unix(),
				Text:       msgInitMCPClient,
				Done:       false,
				DoneReason: "",
				ContentID:  contentId,
			},
		},
		Header: nil,
	})

	mcpTools, err := mc.ListTools(ctx, mcp.ListToolsRequest{})
	if err != nil {
		log.Errorf("Failed to list tools: %v", err)
		WriteStreamWithErrorResponse(ctx.Writer, &Response[common.ChatMessageChunk]{
			Err: SSEError(http.StatusInternalServerError, err),
		})
		return
	}

	log.Infof("mcpTools: %v", mcpTools)
	msgListTools := "获取可用 MCP 工具列表成功\n开始判断适合的工具\n"
	startForU += msgListTools
	WriteStreamNormalInfoResponse(ctx.Writer, &Response[common.ChatMessageChunk]{
		Err: nil,
		Result: common.ChatMessageChunk{
			ID:      contentId,
			RunId:   runId,
			Role:    RoleAssistant,
			Content: nil,
			Event: &common.MessageEvent{
				Stage:      StagePlanning,
				CreatedAt:  time.Now().Unix(),
				Text:       msgListTools,
				Done:       false,
				DoneReason: "",
				ContentID:  contentId,
			},
		},
		Header: nil,
	})

	var toolTemplate string
	var toolsMap = make(map[string]mcp.Tool)
	for _, tool := range mcpTools.Tools {
		toolTemplate += "<" + tool.Name + ">\n"
		toolTemplate += "	<description>" + tool.Description + "	</description>\n"
		for k, _ := range tool.InputSchema.Properties {
			toolTemplate += "	<param>" + k + "</param>\n"
		}
		toolTemplate += "</" + tool.Name + ">\n"
		toolsMap[tool.Name] = tool
	}

	// 读取用户权限
	userDataPerms, err := s.UserDataPermissionsMap(ctx, ctx.GetString(UserIdKey))
	if err != nil {
		WriteStreamWithErrorResponse(ctx.Writer, &Response[common.ChatMessageChunk]{
			Err: SSEError(http.StatusInternalServerError, err),
		})
		return
	}
	var userTopicList string
	for _, topic := range userDataPerms {
		userTopicList += topic.PermissionName + ","
	}
	userTopicList = strings.TrimRight(userTopicList, ",")

	toolSelectPrompt := fmt.Sprintf(mcpToolsSelectPromptTemplate, toolTemplate, req.Question, userTopicList)
	options := client.DefaultLLMEngineOptions(s.Config)
	options.Temperature = 0.7  // Set higher temperature for more creative suggestions
	options.MaxNewTokens = 512 // Limit token count for recommendations
	genText, err := s.Client.LLMEngineCall(ctx, toolSelectPrompt, options)
	if err != nil {
		log.Errorf("Failed to generate text: %v", err)
		WriteStreamWithErrorResponse(ctx.Writer, &Response[common.ChatMessageChunk]{
			Err: SSEError(http.StatusInternalServerError, err),
		})
		return
	}

	log.Infof("genText: %s", genText)

	if strings.Contains(genText, "```\n```json") {
		genTexts := strings.Split(genText, "```\n```json")
		if len(genTexts) > 1 {
			genText = genTexts[0]
		} else {
			log.Errorf("Generated text does not contain valid JSON format: %s", genText)
			WriteStreamWithErrorResponse(ctx.Writer, &Response[common.ChatMessageChunk]{
				Err: SSEError(http.StatusInternalServerError, fmt.Errorf("生成的文本不包含有效的 JSON 格式")),
			})
			return
		}
	}

	genText = strings.TrimLeft(genText, "```json\n")
	genText = strings.TrimRight(genText, "\n```\n")

	toolS := &ToolSelectResp{
		Name:      "",
		Arguments: map[string]interface{}{},
	}
	if err = json.Unmarshal([]byte(genText), toolS); err != nil {
		log.Errorf("Failed to unmarshal JSON: %v", err)
		WriteStreamWithErrorResponse(ctx.Writer, &Response[common.ChatMessageChunk]{
			Err: SSEError(http.StatusInternalServerError, err),
		})
		return
	}

	currentTool, ok := toolsMap[toolS.Name]
	if !ok {
		log.Errorf("Tool not found: %s", toolS.Name)
		WriteStreamWithErrorResponse(ctx.Writer, &Response[common.ChatMessageChunk]{
			Err: SSEError(http.StatusInternalServerError, fmt.Errorf("工具不存在，请重新选择")),
		})
		return
	}

	log.Infof("命中工具: %s", currentTool.Name)

	// 补全默认参数
	toolS.Arguments["user_id"] = req.UserId
	if toolS.Arguments["topic_name"] == nil || toolS.Arguments["topic_name"] == "" {
		toolS.Arguments["topic_name"] = req.Topic
	}

	callRequest := mcp.CallToolRequest{
		Params: mcp.CallToolParams{
			Name:      currentTool.Name,
			Arguments: toolS.Arguments,
		},
	}

	callRequestBytes, _ := json.MarshalIndent(callRequest.Params, "", "    ")

	msgSelectedTool := "准备使用工具: {{ " + currentTool.Name + " }} 来完成数据查询任务\n工具调用参数如下:\n```json\n" + string(callRequestBytes) + "\n```\n\n"
	startForU += msgSelectedTool
	WriteStreamNormalInfoResponse(ctx.Writer, &Response[common.ChatMessageChunk]{
		Err: nil,
		Result: common.ChatMessageChunk{
			ID:      contentId,
			RunId:   runId,
			Role:    RoleAssistant,
			Content: nil,
			Event: &common.MessageEvent{
				Stage:      StagePlanning,
				CreatedAt:  time.Now().Unix(),
				Text:       msgSelectedTool,
				Done:       false,
				DoneReason: "",
				ContentID:  contentId,
			},
		},
		Header: nil,
	})

	callResult, err := mc.CallTool(ctx, callRequest)
	if err != nil {
		log.Errorf("Failed to call tool: %v", err)
		WriteStreamWithErrorResponse(ctx.Writer, &Response[common.ChatMessageChunk]{
			Err: SSEError(http.StatusInternalServerError, err),
		})
		return
	}

	log.Infof("callResult: %v", callResult)

	if callResult.IsError {
		log.Errorf("Call result is error: %v", callResult.Content)
		WriteStreamWithErrorResponse(ctx.Writer, &Response[common.ChatMessageChunk]{
			Err: SSEError(http.StatusInternalServerError, fmt.Errorf("callResult is err: %v", callResult.Content)),
		})
		return
	}

	if len(callResult.Content) == 0 {
		WriteStreamWithErrorResponse(ctx.Writer, &Response[common.ChatMessageChunk]{
			Err: SSEError(http.StatusInternalServerError, fmt.Errorf("callResult is empty")),
		})
		return
	}

	callResultText, ok := callResult.Content[0].(mcp.TextContent)
	if !ok {
		WriteStreamWithErrorResponse(ctx.Writer, &Response[common.ChatMessageChunk]{
			Err: SSEError(http.StatusInternalServerError, fmt.Errorf("callResult is not text")),
		})
		return
	}
	if callResultText.Text == "" {
		WriteStreamWithErrorResponse(ctx.Writer, &Response[common.ChatMessageChunk]{
			Err: SSEError(http.StatusInternalServerError, fmt.Errorf("callResult is empty")),
		})
		return
	}

	msgCallRes := "工具调用成功，整理结果，准备输出。\n"
	startForU += msgCallRes
	WriteStreamNormalInfoResponse(ctx.Writer, &Response[common.ChatMessageChunk]{
		Err: nil,
		Result: common.ChatMessageChunk{
			ID:      contentId,
			RunId:   runId,
			Role:    RoleAssistant,
			Content: nil,
			Event: &common.MessageEvent{
				Stage:      StagePlanning,
				CreatedAt:  time.Now().Unix(),
				Text:       msgCallRes,
				Done:       false,
				DoneReason: "",
				ContentID:  contentId,
			},
		},
		Header: nil,
	})

	// 单独存一个特殊的主流程的记录
	if err = s.saveMessages(ctx, &threadMessageSave{
		threadId:    threadId,
		runId:       runId,
		stage:       StagePlanning,
		messageRole: RoleAssistant,
		messages: []*common.MessageContentDB{
			{
				Thought:      startForU,
				RunId:        runId,
				Scene:        req.Scene,
				WithPlanning: true,
			},
		},
	}); err != nil {
		WriteStreamWithErrorResponse(ctx.Writer, &Response[common.ChatMessageChunk]{
			Err: SSEError(http.StatusInternalServerError, err),
		})
		return
	}

	callResultJSONContent := callResultText.Text

	mcpToolData := &ToolData{}
	if err = json.Unmarshal([]byte(callResultJSONContent), mcpToolData); err != nil {
		WriteStreamWithErrorResponse(ctx.Writer, &Response[common.ChatMessageChunk]{
			Err: SSEError(http.StatusInternalServerError, err),
		})
		return
	}

	callResultTextContent := mcpToolData.ToMarkdown(0)

	messageContents := []*common.MessageContentDB{
		{
			Type:    99,
			Text:    callResultJSONContent,
			Thought: taskDataAssetDefaultThought,
			TaskId:  taskId,
			Query:   question,
			RunId:   runId,
			Results: []*common.ResultDataAssetValue{
				{
					Text: callResultTextContent,
					More: len(mcpToolData.Body) > limit,
				},
			},
		},
	}
	if err = s.saveMessages(ctx, &threadMessageSave{
		threadId:    threadId,
		runId:       runId,
		stage:       StageTask,
		messageRole: RoleAssistant,
		messages:    messageContents,
	}); err != nil {
		WriteStreamWithErrorResponse(ctx.Writer, &Response[common.ChatMessageChunk]{
			Err: SSEError(http.StatusInternalServerError, err),
		})
		return
	}
	// 更新 current_run_id, current_stage 和 current_run_id_status
	if err = s.Store.UpdateThreadCurrentRunId(ctx, threadId, runId, StageFinalize, RunIdStatusActive); err != nil {
		WriteStreamWithErrorResponse(ctx.Writer, &Response[common.ChatMessageChunk]{
			Err: SSEError(http.StatusInternalServerError, err),
		})
		return
	}

	WriteStreamNormalInfoResponse(ctx.Writer, &Response[common.ChatMessageChunk]{
		Err: nil,
		Result: common.ChatMessageChunk{
			ID:      contentId,
			RunId:   runId,
			Role:    RoleAssistant,
			Content: nil,
			Event: &common.MessageEvent{
				Stage:      StagePlanning,
				CreatedAt:  time.Now().Unix(),
				Text:       "",
				Done:       true,
				DoneReason: "planning finished",
				ContentID:  contentId,
			},
		},
		Header: nil,
	})

	WriteStreamNormalInfoResponse(ctx.Writer, &Response[common.ChatMessageChunk]{
		Err: nil,
		Result: common.ChatMessageChunk{
			ID:    contentId,
			RunId: runId,
			Role:  RoleAssistant,
			Content: &common.MessageContent{
				ID:    contentId,
				Stage: StagePlanning,
				Type:  "json",
				Text:  callResultTextContent,
				JSON: &common.JSONContent{
					Kind: "task",
					Data: &common.TaskData{
						Task: common.Task{
							ID:     taskId,
							RunID:  runId,
							Query:  question,
							Type:   99,
							Status: 1,
							Result: []*common.ResultDataAssetValue{
								{
									Text: callResultTextContent,
								},
							},
						},
					},
				},
				Thought: startForU,
			},
			Event: nil,
		},
		Header: nil,
	})
	WriteStreamDoneResponse(ctx.Writer)
}
