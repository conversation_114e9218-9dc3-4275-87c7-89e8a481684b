package client

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	log "github.com/sirupsen/logrus"

	"gitlab.sz.sensetime.com/fin/nl2sql/nl2sql-api-service/components/common"
)

type GetAnalysisTopicsResponse struct {
	Data []*common.AnalysisTopic `json:"data"`
	Page *common.Page            `json:"page"`
}

func (c *Client) GetAnalysisTopics(ctx context.Context, page *common.Page, search string) (*GetAnalysisTopicsResponse, error) {
	headers := map[string]string{
		"Accept": "application/json",
	}

	client := http.DefaultClient
	client.Timeout = time.Duration(c.config.DependenceTimeout) * time.Second

	req, err := http.NewRequestWithContext(ctx, "GET", fmt.Sprintf("%s/v1/analysis-topics", c.config.DataSourcesEndpoint.String()), nil)
	if err != nil {
		return nil, err
	}

	q := req.URL.Query()
	q.Add("page.offset", fmt.Sprintf("%d", page.Offset))
	q.Add("page.limit", fmt.Sprintf("%d", page.Limit))
	q.Add("search", search)
	req.URL.RawQuery = q.Encode()

	for key, value := range headers {
		req.Header.Set(key, value)
	}

	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		b, _ := io.ReadAll(resp.Body)
		log.Debugf("HTTP code: %d, content: %s", resp.StatusCode, string(b))
		return nil, fmt.Errorf("%s", c.DataManagerErr(b))
	}

	analysisTopicsResponse := &GetAnalysisTopicsResponse{}
	if err := json.NewDecoder(resp.Body).Decode(analysisTopicsResponse); err != nil {
		log.Errorf("unmarshal failed: %v", err)
		return nil, err
	}

	return analysisTopicsResponse, nil
}

func (c *Client) GetAnalysisTopicByName(ctx context.Context, name string) (*common.AnalysisTopic, error) {
	headers := map[string]string{
		"Accept": "application/json",
	}

	client := http.DefaultClient
	client.Timeout = time.Duration(c.config.DependenceTimeout) * time.Second

	req, err := http.NewRequestWithContext(ctx, "GET", fmt.Sprintf("%s/v1/analysis-topics/by-name", c.config.DataSourcesEndpoint.String()), nil)
	if err != nil {
		return nil, err
	}

	q := req.URL.Query()
	q.Add("name", name)
	req.URL.RawQuery = q.Encode()

	for key, value := range headers {
		req.Header.Set(key, value)
	}

	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		b, _ := io.ReadAll(resp.Body)
		log.Debugf("HTTP code: %d, content: %s", resp.StatusCode, string(b))
		return nil, fmt.Errorf("%s", c.DataManagerErr(b))
	}

	analysisTopicResponse := &common.AnalysisTopic{}
	if err = json.NewDecoder(resp.Body).Decode(analysisTopicResponse); err != nil {
		log.Errorf("unmarshal failed: %v", err)
		return nil, err
	}

	return analysisTopicResponse, nil
}

type AnalysisTopicIndicatorList struct {
	Data []*common.Indicator `json:"data"`
	Page *common.Page        `json:"page"`
}

func (c *Client) GetAnalysisTopicIndicators(ctx context.Context, topicId string) (*AnalysisTopicIndicatorList, error) {
	headers := map[string]string{
		"Accept": "application/json",
	}

	client := http.DefaultClient
	client.Timeout = time.Duration(c.config.DependenceTimeout) * time.Second

	req, err := http.NewRequestWithContext(ctx, "GET", fmt.Sprintf("%s/v1/analysis-topics/%s/indicator-sems?page.limit=1000000", c.config.DataSourcesEndpoint.String(), topicId), nil)
	if err != nil {
		return nil, err
	}

	for key, value := range headers {
		req.Header.Set(key, value)
	}

	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		b, _ := io.ReadAll(resp.Body)
		log.Debugf("HTTP code: %d, content: %s", resp.StatusCode, string(b))
		return nil, fmt.Errorf("%s", c.DataManagerErr(b))
	}

	indicatorsResponse := &AnalysisTopicIndicatorList{}
	if err = json.NewDecoder(resp.Body).Decode(indicatorsResponse); err != nil {
		log.Errorf("unmarshal failed: %v", err)
		return nil, err
	}

	return indicatorsResponse, nil
}

func (c *Client) SearchAnalysisTopicIndicators(ctx context.Context, topicId int64, keyword string) (*AnalysisTopicIndicatorList, error) {
	headers := map[string]string{
		"Accept": "application/json",
	}

	client := http.DefaultClient
	client.Timeout = time.Duration(c.config.DependenceTimeout) * time.Second

	requestUrl := fmt.Sprintf(`%s/v1/analysis-topics/%d/indicator-sems?page.limit=10`, c.config.DataSourcesEndpoint.String(), topicId)

	req, err := http.NewRequestWithContext(ctx, "GET", requestUrl, nil)
	if err != nil {
		return nil, err
	}

	for key, value := range headers {
		req.Header.Set(key, value)
	}

	q := req.URL.Query()
	q.Add("search", keyword)
	req.URL.RawQuery = q.Encode()

	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		b, _ := io.ReadAll(resp.Body)
		log.Debugf("HTTP code: %d, content: %s", resp.StatusCode, string(b))
		return nil, fmt.Errorf("%s", c.DataManagerErr(b))
	}

	indicatorsResponse := &AnalysisTopicIndicatorList{}
	if err = json.NewDecoder(resp.Body).Decode(indicatorsResponse); err != nil {
		log.Errorf("unmarshal failed: %v", err)
		return nil, err
	}

	return indicatorsResponse, nil
}

type AnalysisTopicDimensionList struct {
	Data []*common.Dimension `json:"data"`
	Page *common.Page        `json:"page"`
}

func (c *Client) GetAnalysisTopicDimensions(ctx context.Context, topicId string) (*AnalysisTopicDimensionList, error) {
	headers := map[string]string{
		"Accept": "application/json",
	}

	client := http.DefaultClient
	client.Timeout = time.Duration(c.config.DependenceTimeout) * time.Second

	req, err := http.NewRequestWithContext(ctx, "GET", fmt.Sprintf("%s/v1/analysis-topics/%s/dimensions?page.limit=1000000", c.config.DataSourcesEndpoint.String(), topicId), nil)
	if err != nil {
		return nil, err
	}

	for key, value := range headers {
		req.Header.Set(key, value)
	}

	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		b, _ := io.ReadAll(resp.Body)
		log.Debugf("HTTP code: %d, content: %s", resp.StatusCode, string(b))
		return nil, fmt.Errorf("%s", c.DataManagerErr(b))
	}

	dimensionsResponse := &AnalysisTopicDimensionList{}
	if err = json.NewDecoder(resp.Body).Decode(dimensionsResponse); err != nil {
		log.Errorf("unmarshal failed: %v", err)
		return nil, err
	}

	return dimensionsResponse, nil
}

func (c *Client) GetAnalysisTopicAnalysisModel(ctx context.Context, topicId string, modelId int) (*common.AnalysisModel, error) {
	headers := map[string]string{
		"Accept": "application/json",
	}

	client := http.DefaultClient
	client.Timeout = time.Duration(c.config.DependenceTimeout) * time.Second

	req, err := http.NewRequestWithContext(ctx, "GET", fmt.Sprintf("%s/v1/analysis-topics/%s/analysis-models/%d", c.config.DataSourcesEndpoint.String(), topicId, modelId), nil)
	if err != nil {
		return nil, err
	}

	for key, value := range headers {
		req.Header.Set(key, value)
	}

	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		b, _ := io.ReadAll(resp.Body)
		log.Debugf("HTTP code: %d, content: %s", resp.StatusCode, string(b))
		return nil, fmt.Errorf("%s", c.DataManagerErr(b))
	}

	analysisModelResponse := &common.AnalysisModel{}
	if err = json.NewDecoder(resp.Body).Decode(analysisModelResponse); err != nil {
		log.Errorf("unmarshal failed: %v", err)
		return nil, err
	}

	return analysisModelResponse, nil
}

type DataSourcesList struct {
	DataSources []*common.DataSources `json:"data_sources"`
	Page        *common.Page          `json:"page"`
}

func (c *Client) GetDataSources(ctx context.Context) (*DataSourcesList, error) {
	headers := map[string]string{
		"Accept": "application/json",
	}

	client := http.DefaultClient
	client.Timeout = time.Duration(c.config.DependenceTimeout) * time.Second

	req, err := http.NewRequestWithContext(ctx, "GET", fmt.Sprintf("%s/v1/data-sources?page.offset=0&page.limit=1000", c.config.DataSourcesEndpoint.String()), nil)
	if err != nil {
		return nil, err
	}

	for key, value := range headers {
		req.Header.Set(key, value)
	}

	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		b, _ := io.ReadAll(resp.Body)
		log.Debugf("HTTP code: %d, content: %s", resp.StatusCode, string(b))
		return nil, fmt.Errorf("%s", c.DataManagerErr(b))
	}

	response := &DataSourcesList{}
	if err = json.NewDecoder(resp.Body).Decode(response); err != nil {
		log.Errorf("unmarshal failed: %v", err)
		return nil, err
	}

	return response, nil
}
