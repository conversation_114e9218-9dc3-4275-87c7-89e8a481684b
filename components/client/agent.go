package client

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	log "github.com/sirupsen/logrus"

	"gitlab.sz.sensetime.com/fin/nl2sql/nl2sql-api-service/components/common"
)

const (
	RunIdKey  = "run_id"
	UserIdKey = "user_id"
	QueryKey  = "query"
	SourceKey = "source"
)

func SetCommonCtx(ctx context.Context, runId, userId, query string) context.Context {
	ctx = context.WithValue(ctx, RunIdKey, runId)
	ctx = context.WithValue(ctx, UserIdKey, userId)
	query = base64.StdEncoding.EncodeToString([]byte(query))
	ctx = context.WithValue(ctx, QueryKey, query)
	ctx = context.WithValue(ctx, SourceKey, "online")
	return ctx
}

type AgentCreateRequest struct {
	Topic   string `json:"topic"`
	AgentID string `json:"agent_id"`
}

type AgentCreateResponse struct {
	Agent *common.Agent `json:"agent"`
}

func (c *Client) AgentCreate(ctx context.Context, agentRequest *AgentCreateRequest) (*AgentCreateResponse, error) {
	headers := map[string]string{
		"Content-type": "application/json",
	}

	client := http.DefaultClient
	client.Timeout = time.Duration(c.config.DependenceTimeout) * time.Second

	body, err := json.Marshal(agentRequest)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequestWithContext(ctx, "POST", fmt.Sprintf("%s/v1/agents", c.config.AgentsEndpoint.String()), bytes.NewBuffer(body))
	if err != nil {
		return nil, err
	}

	for key, value := range headers {
		req.Header.Set(key, value)
	}

	for _, key := range []string{"X-Run-ID", "X-User-ID", "X-Query", "X-Source"} {
		var ctxKey interface{}
		switch key {
		case "X-Run-ID":
			ctxKey = RunIdKey
		case "X-User-ID":
			ctxKey = UserIdKey
		case "X-Query":
			ctxKey = QueryKey
		case "X-Source":
			ctxKey = SourceKey
		}
		if val, ok := ctx.Value(ctxKey).(string); ok && val != "" {
			req.Header.Set(key, val)
		}
	}

	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		// 读取 body 并打印
		b, _ := io.ReadAll(resp.Body)
		log.Debugf("HTTP code: %d, content: %s", resp.StatusCode, string(b))
		return nil, fmt.Errorf("HTTP code: %d, content: %s", resp.StatusCode, string(b))
	}

	bodyData, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("read body failed: %v", err)
		return nil, err
	}

	agentResponse := &AgentCreateResponse{}
	if err := json.NewDecoder(bytes.NewReader(bodyData)).Decode(agentResponse); err != nil {
		log.Errorf("unmarshal failed: %v", err)
		return nil, err
	}

	return agentResponse, nil
}

func (c *Client) GetAgent(ctx context.Context, agentID string) (*common.Agent, error) {
	headers := map[string]string{
		"Accept": "application/json",
	}

	client := http.DefaultClient
	client.Timeout = time.Duration(c.config.DependenceTimeout) * time.Second

	req, err := http.NewRequestWithContext(ctx, "GET", fmt.Sprintf("%s/v1/agents/%s", c.config.AgentsEndpoint.String(), agentID), nil)
	if err != nil {
		return nil, err
	}

	for key, value := range headers {
		req.Header.Set(key, value)
	}

	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		// 读取 body 并打印
		b, _ := io.ReadAll(resp.Body)
		log.Debugf("HTTP code: %d, content: %s", resp.StatusCode, string(b))
		return nil, fmt.Errorf("HTTP code: %d, content: %s", resp.StatusCode, string(b))
	}

	bodyData, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("read body failed: %v", err)
		return nil, err
	}

	agentResponse := struct {
		Agent common.Agent `json:"agent"`
	}{}
	if err := json.NewDecoder(bytes.NewReader(bodyData)).Decode(&agentResponse); err != nil {
		log.Errorf("unmarshal failed: %v", err)
		return nil, err
	}

	return &agentResponse.Agent, nil
}

type AgentsListResponse struct {
	Agents []*common.Agent `json:"agents"`
	Page   struct {
		Offset int `json:"offset"`
		Limit  int `json:"limit"`
		Total  int `json:"total"`
	} `json:"page"`
}

func (c *Client) GetAgents(ctx context.Context, offset, limit int) (*AgentsListResponse, error) {
	headers := map[string]string{
		"Accept": "application/json",
	}

	client := http.DefaultClient
	client.Timeout = time.Duration(c.config.DependenceTimeout) * time.Second

	req, err := http.NewRequestWithContext(ctx, "GET", fmt.Sprintf("%s/v1/agents?offset=%d&limit=%d", c.config.AgentsEndpoint.String(), offset, limit), nil)
	if err != nil {
		return nil, err
	}

	for key, value := range headers {
		req.Header.Set(key, value)
	}

	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		// 读取 body 并打印
		b, _ := io.ReadAll(resp.Body)
		log.Debugf("HTTP code: %d, content: %s", resp.StatusCode, string(b))
		return nil, fmt.Errorf("HTTP code: %d, content: %s", resp.StatusCode, string(b))
	}

	bodyData, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("read body failed: %v", err)
		return nil, err
	}

	agentsListResponse := &AgentsListResponse{}
	if err := json.NewDecoder(bytes.NewReader(bodyData)).Decode(agentsListResponse); err != nil {
		log.Errorf("unmarshal failed: %v", err)
		return nil, err
	}

	return agentsListResponse, nil
}

func (c *Client) DeleteAgent(ctx context.Context, agentID string) error {
	headers := map[string]string{
		"Accept": "application/json",
	}

	client := http.DefaultClient
	client.Timeout = time.Duration(c.config.DependenceTimeout) * time.Second

	req, err := http.NewRequestWithContext(ctx, "DELETE", fmt.Sprintf("%s/v1/agents/%s", c.config.AgentsEndpoint.String(), agentID), nil)
	if err != nil {
		return err
	}

	for key, value := range headers {
		req.Header.Set(key, value)
	}

	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		// 读取 body 并打印
		b, _ := io.ReadAll(resp.Body)
		log.Debugf("HTTP code: %d, content: %s", resp.StatusCode, string(b))
		return fmt.Errorf("HTTP code: %d, content: %s", resp.StatusCode, string(b))
	}

	return nil
}
